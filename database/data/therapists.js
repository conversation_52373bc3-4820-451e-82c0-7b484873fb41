const { faker } = require('@faker-js/faker');
const { randomInt } = require('crypto');

const sex = ['female', 'male'];
const therapists = [...Array(20)].map(() => {
    const gender = sex[Math.round(Math.random())];
    return {
        firstname: faker.person.firstName(gender),
        lastname: faker.person.lastName(),
        email: faker.internet.email(),
        emailVerifiedAt: new Date(),
        password: '$2a$10$cu0RvcHAGsDzsiZj74GGJe99Q7oKqQufIQhpURnlfkVSYSoRHTbvW',
        dob: faker.date.past({
            years: randomInt(18, 32),
        }),
        gender,
        role: 'therapist',
        address: JSON.stringify({
            street: faker.location.street(),
            city: faker.location.city(),
            state: faker.location.state(),
            zip: faker.location.zipCode(),
        }),
        phone: faker.phone.number(),
        // coordinate: {
        //     latitude: faker.location.latitude(),
        //     longitude: faker.location.longitude(),
        // },
    };
});

module.exports = therapists;