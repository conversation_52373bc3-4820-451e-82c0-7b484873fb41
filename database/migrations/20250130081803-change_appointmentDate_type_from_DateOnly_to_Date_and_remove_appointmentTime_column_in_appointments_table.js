'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.changeColumn('appointments', 'appointmentDate', { type: Sequelize.DATE, allowNull: false }, { transaction });
      await queryInterface.removeColumn('appointments', 'appointmentTime', { transaction });
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn('appointments', 'appointmentTime', { type: Sequelize.TIME, allowNull: true }, { transaction });
      await queryInterface.changeColumn('appointments', 'appointmentDate', { type: Sequelize.DATEONLY, allowNull: false }, { transaction });
    });
  }
};
