module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user_therapist_valuation', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      therapistValuationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'therapist_evaluation',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      rating: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('user_therapist_valuation');
  }
};