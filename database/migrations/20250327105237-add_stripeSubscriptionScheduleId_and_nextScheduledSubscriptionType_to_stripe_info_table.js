'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn('stripe_info', 'stripeSubscriptionScheduleId', {
        type: Sequelize.STRING,
        allowNull: true,
      });

      await queryInterface.addColumn('stripe_info', 'nextScheduledSubscriptionType', {
        type: Sequelize.STRING,
        allowNull: true,
      });
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('stripe_info', 'stripeSubscriptionScheduleId');
      await queryInterface.removeColumn('stripe_info', 'nextScheduledSubscriptionType');
    });
  }
};
