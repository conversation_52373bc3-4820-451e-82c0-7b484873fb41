module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('pages', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      code: {
        allowNull: false,
        type: Sequelize.STRING
      },
      title: {
        allowNull: true,
        type: Sequelize.STRING
      },
      info: {
        allowNull: true,
        type: Sequelize.TEXT
      },
      extra: {
        allowNull: true,
        type: Sequelize.TEXT
      },
      button: {
        allowNull: false,
        type: Sequelize.STRING(50)
      },
      type: { // normal, questionnaire...
        allowNull: false,
        type: Sequelize.STRING(25)
      },
      sortOrder: {
        allowNull: false,
        type: Sequelize.INTEGER,
        defaultValue: 0
      },
      category: {
        allowNull: false,
        type: Sequelize.ENUM('self', 'couple', 'child', 'teen', 'therapist')
      },
      questionnaireId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'questionnaires',
          key: 'id'
        },
        onDelete: 'set null',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('pages');
  }
};