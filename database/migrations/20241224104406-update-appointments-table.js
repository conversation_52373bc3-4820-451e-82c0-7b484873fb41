'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('appointments', 'startDate');
    await queryInterface.removeColumn('appointments', 'endDate');
    await queryInterface.addColumn('appointments', 'appointmentDate', {
      allowNull: false,
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('appointments', 'appointmentTime', {
      allowNull: false,
      type: Sequelize.TIME,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('appointments', 'startDate', {
      allowNull: false,
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('appointments', 'endDate', {
      allowNull: true,
      type: Sequelize.DATE,
    });
    await queryInterface.removeColumn('appointments', 'appointmentDate');
    await queryInterface.removeColumn('appointments', 'appointmentTime');
  }
};