'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('patient_cards', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      patientId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      stripeSetupIntentId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      stripePaymentMethodId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      cardLastFourDigits: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      cardBrand: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      cardExpMonth: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      cardExpYear: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down (queryInterface) {
    await queryInterface.dropTable('patient_cards');
  }
};
