'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      // Remove the unique constraint inside the transaction
      await queryInterface.removeConstraint('user_devices', 'user_devices_deviceId_key', { transaction });

      // Modify the column inside the same transaction
      await queryInterface.changeColumn(
        'user_devices',
        'deviceId',
        {
          type: Sequelize.TEXT,
          allowNull: false,
        },
        { transaction }
      );
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.changeColumn('user_devices', 'deviceId', {
      type: Sequelize.TEXT,
      unique: true,
      allowNull: false,
    })
  }
};
