'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('calendar_invite_patient', 'appointmentId', {
      type: Sequelize.INTEGER, 
      allowNull: true,
      references: {
        model: 'appointments',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('calendar_invite_patient', 'appointmentId');
  },
};
