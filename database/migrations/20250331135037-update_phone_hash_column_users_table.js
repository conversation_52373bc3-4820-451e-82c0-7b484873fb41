const crypto = require('crypto');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const users = await queryInterface.sequelize.query(
      `SELECT id, phone FROM users WHERE phone IS NOT NULL`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    // Update each user's phone_hash with the hashed value
    for (const user of users) {
    const hashedPhone = crypto.createHash('sha512').update(user.phone).digest('hex');
      await queryInterface.sequelize.query(
        `UPDATE users SET phone_hash = :hashedPhone WHERE id = :id`,
        {
          replacements: { hashedPhone, id: user.id },
        }
      );
    }
  },

  down: async (queryInterface, Sequelize) => {
    // No rollback logic 
  }
};