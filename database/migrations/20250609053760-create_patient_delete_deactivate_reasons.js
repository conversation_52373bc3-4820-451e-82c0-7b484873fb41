'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First create the table
    await queryInterface.createTable('patient_delete_deactivate_reasons', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      heading: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });

    // Then insert the predefined reasons
    await queryInterface.bulkInsert('patient_delete_deactivate_reasons', [
      {
        heading: 'Patient requested account deletion',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Duplicate or fraudulent accounts detected',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Misuse of the platform (e.g., harassment, abuse of scheduling system)',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Payment disputes or repeated no-shows',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Violation of platform terms of use or community standards',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Security or safety concerns flagged by a therapist or admin',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Attempt to impersonate another individual or use false identity information',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    // Drop the table in the down migration
    await queryInterface.dropTable('patient_delete_deactivate_reasons');
  }
};