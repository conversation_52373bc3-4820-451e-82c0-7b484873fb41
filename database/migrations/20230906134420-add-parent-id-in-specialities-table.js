'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("specialities", "parentId", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'specialities',
        key: 'id'
      },
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn("specialities", "parentId")
  }
};
