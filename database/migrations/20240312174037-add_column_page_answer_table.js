'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn("page_answer", "followedByAns", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'answers',
        key: 'id'
      },
      onDelete: 'cascade',
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn("page_answer", "followedByAns")
  }
};
