'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('normal_office_hours', 'appointmentMethod', {
      type: Sequelize.ENUM('in-person', 'telehealth', 'in-person-and-telehealth'),
      allowNull: true
    });
  },

  async down (queryInterface) {
    await queryInterface.removeColumn('normal_office_hours', 'appointmentMethod');
  }
};
