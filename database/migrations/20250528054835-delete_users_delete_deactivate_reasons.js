'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE TYPE enum_user_delete_deactivate_reasons_type AS ENUM ('deleted', 'deactivated','rejected');
    `);

    await queryInterface.addColumn('user_delete_deactivate_reasons', 'type', {
      type: 'enum_user_delete_deactivate_reasons_type',
      allowNull: false,
      defaultValue: 'deleted'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('user_delete_deactivate_reasons', 'type');
    
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_user_delete_deactivate_reasons_type;
    `);
  }
};