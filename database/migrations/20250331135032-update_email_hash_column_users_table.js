const crypto = require('crypto');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const users = await queryInterface.sequelize.query(
      `SELECT id, email FROM users WHERE email IS NOT NULL`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    for (const user of users) {
    const hashedEmail = crypto.createHash('sha512').update(user.email).digest('hex');
      await queryInterface.sequelize.query(
        `UPDATE users SET email_hash = :hashedEmail WHERE id = :id`,
        {
          replacements: { hashedEmail, id: user.id },
        }
      );
    }
  },

  down: async (queryInterface, Sequelize) => {
    // No rollback logic 
  }
};