'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('patient_profile', 'seekingTherapyFor', {
      type: Sequelize.ENUM('self', 'couple', 'child', 'teen'),
      allowNull: true,
    })
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('patient_profile', 'seekingTherapyFor')
  }
};
