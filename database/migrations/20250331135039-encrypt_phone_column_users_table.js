'use strict';

const crypto = require('crypto');
const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager');

let PUBLIC_KEY = process.env.PUBLIC_KEY || '';
let PRIVATE_KEY = process.env.PRIVATE_KEY || '';
const RSA_DEV_KEY = 'dev/rsa-keys';
const RSA_PROD_KEY = 'prod/rsa-keys';


async function initializeCryptoUtils() {
  try {
    console.log('Initializing crypto utils...');

    const key = RSA_DEV_KEY;

    const secretData = await getAWSSecretById(key);

    if (secretData?.SecretString) {
      const parsedSecret = JSON.parse(secretData?.SecretString);

      PUBLIC_KEY = parsedSecret?.PUBLIC_KEY ;
      PRIVATE_KEY = parsedSecret?.PRIVATE_KEY ;
    } else {
      console.error('No SecretString found in secret data');
    }

    if (PUBLIC_KEY) {
      PUBLIC_KEY = PUBLIC_KEY.replace(/\\n/g, '\n');
      console.log('PUBLIC_KEY contains newlines:', PUBLIC_KEY.includes('\n'));
    }

    if (PRIVATE_KEY) {
      PRIVATE_KEY = PRIVATE_KEY.replace(/\\n/g, '\n');
      console.log('PRIVATE_KEY contains newlines:', PRIVATE_KEY.includes('\n'));
    }

  } catch (error) {
    console.error('Error initializing crypto utils:', error);
    throw error;
  }
}

async function getAWSSecretById(secretId) {
  const client = new SecretsManagerClient({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
    },
  });
  const command = new GetSecretValueCommand({ SecretId: secretId });
  return await client.send(command);
}

function encryptData(data) {
  const stringData = typeof data === 'object' ? JSON.stringify(data) : String(data);
  const buffer = Buffer.from(stringData, 'utf8');

  if (buffer.length <= 190) {
    return crypto.publicEncrypt(
      {
        key: PUBLIC_KEY,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256',
      },
      buffer
    ).toString('base64');
  }

  const aesKey = crypto.randomBytes(32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
  let encryptedData = cipher.update(stringData, 'utf8', 'base64');
  encryptedData += cipher.final('base64');

  const encryptedKey = crypto.publicEncrypt(
    {
      key: PUBLIC_KEY,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: 'sha256',
    },
    aesKey
  ).toString('base64');

  const result = {
    encryptedKey,
    iv: iv.toString('base64'),
    encryptedData,
  };

  return JSON.stringify(result);
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await initializeCryptoUtils();
    const users = await queryInterface.sequelize.query(
      `SELECT id, phone_backup FROM users WHERE phone_backup IS NOT NULL`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    for (const user of users) {
      const encryptedPhone = encryptData(user.phone_backup);
      await queryInterface.sequelize.query(
        `UPDATE users SET phone = :encryptedPhone WHERE id = :id`,
        {
          replacements: { encryptedPhone, id: user.id },
        }
      );
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      UPDATE users
      SET phone = phone_backup
      WHERE phone_backup IS NOT NULL
    `);
  },
};