module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('therapist_profile', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      bio: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      acceptingPatients: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false
      },
      acceptsInsurance: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false
      },
      telehealth: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false
      },
      education: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {
          degree: '',
          school: '',
        }
      },
      license: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      experience: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {
          duration: '',
          date: '',
        }
      },
      therapyStyle: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('therapist_profile');
  }
};