module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('temp_uploads', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      uploaded_file_key: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      uploadedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('temp_uploads');
  }
};