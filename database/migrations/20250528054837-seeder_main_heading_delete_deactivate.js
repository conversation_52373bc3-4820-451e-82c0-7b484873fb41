'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface) {
    await queryInterface.bulkInsert('main_heading_delete_deactivate', [
      {
        heading: 'Licensing & Credential Issues',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Verification & Incomplete Profile',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Conduct & Complaints',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        heading: 'Operational or Administrative Reasons',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ], {});
  },

  async down (queryInterface) {
    await queryInterface.bulkDelete('main_heading_delete_deactivate', null, {});
  }
};