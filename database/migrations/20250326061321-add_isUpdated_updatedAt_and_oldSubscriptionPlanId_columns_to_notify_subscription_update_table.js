'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn('notify_subscription_update', 'isUpdated', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        transaction,
      });

      await queryInterface.addColumn('notify_subscription_update', 'updatedAt', {
        type: Sequelize.DATE,
        allowNull: true,
        transaction,
      });

      await queryInterface.addColumn('notify_subscription_update', 'oldSubscriptionPlanId', {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'subscription_plan',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        transaction,
      });

      await queryInterface.addColumn('notify_subscription_update', 'therapistSubscriptionId', {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'therapist_subscription',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        transaction,
      });
    });
  },

  async down (queryInterface) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('notify_subscription_update', 'isUpdated', {
        transaction,
      });

      await queryInterface.removeColumn('notify_subscription_update', 'updatedAt', {
        transaction,
      });

      await queryInterface.removeColumn('notify_subscription_update', 'oldSubscriptionPlanId', {
        transaction,
      });
      
      await queryInterface.removeColumn('notify_subscription_update', 'therapistSubscriptionId', {
        transaction,
      });
    });
  }
};
