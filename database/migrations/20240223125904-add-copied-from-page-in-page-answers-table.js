'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn("page_answer", "copiedFromPage", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'pages',
        key: 'id'
      },
      onDelete: 'cascade',
    });
  },

  async down (queryInterface) {
    await queryInterface.removeColumn("page_answer", "copiedFromPage")
  }
};
