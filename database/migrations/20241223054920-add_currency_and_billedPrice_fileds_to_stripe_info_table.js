'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('stripe_info', 'currency', {
      type: Sequelize.STRING,
      allowNull: true
    })
    await queryInterface.addColumn('stripe_info', 'billedPrice', {
      type: Sequelize.INTEGER,
      allowNull: true
    })
  },

  async down (queryInterface) {
    await queryInterface.removeColumn('stripe_info', 'currency')
    await queryInterface.removeColumn('stripe_info', 'billedPrice')
  }
};
