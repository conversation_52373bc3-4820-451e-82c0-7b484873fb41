'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First clear all data
    await queryInterface.bulkDelete('profile_delete_deactivate_reasons', null, {});

    // Remove old columns and add new ones
    await queryInterface.removeColumn('profile_delete_deactivate_reasons', 'type');
    await queryInterface.removeColumn('profile_delete_deactivate_reasons', 'reason');
    
    // Add new columns
    await queryInterface.addColumn('profile_delete_deactivate_reasons', 'subheading', {
      type: Sequelize.TEXT,
      allowNull: false
    });
    
    await queryInterface.addColumn('profile_delete_deactivate_reasons', 'headingId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'main_heading_delete_deactivate',
        key: 'id'
      },
      onDelete: 'CASCADE'
    });

    // Seed new data
    await queryInterface.bulkInsert('profile_delete_deactivate_reasons', [
      // Licensing & Credential Issues (id: 1)
      {
        subheading: 'No valid license submitted',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'License could not be verified',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'License is expired',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Therapist is under investigation, suspension, or disciplinary action by a licensing board',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Therapist is not licensed in the state(s) they selected',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Therapist submitted a license that does not match their name or credentials',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'No active license on file',
        headingId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Verification & Incomplete Profile (id: 2)
      {
        subheading: 'Insufficient information provided to verify therapist identity or qualifications',
        headingId: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Key documents or profile details are missing',
        headingId: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Information submitted appears inconsistent or inaccurate',
        headingId: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Profile remained incomplete or unverified for an extended period of time',
        headingId: 2,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Conduct & Complaints (id: 3)
      {
        subheading: 'A formal complaint was submitted and is under investigation',
        headingId: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Reports of unprofessional, unethical, or inappropriate conduct',
        headingId: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Violation of platform guidelines or terms of service',
        headingId: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Misrepresentation of qualifications or scope of practice',
        headingId: 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // Operational or Administrative Reasons (id: 4)
      {
        subheading: 'Therapist requested account deletion',
        headingId: 4,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Duplicate accounts identified',
        headingId: 4,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Prolonged inactivity on the platform',
        headingId: 4,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        subheading: 'Technical error or security concern requiring a temporary suspension',
        headingId: 4,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    // Remove new columns
    await queryInterface.removeColumn('profile_delete_deactivate_reasons', 'headingId');
    await queryInterface.removeColumn('profile_delete_deactivate_reasons', 'subheading');
    
    // Add back old columns
    await queryInterface.addColumn('profile_delete_deactivate_reasons', 'type', {
      type: Sequelize.ENUM('deleted', 'deactivated'),
      allowNull: false
    });
    await queryInterface.addColumn('profile_delete_deactivate_reasons', 'reason', {
      type: Sequelize.TEXT,
      allowNull: false
    });
  }
};