'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('matching_algo_resources', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      therapistPageId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'therapist_pages',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      patientPageId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'pages',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      category: {
        allowNull: false,
        type: Sequelize.ENUM('self', 'couple', 'child', 'teen', 'therapist')
      },
      questionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'questionnaires',
          key: 'id'
        },
        onDelete: 'set null',
      },
      answerId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'answers',
          key: 'id'
        },
        onDelete: 'set null',
      },
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('matching_algo_resources');
  }
};
