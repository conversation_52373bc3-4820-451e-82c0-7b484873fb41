'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Step 1: Add a new column `phone_backup` to the `users` table
    await queryInterface.addColumn('users', 'phone_backup', {
      type: Sequelize.STRING(1024),
      allowNull: true,
    });

    // Step 2: Transfer all existing phones from `phone` to `phone_backup`
    await queryInterface.sequelize.query(`
      UPDATE users
      SET phone_backup = phone
      WHERE phone IS NOT NULL
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // Step 1: Remove the `phone_backup` column
    await queryInterface.removeColumn('users', 'phone_backup');
  },
};