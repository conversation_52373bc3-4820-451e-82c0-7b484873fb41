'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {  
    await queryInterface.addColumn('appointments', 'lockedByUserId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    await queryInterface.addColumn('appointments', 'lockedAt', {
      type: Sequelize.DATE,
      allowNull: true
    });
    
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('appointments', 'lockedByUserId');
    await queryInterface.removeColumn('appointments', 'lockedAt');
  }
};
