'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First check if table exists and drop it to remove dependencies
    await queryInterface.sequelize.query(`
      DROP TABLE IF EXISTS profile_delete_deactivate_reasons CASCADE;
    `);

    // Now we can safely drop and recreate the ENUM type
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_profile_delete_deactivate_reasons_type;
      CREATE TYPE enum_profile_delete_deactivate_reasons_type AS ENUM ('deleted', 'deactivated');
    `);

    await queryInterface.createTable('profile_delete_deactivate_reasons', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false
      },
      type: {
        type: 'enum_profile_delete_deactivate_reasons_type',
        allowNull: false
      },
      reason: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Insert predefined reasons
    await queryInterface.bulkInsert('profile_delete_deactivate_reasons', [
      {
        type: 'deleted',
        reason: 'User requested to delete the account',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        type: 'deactivated',
        reason: 'Suspension of license',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        type: 'deactivated',
        reason: 'Complaint from NT user',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        type: 'deactivated',
        reason: 'Policy violation',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        type: 'deactivated',
        reason: 'Past due subscription balance',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      DROP TABLE IF EXISTS profile_delete_deactivate_reasons CASCADE;
      DROP TYPE IF EXISTS enum_profile_delete_deactivate_reasons_type;
    `);
  }
};