module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.addColumn('appointments', 'workingHourId', {
            type: Sequelize.INTEGER,
            allowNull: true, // Allow NULL if it's optional
            references: {
                model: 'normal_office_hours', // Name of the table being referenced
                key: 'id', // Primary key of the referenced table
            },
            onDelete: 'CASCADE', // Deletes appointments if the referenced row is deleted
            onUpdate: 'CASCADE', // Updates appointments if the referenced row is updated
        });
    },

    async down(queryInterface) {
        await queryInterface.removeColumn('appointments', 'workingHourId');
    },
};
