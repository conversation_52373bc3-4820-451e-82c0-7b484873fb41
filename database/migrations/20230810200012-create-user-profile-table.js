module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('patient_profile', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      paymentPreference: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      preferredDistance: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      telehealth: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('patient_profile');
  }
};