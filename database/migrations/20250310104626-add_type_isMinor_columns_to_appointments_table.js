'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('appointments', 'minorId', {
        type: Sequelize.INTEGER,
        allowNull: true,
    });
    await queryInterface.addColumn('appointments', 'isMinor', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('appointments', 'minorId');
    await queryInterface.removeColumn('appointments', 'isMinor');
  }
};