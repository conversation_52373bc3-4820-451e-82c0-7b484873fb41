module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('answer_follow_through', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      followingBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'answers',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      followingTo: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'answers',
          key: 'id'
        },
        onDelete: 'cascade',
      }
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('answer_follow_through');
  }
};