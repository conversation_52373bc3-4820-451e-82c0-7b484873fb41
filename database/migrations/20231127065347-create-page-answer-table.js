module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('page_answer', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      pageId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'pages',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      answerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'answers',
          key: 'id'
        },
        onDelete: 'cascade',
      },
      conditions: {
        allowNull: true,
        type: Sequelize.JSONB,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
    });
  },
  async down(queryInterface) {
    await queryInterface.dropTable('page_answer');
  }
};