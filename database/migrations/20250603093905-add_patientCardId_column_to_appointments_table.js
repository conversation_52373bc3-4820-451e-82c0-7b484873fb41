'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('appointments', 'patientCardId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'patient_cards',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down (queryInterface) {
    await queryInterface.removeColumn('appointments', 'patientCardId');
  }
};
