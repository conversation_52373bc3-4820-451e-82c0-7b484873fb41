'use strict';
const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager');
const crypto = require('crypto');

let PUBLIC_KEY = process.env.PUBLIC_KEY || '';

let PRIVATE_KEY = process.env.PRIVATE_KEY || '';
const RSA_DEV_KEY = 'dev/rsa-keys';
const RSA_PROD_KEY = 'prod/rsa-keys';


async function initializeCryptoUtils() {
  try {
    console.log('Initializing crypto utils...');

    const key = RSA_DEV_KEY;

    const secretData = await getAWSSecretById(key);

    if (secretData?.SecretString) {
      const parsedSecret = JSON.parse(secretData?.SecretString);

      PUBLIC_KEY = parsedSecret?.PUBLIC_KEY ;
      PRIVATE_KEY = parsedSecret?.PRIVATE_KEY ;
    } else {
      console.error('No SecretString found in secret data');
    }

    if (PUBLIC_KEY) {
      PUBLIC_KEY = PUBLIC_KEY.replace(/\\n/g, '\n');
      console.log('PUBLIC_KEY contains newlines:', PUBLIC_KEY.includes('\n'));
    }

    if (PRIVATE_KEY) {
      PRIVATE_KEY = PRIVATE_KEY.replace(/\\n/g, '\n');
      console.log('PRIVATE_KEY contains newlines:', PRIVATE_KEY.includes('\n'));
    }

  } catch (error) {
    console.error('Error initializing crypto utils:', error);
    throw error;
  }
}

async function getAWSSecretById(secretId) {
  const client = new SecretsManagerClient({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_KEY,
    },
  });
  const command = new GetSecretValueCommand({ SecretId: secretId });
  return await client.send(command);
}
function encryptData(data) {
  try {
    const stringData = typeof data === 'object' ? JSON.stringify(data) : String(data);
    const buffer = Buffer.from(stringData, 'utf8');

    if (!PUBLIC_KEY || !PUBLIC_KEY.includes('-----BEGIN PUBLIC KEY-----')) {
      console.error('Invalid or missing PUBLIC_KEY format');
      throw new Error('Invalid PUBLIC_KEY format');
    }

    if (buffer.length <= 190) {
      return crypto.publicEncrypt(
        {
          key: PUBLIC_KEY,
          padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
          oaepHash: 'sha256',
        },
        buffer
      ).toString('base64');
    }

    const aesKey = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
    let encryptedData = cipher.update(stringData, 'utf8', 'base64');
    encryptedData += cipher.final('base64');

    const encryptedKey = crypto.publicEncrypt(
      {
        key: PUBLIC_KEY,
        padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256',
      },
      aesKey
    ).toString('base64');

    const result = {
      encryptedKey,
      iv: iv.toString('base64'),
      encryptedData,
    };

    return JSON.stringify(result);
  } catch (error) {
    console.error('Encryption error:', error.message);
    throw error;
  }
}

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await initializeCryptoUtils();
    const users = await queryInterface.sequelize.query(
      `SELECT id, email_backup FROM users WHERE email_backup IS NOT NULL`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    for (const user of users) {
      const encryptedEmail = encryptData(user.email_backup);
      await queryInterface.sequelize.query(
        `UPDATE users SET email = :encryptedEmail WHERE id = :id`,
        {
          replacements: { encryptedEmail, id: user.id },
        }
      );
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      UPDATE users
      SET email = email_backup
      WHERE email_backup IS NOT NULL
    `);
  },
};