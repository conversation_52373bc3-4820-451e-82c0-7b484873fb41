'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('appointments', 'workingHourId');

      await queryInterface.addColumn('appointments', 'workingHourId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'normal_office_hours',
          key: 'id',
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }, { transaction });
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('appointments', 'workingHourId');

      await queryInterface.addColumn('appointments', 'workingHourId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'normal_office_hours',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }, { transaction });
    });
  }
};
