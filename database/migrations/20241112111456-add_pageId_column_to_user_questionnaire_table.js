'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('user_questionnaire', 'pageId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'pages',
        key: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    })
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn('user_questionnaire', 'pageId')
  }
};
