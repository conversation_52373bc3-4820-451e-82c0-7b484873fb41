'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('appointments', 'tagId', { transaction });
      await queryInterface.removeColumn('appointments', 'isAllDay', { transaction });
      await queryInterface.removeColumn('appointments', 'isRecurring', { transaction });
      await queryInterface.removeColumn('appointments', 'recurringType', { transaction });
      await queryInterface.changeColumn(
        'appointments',
        'appointmentDate',
        { type: Sequelize.DATEONLY },
        { transaction }
      );
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'appointments',
        'tagId',
        {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'event_tags',
            key: 'id',
          },
          onDelete: 'cascade',
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'appointments',
        'isAllDay',
        { type: Sequelize.BOOLEAN, allowNull: false },
        { transaction }
      );
      await queryInterface.addColumn(
        'appointments',
        'isRecurring',
        { type: Sequelize.BOOLEAN, allowNull: false },
        { transaction }
      );
      await queryInterface.addColumn(
        'appointments',
        'recurringType',
        { type: Sequelize.STRING, allowNull: true },
        { transaction }
      );
      await queryInterface.changeColumn(
        'appointments',
        'appointmentDate',
        { type: Sequelize.DATE },
        { transaction }
      );
    });
  },
};
