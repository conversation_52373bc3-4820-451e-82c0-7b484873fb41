'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Step 1: Add a new column `email_backup` to the `users` table
    await queryInterface.addColumn('users', 'email_backup', {
      type: Sequelize.STRING(1024),
      allowNull: true,
    });

    // Step 2: Transfer all existing emails from `email` to `email_backup`
    await queryInterface.sequelize.query(`
      UPDATE users
      SET email_backup = email
      WHERE email IS NOT NULL
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'email_backup');
  },
};