'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("pages", "buttonAction", {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn("pages", "nextPageId", {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'pages',
        key: 'id'
      },
      onDelete: 'set null',
    })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn("pages", "nextPageId")
    await queryInterface.removeColumn("pages", "buttonAction")
  }
};
