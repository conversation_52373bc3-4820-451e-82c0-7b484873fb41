'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'appointments', 
        'googleEventId', 
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'appointments', 
        'outlookEventId', 
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        { transaction }
      );
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('appointments', 'googleEventId', { transaction });
      await queryInterface.removeColumn('appointments', 'outlookEventId', { transaction });
    });
  }
};
