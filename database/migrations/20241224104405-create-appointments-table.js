module.exports = {
    async up(queryInterface, Sequelize) {
        await queryInterface.createTable('appointments', {
            id: {
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
                type: Sequelize.INTEGER
            },
            tagId: {
                type: Sequelize.INTEGER,
                allowNull: true,
                references: {
                    model: 'event_tags',
                    key: 'id'
                },
                onDelete: 'cascade',
            },
            name: {
                type: Sequelize.STRING,
                allowNull: false,
            },
            description: {
                type: Sequelize.TEXT,
                allowNull: true,
            },
            startDate: {
                allowNull: false,
                type: Sequelize.DATE,
            },
            endDate: {
                allowNull: true,
                type: Sequelize.DATE,
            },
            isAllDay: {
                allowNull: false,
                type: Sequelize.BOOLEAN,
            },
            isRecurring: {
                allowNull: false,
                type: Sequelize.BOOLEAN,
            },
            recurringType: {
                allowNull: true,
                type: Sequelize.STRING,
            },
            therapistId: {
                type: Sequelize.INTEGER,
                allowNull: true,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'cascade',
            },
            patientId: {
                type: Sequelize.INTEGER,
                allowNull: true,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'cascade',
            },
            createdAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.fn('now')
            },
            updatedAt: {
                allowNull: false,
                type: Sequelize.DATE,
                defaultValue: Sequelize.fn('now')
            },
        });
    },
    async down(queryInterface) {
        await queryInterface.dropTable('appointments');
    }
};