'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("events", "location", {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn("events", "attendees", {
      type: Sequelize.JSONB,
      allowNull: true,
    });
  },

  async down(queryInterface) {
    await queryInterface.removeColumn("events", "attendees")
    await queryInterface.removeColumn("events", "location")
  }
};
