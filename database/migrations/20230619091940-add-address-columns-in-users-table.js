'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn("users", "coordinate", {
      type: Sequelize.GEOMETRY('POINT'),
      allowNull: true,
    })
    await queryInterface.addColumn("users", "address", {
      type: Sequelize.JSONB,
      allowNull: true,
    })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn("users", "address")
    await queryInterface.removeColumn("users", "coordinate")
  }
};
