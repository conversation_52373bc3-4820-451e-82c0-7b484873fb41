'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('stripe_info', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      therapistId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      therapistSubscriptionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'therapist_subscription',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      stripeSubscriptionId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      stripeCustomerId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      stripeCurrentPeriodStart: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      stripeCurrentPeriodEnd: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      stripeCancelAtPeriodEnd: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('now')
      }
    });
  },

  async down (queryInterface) {
    await queryInterface.dropTable('stripe_info');
  }
};
