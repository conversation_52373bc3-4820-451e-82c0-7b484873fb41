'use strict';
const answers = require('../data/answers');
const questions = require('../data/questions');
const specialities = require('../data/specialities');
const therapists = require('../data/therapists');
const therapistPages = require('../data/therapistPages');
const { faker } = require('@faker-js/faker');

module.exports = {
    async up(queryInterface) {
        const users = [
            {
                firstname: 'Next',
                lastname: 'Therapist',
                email: '<EMAIL>',
                emailVerifiedAt: new Date(),
                dob: '1990-01-01',
                gender: 'other',
                password: '$2a$10$cu0RvcHAGsDzsiZj74GGJe99Q7oKqQufIQhpURnlfkVSYSoRHTbvW', // P@ssw0rd
                role: 'system',
            },
        ];

        await queryInterface.bulkInsert('users', users, {}, {});

        await queryInterface.bulkInsert('specialities', specialities, {}, {});

        await queryInterface.bulkInsert(
            'answers',
            answers.map((answer) => ({
                answer,
            })),
            {},
            {}
        );

        await queryInterface.bulkInsert(
            'questionnaires',
            questions.map((question) => ({
                question,
            })),
            {},
            {}
        );

        await queryInterface.bulkInsert(
            'therapist_pages',
            therapistPages.map((pageName) => ({
                pageName,
            })),
            {},
            {}
        );

        await queryInterface.bulkInsert('users', therapists, {}, {});
        const userIds = await queryInterface.sequelize.query(`SELECT id from users;`);

        const userProfiles = [];

        userIds[0].forEach((userId) => {
            userProfiles.push({
                userId: userId.id,
                bio: faker.lorem.paragraph(),
                acceptingPatients: faker.datatype.boolean(),
                acceptsInsurance: faker.datatype.boolean(),
                telehealth: faker.datatype.boolean(),
                education: JSON.stringify({
                    degree: faker.lorem.word(),
                    school: faker.lorem.word(),
                }),
                license: faker.lorem.word(),
                experience: JSON.stringify({}),
            });
        });

        await queryInterface.bulkInsert('therapist_profile', userProfiles, {}, {});
    },

    async down(queryInterface) {
        await queryInterface.bulkDelete('users', null, {});
        await queryInterface.bulkDelete('specialities', null, {});
        await queryInterface.bulkDelete('answers', null, {});
        await queryInterface.bulkDelete('questionnaires', null, {});
    },
};
