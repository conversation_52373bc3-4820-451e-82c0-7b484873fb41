import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig as defineVitestConfig } from 'vitest/config';

export default defineConfig(defineVitestConfig({
  build: {
    manifest: true,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, "index.html"),
      },
      output: {
        entryFileNames: 'assets/[name].js',
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]'
      }
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "src"),
    },
  },
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: 'tests/setup.ts',
    coverage: {
      provider: "v8", 
      reporter: ["text", "html", "lcov"],
      exclude: ["node_modules/", "tests/", "dist/","plugins/", "vite.config.ts",
        "ecosystem.config.cjs",
        "postcss.config.js","vite-env.d.ts","src/vite-env.d.ts",
        "tailwind.config.cjs", "src/main.tsx", "src/types/","src/plugins/"],
    },
    alias: {
      "@": path.resolve(__dirname, "src")
    }
  },
  plugins: [react()],
}));