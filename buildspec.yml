version: 0.2
environment_variables:
  plaintext:
    projectKey: "NextTherapist"
    projctVersion: "Sprint-2"
    projectName: "NextTherapist-BE"

env:
  parameter-store:
    SONAR_TOKEN: "SONAR_TOKEN"
    SONAR_HOST: "SONAR_HOST"

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - echo Installing source NPM dependencies...
      - npm install
  build:
    commands:
      - echo Runnng sonar-scanner on `date`
      # - curl ifconfig.co
      - wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-3.2.0.1227-linux.zip
      - unzip sonar-scanner-cli-3.2.0.1227-linux.zip
      - cp sonar-scanner.properties sonar-scanner-3.2.0.1227-linux/conf/sonar-scanner.properties
      - echo $SONAR_HOST
      - ./sonar-scanner-3.2.0.1227-linux/bin/sonar-scanner -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectKey=$projectKey -Dsonar.projectVersion=$projectVersion
      - npm run build
      - echo Build started on `date`
  post_build:
    commands:
      - echo Build completed on `date`
      # - export NODE_ENV=production
      - npm run migrate
cache:
  paths:
    - "node_modules/**/*"

artifacts:
  files:
    - "build/**/*"
    - "package.json"
    - "package-lock.json"
    - "database.js"
    - "Procfile"
    - ".sequelizerc"
    - "src/templates/**/*"
    - "server.js"
    - ".platform/**/*" # AWS Elastic Beanstalk configuration files
    - ".ebextensions/**/*" # AWS Elastic Beanstalk configuration files
  discard-paths: no
  name: ${CODEBUILD_BUILD_NUMBER}
