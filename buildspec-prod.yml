version: 0.2
environment_variables:
  plaintext:
    S3_BUCKET: "nexttherapist-production-artifact"
    repository_url: "767397971879.dkr.ecr.us-east-2.amazonaws.com/next-therapist-production" 

phases:
  pre_build:
    commands:
      - echo aws --version
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 767397971879.dkr.ecr.us-east-2.amazonaws.com
      - REPOSITORY_URI=767397971879.dkr.ecr.us-east-2.amazonaws.com/next-therapist-production
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - echo Entered the pre_build phase...
      - aws s3 cp s3://nexttherapist-production-artifact/config/.env .
      - ls
     
 
  build:
    commands:
      - echo "Installing dependencies..."
      - npm install
      - set -a && . ./.env && set +a
      - echo "REDIS_URL set to $REDIS_URL"
      - export NODE_OPTIONS="--max-old-space-size=4096"
      - docker build -f Dockerfile.prod -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
      
      
      

  post_build:
    commands:
      - echo SonarQube analysis completed    
      - echo Build completed on date
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...  
      - printf '[{"name":"next-therapist-production","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
cache:
  paths:
    - '/root/.cache/docker/**/*'
    - 'node_modules/**/*' 

artifacts:
  files:
    - imagedefinitions.json
  discard-paths: yes
  name: ${CODEBUILD_BUILD_NUMBER}
