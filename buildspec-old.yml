version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - echo Installing source NPM dependencies...
      - npm install
  build:
    commands:
      - npm run build
      - echo Build started on `date`
  post_build:
    commands:
      - echo Build completed on `date`
      # - export NODE_ENV=production
      - npm run migrate
cache:
  paths:
    - "node_modules/**/*"

artifacts:
  files:
    - "build/**/*"
    - "package.json"
    - "package-lock.json"
    - "database.js"
    - "Procfile"
    - ".sequelizerc"
    - "src/templates/**/*"
    - "server.js"
    - ".platform/**/*" # AWS Elastic Beanstalk configuration files
    - ".ebextensions/**/*" # AWS Elastic Beanstalk configuration files
  discard-paths: no
  name: ${CODEBUILD_BUILD_NUMBER}
