import FormDialog from "@/components/dialogs/FormDialog";
import InputComponent from "@/components/forms/InputComponent";
import { AppDispatch } from "@/store/index";
import { updateSubscriptionPlanAction, createSubscriptionPlanAction } from "@/store/slicers/admin.slicer";
import { SubscriptionPlan } from "@/types/subscription-plan.interface";
import { validatePrice } from "@/utils/app.util";
import { FormEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { OverlayTriggerState } from "react-stately";

type SubscriptionPlanFormProps = {
  state: OverlayTriggerState;
  plan?: SubscriptionPlan;
  refetch?: () => void;
};

type SubscriptionPlanFormData = {
  annualPrice?: number;
	monthlyPrice?: number;
};

const SubscriptionPlanForm = ({ state, plan, refetch }: SubscriptionPlanFormProps) => {
	const dispatch = useDispatch<AppDispatch>()
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<SubscriptionPlanFormData>(
    {} as SubscriptionPlanFormData
  );
  const [errors, setErrors] = useState<{ annualPrice?: string; monthlyPrice?: string }>({});

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const annualPriceError = validatePrice(formData.annualPrice?.toString());
    const monthlyPriceError = validatePrice(formData.monthlyPrice?.toString());
    if (annualPriceError || monthlyPriceError) {
      setErrors({ annualPrice: annualPriceError, monthlyPrice: monthlyPriceError });
      return;
    }

    setLoading(true);
		const res = await dispatch(
      plan?.id
        ? updateSubscriptionPlanAction(formData, plan.id)
        : createSubscriptionPlanAction(formData)
    );
    setLoading(false);

    if (res?.status === 200 || res?.status === 201) {
      refetch?.();
      setFormData({} as SubscriptionPlanFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({} as SubscriptionPlanFormData);
    setErrors({});
    state.close();
  };

  useEffect(() => {
    if (plan) {
      setFormData({
        annualPrice: plan.annualPrice,
				monthlyPrice: plan.monthlyPrice,
      });
    } else {
      setFormData({} as SubscriptionPlanFormData);
    }
  }, [plan]);

  return (
    <FormDialog
      title="Subscription Plan Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel={plan?.id ? "Update" : "Create"}
      loading={loading}
      width="w-[420px]"
      state={state}
			disabled={!formData.annualPrice || !formData.monthlyPrice || !!errors.annualPrice || !!errors.monthlyPrice}
    >
      <div className="flex flex-col space-y-4">
        <InputComponent
          label={"Annual Price"}
          id="annual-price"
					type="number"
          onChange={(value) => {
            setFormData({ ...formData, annualPrice: Number(value) });
            setErrors({ ...errors, annualPrice: validatePrice(value) });
          }}
          value={formData.annualPrice?.toString() || ""}
          isRequired
          errorMessage={errors.annualPrice}
        />
				<InputComponent
          label={"Monthly Price"}
          id="monthly-price"
					type="number"
          onChange={(value) => {
            setFormData({ ...formData, monthlyPrice: Number(value) });
            setErrors({ ...errors, monthlyPrice: validatePrice(value) });
          }}
          value={formData.monthlyPrice?.toString() || ""}
          isRequired
          errorMessage={errors.monthlyPrice}
        />
      </div>
    </FormDialog>
  );
};

export default SubscriptionPlanForm;
