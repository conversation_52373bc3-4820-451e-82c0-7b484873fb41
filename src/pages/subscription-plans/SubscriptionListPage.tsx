import AddButton from "@/components/buttons/AddButton";
import PaginatedDataContainer from "@/components/PaginatedDataContainer";
import { useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { FC, useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { OverlayTriggerState, useOverlayTriggerState } from "react-stately";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import ActionButton from "@/components/buttons/ActionButton";
import Meta from "@/types/meta.interface";
import { AppDispatch } from "@/store/index";
import { activateSubscriptionPlanAction, deleteSubscriptionPlanAction, fetchSubscriptionPlansAction } from "@/store/slicers/admin.slicer";
import { useDispatch } from "react-redux";
import { SubscriptionPlan } from "@/types/subscription-plan.interface";
import SubscriptionPlanForm from "./SubscriptionPlanForm";
import NormalDialog from "@/components/dialogs/NormalDialog";
import dayjs from "dayjs";
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

interface IApplicationProps {}

const SubscriptionListPage: FC<IApplicationProps> = () => {
  const { ref } = useInView();
	const dispatch = useDispatch<AppDispatch>()
  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});
	const statusState = useOverlayTriggerState({});

  const [plan, setPlan] = useState<SubscriptionPlan>();
	const [loading, setLoading] = useState<boolean>(false);

  const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
    queryKey: ["subscription-plans"],
    queryFn: async ({ pageParam = 1 }) => {
			return dispatch(fetchSubscriptionPlansAction({ perPage:20, page: pageParam }));
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  const handleSuccess = (status: number | undefined, dialogState: OverlayTriggerState) => {
    if (status === 200) {
      setPlan(undefined);
      refetch();
      dialogState.close();
    }
  };  

  const handleActivatePlan = async () => {
    if (!plan?.id) return;
    setLoading(true);
    const res = await dispatch(activateSubscriptionPlanAction(plan.id));
    setLoading(false);
    handleSuccess(res?.status, statusState);
  };
  
  const handleDeletePlan = async () => {
    if (!plan?.id) return;
    setLoading(true);
    const res = await dispatch(deleteSubscriptionPlanAction(plan.id));
    setLoading(false);
    handleSuccess(res?.status, deleteState);
  }; 

  useEffect(() => {
    if (!state.isOpen && !statusState.isOpen && !deleteState.isOpen) {
      setPlan(undefined);
    }
  }, [state.isOpen, statusState.isOpen, deleteState.isOpen]);  

  return (
    <PaginationAwareContainer meta={meta} onLoad={fetchNextPage} fetching={isLoading}>
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">Subscription Plan List</div>
          <div className="flex flex-row space-x-2 overflow-hidden">
            <AddButton label="Add Subscription Plan" onPress={state.open} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <span></span>
          <span className="col-span-2">Annual Price</span>
          <span className="col-span-2">Monthly Price</span>
          <span className="col-span-2">Status</span>
          <span className="col-span-3">Created At</span>
          <span></span>
        </div>
        <div className="flex-grow">
          {error ? (
            <span>Error: {JSON.stringify(error)}</span>
          ) : (
            <PaginatedDataContainer meta={meta}>
              <>
                {data?.pages
                  .flatMap((dat) => dat.data)
                  .map((pln: SubscriptionPlan, index: number) => (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      <>
                        <SubscriptionPlanRowDetails index={index} plan={pln} />
                        <div className="col-span-2 flex flex-row items-center justify-end space-x-4 text-white">
                          <ActionButton
                            onEdit={() => {
                              setPlan(pln);
                              state.open();
                            }}
														{...(!pln?.isActive &&
                              {
                                onActivate: () => {
                                  setPlan(pln);
                                  statusState.open();
                                },
                                onDelete: () => {
                                  setPlan(pln);
                                  deleteState.open();
                                }
                              }
                            )}
                          />
                        </div>
                      </>
                    </motion.div>
                  ))}
              </>
            </PaginatedDataContainer>
          )}
        </div>
        <SubscriptionPlanForm state={state} plan={plan} refetch={refetch} />
				<NormalDialog
          state={statusState}
          title={`Activate Subscription Plan`}
          confirmLabel={"Activate"}
          verifyText={"ACTIVATE"}
          loading={loading}
          onCancel={() => {
            setPlan(undefined);
            statusState.close();
          }}
          onAccept={handleActivatePlan}
					primaryButtonColor={true}
        >
          <p className="text-sm">
            Are you sure you want to <strong>Activate</strong> this Subscription Plan?
						<br />
						{!plan?.isActive && "Activating this plan will deactivate the current active plan."}
          </p>
        </NormalDialog>

        <NormalDialog
          state={deleteState}
          title={'Delete Subscription Plan'}
          confirmLabel={"Delete"}
          verifyText={"DELETE"}
          loading={loading}
          onCancel={() => {
            setPlan(undefined);
            deleteState.close();
          }}
          onAccept={handleDeletePlan}
					primaryButtonColor={true}
        >
          <p className="text-sm">
            Are you sure you want to <strong>Delete</strong> this Subscription Plan?
          </p>
        </NormalDialog>
      </div>
    </PaginationAwareContainer>
  );
};

export default SubscriptionListPage;

const SubscriptionPlanRowDetails: FC<{ index: number; plan: SubscriptionPlan }> = ({ index, plan }) => {
  return (
    <>
      <div className="flex items-center">
        <div>
          <div className="w-8 h-8 rounded-full font-semibold flex items-center justify-center bg-gray-100">
            {index + 1}
          </div>
        </div>
      </div>
      <div className="col-span-2 flex items-center">
        <strong>$ {plan.annualPrice}</strong>
      </div>
      <div className="col-span-2 flex items-center">
        <strong>$ {plan.monthlyPrice}</strong>
      </div>
      <div className="col-span-2 flex items-center">
        <strong className={plan.isActive ? 'text-green-600' : 'text-red-500'}>
          {plan.isActive ? 'Active' : 'Inactive'}
        </strong>
      </div>
      <div className="col-span-3 flex items-center">
        <strong>
          {dayjs(plan.createdAt).format("DD MMM YYYY")},{" "}
          {dayjs(plan.createdAt).fromNow()}
        </strong>
      </div>
    </>
  );
};
