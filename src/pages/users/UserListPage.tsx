import PaginatedDataContainer from "@/components/PaginatedDataContainer";
import VerificationDateComponent from "@/components/VerificationDateComponent";
import UserRepository from "@/repositories/UserRepository";
import { User } from "@/types/user.interface";
import { getFullName } from "@/utils/app.util";
import { useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { FC, FormEvent, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { useOverlayTriggerState } from "react-stately";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import Meta from "@/types/meta.interface";
import InputComponent from "@/components/forms/InputComponent";
import { debounce } from "lodash";
import ActionButton from "@/components/buttons/ActionButton";
import FormDialog from "@/components/dialogs/FormDialog";
import NormalDialog from "@/components/dialogs/NormalDialog";
import DeleteDialog from "@/components/dialogs/DeleteDialog";

interface IApplicationProps {}

const UserListPage: FC<IApplicationProps> = () => {
  const userRepo = new UserRepository();
  const { ref } = useInView();
  const deleteState = useOverlayTriggerState({});
  const deactivateState = useOverlayTriggerState({});
  const resetPasswordState = useOverlayTriggerState({});

  const [params, setParams] = useState({});
  const [user, setUser] = useState<User>();
  const [enteredEmail, setEnteredEmail] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
    queryKey: ["users", params],
    queryFn: async ({ pageParam = 1 }) => {
      return userRepo.getAll({
        perPage: 20,
        page: pageParam,
        ...params,
      });
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  const debouncedSearch = debounce((value: string) => {
    setParams({ ...params, search: value });
  }, 500);

  const handleResetPasswordSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!user || enteredEmail !== user.email) {
			setErrorMessage("Entered email does not match the user's email")
			return
		}

    setLoading(true);
    const res = await userRepo.resetPassword("/auth/forgot-password", enteredEmail);
    if (res) {
      setEnteredEmail("");
      resetPasswordState.close();
    }
    setLoading(false);
  };

  const getUserRowClasses = (userParam: User): string => {
		if (userParam.deactivatedAt) {
			return 'opacity-50 text-gray-500';
		}
		return '';
	};

  return (
    <PaginationAwareContainer
      meta={meta}
      onLoad={fetchNextPage}
      fetching={isLoading}
    >
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">User List</div>
          {/* <div>
            <AddButton label="Add User" onPress={state.open} />
          </div> */}
        </div>
        <div className="flex-none grid grid-cols-12">
          <div className="col-span-3">
            <InputComponent id="search" placeholder="Search..." aria-labelledby="search" onChange={debouncedSearch} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <span></span>
          <div className="col-span-4 md:col-span-3">Name</div>
          <div className="col-span-4 md:col-span-3">Email</div>
          <div className="col-span-4 md:col-span-2">Phone</div>
          <div className="col-span-2 md:col-span-1">Role</div>
          <div className="col-span-2"></div>
        </div>
        <div className="flex-grow">
          {error ? (
            <span>Error: {JSON.stringify(error)}</span>
          ) : (
            <PaginatedDataContainer meta={meta}>
              <>
                {data?.pages
                  .flatMap((pageData: any) => pageData.data)
                  .map((userItem: User, index: number) => (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      {userItem && (
                        <>
                          <div className="flex items-center">
                            <div>
                              <div
                                className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center bg-gray-100`}
                              >
                                {index + 1}
                              </div>
                            </div>
                          </div>
                          <div className={`col-span-4 md:col-span-3 flex items-center ${getUserRowClasses(userItem)}`}>
                            <strong>
                              {getFullName(userItem.firstname, userItem.lastname)}
                            </strong>
                            {userItem.deactivatedAt && (
                              <span className="text-red-500 ml-2">(Deactivated)</span>
                            )}
                          </div>
                          <div className={`col-span-4 md:col-span-3 flex flex-row items-center text-ellipsis ${getUserRowClasses(userItem)}`}>
                            <span className="text-sm">{userItem.email}</span>
                            {VerificationDateComponent(userItem.emailVerifiedAt)}
                          </div>

                          <UserListPhoneColumn cUser={userItem} className={getUserRowClasses(userItem)} />
                          
                          <div className={`col-span-2 md:col-span-1 flex text-green-700 space-x-1 flex-row items-center justify-start ${getUserRowClasses(userItem)}`}>
                            <i className="fa-duotone fa-lock fa-xs"></i>
                            <small className="font-bold uppercase">
                              {userItem.role}
                            </small>
                          </div>
                          <div className="col-span-2 md:col-span-2 flex flex-row items-center justify-end space-x-2 text-white">
                            <ActionButton
                              {...(userItem?.deactivatedAt
                                ? {
                                    onActivate: () => {
                                      setUser(userItem);
                                      deactivateState.open();
                                    },
                                  }
                                : {
                                    onDeactivate: () => {
                                      setUser(userItem);
                                      deactivateState.open();
                                    },
                                  }
                                )
                              }
                              onResetPassword={() => {
                                setUser(userItem);
                                resetPasswordState.open();
                                setEnteredEmail("");
                              }}
                              isResetPasswordDisabled={!!userItem?.deactivatedAt || !user?.acceptedAt}
                              onDelete={() => {
                                setUser(userItem);
                                deleteState.open();
                              }}
                            />
                          </div>
                        </>
                      )}
                    </motion.div>
                  ))}
              </>
            </PaginatedDataContainer>
          )}
        </div>
        <NormalDialog
          state={deactivateState}
          title={`${user?.deactivatedAt ? "Activate" : "Deactivate"} User Account`}
          confirmLabel={user?.deactivatedAt ? "Activate" : "Deactivate"}
          verifyText={user?.deactivatedAt ? "ACTIVATE" : "DEACTIVATE"}
          loading={loading}
          onCancel={() => {
            setUser(undefined);
            deactivateState.close();
          }}
          onAccept={async () => {
            if (!user?.id) return;
            setLoading(true);
            const res = user.deactivatedAt
              ? await userRepo.activate(`/users/activate/${user.id}`)
              : await userRepo.deactivate(`/users/deactivate/${user.id}`);
            if (res) {
              setLoading(false);
              setUser(undefined);
              refetch();
              deactivateState.close();
            }
          }}
        >
          <p className="text-sm">
            Are you sure you want to {user?.deactivatedAt ? "activate" : "deactivate"} the account of&nbsp;
            <strong>{user ? getFullName(user.firstname, user.lastname) : ''}</strong>?
          </p>
        </NormalDialog>
        <FormDialog
          state={resetPasswordState}
          title="Reset Password"
          confirmLabel="Reset"
          loading={loading}
          onCancel={() => {
            setUser(undefined);
            setEnteredEmail("");
            setErrorMessage(null);
            resetPasswordState.close();
          }}
          onSubmit={handleResetPasswordSubmit}
        >
          <p className="text-sm">
            Please enter the email address associated with{" "}
            <strong>{user ? getFullName(user.firstname, user.lastname) : ""}</strong>
            's account to reset the password. <br /> <br /> <strong>{user?.email}</strong>
          </p>
          <br />
          <InputComponent
            label="Email"
            id="email"
            //type="email"
            value={enteredEmail}
            onChange={(value) => {
              setEnteredEmail(value)
              setErrorMessage(null);
            }}
            isRequired
          />
          {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
        </FormDialog>
        <DeleteDialog
          state={deleteState}
          deletePassword={true}
          title={"Delete User"}
          url={`/users/${user?.id}`}
          refetch={refetch}
        >
          <p className="text-sm">Are you sure you want to delete this user? This process is not reversible.</p>
        </DeleteDialog>
      </div>
    </PaginationAwareContainer>
  );
};

export default UserListPage;

const UserListPhoneColumn: FC<{ cUser: User, className?: string }> = ({ cUser, className = '' }) => {
	return (
    <div className={`col-span-4 md:col-span-2 flex flex-row items-center ${className}`}>
      {cUser.phone ? (
        <>
          <p className="text-sm">{cUser.phone}</p>
          {VerificationDateComponent(cUser.phoneVerifiedAt)}
        </>
      ) : (
        <small className="text-red-500">Phone number not available</small>
      )}
    </div>
  )
}
