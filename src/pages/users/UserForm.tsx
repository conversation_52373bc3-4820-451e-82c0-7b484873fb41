import FormDialog from "@/components/dialogs/FormDialog";
import DateComponent from "@/components/forms/DateComponent";
import InputComponent from "@/components/forms/InputComponent";
import UserRepository from "@/repositories/UserRepository";
import { User } from "@/types/user.interface";
import { FormEvent, useEffect, useState } from "react";
import { OverlayTriggerState } from "react-stately";

type UserFormProps = {
  state: OverlayTriggerState;
  user?: User;
  repo: UserRepository;
  refetch?: () => void;
};

type UserFormData = {
  firstname?: string;
  lastname?: string;
  dob?: string;
  email?: string;
  phone?: string;
};

const UserForm = ({ state, user, repo, refetch }: UserFormProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<UserFormData>({} as UserFormData);
  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    let result;
    setLoading(true);
    if (user?.id) {
      result = await repo.update(user.id, formData);
    } else {
      result = await repo.create(formData);
    }
    setLoading(false);
    if (result) {
      refetch && refetch();
      setFormData({} as UserFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({} as UserFormData);
    state.close();
  };

  useEffect(() => {
    if (user) {
      setFormData({
        firstname: user.firstname,
        lastname: user.lastname,
        dob: user.dob,
        email: user.email,
        phone: user.phone,
      });
    }
  }, [user]);

  return (
    <FormDialog
      title="User Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel="Save"
      loading={loading}
      width="w-[680px]"
      state={state}
    >
      <div className="grid grid-cols-2 gap-4 items-start mb-4">
        <InputComponent
          label={"Firstname"}
          id="firstname"
          onChange={(value) => setFormData({ ...formData, firstname: value })}
          value={formData.firstname || ""}
          isRequired
        />
        <InputComponent
          label={"Lastname"}
          id="lastname"
          onChange={(value) => setFormData({ ...formData, lastname: value })}
          value={formData.lastname || ""}
          isRequired
        />
      </div>
      <div className="grid grid-cols-3 gap-4 items-start mb-4">
        <InputComponent
          label={"E-mail"}
          id="therapy-email"
          onChange={(value) => setFormData({ ...formData, email: value })}
          value={formData.email || ""}
          isRequired
        />
        <InputComponent
          id="phone"
          label="Phone Number"
          value={formData.phone || ""}
          onChange={(value) => setFormData({ ...formData, phone: value })}
        />

        <DateComponent
          label={"Date of Birth"}
          id="dob"
          onChange={(value) => setFormData({ ...formData, dob: value })}
          value={formData.dob || ""}
        />
      </div>
    </FormDialog>
  );
};

export default UserForm;
