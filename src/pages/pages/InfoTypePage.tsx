import InputComponent from "@/components/forms/InputComponent";

type Props = {
  formData: any;
  setFormData: (formData: any) => void;
};

const InfoTypePage = ({ formData, setFormData }: Props) => {
  return (
    <div className="flex flex-col gap-4">
      <InputComponent
        isRequired
        label="Info"
        value={formData.info || ""}
        onChange={(value) => setFormData({ ...formData, info: value })}
      />
      <InputComponent
        label="Extra Info."
        value={formData.extra || ""}
        onChange={(value) => setFormData({ ...formData, extra: value })}
      />
    </div>
  );
};

export default InfoTypePage;
