import { Page } from "@/types/page.interface";
import { useInfiniteQuery } from "@tanstack/react-query";
import { FC, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import { useNavigate, useParams } from "react-router-dom";
import Meta from "@/types/meta.interface";
import PageRepository from "@/repositories/PageRepository";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";

interface IApplicationProps {}

const Sort: FC<IApplicationProps> = () => {
  const repo = new PageRepository();
  const { ref } = useInView();
  const navigate = useNavigate();
  const { category } = useParams();
  const [pages, setPages] = useState<Page[]>([]);

  const { error, data, isLoading, fetchNextPage } = useInfiniteQuery({
    queryKey: ["sort-pages", category],
    queryFn: ({ pageParam = 1 }) => repo.getAll({
      perPage: 100,
      page: pageParam,
      category,
    }),    
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    setPages(data?.pages.flatMap((d) => d.data));
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  const onDragEnd = async (result: any) => {
    const { source, destination } = result;
    const items = Array.from(pages);
    const [reorderedItem] = items.splice(source.index, 1);
    items.splice(destination.index, 0, reorderedItem);
    setPages(items);
  };

  const SaveQuestions = async () => {
    const saveQuestionData = pages.map((page, index) => ({
      id: page.id,
      order: index + 1,
      initialPage: index === 0,
    }));
    await repo.updateOrder(saveQuestionData);
  };

  if (error) {
    return (
      <div
        className="px-16 h-full flex flex-col flex-grow items-center justify-center"
        ref={ref}
      >
        Page has error...
      </div>
    );
  }

  return (
    <PaginationAwareContainer
      meta={meta}
      onLoad={fetchNextPage}
      fetching={isLoading}
    >
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">Reorder Pages</div>
          <div className="flex flex-row space-x-2">
            <button
              onClick={SaveQuestions}
              className="text-white border rounded-lg py-1.5 bg-neutral-500 px-2 active:scale-95 transition cursor-pointer text-sm"
            >
              <i className="fa-duotone fa-save fa-sm"></i> Save Order
            </button>
            <button
              onClick={() => navigate(-1)}
              className="text-white border rounded-lg py-1.5 bg-neutral-500 px-2 active:scale-95 transition cursor-pointer text-sm"
            >
              Go Back
            </button>
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <span></span>
          <span className="col-span-2">Code</span>
          <span className="col-span-5">Title</span>
          <span className="col-span-2">Type</span>
          <span className="col-span-2"></span>
        </div>
        <div className="flex-grow">
          <DragDropContext onDragEnd={onDragEnd}>
            <div>
              <Droppable key="droppable" droppableId="droppable">
                {(provided) => (
                  <div
                    className="flex-grow"
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                    {pages.map((page: Page, index: number) => (
                      <Draggable
                        key={page.id}
                        draggableId={page.id.toString()}
                        index={index}
                      >
                        {(providedInnerRef) => (
                          <div
                            ref={providedInnerRef.innerRef}
                            {...providedInnerRef.draggableProps}
                            {...providedInnerRef.dragHandleProps}
                            className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                            key={page.id}
                          >
                            <div className="flex items-center">
                              <div
                                className={`w-8 h-8 rounded-full bg-gray-100 font-semibold flex items-center justify-center`}
                              >
                                {index + 1}
                              </div>
                            </div>
                            <div className="col-span-2 flex items-center text-black">
                              <strong>{page.code}</strong>
                            </div>
                            <div className="col-span-5 flex items-center text-black">
                              <strong>{page.title}</strong>
                            </div>
                            <div className="col-span-2 flex items-center text-black">
                              <strong>{page.type}</strong>
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  </div>
                )}
              </Droppable>
            </div>
          </DragDropContext>
        </div>
      </div>
    </PaginationAwareContainer>
  );
};

export default Sort;
