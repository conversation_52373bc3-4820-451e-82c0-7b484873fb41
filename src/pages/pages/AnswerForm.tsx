import NormalDialog from "@/components/dialogs/NormalDialog";
import CheckBoxComponent from "@/components/forms/CheckBoxComponent";
import InputComponent from "@/components/forms/InputComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import PageRepository from "@/repositories/PageRepository";
import OptionItem from "@/types/option.interface";
import { Page } from "@/types/page.interface";
import { debounce } from "lodash";
import { Fragment, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { createFilter } from "react-select";
import AsyncSelect from "react-select/async";
import { OverlayTriggerState } from "react-stately";

type ConditionProps = {
  state: OverlayTriggerState;
  answer?: any;
  category?: string | number;
  setAnswer: (ans: any) => void;
};

const AnswerForm = ({ state, answer, category, setAnswer }: ConditionProps) => {
  const { pageId } = useParams();
  const pageRepository = new PageRepository();
  
  const [infoData, setInfoData] = useState<any>({
    checked: false,
    info: "",
    title: "",
    show_on_select: false,
    show_on_demand: false,
  });
  const [pageData, setPageData] = useState<any>({
    checked: false,
    pageId: null,
    pageTitle: null,
  });
  const [ansOptionsData, setAnsOptionsData] = useState<any>({
    checked: false,
    ansOptions: "",
  });
  const [isDefaultAns, setIsDefaultAns] = useState<any>({
    checked: false,
  });
  const [page, setPage] = useState<OptionItem>();

  const onCancel = () => {
    reset();
  };

  const reset = () => {
    setInfoData({
      checked: false,
      info: "",
      title: "",
    });
    setPageData({
      checked: false,
      pageId: null,
      pageTitle: null,
    });
    setAnsOptionsData({
      checked: false,
      ansOptions: "",
    });
    setIsDefaultAns({
      checked: false,
    });
    setPage(undefined);
  };

  const onClose = () => {
    state.close();
  }

  const onSubmit = () => {
    const conditions = {
      info: infoData,
      page: pageData,
      ansOptions: {
        ...ansOptionsData,
        ansOptions:
          ansOptionsData.ansOptions.trim() !== ""
            ? ansOptionsData.ansOptions
                .split(",")
                .map((option: string) => option.trim())
                .filter(Boolean)
            : [],
      },
      isDefault: isDefaultAns,
    };

    const updatedAnswer = {
     ...(answer ?? {}),
      conditions: answer.conditions ? { ...answer.conditions, ...conditions } : conditions,
    };
  
    setAnswer(updatedAnswer);

    reset();
    state.close();
  };

  const loadPages = (search: string, callback: (options: OptionItem[]) => void) => {
    pageRepository
      .getAll({ search, perPage: 10, exclude: [pageId], category })
      .then((res) => {
        callback(
          res.data.map((inPage: Page) => ({
            label: `${inPage.code} - ${inPage.title}`,
            value: inPage.id,
          })),
        );
      })
      .catch(() => {
        callback([]);
      });
  };

  useEffect(() => {
    if (answer) {
      setInfoData(answer.conditions?.info ? answer.conditions.info : infoData);
      setPageData(answer.conditions?.page ? answer.conditions.page : pageData);
      
      if (Array.isArray(answer.conditions?.ansOptions?.ansOptions)) {
        setAnsOptionsData({
          ...ansOptionsData,
          checked: answer.conditions.ansOptions.checked,
          ansOptions: answer.conditions.ansOptions.ansOptions.join(", "),
        });
      } else {
        setAnsOptionsData(answer.conditions?.ansOptions ? answer.conditions.ansOptions : ansOptionsData);
      }

      if (answer.conditions?.page) {
        setPage({
          label: answer.conditions.page.pageTitle,
          value: answer.conditions.page.pageId,
        });
      }

      setIsDefaultAns(answer.conditions?.isDefault ? answer.conditions.isDefault : isDefaultAns);
    }
  }, [answer]);

  return (
    <NormalDialog
      title="Answer Logic"
      onCancel={onCancel}
      onAccept={onSubmit}
      // onSubmit={onSubmit}
      onClose={onClose}
      confirmLabel="Save"
      // width="w-[420px]"
      state={state}
    >
      <div className="text-xs mb-2">
        Add conditions that needs to be met whenever this answer is selected by a user.
      </div>
      <div className="flex flex-col space-y-4">
        <div className="flex flex-row gap-2">
          <CheckBoxComponent
            checked={infoData.checked || false}
            onChange={(value: boolean) => {
              if (value) {
                setInfoData({ ...infoData, checked: value });
              } else {
                setInfoData({ checked: false, info: "", title: "" });
              }
            }}
            label={"Info Dialog"}
          />
          <CheckBoxComponent
            checked={pageData.checked || false}
            onChange={(value: boolean) => {
              if (value) {
                setPageData({ ...pageData, checked: value });
              } else {
                setPageData({ checked: false, pageId: null, pageTitle: null });
                setPage(undefined);
              }
            }}
            label={"Page Jump"}
          />
          <CheckBoxComponent
            checked={ansOptionsData.checked || false}
            onChange={(value: boolean) => {
              if (value) {
                setAnsOptionsData({ ...ansOptionsData, checked: value });
              } else {
                setAnsOptionsData({ checked: false, ansOptions: "" });
              }
            }}
            label={"Options"}
          />
          <CheckBoxComponent
            checked={isDefaultAns.checked || false}
            onChange={(value: boolean) => {
              if (value) {
                setIsDefaultAns({ checked: value });
              } else {
                setIsDefaultAns({ checked: false });
              }
            }}
            label={"Set as Default"}
          />
        </div>
        {pageData.checked && (
          <section className="flex-none col-span-4">
            <label className="text-sm">
              Page
              <span className="text-red-500 font-bold ml-1">*</span>
            </label>
            <AsyncSelect
              menuPortalTarget={document.body}
              styles={{ menuPortal: (base) => ({ ...base, zIndex: 99999999 }) }}
              id="page"
              instanceId="page"
              isClearable={true}
              filterOption={createFilter({ ignoreAccents: false })}
              unstyled
              placeholder="Select page"
              loadOptions={debounce(loadPages, 500)}
              defaultOptions={true}
              classNames={{
                control: () =>
                  "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                placeholder: () => "text-gray-400 cursor-pointer",
                option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
                noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                loadingMessage: () => "text-gray-400 text-sm py-2",
                valueContainer: () => "cursor-pointer",
              }}
              menuPlacement="auto"
              value={page}
              onChange={(value) => {
                setPage(value as OptionItem);
                setPageData({
                  ...pageData,
                  pageId: value?.value,
                  pageTitle: value?.label,
                });
              }}
            />
          </section>
        )}
        {infoData.checked && (
          <Fragment>
            <div className="flex-1">
              <label className="text-sm">Choose when to show info:</label>
              <AsyncSelect
                // isDisabled={}
                id="show-info"
                instanceId="show-info"
                isClearable={true}
                filterOption={createFilter({ ignoreAccents: false })}
                unstyled
                isMulti
                placeholder="Select when to show info"
                defaultOptions={[
                  { value: "show_on_select", label: "Show on Select" },
                  { value: "show_on_demand", label: "Show on Demand" },
                ]}
                classNames={{
                  control: () =>
                    "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                  menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                  placeholder: () => "text-gray-400 cursor-pointer",
                  option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                  multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
                  noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                  loadingMessage: () => "text-gray-400 text-sm py-2",
                  valueContainer: () => "cursor-pointer",
                }}
                menuPlacement="auto"
                value={
                  [
                    { value: "show_on_select", label: "Show on Select" },
                    { value: "show_on_demand", label: "Show on Demand" },
                  ].filter(option => infoData[option.value])
                }
                onChange={(value) => {
                  if (value) {
                    setInfoData({
                      ...infoData,
                      show_on_select: value.some(val => val.value === "show_on_select"),
                      show_on_demand: value.some(val => val.value === "show_on_demand"),
                    });
                  }
                }}
              />
            </div>
            <InputComponent
              isRequired
              label="Title"
              placeholder="title"
              value={infoData.title || ""}
              onChange={(value) => setInfoData({ ...infoData, title: value })}
            />
            <TextAreaComponent
              label={"Info"}
              id="info"
              onChange={(value) => setInfoData({ ...infoData, info: value })}
              value={infoData.info || ""}
            />
          </Fragment>
        )}
        {ansOptionsData.checked && (
          <InputComponent
            isRequired
            label="Answer Options"
            placeholder="option1, option2, option3, ....."
            value={ansOptionsData.ansOptions || ""}
            onChange={(value) => setAnsOptionsData({ ...ansOptionsData, ansOptions: value })}
          />
        )}
      </div>
    </NormalDialog>
  );
};

export default AnswerForm;
