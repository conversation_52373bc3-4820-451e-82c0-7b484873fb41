import QuestionnaireRepository from "@/repositories/QuestionnaireRepository";
import OptionItem from "@/types/option.interface";
import { Questionnaire } from "@/types/questionnaire.interface";
import { debounce } from "lodash";
import { Fragment, useMemo } from "react";
import { createFilter } from "react-select";
import AsyncSelect from "react-select/async";
import PerfectScrollbar from "react-perfect-scrollbar";
import AnswerRepository from "@/repositories/AnswerRepository";
import { Answer } from "@/types/answer.interface";
import { useOverlayTriggerState } from "react-stately";
import NormalDialog from "@/components/dialogs/NormalDialog";
import ConditionPage from "./AnswerForm";
import { v4 as uuidv4 } from "uuid";

type CustomOptionItem = {
  conditions?: any;
  answerGroup?: string;
  uid?: string;
  position?: string;
} & OptionItem;

type Props = {
  formData: any;
  setFormData: (formData: any) => void;
  question?: OptionItem;
  setQuestion: (question: OptionItem) => void;
  answers: CustomOptionItem[];
  setAnswers: (answers: CustomOptionItem[]) => void;
  answer?: CustomOptionItem;
  setAnswer: (answer?: CustomOptionItem) => void;
  removedAnswers: (string | number)[];
  setRemovedAnswers: (data: any) => void;
};

const ValueTypePage = ({
  formData,
  question,
  setQuestion,
  answers,
  setAnswers,
  answer,
  setAnswer,
  removedAnswers,
  setRemovedAnswers,
}: Props) => {
  const questionnaireRepo = new QuestionnaireRepository();
  const answerRepo = new AnswerRepository();

  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});
  
  const loadQuestions = (
    search: string,
    callback: (options: OptionItem[]) => void
  ) => {
    questionnaireRepo
      .getAll({ search })
      .then((res) => {
        callback(
          res.data.map((q: Questionnaire) => ({
            label: q.question,
            value: q.id,
          }))
        );
      })
      .catch(() => {
        callback([]);
      });
  };

  const loadAnswers = (
    search: string,
    callback: (options: CustomOptionItem[]) => void
  ) => {
    answerRepo
      .getAll({ search, perPage: 20 })
      .then((res) => {
        callback(
          res.data.map((ans: Answer) => ({
            label: ans.answer,
            value: ans.id,
            conditions: {},
            answerGroup: ans.answerGroup,
          }))
        );
      })
      .catch(() => {
        callback([]);
      });
  };

  const groups = useMemo(() => {
    const ansGroups: any = {};
    answers.forEach((item: CustomOptionItem) => {
      // Check if the uid field doesn't exist
      if (!item.uid) {
        // Generate a new uid for each answer
        const uid = uuidv4();
        item.uid = uid;
      }

      if (item.answerGroup) {
        if (ansGroups[item.answerGroup]) {
          ansGroups[item.answerGroup].push(item);
        } else {
          ansGroups[item.answerGroup] = [item];
        }
      }
    });
    return Object.keys(ansGroups).map((group) => ({
      group,
      answers: ansGroups[group],
    }));
  }, [answers]);
  
  return (
    <Fragment>
      <div className="grid grid-cols-5 gap-4">
        <section className="flex-none col-span-4 relative z-[2]">
          <label className="text-sm">
            Question
            <span className="text-red-500 font-bold ml-1">*</span>
          </label>
          <AsyncSelect
            isDisabled={!formData.category}
            id="question"
            instanceId="question"
            isClearable={true}
            filterOption={createFilter({ ignoreAccents: false })}
            unstyled
            placeholder="Select question"
            loadOptions={debounce(loadQuestions, 500)}
            defaultOptions={true}
            classNames={{
              control: () =>
                "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
              menu: () =>
                "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-[10] text-sm",
              placeholder: () => "text-gray-400 cursor-pointer",
              option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
              multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
              noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
              loadingMessage: () => "text-gray-400 text-sm py-2",
              valueContainer: () => "cursor-pointer",
            }}
            menuPlacement="auto"
            value={question}
            onChange={(value) => setQuestion(value as OptionItem)}
          />
        </section>
        <section>
          <label className="text-sm">
            &nbsp;
            <span className="text-red-500 font-bold ml-1"></span>
          </label>
          <button
            disabled={!question || !formData.category}
            onClick={() => {
              const uid = uuidv4();
              const sectionAnswer = [{
                label: "",
                value: "",
                position: "left",
                answerGroup: uid,
                uid: uuidv4(),
                conditions: {},
              } as CustomOptionItem];

              if(formData.type.value === 'value') {
                  sectionAnswer.push({
                    label: "",
                    value: "",
                    position: "right",
                    answerGroup: uid,
                    uid: uuidv4(),
                    conditions: {},
                  } as CustomOptionItem)
              }

              setAnswers([
                ...answers,
                ...sectionAnswer,
              ]);
            }}
            className="border-2 mt-1 w-full ring-0 focus:outline-neutral-500 rounded-lg bg-gray-50 disabled:bg-gray-50 disabled:border-gray-100 disabled:cursor-not-allowed py-2.5 px-2 text-sm hover:bg-gray-200"
            type="button"
          >
            Add Section
          </button>
        </section>
      </div>
      <div className="flex-grow relative overflow-hidden border rounded-md p-4">
        {(!formData.category || !question) && (
          <div className="absolute top-0 left-0 right-0 z-[1] bottom-0 bg-white opacity-80 flex items-center justify-center text-sm">
            Select `Page for:` and `Question` to add section.
          </div>
        )}
        <PerfectScrollbar className="pr-2.5">
          <div className="grid grid-cols-12 pb-2 px-2 text-sm text-gray-400">
            <div className="col-span-9">Sections</div>
            <div className="col-span-3"></div>
          </div>
          {groups.map((group: any, index: number) => (
            <div
              className={`grid border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out ${formData.type.value === 'value' ? ' grid-cols-2' : ''}`}
              key={index}
            >
              <div className="relative">
                <AsyncSelect
                  id={`${group.group}-0`}
                  instanceId={`${group.group}-0`}
                  isClearable={true}
                  filterOption={createFilter({ ignoreAccents: false })}
                  unstyled
                  placeholder="Select answer"
                  loadOptions={debounce(loadAnswers, 500)}
                  defaultOptions={true}
                  classNames={{
                    control: () =>
                      "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                    menu: () =>
                      "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                    placeholder: () => "text-gray-400 cursor-pointer",
                    option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                    multiValue: () =>
                      "bg-primary text-white mr-1 rounded-full px-2",
                    noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                    loadingMessage: () => "text-gray-400 text-sm py-2",
                    valueContainer: () => "cursor-pointer",
                  }}
                  menuPlacement="auto"
                  value={group.answers[0]?.value !== "" ? group.answers[0] : null}
                  onChange={(value: any) => {
                    const ans = [...answers];

                    // find answer with uid
                    const answerFound = ans.find(
                      (a) => a.uid === group.answers[0].uid
                    );

                    if (answerFound) {
                      answerFound.value = value?.value || "";
                      answerFound.label = value?.label || "";
                    }
                    setAnswers(ans);
                  }}
                />
              </div>
              {formData.type.value === 'value' &&
              <div className="relative">
                <AsyncSelect
                  id={`${group.group}-1`}
                  instanceId={`${group.group}-1`}
                  isClearable={true}
                  filterOption={createFilter({ ignoreAccents: false })}
                  unstyled
                  placeholder="Select answer"
                  loadOptions={debounce(loadAnswers, 500)}
                  defaultOptions={true}
                  classNames={{
                    control: () =>
                      "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                    menu: () =>
                      "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                    placeholder: () => "text-gray-400 cursor-pointer",
                    option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                    multiValue: () =>
                      "bg-primary text-white mr-1 rounded-full px-2",
                    noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                    loadingMessage: () => "text-gray-400 text-sm py-2",
                    valueContainer: () => "cursor-pointer",
                  }}
                  menuPlacement="auto"
                  value={group.answers[1]?.value !== "" ? group.answers[1] : null}
                  onChange={(value: any) => {
                    const ans = [...answers];
                    // find answer with uid
                    const answerUid = ans.find(
                      (a) => a.uid === group.answers[1].uid
                    );
                    if (answerUid) {
                      answerUid.value = value?.value || "";
                      answerUid.label = value?.label || "";
                    }
                    setAnswers(ans);
                  }}
                />
              </div>
              }
            </div>
          ))}
          {groups.length === 0 && (
            <div className="px-2 text-sm">No sections added yet.</div>
          )}
        </PerfectScrollbar>
      </div>
      {answer && (
        <NormalDialog
          state={deleteState}
          title={"Remove Answer"}
          confirmLabel={"Delete"}
          onCancel={() => {
            deleteState.close();
            setAnswer(undefined);
          }}
          onAccept={() => {
            deleteState.close();
            const ans = [...answers];
            ans.splice(
              ans.findIndex((a) => a.value === answer.value),
              1
            );
            setRemovedAnswers([...removedAnswers, answer.value]);
            setAnswers(ans);
            setAnswer(undefined);
          }}
        >
          <p className="text-sm">
            Are you sure you want to remove this answer? This process is not
            reversible.
          </p>
        </NormalDialog>
      )}
      <ConditionPage
        state={state}
        category={formData.category?.value}
        answer={answer}
        setAnswer={(ans) => {
          const index = answers.findIndex((a) => a.value === ans.value);
          const ansArr = [...answers];
          ansArr[index] = ans;
          setAnswers(ansArr);
        }}
      />
    </Fragment>
  );
};

export default ValueTypePage;
