import QuestionnaireRepository from "@/repositories/QuestionnaireRepository";
import OptionItem from "@/types/option.interface";
import { Questionnaire } from "@/types/questionnaire.interface";
import { debounce } from "lodash";
import { Fragment, useEffect, useState } from "react";
import Select, { createFilter } from "react-select";
import AsyncSelect from "react-select/async";
import PerfectScrollbar from "react-perfect-scrollbar";
import AnswerRepository from "@/repositories/AnswerRepository";
import { Answer } from "@/types/answer.interface";
import { useOverlayTriggerState } from "react-stately";
import NormalDialog from "@/components/dialogs/NormalDialog";
import ConditionPage from "./AnswerForm";
import CheckBoxComponent from "@/components/forms/CheckBoxComponent";
import { Page } from "@/types/page.interface";

type CustomOptionItem = {
  conditions?: any;
  uid?: string;
  group?: string;
  page?: Page;
  followThrough?: { label: string; value: number };
  order?: number | null;
} & OptionItem;

const selects: OptionItem[] = [
  { label: "Multiple", value: "multiple" },
  { label: "Single", value: "single" },
];

type Props = {
  followThroughAnswers?: OptionItem[];
  formData: any;
  setFormData: (formData: any) => void;
  question?: OptionItem;
  copiedAnswers?: OptionItem[];
  setQuestion: (question: OptionItem) => void;
  answers: CustomOptionItem[];
  setAnswers: (answers: CustomOptionItem[]) => void;
  answer?: CustomOptionItem;
  setAnswer: (answer?: CustomOptionItem) => void;
  removedAnswers: (string | number)[];
  setRemovedAnswers: (data: any) => void;
  loadPages: (search: string, callback: (options: OptionItem[]) => void, params?: any) => void;
};

const QuestionnaireTypePage = ({
  followThroughAnswers,
  formData,
  setFormData,
  question,
  setQuestion,
  answers,
  setAnswers,
  answer,
  setAnswer,
  removedAnswers,
  setRemovedAnswers,
  loadPages,
  copiedAnswers,
}: Props) => {
  const questionnaireRepo = new QuestionnaireRepository();
  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});
  const [page, setPage] = useState<OptionItem[]>([]);
  const [followThroughFor, setFollowThroughFor] = useState<OptionItem[]>([]);

  const addOrder = (item: CustomOptionItem, i: number) => ({
    ...item,
    order: item.order || i + 1,
  });

  const uniqueAnswers =
    followThroughFor?.length > 0
      ? answers.map(addOrder)
      : answers
          .filter(
            (obj, index) =>
              index === answers.findIndex((o) => obj.label === o.label)
          )
          .map(addOrder);
  
  const sortedUniqueAnswers = [...uniqueAnswers].sort((a, b) => {
    if (a.order == null) return 1;
    if (b.order == null) return -1;
    return a.order - b.order;
  });

  const loadQuestions = (search: string, callback: (options: OptionItem[]) => void) => {
    questionnaireRepo
      .getAll({ search })
      .then((res) => {
        callback(
          res.data.map((questions: Questionnaire) => ({
            label: questions.question,
            value: questions.id,
          })),
        );
      })
      .catch(() => {
        callback([]);
      });
  }; 

  useEffect(() => {
    setPage(copiedAnswers!);
  }, []);

  useEffect(() => {
    if (followThroughAnswers && followThroughAnswers.length > 0) {
      const formattedAnswers = followThroughAnswers.map((ans: any) => ({
        label: ans.followingToAnswer.answer,
        value: ans.followingToAnswer.id,
        condition: {},
        followThrough: {
          label: ans.followingByAnswer.answer,
          value: ans.followingByAnswer.id,
        },
      }));

      setAnswers(formattedAnswers);
    }
  }, []);

  return (
    <Fragment>
      <div className="grid grid-cols-5 gap-4">
        <section className="flex-none col-span-4">
          <label className="text-sm">
            Question
            <span className="text-red-500 font-bold ml-1">*</span>
          </label>
          <AsyncSelect
            isDisabled={!formData.category}
            id="question"
            instanceId="question"
            isClearable={true}
            filterOption={createFilter({ ignoreAccents: false })}
            unstyled
            placeholder="Select question"
            loadOptions={debounce(loadQuestions, 500)}
            defaultOptions={true}
            classNames={{
              control: () =>
                "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
              menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
              placeholder: () => "text-gray-400 cursor-pointer",
              option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
              multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
              noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
              loadingMessage: () => "text-gray-400 text-sm py-2",
              valueContainer: () => "cursor-pointer",
            }}
            menuPlacement="auto"
            value={question}
            onChange={(value) => setQuestion(value as OptionItem)}
          />
        </section>
        <section>
          <label className="text-sm">
            Select Type
            <span className="text-red-500 font-bold ml-1">*</span>
          </label>
          <Select
            isDisabled={!question || !formData.category}
            id="question-type"
            instanceId="question-type"
            isClearable={true}
            unstyled
            placeholder="Select"
            classNames={{
              control: () =>
                "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
              menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
              placeholder: () => "text-gray-400 cursor-pointer",
              option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
              multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
              noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
              loadingMessage: () => "text-gray-400 text-sm py-2",
              valueContainer: () => "cursor-pointer",
            }}
            menuPlacement="auto"
            options={selects}
            value={formData.questionnaireType}
            onChange={(value) => {
              setFormData({
                ...formData,
                questionnaireType: value as OptionItem,
              });
            }}
          />
        </section>
      </div>
      <AnswerSelectionSection
        formData={formData}
        question={question}
        followThroughFor={followThroughFor}
        setAnswers={setAnswers}
        answers={answers}
        setFollowThroughFor={setFollowThroughFor}
        page={page}
        setPage={setPage}
        loadPages={loadPages}
      />
      <div className="flex-grow relative overflow-hidden border rounded-md p-4">
        {!formData.category && (
          <div className="absolute top-0 left-0 right-0 bottom-0 bg-white opacity-80 z-10 flex items-center justify-center text-sm">
            Select `Page for:` and `Question` to add answers.
          </div>
        )}
        <PerfectScrollbar className="pr-2.5">
          <div className="grid grid-cols-12 pb-2 px-2 text-sm text-gray-400">
            <div className="col-span-9">Answer</div>
            <div className="col-span-3">
              <section>
                <div className="flex items-center justify-center text-black">
                  <CheckBoxComponent
                    label="Answer Required"
                    onChange={(value) => {
                      setFormData((prev: any) => ({
                        ...prev,
                        required: value,
                      }));
                    }}
                    checked={formData.required}
                  />
                </div>
              </section>
            </div>
          </div>
          <SortedAnswersSection
            sortedAnswers={sortedUniqueAnswers}
            answers={answers}
            setAnswers={setAnswers}
            setAnswer={setAnswer}
            deleteState={deleteState}
            state={state}
          />
          {answers.length === 0 && <div className="px-2 text-sm">No answers selected yet.</div>}
        </PerfectScrollbar>
      </div>
      {answer && (
        <NormalDialog
          state={deleteState}
          title={"Remove Answer"}
          confirmLabel={"Delete"}
          onCancel={() => {
            deleteState.close();
            setAnswer(undefined);
          }}
          onAccept={() => {
            deleteState.close();
            const ans = [...answers];
            ans.splice(
              ans.findIndex((a) => a.value === answer.value),
              1,
            );
            setRemovedAnswers([...removedAnswers, answer.value]);
            setAnswers(ans);
            setAnswer(undefined);
          }}
        >
          <p className="text-sm">Are you sure you want to remove this answer? This process is not reversible.</p>
        </NormalDialog>
      )}
      <ConditionPage
        state={state}
        category={formData.category?.value}
        answer={answer}
        setAnswer={(ans) => {
          const index = answers.findIndex((a) => a.value === ans.value);
          const ansArr = [...answers];
          ansArr[index] = ans;
          setAnswers(ansArr);
        }}
      />
    </Fragment>
  );
};

export default QuestionnaireTypePage;

const SortedAnswersSection = ({
  sortedAnswers,
  answers,
  setAnswers,
  setAnswer,
  deleteState,
  state,
}: {
  sortedAnswers: CustomOptionItem[];
  answers: CustomOptionItem[];
  setAnswers: (a: CustomOptionItem[]) => void;
  setAnswer: (a?: CustomOptionItem) => void;
  deleteState: any;
  state: any;
}) => {
  return (
    <>
      {sortedAnswers.map((ans, index) => (
        <div
          className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
          key={index}
        >
          <div className="col-span-9">
            <div className="flex">
              <input
                value={ans.order ? ans.order.toString() : ""}
                className="remove-arrow"
                onFocus={(e) => e.target.select()}
                type="number"
                style={{
                  width: 40,
                  marginRight: 10,
                  padding: 3,
                  border: "1px solid #ccc",
                  textAlign: "center",
                }}
                onChange={(e) => {
                  const updated = answers.map((a) =>
                    a.value === ans.value ? { ...a, order: parseInt(e.target.value) } : a
                  );
                  setAnswers(updated);
                }}
              />
              <div>
                <div>{ans.label}</div>
                {ans.page && (
                  <div>
                    <small className="text-neutral-400">
                      Copied from{" "}
                      <strong className="text-neutral-500">
                        <u>{ans.page.title}</u>
                      </strong>
                    </small>
                  </div>
                )}
                {ans.followThrough && (
                  <div>
                    <small className="text-neutral-400">
                      Followed By{" "}
                      <strong className="text-neutral-500">
                        <u>{ans.followThrough.label}</u>
                      </strong>
                    </small>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="col-span-3 flex flex-row items-center justify-end">
            <i
             data-testid="delete-button"
              onClick={() => {
                setAnswer(ans);
                deleteState.open();
              }}
              className="fas fa-remove text-primary/80 hover:text-red-700 cursor-pointer mr-4"
            ></i>
            <i
              onClick={() => {
                setAnswer(ans);
                state.open();
              }}
              className="fad fa-gears text-red-500 hover:text-red-700 cursor-pointer"
            ></i>
          </div>
        </div>
      ))}
    </>
  );
};

const AnswerSelectionSection = ({
  formData,
  question,
  followThroughFor,
  setAnswers,
  answers,
  setFollowThroughFor,
  page,
  setPage,
  loadPages,
}: {
  formData: any;
  question?: OptionItem;
  followThroughFor: OptionItem[];
  setAnswers: (answers: CustomOptionItem[]) => void;
  answers: CustomOptionItem[];
  setFollowThroughFor: (value: OptionItem[]) => void;
  page: OptionItem[];
  setPage: (value: OptionItem[]) => void;
  loadPages: (search: string, callback: (options: OptionItem[]) => void, params?: any) => void;
}) => {
  const answerRepo = new AnswerRepository();

  const loadAnswers = (search: string, callback: (options: CustomOptionItem[]) => void) => {
    answerRepo
      .getAll({ search, perPage: 20 })
      .then((res) => {
        callback(
          res.data.map((ans: Answer) => ({
            label: ans.answer,
            value: ans.id,
            conditions: {},
          })),
        );
      })
      .catch(() => {
        callback([]);
      });
  };

  const getDebouncedLoadOptions = () =>
    debounce((search, callback) => {
      return loadPages(search, callback, {
        type: "questionnaire",
      });
    }, 500);

  useEffect(() => {
    if (followThroughFor && followThroughFor.length > 0) {
      answerRepo
        .getAll({
          perPage: 200,
          pageId: followThroughFor.map((item: OptionItem) => item.value).join(","),
          followThrough: true,
        })
        .then((res) => {
          const ans = res.data.map((a: any) => {
            return {
              label: a.followingToAnswer.answer,
              value: a.followingToAnswer.id,
              condition: {},
              followThrough: {
                label: a.followingByAnswer.answer,
                value: a.followingByAnswer.id,
              },
            };
          });
          setAnswers([...ans]);
        });
    }
  }, [followThroughFor]);

  return (
    <section className="flex-none flex flex-row w-full">
      <div className="flex-1">
        <label className="text-sm">
          Select Answer
          <span className="text-red-500 font-bold ml-1">*</span>
        </label>
        <AsyncSelect
          isDisabled={!formData.category || !question || followThroughFor.length > 0}
          id="answer"
          instanceId="answer"
          isClearable={true}
          filterOption={createFilter({ ignoreAccents: false })}
          unstyled
          placeholder="Select answer"
          loadOptions={debounce(loadAnswers, 500)}
          defaultOptions={true}
          classNames={{
            control: () =>
              "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
            menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
            placeholder: () => "text-gray-400 cursor-pointer",
            option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
            multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
            noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
            loadingMessage: () => "text-gray-400 text-sm py-2",
            valueContainer: () => "cursor-pointer",
          }}
          menuPlacement="auto"
          value={{}}
          onChange={(value) => {
            if (value) {
              setAnswers([...answers, value as OptionItem]);
            }
          }}
        />
      </div>
      <div className="flex items-center justify-center px-4 text-sm">
        <span className="pt-6">OR</span>
      </div>
      <div className="flex-1">
        <label className="text-sm">Copy answers from:</label>
        <AsyncSelect
          isDisabled={!formData.category || !question || followThroughFor.length > 0}
          id="page"
          instanceId="page"
          isClearable={true}
          filterOption={createFilter({ ignoreAccents: false })}
          unstyled
          isMulti
          placeholder="Select page"
          loadOptions={getDebouncedLoadOptions()}
          defaultOptions={true}
          classNames={{
            control: () =>
              "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
            menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
            placeholder: () => "text-gray-400 cursor-pointer",
            option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
            multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
            noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
            loadingMessage: () => "text-gray-400 text-sm py-2",
            valueContainer: () => "cursor-pointer",
          }}
          menuPlacement="auto"
          value={page}
          onChange={(value) => {
            if (!value) return;
            setAnswers([]);
            setPage(value as OptionItem[]);
            answerRepo
              .getAll({
                perPage: 200,
                pageId: value.map((item: OptionItem) => item.value).join(","),
              })
              .then((res) => {
                const ans = res.data.map((a: Answer & { pages?: Page[] }) => ({
                  label: a.answer,
                  value: a.id,
                  conditions: {},
                  page: a.pages ? a.pages[0] : null,
                }));
                setAnswers(ans);
              });
          }}
        />
      </div>

      <div className="flex items-center justify-center px-4 text-sm">
        <span className="pt-6">OR</span>
      </div>
      <div className="flex-1">
        <label className="text-sm">Create dynamic follow through:</label>
        <AsyncSelect
          isDisabled={!formData.category || !question}
          id="page"
          instanceId="page"
          isClearable={true}
          filterOption={createFilter({ ignoreAccents: false })}
          unstyled
          isMulti
          placeholder="Select page"
          loadOptions={getDebouncedLoadOptions()}
          defaultOptions={true}
          classNames={{
            control: () =>
              "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
            menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
            placeholder: () => "text-gray-400 cursor-pointer",
            option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
            multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
            noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
            loadingMessage: () => "text-gray-400 text-sm py-2",
            valueContainer: () => "cursor-pointer",
          }}
          menuPlacement="auto"
          value={followThroughFor}
          onChange={(value) => {
            if (value) {
              setAnswers([]);
              setFollowThroughFor(value as OptionItem[]);
            }
          }}
        />
      </div>
    </section>
  );
};
