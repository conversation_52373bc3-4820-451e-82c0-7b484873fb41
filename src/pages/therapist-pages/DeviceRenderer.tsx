import Device from "@/components/mobile/device";
import OptionItem from "@/types/option.interface";
import { Fragment, useMemo } from "react";

type CustomOptionItem = {
  conditions?: any;
  answerGroup?: string;
  uid?: string;
} & OptionItem;

type Props = {
  formData: any;
  question: any;
  answers: any;
};

type QuestionnaireSectionProps = {
  data: any;
  question: any;
  answers: CustomOptionItem[];
};

const InfoSection = ({ data }: any) => {
  return (
    <Fragment>
      {data.title && (
        <div className="mt-16 text-center font-[600] muller text-[24px] mb-4">
          {data.title}
        </div>
      )}
      {data.info && (
        <div className={`mb-4 px-4 text-center ${!data.title && "mt-16"}`}>
          {data.info}
        </div>
      )}
      {data.extra && (
        <div className="text-[14] mb-4 px-4 text-center">{data.extra}</div>
      )}
    </Fragment>
  );
};

const QuestionnaireSection = ({
  data,
  question,
  answers,
}: QuestionnaireSectionProps) => {
  return (
    <Fragment>
      {question && (
        <div className="font-[700] leading-tight text-therapy-blue text-[24] mb-4">
          {question.label}
        </div>
      )}
      <div className="flex flex-col space-y-2">
        {answers.map((ans, index) => (
          <div
            className="flex flex-row items-start justify-center text-sm"
            key={index}
          >
            <div role="presentation"
              className={`flex-none bg-primary/80 w-4 h-4 inline-block ${
                data.questionnaireType?.value === "multiple"
                  ? "rounded"
                  : "rounded-full"
              } mr-2`}
            ></div>
            <div className="flex-grow text-sm">{ans.label}</div>
          </div>
        ))}
      </div>
    </Fragment>
  );
};

const ValueSection = ({ question, answers }: QuestionnaireSectionProps) => {
  const groups = useMemo(() => {
    const ansGroups: any = {};
    answers.forEach((answer: CustomOptionItem) => {
      if (answer.answerGroup) {
        if (ansGroups[answer.answerGroup]) {
          ansGroups[answer.answerGroup].push(answer);
        } else {
          ansGroups[answer.answerGroup] = [answer];
        }
      }
    });
    return Object.keys(ansGroups).map((group) => ({
      group,
      answers: ansGroups[group],
    }));
  }, [answers]);
  return (
    <Fragment>
      {question && (
        <div className="font-[700] text-center leading-tight text-therapy-blue text-[24] mb-4">
          {question.label}
        </div>
      )}
      <div className="flex flex-col space-y-2">
        {groups.map((group, index: number) => (
          <div key={index} className="border-b pb-2">
            <div className="grid grid-cols-2 text-xs font-medium">
              <span className="text-left">{group.answers[0].label}</span>
              <span className="text-right">{group.answers[1].label}</span>
            </div>
            <div className="mt-2 grid grid-cols-5 gap-2" key={index}>
              <div className="text-[10px] flex flex-col items-center leading-tight">
                <span className="w-4 h-4 rounded-full bg-gray-200"></span>
                <span className="mt-1.5">Most</span>
              </div>
              <div className="text-[10px] flex flex-col items-center text-center leading-tight">
                <span className="w-4 h-4 rounded-full bg-gray-200"></span>
                <span className="mt-1.5">Slightly More</span>
              </div>
              <div className="text-[10px] flex flex-col items-center text-center leading-tight">
                <span className="w-4 h-4 rounded-full bg-gray-200"></span>
                <span className="mt-1.5">Both Equally</span>
              </div>
              <div className="text-[10px] flex flex-col items-center text-center leading-tight">
                <span className="w-4 h-4 rounded-full bg-gray-200"></span>
                <span className="mt-1.5">Slightly More</span>
              </div>
              <div className="text-[10px] flex flex-col items-center text-center leading-tight">
                <span className="w-4 h-4 rounded-full bg-gray-200"></span>
                <span className="mt-1.5">Most</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Fragment>
  );
};

const DeviceRenderer = ({ formData, question, answers }: Props) => {
  const template = useMemo(() => {
    if (formData.type?.value === "info") {
      return <InfoSection data={formData} />;
    } else if (formData.type?.value === "questionnaire") {
      return (
        <QuestionnaireSection
          data={formData}
          question={question}
          answers={answers}
        />
      );
    } else if (formData.type?.value === "value") {
      return (
        <ValueSection data={formData} question={question} answers={answers} />
      );
    } else if (formData.type?.value !== undefined) {
      return (
        <div className="flex-grow text-sm h-full w-full flex flex-col items-center justify-center">
          <span className="font-semibold">Registration Page</span>
          <span className="text-xs text-therapy-blue">
            Preview not available
          </span>
        </div>
      );
    } else {
      return <span>Page not selected</span>;
    }
  }, [formData, question, answers]);
  return (
    <Device title={formData.title} type={formData.type?.value}>
      {template}
      {["info", "questionnaire", "value"].includes(formData.type?.value) && (
        <span
          className={`${
            formData.type?.value === "info" ? "absolute bottom-6" : "mt-2 mb-2"
          } py-2 left-4 right-4 block text-center rounded-full muller text-sm bg-therapy-blue text-white`}
        >
          {formData.button}
        </span>
      )}
    </Device>
  );
};

export default DeviceRenderer;
