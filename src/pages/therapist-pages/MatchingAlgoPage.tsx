import TherapistPagesRepository from "@/repositories/TherapistPagesRepository";
import PageRepository from "@/repositories/PageRepository";
import MatchingAlgoRepository from "@/repositories/MatchingAlgoRepository";
import Select, { createFilter } from "react-select";
import AsyncSelect from "react-select/async";
import OptionItem from "@/types/option.interface";
import { useQuery } from "@tanstack/react-query";
import { FC, useEffect, useState } from "react";
import { TherapistPage } from "@/types/therapist-page.interface";
import { motion } from "framer-motion";
import { debounce } from "lodash";
import { Page } from "@/types/page.interface";
import { Answer } from "@/types/answer.interface";

interface IApplicationProps {}

type MatchingAlgoFormData = {
  therapistPage: OptionItem;
  category: OptionItem;
  patientPage: OptionItem;
  question: OptionItem;
  answer: OptionItem;
};

type SearchParams = {
  search?: string;
  category?: string | number;
};

const categories: OptionItem[] = [
  { label: "MySelf", value: "self" },
  { label: "Couple", value: "couple" },
  { label: "Child", value: "child" },
  { label: "Teen", value: "teen" },
];

const MatchingAlgoPage: FC<IApplicationProps> = () => {
  const thPagesRepo = new TherapistPagesRepository();
  const pageRepo = new PageRepository();
  const matchingAlgoRepo = new MatchingAlgoRepository();
  
  const [params, ] = useState({
    search: "",
    category: "self",
  } as SearchParams);

  const [formData, setFormData] = useState<{ [key: number]: MatchingAlgoFormData }>({});

  const [loading, setLoading] = useState(false);

  const { data, error } = useQuery({
    queryKey: ["therapistPages"],
    queryFn: () => thPagesRepo.getAll(),
    refetchOnWindowFocus: false,
  });

  const { data: matchingAlgoData } = useQuery({
    queryKey: ["matchingAlgoResources"],
    queryFn: () => matchingAlgoRepo.getAll({
      ...params
    }),
    refetchOnWindowFocus: false,
  });

  //run after save data button is clicked
  const handleSubmit = async () => {
    setLoading(true);
    await matchingAlgoRepo.create(formData);
    setLoading(false);
  };

  useEffect(() => {
    if (matchingAlgoData) {
      const initialFormData: { [key: number]: MatchingAlgoFormData } = {};
      matchingAlgoData.forEach((item: any) => {
        initialFormData[item.therapistPageId] = {
          therapistPage: {
            label: item.therapistPage.pageName,
            value: item.therapistPage.id
          },
          category: {
            label: categories.find(category => category.value === item.category)?.label || "",
            value: item.category
          },
          patientPage: {
            label: `${item.patientPage.code} - ${item.patientPage.title}`,
            value: item.patientPage.id,
            // questionnaireId: item.patientPage.questionnaireId
          },
          question: {
            label: item.questionnaire.question,
            value: item.questionnaire.id
          },
          answer: {
            label: item.answer?.answer,
            value: item.answer?.id
          }
        };
      });
      setFormData(initialFormData);
    }
  }, [matchingAlgoData]);

  const loadPages = 
    (search: string, callback: (options: OptionItem[]) => void, index: number, param: any = {}) => {
    const formParam: any = {
      search,
      //"exclude[]": pageId,
      ...param,
    };
    if (formData[index]?.category?.value) {
      formParam["category"] = formData[index].category?.value;
    }
    pageRepo
      .getAll(formParam)
      .then((res) => {
        callback([
          ...res.data.map((page: Page) => ({
            label: `${page.code} - ${page.title}`,
            value: page.id,
            // questionnaireId: page.questionnaireId,
          })),
        ]);
      })
      .catch(() => {
        callback([]);
      });
  };
  
  const loadQuestion = (search: string, callback: (options: OptionItem[]) => void, index: number) => {
    const pageId = formData[index]?.patientPage?.value;
  
    if (pageId) {
        pageRepo
          .getOne(pageId as number)
          .then((res) => {
            const pageQuestion = {
              label: res.questionnaire.question,
              value: res.questionnaire.id,
            };
            setFormData((prevFormData) => {
              const newFormData = {...prevFormData};
              newFormData[index] = { ...newFormData[index], question: pageQuestion as OptionItem };
              return newFormData;
            });
            callback([pageQuestion]);
          })
        .catch(() => {
          callback([]);
        });
    } else {
      callback([]);
    }
  };
  
  const loadAnswer = (search: string, callback: (options: OptionItem[]) => void, index: number) => {
    const pageId = formData[index]?.patientPage?.value;
  
    if (pageId) {
        pageRepo
          .getOne(pageId as number)
          .then((res) => {
            callback([
              ...res.answers.map((answer: Answer) => ({
                label: answer.answer,
                value: answer.id,
              })),
            ]);
          })
        .catch(() => {
          callback([]);
        });
    } else {
      callback([]);
    }
  };

  return (
    <div className="px-16 h-full flex flex-col flex-grow">
      <div className="flex flex-row justify-between items-center mb-4">
        <div className="font-thin text-3xl mt-4">Matching Algo Page</div>
      </div>
      <div className="flex-none grid grid-cols-12 mt-8 pb-2 gap-6 text-sm text-gray-400">
        <span className="col-span-1"></span>
        <div className="col-span-3">Therapist Page</div>
        <div className="col-span-2">Category</div>
        <div className="col-span-2">Patient Page</div>
        <div className="col-span-2">Questions</div>
        <div className="col-span-2">Answers</div>
      </div>
      <div className="flex-grow">
        {error ? (
          <span>Error: {JSON.stringify(error)}</span>
        ) : (
          <>
            {data?.map((item: TherapistPage, index: number) => (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                key={index}
              >
                <div className="col-span-1 flex items-center">
                  <div className="w-8 h-8 rounded-full bg-gray-100 font-semibold flex items-center justify-center">
                    {index + 1}
                  </div>
                </div>
                <div className="col-span-3 flex items-center">
                  <strong>{item.pageName}</strong>
                </div>
                <div className="col-span-2 flex flex-row items-center">
                  {/* Category */}
                  <section data-testid="category-value">
                    <Select
                      id="category"
                      instanceId="category"
                      isClearable={true}
                      unstyled
                      placeholder="Select"
                      classNames={{
                        control: () =>
                          "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 p-1 w-[130px]",
                        menu: () =>
                          "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                        placeholder: () => "text-gray-400 cursor-pointer",
                        option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                        multiValue: () =>
                          "bg-primary text-white mr-1 rounded-full px-2",
                        noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                        loadingMessage: () => "text-gray-400 text-sm py-2",
                        valueContainer: () => "cursor-pointer",
                      }}
                      menuPlacement="auto"
                      options={categories}
                      value={formData[item.id]?.category}
                      onChange={(value) => {
                        setFormData((prevFormData) => {
                          const newFormData = {...prevFormData};
                          newFormData[item.id] = { 
                            ...newFormData[item.id], 
                            therapistPage: {
                              label: item.pageName, 
                              value: item.id
                            },
                            category: value as OptionItem 
                          };
                          return newFormData;
                        });
                      }}
                    />
                  </section>
                </div>
                <div className="col-span-2 flex flex-row items-center">
                  {/* Patient Page */}
                  <section data-testid="patient-page-select">
                    <AsyncSelect
                      key={`${formData[item.id]?.category?.value}-patient-page`}
                      id="patient-page"
                      instanceId="patient-page"
                      isClearable={true}
                      filterOption={createFilter({ ignoreAccents: false })}
                      unstyled
                      placeholder="Select page"
                      loadOptions={debounce((inputValue, callback) => loadPages(inputValue, callback, item.id), 500)}
                      defaultOptions={true}
                      classNames={{
                        control: () =>
                          "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-[130px] p-1",
                        menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                        placeholder: () => "text-gray-400 cursor-pointer",
                        option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                        multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
                        noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                        loadingMessage: () => "text-gray-400 text-sm py-2",
                        valueContainer: () => "cursor-pointer",
                      }}
                      menuPlacement="auto"
                      value={formData[item.id]?.patientPage}
                      onChange={(value) => {
                        setFormData((prevFormData) => {
                          const newFormData = {...prevFormData};
                          newFormData[item.id] = { ...newFormData[item.id], patientPage: value as OptionItem };
                          return newFormData;
                        });
                      }}
                    />
                  </section>
                </div>
                <div className="col-span-2 flex flex-row items-center">
                  {/* Questions */}
                  <section data-testid="question-select">
                    <AsyncSelect 
                      key={`${formData[item.id]?.patientPage?.value}-question`}
                      id="patient-question"
                      instanceId="patient-question"
                      isClearable={true}
                      filterOption={createFilter({ ignoreAccents: false })}
                      unstyled
                      placeholder="Select question"
                      loadOptions={debounce((inputValue, callback) => loadQuestion(inputValue, callback, item.id), 500)}
                      defaultOptions={true}
                      classNames={{
                        control: () =>
                          "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-[130px] p-1",
                        menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                        placeholder: () => "text-gray-400 cursor-pointer",
                        option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                        multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
                        noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                        loadingMessage: () => "text-gray-400 text-sm py-2",
                        valueContainer: () => "cursor-pointer",
                      }}
                      menuPlacement="auto"
                      value={formData[item.id]?.question}
                      onChange={(value) => {
                        setFormData((prevFormData) => {
                          const newFormData = {...prevFormData};
                          newFormData[item.id] = { ...newFormData[item.id], question: value as OptionItem };
                          return newFormData;
                        });
                      }}
                    />
                  </section>
                </div>
                <div className="col-span-2 flex flex-row items-center">
                  {/* Answers */}
                  <section data-testid="answer-select">
                    <AsyncSelect
                      key={`${formData[item.id]?.patientPage?.value}-answer`}
                      id="patient-answer"
                      instanceId="patient-answer"
                      isClearable={true}
                      filterOption={createFilter({ ignoreAccents: false })}
                      unstyled
                      placeholder="Select answer"
                      loadOptions={debounce((inputValue, callback) => loadAnswer(inputValue, callback, item.id), 500)}
                      defaultOptions={true}
                      classNames={{
                        control: () =>
                          "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-[130px] p-1",
                        menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                        placeholder: () => "text-gray-400 cursor-pointer",
                        option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                        multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
                        noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                        loadingMessage: () => "text-gray-400 text-sm py-2",
                        valueContainer: () => "cursor-pointer",
                      }}
                      menuPlacement="auto"
                      value={formData[item.id]?.answer}
                      onChange={(value) => {
                        setFormData((prevFormData) => {
                          const newFormData = {...prevFormData};
                          newFormData[item.id] = { ...newFormData[item.id], answer: value as OptionItem };
                          return newFormData;
                        });
                      }}
                    />
                  </section>
                </div>
              </motion.div>
            ))}
          </>
        )}
      </div>

      <div className="absolute top-6 right-8">
        <button
          type="submit"
          className="w-full justify-center rounded-md border border-transparent bg-primary px-4 py-2.5 text-sm font-medium text-white hover:bg-primary active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
          onClick={handleSubmit}
        >
          {loading && <i className="fa fa-spinner animate-spin mr-1"></i>} Save Data
        </button>
      </div>
    </div>
  );
}

export default MatchingAlgoPage;
