import QuestionnaireRepository from "@/repositories/QuestionnaireRepository";
import OptionItem from "@/types/option.interface";
import { Questionnaire } from "@/types/questionnaire.interface";
import { debounce } from "lodash";
import { Fragment } from "react";
import Select, { createFilter } from "react-select";
import AsyncSelect from "react-select/async";
import PerfectScrollbar from "react-perfect-scrollbar";
import AnswerRepository from "@/repositories/AnswerRepository";
import { Answer } from "@/types/answer.interface";
import { useOverlayTriggerState } from "react-stately";
import NormalDialog from "@/components/dialogs/NormalDialog";
import ConditionPage from "./AnswerForm";

type CustomOptionItem = {
  conditions?: any;
  uid?: string;
  group?: string;
} & OptionItem;

const selects: OptionItem[] = [
  { label: "Multiple", value: "multiple" },
  { label: "Single", value: "single" },
];

type Props = {
  formData: any;
  setFormData: (formData: any) => void;
  question?: OptionItem;
  setQuestion: (question: OptionItem) => void;
  answers: CustomOptionItem[];
  setAnswers: (answers: CustomOptionItem[]) => void;
  answer?: CustomOptionItem;
  setAnswer: (answer?: CustomOptionItem) => void;
  removedAnswers: (string | number)[];
  setRemovedAnswers: (data: any) => void;
};

const QuestionnaireTypePage = ({
  formData,
  setFormData,
  question,
  setQuestion,
  answers,
  setAnswers,
  answer,
  setAnswer,
  removedAnswers,
  setRemovedAnswers,
}: Props) => {
  const questionnaireRepo = new QuestionnaireRepository();
  const answerRepo = new AnswerRepository();

  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});

  const loadQuestions = (
    search: string,
    callback: (options: OptionItem[]) => void
  ) => {
    questionnaireRepo
      .getAll({ search })
      .then((res) => {
        callback(
          res.data.map((questionData: Questionnaire) => ({
            label: questionData.question,
            value: questionData.id,
          }))
        );
      })
      .catch(() => {
        callback([]);
      });
  };

  const loadAnswers = (
    search: string,
    callback: (options: CustomOptionItem[]) => void
  ) => {
    answerRepo
      .getAll({ search, perPage: 20 })
      .then((res) => {
        callback(
          res.data.map((ans: Answer) => ({
            label: ans.answer,
            value: ans.id,
            conditions: {},
          }))
        );
      })
      .catch(() => {
        callback([]);
      });
  };

  return (
    <Fragment>
      <div className="grid grid-cols-5 gap-4">
        <section className="flex-none col-span-4">
          <label className="text-sm">
            Question
            <span className="text-red-500 font-bold ml-1">*</span>
          </label>
          <AsyncSelect
            isDisabled={!formData.category}
            id="question"
            instanceId="question"
            isClearable={true}
            filterOption={createFilter({ ignoreAccents: false })}
            unstyled
            placeholder="Select question"
            loadOptions={debounce(loadQuestions, 500)}
            defaultOptions={true}
            classNames={{
              control: () =>
                "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
              menu: () =>
                "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
              placeholder: () => "text-gray-400 cursor-pointer",
              option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
              multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
              noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
              loadingMessage: () => "text-gray-400 text-sm py-2",
              valueContainer: () => "cursor-pointer",
            }}
            menuPlacement="auto"
            value={question}
            onChange={(value) => setQuestion(value as OptionItem)}
          />
        </section>
        <section>
          <label className="text-sm">
            Select Type
            <span className="text-red-500 font-bold ml-1">*</span>
          </label>
          <Select
            isDisabled={!question || !formData.category}
            id="question-type"
            instanceId="question-type"
            isClearable={true}
            unstyled
            placeholder="Select"
            classNames={{
              control: () =>
                "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
              menu: () =>
                "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
              placeholder: () => "text-gray-400 cursor-pointer",
              option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
              multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
              noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
              loadingMessage: () => "text-gray-400 text-sm py-2",
              valueContainer: () => "cursor-pointer",
            }}
            menuPlacement="auto"
            options={selects}
            value={formData.questionnaireType}
            onChange={(value) => {
              setFormData({
                ...formData,
                questionnaireType: value as OptionItem,
              });
            }}
          />
        </section>
      </div>
      <section className="flex-none">
        <label className="text-sm">
          Answer
          <span className="text-red-500 font-bold ml-1">*</span>
        </label>
        <AsyncSelect
          isDisabled={!formData.category || !question}
          id="answer"
          instanceId="answer"
          isClearable={true}
          filterOption={createFilter({ ignoreAccents: false })}
          unstyled
          placeholder="Select answer"
          loadOptions={debounce(loadAnswers, 500)}
          defaultOptions={true}
          classNames={{
            control: () =>
              "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
            menu: () =>
              "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
            placeholder: () => "text-gray-400 cursor-pointer",
            option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
            multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
            noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
            loadingMessage: () => "text-gray-400 text-sm py-2",
            valueContainer: () => "cursor-pointer",
          }}
          menuPlacement="auto"
          value={{}}
          onChange={(value) => {
            if (value) {
              setAnswers([...answers, value as OptionItem]);
            }
          }}
        />
      </section>
      <div className="flex-grow relative overflow-hidden border rounded-md p-4">
        {!formData.category && (
          <div className="absolute top-0 left-0 right-0 bottom-0 bg-white opacity-80 z-10 flex items-center justify-center text-sm">
            Select `Page for:` and `Question` to add answers.
          </div>
        )}
        <PerfectScrollbar className="pr-2.5">
          <div className="grid grid-cols-12 pb-2 px-2 text-sm text-gray-400">
            <div className="col-span-9">Answer</div>
            <div className="col-span-3"></div>
          </div>
          {answers.map((ans, index) => (
            <div
              className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
              key={index}
            >
              <div className="col-span-9">{ans.label}</div>
              <div className="col-span-3 flex flex-row items-center justify-end">
                <i
                  onClick={() => {
                    setAnswer(ans);
                    deleteState.open();
                  }}
                  className="fas fa-remove text-primary/80 hover:text-red-700 cursor-pointer mr-4"
                  data-testid="delete-answer-icon"></i>
                <i
                  onClick={() => {
                    setAnswer(ans);
                    state.open();
                  }}
                  className="fad fa-gears text-red-500 hover:text-red-700 cursor-pointer"
                ></i>{" "}
              </div>
            </div>
          ))}
          {answers.length === 0 && (
            <div className="px-2 text-sm">No answers selected yet.</div>
          )}
        </PerfectScrollbar>
      </div>
      {answer && (
        <NormalDialog
          state={deleteState}
          title={"Remove Answer"}
          confirmLabel={"Delete"}
          onCancel={() => {
            deleteState.close();
            setAnswer(undefined);
          }}
          onAccept={() => {
            deleteState.close();
            const ans = [...answers];
            ans.splice(
              ans.findIndex((a) => a.value === answer.value),
              1
            );
            setRemovedAnswers([...removedAnswers, answer.value]);
            setAnswers(ans);
            setAnswer(undefined);
          }}
        >
          <p className="text-sm">
            Are you sure you want to remove this answer? This process is not
            reversible.
          </p>
        </NormalDialog>
      )}
      <ConditionPage
        state={state}
        category={formData.category?.value}
        answer={answer}
        setAnswer={(ans) => {
          const index = answers.findIndex((a) => a.value === ans.value);
          const ansArr = [...answers];
          ansArr[index] = ans;
          setAnswers(ansArr);
        }}
      />
    </Fragment>
  );
};

export default QuestionnaireTypePage;
