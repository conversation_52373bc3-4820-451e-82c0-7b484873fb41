import { useQuery } from "@tanstack/react-query";
import { FC, FormEvent, useEffect, useState } from "react";
import { useInView } from "react-intersection-observer";
import { useNavigate, useParams } from "react-router-dom";
import AsyncSelect from "react-select/async";
import Select, { createFilter } from "react-select";
import { debounce } from "lodash";
import OptionItem from "@/types/option.interface";
import PageRepository from "@/repositories/PageRepository";
import { Answer } from "@/types/answer.interface";
import InputComponent from "@/components/forms/InputComponent";
import { Page } from "@/types/page.interface";
import InfoTypePage from "./InfoTypePage";
import QuestionnaireTypePage from "./QuestionnaireTypePage";
import ValueTypePage from "./ValueTypePage";
import DeviceRenderer from "./DeviceRenderer";

interface IApplicationProps {}

type PageFormData = {
  answers: any[];
  questionnaireId: number;
  code: string;
  title: string;
  info: string;
  extra: string;
  button: string;
  type?: OptionItem;
  category?: OptionItem;
  questionnaireType?: OptionItem;
  buttonClick?: OptionItem;
};

type CustomOptionItem = {
  conditions?: any;
  answerGroup?: string;
} & OptionItem;

const selects: OptionItem[] = [
  { label: "Multiple", value: "multiple" },
  { label: "Single", value: "single" },
];

const categories: OptionItem[] = [
  { label: "MySelf", value: "self" },
  { label: "Couple", value: "couple" },
  { label: "Child", value: "child" },
  { label: "Teen", value: "teen" },
  // { label: "Therapist", value: "therapist" },
];

const types: OptionItem[] = [
  { label: "Info", value: "info" },
  { label: "Questionnaire", value: "questionnaire" },
  { label: "Value", value: "value" },
  { label: "Register", value: "register" },
];

const PageForm: FC<IApplicationProps> = () => {
  const navigate = useNavigate();
  const pageRepo = new PageRepository();

  const { ref } = useInView();
  const { pageId } = useParams();

  const [question, setQuestion] = useState<OptionItem>();
  const [answers, setAnswers] = useState<CustomOptionItem[]>([]);
  const [answer, setAnswer] = useState<CustomOptionItem>();
  const [removedAnswers, setRemovedAnswers] = useState<(string | number)[]>([]);
  const [uid, setUid] = useState<string>("");

  const { data } = useQuery({
    queryKey: [`page-form`, pageId],
    queryFn: () => pageRepo.getOne(parseInt(pageId ?? "")),
    enabled: !!pageId,
    refetchOnWindowFocus: false,
  });

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<PageFormData>({
    button: "Continue",
  } as PageFormData);

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (loading) return;

    if (!formData.type) {
      pageRepo.errorToast("Please select page type.");
      return;
    }

    if (!formData.category) {
      pageRepo.errorToast("Please select page category.");
      return;
    }

    if (formData.type.value === "questionnaire") {
      if (!question) {
        pageRepo.errorToast("Please select question.");
        return;
      }

      if (!formData.questionnaireType) {
        pageRepo.errorToast("Please select type.");
        return;
      }

      if (answers.length === 0) {
        pageRepo.errorToast("Select answers.");
        return;
      }
    }

    setLoading(true);

    const prepareAnswersForSubmit = (prepareAnswers: CustomOptionItem[]) =>
      prepareAnswers.map((a) => ({
        id: a.value,
        conditions: a.conditions,
        answerGroup: a.answerGroup,
    }));

    const formPayload = {
      ...formData,
      type: formData.type?.value,
      category: formData.category?.value,
      questionnaireId: question?.value,
      questionnaireType: formData.questionnaireType?.value,
      answers:prepareAnswersForSubmit(answers),
      removedAnswers,
      buttonClickAction: formData.buttonClick?.value,
    };

   



    const result = await (pageId
      ? pageRepo.update(parseInt(pageId), formPayload)
      : pageRepo.create(formPayload));
    setLoading(false);
    if (result) {
      setFormData({} as PageFormData);
      navigate("/pages");
    }
  };

  const mapAnswers = (mapAns: Answer[]): CustomOptionItem[] =>
    mapAns.map((a: Answer) => ({
      label: a.answer,
      value: a.id,
      conditions: a.conditions,
      answerGroup: a.answerGroup,
    }));


  useEffect(() => {
    if (!data) return;
    let buttonClickOption;

    if (data.nextPageId) {
      buttonClickOption = {
        label: `${data.nextPage.code} - ${data.nextPage.title}`,
        value: data.nextPageId,
      };
    } else if (data.buttonAction === "register") {
      buttonClickOption = {
        value: "register",
        label: "Register Page",
      };
    } else {
      buttonClickOption = {
        value: "end",
        label: "End Page",
      };
    }

   
    if (data) {
      setFormData({
        answers:  mapAnswers(data.answers),
        questionnaireId: data.questionnaireId,
        code: data.code,
        title: data.title,
        info: data.info,
        extra: data.extra,
        button: data.button,
        type: types.find((t) => t.value === data.type),
        category: categories.find((c) => c.value === data.category),
        questionnaireType: selects.find(
          (t) => t.value === data.questionnaireType
        ),
        buttonClick: buttonClickOption,
      });
      if (data.questionnaire) {
        setQuestion({
          label: data.questionnaire.question,
          value: data.questionnaire.id,
        } as OptionItem);
      }
      if (data.answers) {
        setAnswers(mapAnswers(data.answers));
      }
    }
  }, [data]);

  const loadPages = (
    search: string,
    callback: (options: OptionItem[]) => void
  ) => {
    const formParam: any = {
      search,
      "exclude[]": pageId,
    };
    if (formData.category?.value) {
      formParam["category"] = formData.category?.value;
    }
    pageRepo
      .getAll(formParam)
      .then((res) => {
        callback([
          {
            label: "End Page",
            value: "end",
          },
          ...res.data.map((page: Page) => ({
            label: `${page.code} - ${page.title}`,
            value: page.id,
          })),
        ]);
      })
      .catch(() => {
        callback([]);
      });
  };

  useEffect(() => {
    setUid(formData?.category?.value?.toString() || "");
    setFormData(
      (prev) =>
        ({
          ...prev,
          buttonClick: undefined,
        }) as PageFormData
    );
  }, [formData?.category]);

  return (
    <div
      className="pr-3 pt-8 pb-4 px-16 h-full flex flex-col overflow-hidden"
      ref={ref}
    >
      <div className="flex-none flex flex-row justify-between items-center mb-4">
        <div className="font-thin text-3xl">
          Page Form
          <br />
          <span className="-mt-1 mb-1 block text-sm font-normal">
            Design your page from here, mobile app will be updated
            automatically.
          </span>
        </div>
        <div className="flex flex-row space-x-2"></div>
      </div>
      <div className="flex-grow flex flex-row overflow-hidden">
        <form onSubmit={onSubmit} className="flex-grow flex flex-col space-y-4">
          <div className="flex-none grid grid-cols-4 gap-4">
            <InputComponent
              id="code"
              placeholder="Unique Code"
              label="Code"
              isRequired
              value={formData.code || ""}
              onChange={(value) => setFormData({ ...formData, code: value })}
            />
            <div className="col-span-2">
              <InputComponent
                isRequired
                label="Title"
                placeholder="Page title"
                value={formData.title || ""}
                onChange={(value) =>
                  setFormData((prev) => ({ ...prev, title: value }))
                }
              />
            </div>
            <section className="z-[4]">
              <label className="text-sm">
                Page type:
                <span className="text-red-500 font-bold ml-1">*</span>
              </label>
              <Select
                id="type"
                instanceId="type"
                isClearable={true}
                unstyled
                placeholder="Select type"
                classNames={{
                  control: () =>
                    "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                  menu: () =>
                    "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                  placeholder: () => "text-gray-400 cursor-pointer",
                  option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                  multiValue: () =>
                    "bg-primary text-white mr-1 rounded-full px-2",
                  noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                  loadingMessage: () => "text-gray-400 text-sm py-2",
                  valueContainer: () => "cursor-pointer",
                }}
                menuPlacement="auto"
                options={types}
                value={formData.type}
                onChange={(value) => {
                  setFormData({
                    ...formData,
                    type: value as OptionItem,
                  });
                }}
              />
            </section>
          </div>

          <div className="flex-none grid grid-cols-4 gap-4">
            <section className="relative z-[3]">
              <label className="text-sm">
                Page for:
                <span className="text-red-500 font-bold ml-1">*</span>
              </label>
              <Select
                id="category"
                instanceId="category"
                isClearable={true}
                unstyled
                placeholder="Select"
                classNames={{
                  control: () =>
                    "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                  menu: () =>
                    "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                  placeholder: () => "text-gray-400 cursor-pointer",
                  option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                  multiValue: () =>
                    "bg-primary text-white mr-1 rounded-full px-2",
                  noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                  loadingMessage: () => "text-gray-400 text-sm py-2",
                  valueContainer: () => "cursor-pointer",
                }}
                menuPlacement="auto"
                options={categories}
                value={formData.category}
                onChange={(value) => {
                  setFormData({
                    ...formData,
                    category: value as OptionItem,
                  });
                }}
              />
            </section>
            <InputComponent
              label="Button Text"
              value={formData.button || ""}
              onChange={(value) => setFormData({ ...formData, button: value })}
            />
            <section className="col-span-2 z-[3]">
              <label className="text-sm">
                On Button Click:
                <span className="text-red-500 font-bold ml-1">*</span>
              </label>
              <AsyncSelect
                key={uid}
                id="button-click"
                data-testid="async-select"
                instanceId="button-click"
                isClearable={true}
                filterOption={createFilter({ ignoreAccents: false })}
                unstyled
                placeholder="Select page"
                loadOptions={debounce(loadPages, 500)}
                defaultOptions={true}
                classNames={{
                  control: () =>
                    "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                  menu: () =>
                    "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                  placeholder: () => "text-gray-400 cursor-pointer",
                  option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                  multiValue: () =>
                    "bg-primary text-white mr-1 rounded-full px-2",
                  noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                  loadingMessage: () => "text-gray-400 text-sm py-2",
                  valueContainer: () => "cursor-pointer",
                }}
                menuPlacement="auto"
                value={formData.buttonClick}
                onChange={(value) => {
                  if (value) {
                    setFormData({
                      ...formData,
                      buttonClick: value,
                    });
                  }
                }}
              />
            </section>
          </div>

          {formData.type?.value === "info" && (
            <InfoTypePage formData={formData} setFormData={setFormData} />
          )}

          {formData.type?.value === "questionnaire" && (
            <QuestionnaireTypePage
              formData={formData}
              setFormData={setFormData}
              question={question}
              setQuestion={setQuestion}
              answers={answers}
              setAnswers={setAnswers}
              answer={answer}
              setAnswer={setAnswer}
              removedAnswers={removedAnswers}
              setRemovedAnswers={setRemovedAnswers}
            />
          )}

          {formData.type?.value === "value" && (
            <ValueTypePage
              formData={formData}
              setFormData={setFormData}
              question={question}
              setQuestion={setQuestion}
              answers={answers}
              setAnswers={setAnswers}
              answer={answer}
              setAnswer={setAnswer}
              removedAnswers={removedAnswers}
              setRemovedAnswers={setRemovedAnswers}
            />
          )}

          <div className="flex-none">
            <button
              type="submit"
              className="w-full justify-center rounded-md border border-transparent bg-primary px-4 py-2.5 text-sm font-medium text-white hover:bg-primary active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
            >
              {loading && <i className="fa fa-spinner animate-spin mr-1"></i>}{" "}
              Save Page
            </button>
          </div>
        </form>
        <div className="flex-none w-96 ml-2">
          <DeviceRenderer
            formData={formData}
            question={question}
            answers={answers}
          />
        </div>
      </div>
    </div>
  );
};

export default PageForm;
