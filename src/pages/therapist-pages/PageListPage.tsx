import PaginatedDataContainer from "@/components/PaginatedDataContainer";
import { Page } from "@/types/page.interface";
import { useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { FC, useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { useOverlayTriggerState } from "react-stately";
import DeleteDialog from "@/components/dialogs/DeleteDialog";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import { useNavigate } from "react-router-dom";
import Meta from "@/types/meta.interface";
import PageRepository from "@/repositories/PageRepository";
import AddButton from "@/components/buttons/AddButton";
import ActionButton from "@/components/buttons/ActionButton";
import InputComponent from "@/components/forms/InputComponent";
import { debounce } from "lodash";
import OptionItem from "@/types/option.interface";
import Select from "react-select";
import CheckBoxComponent from "@/components/forms/CheckBoxComponent";
import NormalDialog from "@/components/dialogs/NormalDialog";

interface IApplicationProps {}

const categories: OptionItem[] = [
  { label: "MySelf", value: "self" },
  { label: "Couple", value: "couple" },
  { label: "Child", value: "child" },
  { label: "Teen", value: "teen" },
  { label: "Therapist", value: "therapist" },
];

type SearchParams = {
  search?: string;
  category?: string | number;
};

const PageListPage: FC<IApplicationProps> = () => {
  const pageRepo = new PageRepository();
  const { ref } = useInView();
  const pageState = useOverlayTriggerState({});
  const state = useOverlayTriggerState({});
  const navigate = useNavigate();

  const [params, setParams] = useState({
    search: "",
    category: "",
  } as SearchParams);

  const [category, setCategory] = useState<OptionItem>();
  const [page, setPage] = useState<Page>();

  const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
    queryKey: ["pages", params],
    queryFn: async ({ pageParam = 1 }) => {
     return pageRepo.getAll({
        perPage: 20,
        page: pageParam,
        ...params,
      });
      
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  const debouncedSearch = debounce((value: string) => {
    setParams({ ...params, search: value });
  }, 500);

  useEffect(() => {
    if (!state.isOpen) {
      setPage(undefined);
    }
  }, [state.isOpen, state.isOpen]);

  return (
    <PaginationAwareContainer meta={meta} onLoad={fetchNextPage} fetching={isLoading}>
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">Manage Pages</div>
          <div className="flex flex-row space-x-2">
            <AddButton label="Add Page" onPress={() => navigate("/pages/form")} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 gap-4">
          <div className="col-span-3">
            <InputComponent id="search" placeholder="Search..." aria-labelledby="search" onChange={debouncedSearch} />
          </div>
          <div className="col-span-3">
            <section>
              <Select
                id="category"
                instanceId="category"
                isClearable={true}
                unstyled
                placeholder="Select"
                classNames={{
                  control: () =>
                    "cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5",
                  menu: () => "bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm",
                  placeholder: () => "text-gray-400 cursor-pointer",
                  option: () => "py-2 hover:bg-primary/10 px-4 py-1.5",
                  multiValue: () => "bg-primary text-white mr-1 rounded-full px-2",
                  noOptionsMessage: () => "text-gray-400 text-sm py-1.5",
                  loadingMessage: () => "text-gray-400 text-sm py-2",
                  valueContainer: () => "cursor-pointer",
                }}
                menuPlacement="auto"
                options={categories}
                value={category}
                onChange={(value) => {
                  setCategory(value as OptionItem);
                  setParams({ ...params, category: value?.value });
                }}
              />
            </section>
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <span></span>
          <span className="col-span-2">Code</span>
          <span className="col-span-5">Title</span>
          <span className="col-span-2">Type</span>
          <span className="col-span-2"></span>
        </div>
        <div className="flex-grow">
          {error ? (
            <span>Error: {JSON.stringify(error)}</span>
          ) : (
            <PaginatedDataContainer meta={meta}>
              <>
                {data?.pages
                  .flatMap((pageData) => pageData.data)
                  .map((pageResult: Page, index: number) => (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      <>
                        <div className="flex items-center">
                          <div
                            className={`w-8 h-8 rounded-full bg-gray-100 font-semibold flex items-center justify-center`}
                          >
                            {index + 1}
                          </div>
                        </div>
                        <div className="col-span-2 flex items-center text-black">
                          <strong>{pageResult.code}</strong>
                        </div>
                        <div className="col-span-5 flex items-center text-black">
                          <strong>{pageResult.title}</strong>
                        </div>
                        <div className="col-span-2 flex items-center text-black">
                          <strong>{pageResult.type}</strong>
                        </div>
                        <div className="col-span-2 flex flex-row items-center justify-end space-x-4 cursor-pointer">
                          <CheckBoxComponent
                            label="Start Page"
                            checked={pageResult.initialPage || false}
                            onChange={() => {
                              setPage(page);
                              pageState.open();
                            }}
                          />
                          <ActionButton
                            onEdit={() => navigate(`/pages/form/${pageResult.id}`)}
                            onDelete={() => {
                              state.open();
                              setPage(page);
                            }}
                             data-testid={`delete-button-${pageResult.id}`}
                          />
                        </div>
                      </>
                    </motion.div>
                  ))}
              </>
            </PaginatedDataContainer>
          )}
        </div>
        <DeleteDialog
          state={state}
          verifyText={"Delete Me"}
          title={"Delete Page"}
          url={`/pages/${page?.id}`}
          refetch={refetch}
        >
          <p className="text-sm">Are you sure you want to delete this question? This process is not reversible.</p>
        </DeleteDialog>
        <NormalDialog
          state={pageState}
          title="Set Start Page"
          confirmLabel={"Yes"}
          onCancel={() => {
            setPage(undefined);
            pageState.close();
          }}
          onAccept={() => {
            if (!page?.id) return;
            pageRepo.startPage(page?.id).then((status) => {
              if (status) {
                refetch();
                setPage(undefined);
                pageState.close();
              }
            });
          }}
        >
          <p className="text-sm">Are you sure you want to set this page as start page for `{page?.category}`?</p>
        </NormalDialog>
      </div>
    </PaginationAwareContainer>
  );
};

export default PageListPage;
