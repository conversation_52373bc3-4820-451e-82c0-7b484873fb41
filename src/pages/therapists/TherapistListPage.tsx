import ActionButton from '@/components/buttons/ActionButton'
import DeleteDialog from '@/components/dialogs/DeleteDialog'
import FormDialog from '@/components/dialogs/FormDialog'
import NormalDialog from '@/components/dialogs/NormalDialog'
import InputComponent from '@/components/forms/InputComponent'
import PaginatedDataContainer from '@/components/PaginatedDataContainer'
import PaginationAwareContainer from '@/components/PaginationAwareContainer'
import { TherapistRowSkeleton } from '@/components/SkeletonLoader'
import VerificationDateComponent from '@/components/VerificationDateComponent'
import TherapistRepository from '@/repositories/TherapistRepository'
import UserRepository from '@/repositories/UserRepository'
import Meta from '@/types/meta.interface'
import OptionItem from '@/types/option.interface'
import { User } from '@/types/user.interface'
import { formatDateMDY, getFullName } from '@/utils/app.util'
import { useInfiniteQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { debounce } from 'lodash'
import { FC, FormEvent, useEffect, useMemo, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { useOverlayTriggerState } from 'react-stately'
import Select from 'react-select'
import { useNavigate } from 'react-router-dom'
import { DELETED_AT, DELETED_BY, DELETE_MESSAGE } from '@/pages/auth/constants'

interface IApplicationProps {}

const filters: OptionItem[] = [
	{ label: 'Pending', value: 'pending' },
	{ label: 'Review in progress', value: 'under_review' },
	{ label: 'Accepted', value: 'accepted' },
	{ label: 'Rejected', value: 'rejected' },
	{ label: 'Deactivated', value: 'deactivated' },
	{ label: 'Deleted', value: 'deleted' },
]

const TherapistListPage: FC<IApplicationProps> = () => {
	const getUserClassName = () => {
		return 'px-4 py-3';
	}
	const getTherapistName = (therapistName: any) => {
		return therapistName
			? getFullName(therapistName.firstname, therapistName.lastname)
			: ''
	}
	const therapistRepo = new TherapistRepository()
	const userRepo = new UserRepository()
	const navigate = useNavigate()

	const { ref } = useInView()

	const [params, setParams] = useState({})

	const deactivateState = useOverlayTriggerState({})
	const resetPasswordState = useOverlayTriggerState({})
	const deleteState = useOverlayTriggerState({})
	const acceptState = useOverlayTriggerState({})
	const rejectState = useOverlayTriggerState({})
	const restoreState = useOverlayTriggerState({})

	const [therapist, setTherapist] = useState<User>()
	const [enteredEmail, setEnteredEmail] = useState<string>('')
	const [errorMessage, setErrorMessage] = useState<string | null>(null)
	const [loading, setLoading] = useState<boolean>(false)
	const [filter, setFilter] = useState<OptionItem>()
	const [expandedRow, setExpandedRow] = useState<number | null>(null)
	const [viewHistory, setViewHistory] = useState<any[]>([])
	const viewHistoryPopupState = useOverlayTriggerState({})
	const [showViewHistoryPopup, setViewHistoryPopup] = useState(false)

	const currentDate = new Date().toISOString()
	const [loadingHistory, setLoadingHistory] = useState(false)
	useEffect(() => {
		if (showViewHistoryPopup) {
			viewHistoryPopupState.open()
		} else {
			viewHistoryPopupState.close()
		}
	}, [showViewHistoryPopup])

	const fetchReasons = async (therapistId: number) => {
		if (!therapistId) return
		setLoadingHistory(true)
		try {
			const res = await therapistRepo.getViewHistoryReasons(therapistId)
			if (res) {
				setViewHistory(res.data.reasons)
				setViewHistoryPopup(true)
			}
		} catch (e) {
			setViewHistory([])
			setViewHistoryPopup(false)
		}
		setLoadingHistory(false)
	}

	const { error, data, isLoading, isFetchingNextPage, fetchNextPage, refetch } =
		useInfiniteQuery({
			queryKey: ['therapists', params],
			queryFn: async ({ pageParam = 1 }) => {
				return therapistRepo.getAll({
					perPage: 20,
					page: pageParam,
					...params,
				})
			},
			initialPageParam: 0,
			getNextPageParam: (lastPage) => lastPage.meta.nextPage,
			refetchOnWindowFocus: false,
		})

	const customRefetch = async () => {
		setParams((prevParams) => ({
			...prevParams,
			page: 0,
			perPage: 20,
		}))

		await refetch()
	}

	const debouncedSearch = debounce((value: string) => {
		setParams({ ...params, search: value })
	}, 500)

	const meta = useMemo(() => {
		if (!data?.pages?.length) return {} as Meta
		return data.pages[data.pages.length - 1].meta
	}, [data])

	const handleResetPasswordSubmit = async (e: FormEvent<HTMLFormElement>) => {
		e.preventDefault()

		// set reset password email to the therapist email
		if (therapist && enteredEmail === therapist.email) {
			setLoading(true)
			const res = await userRepo.resetPassword(
				'/auth/forgot-password',
				enteredEmail
			)
			if (res) {
				setLoading(false)
				setEnteredEmail('')
				resetPasswordState.close()
			}
			setLoading(false)
		} else {
			setErrorMessage("Entered email does not match the therapist's email")
		}
	}

	// Function to handle user restore
	const handleRestoreUser = async () => {
		if (!therapist?.id) return
		setLoading(true)
		try {
			// Call the restore method with the therapist's ID as a number
			const res = await therapistRepo.restore(therapist.id)
			if (res) {
				setTherapist(undefined)
				refetch()
				restoreState.close()
			}
		} catch (err) {
			console.error('Error restoring user:', err)
		} finally {
			setLoading(false)
		}
	}

	const typeColorMap: Record<string, string> = {
		deleted: 'text-red-500',
		deactivated: 'text-red-500',
		rejected: 'text-yellow-600',
	}

	const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1)

	let renderHistoryContent;

if (loadingHistory) {
  renderHistoryContent = (
    <div
      className="max-h-[400px] overflow-y-auto mt-2 animate-pulse space-y-4"
      data-testid="loading-indicator"
    >
      {/* Header skeleton */}
      <div className="flex justify-between px-4">
        <div className="h-4 w-24 bg-gray-300 rounded" />
        <div className="h-4 w-40 bg-gray-300 rounded" />
        <div className="h-4 w-16 bg-gray-300 rounded" />
      </div>

      {/* Row skeletons */}
      {Array(5)
        .fill(null)
        .map((_, i) => (
          <div
            key={i}
            className="flex justify-between px-4 py-2 border-t border-gray-200"
          >
            <div className="h-4 w-24 bg-gray-200 dark:bg-gray-600 rounded" />
            <div className="h-4 w-40 bg-gray-200 dark:bg-gray-600 rounded" />
            <div className="h-4 w-16 bg-gray-200 dark:bg-gray-600 rounded" />
          </div>
        ))}
    </div>
  );
} else if (viewHistory.length > 0) {
  renderHistoryContent = (
    <div className="max-h-[400px] overflow-y-auto mt-2">
      <table className="min-w-full divide-y divide-gray-200 shadow-md rounded-lg">
        <thead className="bg-gray-100 text-gray-700 text-sm sticky top-0 z-10">
          <tr>
            <th className="px-4 py-2 text-left">Date</th>
            <th className="px-4 py-2 text-left">Reason</th>
            <th className="px-4 py-2 text-left">Account Status</th>
          </tr>
        </thead>
        <tbody className="text-sm text-gray-700">
          {viewHistory.map((reasonData, index) => (
            <tr key={index} className="border-t">
              <td className="px-4 py-2">{formatDateMDY(reasonData.date)}</td>
              <td className="px-4 py-2">{reasonData.reason}</td>
              <td className="px-4 py-2">
                <span className={typeColorMap[reasonData.type]}>
                  {capitalize(reasonData.type)}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} else {
  renderHistoryContent = (
    <p className="text-sm text-gray-500 mt-2">History not found.</p>
  );
}



	return (
		<PaginationAwareContainer
			meta={meta}
			onLoad={fetchNextPage}
			fetching={isLoading}
		>
			<div className="px-16 h-full flex flex-col flex-grow text-base" ref={ref}>
				<div className="flex-none flex flex-row justify-between items-center mb-4">
					<div className="font-thin text-3xl">Therapist List</div>
					<div>{/* <AddButton label="Add User" onPress={state.open} /> */}</div>
				</div>
				<div className="flex-none grid grid-cols-12 gap-4">
					<div className="col-span-3">
						<InputComponent
							id="search"
							placeholder="Search..."
							aria-labelledby="search"
							onChange={debouncedSearch}
						/>
					</div>
					<div className="col-span-3">
						<section>
							<Select
								id="filter"
								instanceId="filter"
								isClearable={true}
								unstyled
								placeholder="Select Filter"
								classNames={{
									control: () =>
										'cursor-pointer mt-1 bg-gray-50 border hover:border-primary text-gray-900 sm:text-sm rounded-lg border-2 block w-full p-2.5',
									menu: () =>
										'bg-white border border-gray-300 overflow-hidden rounded-lg mt-1 z-10 text-sm',
									placeholder: () => 'text-gray-400 cursor-pointer',
									option: () => 'py-2 hover:bg-primary/10 px-4 py-1.5',
									multiValue: () =>
										'bg-primary text-white mr-1 rounded-full px-2',
									noOptionsMessage: () => 'text-gray-400 text-sm py-1.5',
									loadingMessage: () => 'text-gray-400 text-sm py-2',
									valueContainer: () => 'cursor-pointer',
								}}
								menuPlacement="auto"
								options={filters}
								value={filter}
								onChange={(value) => {
									setFilter(value as OptionItem)
									setParams({ ...params, filter: value?.value })
								}}
							/>
						</section>
					</div>
				</div>

				<div className="pt-4">
					<table className="min-w-full divide-y divide-gray-200 shadow-md rounded-lg table-fixed text-base">
						<thead className="bg-gray-100 text-gray-700 text-base md:text-lg font-medium">
							<tr>
								<th className="w-1/12 px-4 py-2 text-left">#</th>
								<th className="w-1/4 px-4 py-2 text-left">Name</th>
								<th className="w-1/4 px-4 py-2 text-left">License(s)</th>
								<th className="w-1/6 px-4 py-2 text-left">Account Status</th>
								<th className="w-1/4 px-4 py-2 text-left">Reasons</th>
								<th className="w-1/12 px-4 py-2 text-left"></th>
							</tr>
						</thead>
						{(() => {
							if (error) {
								return <span>Error: {JSON.stringify(error)}</span>
							} else if (isLoading || loading) {
								return (
									<tbody className="text-base text-gray-700">
										<TherapistRowSkeleton count={10} />
									</tbody>
								)
							} else {
								return (
									<PaginatedDataContainer meta={meta}>
										<tbody className="text-base text-gray-700">
											{data?.pages
												.flatMap((pageData) => pageData.data)
												.map((user: User, index: number) => {
													const licensePayload = user?.registrationInfo?.find(
														(page) => page.pageName === 'license'
													)?.payloadInfo

													let licenses
													if (Array.isArray(licensePayload?.licenses)) {
														licenses = licensePayload.licenses
													} else {
														licenses = []
													}

													let toggleButtonText
													if (expandedRow === index) {
														toggleButtonText = 'Show less ▲'
													} else {
														toggleButtonText = 'Show more ▼'
													}

													let licensesToShow
													if (expandedRow === index) {
														licensesToShow = licenses.length
													} else {
														licensesToShow = 1
													}

													let rowBgClass =
														'unregistered-user-list hover:bg-gray-50'

													if (user.acceptedAt) {
														rowBgClass = 'bg-green-100'
													}

													// Configure user actions based on user status
													const userActions: any = {}

													// Common actions for all statuses
													userActions.onResetPassword = () => {
														setTherapist(user)
														resetPasswordState.open()
														setEnteredEmail('')
													}
													userActions.onViewProfile = () => {
														navigate(`/therapists/${user.id}/profile`)
													}
													userActions.onViewHistory = () => {
														setTherapist(user)
														viewHistoryPopupState.open()
														fetchReasons(user.id)
													}
													userActions.isResetPasswordDisabled =
														!!user.deactivatedAt || !user.acceptedAt

													// Determine action set based on status
													if (user.deletedBy) {
														userActions.onRestore = () => {
															setTherapist(user)
															restoreState.open()
														}
													} else if (user.deactivatedAt) {
														userActions.onActivate = () => {
															setTherapist(user)
															deactivateState.open() // NormalDialog: Reactivate
														}
														userActions.onDelete = () => {
															setTherapist(user)
															deleteState.open()
														}
													} else if (user.rejectedAt) {
														userActions.onAccept = () => {
															setTherapist(user)
															acceptState.open()
														}
														userActions.onDelete = () => {
															setTherapist(user)
															deleteState.open()
														}
													} else if (user.acceptedAt) {
														userActions.onDeactivate = () => {
															setTherapist(user)
															deactivateState.open()
														}
														userActions.onDelete = () => {
															setTherapist(user)
															deleteState.open()
														}
													} else if (
														!user.acceptedAt &&
														!user.rejectedAt &&
														user.isUnderReview
													) {
														userActions.onAccept = () => {
															setTherapist(user)
															acceptState.open()
														}
														userActions.onReject = () => {
															setTherapist(user)
															rejectState.open()
														}
													} else {
														userActions.onAccept = () => {
															setTherapist(user)
															acceptState.open()
														}
														userActions.onReject = () => {
															setTherapist(user)
															rejectState.open()
														}
														userActions.onDelete = () => {
															setTherapist(user)
															deleteState.open()
														}
													}

													return (
														<motion.tr
															key={index}
															initial={{ opacity: 0 }}
															animate={{ opacity: 1 }}
															exit={{ opacity: 0 }}
															className={`border-t gap-2 text-sm py-3 px-2 transition duration-200 ease-in-out ${rowBgClass}`}
														>
															<td className="px-4 py-3 ">
																{' '}
																<div
																	className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center bg-gray-100`}
																>
																	{index + 1}
																</div>
															</td>
															<td className="px-4 py-3">
																<div
																	onClick={() =>
																		navigate(`/therapists/${user.id}/profile`)
																	}
																	className='font-normal cursor-pointer'
																>
																	<span className="text-sm">
																		<strong>
																			{getFullName(
																				user.firstname,
																				user.lastname
																			)}
																		</strong>
																		{(() => {
																			if (
																				user.deactivatedAt &&
																				!user.deletedBy
																			) {
																				return (
																					<span className="text-red-500 text-sm ml-2">
																						(Deactivated)
																					</span>
																				)
																			} else if (user.deletedBy) {
																				return (
																					<span className="text-red-500 text-sm ml-2">
																						(Deleted)
																					</span>
																				)
																			}
																			return null
																		})()}
																	</span>
																</div>
																<div className="text-sm">
																	<div className="col-span-4 md:col-span-3 flex flex-row items-center">
																		<span>{user.email}</span>
																		{VerificationDateComponent(
																			user.emailVerifiedAt
																		)}
																	</div>
																</div>
																{(() => {
																	if (user.deletedBy) {
																		return (
																			<div className="text-xs text-red-500 font-medium mt-1">
																				{DELETED_BY}{' '}
																				{user.deletedBy
																					.charAt(0)
																					.toUpperCase() +
																					user.deletedBy.slice(1)}
																			</div>
																		)
																	}
																	return null
																})()}

																{(() => {
																	if (user.userDeletedAt) {
																		return (
																			<div className="text-xs text-red-500 font-medium mt-1">
																				{DELETED_AT}{' '}
																				{formatDateMDY(user.userDeletedAt)}
																			</div>
																		)
																	}
																	return null
																})()}
															</td>

															<td className={getUserClassName()}>
																{(() => {
																	if (licenses.length > 0) {
																		return (
																			<div className="space-y-1">
																				{licenses
																					.slice(0, licensesToShow)
																					.map((license: any, i: number) => (
																						<div key={i} className="space-y-1">
																							{(() => {
																								if (licenses.length > 1) {
																									return (
																										<p className="font-semibold text-gray-700">
																											<strong>
																												License {i + 1}
																											</strong>
																										</p>
																									)
																								}
																								return null
																							})()}
																							<p>
																								<span className="font-semibold">
																									Type:
																								</span>{' '}
																								{(() => {
																									if (
																										license?.license_type?.label
																									) {
																										return license.license_type
																											.label
																									} else {
																										return 'N/A'
																									}
																								})()}
																							</p>
																							<p>
																								<span className="font-semibold">
																									License No.:
																								</span>{' '}
																								{(() => {
																									if (license?.license_number) {
																										return license.license_number
																									} else {
																										return 'N/A'
																									}
																								})()}
																							</p>
																							<p>
																								<span className="font-semibold">
																									Status:
																								</span>{' '}
																								{(() => {
																									if (
																										license?.license_status
																											?.label
																									) {
																										return license
																											.license_status.label
																									} else {
																										return 'N/A'
																									}
																								})()}
																							</p>
																							<p>
																								<span className="font-semibold">
																									State:
																								</span>{' '}
																								{(() => {
																									if (
																										license?.license_state
																											?.label
																									) {
																										return license.license_state
																											.label
																									} else {
																										return 'N/A'
																									}
																								})()}
																							</p>
																						</div>
																					))}

																				{(() => {
																					if (licenses.length > 1) {
																						return (
																							<button
																								className="text-therapy-blue-dark text-xs hover:underline font-medium mt-1"
																								onClick={() =>
																									setExpandedRow(
																										expandedRow === index
																											? null
																											: index
																									)
																								}
																							>
																								{toggleButtonText}
																							</button>
																						)
																					}
																					return null
																				})()}
																			</div>
																		)
																	} else {
																		return (
																			<span className="text-red-500">
																				License details not available
																			</span>
																		)
																	}
																})()}
															</td>

															<td className="px-4 py-3 ">
																{(() => {
																	if (user.deletedBy)
																		return (
																			<span className="text-red-500">
																				Deleted
																			</span>
																		)
																	if (user.deactivatedAt)
																		return (
																			<span className="text-red-500">
																				Deactivated
																			</span>
																		)
																	if (user.acceptedAt)
																		return (
																			<span className="text-green-600">
																				Accepted
																			</span>
																		)
																	if (user.rejectedAt)
																		return (
																			<span className="text-yellow-600">
																				Rejected
																			</span>
																		)
																	if (user.isUnderReview)
																		return (
																			<span className="text-orange-500">
																				Review in Progress
																			</span>
																		)
																	return (
																		<span className="text-gray-600">
																			Pending
																		</span>
																	)
																})()}
															</td>

															<td className="px-4 py-3 ">
																{user.statusReason}
															</td>

															<td className="px-4 py-3 text-xs">
																<ActionButton {...userActions} />
															</td>
														</motion.tr>
													)
												})}
										</tbody>
									</PaginatedDataContainer>
								)
							}
						})()}
					</table>
					{(() => {
						if (!isFetchingNextPage) {
							return (
								<div className="flex justify-center items-center my-6">
									<span className="text-gray-500 text-md">No more data</span>
								</div>
							)
						}
						return null
					})()}
				</div>
				{/* Only show DeleteDialog with reasons dropdown for deactivation, not for activation */}
				{therapist?.deactivatedAt ? (
					<NormalDialog
						state={deactivateState}
						title={'Activate Therapist Account'}
						confirmLabel={'Reactivate'}
						verifyText={'REACTIVATE'}
						loading={loading}
						onCancel={() => {
							setTherapist(undefined)
							deactivateState.close()
						}}
						onAccept={async () => {
							if (!therapist?.id) return
							setLoading(true)
							const res = await userRepo.activate(
								`/users/activate/${therapist.id}`
							)
							if (res) {
								setLoading(false)
								setTherapist(undefined)
								refetch()
								deactivateState.close()
							}
						}}
					>
						<p className="text-sm">
							Are you sure you want to Reactivate the account of&nbsp;
							<strong>{getTherapistName(therapist)}</strong>?
						</p>
					</NormalDialog>
				) : (
					<DeleteDialog
						state={deactivateState}
						deletePassword={true}
						title={'Deactivate Therapist'}
						url={`/users/deactivate/${therapist?.id}?currentDate=${currentDate}`}
						refetch={customRefetch}
						setGlobalLoading={setLoading}
						isDeactivate={true}
					>
						<p className="text-sm">
							Are you sure you want to deactivate the account of&nbsp;
							<strong>{getTherapistName(therapist)}</strong>?
						</p>
					</DeleteDialog>
				)}
				<FormDialog
					data-testid="reset-password-confirm-button"
					state={resetPasswordState}
					title="Reset Password"
					confirmLabel="Reset"
					loading={loading}
					onCancel={() => {
						setTherapist(undefined)
						setEnteredEmail('')
						setErrorMessage(null)
						resetPasswordState.close()
					}}
					onSubmit={handleResetPasswordSubmit}
				>
					<p className="text-sm">
						Please enter the email address associated with{' '}
						<strong>
							{therapist
								? getFullName(therapist.firstname, therapist.lastname)
								: ''}
						</strong>
						's account to reset the password. <br /> <br />{' '}
						<strong>{therapist?.email}</strong>
					</p>
					<br />
					<InputComponent
						label="Email"
						id="email"
						data-testid="email"
						aria-label="Email"
						//type="email"
						value={enteredEmail}
						onChange={(value) => {
							setEnteredEmail(value)
							setErrorMessage(null)
						}}
						isRequired
					/>
					{errorMessage && (
						<p className="text-red-500 text-sm">{errorMessage}</p>
					)}
				</FormDialog>
				<DeleteDialog
					state={deleteState}
					deletePassword={true}
					title={'Delete Therapist'}
					url={`/users/${therapist?.id}?currentDate=${currentDate}`}
					refetch={customRefetch}
					setGlobalLoading={setLoading}
					isDeactivate={false}
				>
					<p className="text-sm">{DELETE_MESSAGE}</p>
				</DeleteDialog>
				<NormalDialog
					state={acceptState}
					title="Accept Therapist Account"
					confirmLabel="Accept"
					verifyText="ACCEPT"
					loading={loading}
					onCancel={() => {
						setTherapist(undefined)
						acceptState.close()
					}}
					onAccept={async () => {
						if (!therapist?.id) return
						setLoading(true)
						const res = await therapistRepo.accept(therapist.id)
						if (res) {
							setLoading(false)
							setTherapist(undefined)
							refetch()
							acceptState.close()
						}
					}}
				>
					<p className="text-sm">
						Are you sure you want to accept the account of&nbsp;
						<strong>
							{therapist
								? getFullName(therapist.firstname, therapist.lastname)
								: ''}
						</strong>
						?
					</p>
				</NormalDialog>
				<DeleteDialog
					state={rejectState}
					title="Reject Therapist Account"
					verifyText="REJECT"
					url={`/therapists/reject/${therapist?.id}`}
					refetch={customRefetch}
					setGlobalLoading={setLoading}
					isDeactivate={false}
				>
					<p className="text-sm">
						Are you sure you want to reject the account of&nbsp;
						<strong>
							{therapist
								? getFullName(therapist.firstname, therapist.lastname)
								: ''}
						</strong>
						?
					</p>
				</DeleteDialog>
				{/* Added restore dialog */}
				<NormalDialog
					state={restoreState}
					title="Restore Therapist Account"
					confirmLabel="Restore"
					verifyText="RESTORE"
					loading={loading}
					onCancel={() => {
						setTherapist(undefined)
						restoreState.close()
					}}
					onAccept={handleRestoreUser}
				>
					<p className="text-sm">
						Are you sure you want to restore the account of&nbsp;
						<strong>
							{therapist
								? getFullName(therapist.firstname, therapist.lastname)
								: ''}
						</strong>
						?
					</p>
				</NormalDialog>

				<NormalDialog
					state={viewHistoryPopupState}
					title="View History"
					onCancel={() => setViewHistoryPopup(false)}
					width="w-[800px]"
				>
					{renderHistoryContent}
				</NormalDialog>
			</div>
		</PaginationAwareContainer>
	)
}

export default TherapistListPage
