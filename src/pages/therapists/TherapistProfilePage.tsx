import TherapistRepository from '@/repositories/TherapistRepository'
import { User } from '@/types/user.interface'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
	genders,
	religions,
	areaOfFocus,
	specialities,
} from '@/configs/registration.configs'
import LicenseSection from '../domain/therapy/profile/license-section.page'
import SpecializationsSection from '../domain/therapy/profile/specializations-section.page'
import RankSpecialtiesSection from '../domain/therapy/profile/rank-specialties-section.page'
import RankSubSpecialtiesSection from '../domain/therapy/profile/rank-sub-specialties-section.page'
import ModalitiesSection from '../domain/therapy/profile/modalities-section.page'
import PracticeFocusSection from '../domain/therapy/profile/practice-focus-section.page'
import PracticeInfo from '../domain/therapy/profile/practice-info-section.page'
import { ProfileSection } from '../domain/therapy/profile/profile-info-section.page'
import { request } from "@/utils/request.utils";
import { motion } from 'framer-motion'
import { US_TIMEZONES } from '../auth/constants'
import { getTherapistSubscriptionActionById } from '@/store/slicers/therapist.slicer'
import { useDispatch } from 'react-redux'
import { AppDispatch } from '@/store/index'
import SubscriptionSection from '../domain/therapy/profile/subscription-section.page'

const { VITE_USER_PROFILE } = import.meta.env
interface Appointment {
	id: number
	appointmentDate: string // ISO 8601 format (e.g., "2025-01-30T16:00:00.000Z")
	description: string
	initial_appointment?: string
	cancelledAppointment?: string
	timezone?: string
	patient: {
		id: number
		firstname: string
		lastname: string
		email: string
		phone: string | null
		dob: string
		gender: string
		address: {
			zip?: string
			city: string
			state: string
			street: string
			country: string
			zipCode?: string
			lat?: number
			lng?: number
			place_id?: string
			description?: string
			full_address?: string
		}
		bio: string | null
		userProfile: string | null
		cancelled: boolean
	}
	formattedDateTime: string
	past: boolean
}

const TherapistProfilePage = () => {
	const therapistRepo = new TherapistRepository()	
	const location = useLocation()
	const pathname = location.pathname.replace(/\/$/, '').split('/')
	const therapistId = parseInt(pathname[pathname.length - 2], 10)	
	const [therapist, setTherapist] = useState<User>()
	const [loading, setLoading] = useState(true)
	const [tab, setTab] = useState<'about' | 'schedule'>('about')

	const [scheduleFetched, setScheduleFetched] = useState(false)
	const [appointments, setAppointments] = useState<
		{
			id: string
			name: string
			avatar: string
			dateTime: string
			type: string
			isPast: boolean
			cancelled: boolean
			timezone: string
		}[]
	>([])

	const [visibleAppointments, setVisibleAppointments] = useState(5) // Number of appointments to display
	const lastAppointmentRef = useRef<HTMLDivElement>(null)
	const [shouldScroll, setShouldScroll] = useState(false)
	const containerRef = useRef<HTMLDivElement>(null)
	const dispatch = useDispatch<AppDispatch>()

	const formatTime = (dateTime: string) => {
		const date = new Date(dateTime)
		const hours = date.getHours()
		const minutes = date.getMinutes()
		const suffix = hours >= 12 ? 'PM' : 'AM'
		const formattedHour = hours % 12 || 12 // Convert to 12-hour format
		return `${formattedHour}:${minutes.toString().padStart(2, '0')} ${suffix}`
	}

	const usTimezones = US_TIMEZONES

	const [subscriptionInfo, setSubscriptionInfo] = useState<any>(null)

	useEffect(() => {
		const fetchTherapistSubscription = async () => {
			if (!therapistId) return

			const res = await dispatch(
				getTherapistSubscriptionActionById(therapistId)
			)
			if (res && res.status === 200) {
				if (res.data.plan) {
					setSubscriptionInfo(res.data.plan)
				} else {
					setSubscriptionInfo(null)
				}
			}
		}
		fetchTherapistSubscription()
	}, [therapistId])

	useEffect(() => {
		if (tab === 'schedule' && !scheduleFetched) {
			const fetchAppointments = async () => {
				try {
					const response = await request.get<Appointment[]>(
						`patients/appointments?therapistId=${therapistId}`
					)

					interface FormattedAppointment {
						id: string
						name: string
						avatar: string
						dateTime: string
						type: string
						isPast: boolean
						cancelled: boolean
						timezone: string
					}

					const formattedAppointments: FormattedAppointment[] =
						response.data.map(
							(appointment: Appointment): FormattedAppointment => {
								const appointmentDateTime = new Date(
									appointment.appointmentDate
								).toISOString() // Convert to ISO format

								const isPast = appointment.past
								const cancelledAt = appointment.cancelledAppointment

								const dateTime = `${new Date(
									appointmentDateTime
								).toLocaleDateString(
									'en-US'
								)} - ${formatTime(appointmentDateTime)}`

								// Check if the appointment is the first one for the patient
								const appointmentType =
									appointment.initial_appointment === 'first'
										? 'First Appointment'
										: 'Regular Appointment'

								return {
									id: appointment.id.toString(),
									name: `${appointment.patient.firstname} ${appointment.patient.lastname}`,
									avatar: appointment.patient.userProfile
										? `${VITE_USER_PROFILE}${appointment.patient.userProfile}?size=10`
										: `https://ui-avatars.com/api?name=${appointment.patient.firstname || ''}+${appointment.patient.lastname || ''}`,
									dateTime,
									type: appointmentType,
									isPast,
									cancelled: !!cancelledAt,
									timezone:
										usTimezones.find((tz) => tz.value === appointment.timezone)
											?.label || '',
								}
							}
						)
					// Set appointments directly (backend handles sorting)
					setAppointments(formattedAppointments)
				} catch (error: any) {
					throw new Error(`Failed to fetch appointments: ${error.message}`)
				}
			}

			fetchAppointments()
			setScheduleFetched(true)
		}
	}, [tab, scheduleFetched])

	// Function to handle "Show More" click
	const handleShowMore = useCallback(() => {
		setVisibleAppointments((prev) => prev + 5)
		setShouldScroll(true)
	}, [])

	// Function to handle "Show Less" click
	const handleShowLess = useCallback(() => {
		setVisibleAppointments(5)
		setShouldScroll(true)
	}, [])

	useEffect(() => {
		if (shouldScroll && containerRef.current) {
			const container = containerRef.current
			const scrollToBottom = () => {
				container.scrollTop = container.scrollHeight - container.clientHeight
			}
			const scrollToTop = () => {
				container.scrollTop = 0
			}

			if (visibleAppointments > 5) {
				scrollToBottom()
			} else {
				scrollToTop()
			}

			setShouldScroll(false)
		}
	}, [visibleAppointments, shouldScroll])

	const license = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'license'
	)?.payloadInfo
	const specializations = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'specialization'
	)?.payloadInfo
	const rankSpecialties = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'rank-specialties'
	)?.payloadInfo
	const rankSubSpecialties = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'rank-sub-specialties'
	)?.payloadInfo
	const modalities = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'modalities'
	)?.payloadInfo
	const practiceFocus = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'practice-focus'
	)?.payloadInfo
	const practiceInfo = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'practice-info'
	)?.payloadInfo
	const profile = therapist?.registrationInfo?.find(
		(page) => page.pageName === 'profile'
	)?.payloadInfo
	

	const imageUrl =
		profile?.user_profile ||
		`https://ui-avatars.com/api?name=${therapist?.firstname}+${therapist?.lastname}`

	useEffect(() => {
		if (therapistId) {							
			therapistRepo.getOne(therapistId).then((res) => {			
				setTherapist(res)
				setLoading(false)
			})
		}
	}, [])

	const emailVerificationIconClass = therapist?.emailVerifiedAt
		? 'fa-circle-check text-green-500'
		: 'fa-circle-xmark text-red-500'

	const aboutTabClass = tab === 'about' ? 'tab active' : 'tab'
	const scheduleTabClass = tab === 'schedule' ? 'tab active' : 'tab'
	const showMoreOrLessButton =
		visibleAppointments < appointments.length ? (
			<button
				onClick={handleShowMore}
				className="text-therapy-blue-dark font-bold hover:underline"
			>
				+ Show More
			</button>
		) : (
			<button
				onClick={handleShowLess}
				className="text-therapy-blue-dark font-bold hover:underline"
			>
				Show Less
			</button>
		)
		
	// Extracted components for the appointments display
	const renderNoAppointmentsFound = () => (
		<div className="flex flex-col items-center justify-center py-8">
			<div className="text-lg text-gray-600 font-medium mb-2">
				No appointments found
			</div>
			<p className="text-sm text-gray-500 text-center max-w-md">
				This therapist currently has no scheduled appointments.
			</p>
		</div>
	)

	const renderAppointmentsList = () => (
		<div className="overflow-hidden">
			<div className="grid grid-cols-1 sm:grid-cols-12 py-3 px-4 text-sm font-bold text-black border-b-2 border-gray-400">
				<div className="sm:col-span-4 mb-2 sm:mb-0">Name</div>
				<div className="sm:col-span-4 mb-2 sm:mb-0">
					Date & Time
				</div>
				<div className="sm:col-span-4 mb-2 sm:mb-0">
					Appointment Type
				</div>
			</div>
			<div
				className="appointment-section overflow-y-auto max-h-[400px]"
				id="appointmentsContainer"
				ref={containerRef}
			>
				{appointments
					.slice(0, visibleAppointments)
					.map((appointment, index) => (
						<motion.div
							key={appointment.id}
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							className={`grid grid-cols-1 sm:grid-cols-12 border-b-2 border-gray-400 gap-2 text-sm hover:bg-gray-50 py-3 px-4 transition duration-200 ease-in-out ${
								appointment.isPast ? 'text-gray-400' : ''
							}`}
							ref={
								index === visibleAppointments - 1
									? lastAppointmentRef
									: null
							}
						>
							<div className="sm:col-span-4 flex items-center mb-2 sm:mb-0">
								<img
									src={appointment.avatar || '/placeholder.svg'}
									alt={appointment.name}
									className="w-8 h-8 rounded-full mr-3"
								/>
								<span className="font-medium">
									{appointment.name}
								</span>
							</div>
							<div className="sm:col-span-4 flex items-center mb-2 sm:mb-0">
								{appointment.dateTime}
							</div>
							<div className="sm:col-span-4 flex items-center sm:justify-start mb-2 sm:mb-0">
								{appointment.type}

								{appointment.cancelled && (
									<span
										className="ml-2 px-2 py-1 text-xs font-bold text-red-600 bg-red-100 rounded"
										title="Cancelled Appointment"
									>
										Canceled
									</span>
								)}
							</div>
						</motion.div>
					))}
			</div>
		</div>
	)

	const appointmentsContent =
	appointments.length === 0
		? renderNoAppointmentsFound()
		: renderAppointmentsList()


	return (
		<div className="user-profile-page">
			{loading ? (
				<div className="page-content px-4 sm:px-14 pt-10 pb-10 space-y-10 animate-pulse"  data-testid="loading-indicator">
					{/* Profile Picture & Name */}
					<div className="flex flex-col items-center space-y-4">
						<div className="w-24 h-24 rounded-full bg-gray-300 dark:bg-gray-500" />
						<div className="w-40 h-6 rounded-full bg-gray-300 dark:bg-gray-500" />
						<div className="w-32 h-4 rounded-full bg-gray-300 dark:bg-gray-500" />
					</div>

					{/* Tabs */}
					<div className="flex justify-center gap-6">
						<div className="w-20 h-6 rounded-full bg-gray-300 dark:bg-gray-500" />
						<div className="w-20 h-6 rounded-full bg-gray-300 dark:bg-gray-500" />
					</div>

					{/* Section Cards */}
					<div className="space-y-10">
						{/* Each section simulates Profile Info, License, etc. */}
						{Array(4)
							.fill(null)
							.map((_, i) => (
								<div
									key={i}
									className="bg-white p-6 rounded-lg shadow-md border border-gray-300 space-y-4 mx-auto"
								>
									<div className="h-6 w-40 bg-gray-300 dark:bg-gray-500 rounded-full" />
									<div className="h-4 w-full bg-gray-200 dark:bg-gray-600 rounded" />
									<div className="h-4 w-5/6 bg-gray-200 dark:bg-gray-600 rounded" />
									<div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-600 rounded" />
								</div>
							))}
					</div>

					{/* Appointments (if 'schedule' tab) */}
					{tab === 'schedule' && (
						<div className="space-y-4 max-w-3xl mx-auto">
							<div className="h-6 w-32 bg-gray-300 rounded-full dark:bg-gray-500 mx-auto" />
							{Array(3)
								.fill(null)
								.map((_, i) => (
									<div key={i} className="flex items-center gap-4">
										<div className="w-12 h-12 rounded-full bg-gray-300 dark:bg-gray-500" />
										<div className="flex-1 space-y-2">
											<div className="h-4 w-1/2 bg-gray-200 dark:bg-gray-600 rounded" />
											<div className="h-4 w-1/3 bg-gray-200 dark:bg-gray-600 rounded" />
										</div>
									</div>
								))}
						</div>
					)}
				</div>
			) : (
				<>
					<div className="profile-header">
						<div className="navigation">
							<Link to="/therapists" title="Therapist list">
								<i className="fa-solid fa-chevron-left"></i>
							</Link>
						</div>

						<h3 className="text-therapy-blue-dark">Therapist Profile</h3>

						<div className="stats-information">
							<Link to={`/notification-settings/${therapistId}`}>
								<i className="fa-solid fa-bell"></i>
							</Link>
						</div>
					</div>

					<div className="user-personal-info">
						<div className="profile-picture">
							<img src={imageUrl} />
						</div>
						<div className="flex flex-col items-center text-center space-y-1">
							<div className="flex items-center justify-center space-x-1">
								<h4 className="capitalize text-lg font-medium">
									{therapist?.firstname} {therapist?.lastname}
								</h4>

								<i
									className={`mt-4 fa-sharp fa-solid ${emailVerificationIconClass}`}
								></i>
							</div>

							{practiceInfo?.business_address?.description && (
								<p className="text-base text-gray-700 capitalize">
									{practiceInfo.business_address.description ?? ''}
								</p>
							)}
						</div>
					</div>

					<div className="user-additional-info">
						<div className="user-tab">
							<div className={aboutTabClass} onClick={() => setTab('about')} data-testid="about-tab">
								About
							</div>
							<div
								className={`tab ${scheduleTabClass}`}
								onClick={() => setTab('schedule')}
							>
								Schedule
							</div>
						</div>

						{tab === 'about' && (
							<>
								<div className="page-content px-14">
									{/* License Information Section */}
									<div className="mt-8">
										<div className="flex gap-4 items-center">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												License(s)
											</h1>
										</div>

										<LicenseSection license={license} />
									</div>

									{/* Specialization Section */}
									<div className="mt-8">
										<div className="flex items-center gap-4">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Specialization
											</h1>
										</div>

										<SpecializationsSection
											specializations={specializations}
											specialities={specialities}
										/>
									</div>

									<div className="mt-8">
										{/* Title with Edit Icon */}
										<div className="flex items-center gap-4">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Rank Specialties
											</h1>
										</div>

										{/* Render Rank Specialties */}
										<RankSpecialtiesSection
											rankSpecialties={rankSpecialties}
											specialities={specialities}
										/>
										<div className="flex items-center gap-4 mt-8">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Rank Sub-Specialties
											</h1>
										</div>

										<RankSubSpecialtiesSection
											rankSubSpecialties={rankSubSpecialties}
											specialities={specialities}
										/>
									</div>

									<div className="mt-8">
										<div className="flex gap-4 items-center">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Modalities
											</h1>
										</div>

										<ModalitiesSection modalities={modalities} />
									</div>

									{/* {Practice focus} */}
									<div className="mt-8">
										<div className="flex gap-4 items-center">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Practice Focus
											</h1>
										</div>

										<PracticeFocusSection
											practiceFocus={practiceFocus}
											genders={genders}
											religions={religions}
											areaOfFocus={areaOfFocus}
										/>
									</div>

									<div className="mt-8" data-testid="practice-info-section">
										<div className="flex gap-4 items-center">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Practice Information
											</h1>
										</div>

										<PracticeInfo practiceInfo={practiceInfo} />
									</div>

									<div className="mt-8" data-testid="profile-info-section">
										<div className="flex gap-4 items-center">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Profile Info
											</h1>
										</div>

										<ProfileSection profile={profile} />
									</div>
									
									<div className="mt-8" data-testid="profile-info-section">
										<div className="flex gap-4 items-center">
											<h1 className="text-3xl font-bold text-therapy-blue-dark">
												Subscription Details
											</h1>
										</div>

										<SubscriptionSection subscription={subscriptionInfo} />
									</div>
									
								</div>

								
							</>
						)}

						{tab === 'schedule' && (
							<>
								<div className="h-full flex flex-col flex-grow mt-4">
									<div className="flex-none flex flex-col justify-center items-center mb-4 text-center">
										<div className="text-2xl sm:text-3xl font-medium text-therapy-blue-dark">
											Appointments
										</div>

										{appointments.length > 0 && appointments[0]?.timezone && (
											<p className="text-sm text-gray-600 mt-1">
												Note: Appointment times are displayed in the therapist's
												timezone ({appointments[0]?.timezone || ''}).
											</p>
										)}
									</div>

									<div className="mx-4 mt-8 mb-8">
									{appointmentsContent}
										{appointments.length > 5 && (
											<div className="flex justify-center mt-4">
												{showMoreOrLessButton}
											</div>
										)}
									</div>
								</div>
							</>
						)}
					</div>
				</>
			)}
		</div>
	)
}

export default TherapistProfilePage