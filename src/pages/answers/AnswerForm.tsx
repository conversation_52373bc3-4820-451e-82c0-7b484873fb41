import FormDialog from "@/components/dialogs/FormDialog";
import InputComponent from "@/components/forms/InputComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import AnswerRepository from "@/repositories/AnswerRepository";
import { Answer } from "@/types/answer.interface";
import { FormEvent, useEffect, useState } from "react";
import { OverlayTriggerState } from "react-stately";

type AnswerFormProps = {
  state: OverlayTriggerState;
  answer?: Answer;
  refetch?: () => void;
};

type AnswerFormData = {
  answer?: string;
  info?: string;
};

const AnswerForm = ({ state, answer, refetch }: AnswerFormProps) => {
  const repository = new AnswerRepository();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<AnswerFormData>(
    {} as AnswerFormData
  );

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    let result;
    setLoading(true);
    if (answer?.id) {
      result = await repository.update(answer.id, formData);
    } else {
      result = await repository.create(formData);
    }
    setLoading(false);
    if (result) {
      refetch && refetch();
      setFormData({} as AnswerFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({} as AnswerFormData);
    state.close();
  };

  useEffect(() => {
    if (answer) {
      setFormData({
        answer: answer.answer,
        info: answer.info,
      });
    } else {
      setFormData({} as AnswerFormData);
    }
  }, [answer]);

  return (
    <FormDialog
      title="Answer Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel="Save"
      loading={loading}
      width="w-[420px]"
      state={state}
    >
      <div className="flex flex-col space-y-4">
        <InputComponent
          label={"Answer"}
          id="answer"
          onChange={(value) => setFormData({ ...formData, answer: value })}
          value={formData.answer || ""}
          isRequired
        />
        <TextAreaComponent
          label={"Info"}
          id="info"
          onChange={(value) => setFormData({ ...formData, info: value })}
          value={formData.info || ""}
        />
      </div>
    </FormDialog>
  );
};

export default AnswerForm;
