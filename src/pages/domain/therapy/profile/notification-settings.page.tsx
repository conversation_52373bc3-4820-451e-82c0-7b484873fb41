import 'react-phone-number-input/style.css'
import PhoneInput from 'react-phone-number-input'
import { FC, useEffect, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { appointmentNotifications } from '@/configs/registration.configs'
import { fetchRegInfoAction } from '@/store/slicers/therapist.slicer'
import { AppDispatch } from '@/store/index'
import { useDispatch } from 'react-redux'
import { Link, useParams } from 'react-router-dom'

const NotificationSettings: FC = () => {
	const { ref } = useInView()
	const dispatch = useDispatch<AppDispatch>()
	const [notificationInfo, setNotificationInfo] = useState<any>({})
	const { therapistId } = useParams()
	const [loading, setLoading] = useState(true)

	const fetchNotificationInfo = async () => {
		try {
			if (!therapistId) return
			const res = await dispatch(
				fetchRegInfoAction({
					pageName: 'waitlist-notifications',
				})
			)
			if (res && res.status === 200) {
				setNotificationInfo(res.data.payloadInfo)
			}
		} catch (error: any) {
			throw new Error(`Failed to fetch appointments: ${error.message}`)
		}
	}

	useEffect(() => {
		const loadData = async () => {
			setLoading(true)
			await fetchNotificationInfo()
			setLoading(false)
		}
	
		loadData()
	}, [therapistId])
	

	const selectedNotifications = notificationInfo?.notification_preferences || []

	let notificationItems;

	if (selectedNotifications.length > 0) {
	notificationItems = selectedNotifications.map((val: string) => {
		const label =
		appointmentNotifications.find((item) => item.value === val)?.label || val;
		return <li key={val}>{label}</li>;
	});
	} else {
	notificationItems = <li>No preferences selected</li>;
	}

	return (
		
		<div ref={ref}>		

			<div
				style={{ borderBottom: '1px solid #ccc' }}
				className="w-full h-20 flex items-center justify-center relative px-14 text-white"
			>
				
				<div className="absolute left-6 top-1/2 -translate-y-1/2">
					<Link
						to={`/therapists/${therapistId}/profile`}
						title="Therapist list"
						className="text-therapy-blue-dark text-2xl hover:text-therapy-blue-light"
					>
						<i className="fa-solid fa-chevron-left"></i>
					</Link>
				</div>

				
				<h3 className="text-3xl font-bold text-therapy-blue-dark m-0">
					Notifications Preferences
				</h3>
			</div>

			{loading ? (
				<div className="animate-pulse">	
				<div className="page-content px-4 sm:px-14 pt-10 pb-10 space-y-10">		
					<div className="mt-8 space-y-4">
						<div className="w-40 h-6 bg-gray-300 dark:bg-gray-500 rounded" />
						<ul className="space-y-2 pl-5 list-disc">
							<li className="w-60 h-4 bg-gray-300 dark:bg-gray-500 rounded" />
							<li className="w-60 h-4 bg-gray-300 dark:bg-gray-500 rounded" />							
						</ul>
					</div>
					<>
						<div className="w-[300px] ml-5 mt-5 mb-5 space-y-2">
							<div className="w-40 h-4 bg-gray-300 dark:bg-gray-500 rounded" />							
						</div>
					</>
				</div>
			</div>
			) : (
				<>
			<div className="page-content px-14 pt-4">
				
				<div className="mt-8">
					<div className="ml-5 mb-4">
						<h2 className="font-semibold mb-2 text-lg">
							Selected Preferences
						</h2>
						<ul className="list-disc list-inside text-gray-700">
						{notificationItems}
						</ul>
					</div>

					{selectedNotifications.includes('text') && (
						<div className="w-[300px] ml-5 mt-5 mb-5">
							<p className="mb-2 text-sm text-gray-400">Mobile Phone Number</p>
							<PhoneInput
								international
								defaultCountry="US"
								countryCallingCodeEditable={false}
								value={notificationInfo?.phone_number || ''}
								onChange={() => {
									// No-op: PhoneInput is disabled, so changes are not handled
								}}
								disabled
								className="register-phone-input w-full"
							/>							
						</div>
					)}
				</div>
			</div>

			</>
			)}
		</div>
	)
}

export default NotificationSettings
