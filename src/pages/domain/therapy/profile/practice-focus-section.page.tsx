import React from 'react'
import {
	practiceFocus as practiceFocusOptions
} from '@/configs/registration.configs'

// Removed unused interfaces: ReligiousSpecialization, AdditionalFocus

type OptionType = {
	value: string | number
	label: string
}

interface PracticeFocusSectionProps {
	practiceFocus: Record<string, any> | null
	genders: OptionType[]
	religions: OptionType[]
	areaOfFocus: OptionType[]
}

const getLabel = (value: string, configArray?: OptionType[]): string => {
	if (!Array.isArray(configArray)) {
		console.warn('getLabel received a non-array:', configArray)
		return value
	}

	const found = configArray.find((item) => item.value === value)
	return found ? found.label : value
}

const keyLabels: Record<string, string> = {
	client_served: 'Clients Served',
	client_speciality: 'Preferred Client',
	genders: 'Genders I Work With',
	additional_focus: 'Additional Area of Focus',
}

const getOptionsForKey = (key: string, genders: OptionType[], religions: OptionType[], areaOfFocus: OptionType[]) => {
	switch (key) {
		case 'genders':
			return genders
		case 'client_served':
		case 'client_preferred':
			return practiceFocusOptions
		case 'religious_specialization':
			return religions
		case 'additional_focus':
			return areaOfFocus
		default:
			return []
	}
}

const renderListItems = (items: string[], options: OptionType[]) => (
	<ul className="ml-6 list-disc">
		{items.filter(item => item !== 'other').map((item) => (
			<li key={item} className="text-gray-600">
				{getLabel(item, options)}
			</li>
		))}
	</ul>
)

const renderObjectValue = (value: any, key: string, religions: OptionType[], areaOfFocus: OptionType[]) => {
	if (typeof value !== 'object' || value === null) return null

	const options = getOptionsForKey(key, [], religions, areaOfFocus)
	const checkedList = Array.from(new Set(value.checked_list || []))

	return (
		<ul className="list-disc">
			{renderListItems(checkedList as string[], options)}
			{value.other_religion && <li className="text-gray-600 ml-6">{value.other_religion}</li>}
			{value.other_focus && <li className="text-gray-600 ml-6">{value.other_focus}</li>}
		</ul>
	)
}

const PracticeFocusSection: React.FC<PracticeFocusSectionProps> = ({
	practiceFocus,
	genders,
	religions,
	areaOfFocus,
}) => {
	if (!practiceFocus || Object.keys(practiceFocus).length === 0) {
		return <p className="text-gray-500 mt-2">No practice focus available</p>
	}

	return (
		<ul className="mt-4 space-y-4">
			{Object.entries(practiceFocus).map(([key, value]) => {
				const label = keyLabels[key] || key.replace(/_/g, ' ')
				const options = getOptionsForKey(key, genders, religions, areaOfFocus)

				let content

				if (Array.isArray(value)) {
					content = renderListItems(value, options)
				} else if (typeof value === 'object' && value !== null) {
					content = renderObjectValue(value, key, religions, areaOfFocus)
				} else {
					content = (
						<p className="text-gray-600">
							{getLabel(String(value), options)}
						</p>
					)
				}

				return (
					<li key={key} className="capitalize">
						<span className="font-semibold text-lg text-gray-700 capitalize">
							{label}
						</span>
						{content}
					</li>
				)
			})}
		</ul>
	)
}

export default PracticeFocusSection
