import { AppDispatch, RootState } from '@/store/index'
import { fetchRegInfoAction } from '@/store/slicers/therapist.slicer'
import { FC, startTransition, useEffect, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import {
	setEditFlag,
	setFormContent,
	signOut,
} from '@/store/slicers/auth.slicer'
import Modal from './modal'
import { request } from "@/utils/request.utils";
import {
	genders,
	religions,
	areaOfFocus,
	specialities,
} from '@/configs/registration.configs'
import LicenseSection from './license-section.page'
import SpecializationsSection from './specializations-section.page'
import RankSpecialtiesSection from './rank-specialties-section.page'
import ModalitiesSection from './modalities-section.page'
import RankSubSpecialtiesSection from './rank-sub-specialties-section.page'
import PracticeFocusSection from './practice-focus-section.page'
import PracticeInfo from './practice-info-section.page'
import { ProfileSection } from './profile-info-section.page'
import { DELETE_PROFILE, DELETE_PROFILE_CONFIRM } from './constants'

// Profile Share Section Component
const ProfileShareSection: FC<{ profileId: string | undefined }> = ({
	profileId,
}) => {
	const [profileUrl, setProfileUrl] = useState('')
	const [copied, setCopied] = useState(false)
	
	useEffect(() => {
		if (profileId) {
			setProfileUrl(`${import.meta.env.VITE_PROFILE_URL}${profileId}`)
		}
	}, [profileId])

	const handleCopyLink = () => {
		navigator.clipboard.writeText(profileUrl)
		setCopied(true)

		setTimeout(() => {
			setCopied(false)
		}, 2000)
	}

	return (
		<div className="mb-8 max-w-2xl">
	{/* Link Sharing Section */}
	<div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4">
		<div className="flex items-start mb-3">
			<div>
				<h2 className="text-2xl font-bold text-therapy-blue-dark">
					Share your profile
				</h2>
				<p className="text-lg text-gray-600">
					Copy this link to share your therapist profile with potential
					clients
				</p>
			</div>
		</div>

		<div className="flex flex-col sm:flex-row gap-2">
			<div className="flex-1 relative">
				<input
					type="text"
					value={profileUrl}
					readOnly
					className="w-full p-3 border border-gray-300 rounded bg-gray-50 text-gray-700 text-base"
				/>
			{copied && (
    <div className="absolute top-1/2 -translate-y-1/2 right-2 bg-green-500 text-white px-2 py-1 rounded shadow-md text-sm whitespace-nowrap mr-2">
        <span className="flex items-center">
            <i className="fa-solid fa-check mr-1 text-xs"></i>
            Copied!
        </span>
    </div>
)}
			</div>

			<button
				onClick={handleCopyLink}
				className="px-5 py-2 bg-therapy-blue-dark text-white rounded-full font-medium flex items-center justify-center text-base"
			>
				<i className="fa-regular fa-copy mr-1"></i>
				Copy
			</button>
		</div>
	</div>
</div>

	)
}

const SectionHeader: FC<{
	title: string;
	editPath?: string;
	onEdit?: () => void;
}> = ({ title, editPath, onEdit }) => {
	const navigate = useNavigate()
	const dispatch = useDispatch<AppDispatch>()
	
	const handleEdit = () => {
		if (editPath) {
			startTransition(() => {
				dispatch(setEditFlag(true))
				navigate(`/edit/${editPath}`)
			})
		} else if (onEdit) {
			onEdit()
		}
	}
	
	return (
		<div className="flex gap-4 items-center">
			<h1 className="text-3xl font-bold text-therapy-blue-dark">
				{title}
			</h1>
			{editPath && (
				<i
					data-testid={`edit-${editPath}`}
					className="fa-solid fa-pencil text-therapy-blue-dark cursor-pointer"
					onClick={handleEdit}
				></i>
			)}
		</div>
	)
}

// Extract skeleton loader to reduce repetition
const SkeletonLoader: FC = () => (
	<div className="animate-pulse">
		<div className="h-8 w-48 bg-gray-200 rounded mb-4"></div>
		<div className="space-y-3">
			<div className="h-4 bg-gray-200 rounded w-1/4"></div>
			<div className="h-4 bg-gray-200 rounded w-1/4"></div>
			<div className="h-4 bg-gray-200 rounded w-1/4"></div>
		</div>
	</div>
)

// Create a component for section content to simplify conditional rendering
const SectionContent: FC<{
	isLoading: boolean;
	children: React.ReactNode;
}> = ({ isLoading, children }) => {
	return isLoading ? <SkeletonLoader /> : <>{children}</>
}

const normalizeLicenseVerification = (pLicense: any) => {
	if (Array.isArray(pLicense.license_verification)) {
		return pLicense.license_verification;
	}

	if (
		pLicense.license_verification &&
		Object.keys(pLicense.license_verification).length > 0
	) {
		return [pLicense.license_verification];
	}

	return [];
};

const Page: FC = () => {
	const { ref } = useInView()
	const dispatch = useDispatch<AppDispatch>()
	const authStore = useSelector((state: RootState) => state.auth)

	const [profilePageInfo, setProfilePageInfo] = useState<any[]>([])
	const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
	const [isLoading, setIsLoading] = useState(true)

	// Extract section data retrieval
	const getSectionData = (pageName: string) => {
		return profilePageInfo.find(page => page.pageName === pageName)?.payloadInfo
	}

	const license = getSectionData('license')
	const specializations = getSectionData('specialization')
	const rankSpecialties = getSectionData('rank-specialties')
	const rankSubSpecialties = getSectionData('rank-sub-specialties')
	const modalities = getSectionData('modalities')
	const practiceFocus = getSectionData('practice-focus')
	const practiceInfo = getSectionData('practice-info')
	const profile = getSectionData('profile')	

	const fetchProfilePageInfo = async () => {
		if (!authStore.user) return

		setIsLoading(true)
		const res = await dispatch(
			fetchRegInfoAction({
				pages: [
					'license',
					'specialization',
					'rank-specialties',
					'rank-sub-specialties',
					'modalities',
					'practice-focus',
					'practice-info',
					'profile',
				],
			})
		)
		setIsLoading(false)
		if(res?.status !== 200) return;		
		const updatedFormContent = res.data.reduce((acc: any, page: any) => {
			const payload = page.payloadInfo;
		
			if (page.pageName === 'license' && Array.isArray(payload.licenses)) {
				payload.licenses = payload.licenses.map((pLicense: any) => ({
					...pLicense,
					license_verification: normalizeLicenseVerification(pLicense),
				}));
			}
		
			acc[page.pageName] = payload
			return acc
		}, {});

		setProfilePageInfo(res.data)
		
		dispatch(
			setFormContent({
				...authStore.registration.formContent,
				...updatedFormContent,
			})
		)
		
	}

	const handleDeleteProfile = async () => {
        if (!authStore.user) return
        try {
            const currentDate = new Date().toISOString()
            const response = await request.delete(
                `/therapists/profile/${authStore.user.id}`,
                {
                    params: {
                        deletedAt: currentDate
                    }
                }
            )
    
            setIsDeleteModalOpen(false)
    
            if (response.status === 200) {
                dispatch(signOut())
            }
            return response.data
        } catch (error: any) {
            return error.response
        }
    }
 
 

	useEffect(() => {
		fetchProfilePageInfo()
	}, [])

	return (
		<div ref={ref}>
			{/* Header */}
			<div
				style={{ borderBottom: '1px solid #ccc' }}
				className="cursor-pointer flex-none w-full h-20 flex flex-row justify-center items-center px-14 text-white gap-4"
			>
				<h1 className="text-3xl font-bold text-therapy-blue-dark">
					Your Profile Information
				</h1>
			</div>
			
			<div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
				{/* License Information Section */}
				<div>
					<SectionHeader title="License(s)" editPath="license" />
					<SectionContent isLoading={isLoading}>
						<LicenseSection license={license} />
					</SectionContent>
				</div>

				{/* Specialization Section */}
				<div className="mt-8">
					<SectionHeader title="Specialization" editPath="specialization" />
					<SectionContent isLoading={isLoading}>
						<SpecializationsSection
							specializations={specializations}
							specialities={specialities}
						/>
					</SectionContent>
				</div>

				<div className="mt-8">
					{/* Rank Specialties Section */}
					<SectionHeader title="Rank Specialties" />
					<SectionContent isLoading={isLoading}>
						<RankSpecialtiesSection
							rankSpecialties={rankSpecialties}
							specialities={specialities}
						/>
					</SectionContent>

					{/* Rank Sub-Specialties Section */}
					<div className="mt-8">
						<SectionHeader title="Rank Sub-Specialties" />
						<SectionContent isLoading={isLoading}>
							<RankSubSpecialtiesSection
								rankSubSpecialties={rankSubSpecialties}
								specialities={specialities}
							/>
						</SectionContent>
					</div>
				</div>

				{/* Modalities Section */}
				<div className="mt-8">
					<SectionHeader title="Modalities" editPath="modalities" />
					<SectionContent isLoading={isLoading}>
						<ModalitiesSection modalities={modalities} />
					</SectionContent>
				</div>

				{/* Practice Focus Section */}
				<div className="mt-8">
					<SectionHeader title="Practice Focus" editPath="practice-focus" />
					<SectionContent isLoading={isLoading}>
						<PracticeFocusSection
							practiceFocus={practiceFocus}
							genders={genders}
							religions={religions}
							areaOfFocus={areaOfFocus}
						/>
					</SectionContent>
				</div>

				{/* Practice Information Section */}
				<div className="mt-8" data-testid="practice-info-section">
					<SectionHeader title="Practice Information" editPath="practice-info" />
					<SectionContent isLoading={isLoading}>
						<PracticeInfo practiceInfo={practiceInfo} />
					</SectionContent>
				</div>

				{/* Profile Info Section */}
				<div className="mt-8" data-testid="profile-info-section">
					<SectionHeader title="Profile Info" editPath="profile" />
					<SectionContent isLoading={isLoading}>
						<ProfileSection profile={profile} />
					</SectionContent>
				</div>
				<ProfileShareSection
					profileId={authStore.user?.id ? String(authStore.user.id) : undefined}
				/>

				{/* Delete Profile Button */}
				<button
					className={`px-4 sm:px-6 mt-6 sm:mt-12 mb-6 sm:mb-10 inline-block text-center w-full sm:w-[350px] py-3 sm:py-3.5 bg-red-700 rounded-full text-sm sm:text-base text-white font-bold`}
					onClick={() => setIsDeleteModalOpen(true)}
				>
					Delete Profile
				</button>
			</div>

			{/* Delete Confirmation Modal */}
			<Modal
				isOpen={isDeleteModalOpen}
				onClose={() => setIsDeleteModalOpen(false)}
			>
				<div className="p-6">
					<h2 className="text-2xl font-bold mb-4">Confirm Deletion</h2>
					<p className="mb-6">
					<p className = "font-bold">{DELETE_PROFILE}</p>
					{DELETE_PROFILE_CONFIRM}
					</p>
					<div className="flex justify-end gap-4">
						<button
							className="px-4 py-2 bg-gray-300 rounded-md hover:bg-gray-400"
							onClick={() => setIsDeleteModalOpen(false)}
						>
							Cancel
						</button>
						<button
							className="px-4 py-2 bg-red-700 text-white rounded-md hover:bg-red-800"
							onClick={handleDeleteProfile}
						>
							Confirm
						</button>
					</div>
				</div>
			</Modal>
		</div>
	)
}

export default Page
