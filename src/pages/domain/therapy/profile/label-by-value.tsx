export interface Speciality {
	label: string
	value: string | number
	children?: Speciality[]
}

export const getSpecialityLabelByValue = (
	value: string,
	specialities: Speciality[]
): string => {
	for (const speciality of specialities) {
		if (String(speciality.value) === value) {
			return speciality.label
		}
		if (speciality.children) {
			const match = speciality.children.find(
				(specialityValue) => String(specialityValue.value) === value
			)
			if (match) {
				return match.label
			}
		}
	}
	return value.replace(/-/g, ' ')
}
