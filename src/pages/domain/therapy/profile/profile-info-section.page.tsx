import React from 'react'

export type Education = {
	college: string
	degree_type: string
	year_graduated: string
}

export type Profile = Omit<
	{
		first_name?: string
		last_name?: string
		email?: string
		password?: string
		confirm_password?: string
		dob?: string
		user_profile?: string
		educations?: Education[]
		experience_years?: string
		identify?: { checked: string; other_identify?: string }
		race?: { checked: string; other_race?: string }
		language?: { checked_list: string[]; other_language?: string }
		profile_statement?: string
		social_media?: Record<string, string>
		video_links?: { url: string }[]
	},
	| 'first_name'
	| 'last_name'
	| 'email'
	| 'password'
	| 'confirm_password'
	| 'dob'
	| 'user_profile'
>

export const profileLabels: Record<keyof Profile, string> = {
	educations: 'Education Experience',
	experience_years: 'Experience Years',
	race: 'What Race Are You?',
	language: 'What languages are you fluent in / use in your therapy practice?',
	profile_statement: 'Profile Statement',
	social_media: 'Social Media Accounts',
	video_links: 'Video Links',
	identify: 'How do you identify?',
}

const hiddenFields = [
	'first_name',
	'last_name',
	'email',
	'password',
	'confirm_password',
	'dob',
	'user_profile',
]

export const getFilteredProfile = (profile: any) => {
	return profile
		? Object.fromEntries(
				Object.entries(profile).filter(([key]) => !hiddenFields.includes(key))
			)
		: {}
}

// Helper render functions
const renderEducations = (value: Education[]) => (
	<div className="space-y-4">
		{value.map((edu, index) => (
			<div key={index} className="text-gray-600 pb-2">
				{value.length > 1 && (
					<h2 className="text-lg font-semibold text-gray-700">
						Education Experience #{index + 1}
					</h2>
				)}
				<p><strong>College/University:</strong> {edu.college}</p>
				<p><strong>Degree Type:</strong> {edu.degree_type}</p>
				<p><strong>Year Graduated:</strong> {edu.year_graduated}</p>
			</div>
		))}
	</div>
)

const renderExperience = (value: string) => (
	<p className="mt-1 text-gray-600">{value ? `${value} Years` : '-'}</p>
)

const renderIdentifyOrRace = (value: any, key: 'identify' | 'race') => {
	const label = value?.checked === 'other' ? value[`other_${key}`] || 'Other' : value?.checked
	return <p className="mt-1 text-gray-600">{label}</p>
}

const renderLanguage = (value: any) => (
	value?.checked_list?.length > 0 ? (
		<ul className="ml-6 list-disc">
			{value.checked_list.map((lang: string, index: number) => (
				<li key={index} className="text-gray-600">
					{lang === 'other' ? value.other_language || 'Other' : lang}
				</li>
			))}
		</ul>
	) : null
)

const renderStatement = (value: string) => (
	<p className="mt-1 text-gray-600">{value || '-'}</p>
)

const renderSocialMedia = (value: Record<string, string>) => (
	Object.keys(value).length > 0 ? (
		<ul className="ml-6 list-disc">
			{Object.entries(value).map(([platform, link]) => (
				<li key={platform}>
					<a
						href={String(link)}
						target="_blank"
						rel="noopener noreferrer"
						className="text-gray-600 underline"
					>
						{platform.replace(/_/g, ' ')}
					</a>
				</li>
			))}
		</ul>
	) : null
)

const renderVideoLinks = (value: { url: string }[]) => (
	value.some((v) => v.url) ? (
		<ul className="ml-6 list-disc">
			{value.map((video, index) =>
				video.url ? (
					<li key={index}>
						<a
							href={video.url}
							target="_blank"
							rel="noopener noreferrer"
							className="text-gray-600 underline"
						>
							Video {index + 1}
						</a>
					</li>
				) : null
			)}
		</ul>
	) : null
)

const renderValue = (key: string, value: any): React.ReactNode => {
	switch (key) {
		case 'educations':
			return Array.isArray(value) ? renderEducations(value) : null
		case 'experience_years':
			return renderExperience(value)
		case 'identify':
		case 'race':
			return renderIdentifyOrRace(value, key)
		case 'language':
			return renderLanguage(value)
		case 'profile_statement':
			return renderStatement(value)
		case 'social_media':
			return renderSocialMedia(value)
		case 'video_links':
			return renderVideoLinks(value)
		default:
			return typeof value === 'string' && value !== 'No data available'
				? value
				: ' -'
	}
}

export const ProfileSection = ({ profile }: { profile: any }) => {
	const filteredProfile = getFilteredProfile(profile)

	if (Object.keys(filteredProfile).length === 0) {
		return <p className="text-gray-500 mt-2">No profile information available</p>
	}

	return (
		<ul className="mt-4 space-y-4 mb-8">
			<div className="mt-4">
				<img
					src={profile.user_profile || `https://ui-avatars.com/api?name=${profile.first_name || ''}+${profile.last_name || ''}`}
					alt="Profile"
					className="w-32 h-32 rounded-full object-cover"
				/>
			</div>
			{Object.entries(filteredProfile).map(([key, value]) => (
				<li key={key} className="capitalize">
					<span className="font-semibold text-lg text-gray-700 capitalize">
						{profileLabels[key as keyof Profile] || key.replace(/_/g, ' ')}
					</span>
					{renderValue(key, value)}
				</li>
			))}
		</ul>
	)
}
