import { format } from 'date-fns'
import dayjs from 'dayjs'

interface SubscriptionSectionProps {
	subscription?: {
		subscriptionType: string
		isActive: boolean
		subscribedAt: string
		expiredAt: string | null
		subscriptionPlan: {
			monthlyPrice: string
			annualPrice: string
		}
		stripeInfo: {
			status: string
			billedPrice: number
			stripeCurrentPeriodStart: string
			stripeCurrentPeriodEnd: string
			stripeCancelAtPeriodEnd: string
			stripeSubscriptionScheduleId: string
		}
	}
}

const SubscriptionSection = ({ subscription }: SubscriptionSectionProps) => {
	if (!subscription) {
		return (
			<section className="mt-4 space-y-4 mb-8">
				<p className="text-gray-500">No active subscription.</p>
			</section>
		)
	}

	const { subscriptionType, stripeInfo } = subscription
	const isActive = subscription.isActive && stripeInfo?.status === 'active'

	const planLabel = subscriptionType === 'monthly' ? 'Monthly' : 'Annually'

	const billedPrice = stripeInfo?.billedPrice && stripeInfo?.billedPrice / 100

	const validUntil =  dayjs(stripeInfo?.stripeCurrentPeriodEnd).format("MM-DD-YYYY")

	const renewalDate = stripeInfo.stripeCancelAtPeriodEnd && !stripeInfo.stripeSubscriptionScheduleId ? (
		<>
			Subscription Cancelled - No Renewal (
			<strong>Valid Until: {validUntil}</strong>)
		</>
		) : (
			dayjs(stripeInfo?.stripeCurrentPeriodEnd).format("MM-DD-YYYY")
		);

	if (!isActive) {
		return (
			<section className="mt-4 space-y-4 mb-8">
				<p className="text-gray-500">No active subscription.</p>
			</section>
		)
	}

	return (
		<section className="mt-4 space-y-4 mb-8">
			<div className="space-y-2 text-base">
				<div className="text-gray-600">
					<span className="font-medium">
						<strong>Plan:</strong>
					</span>{' '}
					{planLabel}
				</div>
				<div className="text-gray-600">
					<span className="font-medium">
						<strong>Status:</strong>
					</span>{' '}
					<span
						className={`${
							isActive ? 'text-green-600' : 'text-red-600'
						} capitalize`}
					>
						{stripeInfo.status}
					</span>
				</div>
				<div className="text-gray-600">
					<span className="font-medium">
						<strong>Start Date:</strong>
					</span>{' '}
					{format(new Date(stripeInfo.stripeCurrentPeriodStart), 'MM-dd-yyyy')}
				</div>
				<div className="text-gray-600">
					<span className="font-medium">
						<strong>Renewal Date:</strong>
					</span>{' '}
					{renewalDate}
				</div>
				<div className="text-gray-600">
					<span className="font-medium">
						<strong>Amount Paid:</strong>
					</span>{' '}
					${billedPrice.toFixed(2)}
				</div>
			</div>
		</section>
	)
}

export default SubscriptionSection
