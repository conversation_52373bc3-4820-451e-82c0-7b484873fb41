import React, { useState } from 'react';

type BusinessAddress = {
  street?: string;
  city?: string;
  country?: string;
  full_address?: string;
  description?: string;
  zipCode?: string;
  lat?: number;
  lng?: number;
  place_id?: string;
  zone?: string;
};

type PatientInfoSheet = {
  location?: string;
  upload_name?: string;
};

type PracticeInfo = {
  practice_name?: string;
  business_phone?: string;
  business_email?: string;
  business_address?: BusinessAddress;
  location_description?: string;
  parking_information?: string;
  practice_photos?: string[];
  patient_info_sheet?: PatientInfoSheet | PatientInfoSheet[];
};

const PracticeInfoKeyLabels: Record<keyof PracticeInfo, string> = {
  practice_name: 'Practice Name',
  business_phone: 'Business Phone',
  business_email: 'Business Email',
  business_address: 'Business Address',
  location_description: 'Description of Location',
  parking_information: 'Parking Information',
  practice_photos: 'Photos of Practice',
  patient_info_sheet: 'Patient Info Sheets',
};

interface PracticeInfoProps {
  practiceInfo: PracticeInfo;
}

const isImageFile = (url: string): boolean => /\.(jpg|jpeg|png)$/i.test(url);

const renderImage = (src: string, alt: string, onClick?: () => void) => (
  <div
    className="h-24 w-24 rounded-md border-2 bg-gray-200 flex items-center justify-center user_registration_profile cursor-pointer"
    onClick={onClick}
  >
    <img src={src} alt={alt} className="h-full w-full object-cover rounded-md" />
  </div>
);

const getUploadDisplayName = (uploadName?: string): string => {
  const name = uploadName ?? '';
  if (!name) return 'No name provided';
  return name.length > 15 ? `${name.substring(0, 12)}...` : name;
};

const renderPatientFile = (file: PatientInfoSheet, index: number, onImageClick: (url: string) => void) => {
  if (!file.location) return 'No document uploaded';
  return isImageFile(file.location)
    ? renderImage(file.location, `Patient Info Sheet ${index + 1}`, () => onImageClick(file.location!))
    : (
      <div
        className="flex flex-col items-center justify-center text-gray-500 cursor-pointer"
        onClick={() => window.open(file.location, '_blank')}
      >
        <i className="fa-solid fa-file text-3xl"></i>
        <span className="text-xs text-center mt-1 px-1 break-words">
        {getUploadDisplayName(file.upload_name)}
        </span>
      </div>
    );
};

const renderPatientInfo = (value: PatientInfoSheet | PatientInfoSheet[], onImageClick: (url: string) => void) => {
  if (Array.isArray(value)) {
    return value.length > 0 ? (
      <div className="flex mt-3">
        {value.map((file, index) => (
          <div key={index} className="relative flex flex-row mt-1 gap-4 mr-4 cursor-pointer">
            {renderPatientFile(file, index, onImageClick)}
          </div>
        ))}
      </div>
    ) : 'No document uploaded';
  }

  if (value?.location) {
    return (
      <a
        href={value.location}
        target="_blank"
        rel="noopener noreferrer"
        className="text-gray-600 underline"
      >
        {value.upload_name || 'View Document'}
      </a>
    );
  }

  return 'No document uploaded';
};

const getDisplayValue = (key: string, value: any, onImageClick: (url: string) => void, openModal: (i: number) => void) => {
  switch (key) {
    case 'practice_photos':
      return Array.isArray(value) && value.length > 0 ? (
        <div className="flex mt-3">
          {value.map((pic: string, i: number) => (
            <div
              key={i}
              className="relative flex flex-row mt-1 gap-4 mr-4 cursor-pointer"
              onClick={() => openModal(i)}
            >
              {renderImage(pic, `Practice Photo ${i + 1}`)}
            </div>
          ))}
        </div>
      ) : 'No data available';
    case 'business_address':
      return value?.full_address || 'No address provided';
    case 'patient_info_sheet':
      return renderPatientInfo(value, onImageClick);
    default:
      return typeof value === 'string' ? value : 'No data available';
  }
};


const PracticeInfoSection: React.FC<PracticeInfoProps> = ({ practiceInfo }) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState<number>(0);

  const openModal = (index: number) => {
    setSelectedImage(practiceInfo.practice_photos?.[index] || null);
    setCurrentIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const changeImage = (direction: 'next' | 'prev') => {
    if (!practiceInfo.practice_photos?.length) return;
    const length = practiceInfo.practice_photos.length;
    const newIndex = direction === 'next'
      ? (currentIndex + 1) % length
      : (currentIndex - 1 + length) % length;
    setCurrentIndex(newIndex);
    setSelectedImage(practiceInfo.practice_photos[newIndex]);
  };

  return (
    <div>
      {practiceInfo && Object.keys(practiceInfo).length > 0 ? (
        <ul className="mt-4 space-y-4">
          {Object.entries(practiceInfo).map(([key, value]) => (
            <li key={key} className={key === 'business_email' ? '' : 'capitalize'}>
              <span className="font-semibold text-lg text-gray-700 capitalize">
                {PracticeInfoKeyLabels[key as keyof PracticeInfo] || key.replace(/_/g, ' ')}
              </span>
              <p className="text-gray-600">{getDisplayValue(key, value, setSelectedImage, openModal)}</p>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500 mt-2">No practice information available</p>
      )}

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-80 flex items-center justify-center z-50">
          <button onClick={closeModal} className="absolute top-4 right-4 text-white text-3xl cursor-pointer">✖</button>
          <button onClick={() => changeImage('prev')} className="absolute left-4 text-white text-3xl cursor-pointer">◀</button>
          <img src={selectedImage} alt="Practice Photo" className="max-w-3xl max-h-[80vh] rounded-lg shadow-lg" />
          <button onClick={() => changeImage('next')} className="absolute right-4 text-white text-3xl cursor-pointer">▶</button>
        </div>
      )}
    </div>
  );
};

export default PracticeInfoSection;
