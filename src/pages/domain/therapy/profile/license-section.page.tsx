import React from 'react'

interface CompactLicense {
	compact_privilege?: { label: string; value: string }
	compact_name?: string
	compact_identification_number?: string
	compact_states?: { label: string; value: string; id: string }[]
}

interface License {
	license_type?: { label: string }
	license_state?: { label: string }
	issuing_board?: string
	license_number?: string
	issue_date?: string
	expiry_date?: string
	clinical_supervisor_email?: string
	clinical_supervisor_name?: string
	clinical_supervisor_phone?: string
	license_verification?: LicenseVerificationItem[]
	license_status?: { label: string }
	probation_or_disciplinary_action?: { label: string }
	disciplinary_action_description?: string
	npi_number?: string
	compact_licenses?: CompactLicense[]
}

interface Props {
	license: {
		licenses?: License[]
		npi_number?: string
		compact_licenses?: CompactLicense[]
	}
}

interface LicenseVerificationItem {
  location?: string;
  upload_name?: string;
}

const LicenseInfo: React.FC<{ label: string, value?: string }> = ({ label, value }) => (
	<div>
		<h2 className="text-lg font-semibold text-gray-700 capitalize">{label}</h2>
		<p className="text-gray-600">{value || '-'}</p>
	</div>
)

const LicenseVerification: React.FC<{ items: LicenseVerificationItem[] }> = ({ items }) => (
  <div>
    <h2 className="text-lg font-semibold text-gray-700 capitalize mb-2">License Verification Document(s)</h2>
    {items.length > 0 ? (
      <ul className="space-y-1">
        {items.map((item, index) => (
          <li key={index}>
            {item.location ? (
              <a
                href={item.location}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline"
              >
                {item.upload_name || `Document ${index + 1}`}
              </a>
            ) : (
              <span className="text-gray-600">No document link available</span>
            )}
          </li>
        ))}
      </ul>
    ) : (
      <p className="text-gray-600">No verification documents uploaded.</p>
    )}
  </div>
);

const SupervisorInfo: React.FC<{ supervisor: License }> = ({ supervisor }) => (
	<>
		<LicenseInfo label="Full Name of Clinical Supervisor" value={supervisor.clinical_supervisor_name} />
		<LicenseInfo label="Clinical Supervisor Email" value={supervisor.clinical_supervisor_email} />
		<LicenseInfo label="Clinical Supervisor Phone" value={supervisor.clinical_supervisor_phone} />
	</>
)

const DisciplinaryAction: React.FC<{ action: License }> = ({ action }) => (
	action.probation_or_disciplinary_action?.label === 'Yes' ? (
		<LicenseInfo label="Disciplinary Action Description" value={action.disciplinary_action_description} />
	) : null
)

const CompactLicenseInfo: React.FC<{ compactLicense: CompactLicense, index: number }> = ({ compactLicense, index }) => (
	<div className="mt-2 border-b pb-2">
		<h2 className="text-xl font-semibold text-gray-700 capitalize">Compact License #{index + 1}</h2>
		<LicenseInfo label="Name" value={compactLicense.compact_name} />
		<LicenseInfo label="ID Number" value={compactLicense.compact_identification_number} />
		<div>
			<h2 className="text-lg font-semibold text-gray-700 capitalize">States</h2>
			{compactLicense.compact_states?.length ? (
				<ul className="list-disc pl-5 text-gray-600">
					{compactLicense.compact_states.map((state, idx) => (
						<li key={idx}>{state?.label ?? '-'}</li>
					))}
				</ul>
			) : (
				<p className="text-gray-600">-</p>
			)}
		</div>
	</div>
)

const LicenseSection: React.FC<Props> = ({ license }) => {
	const renderLicenseInfo = (licenseItem: License, index: number) => {
		const fields = [
			{ label: 'License Type', value: licenseItem.license_type?.label },
			{ label: 'State', value: licenseItem.license_state?.label },
			{ label: 'License Number', value: licenseItem.license_number },
			{ label: 'Issuing Board', value: licenseItem?.issuing_board },
			{ label: 'Issue Date', value: licenseItem.issue_date },
			{ label: 'Expiration Date', value: licenseItem.expiry_date },
			{ label: 'License Status', value: licenseItem.license_status?.label },
			{ label: 'Probation/Disciplinary Action', value: licenseItem.probation_or_disciplinary_action?.label },
		]

		return (
			<li key={index} className="capitalize pb-2">
				<h2 className="text-xl font-bold text-gray-800">License #{index + 1}</h2>
				<div className="grid grid-cols-1 gap-2">
					{fields.map((item, idx) => (
						<LicenseInfo key={idx} label={item.label} value={item.value} />
					))}

					<LicenseVerification items={licenseItem.license_verification || []} />

					{licenseItem.license_status?.label === 'Provisional' && <SupervisorInfo supervisor={licenseItem} />}
					<DisciplinaryAction action={licenseItem} />
				</div>
			</li>
		)
	}

	return (
		<div className="mt-4">
			{license?.licenses?.length ? (
				<>
					<ul className="mt-4 space-y-4">
						{license.licenses.map(renderLicenseInfo)}
					</ul>

					{license.npi_number && (
						<div className="border-b mt-4">
							<LicenseInfo label="NPI Number" value={license.npi_number} />
						</div>
					)}

					{license.compact_licenses?.some(item => item.compact_privilege?.value?.toLowerCase() === 'yes') && (
						<div className="mt-4">
							{license.compact_licenses
								.filter(item => item.compact_privilege?.value?.toLowerCase() === 'yes')
								.map((compactLicense, index) => (
									<CompactLicenseInfo key={index} compactLicense={compactLicense} index={index} />
								))}
						</div>
					)}
				</>
			) : (
				<p className="mt-4 text-gray-600">No licenses available.</p>
			)}
		</div>
	)
}

export default LicenseSection
