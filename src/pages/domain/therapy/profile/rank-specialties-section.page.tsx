import { specialities } from '@/configs/registration.configs'
import React from 'react'
import { getSpecialityLabelByValue } from './label-by-value'

interface Speciality {
	label: string
	value: string | number
	children?: Speciality[]
}

interface RankSpecialtiesSectionProps {
	rankSpecialties: Record<string, string>
	specialities: Speciality[]
}

const RankSpecialtiesSection: React.FC<RankSpecialtiesSectionProps> = ({
	rankSpecialties	
}) => {
	

	return (
		<div className="mt-4">
			{rankSpecialties && Object.keys(rankSpecialties).length > 0 ? (
				<ul className="list-decimal pl-5 text-gray-600">
					{Object.entries(rankSpecialties).map(([rank, specialty]) => (
						<li key={rank} className="capitalize">
							{getSpecialityLabelByValue(String(specialty), specialities)}
						</li>
					))}
				</ul>
			) : (
				<p className="text-gray-500 mt-2">No rank specialties available</p>
			)}
		</div>
	)
}

export default RankSpecialtiesSection
