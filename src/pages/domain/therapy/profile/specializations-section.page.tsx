import React from 'react'
import { getSpecialityLabelByValue } from './label-by-value'

interface Speciality {
	label: string
	value: string | number
	children?: Speciality[]
}

interface SpecializationsSectionProps {
	specializations?: Record<string, string[]>
	specialities: Speciality[]
}

const SpecializationsSection: React.FC<SpecializationsSectionProps> = ({
	specializations,
	specialities,
}) => {
	
	if (!specializations || Object.keys(specializations).length === 0) {
		return <p className="text-gray-500 mt-2">No specializations available</p>
	}

	return (
		<div className="mt-4">
			{Object.entries(specializations).map(([category, values]) => (
				<div key={category} className="mt-4">
					<h2 className="text-lg font-semibold text-gray-700 capitalize">
						{getSpecialityLabelByValue(String(category), specialities)}
					</h2>
					<ul className="list-disc pl-5 text-gray-600">
						{(values).map((item, index) => (
							<li key={index}>
								{getSpecialityLabelByValue(String(item),specialities)}
							</li>
						))}
					</ul>
				</div>
			))}
		</div>
	)
}

export default SpecializationsSection
