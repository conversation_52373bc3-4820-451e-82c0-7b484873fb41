import React from 'react'

interface ModalitiesSectionProps {
	modalities: Record<string, string[]> | null
}

const ModalitiesSection: React.FC<ModalitiesSectionProps> = ({ modalities }) => {
	const trained: string[] = []
	const certified: string[] = []

	if (modalities && Object.keys(modalities).length > 0) {
		Object.values(modalities).forEach((subKeys) => {
			subKeys.forEach((subKey) => {
				const formattedKey = subKey
					.replace(/_/g, ' ')
					.replace(/\b(trained|certified)\b/i, '')
					.trim()

				if (subKey.toLowerCase().includes('_trained')) {
					trained.push(formattedKey)
				} else if (subKey.toLowerCase().includes('_certified')) {
					certified.push(formattedKey)
				}
			})
		})
	}

	return (
		<>
		
		{trained.length > 0 || certified.length > 0 ? (
				
		<ul className="mt-4 space-y-4">
				{trained.length > 0 && (
					<li>
						<strong className="font-semibold text-lg text-gray-700 capitalize">
							Trained Modalities
						</strong>
						<ul className="ml-6 list-disc">
							{trained.map((item, index) => (
								<li key={index} className="text-gray-600">
									{item}
								</li>
							))}
						</ul>
					</li>
				)}
				{certified.length > 0 && (
					<li>
						<strong className="font-semibold text-lg text-gray-700 capitalize">
							Certified Modalities
						</strong>
						<ul className="ml-6 list-disc">
							{certified.map((item, certifiedIndex) => (
								<li key={certifiedIndex} className="text-gray-600">
									{item}
								</li>
							))}
						</ul>
					</li>
				)}
		</ul>
		) : (
			<p className="mt-2 text-gray-600">No modalities available.</p>
		)}
		</>
	)
}

export default ModalitiesSection
