import React from 'react'
import { getSpecialityLabelByValue , Speciality} from './label-by-value'

interface RankSubSpecialtiesSectionProps {
	rankSubSpecialties: Record<string, string> | null
	specialities: Speciality[]
}

const RankSubSpecialtiesSection: React.FC<RankSubSpecialtiesSectionProps> = ({
	rankSubSpecialties,
	specialities,
}) => {
	if (!rankSubSpecialties || Object.keys(rankSubSpecialties).length === 0) {
		return <p className="text-gray-500 mt-2">No rank sub-specialties available</p>
	}

	return (
		<ul className="list-decimal pl-5 text-gray-600">
			{Object.entries(rankSubSpecialties).map(([rank, subSpecialty]) => (
				<li key={rank} className="capitalize">
					{getSpecialityLabelByValue(String(subSpecialty), specialities)}
				</li>
			))}
		</ul>
	)
}

export default RankSubSpecialtiesSection
