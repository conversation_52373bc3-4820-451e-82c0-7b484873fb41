import { type FC, useEffect, useState, useRef, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { request } from "@/utils/request.utils";
import notification from '@/utils/notification.util'
import ActionButton from '@/components/buttons/ActionButton';
import NormalDialog from '@/components/dialogs/NormalDialog';
import { useOverlayTriggerState } from 'react-stately';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '@/store/index';
import { confirmPaymentAction } from '@/store/slicers/therapist.slicer';
const { VITE_USER_PROFILE } = import.meta.env


interface Appointment {
	id: number
	appointmentDate: string // ISO 8601 format (e.g., "2025-01-30T16:00:00.000Z")
	description: string
	initial_appointment?: string
	cancelledAppointment?: string
	isBilled: boolean
	patient: {
		id: number
		firstname: string
		lastname: string
		email: string
		phone: string | null
		dob: string
		gender: string
		address: {
			zip?: string
			city: string
			state: string
			street: string
			country: string
			zipCode?: string
			lat?: number
			lng?: number
			place_id?: string
			description?: string
			full_address?: string
		}
		bio: string | null
		userProfile: string | null
		cancelled: boolean
	}
	formattedDateTime: string
	past: boolean
}

interface Payment {
	id: number
	createDate: string // ISO 8601 format (e.g., "2025-01-30T16:00:00.000Z")
	patient: {
		id: number
		firstname: string
		lastname: string
		email: string
		phone: string | null
		dob: string | null
		gender: string | null
		userProfile: string | null
	}
	paymentCreateDate: string
	cost: number | null
	currency: string | null
}

interface FormattedAppointment {
	id: string
	name: string
	avatar: string
	dateTime: string
	type: string
	isPast: boolean
	cancelled: boolean
	isBilled: boolean
}

const AppointmentSkeletonLoader: FC = () => {
  return (
    <div className="overflow-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-12 py-3 px-4 text-sm font-bold text-black border-b-2 border-gray-400">
        <div className="sm:col-span-4 mb-2 sm:mb-0">Name</div>
        <div className="sm:col-span-4 mb-2 sm:mb-0">Date & Time</div>
        <div className="sm:col-span-4 mb-2 sm:mb-0">Appointment Type</div>
      </div>
      <div className="appointment-section overflow-y-auto max-h-[400px]">
        {[...Array(5)].map((_, index) => (
          <div
            key={index}
            className="grid grid-cols-1 sm:grid-cols-12 border-b-2 border-gray-400 gap-2 text-sm py-3 px-4"
          >
            <div className="sm:col-span-4 flex items-center mb-2 sm:mb-0">
              <div className="w-8 h-8 rounded-full mr-3 bg-gray-200 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
            </div>
            <div className="sm:col-span-4 flex items-center mb-2 sm:mb-0">
              <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
            </div>
            <div className="sm:col-span-4 flex items-center sm:justify-start mb-2 sm:mb-0">
              <div className="h-4 bg-gray-200 rounded w-28 animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const PaymentSkeletonLoader: FC = () => {
  return (
    <div className="overflow-hidden">
      <div className="grid grid-cols-1 sm:grid-cols-12 py-3 px-4 text-sm font-bold text-black border-b-2 border-gray-400">
        <div className="sm:col-span-4 mb-2 sm:mb-0">Payment Made By</div>
        <div className="sm:col-span-4 mb-2 sm:mb-0">Amount</div>
        <div className="sm:col-span-4 mb-2 sm:mb-0">Date & Time</div>
      </div>
      <div className="payment-section overflow-y-auto max-h-[400px]">
        {[...Array(5)].map((_, index) => (
          <div key={index} className="grid grid-cols-1 sm:grid-cols-12 border-b-2 border-gray-400 text-sm py-3 px-4">
            <div className="sm:col-span-4 flex items-center min-w-0 mb-2 sm:mb-0">
              <div className="w-8 h-8 rounded-full mr-3 flex-shrink-0 bg-gray-200 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
            </div>
            <div className="sm:col-span-4 mb-3 sm:mb-0 w-[60px] text-right">
              <div className="h-4 bg-gray-200 rounded w-16 ml-auto animate-pulse"></div>
            </div>
            <div className="sm:col-span-4 flex items-center text-left min-w-0 mb-2 sm:mb-0">
              <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const Page: FC = () => {
	const dispatch = useDispatch<AppDispatch>();
	const { ref } = useInView()
	const [appointments, setAppointments] = useState<FormattedAppointment[]>([])

	const [payments, setPayments] = useState<
		{
			id: string
			name: string
			avatar: string
			dateTime: string
			cost: string
			currency: string
		}[]
	>([])

	const [visibleAppointments, setVisibleAppointments] = useState(5) // Number of appointments to display
	const lastAppointmentRef = useRef<HTMLDivElement>(null)
	const [shouldScroll, setShouldScroll] = useState(false)
	const containerRef = useRef<HTMLDivElement>(null)

	const [visiblePayments, setVisiblePayments] = useState(5) // Number of appointments to display
	const lastPaymentRef = useRef<HTMLDivElement>(null)
	const [shouldScrollPayment, setShouldScrollPayment] = useState(false)
	const containerPaymentRef = useRef<HTMLDivElement>(null)
	const [sortOrder, setSortOrder] = useState<'asc' | 'desc' | ''>('')
	
	// Loading states
	const [loadingAppointments, setLoadingAppointments] = useState(true)
	const [loadingPayments, setLoadingPayments] = useState(true)
	const [isConfirmingPayment, setIsConfirmingPayment] = useState(false);

	const urlSearchParams = new URLSearchParams(window.location.search);
	
	const [selectedAppointment, setSelectedAppointment] = useState<FormattedAppointment | null>(null);
	const confirmPaymentState = useOverlayTriggerState({});
	
	const sortPayments = () => {
		const newOrder = sortOrder === 'asc' ? 'desc' : 'asc'
		setSortOrder(newOrder)
		setPayments(
			[...payments].sort((a, b) => {
				return newOrder === 'asc'
					? Number.parseFloat(a.cost) - Number.parseFloat(b.cost)
					: Number.parseFloat(b.cost) - Number.parseFloat(a.cost)
			})
		)
	}

	// Function to format time (e.g., "16:00:00.000Z" -> "4:00 PM")
	const formatTime = (dateTime: string) => {
		const date = new Date(dateTime)
		const hours = date.getHours()
		const minutes = date.getMinutes()
		const suffix = hours >= 12 ? 'PM' : 'AM'
		const formattedHour = hours % 12 || 12 // Convert to 12-hour format
		return `${formattedHour}:${minutes.toString().padStart(2, '0')} ${suffix}`
	}

	// Function to format date with leading zeros (e.g., "1/9/2025" -> "01/09/2025")
	const formatDateWithLeadingZeros = (dateString: string) => {
		const date = new Date(dateString)
		const month = (date.getMonth() + 1).toString().padStart(2, '0')
		const day = date.getDate().toString().padStart(2, '0')
		const year = date.getFullYear()
		return `${month}/${day}/${year}`
	}

	const fetchAppointments = async () => {
		setLoadingAppointments(true)
		try {
			const response = await request.get<Appointment[]>(
				`patients/appointments`
			)

			const formattedAppointments: FormattedAppointment[] = response.data.map(
				(appointment: Appointment): FormattedAppointment => {
					const appointmentDateTime = new Date(
						appointment.appointmentDate
					).toISOString() // Convert to ISO format

					const isPast = appointment.past
					const cancelledAt = appointment.cancelledAppointment

					const formattedDate = formatDateWithLeadingZeros(appointmentDateTime)
					const dateTime = `${formattedDate} - ${formatTime(appointmentDateTime)}`

					// Check if the appointment is the first one for the patient
					const appointmentType =
						appointment.initial_appointment === 'first'
							? 'First Appointment'
							: 'Regular Appointment'

					return {
						id: appointment.id.toString(),
						name: `${appointment.patient.firstname} ${appointment.patient.lastname}`,
						avatar: appointment.patient.userProfile
							? `${VITE_USER_PROFILE}${appointment.patient.userProfile}?size=10`
							: `https://ui-avatars.com/api?name=${appointment.patient.firstname || ''}+${appointment.patient.lastname || ''}`,
						dateTime,
						type: appointmentType,
						isPast,
						cancelled: !!cancelledAt,
						isBilled: appointment.isBilled,
					}
				}
			)
			// Set appointments directly (backend handles sorting)
			setAppointments(formattedAppointments)

			if (urlSearchParams.get('appointmentId')) {
				const appointmentId = urlSearchParams.get('appointmentId');
				const toConfirmAppointment = formattedAppointments.find((appointment) => appointment.id === appointmentId);
				if (toConfirmAppointment) {
					setSelectedAppointment(toConfirmAppointment);
					confirmPaymentState.open();
				}
				window.history.replaceState({}, document.title, window.location.pathname);
			}
		} catch (error: any) {
			notification.error(`Failed to fetch appointments: ${error.message}`)
		} finally {
			setLoadingAppointments(false)
		}
	}

	const fetchPaymentsData = async () => {
		setLoadingPayments(true)
		try {
			const response = await request.get<Payment[]>(
				`patients/payments-appointments`
			)

			interface FormattedPayment {
				id: string
				name: string
				avatar: string
				dateTime: string
				cost: string
				currency: string
			}

			const formattedPayment: FormattedPayment[] = response.data.map(
				(payment: Payment): FormattedPayment => {
					const paymentDateTime = new Date(
						payment.paymentCreateDate
					).toISOString()

					const formattedDate = formatDateWithLeadingZeros(paymentDateTime)
					const dateTime = `${formattedDate} - ${formatTime(paymentDateTime)}`

					const amountPaid: string = ((payment.cost || 0) / 100).toFixed(2)
					return {
						id: payment.id.toString(),
						name: `${payment.patient.firstname} ${payment.patient.lastname}`,
						avatar: payment.patient.userProfile
							? `${VITE_USER_PROFILE}${payment.patient.userProfile}?size=10`
							: `https://ui-avatars.com/api?name=${payment.patient.firstname || ''}+${payment.patient.lastname || ''}` ,
						dateTime,
						cost: amountPaid.toString(),
						currency: payment.currency || '',
					}
				}
			)
			// Set appointments directly (backend handles sorting)
			setPayments(formattedPayment)
		} catch (error: any) {
			notification.error(`Failed to fetch payment: ${error.message}`)
		} finally {
			setLoadingPayments(false)
		}
	}

	useEffect(() => {
		fetchAppointments();
		fetchPaymentsData();
	}, [])

	// Function to handle "Show More" click
	const handleShowMore = useCallback(() => {
		setVisibleAppointments((prev) => prev + 5)
		setShouldScroll(true)
	}, [])

	// Function to handle "Show Less" click
	const handleShowLess = useCallback(() => {
		setVisibleAppointments(5)
		setShouldScroll(true)
	}, [])

	// Function to handle "Show More" click
	const handleShowMorePayment = useCallback(() => {
		setVisiblePayments((prev) => prev + 5)
		setShouldScrollPayment(true)
	}, [])

	// Function to handle "Show Less" click
	const handleShowLessPayment = useCallback(() => {
		setVisiblePayments(5)
		setShouldScrollPayment(true)
	}, [])

	useEffect(() => {
		if (shouldScroll && containerRef.current) {
			const container = containerRef.current
			const scrollToBottom = () => {
				container.scrollTop = container.scrollHeight - container.clientHeight
			}
			const scrollToTop = () => {
				container.scrollTop = 0
			}

			if (visibleAppointments > 5) {
				scrollToBottom()
			} else {
				scrollToTop()
			}

			setShouldScroll(false)
		}
	}, [visibleAppointments, shouldScroll])

	useEffect(() => {
		if (shouldScrollPayment && containerPaymentRef.current) {
			const container = containerPaymentRef.current
			const scrollToBottom = () => {
				container.scrollTop = container.scrollHeight - container.clientHeight
			}
			const scrollToTop = () => {
				container.scrollTop = 0
			}

			if (visiblePayments > 5) {
				scrollToBottom()
			} else {
				scrollToTop()
			}

			setShouldScrollPayment(false)
		}
	}, [visiblePayments, shouldScrollPayment])

	// Function to handle "Export to .xls" click
	const handleExport = async (apiPath: string, filename: string) => {
		try {
			const nowUTC = new Date().toISOString() // Get current system time in UTC
			const response = await request.get(
				`${apiPath}?date=${encodeURIComponent(nowUTC)}`,
				{
					responseType: 'blob',
				}
			)

			const url = window.URL.createObjectURL(new Blob([response.data]))
			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', filename)
			document.body.appendChild(link)
			link.click()

			document.body.removeChild(link)
			window.URL.revokeObjectURL(url)

			// Show success toast
			notification.success('Report downloaded successfully!')
		} catch (error: any) {
			notification.error('Failed to download report. Please try again.')
		}
	}

	return (
		<div ref={ref} className="responsive-container">
			<div
				style={{ borderBottom: '1px solid #ccc' }}
				className="cursor-pointer flex-none w-full h-20 flex flex-row justify-center items-center px-4 sm:px-14 text-white gap-4"
			>
				<span className="text-3xl font-bold text-therapy-blue-dark">
					<h3>Reporting</h3>
				</span>
			</div>
			<div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
				{/* Appointments Section */}
				<div>
					<div className="h-full flex flex-col flex-grow">
						<div className="flex-none flex flex-row justify-between items-center mb-4 -mt-5">
							<div className="text-2xl sm:text-3xl font-medium text-therapy-blue-dark">
								Appointments
							</div>
							<div>
								<button
									onClick={() =>
										handleExport(
											'/patients/appointments/export?',
											'appointments.xls'
										)
									}
									style={
										loadingAppointments || appointments.length === 0
											? { backgroundColor: 'transparent' }
											: {}
									}
									className={`inline-flex items-center px-4 py-2 mt-6 border font-bold rounded-md transition-all duration-200
    ${
			loadingAppointments || appointments.length === 0
				? 'border-gray-300 text-gray-700 shadow-sm cursor-not-allowed'
				: 'border-therapy-blue-dark text-therapy-blue-dark hover:bg-blue-50 hover:shadow-md'
		}`}
									disabled={loadingAppointments || appointments.length === 0}
								>
									Export to .xls
								</button>
							</div>
						</div>

            <div>
              {loadingAppointments ? (
                <AppointmentSkeletonLoader />
              ) : appointments.length > 0 ? (
                <div className="overflow-hidden">
                  <div className="grid grid-cols-1 sm:grid-cols-12 py-3 px-4 text-sm font-bold text-black border-b-2 border-gray-400">
                    <div className="sm:col-span-4 mb-2 sm:mb-0">Name</div>
                    <div className="sm:col-span-3 mb-2 sm:mb-0">Date & Time</div>
                    <div className="sm:col-span-4 mb-2 sm:mb-0">Appointment Type</div>
										<div className="sm:col-span-1 mb-2 sm:mb-0"></div>
                  </div>
                  <div
                    className="appointment-section overflow-y-auto max-h-[400px]"
                    id="appointmentsContainer"
                    ref={containerRef}
                  >
                    {appointments.slice(0, visibleAppointments).map((appointment, index) => (
                      <motion.div
                        key={appointment.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className={`grid grid-cols-1 sm:grid-cols-12 border-b-2 border-gray-400 text-sm hover:bg-gray-50 py-3 px-4 transition duration-200 ease-in-out ${
                          appointment.isPast ? "text-gray-400" : ""
                        }`}
                        ref={index === visibleAppointments - 1 ? lastAppointmentRef : null}
                      >
                        <div className="sm:col-span-4 flex items-center mb-2 sm:mb-0">
                          <img
                            src={appointment.avatar || "/placeholder.svg"}
                            alt={appointment.name}
                            className="w-8 h-8 rounded-full mr-3"
                          />
                          <span className="font-medium">{appointment.name}</span>
                        </div>
                        <div className="sm:col-span-3 flex items-center mb-2 sm:mb-0">{appointment.dateTime}</div>
                        <div className="sm:col-span-4 flex items-center sm:justify-start mb-2 sm:mb-0">
                          {appointment.type}

                          {appointment.cancelled ? (
                            <span
                              className="ml-2 px-2 py-1 text-xs font-bold text-red-600 bg-red-100 rounded"
                              title="Cancelled Appointment"
                            >
                              Canceled
                            </span>
                          ) : appointment.isBilled ? (
														<span
															className="ml-2 px-2 py-1 text-xs font-bold text-green-600 bg-green-100 rounded"
															title="Billed Appointment"
														>
															Billed
														</span>
													) : appointment.isPast ? (
														<span
															className="ml-2 px-2 py-1 text-xs font-bold text-orange-400 bg-red-100 rounded"
															title="Past Appointment"
														>
															Past
														</span>
													) : (
														<span
															className="ml-2 px-2 py-1 text-xs font-bold text-therapy-blue-dark bg-gray-100 rounded"
															title="Upcoming Appointment"
														>
															Upcoming
														</span>
													)}
                        </div>
												<div className="sm:col-span-1 flex justify-end mb-2 sm:mb-0">
													{appointment.isPast && !appointment.isBilled && !appointment.cancelled && (
														<ActionButton
															onAccept={() => {
																setSelectedAppointment(appointment);
																confirmPaymentState.open();
															}}
															data-testid="accept-button"
															acceptLabel='Confirm Payment'
														/>
													)}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="py-10 text-center text-lg text-gray-500 border-2 border-dashed rounded-lg italic">
                  No appointments found
                </div>
              )}

              {!loadingAppointments && appointments.length > 5 && (
                <div className="flex justify-center mt-4">
                  {visibleAppointments < appointments.length ? (
                    <button onClick={handleShowMore} className="text-therapy-blue-dark font-bold hover:underline">
                      + Show More
                    </button>
                  ) : (
                    <button onClick={handleShowLess} className="text-therapy-blue-dark font-bold hover:underline">
                      Show Less
                    </button>
                  )}
                </div>
              )}

							<NormalDialog
								opacity={80}
								state={confirmPaymentState}
								title="Confirm Appointment Payment"
								onClose={() => {
									confirmPaymentState.close();
									setSelectedAppointment(null);
								}}
								onCancel={() => {
									confirmPaymentState.close();
									setSelectedAppointment(null);
								}}
								width="w-[500px] max-w-[95vw]"
								titleCentered={true}
								confirmLabel='Yes'
								verifyText='CONFIRM'
								primaryButtonColor={true}
								loading={isConfirmingPayment}
								onAccept={async () => {
									if (!selectedAppointment?.id) return;
									setIsConfirmingPayment(true);
									const result = await dispatch(confirmPaymentAction(Number(selectedAppointment.id)));
									setIsConfirmingPayment(false);
									if (result?.status === 200) {
										fetchAppointments();
										fetchPaymentsData();
										confirmPaymentState.close();
										setSelectedAppointment(null);
									}
								}}
							>
								<p className="text-sm">
									Are you sure you want to confirm payment for this appointment with <strong>{selectedAppointment?.name}</strong>?
									<br />
									<br />
									<strong>This process is not reversible.</strong>
									<br />
									<br />
									Only continue if the appointment session was conducted with the patient successfully.
								</p>
							</NormalDialog>
            </div>
          </div>
        </div>

				{/* Payments Section */}
				<div className="mt-10">
					<div className="h-full flex flex-col flex-grow">
						<div className="flex-none flex flex-row justify-between items-center mb-4">
							<div className="text-2xl sm:text-3xl font-medium text-therapy-blue-dark">
								Payments
							</div>
							<div>
								<button
									onClick={() =>
										handleExport('/patients/payments/export?', 'Payments.xls')
									}
									style={
										loadingPayments || payments.length === 0
											? { backgroundColor: 'transparent' }
											: {}
									}
									className={`inline-flex items-center px-4 py-2 mt-6 border font-bold rounded-md transition-all duration-200
    ${
			loadingPayments || payments.length === 0
				? 'border-gray-300 text-gray-700 shadow-sm cursor-not-allowed'
				: 'border-therapy-blue-dark text-therapy-blue-dark hover:bg-blue-50 hover:shadow-md'
		}`}
									disabled={loadingPayments || payments.length === 0}
								>
									Export to .xls
								</button>
							</div>
						</div>

            <div>
              {loadingPayments ? (
                <PaymentSkeletonLoader />
              ) : payments.length > 0 ? (
                <div className="overflow-hidden">
                  <div className="grid grid-cols-1 sm:grid-cols-12 py-3 px-4 text-sm font-bold text-black border-b-2 border-gray-400">
                    <div className="sm:col-span-4 mb-2 sm:mb-0">Payment Made By</div>
                    <div className="sm:col-span-4 mb-2 sm:mb-0">
                      <button onClick={sortPayments} className="flex items-center">
                        Amount
                        <svg
                          width="25"
                          height="25"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                          style={{ marginLeft: "5px" }}
                        >
                          <path d="M12 5L7 11H17L12 5Z" fill={sortOrder === "asc" ? "rgb(62, 31, 64)" : "gray"} />
                          <path d="M12 19L7 13H17L12 19Z" fill={sortOrder === "desc" ? "rgb(62, 31, 64)" : "gray"} />
                        </svg>
                      </button>
                    </div>
                    <div className="sm:col-span-4 mb-2 sm:mb-0">Date & Time</div>
                  </div>
                  <div
                    className="payment-section overflow-y-auto max-h-[400px]"
                    id="appointmentsContainer"
                    ref={containerPaymentRef}
                  >
                    {payments.slice(0, visiblePayments).map((payment, index) => (
                      <motion.div
                        key={payment.id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className={`grid grid-cols-1 sm:grid-cols-12 border-b-2 border-gray-400 text-sm hover:bg-gray-50 py-3 px-4 transition duration-200 ease-in-out`}
                        ref={index === visiblePayments - 1 ? lastPaymentRef : null}
                      >
                        <div className="sm:col-span-4 flex items-center min-w-0 mb-2 sm:mb-0">
                          <img
                            src={payment.avatar || ""}
                            alt={payment.name || ""}
                            className="w-8 h-8 rounded-full mr-3 flex-shrink-0"
                          />
                          <span className="font-medium break-words whitespace-normal max-w-[200px] md:max-w-[300px] lg:max-w-[400px]">
                            {payment.name}
                          </span>
                        </div>
                        <div className="sm:col-span-4 mb-3 sm:mb-0 w-[60px] text-right ">{`$${payment.cost}`}</div>
                        <div className="sm:col-span-4 flex items-center text-left min-w-0 mb-2 sm:mb-0">
                          {payment.dateTime}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="py-10 text-center text-lg text-gray-500 border-2 border-dashed rounded-lg italic">
                  No payments found
                </div>
              )}

              {!loadingPayments && payments.length > 5 && (
                <div className="flex justify-center mt-4">
                  {visiblePayments < payments.length ? (
                    <button
                      onClick={handleShowMorePayment}
                      className="text-therapy-blue-dark font-bold hover:underline"
                    >
                      + Show More
                    </button>
                  ) : (
                    <button
                      onClick={handleShowLessPayment}
                      className="text-therapy-blue-dark font-bold hover:underline"
                    >
                      Show Less
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Page
