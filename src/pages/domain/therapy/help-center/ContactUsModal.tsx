import FormDialog from "@/components/dialogs/FormDialog";
import InputComponent from "@/components/forms/InputComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import { AppDispatch } from "@/store/index";
import { contactUsAction } from "@/store/slicers/therapist.slicer";
import { Formik } from "formik";
import { useDispatch } from "react-redux";
import { OverlayTriggerState } from "react-stately";

type ContactUsFormData = {
  subject: string,
  reply_email: string,
  email_content: string,
}

type ContactUsModalProps = {
	email: string,
	state: OverlayTriggerState;
}

const ContactUsModal = ({ email, state } : ContactUsModalProps) => {
	const dispatch = useDispatch<AppDispatch>();

	return (
		<Formik<ContactUsFormData>
			initialValues={{
				subject: "",
				reply_email: email,
				email_content: ""
			}}
			validate={(values) => {
				const errors = {} as ContactUsFormData;

				if (!values.subject) errors.subject = "Subject is required";
				if (!values.reply_email) {
					errors.reply_email = "Reply Email is required";
				} else if (!/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(values.reply_email)) {
					errors.reply_email = "Please enter a valid Email.";
				}
				if (!values.email_content) errors.email_content = "Email Content is required";

				return errors;
			}}
			onSubmit={async (values, actions) => {
				const res = await dispatch(contactUsAction(values));
				if (res && res.status === 200) {
					state.close();
					actions.resetForm();
				}
			}}
		>
			{({ values, handleSubmit, handleChange, errors, isSubmitting, resetForm }) => (
				<FormDialog
					state={state}
					title="Contact Us"
					onCancel={() => {
						resetForm();
						state.close();
					}}
					onSubmit={handleSubmit}
					confirmLabel="Send"
					loading={isSubmitting}
					width={"w-[600px]"}
				>
					<div className="flex flex-col space-y-4">
						<InputComponent
							label="Subject"
							id="subject"
							value={values.subject}
							onChange={(value) => handleChange({ target: { value, name: "subject" } })}
							errorMessage={errors.subject}
						/>

						<InputComponent
							label="Reply Email"
							id="reply_email"
							value={values.reply_email}
							onChange={(value) => handleChange({ target: { value, name: "reply_email" } })}
							errorMessage={errors.reply_email}
						/>

						<TextAreaComponent
							label="Email Content"
							id="email_content"
							value={values.email_content}
							onChange={(value) => handleChange({ target: { value, name: "email_content" } })}
							errorMessage={errors.email_content}
							rows={8}
						/>
					</div>
				</FormDialog>
			)}
		</Formik>
	)
}

export default ContactUsModal