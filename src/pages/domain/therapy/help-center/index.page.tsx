import { RootState } from "@/store/index";
import { FC } from "react";
import { useInView } from "react-intersection-observer";
import { useSelector } from "react-redux";
import { useOverlayTriggerState } from "react-stately";
import ContactUsModal from "./ContactUsModal";

const Page: FC = () => {
  const { ref } = useInView();
  const contactUsState = useOverlayTriggerState({});
  const authStore = useSelector((state: RootState) => state.auth);

  return (
    <div ref={ref}>
      <div
        style={{ borderBottom: "1px solid #ccc" }}
        className="cursor-pointer flex-none w-full h-20 flex flex-row justify-center items-center px-14 text-white gap-4"
      >
        <span className="text-3xl font-bold text-therapy-blue-dark">
          <h3>Help Center</h3>
        </span>
      </div>
      <div className="page-content px-14 pt-10">
        <div>
          <h1 className="text-3xl font-medium text-therapy-blue-dark">Frequently Asked Questions</h1>

          <div className="mt-6">
            <h1 className="text-2xl font-medium text-therapy-blue-dark">[Question Here]?</h1>

            <p>The answer will go here.......</p>
          </div>
        </div>

        <div className="mt-10">
          <h1 className="text-3xl font-medium text-therapy-blue-dark">Contact Us</h1>

          <button onClick={() => contactUsState.open()} className="inline-flex items-center mt-4 px-8 py-2 border-2 border-therapy-blue-dark text-therapy-blue-dark font-medium rounded-md hover:bg-blue-50 transition-colors">
            <i className="fa-solid fa-envelope mr-2"></i>
            Contact Us
          </button>
        </div>
      </div>
      <ContactUsModal email={authStore.user?.email || ""} state={contactUsState} />
    </div>
  );
};

export default Page;
