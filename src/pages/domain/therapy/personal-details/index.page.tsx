import SaveButton from "@/components/buttons/SaveButton"
import InputComponent from "@/components/forms/InputComponent"
import { genders } from "@/configs/registration.configs"
import { Formik } from "formik"
import type { FC } from "react"
import { useInView } from "react-intersection-observer"
import type { AppDispatch, RootState } from "@/store/index"
import { useDispatch, useSelector } from "react-redux"
import TherapistRepository from "@/repositories/TherapistRepository"
import { setUser } from "@/store/slicers/auth.slicer"
import { Link } from "react-router-dom"
import NormalSelectComponent from "@/components/forms/NormalSelectComponent"
import DateComponent from "@/components/forms/DateComponent"

const Page: FC = () => {
  const { ref } = useInView()
  const authStore = useSelector((state: RootState) => state?.auth)
  const { user } = authStore
  const therapistRepo = new TherapistRepository()
  const dispatch = useDispatch<AppDispatch>()

  const filteredGenders = [
    ...genders.filter((gender) => gender.value !== "all"),
    {
      label: "Prefer Not to Say",
      value: "not-say",
      name: "gender",
      id: "prefer-not-to-say",
    },
  ]

  return (
    <div ref={ref}>
      <div
        style={{ borderBottom: "1px solid #ccc" }}
        className="cursor-pointer flex-none w-full h-16 md:h-20 flex flex-row justify-center items-center px-4 sm:px-8 md:px-14 text-white gap-4"
      >
        <h1 className="text-2xl md:text-3xl font-bold text-therapy-blue-dark">Your Personal Details</h1>
      </div>
      <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
        <Formik
          initialValues={{
            first_name: user?.firstname,
            last_name: user?.lastname,            
            gender: user?.gender,
            dob: user?.dob,
          }}
          validate={(values) => {
            const errors = {} as any
            const namePattern = /^[a-zA-Z\s]+$/

            if (!values.first_name) {
              errors.first_name = "First name is a required field"
            } else if (values.first_name.length > 20) {
              errors.first_name = "Character limit exceeded"
            } else if (!namePattern.test(values.first_name)) {
              errors.first_name = "First name should contain only letters"
            }

            if (!values.last_name) {
              errors.last_name = "Last name is a required field"
            } else if (values.last_name.length > 20) {
              errors.last_name = "Character limit exceeded"
            } else if (!namePattern.test(values.last_name)) {
              errors.last_name = "Last name should contain only letters"
            }
            if (!values.dob) errors.dob = "Date of birth is a required field"
            else {
              const selectedDate = new Date(values.dob)
              const currentDate = new Date()
              if (selectedDate > currentDate) {
                errors.dob = "Please select a valid date"
              }
            }

            return errors
          }}
          onSubmit={async (values) => {
            if (!user || !user.id) return
            const res = await therapistRepo.update(user.id, values)
            if (res && res.status === 200) dispatch(setUser(res.data.therapist))
          }}
        >
          {({ handleChange, values, handleSubmit, errors, isValid, isSubmitting }) => {
            return (
              <form onSubmit={handleSubmit}>
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold text-therapy-blue-dark">Contact Info</h1>

                  <div className="mt-8 w-full max-w-[350px]">
                    <InputComponent
                      value={values.first_name}
                      maxLength={21}
                      onChange={(value) => {
                        handleChange({ target: { value, name: "first_name" } })
                      }}
                      id="first_name"
                      class="mb-5"
                      label={"First Name"}
                    />
                    {errors.first_name && (
                      <p style={{ color: "red", fontSize: 12, margin: "-18px 0 0 0" }}>{errors.first_name}</p>
                    )}

                    <InputComponent
                      value={values.last_name}
                      onChange={(value) => {
                        handleChange({ target: { value, name: "last_name" } })
                      }}
                      class="mb-5"
                      id="last_name"
                      label={"Last Name"}
                    />
                    {errors.last_name && (
                      <p style={{ color: "red", fontSize: 12, margin: "-18px 0 0 0" }}>{errors.last_name}</p>
                    )}

                  </div>
                </div>
                <br />
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold text-therapy-blue-dark">Additional Info</h1>

                  <div className="flex flex-col gap-4 mt-8 w-full max-w-[350px] md:max-w-[150px]">
                    <NormalSelectComponent
                      label="Gender"
                      id="gender"
                      value={values.gender}
                      onChange={(value) => {
                        handleChange({ target: { value, name: "gender" } })
                      }}
                      options={filteredGenders}
                    />

                    <section className="w-full mb-5">
                      <DateComponent
                        id="dob"
                        label="Date of Birth"
                        value={values.dob || ""}
                        isRequired={false}
                        maxDate={new Date()}
                        outputFormat="iso" 
                        onChange={(dateOnly) =>
                          handleChange({ target: { value: dateOnly, name: "dob" } })
                        }
                      />
                     
                      {errors.dob && typeof errors.dob === "string" && (
                        <p style={{ color: "red", fontSize: 12 }} className="text-error">
                          {errors.dob}
                        </p>
                      )}
                    </section>
                  </div>
                </div>

                <div
                  className="p-4 mb-4 w-full max-w-[500px] text-therapy-blue border border-blue-300 rounded-lg bg-blue-50"
                  role="alert"
                >
                  <div className="flex items-center">
                    <svg
                      className="flex-shrink-0 w-4 h-4 me-2"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                    </svg>
                    <h3 className="text-lg font-medium">Info</h3>
                  </div>
                  <div className="mt-2 mb-4 text-md">
                    Email and Phone Number can be updated on the{" "}
                    <Link to="/security" className="font-bold hover:underline">
                      Security Page
                    </Link>
                    .
                  </div>
                </div>

                <div className="mt-10 mb-10">
                  <SaveButton
                    disabled={!isValid || isSubmitting}
                    value="Save"
                    loading={isSubmitting}
                  />
                </div>
              </form>
            )
          }}
        </Formik>
      </div>
    </div>
  );
};

export default Page