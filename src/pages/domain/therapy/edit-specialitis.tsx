import AppButton from "@/components/buttons/AppButton";
import { specialities } from "@/configs/registration.configs";
import { AppDispatch, RootState } from "@/store/index";
import { updateRegistrationForm } from "@/store/slicers/auth.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import { FC, useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { DragDropContext, Draggable, Droppable, DropResult } from "react-beautiful-dnd";
import ToolbarComponent from "@/components/ToolbarComponent";
import notification from "@/utils/notification.util";

const EditSpecialitis: FC = () => {
  const [specList, setSpecList] = useState<any[]>([]);
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop()!;
  const authStore = useSelector((state: RootState) => state.auth);
  const selectedSpecialities = authStore.registration.formContent["specialization"] || [];
  const pageContent = authStore.registration.formContent[pageId] || {};
  const { userRegistrationToken } = authStore.registration;

  useEffect(() => {
    const alreadySelected = Object.values(pageContent);
    const filtered = specialities.filter(
      (spc) =>
        Object.keys(selectedSpecialities).includes(spc.value.toString()) &&
        !alreadySelected.includes(spc.value.toString())
    );
    setSpecList(filtered);
  }, []);

  const totalDroppableLoopNumbers = Array.from(
    { length: Object.keys(selectedSpecialities).length },
    (_, i) => i + 1
  );

  const handleRemove = (key: string) => {
    const currentSpec = specialities.find(opt => opt.value === pageContent[key]);
    const newPageContent = { ...pageContent };
    delete newPageContent[key];
    dispatch(updateRegistrationForm({ pageId, values: newPageContent }));
    if (currentSpec) setSpecList(prev => [...prev, currentSpec]);
  };

  const handleDragEnd = useCallback((result: DropResult) => {
    const { destination, draggableId } = result;
    if (!destination || destination.droppableId === "pre-selected-list") return;

    const updatedSpecList = specList.filter(opt => opt.value !== draggableId);
    const existingValue = pageContent[destination.droppableId];

    if (existingValue) {
      const selectedSpecOption = specialities.find(spc => spc.value.toString() === existingValue);
      if (selectedSpecOption) updatedSpecList.push(selectedSpecOption);
    }

    setSpecList(updatedSpecList);
    dispatch(
      updateRegistrationForm({
        pageId,
        values: { ...pageContent, [destination.droppableId]: draggableId },
      })
    );
  }, [specList, pageContent, dispatch, pageId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userRegistrationToken) {
      notification.error("User Token is required");
      return;
    }
    const res = await dispatch(registerTherapistAction(pageContent, pageId, userRegistrationToken));
    if (res && res.status === 201) navigate("/auth/register/additional-focus");
  };

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-16 py-8">
        <h1 className="text-3xl font-medium text-therapy-blue">
          Rank your sub-specialities (1 being the strongest):
        </h1>
        <h3 className="text-2xl font-medium text-therapy-blue mt-8">
          Drag and drop selections to indicate your preferred rankings.
        </h3>
        <br />

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable direction="horizontal" droppableId="pre-selected-list">
            {(provided) => (
              <div ref={provided.innerRef} {...provided.droppableProps} style={{ display: "flex" }}>
                {specList.map((spec, i) => (
                  <Draggable key={spec.value} draggableId={spec.value} index={i}>
                    {(providedIn) => (
                      <div
                        ref={providedIn.innerRef}
                        {...providedIn.draggableProps}
                        {...providedIn.dragHandleProps}
                        className="specialities-box"
                      >
                        {spec.label}
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>

          <br />
          <br />
          <ul className="dragger-sub-spec">
            {totalDroppableLoopNumbers.map((item) => (
              <Droppable key={item} droppableId={item.toString()}>
                {(provided, snapshot) => (
                  <li
                    className={`drag-here ${snapshot.isDraggingOver ? "about-to-dropped" : ""}`}
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                  >
                    {item}
                    {provided.placeholder}
                    {pageContent[item] && (
                      <div className="specialities-box">
                        {specialities.find(opt => opt.value === pageContent[item])?.label}
                        <i
                          onClick={() => handleRemove(item.toString())}
                          className="fa fa-times-square text-white"
                          aria-label="remove speciality"
                        ></i>
                      </div>
                    )}
                  </li>
                )}
              </Droppable>
            ))}
          </ul>
        </DragDropContext>

        <div className="mt-8">
          <form className="flex-none flex flex-col gap-2" onSubmit={handleSubmit}>
            <div className="mt-5">
              <AppButton
                disabled={Object.keys(pageContent).length < totalDroppableLoopNumbers.length}
                value="Continue"
              />
            </div>
            <br />
          </form>
        </div>
      </div>
    </>
  );
};

export default EditSpecialitis;
