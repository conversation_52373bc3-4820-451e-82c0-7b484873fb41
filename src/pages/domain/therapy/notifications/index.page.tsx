import 'react-phone-number-input/style.css'
import PhoneInput, { formatPhoneNumber, isPossiblePhoneNumber } from 'react-phone-number-input'
import SaveButton from "@/components/buttons/SaveButton";
import { FC, useEffect, useState } from "react";
import { useInView } from "react-intersection-observer";
import { appointmentNotifications } from "@/configs/registration.configs";
import { fetchRegInfoAction, registerTherapistAction, sendSmsAction } from "@/store/slicers/therapist.slicer";
import { AppDispatch, RootState } from "@/store/index";
import { useDispatch, useSelector } from "react-redux";
import notification from "@/utils/notification.util";
import { Formik } from "formik";
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent";
import { useOverlayTriggerState } from 'react-stately';
import NormalDialog from '@/components/dialogs/NormalDialog';
import InputComponent from '@/components/forms/InputComponent';
 
const Page: FC = () => {
  const { ref } = useInView();
  const dispatch = useDispatch<AppDispatch>();
  const authStore = useSelector((state: RootState) => state.auth);
  const [notificationInfo, setNotificationInfo] = useState<any>({});
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [countdown, setCountdown] = useState(0)
 
  const updatePhoneState = useOverlayTriggerState({});
  const phoneTokenState = useOverlayTriggerState({});
 
  const isMobile = window.innerWidth < 640
 
  const fetchNotificationInfo = async () => {
    if (!authStore.user) return
    const res = await dispatch(fetchRegInfoAction({ pageName: 'waitlist-notifications' }));
    if (res && res.status === 200) {
      setNotificationInfo(res.data.payloadInfo)
    }
  }
  
  const startCountdown = () => {
    setCountdown(60)
    const timer = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          clearInterval(timer)
          return 0
        }
        return prevCount - 1
      })
    }, 1000)
  }
 
  const sendSms = async (phone: string) => {
    setIsLoading(true)
    const res = await dispatch(sendSmsAction({ new_phone: phone }))
    startCountdown()
    if (res && res.status === 200) {
      updatePhoneState.close()
      phoneTokenState.open()
    }
    setIsLoading(false)
  }

  const renderResendMessage = (phone: string) => {
    if (countdown > 0) {
      return <span className="text-danger">Try again in {countdown}s</span>;
    } else if (isLoading) {
      return <span className="text-therapy-blue-dark">Sending...</span>;
    } else {
      return (
        <span
          className="text-therapy-blue-dark cursor-pointer"
          onClick={() => sendSms(phone)}
        >
          Try again.
        </span>
      );
    }
  };  
 
  useEffect(() => {
    fetchNotificationInfo();
  }, []);
 
  return (
    <div ref={ref}>
      <div
        style={{ borderBottom: "1px solid #ccc" }}
        className="cursor-pointer flex-none w-full h-20 flex flex-row justify-center items-center px-14 text-white gap-4"
      >
        <span className="text-3xl font-bold text-therapy-blue-dark">
          <h3>Notifications</h3>
        </span>
      </div>
      <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-therapy-blue-dark">
          How Would You Like To Be Notified of Newly
          <span className="hidden sm:inline">
            <br />
          </span>{" "}
          Scheduled Appointments?
        </h1>
 
        <div className="mt-8">
          <Formik
            enableReinitialize
            initialValues={{
              booking_time_hour: notificationInfo?.booking_time_hour || "",
              notification_preferences: notificationInfo?.notification_preferences || [],
              phone_number: notificationInfo?.phone_number || "",
              code: "",
            }}
            validate={(values) => {
              const errors = {} as any;
 
              if (!values.notification_preferences || values.notification_preferences.length === 0) errors.notification_preferences = "Notification Preferences is required";
              if (values.notification_preferences.includes("text")) {
                if (!values.phone_number) errors.phone_number = "Phone number is required";
                else if (!isPossiblePhoneNumber(values.phone_number)) errors.phone_number = "Input a valid phone number";
              }
 
              return errors;
            }}
            onSubmit={async (values) => {
              if (!authStore?.user?.userToken) {
                notification.error("User Token is required");
                return;
              }
              let res;
              if (values.notification_preferences.includes("text") && values.phone_number && values.phone_number !== notificationInfo?.phone_number) {
                if ((authStore.user.phone === values.phone_number) && authStore.user.phoneVerifiedAt) {
                  res = await dispatch(registerTherapistAction(values, 'waitlist-notifications', authStore.user.userToken));
                } else {
                  updatePhoneState.open();
                }
              } else {
                res = await dispatch(registerTherapistAction(values, 'waitlist-notifications', authStore.user.userToken));
              }
              if (res && (res.status === 200 || res.status === 201)) fetchNotificationInfo();
            }}
          >
            {({ values, errors, handleChange, isValid, isSubmitting, isValidating, handleSubmit, dirty, setValues, setSubmitting }) => {
              return (
                <>
                  <form onSubmit={handleSubmit}>
                    <div className="mt-5">
                      <AppCheckBoxListComponent
                        checked={values.notification_preferences}
                        onChange={(val) => {
                          setValues({
                            ...values,
                            notification_preferences: val,
                            phone_number: val.includes('text') ? values.phone_number : '',
                          });
                        }}
                        checkList={appointmentNotifications}
                      />
                      <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                        {errors.notification_preferences &&
                          errors.notification_preferences.toString()}
                      </div>
                      {values.notification_preferences.includes("text") && (
                        <div className="w-[300px] ml-5 mt-5 mb-5">
                          <p className="mb-2 text-sm text-gray-400"><span className="text-danger">*</span>Mobile Phone Number</p>
 
                          <PhoneInput
                            international
                            countryCallingCodeEditable={false}
                            defaultCountry="US"
                            value={values.phone_number}
                            onChange={(val) => {
                              handleChange({ target: { value: val, name: "phone_number" } });
                            }}
                            className='register-phone-input'
                          />
                          {errors.phone_number && (
                            <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                              {errors.phone_number.toString()}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
 
                    <div className="mt-6 mb-10">
                      <SaveButton
                        disabled={!isValid || isValidating || !dirty}
                        loading={isSubmitting}
                        value="Save"
                      />
                    </div>
                  </form>
                  <NormalDialog
                    state={updatePhoneState}
                    title="Update Phone Number?"
                    confirmLabel="Ok"
                    onCancel={() => {
                      updatePhoneState.close()
                    }}
                    onAccept={() => sendSms(values.phone_number)}
                    marginTop={isMobile ? -100 : -150}
                    loading={isLoading}
                  >
                    <p className="text-sm break-words">
                      You need to first verify your new phone number for receiving notifications.{' '}
                      You will receive a verification token on your number
                      <strong className="block mt-1">{formatPhoneNumber(values.phone_number)}</strong>
                      Do you want to continue?
                    </p>
                  </NormalDialog>
 
                  <NormalDialog
                    state={phoneTokenState}
                    title="Verify Phone Number"
                    confirmLabel="Confirm"
                    onCancel={() => {
                      phoneTokenState.close();
                    }}
                    onAccept={async () => {
                      if (!authStore?.user?.userToken) {
                        notification.error("User Token is required");
                        return;
                      }
                      setSubmitting(true);
                      const res = await dispatch(registerTherapistAction(values, 'waitlist-notifications', authStore.user.userToken));
                      setSubmitting(false);
                      if (res && (res.status === 200 || res.status === 201)) {
                        fetchNotificationInfo();
                        phoneTokenState.close();
                      }
                    }}
                    marginTop={-100}
                  >
                    <div>
                      <p className="text-sm">
                        A verification code has been sent to your new phone number
                        <br />
                        <strong>{formatPhoneNumber(values.phone_number)}</strong>
                        Please input the verification code to replace the old phone number.
                      </p>
 
                      <InputComponent
                        value={values.code}
                        type="text"
                        placeholder="Enter verification code"
                        onChange={(value) => handleChange({ target: { value, name: "code" } })}
                        id="reset_code"
                        class="mt-5 mb-5"
                        errorMessage={errors.code}
                      />
 
                      <div>
                        <p>
                          Didn't receive verification code? {!isLoading && "You can "}
                          {renderResendMessage(values.phone_number)}
                        </p>
                      </div>
                    </div>
                  </NormalDialog>
                </>
              )
            }}
          </Formik>
 
        </div>
      </div>
    </div>
  );
};
 
export default Page;