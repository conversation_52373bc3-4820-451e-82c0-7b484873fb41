import "react-phone-number-input/style.css"
import PhoneInput, { formatPhoneNumber, isPossiblePhoneNumber } from "react-phone-number-input"
import AppButton from "@/components/buttons/AppButton"
import NormalDialog from "@/components/dialogs/NormalDialog"
import InputComponent from "@/components/forms/InputComponent"
import type { RootState, AppDispatch } from "@/store/index"
import {
  changePasswordAction,
  resetEmailAction,
  sendTokenAction,
  sendSmsAction,
  resetPhoneAction,
  enableDisableMfaAction,
} from "@/store/slicers/therapist.slicer"
import dayjs from "dayjs"
import { Formik } from "formik"
import { type FC, useState } from "react"
import { useInView } from "react-intersection-observer"
import { useSelector, useDispatch } from "react-redux"
import { useOverlayTriggerState } from "react-stately"
import relativeTime from "dayjs/plugin/relativeTime"
import advancedFormat from "dayjs/plugin/advancedFormat"

dayjs.extend(advancedFormat)
dayjs.extend(relativeTime)

const Page: FC = () => {
  const { ref } = useInView()
  const authStore = useSelector((state: RootState) => state.auth)
  const { user } = authStore
  const dispatch = useDispatch<AppDispatch>()
  const [showEditEmail, setShowEditEmail] = useState<boolean>(false)
  const [showEditPassword, setShowEditPassword] = useState<boolean>(false)
  const [showEditPhone, setShowEditPhone] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const isMobile = window.innerWidth < 640

  const updateEmailState = useOverlayTriggerState({})
  const updatePhoneState = useOverlayTriggerState({})
  const emailTokenState = useOverlayTriggerState({})
  const phoneTokenState = useOverlayTriggerState({})
  const mfaState = useOverlayTriggerState({})

  const [countdown, setCountdown] = useState(0)
  

  const startCountdown = () => {
    setCountdown(60)
    const timer = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          clearInterval(timer)
          return 0
        }
        return prevCount - 1
      })
    }, 1000)
  }

  const sendToken = async (email: string) => {
    setIsLoading(true)
    const res = await dispatch(sendTokenAction({ old_email: user?.email, new_email: email }))
    startCountdown()
    if (res && res.status === 200) {
      updateEmailState.close()
      emailTokenState.open()
    }
    setIsLoading(false)
  }

  const sendSms = async (phone: string) => {
    setIsLoading(true)
    const res = await dispatch(sendSmsAction({ new_phone: phone }))
    startCountdown()
    if (res && res.status === 200) {
      updatePhoneState.close()
      phoneTokenState.open()
    }
    setIsLoading(false)
  }

  return (
    <div ref={ref}>
      <div
        style={{ borderBottom: "1px solid #ccc" }}
        className="cursor-pointer flex-none w-full h-20 flex flex-row justify-center items-center px-4 sm:px-8 md:px-14 text-white gap-4"
      >
        <span className="text-3xl font-bold text-therapy-blue-dark">
          <h3>Password & Security</h3>
        </span>
      </div>

      {showEditEmail ? (
        <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
          <Formik
            initialValues={{
              email: user?.email || "",
              code: "",
            }}
            validate={(values) => {
              const errors = {} as any

              if (!values.email) errors.email = "Email is a required field"
              else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(values.email))
                errors.email = "Invalid email address"

              if (!values.code && emailTokenState.isOpen) errors.code = "Code is a required field"

              return errors
            }}
            onSubmit={async (values, actions) => {
              setIsLoading(true)
              const res = await dispatch(
                resetEmailAction({ old_email: user?.email, new_email: values.email, code: values.code }),
              )
              if (res && res.status === 200) {
                emailTokenState.close()
                actions.resetForm()
                setShowEditEmail(false)
              }
              setIsLoading(false)
            }}
          >
            {({ values, handleChange, handleSubmit, errors, resetForm }) => {
              return (
                <>
                  <div className="card-container w-full overflow-hidden">
                    <div className="card-header">
                      <h4>Change Email</h4>
                    </div>

                    <div className="mt-8 ml-4 md:ml-8 max-w-[400px]">
                      <InputComponent
                        value={values.email}
                        type="email"
                        onChange={(value) => {
                          handleChange({ target: { value, name: "email" } })
                        }}
                        id="email"
                        class="mb-5"
                        label={"Email"}
                        errorMessage={errors.email}
                      />
                    </div>

                    <div className="flex gap-2 mt-10 ml-4 md:ml-8 mb-10 max-w-[400px]">
                      <AppButton
                        type="button"
                        disabled={!!errors.email || user?.email === values.email}
                        onClick={updateEmailState.open}
                        value="Save"
                      />
                      <AppButton
                        type="button"
                        value="Cancel"
                        onClick={() => {
                          setShowEditEmail(false)
                          resetForm()
                        }}
                      />
                    </div>
                  </div>

                  <NormalDialog
                    state={updateEmailState}
                    title="Update Email?"
                    confirmLabel="Ok"
                    onCancel={() => {
                      updateEmailState.close()
                    }}
                    onAccept={() => sendToken(values.email)}
                    marginTop={isMobile ? -100 : -150}
                    loading={isLoading}
                  >
                    <p className="text-sm break-words">
                      You will receive a reset token on your new email address <strong>({values.email})</strong>. Please
                      verify your new email with the received token.
                    </p>
                  </NormalDialog>

                  <NormalDialog
                    state={emailTokenState}
                    title="You requested to update your email address."
                    confirmLabel="Reset"
                    onCancel={() => {
                      resetForm()
                      emailTokenState.close()
                    }}
                    onAccept={() => {
                      handleSubmit()
                    }}
                    marginTop={-100}
                  >
                    <div>
                      <p className="text-sm">
                        A reset code has been sent to your new email address
                        <strong className="block mt-1">({values.email})</strong>. Please input the reset code to replace
                        the old email.
                      </p>

                      <InputComponent
                        value={values.code}
                        type="text"
                        placeholder="Enter reset code"
                        onChange={(value) => handleChange({ target: { value, name: "code" } })}
                        id="reset_code"
                        class="mt-5 mb-5"
                        label={"Reset Code"}
                        errorMessage={errors.code}
                      />

                      <div>
                        <p>
                          Didn't received verification link? {!isLoading && "You can "}
                          {countdown > 0 ? (
                            <span className="text-danger">Try again in {countdown}s</span>
                          ) : isLoading ? (
                            <span className="text-therapy-blue-dark">Sending...</span>
                          ) : (
                            <span
                              className="text-therapy-blue-dark cursor-pointer"
                              onClick={() => sendToken(values.email)}
                            >
                              Try again.
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </NormalDialog>
                </>
              )
            }}
          </Formik>
        </div>
      ) : (
        <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
          <div className="card-container w-full overflow-hidden">
            <div className="card-header">
              <h4>Primary Contact</h4>
            </div>

            <div className="card-body flex flex-col md:flex-row xl:flex-row">
              <div className="icon-avatar">
                <i className="fa-solid fa-envelope"></i>
              </div>
              <div className="body-content w-full sm:w-auto">
                <p>
                  <strong>
                    {user?.email}
                    {user?.emailVerifiedAt && (
                      <sup>
                        <i className="fa-solid fa-circle-check"></i>
                      </sup>
                    )}
                  </strong>
                </p>
                <small>
                  {dayjs(user?.emailVerifiedAt).format("dddd, MMMM Do YYYY")} | {dayjs(user?.emailVerifiedAt).fromNow()}
                </small>
              </div>
              <div className="body-action">
                {" "}
                &nbsp;
                <button
                  type="button"
                  onClick={() => setShowEditEmail(true)}
                  className="px-3 py-0.5 border bg-therapy-blue-dark text-white rounded-xl hover:opacity-70 transition-colors"
                >
                  <span>Edit</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showEditPhone ? (
        <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
          <Formik
            initialValues={{
              phone: user?.phone || "",
              code: "",
            }}
            validate={(values) => {
              const errors = {} as any

              if (!values.phone) errors.phone = "Phone number is a required field"
              else if (!isPossiblePhoneNumber(values.phone)) errors.phone = "Invalid phone number"

              if (!values.code && phoneTokenState.isOpen) errors.code = "Code is a required field"

              return errors
            }}
            onSubmit={async (values, actions) => {
              setIsLoading(true)
              const res = await dispatch(resetPhoneAction({ new_phone: values.phone, code: values.code }))
              if (res && res.status === 200) {
                phoneTokenState.close()
                actions.resetForm()
                setShowEditPhone(false)
              }
              setIsLoading(false)
            }}
          >
            {({ values, handleChange, handleSubmit, errors, resetForm }) => {
              return (
                <>
                  <div className="card-container overflow-hidden">
                    <div className="card-header">
                      <h4>Change Phone Number</h4>
                    </div>

                    <div className="mt-8 ml-4 md:ml-8 max-w-[400px]">
                      <PhoneInput
                        international
                        countryCallingCodeEditable={false}
                        defaultCountry="US"
                        value={values.phone}
                        onChange={(value) => handleChange({ target: { value, name: "phone" } })}
                        className="custom-phone-input w-full"
                      />
                      {errors.phone && <div className="text-danger mt-4">{errors.phone}</div>}
                    </div>

                    <div className="flex gap-2 mt-10 ml-4 md:ml-8 mb-10 max-w-[400px]">
                      <AppButton
                        type="button"
                        disabled={!!errors.phone || user?.phone === values.phone}
                        onClick={updatePhoneState.open}
                        value="Save "
                      />
                      <AppButton
                        type="button"
                        value="Cancel"
                        onClick={() => {
                          setShowEditPhone(false)
                          resetForm()
                        }}
                      />
                    </div>
                  </div>

                  <NormalDialog
                    state={updatePhoneState}
                    title="Update Phone Number?"
                    confirmLabel="Ok"
                    onCancel={() => {
                      updatePhoneState.close()
                    }}
                    onAccept={() => sendSms(values.phone)}
                    marginTop={isMobile ? -100 : -150}
                    loading={isLoading}
                  >
                    <p className="text-sm break-words">
                      You will receive a verification token on your new phone number
                      <strong className="block mt-1">{formatPhoneNumber(values.phone)}</strong>
                      Please verify your number with the received token.
                    </p>
                  </NormalDialog>

                  <NormalDialog
                    state={phoneTokenState}
                    title="You requested to update your phone number."
                    confirmLabel="Reset"
                    onCancel={() => {
                      resetForm()
                      phoneTokenState.close()
                    }}
                    onAccept={() => {
                      handleSubmit()
                    }}
                    marginTop={-100}
                  >
                    <div>
                      <p className="text-sm">
                        A reset code has been sent to your new phone number
                        <br />
                        <strong>{formatPhoneNumber(values.phone)}</strong>. Please input the reset code to replace the
                        old phone number.
                      </p>

                      <InputComponent
                        value={values.code}
                        type="text"
                        placeholder="Enter reset code"
                        onChange={(value) => handleChange({ target: { value, name: "code" } })}
                        id="reset_code"
                        class="mt-5 mb-5"
                        label={"Reset Code"}
                        errorMessage={errors.code}
                      />

                      <div>
                        <p>
                          Didn't received verification link? {!isLoading && "You can "}
                          {countdown > 0 ? (
                            <span className="text-danger">Try again in {countdown}s</span>
                          ) : isLoading ? (
                            <span className="text-therapy-blue-dark">Sending...</span>
                          ) : (
                            <span
                              className="text-therapy-blue-dark cursor-pointer"
                              onClick={() => sendSms(values.phone)}
                            >
                              Try again.
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </NormalDialog>
                </>
              )
            }}
          </Formik>
        </div>
      ) : (
        <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
          <div className="card-container w-full overflow-hidden">
            <div className="card-header">
              <h4>Phone Number</h4>
            </div>

            <div className="card-body flex flex-col sm:flex-row">
              <div className="icon-avatar">
                <i className="fa-solid fa-envelope"></i>
              </div>
              <div className="body-content w-full sm:w-auto">
                <p>
                  <strong>
                    {user?.phone ? (
                      <>
                        {user?.phone}
                        {user?.phoneVerifiedAt && (
                          <sup>
                            <i className="fa-solid fa-circle-check"></i>
                          </sup>
                        )}
                      </>
                    ) : (
                      "No phone number added"
                    )}
                  </strong>
                </p>
                {user?.phoneVerifiedAt && (
                  <small>
                    {dayjs(user?.phoneVerifiedAt).format("dddd, MMMM Do YYYY")} |{" "}
                    {dayjs(user?.phoneVerifiedAt).fromNow()}
                  </small>
                )}
              </div>
              <div className="body-action">
                {" "}
                &nbsp;
                <button
                  type="button"
                  onClick={() => {
                    setShowEditPhone(true)
                  }}
                  className="px-3 py-0.5 border bg-therapy-blue-dark text-white rounded-xl hover:opacity-70 transition-colors"
                >
                  <span>Edit</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showEditPassword ? (
        <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
          <Formik
            initialValues={{
              old_password: "",
              new_password: "",
              confirm_password: "",
            }}
            validate={(values) => {
              const errors = {} as any

              if (!values.old_password) errors.old_password = "Old password is a required field"
              
              if (!values.new_password) {
                errors.new_password = "New password is a required field"
              } else {
                const hasUpperCase = /[A-Z]/.test(values.new_password);
                const hasNumber = /\d/.test(values.new_password);
                const hasSpecial = /[\W_]/.test(values.new_password);
                const isLongEnough = values.new_password.length >= 8;
                
                if (!(hasUpperCase && hasNumber && hasSpecial && isLongEnough)) {
                  // Create custom error message based on what's missing
                  let errorMsg = '* Password must be';
                  
                  if (!isLongEnough) {
                    errorMsg += ' at least 8 characters long';
                  }
                  
                  // Only include uppercase requirement if it's not met
                  if (!hasUpperCase) {
                    errorMsg += (errorMsg !== '* Password must be' ? ',' : '') + 
                                ' contain at least one uppercase letter';
                  }
                  
                  if (!hasNumber) {
                    errorMsg += (errorMsg !== '* Password must be' ? ',' : '') + 
                                ' contain at least one number';
                  }
                  
                  if (!hasSpecial) {
                    errorMsg += (errorMsg !== '* Password must be' ? ',' : '') + 
                                ' contain at least one special character';
                  }
                  
                  errorMsg += '.';
                  errors.new_password = errorMsg;
                }
              }
              
              if (!values.confirm_password) errors.confirm_password = "Confirm password is a required field"
              else if (values.new_password !== values.confirm_password)
                errors.confirm_password = "Passwords do not match"

              return errors
            }}
            onSubmit={async (values, actions) => {
              if (!user || !user.email) return
              const data = { ...values, email: user.email }
              const res = await dispatch(changePasswordAction(data))
              if (res && res.status === 201) {
                actions.resetForm()
                setShowEditPassword(false)
              }
            }}
          >
            {({ values, handleChange, handleSubmit, isSubmitting, isValid, errors, resetForm, touched, setFieldTouched }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <div className="card-container w-full overflow-hidden">
                    <div className="card-header">
                      <h4>Change Password</h4>
                    </div>

                    <div className="mt-8 ml-4 md:ml-8 max-w-[400px]">
                      <InputComponent
                        value={values.old_password}
                        type="password"
                        onChange={(value) => {
                          setFieldTouched("old_password")
                          handleChange({ target: { value, name: "old_password" } })
                        }}
                        id="old_password"
                        class="mb-5"
                        label={"Old Password"}
                        errorMessage={errors.old_password && touched.old_password ? errors.old_password : ""}
                      />

                      <InputComponent
                        value={values.new_password}
                        type="password"
                        onChange={(value) => {
                          setFieldTouched("new_password")
                          handleChange({ target: { value, name: "new_password" } })
                        }}
                        id="new_password"
                        class="mb-5"
                        label={"New Password"}
                        errorMessage={errors.new_password && touched.new_password ? errors.new_password : ""}
                        showToggleIcon={true}
                      />

                      <InputComponent
                        value={values.confirm_password}
                        type="password"
                        onChange={(value) => {
                          setFieldTouched("confirm_password")
                          handleChange({ target: { value, name: "confirm_password" } })
                        }}
                        id="confirm_password"
                        class="mb-5"
                        label={"Confirm Password"}
                        errorMessage={errors.confirm_password && touched.confirm_password ? errors.confirm_password : ""}
                      />
                    </div>

                    <div className="flex gap-2 mt-10 ml-4 md:ml-8 mb-10 max-w-[400px]">
                      <AppButton disabled={!isValid || isSubmitting} value="Save" />
                      <AppButton
                        type="button"
                        disabled={isSubmitting}
                        value="Cancel"
                        onClick={() => {
                          setShowEditPassword(false)
                          resetForm()
                        }}
                      />
                    </div>
                  </div>
                </form>
              )
            }}
          </Formik>
        </div>
      ) : (
        <div className="page-content px-4 sm:px-8 md:px-14 pt-6 md:pt-10">
          <div className="card-container w-full overflow-hidden">
            <div className="card-header">
              <h4>Password</h4>
            </div>

            <div className="card-body flex flex-col sm:flex-row">
              <div className="icon-avatar">
                <i className="fa-solid fa-lock"></i>
              </div>
              <div className="body-content w-full sm:w-auto">
                <p>
                  <strong>*******************</strong>
                </p>
                {user?.passwordUpdatedAt && (
                  <small>
                    Password last updated {dayjs(user?.passwordUpdatedAt).format("dddd, MMMM Do YYYY")} |{" "}
                    {dayjs(user?.passwordUpdatedAt).fromNow()}
                  </small>
                )}
              </div>
              <div className="body-action">
                {" "}
                &nbsp;
                <button
                  type="button"
                  onClick={() => setShowEditPassword(true)}
                  className="px-3 py-0.5 border bg-therapy-blue-dark text-white rounded-xl hover:opacity-70 transition-colors"
                >
                  <span>Edit</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="page-content px-4 sm:px-8 md:px-14 pt-6 pb-16 md:pt-10">
        <div className="card-container w-full overflow-hidden">
          <div className="card-header">
            <h4>Multi-Factor Authentication</h4>
          </div>

          <div className="card-body flex flex-col sm:flex-row">
            <div className="icon-avatar">
              <i className="fa-solid fa-lock"></i>
            </div>
            <div className="body-content w-full sm:w-auto">
              <p>
                <strong>MFA has been {user?.mfaEnabled ? "enabled" : "disabled"} for you account</strong>
              </p>
              <small>
                {user?.mfaEnabled
                  ? "Multi-Factor Authentication (MFA) adds an extra layer of security to your account. When logging in, you will receive a one-time verification code via email that must be entered to access your account."
                  : "Multi-Factor Authentication (MFA) is currently disabled. Your account will only require your password to log in, which may be less secure."}
              </small>
            </div>
            <div className="body-action">
              {" "}
              &nbsp;
              <button
                type="button"
                onClick={() => mfaState.open()}
                className="px-3 py-1 border bg-therapy-blue-dark text-white rounded-xl hover:opacity-70 transition-colors"
              >
                <span>{user?.mfaEnabled ? "Disable" : "Enable"} MFA</span>
              </button>
            </div>
          </div>
        </div>

        <NormalDialog
          state={mfaState}
          title={user?.mfaEnabled ? "Disable MFA" : "Enable MFA"}
          confirmLabel={"Confirm"}
          primaryButtonColor={true}
          loading={isLoading}
          onCancel={() => {
            mfaState.close()
          }}
          onAccept={async () => {
            setIsLoading(true)
            const res = await dispatch(enableDisableMfaAction(!user?.mfaEnabled))
            setIsLoading(false)
            if (res && res.status === 200) {
              mfaState.close()
            }
          }}
        >
          <div>
            {user?.mfaEnabled ? (
              <>
                Are you sure you want to <strong>disable</strong> Multi-Factor Authentication (MFA)?  
                Disabling MFA will remove the extra layer of security, and you will no longer receive a verification code via email when logging in.
              </>
            ) : (
              <>
                Are you sure you want to <strong>enable</strong> Multi-Factor Authentication (MFA)?  
                Enabling MFA will add an extra layer of security, requiring you to enter a verification code sent to your email each time you log in.
              </>
            )}
          </div>
        </NormalDialog>
      </div>
    </div>
  )
}

export default Page