import AppButton from "@/components/buttons/AppButton"
import SaveButton from "@/components/buttons/SaveButton"
import NormalDialog from "@/components/dialogs/NormalDialog"
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent"
import InputComponent from "@/components/forms/InputComponent"
import SubscriptionPlanComponent from "@/components/subscription-plan/SubscriptionPlanComponent"
import { insuranceTypes } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import {
  cancelOrRenewSubscriptionAction,
  createTherapistSubscriptionAction,
  fetchRegInfoAction,
  getTherapistSubscriptionAction,
  registerTherapistAction,
  subscribeUsingStripeAction,
  updateTherapistState,
  updateTherapistSubscriptionAction,
} from "@/store/slicers/therapist.slicer"
import { type FC, useEffect, useState } from "react"
import { useInView } from "react-intersection-observer"
import { useDispatch, useSelector } from "react-redux"
import { useOverlayTriggerState } from "react-stately"
import notification from "@/utils/notification.util"
import { fetchSubscriptionPlansAction } from "@/store/slicers/admin.slicer"
import type { SubscriptionPlan } from "@/types/subscription-plan.interface"
import type { TherapistSubscription } from "@/types/therapist-subscription.interface"
import _ from "lodash"
import dayjs from "dayjs"
import { Formik } from "formik"

const Page: FC = () => {
  const { ref } = useInView()
  const dispatch = useDispatch<AppDispatch>()
  const authStore = useSelector((state: RootState) => state.auth)
  const therapistStore = useSelector((state: RootState) => state.therapist)

  const [paymentForms, setPaymentForms] = useState<any>({})
  const [loading, setLoading] = useState<boolean>(false)
  const [plan, setPlan] = useState<TherapistSubscription | null>(null) // subscription plan of the therapist
  const [newPlanType, setNewPlanType] = useState<string>("")
  const [subscriptionPlan, setSubscriptionPlan] = useState<SubscriptionPlan | null>(null) // current subscription plan of the app
  const [isSubPriceChanged, setIsSubPriceChanged] = useState<boolean>(false)

  const changeSubscriptionState = useOverlayTriggerState({})
  const confirmSubscriptionState = useOverlayTriggerState({})
  const paymentSuccessState = useOverlayTriggerState({})
  const revertSubscriptionChangeState = useOverlayTriggerState({})
  const cancelSubscriptionState = useOverlayTriggerState({})

  const fetchTherapistSubscription = async () => {
    if (!authStore.user) return
    const res = await dispatch(getTherapistSubscriptionAction())
    if (res && res.status === 200) {
      if (res.data.plan) {
        setPlan(res.data.plan)

        const plan = res.data.plan as TherapistSubscription
        if (plan.subscriptionType === "yearly") {
          if (plan.stripeInfo?.billedPrice !== Math.round(plan.subscriptionPlan!.annualPrice * 100)) {
            setIsSubPriceChanged(true)
          }
        } else if (plan.subscriptionType === "monthly") {
          if (plan.stripeInfo?.billedPrice !== Math.round(plan.subscriptionPlan!.monthlyPrice * 100)) {
            setIsSubPriceChanged(true)
          }
        }
      } else {
        const subscriptionPlan = await dispatch(fetchSubscriptionPlansAction({ type: "active" }))
        setSubscriptionPlan(subscriptionPlan.data[0])
        changeSubscriptionState.open()
      }
    }
  }

  const fetchClientPaymentForms = async () => {
    if (!authStore.user) return
    const res = await dispatch(fetchRegInfoAction({ pageName: "payment-forms" }))
    if (res && res.status === 200 && res.data) {
      setPaymentForms(res.data.payloadInfo)
    }
  }

  const handlePaymentSuccess = async (sessionId: string) => {
    if (!authStore.user?.userToken) {
      notification.error("User Token is required")
      return
    }
    const res = await dispatch(createTherapistSubscriptionAction(sessionId, authStore.user.userToken))

    if (res && res.status === 201) {
      const { data } = res.data
      setPlan(data)
      changeSubscriptionState.close()
    }
    paymentSuccessState.close()
  }

  useEffect(() => {
    const fetchData = async () => {
      dispatch(updateTherapistState({ key: "fetchingInfo", value: true }))

      try {
        const success = new URLSearchParams(window.location.search).get("success")
        const sessionId = new URLSearchParams(window.location.search).get("session_id")

        if (success === "true" && sessionId) {
          // Remove query parameters from the URL
          const newUrl = window.location.origin + window.location.pathname
          window.history.replaceState({}, "", newUrl)
          paymentSuccessState.open()
          await handlePaymentSuccess(sessionId)
        } else {
          await fetchTherapistSubscription()
        }

        await fetchClientPaymentForms()
      } catch (error: any) {
        notification.error(error.message || "Something Went Wrong")
      } finally {
        dispatch(updateTherapistState({ key: "fetchingInfo", value: false }))
      }
    }

    fetchData()
  }, [])

  return (
    <div ref={ref}>
      <div
        style={{ borderBottom: "1px solid #ccc" }}
        className="cursor-pointer flex-none w-full h-20 flex flex-row justify-center items-center px-4 sm:px-14 text-white gap-4"
      >
        <h1 className="text-2xl sm:text-3xl font-bold text-therapy-blue-dark">Subscriptions & Payment</h1>
      </div>

      {therapistStore.fetchingInfo ? (
        <div className="page-content px-4 sm:px-14 pt-10 pb-10">
          {/* Loader */}
          <div role="status" className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded-full dark:bg-gray-400 w-64 mb-6"></div>
            <div className="bg-white p-6 ml-3 rounded-lg border-[1px] w-full max-w-[330px] mt-10">
              <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-400 w-48 mb-5"></div>
              <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-24 mb-3"></div>
              <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-32 mb-8"></div>
              <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-400 w-48 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-40 mb-8"></div>
              <div className="h-10 bg-gray-200 rounded-lg dark:bg-gray-400 w-full max-w-[250px]"></div>
            </div>
          </div>

          <div role="status" className="animate-pulse mt-10">
            <div className="h-8 bg-gray-200 rounded-full dark:bg-gray-400 w-64 mb-6"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-full max-w-[384px] mb-4"></div>
            <div className="h-10 bg-gray-200 rounded-lg dark:bg-gray-400 w-48"></div>
          </div>

          <div role="status" className="animate-pulse mt-10">
            <div className="h-8 bg-gray-200 rounded-full dark:bg-gray-400 w-64 mb-6"></div>
            <div className="h-6 bg-gray-200 rounded-full dark:bg-gray-400 w-full max-w-[384px] mb-4"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-full max-w-[320px] mb-2.5"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-full max-w-[288px] mb-2.5"></div>
            <div className="h-4 bg-gray-200 rounded-full dark:bg-gray-400 w-full max-w-[256px] mb-2.5"></div>
            <div className="h-10 bg-gray-200 rounded-lg dark:bg-gray-400 w-32 mt-10"></div>
          </div>
        </div>
      ) : (
        <div className="page-content px-4 sm:px-14 pt-10">
          {/* NextTherapist Plan Section */}
          {plan ? (
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-therapy-blue-dark">Your NextTherapist Subscription</h1>

              <div
                className={`bg-white p-4 sm:p-6 mx-auto sm:ml-3 rounded-lg border-[1px] flex flex-col items-center w-full max-w-[300px] sm:max-w-[350px] mt-10 transition duration-100 ease-in-out
									border-therapy-blue-dark shadow-lg transform scale-105`}
              >
                <h2 className="text-2xl sm:text-3xl font-medium text-therapy-blue-dark mb-3 sm:mb-5 font-weight-bold">
                  {plan?.subscriptionType === "yearly"
                    ? "Annual Plan"
                    : plan?.subscriptionType === "monthly"
                      ? "Monthly Plan"
                      : ""}
                </h2>
                {isSubPriceChanged ? (
                  <>
                    <p className="text-base sm:text-lg mt-3 text-therapy-blue-dark flex flex-col items-center">
                      <span className="font-bold line-through">
                        $ {plan?.stripeInfo?.billedPrice && plan?.stripeInfo?.billedPrice / 100}
                      </span>
                      <span>(Current Billed Price)</span>
                    </p>
                    <p className="text-base sm:text-lg mt-3 text-therapy-blue-dark flex flex-col items-center">
                      <span className="font-bold">
                        ${" "}
                        {plan?.subscriptionType === "yearly"
                          ? plan.subscriptionPlan?.annualPrice
                          : plan?.subscriptionType === "monthly"
                            ? plan.subscriptionPlan?.monthlyPrice
                            : ""}
                      </span>
                      <span>(New Billing Price)</span>
                    </p>
                  </>
                ) : (
                  <p className="text-base sm:text-lg mt-3 text-therapy-blue-dark">
                    <span className="font-bold">
                      ${" "}
                      {plan?.subscriptionType === "yearly"
                        ? plan.subscriptionPlan?.annualPrice
                        : plan?.subscriptionType === "monthly"
                          ? plan.subscriptionPlan?.monthlyPrice
                          : ""}
                    </span>
                  </p>
                )}
                <p className="text-sm sm:text-md font-normal mb-3 text-therapy-blue-dark">
                  per{" "}
                  {plan?.subscriptionType === "monthly" ? "month" : plan?.subscriptionType === "yearly" ? "year" : ""}
                </p>
                <h3 className="text-base sm:text-lg font-medium text-therapy-blue-dark mt-4">Renewal Date:</h3>
                <p className="mb-6 sm:mb-8 text-center text-sm sm:text-base">
                  {
                    plan.stripeInfo ? (
                      plan.stripeInfo.stripeCancelAtPeriodEnd && !plan.stripeInfo.stripeSubscriptionScheduleId ? (
                        "Subscription Cancelled - No Renewal"
                      ) : (
                        dayjs(plan?.stripeInfo?.stripeCurrentPeriodEnd).format("MM-DD-YYYY")
                      )
                    ) : (
                      "N/A"
                    )
                  }
                </p>
                <br />
                <AppButton
                  width="100%"
                  onClick={() => {
                    if (plan?.stripeInfo?.stripeSubscriptionScheduleId && plan?.stripeInfo?.nextScheduledSubscriptionType) {
                      revertSubscriptionChangeState.open()
                    } else {
                      changeSubscriptionState.open()
                    }
                  }}
                  value="Edit Subscription"
                  // disabled={loading || plan?.stripeInfo?.status === "active"}
                  disabled={loading || (plan?.stripeInfo?.stripeCancelAtPeriodEnd && !plan?.stripeInfo?.stripeSubscriptionScheduleId)}
                />
              </div>
            </div>
          ) : (
            !changeSubscriptionState.isOpen &&
            !paymentSuccessState.isOpen && (
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-therapy-blue-dark">
                  Your NextTherapist Subscription
                </h1>

                <div>
                  <p className="mt-3 text-base sm:text-lg text-danger">
                    Your subscription to NextTherapist is not active. Subscribe now to unlock exclusive access and
                    continue supporting your journey with us.
                  </p>
                  <button
                    className="bg-[#4263EB] hover:bg-[#3b5bde] text-white text-base sm:text-lg font-medium py-2 px-4 sm:px-6 mt-3 rounded-xl transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-[#4263EB] focus:ring-opacity-50"
                    disabled={loading}
                    onClick={() => changeSubscriptionState.open()}
                  >
                    Subscribe Now
                  </button>
                </div>
              </div>
            )
          )}

          {!paymentSuccessState.isOpen && (
            <NormalDialog
              state={changeSubscriptionState}
              width="w-full max-w-[850px]"
              onClose={() => changeSubscriptionState.close()}
              contentCentered={true}
            >
              <div className="pb-4">
                <SubscriptionPlanComponent
                  plan={plan}
                  subscriptionPlan={subscriptionPlan || plan?.subscriptionPlan}
                  setPlanType={setNewPlanType}
                  onCLick={() => confirmSubscriptionState.open()}
                />
              </div>
            </NormalDialog>
          )}

          {plan?.stripeInfo && plan?.stripeInfo?.nextScheduledSubscriptionType && (
            <NormalDialog
              state={revertSubscriptionChangeState}
              title="Revert Subscription Change?"
              confirmLabel={"Confirm"}
              loading={loading}
              primaryButtonColor={true}
              onCancel={() => {
                revertSubscriptionChangeState.close()
              }}
              onAccept={async () => {
                setLoading(true);
                const res = await dispatch(
                  updateTherapistSubscriptionAction({
                    subscriptionType: plan?.subscriptionType,
                    subscriptionPlanId: plan?.subscriptionPlan?.id,
                    therapistSubscriptionId: plan?.id,
                  }),
                );
                setLoading(false);
                if (res && res.status === 200) {
                  revertSubscriptionChangeState.close();
                  fetchTherapistSubscription();
                }
              }}
            >
              <div>
                You have already changed your subscription plan from <strong>{_.capitalize(plan?.subscriptionType)}</strong>{' '}
                to <strong>{_.capitalize(plan?.stripeInfo?.nextScheduledSubscriptionType)}</strong>. Do you want to
                revert this change?
              </div>
            </NormalDialog>
          )}

          <NormalDialog title="Payment Successful!" state={paymentSuccessState}>
            <p className="text-sm font-medium">
              Stripe Payment was successful! Please wait for the payment to be verified by us. It should take few
              minutes to process the payment. Once the payment is verified, you will be able to access the services.
            </p>
          </NormalDialog>

          <NormalDialog
            state={confirmSubscriptionState}
            title="Confirm Subscription Plan"
            confirmLabel={"Confirm"}
            primaryButtonColor={true}
            onCancel={() => {
              setNewPlanType("")
              confirmSubscriptionState.close()
              if (plan?.id) changeSubscriptionState.close()
            }}
            onAccept={async () => {
              if (!authStore.user?.userToken) return
              setLoading(true)
              if (plan?.id) {
                const res = await dispatch(
                  updateTherapistSubscriptionAction({
                    subscriptionType: newPlanType,
                    subscriptionPlanId: plan?.subscriptionPlan?.id,
                    therapistSubscriptionId: plan?.id,
                  }),
                )
                if (res && res.status === 200) {
                  fetchTherapistSubscription()
                  confirmSubscriptionState.close()
                  changeSubscriptionState.close()
                }
              } else {
                const res = await dispatch(
                  subscribeUsingStripeAction({
                    subscriptionType: newPlanType,
                    subscriptionPlanId: subscriptionPlan?.id,
                    redirectPath: "/subscriptions-payments",
                    userToken: authStore.user.userToken,
                  }),
                )
                if (res && res.status === 200) {
                  const { stripeSessionUrl } = res.data
                  if (!stripeSessionUrl) notification.error("Something went wrong. Please try again.")
                  window.location.href = stripeSessionUrl
                }
              }
              setLoading(false)
            }}
            loading={loading}
          >
            {plan?.id ? (
              <p className="text-sm">
                Are you sure you want to change your subscription plan from{" "}
                <strong>{_.upperFirst(plan?.subscriptionType)}</strong> to <strong>{_.upperFirst(newPlanType)}</strong>?
              </p>
            ) : (
              <p className="text-sm">
                Are you sure you want to select <strong>{_.upperFirst(newPlanType)}</strong> subscription plan?
              </p>
            )}
          </NormalDialog>

          <NormalDialog
            state={cancelSubscriptionState}
            title="Cancel Subscription"
            confirmLabel={"Confirm"}
            primaryButtonColor={true}
            loading={loading}
            onCancel={() => {
              cancelSubscriptionState.close()
            }}
            onAccept={async () => {
              if (!plan?.stripeInfo?.id) return
              setLoading(true)
              const res = await dispatch(cancelOrRenewSubscriptionAction(plan.stripeInfo.id))
              if (res && res.status === 200) {
                const { billingSessionUrl } = res.data
                if (!billingSessionUrl) notification.error("Something went wrong. Please try again.")
                window.location.href = billingSessionUrl
              } else {
                setLoading(false);
              }
            }}
          >
            <div>
              Are you sure you want to cancel your <strong>{_.capitalize(plan?.subscriptionType)}</strong> nexttherapist subscription plan?{' '}
              You can also update your subscription plan if you want to try a different plan.
            </div>
          </NormalDialog>

          {/* Payment Information Section */}
          {plan && (
            <div className="mt-10">
              <h1 className="text-2xl sm:text-3xl font-bold text-therapy-blue-dark">Payment Information</h1>

              {plan?.stripeInfo?.status === "active" && (
                <div className="mt-4">
                  {plan.stripeInfo?.stripeCancelAtPeriodEnd ? (
                    <>
                      <p className="mb-3 text-base sm:text-lg">
                        Your Current NextTherapist Subscription is valid until:{" "}
                        <strong>{dayjs(plan?.stripeInfo?.stripeCurrentPeriodEnd).format("MM-DD-YYYY")}</strong>
                      </p>
                      {plan.stripeInfo?.stripeSubscriptionScheduleId && plan.stripeInfo?.nextScheduledSubscriptionType ? (
                        <>
                          <p className="mb-3 text-base sm:text-lg">
                            You have changed your subscription plan from <strong>{_.capitalize(plan?.subscriptionType)}</strong>{' '}
                            to <strong>{_.capitalize(plan?.stripeInfo?.nextScheduledSubscriptionType)}</strong>. After your current
                            plan is completed, you will be automatically charged the new plan price{' '}
                            <span className="font-bold">
                              ${plan.stripeInfo?.nextScheduledSubscriptionType === "yearly"
                                  ? plan.subscriptionPlan?.annualPrice
                                  : plan.stripeInfo?.nextScheduledSubscriptionType === "monthly"
                                    ? plan.subscriptionPlan?.monthlyPrice
                                    : ""}
                              </span>.
                          </p>

                          <AppButton
                            width="100%"
                            onClick={async () => {
                              revertSubscriptionChangeState.open()
                            }}
                            disabled={loading}
                            value="Revert Change"                          
                          /> 
                        </>
                      ) : (
                        <>
                          <p className="mb-3 text-base sm:text-lg">
                            You have cancelled your subscription. You can still access the services until the end of the
                            current subscription period. If you'd like to continue your services, you can renew your
                            subscription before the current period ends. This will ensure your subscription is auto-renewed
                            after the current period expires.
                          </p>
                          {isSubPriceChanged && (
                            <p className="text-base sm:text-lg mb-3">
                              <span className="font-bold">Note:</span> The price has been changed from ${" "}
                              <span className="font-bold">
                                {plan?.stripeInfo?.billedPrice && plan?.stripeInfo?.billedPrice / 100} (which you paid)
                              </span>{" "}
                              to ${" "}
                              <span className="font-bold">
                                {plan?.subscriptionType === "yearly"
                                  ? plan.subscriptionPlan?.annualPrice
                                  : plan?.subscriptionType === "monthly"
                                    ? plan.subscriptionPlan?.monthlyPrice
                                    : ""}
                              </span>{" "}
                              and will be charged starting from your next renewal if you renew the subscription.
                            </p>
                          )}

                          <AppButton
                           width="100%"
                           onClick={async () => {
                            if (!plan?.stripeInfo?.id) return
                            setLoading(true)
                            const res = await dispatch(cancelOrRenewSubscriptionAction(plan.stripeInfo.id))
                            if (res && res.status === 200) {
                              const { billingSessionUrl } = res.data
                              if (!billingSessionUrl) notification.error("Something went wrong. Please try again.")
                              window.location.href = billingSessionUrl
                            } else setLoading(false)
                          }}
                          disabled={loading}
                          value="Renew Subscription"                          
                          />
                          
                        </>
                      )}
                    </>
                  ) : (
                    <>
                      <p className="mb-3 text-base sm:text-lg">
                        Your Current NextTherapist Subscription is valid until:{" "}
                        <strong>{dayjs(plan?.stripeInfo?.stripeCurrentPeriodEnd).format("MM-DD-YYYY")}</strong>
                        <br />
                        It will be auto-renewed after the current period ends.
                      </p>
                      {isSubPriceChanged && (
                        <p className="text-base sm:text-lg mb-3">
                          <span className="font-bold">Note:</span> The price has been changed from ${" "}
                          <span className="font-bold">
                            {plan?.stripeInfo?.billedPrice && plan?.stripeInfo?.billedPrice / 100} (which you paid)
                          </span>{" "}
                          to ${" "}
                          <span className="font-bold">
                            {plan?.subscriptionType === "yearly"
                              ? plan.subscriptionPlan?.annualPrice
                              : plan?.subscriptionType === "monthly"
                                ? plan.subscriptionPlan?.monthlyPrice
                                : ""}
                          </span>{" "}
                          and will be charged starting from your next renewal.
                        </p>
                      )}

                <AppButton
                  width="100%"
                  onClick={async () => {
                    cancelSubscriptionState.open();
                  }}
                  disabled={loading}
                  value="Cancel Subscription"
                />

                    </>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Client Payment Options */}
          <div className="mt-10">
            <h1 className="text-2xl sm:text-3xl font-bold text-therapy-blue-dark">Client Payment Options</h1>

            <div className="mt-8">
              <Formik
                enableReinitialize
                initialValues={{
                  session_fee: paymentForms?.session_fee || "",
                  payment_methods: paymentForms?.payment_methods || [],
                  insurance_list: paymentForms.insurance_list || {},
                }}
                validate={(values) => {
                  const errors = {} as any

                  if (!values.session_fee) errors.session_fee = "Session Fee is required"
                  else if (!/^\d+$/.test(values.session_fee)) {
                    errors.session_fee = "Input a valid fee"
                  } else if (Number(values.session_fee) <= 0) {
                    errors.session_fee = "Session Fee must be greater than 0"
                  }

                  if (!values.payment_methods || values.payment_methods.length === 0) {
                    errors.payment_methods = "Select at least one payment method"
                  } else if (values.payment_methods.includes("insurance")) {
                    if (!values.insurance_list?.checked_list || values.insurance_list?.checked_list?.length === 0)
                      errors.insurance_list = "Insurance Provider is required"

                    if (values.insurance_list?.checked_list?.includes("other") && !values.insurance_list?.other_insurance)
                      errors.insurance_list = "Specify other Insurance Provider"
                  }

                  return errors
                }}
                onSubmit={async (values) => {
                  if (!authStore?.user?.userToken) {
                    notification.error("User Token is required")
                    return
                  }
                  await dispatch(registerTherapistAction(values, "payment-forms", authStore.user.userToken))
                }}
              >
                {({ errors, setFieldValue, values, isValidating, isValid, isSubmitting, handleSubmit, setValues }) => {
                  return (
                    <form onSubmit={handleSubmit}>
                      <div>
                        <h3 className="text-base sm:text-lg font-semibold text-therapy-blue-dark">
                          Input your session fee
                        </h3>

                        <div className="flex items-center gap-4 ml-2 mt-2">
                          <div className="w-[150px] sm:w-[200px]">
                            <InputComponent
                              label="50-min session fee"
                              value={values.session_fee}
                              onChange={(val) => {
                                const numericValue = val.replace(/[^0-9]/g, "")
                                if (!/^0/.test(numericValue)) {
                                  setFieldValue("session_fee", numericValue)
                                }
                              }}
                              isRequired
                              placeholder="Enter #"
                              errorMessage={errors.session_fee && (errors.session_fee as string)}
                              maxLength={4}
                            />
                          </div>
                          <div className="pt-8 text-slate-400">dollars</div>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-base sm:text-lg font-semibold text-therapy-blue-dark mt-4">
                          Select Payment Methods you Accept
                        </h3>

                        <div className="mt-2">
                          <AppCheckBoxListComponent 
                            checked={values.payment_methods}
                            onChange={(val) => {
                              setValues((prev) => ({
                                ...prev,
                                payment_methods: val,
                                insurance_list: val.includes("insurance") ? prev.insurance_list : [],
                              }))
                            }}
                            checkList={[
                              { label: "Self-Pay", value: "self_pay" },
                              { label: "Clergy Pay", value: "clergy_pay" },
                              { label: "HSA/FSA", value: "hsa_fsa" },
                              { label: "Insurance", value: "insurance" },
                            ]}
                          />
                          <div className="text-red-500 text-xs mt-1">
                            {errors.payment_methods && errors.payment_methods.toString()}
                            </div>
                        </div>

                        {values?.payment_methods?.includes("insurance") && (
                          <div className="ml-4 sm:ml-8">
                            <AppCheckBoxListComponent
                              checked={values.insurance_list.checked_list}
                              onChange={(val) => {
                                if (!val.includes("other"))
                                  setFieldValue("insurance_list", { checked_list: [...val] })
                                else
                                  setFieldValue("insurance_list", { ...values.insurance_list, checked_list: [...val] })
                              }}
                              checkList={insuranceTypes}
                            />

                            {values.insurance_list?.checked_list?.includes("other") && (
                                <div className="w-full sm:w-[300px] pl-0 sm:pl-5">
                                  <InputComponent
                                    value={values.insurance_list.other_insurance}
                                    onChange={(val) => {
                                      setFieldValue("insurance_list", { ...values.insurance_list, other_insurance: val })
                                    }}
                                    class="mb-5"
                                  />
                                </div>
                              )}
                            <div className="text-red-500 text-xs">
                              {errors.insurance_list && errors.insurance_list.toString()}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="mt-8">
                        <SaveButton disabled={isValidating || !isValid} loading={isSubmitting} value="Save" />
                      </div>

                      <br />
                    </form>
                  )
                }}
              </Formik>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Page

