import SaveButton from "@/components/buttons/SaveButton"
import NormalDialog from "@/components/dialogs/NormalDialog"
import OfficeHoursComponent, { type ActiveTimes } from "@/components/office-hours/office-hours.component"
import { sessionScheduled } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import { setUser, updateRegistrationForm } from "@/store/slicers/auth.slicer"
import {
  fetchRegInfoAction,
  registerTherapistAction,
  removeGoogleCalendarAction,
  removeOutlookCalendarAction,
  syncGoogleCalendarAction,
  syncOutlookCalendarAction,
  updateTherapistState,
} from "@/store/slicers/therapist.slicer"
import notification from "@/utils/notification.util"
import { useGoogleLogin } from "@react-oauth/google"
import { type FC, useEffect, useState } from "react"
import { useInView } from "react-intersection-observer"
import { useDispatch, useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import { useOverlayTriggerState } from "react-stately"
import { generateURLEncodedString, createCodeChallenge } from "@/utils/app.util"
import type { Calendar } from "@/types/user.interface"
import TherapistRepository from "@/repositories/TherapistRepository"
import toast from "react-hot-toast"
import { Formik } from "formik"
import { isPossiblePhoneNumber } from "react-phone-number-input"
import { LOGO_PATH } from "../../../../../src/pages/auth/constants"

const defaultScope = [
  "https://www.googleapis.com/auth/userinfo.email",
  "https://www.googleapis.com/auth/userinfo.profile",
  "https://www.googleapis.com/auth/calendar",
]

const Page: FC = () => {
  const { ref } = useInView()
  const dispatch = useDispatch<AppDispatch>()
  const removeCalendarState = useOverlayTriggerState({})

  const location = useLocation()
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop()
  const authStore = useSelector((state: RootState) => state.auth)
  const therapistStore = useSelector((state: RootState) => state.therapist)
  const currentPageContent = authStore.registration.formContent[pageId!]
  const [showOfficeHours, setShowOfficeHours] = useState<boolean>(false)
  const [activeTimes, setActiveTimes] = useState<ActiveTimes>({})
  const [selectedTimezone, setSelectedTimezone] = useState<string | null>(null)
  const [appointmentMethod, setAppointmentMethod] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [calendar, setCalendar] = useState<Calendar | null>(null)
  const therapistRepo = new TherapistRepository()

  const [waitlistInfo, setWaitlistInfo] = useState<any>({})
  const [officeHoursFetched, setOfficeHoursFetched] = useState<boolean>(false)

  // Calculate if both calendars are connected
  const hasGoogleCalendar = authStore.user?.hasGoogleCalendar || false
  const hasOutlookCalendar = authStore.user?.hasOutlookCalendar || false
  const hasBothCalendars = hasGoogleCalendar && hasOutlookCalendar

  const SkeletonLoader = () => (
    <div className="animate-pulse">
      {/* Office Hours Section Skeleton */}
      <div className="mb-8">
        <div className="h-8 bg-gray-200 rounded-md w-48 mb-4"></div>
        <div className="h-10 bg-gray-200 rounded-md w-64"></div>
      </div>

      {/* Link Calendar Section Skeleton */}
      <div className="mb-8">
        <div className="h-8 bg-gray-200 rounded-md w-48 mb-6"></div>
        <div className="w-full max-w-[600px] mb-4">
          <div className="h-16 bg-gray-200 rounded-md w-full"></div>
        </div>
        <div className="w-full max-w-[600px]">
          <div className="h-16 bg-gray-200 rounded-md w-full"></div>
        </div>
      </div>

      {/* Waitlist Section Skeleton */}
      <div className="mt-10">
        <div className="h-8 bg-gray-200 rounded-md w-32 mb-4"></div>
        <div className="mt-4">
          <div className="h-7 bg-gray-200 rounded-md w-40 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded-md w-full max-w-md mb-4"></div>
          <div className="w-60 h-10 bg-gray-200 rounded-md mb-8"></div>
        </div>

        <div className="mt-4">
          <div className="h-7 bg-gray-200 rounded-md w-48 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded-md w-full max-w-lg mb-4"></div>
          <div className="w-60 h-10 bg-gray-200 rounded-md"></div>
        </div>

        <div className="mt-10 mb-10">
          <div className="h-10 bg-gray-200 rounded-md w-24"></div>
        </div>
      </div>
    </div>
  )

  useEffect(() => {
    return () => {
      (async () => {
        await new Promise((resolve) => setTimeout(resolve, 100)) // Simulate async behavior
        toast.dismiss() // Clears all active toasts on unmount
      })()
    }
  }, [])

  const googleLogin = useGoogleLogin({
    flow: "auth-code",
    scope: defaultScope.join(" "),
    onSuccess: async (tokens) => {
      setIsLoading(true)
      const res = await dispatch(
        syncGoogleCalendarAction({
          code: tokens.code,
          userToken: authStore.user?.userToken,
        }),
      )
      if (res && res.status === 201) dispatch(setUser(res.data.userData))
      setIsLoading(false)
    },
    onError: (error) => {
      console.log(error)
      if (error.error === "access_denied") return
      notification.error(error.error || "Something went wrong. Please try again.")
    },
  })

  const loginToOutlook = async () => {
    const authorizationUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize`
    const clientId = import.meta.env.VITE_AZURE_CLIENT_ID
    const redirectUri = `${import.meta.env.VITE_BASE_URL}/calendars-appointments`
    const scopes = "openid profile offline_access User.Read Calendars.ReadWrite"
    const responseType = "code"
    const prompt = "select_account"
    const state = await generateURLEncodedString()
    localStorage.setItem("state", state)

    // Generate code verifier and challenge for PKCE
    const codeVerifier = await generateURLEncodedString()
    const codeChallenge = await createCodeChallenge(codeVerifier)

    // Store the code verifier in localStorage to use later for token exchange
    localStorage.setItem("codeVerifier", codeVerifier)

    // Build the authorization URL with the code challenge
    const authUrl = `${authorizationUrl}?client_id=${clientId}&response_type=${responseType}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scopes)}&response_mode=query&state=${state}&code_challenge=${codeChallenge}&code_challenge_method=S256&prompt=${prompt}`

    // Redirect to Azure AD for login
    window.location.href = authUrl
  }

  const exchangeCodeForToken = async (authorizationCode: string, state: string) => {
    const codeVerifier = localStorage.getItem("codeVerifier")
    if (!codeVerifier) {
      notification.error("Something went wrong. Please try Logging in again.")
      return
    }
    const storedState = localStorage.getItem("state")
    if (storedState && state !== storedState) {
      notification.error("Something went wrong. Please try Logging in again.")
      return
    }
    notification.info("Syncing Outlook Calendar. Please wait...")
    setIsLoading(true)
    const res = await dispatch(
      syncOutlookCalendarAction({
        authorizationCode,
        codeVerifier,
        userToken: authStore.user?.userToken,
        redirectPath: "/calendars-appointments",
      }),
    )
    if (res && res.status === 201) dispatch(setUser(res.data.userData))
    setIsLoading(false)
  }

  const fetchWaitlistInfo = async () => {
    if (!authStore.user) return
    setIsLoading(true)
    const res = await dispatch(fetchRegInfoAction({ pageName: "waitlist-notifications" }))
    if (res && res.status === 200) {
      setWaitlistInfo(res.data.payloadInfo)
    }
    setIsLoading(false)
  }

  useEffect(() => {
    fetchWaitlistInfo()
    const params = new URLSearchParams(window.location.search)
    const authorizationCode = params.get("code")
    const state = params.get("state")
    window.history.replaceState({}, document.title, window.location.pathname)
    if (authorizationCode && state) exchangeCodeForToken(authorizationCode, state)
  }, [])

  useEffect(() => {
    if (showOfficeHours) {
      const fetchOfficeHours = async () => {
        if (!authStore.user) return
        dispatch(updateTherapistState({ key: "fetchingInfo", value: true }))
        setIsLoading(true)
        const res = await dispatch(fetchRegInfoAction({ pageName: "normal-office-hours" }))
        if (res && res.status === 200) {
          setActiveTimes(res.data?.payloadInfo?.activeTimes || {})
          setSelectedTimezone(res.data?.payloadInfo?.timezone || null)
          setAppointmentMethod(res.data?.payloadInfo?.appointmentMethod || null)
          setOfficeHoursFetched(true)
        }
        setIsLoading(false)
        dispatch(updateTherapistState({ key: "fetchingInfo", value: false }))
      }
      fetchOfficeHours()
    } else {
      document.querySelector("#scroller-div")?.scrollTo({
        top: 0,
      })
    }
  }, [showOfficeHours])

  useEffect(() => {
    const getPageContent = async () => {
      if (!authStore.user) return

      // If Redux store has data, use it; otherwise, fetch from API
      if (!currentPageContent) {
        setIsLoading(true)
        const res = await therapistRepo.getdurationCost()
        if (res && res?.data.status === 200) {
          dispatch(
            updateRegistrationForm({
              pageId,
              values: res?.data.data || {},
            }),
          )
        }
        setIsLoading(false)
      }
    }

    getPageContent()
  }, [authStore.user?.id, pageId, dispatch, currentPageContent])

  return (
    <div ref={ref}>
      {/* No global loading overlay - we'll use targeted skeleton loaders instead */}
      <div
        style={{ borderBottom: "1px solid #ccc" }}
        className="cursor-pointer flex-none w-full h-20 relative px-14 text-white"
      >
        {/* Back button positioned absolutely at the start */}
        {showOfficeHours && (
          <div className="absolute left-14 top-1/2 -translate-y-1/2">
            <span className="flex flex-row gap-2 items-center cursor-pointer" onClick={() => setShowOfficeHours(false)}>
              <i style={{ color: "#888", fontSize: 22 }} className="fa fa-chevron-left text-white" />
              <span style={{ color: "#555", fontWeight: 500, fontSize: 22 }} className="font-thin">
                Back
              </span>
            </span>
          </div>
        )}

        {/* Title absolutely positioned in the center of main div */}
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <h1 className="text-3xl font-bold text-therapy-blue-dark">Calendars & Appointments</h1>
        </div>
      </div>
      {showOfficeHours ? (
        <>
          <div className="px-10 pt-8">
            {therapistStore.fetchingInfo && isLoading ? (
              <div className="space-y-6">
                {/* Skeleton for Office Hours */}
                <div className="space-y-4">
                  <div className="h-8 bg-gray-200 rounded-md w-1/3 animate-pulse"></div>
                  <div className="grid grid-cols-1 md:grid-cols-7 gap-4" data-testid="office-hours-skeleton-grid">
                    {Array(7)
                      .fill(0)
                      .map((_, index) => (
                        <div key={index} className="space-y-2"  data-testid="skeleton-day-block">
                          <div className="h-6 bg-gray-200 rounded-md w-20 animate-pulse"></div>
                          <div className="h-10 bg-gray-200 rounded-md w-full animate-pulse"></div>
                          <div className="h-10 bg-gray-200 rounded-md w-full animate-pulse"></div>
                        </div>
                      ))}
                  </div>
                  <div className="h-8 bg-gray-200 rounded-md w-1/4 animate-pulse mt-4"></div>
                  <div className="h-10 bg-gray-200 rounded-md w-1/3 animate-pulse"></div>
                  <div className="h-10 bg-gray-200 rounded-md w-40 mt-6 animate-pulse"></div>
                </div>
              </div>
            ) : (
              officeHoursFetched && (
                <OfficeHoursComponent
                  activeTimes={activeTimes}
                  setActiveTimes={setActiveTimes}
                  selectedTimezone={selectedTimezone}
                  setSelectedTimezone={setSelectedTimezone}
                  appointmentMethod={appointmentMethod}
                  setAppointmentMethod={setAppointmentMethod}
                  onSave={async () => {
                    if (!authStore.user) return
                    const data = { activeTimes, timezone: selectedTimezone, appointmentMethod }
                    const res = await dispatch(
                      registerTherapistAction(data, "normal-office-hours", authStore.user.userToken),
                    )
                    if (res && (res.status === 201 || res.status === 200)) setShowOfficeHours(false)
                  }}
                />
              )
            )}
          </div>
        </>
      ) : (
        <div className="page-content px-14 pt-10">
          {isLoading ? (
            <SkeletonLoader />
          ) : (
            <>
              {/* Office Hours Section */}
              <div className="mb-8">
                <h2 data-testid="office-hours-title" className="text-2xl font-bold text-therapy-blue-dark mb-4">Office Hours</h2>
                <button
                  onClick={() => setShowOfficeHours(true)}
                  className="inline-flex items-center px-4 py-2 border border-therapy-blue-dark text-therapy-blue-dark rounded-md hover:bg-blue-50 transition-colors"
                >
                  <i className="fa-solid fa-pencil mr-2"></i>
                  <span className="font-bold">View & Edit Office Hours</span>
                </button>
              </div>

              {/* Link Calendar Section */}
              <div>
                <h2 className="text-2xl font-bold text-therapy-blue-dark mb-6">Link Calendar</h2>

                <div>
                  <div className="w-full max-w-[900px]">
                    {hasGoogleCalendar ? (
                      <div className="flex flex-col lg:flex-row lg:items-center pb-4 mb-4 w-full">
                        {/* Calendar Connection Item - Vertical on mobile and tablet, horizontal on desktop */}
                        <div className="flex flex-col lg:flex-row lg:items-center lg:w-[350px] lg:border-r-2 lg:border-therapy-blue-dark text-therapy-blue-dark pb-4 lg:pb-0">
                          <div className="flex flex-col items-center lg:flex-row lg:space-x-8 space-y-3 lg:space-y-0">
                            {/* Google Icon */}
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="64"
                              height="64"
                              viewBox="0 0 48 48"
                              className="mx-auto lg:mx-0"
                            >
                              <rect width="22" height="22" x="13" y="13" fill="#fff"></rect>
                              <polygon
                                fill="#1e88e5"
                                points="25.68,20.92 26.688,22.36 28.272,21.208 28.272,29.56 30,29.56 30,18.616 28.56,18.616"
                              ></polygon>
                              <path
                                fill="#1e88e5"
                                d="M22.943,23.745c0.625-0.574,1.013-1.37,1.013-2.249c0-1.747-1.533-3.168-3.417-3.168 c-1.602,0-2.972,1.009-3.33,2.453l1.657,0.421c0.165-0.664,0.868-1.146,1.673-1.146c0.942,0,1.709,0.646,1.709,1.44 c0,0.794-0.767,1.44-1.709,1.44h-0.997v1.728h0.997c1.081,0,1.993,0.751,1.993,1.64c0,0.904-0.866,1.64-1.931,1.64 c-0.962,0-1.784-0.61-1.914-1.418L17,26.802c0.262,1.636,1.81,2.87,3.6,2.87c2.007,0,3.64-1.511,3.64-3.368 C24.24,25.281,23.736,24.363,22.943,23.745z"
                              ></path>
                              <polygon fill="#fbc02d" points="34,42 14,42 13,38 14,34 34,34 35,38"></polygon>
                              <polygon fill="#4caf50" points="38,35 42,34 42,14 38,13 34,14 34,34"></polygon>
                              <path fill="#1e88e5" d="M34,14l1-4l-1-4H9C7.343,6,6,7.343,6,9v25l4,1l4-1V14H34z"></path>
                              <polygon fill="#e53935" points="34,34 34,42 42,34"></polygon>
                              <path fill="#1565c0" d="M39,6h-5v8h8V9C42,7.343,40.657,6,39,6z"></path>
                              <path fill="#1565c0" d="M9,42h5v-8H6v5C6,40.657,7.343,42,9,42z"></path>
                            </svg>

                            {/* Sync Icon */}
                            <i className="fa-solid fa-arrows-rotate text-3xl"></i>

                            {/* NextTherapist Logo */}
                            <img
                              src={LOGO_PATH || "/placeholder.svg"}
                              alt="logo"
                              className="w-[40%] lg:w-auto max-w-[150px]"
                            />
                          </div>
                        </div>

                        <div className="flex flex-col lg:flex-row lg:items-center mt-4 lg:mt-0 lg:ml-6 space-y-2 lg:space-y-0 lg:space-x-4">
                          <span className="text-therapy-blue-dark text-center lg:text-left">
                            {authStore.user?.calendars?.find((cal) => cal.type === "google")?.email}
                          </span>
                          <div className="flex space-x-2">
                            <button
                              className="bg-[#4285F4] text-white hover:bg-[#4285F4]/90 px-3 py-1 rounded"
                              onClick={googleLogin}
                            >
                              Change
                            </button>
                            <button
                              className={`bg-red-500 text-white px-3 py-1 rounded ${
                                !hasBothCalendars ? "opacity-50 cursor-not-allowed" : ""
                              }`}
                              disabled={!hasBothCalendars}
                              onClick={() => {
                                if (!hasBothCalendars) return;
                                const googleCalendar =
                                  authStore.user && authStore.user.calendars?.find((cal) => cal.type === "google")
                                if (!googleCalendar) {
                                  notification.error("Calendar not found")
                                  return
                                }
                                setCalendar(googleCalendar)
                                removeCalendarState.open()
                              }}
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <button
                        onClick={googleLogin}
                        className="md:w-full lg:w-auto text-white bg-[#4285F4] hover:bg-[#4285F4]/90 focus:ring-4 focus:outline-none focus:ring-[#4285F4]/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center lg:justify-between dark:focus:ring-[#4285F4]/55 mr-2 mb-2 active:scale-95 transition duration-300"
                      >
                        <svg
                          className="mr-2 -ml-1 w-4 h-4"
                          aria-hidden="true"
                          focusable="false"
                          data-prefix="fab"
                          data-icon="google"
                          role="img"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 488 512"
                        >
                          <path
                            fill="currentColor"
                            d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"
                          ></path>
                        </svg>{" "}
                        Sync Google Calendar
                      </button>
                    )}
                  </div>

                  <div className="w-full max-w-[900px] mt-3">
                    {hasOutlookCalendar ? (
                      <div className="flex flex-col lg:flex-row lg:items-center pb-4 mb-4 w-full">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:w-[500px] xl:w-[350px] lg:border-r-2 lg:border-therapy-blue-dark text-therapy-blue-dark pb-4 lg:pb-0">
                          <div className="flex flex-col items-center lg:flex-row lg:space-x-8 space-y-3 lg:space-y-0">
                            {/* Microsoft Icon */}
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-16 h-16 mb-3 md:mb-0" viewBox="0 0 48 48">
                              <path
                                fill="#1976d2"
                                d="M28,13h14.533C43.343,13,44,13.657,44,14.467v19.066C44,34.343,43.343,35,42.533,35H28V13z"
                              ></path>
                              <rect width="14" height="15.542" x="28" y="17.958" fill="#fff"></rect>
                              <polygon fill="#1976d2" points="27,44 4,39.5 4,8.5 27,4"></polygon>
                              <path
                                fill="#fff"
                                d="M15.25,16.5c-3.176,0-5.75,3.358-5.75,7.5s2.574,7.5,5.75,7.5S21,28.142,21,24	S18.426,16.5,15.25,16.5z M15,28.5c-1.657,0-3-2.015-3-4.5s1.343-4.5,3-4.5s3,2.015,3,4.5S16.657,28.5,15,28.5z"
                              ></path>
                              <rect width="2.7" height="2.9" x="28.047" y="29.737" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="31.448" y="29.737" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="34.849" y="29.737" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="28.047" y="26.159" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="31.448" y="26.159" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="34.849" y="26.159" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="38.25" y="26.159" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="28.047" y="22.706" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="31.448" y="22.706" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="34.849" y="22.706" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="38.25" y="22.706" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="31.448" y="19.112" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="34.849" y="19.112" fill="#1976d2"></rect>
                              <rect width="2.7" height="2.9" x="38.25" y="19.112" fill="#1976d2"></rect>
                            </svg>
                            {/* Sync Icon */}
                            <i className="fa-solid fa-arrows-rotate text-3xl"></i>

                            {/* NextTherapist Logo */}
                            <img
                              src={LOGO_PATH}
                              alt="logo"
                              className="w-[40%] lg:w-auto max-w-[150px]"
                            />
                          </div>
                        </div>

                        <div className="flex flex-col lg:flex-row lg:items-center mt-4 lg:mt-0 lg:ml-6 space-y-2 lg:space-y-0 lg:space-x-4">
                          <span className="text-therapy-blue-dark text-center lg:text-left">
                            {authStore.user?.calendars?.find((cal) => cal.type === "outlook")?.email}
                          </span>
                          <div className="flex space-x-2">
                            <button
                              className="bg-[#4285F4] text-white hover:bg-[#4285F4]/90 px-3 py-1 rounded"
                              onClick={loginToOutlook}
                            >
                              Change
                            </button>
                            <button
                              className={`bg-red-500 text-white px-3 py-1 rounded ${
                                !hasBothCalendars ? "opacity-50 cursor-not-allowed" : ""
                              }`}
                              disabled={!hasBothCalendars}
                              onClick={() => {
                                if (!hasBothCalendars) return;
                                const outlookCalendar =
                                  authStore.user && authStore.user.calendars?.find((cal) => cal.type === "outlook")
                                if (!outlookCalendar) {
                                  notification.error("Calendar not found")
                                  return
                                }
                                setCalendar(outlookCalendar)
                                removeCalendarState.open()
                              }}
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <button
                        onClick={loginToOutlook}
                        className="md:w-full lg:w-auto text-white bg-[#4285F4] hover:bg-[#4285F4]/90 focus:ring-4 focus:outline-none focus:ring-[#4285F4]/50 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center lg:justify-between dark:focus:ring-[#4285F4]/55 mr-2 mb-2 active:scale-95 transition duration-300"
                      >
                        <svg
                          className="mr-2 -ml-1 w-4 h-4"
                          aria-hidden="true"
                          focusable="false"
                          data-prefix="fab"
                          data-icon="microsoft"
                          role="img"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 448 512"
                        >
                          <path
                            fill="currentColor"
                            d="M0 224V32h224v192H0zm256-192h192v192H256V32zM0 256h224v224H0V256zm256 224V256h192v224H256z"
                          ></path>
                        </svg>
                        Sync Microsoft Calendar
                      </button>
                    )}
                  </div>

              {calendar && (
                <NormalDialog
                  state={removeCalendarState}
                  title={`Remove ${calendar.type === "google" ? "Google" : "Outlook"} Calendar`}
                  confirmLabel={"Confirm"}
                  verifyText={"CONFIRM"}
                  loading={isLoading}
                  onCancel={() => {
                    removeCalendarState.close()
                    setCalendar(null)
                  }}
                  onAccept={async () => {
                    if (!authStore.user?.userToken) {
                      notification.error("User Token is required")
                      return
                    }
                    setIsLoading(true)
                    const res =
                      calendar.type === "google"
                        ? await dispatch(removeGoogleCalendarAction(authStore.user.userToken))
                        : await dispatch(removeOutlookCalendarAction(authStore.user.userToken))
                    if (res && res.status === 200) {
                      dispatch(setUser(res.data.userData))
                      removeCalendarState.close()
                      setCalendar(null)
                    }
                    setIsLoading(false)
                  }}
                  primaryButtonColor={true}
                >
                  <p className="text-sm">
                    Are you sure you want to <strong>remove</strong> this{" "}
                    {calendar.type === "google" ? "Google" : "Outlook"} Calendar? <strong>{calendar.email}</strong>
                  </p>
                </NormalDialog>
              )}
            </div>
          </div>

          {/* Waitlist Section */}
          <div className="mt-8">
            <Formik
              enableReinitialize
              initialValues={{
                booking_time_hour: waitlistInfo?.booking_time_hour || "",
                notification_preferences: waitlistInfo?.notification_preferences || [],
                phone_number: waitlistInfo?.phone_number || "",
                week_visibility: waitlistInfo?.week_visibility || "2", // Default to 2 weeks
              }}
              validateOnMount={true}
              validate={(values) => {
                const errors = {} as any
                if (values.notification_preferences.includes("text")) {
                  if (!values.phone_number) errors.phone_number = "Phone number is required"
                  else if (!isPossiblePhoneNumber(values.phone_number))
                    errors.phone_number = "Input a valid phone number"
                }

                    if (!values.week_visibility) errors.week_visibility = "Week visibility is required"

                    return errors
                  }}
                  onSubmit={async (values) => {
                    if (!authStore?.user?.userToken) {
                      notification.error("User Token is required")
                      return
                    }

                setIsLoading(true)
                const res = await dispatch(
                  registerTherapistAction(values, "waitlist-notifications", authStore.user.userToken),
                )
                if (res && (res.status === 200 || res.status === 201)) fetchWaitlistInfo()
                setIsLoading(false)
              }}
            >
              {({ values, errors, handleChange, isSubmitting, handleSubmit }) => {
                return (
                  <form onSubmit={handleSubmit}>
                  

                    {/* Waitlist Section */}
                    <div className="mt-8">
                      <h1 className="text-2xl font-bold text-therapy-blue-dark">Waitlist</h1>
                      <div className="mt-4">
                        <h1 className="text-2xl font-medium text-therapy-blue-dark">
                        Advance Notice
                        </h1>

                        <div className="mt-1">
                          <p className="mb-4 text-sm text-gray-500">
                            <span className="text-danger">*</span>Require at least [X] hours' notice to book an appointment
                          </p>
                          <div className="w-full sm:w-60">
                            <select
                              name="booking_time_hour"
                              value={values.booking_time_hour || "1"} // Default value set to "1"
                              onChange={handleChange}
                              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-therapy-blue-light"
                            >
                              {sessionScheduled.map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                            <div className="text-red-500 text-xs mt-2">
                              {errors.booking_time_hour && errors.booking_time_hour.toString()}
                            </div>
                          </div>

                              <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                                {errors.booking_time_hour && errors.booking_time_hour.toString()}
                              </div>
                            </div>
                          </div>
                        </div>

                    {/* Calendar Visibility */}
                    <div className="mb-10">
                      <div className="mt-4">
                        <h1 className="text-2xl font-medium text-therapy-blue-dark">
                          Calendar Visibility
                        </h1>
                        <p className="text-sm text-gray-500 mt-1 mb-4">
                        <span className="text-danger">*</span>Choose how many weeks into the future your availability will be visible to patients.
                        </p>

                            <div className="w-60">
                              <select
                                name="week_visibility"
                                value={values.week_visibility}
                                onChange={handleChange}
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-therapy-blue-light"
                              >
                                <option value="1">1 week</option>
                                <option value="2">2 weeks</option>
                                <option value="3">3 weeks</option>
                                <option value="4">4 weeks</option>
                              </select>
                              <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                                {errors.week_visibility && errors.week_visibility.toString()}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Single Save Button at the bottom */}
                        <div className="mt-8 mb-10 flex justify-start pl-2">
                          <SaveButton loading={isSubmitting} value="Save" />
                        </div>
                      </form>
                    )
                  }}
                </Formik>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default Page;

