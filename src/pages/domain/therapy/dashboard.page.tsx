import { FC } from "react";
import { useInView } from "react-intersection-observer";
import { <PERSON> } from "react-router-dom";
import { LOGO_PATH } from "../../../../src/pages/auth/constants";

const Dashboard: FC = () => {
  const { ref } = useInView();
  return (
    <div ref={ref} className="flex-grow h-full w-full overflow-x-auto p-4 md:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto flex flex-col items-center">
        <div className="w-full flex justify-center mt-6 md:mt-8 lg:mt-10 mb-6 md:mb-8">
          <img
            src={LOGO_PATH}
            alt="logo"
            className="w-[60%] max-w-[250px] md:max-w-[250px]"
          />
        </div>

        <div className="w-full grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 xl:grid-cols-4 gap-6 md:gap-8">
          {[
            { to: "/personal-details", icon: "fa-id-card", label: "Contact Info" },
            { to: "/profile", icon: "fa-user", label: "Your Profile" },
            { to: "/calendars-appointments", icon: "fa-calendar-day", label: "Calendar & Appointments" },
            { to: "/subscriptions-payments", icon: "fa-money-check-dollar", label: "Subscriptions & Payment" },
            { to: "/reporting", icon: "fa-file-invoice", label: "Reporting" },
            { to: "/security", icon: "fa-key", label: "Password & Security" },
            { to: "/notifications", icon: "fa-bell", label: "Notifications" },
            { to: "/help-center", icon: "fa-circle-info", label: "Help Center" },
          ].map(({ to, icon, label }) => (
            <Link
              key={to}
              to={to}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border-2 px-6 py-8 hover:bg-[rgba(84,113,145,0.06)] min-w-[200px] min-h-[180px] flex items-center justify-center"
              style={{
                borderRadius: "5px",
                boxShadow: "0 3px 3px rgba(0, 0, 0, 0.2)",
                borderColor: "#431d42",
              }}
            >
              <div className="flex flex-col items-center justify-center w-full h-full">
                <i
                  className={`fa-solid ${icon} text-[50px] leading-none`}
                  style={{ color: "#431d42" }}
                ></i>
                <h4
                  className="font-medium mt-4 px-2 text-[20px] leading-[1.2] text-center w-full break-words"
                  style={{ color: "#431d42" }}
                >
                  {label}
                </h4>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;


