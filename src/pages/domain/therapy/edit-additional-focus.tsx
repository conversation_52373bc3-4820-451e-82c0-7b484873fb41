import AppButton from "@/components/buttons/AppButton";
import AppCheckBoxComponent from "@/components/forms/AppCheckBoxListComponent";
import InputComponent from "@/components/forms/InputComponent";
import ToolbarComponent from "@/components/ToolbarComponent";
import { areaOfFocus } from "@/configs/registration.configs";
import { AppDispatch, RootState } from "@/store/index";
import { updateRegistrationForm } from "@/store/slicers/auth.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import notification from "@/utils/notification.util";
import { FC } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

const EditAdditionalFocus: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const pageContent = authStore.registration.formContent[pageId!] || {};
  const { userRegistrationToken } = authStore.registration;
  
  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-16 py-8">
        <h1 className="text-3xl font-medium text-therapy-blue">Additional Areas of Focus:</h1>
        <div className="mt-8">
          <form>
            <AppCheckBoxComponent
              checked={pageContent.checked_lists}
              onChange={(val) => {
                dispatch(
                  updateRegistrationForm({
                    pageId,
                    values: { checked_lists: [...val], other_focus: pageContent.other_focus || "" },
                  }),
                );
              }}
              checkList={areaOfFocus}
            />

            {pageContent && pageContent.checked_lists && pageContent.checked_lists.includes("others") && (
              <div className="w-[300px] pl-5">
                <InputComponent
                  value={pageContent.other_focus}
                  errorMessage={
                    pageContent &&
                    pageContent.checked_lists &&
                    pageContent.checked_lists.includes("others") &&
                    !pageContent.other_focus
                      ? "Specify other area of focus"
                      : ""
                  }
                  onChange={(val) => {
                    const checked = pageContent.checked_lists;
                    dispatch(
                      updateRegistrationForm({ pageId, values: { checked_lists: [...checked], other_focus: val } }),
                    );
                  }}
                  class="mb-5"
                />
              </div>
            )}

            <br />
            <div className="mt-5">
              <AppButton
                disabled={
                  (pageContent && Object.keys(pageContent).length < 1) ||
                  (pageContent && pageContent.checked_lists && pageContent.checked_lists.length < 1) ||
                  (pageContent &&
                    pageContent.checked_lists &&
                    pageContent.checked_lists.includes("others") &&
                    pageContent.other_focus === "")
                }
                onClick={async (e) => {
                  e.preventDefault();
                  if (!userRegistrationToken) {
                    notification.error("User Token is required");
                    return;
                  }
                  const res = await dispatch(registerTherapistAction(pageContent, pageId!, userRegistrationToken));
                  if (res && res.status === 201) navigate("/auth/register/modalities");
                }}
                value="Save"
              />
            </div>

            <br />
          </form>
        </div>
      </div>
    </>
  );
};

export default EditAdditionalFocus;
