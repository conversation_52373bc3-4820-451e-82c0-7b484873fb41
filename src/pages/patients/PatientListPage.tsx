import FormDialog from '@/components/dialogs/FormDialog'
import NormalDialog from "@/components/dialogs/NormalDialog";
import InputComponent from '@/components/forms/InputComponent'
import PaginatedDataContainer from '@/components/PaginatedDataContainer'
import PaginationAwareContainer from '@/components/PaginationAwareContainer'
import VerificationDateComponent from '@/components/VerificationDateComponent'
import PatientRepository from '@/repositories/PatientRepository'
import UserRepository from '@/repositories/UserRepository'
import Meta from '@/types/meta.interface'
import { User } from '@/types/user.interface'
import { getFullName } from '@/utils/app.util'
import { useInfiniteQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { FC, FormEvent, useMemo, useState } from 'react'
import { useInView } from 'react-intersection-observer'
import { useOverlayTriggerState } from 'react-stately'
import { debounce } from 'lodash'
import PatientDeleteDialog from '@/components/dialogs/PatientDeleteDialog'
import PatientDeactivateDialog from '@/components/dialogs/PatientDeactivateDialog'
import PatientListActionButtons from './PatientListActionButtons';

interface IApplicationProps {}

const PatientListPage: FC<IApplicationProps> = () => {
	const patientRepo = new PatientRepository()
	const userRepo = new UserRepository()

	const { ref } = useInView()

	const [params, setParams] = useState({})

	const deactivateState = useOverlayTriggerState({})
	const resetPasswordState = useOverlayTriggerState({})
	const deleteState = useOverlayTriggerState({})
	const restoreState = useOverlayTriggerState({})

	const [patient, setPatient] = useState<User>()
	const [enteredEmail, setEnteredEmail] = useState<string>('')
	const [errorMessage, setErrorMessage] = useState<string | null>(null)
	const [loading, setLoading] = useState(false)
	const [modalType, setModalType] = useState<'deactivate' | 'reactivate' | null>(null);

	const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
		queryKey: ['patients', params],
		queryFn: ({ pageParam = 1 }) =>
			patientRepo.getAll({
				perPage: 20,
				page: pageParam,
				...params,
			}),
		initialPageParam: 0,
		getNextPageParam: (lastPage) => lastPage.meta.nextPage,
		refetchOnWindowFocus: false,
	})

	const meta = useMemo(
		() =>
			data?.pages?.length
				? data.pages[data.pages.length - 1].meta
				: ({} as Meta),
		[data]
	)

	const debouncedSearch = debounce((value: string) => {
		setParams({ ...params, search: value })
	}, 500)

	const handleResetPasswordSubmit = async (e: FormEvent<HTMLFormElement>) => {
		e.preventDefault()

		if (!patient || enteredEmail !== patient.email) {
			setErrorMessage("Entered email does not match the patient's email")
			return
		}

		setLoading(true)
		const res = await userRepo.resetPassword(
			'/auth/forgot-password',
			enteredEmail
		)
		setLoading(false)

		if (res) {
			setEnteredEmail('')
			resetPasswordState.close()
		}
	}

	const handleRestorePatient = async () => {
		if (!patient?.id) return;
		setLoading(true);
		try {
			const res = await patientRepo.restore(patient.id);
			if (res) {
				setPatient(undefined);
				refetch();
				restoreState.close();
			}
		} finally {
			setLoading(false);
		}
	};

	const handleReactivatePatient = async () => {
		if (!patient?.id) return;
		setLoading(true);
		try {
			const res = await userRepo.activate(`/users/activate/${patient.id}`);
			if (res) {
				setPatient(undefined);
				refetch();
				deactivateState.close();
			}
		} finally {
			setLoading(false);
		}
	};

	const getUserRowClasses = (user: User): string => {
		if (user.deactivatedAt || user.removedAt) {
			return 'opacity-50 text-gray-500';
		}
		return '';
	};

	return (
		<PaginationAwareContainer
			meta={meta}
			onLoad={fetchNextPage}
			fetching={isLoading}
		>
			<div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
				<div className="flex-none flex flex-row justify-between items-center mb-4">
					<div className="font-thin text-3xl">Patient List</div>
					<div></div>
				</div>
				<div className="flex-none grid grid-cols-12">
					<div className="col-span-3">
						<InputComponent
							id="search"
							placeholder="Search..."
							aria-labelledby="search"
							onChange={debouncedSearch}
						/>
					</div>
				</div>

				<NormalDialog
					state={restoreState}
					title="Restore Patient Account"
					confirmLabel="Restore"
					verifyText="RESTORE"
					loading={loading}
					onCancel={() => {
						setPatient(undefined)
						restoreState.close()
					}}
					onAccept={handleRestorePatient}
				>
					<p className="text-sm">
						Are you sure you want to restore this account? The patient will be able to log in again.
					</p>
				</NormalDialog>

				{modalType === 'reactivate' && (
					<NormalDialog
						state={deactivateState}
						title="Reactivate Patient Account"
						confirmLabel="Reactivate"
						verifyText="REACTIVATE"
						loading={loading}
						onCancel={() => {
							setPatient(undefined)
							deactivateState.close()
							setModalType(null)
						}}
						onAccept={handleReactivatePatient}
					>
						<p className="text-sm">
							Are you sure you want to reactivate this account? The patient will be able to log in again.
						</p>
					</NormalDialog>
				)}
				
				{modalType === 'deactivate' && (
					<PatientDeactivateDialog
						state={deactivateState}
						title={'Deactivate Patient Account'}
						url={patient?.id ? `/patients/deactivate/${patient.id}` : ''}
						refetch={refetch}
						setGlobalLoading={setLoading}
					>
						<p className="text-sm">
							Are you sure you want to deactivate the account of&nbsp;
							<strong>
								{patient ? getFullName(patient.firstname, patient.lastname) : ''}
							</strong>
							?
						</p>
					</PatientDeactivateDialog>
				)}

				<div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
					<span></span>
					<div className="col-span-2 md:col-span-3">Name</div>
					<div className="col-span-2 md:col-span-4">Email</div>
					<div className="col-span-2 md:col-span-2">Phone</div>
					<div className="col-span-2"></div>
				</div>
				<div className="flex-grow">
					{error ? (
						<span>Error: {JSON.stringify(error)}</span>
					) : (
						<PaginatedDataContainer meta={meta}>
							<>
								{data?.pages
									.flatMap((page) => page.data)
									.map((user: User, index: number) => (
										<motion.div
											initial={{ opacity: 0 }}
											animate={{ opacity: 1 }}
											exit={{ opacity: 0 }}
											className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
											key={index}
										>
											{user && (
												<>
													<div className="flex items-center">
														<div>
															<div
																className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center bg-gray-100`}
															>
																{index + 1}
															</div>
														</div>
													</div>
													<div
														className={`col-span-3 flex items-center ${getUserRowClasses(user)}`}
													>
														<strong>
															{getFullName(user.firstname, user.lastname)}
														</strong>
														{user.deactivatedAt && (
															<span className="text-red-500 ml-2">
																(Deactivated)
															</span>
														)}
														{user.removedAt && (
															<span className="text-red-500 ml-2">
																(Deleted)
															</span>
														)}
													</div>
													<div
														className={`col-span-4 flex items-center ${getUserRowClasses(user)}`}
													>
														<span className="text-sm">{user.email}</span>
														{VerificationDateComponent(user.emailVerifiedAt)}
													</div>

													<PatientListPhoneColumn cUser={user} className={getUserRowClasses(user)} />

													<div className="col-span-2 flex flex-row items-center justify-end space-x-4 text-white">
														<PatientListActionButtons
															user={user}
															setPatient={setPatient}
															restoreState={restoreState}
															deactivateState={deactivateState}
															resetPasswordState={resetPasswordState}
															deleteState={deleteState}
															setModalType={setModalType}
															setEnteredEmail={setEnteredEmail}
														/>
													</div>
												</>
											)}
										</motion.div>
									))}
							</>
						</PaginatedDataContainer>
					)}
				</div>
				<FormDialog
					state={resetPasswordState}
					title="Reset Password"
					confirmLabel="Reset"
					loading={loading}
					onCancel={() => {
						setPatient(undefined)
						setEnteredEmail('')
						setErrorMessage(null)
						resetPasswordState.close()
					}}
					onSubmit={handleResetPasswordSubmit}
				>
					<p className="text-sm">
						Please enter the email address associated with{' '}
						<strong>
							{patient ? getFullName(patient.firstname, patient.lastname) : ''}
						</strong>
						's account to reset the password. <br /> <br />{' '}
						<strong>{patient?.email}</strong>
					</p>
					<br />
					<InputComponent
						label="Email"
						id="email"
						//type="email"
						value={enteredEmail}
						onChange={(value) => {
							setEnteredEmail(value)
							setErrorMessage(null)
						}}
						isRequired
					/>
					{errorMessage && (
						<p className="text-red-500 text-sm">{errorMessage}</p>
					)}
				</FormDialog>
				<PatientDeleteDialog
					state={deleteState}
					title={'Delete Patient'}
					url={`/patients/reason/${patient?.id}`}
					refetch={refetch}
				>
					<p className="text-sm mb-1">
						Are you sure you want to delete this user?
					</p>
				</PatientDeleteDialog>
			</div>
		</PaginationAwareContainer>
	)
}

export default PatientListPage

const PatientListPhoneColumn: FC<{ cUser: User, className?: string }> = ({ cUser, className = '' }) => {
	return (
    <div className={`col-span-2 flex items-center ${className}`}>
      {cUser.phone ? (
        <>
          <p className="text-sm">{cUser.phone}</p>
          {VerificationDateComponent(cUser.phoneVerifiedAt)}
        </>
      ) : (
        <small className="text-red-500">Phone number not available</small>
      )}
    </div>
  )
}
