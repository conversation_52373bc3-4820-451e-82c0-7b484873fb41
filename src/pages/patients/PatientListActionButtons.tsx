import ActionButton from "@/components/buttons/ActionButton"
import { User } from "@/types/user.interface"
import { FC } from "react"
import { OverlayTriggerState } from "react-stately"

interface IPatientListActionButtonsProps {
	user: User,
	setPatient: (user: User) => void,
	restoreState: OverlayTriggerState,
	deactivateState: OverlayTriggerState,
	resetPasswordState: OverlayTriggerState,
	deleteState: OverlayTriggerState,
	setModalType: (type: 'deactivate' | 'reactivate' | null) => void,
	setEnteredEmail: (email: string) => void,
}

const PatientListActionButtons: FC<IPatientListActionButtonsProps> = ({
	user,
	setPatient,
	restoreState,
	deactivateState,
	resetPasswordState,
	deleteState,
	setModalType,
	setEnteredEmail,
}) => {
	const getActionButtonProps = () => {
		if (user?.removedAt) {
			// Only show restore button for deleted accounts
			return {
				onRestore: () => {
					setPatient(user)
					restoreState.open()
				},
			}
		} else if (user?.deactivatedAt) {
			// Show reactivate and delete buttons for deactivated accounts
			return {
				onActivate: () => {
					setPatient(user)
					setModalType('reactivate')
					deactivateState.open()
				},
				onDelete: () => {
					setPatient(user)
					deleteState.open()
				},
			}
		} else {
			// Show deactivate, delete and reset password for active accounts
			return {
				onDeactivate: () => {
					setPatient(user)
					setModalType('deactivate')
					deactivateState.open()
				},
				onDelete: () => {
					setPatient(user)
					deleteState.open()
				},
				onResetPassword: () => {
					setPatient(user)
					resetPasswordState.open()
					setEnteredEmail('')
				},
				isResetPasswordDisabled: !user?.acceptedAt,
			}
		}
	}

	return (
		<ActionButton {...getActionButtonProps()} />
	)
}

export default PatientListActionButtons;
