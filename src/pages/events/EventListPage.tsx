import AddButton from "@/components/buttons/AddButton";
import PaginatedDataContainer from "@/components/PaginatedDataContainer";
import { Event } from "@/types/event.interface";
import { useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { FC, useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { useOverlayTriggerState } from "react-stately";
import EventForm from "./EventForm";
import EditButton from "@/components/buttons/EditButton";
import DeleteButton from "@/components/buttons/DeleteButton";
import DeleteDialog from "@/components/dialogs/DeleteDialog";
import EventRepository from "@/repositories/EventRepository";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import Meta from "@/types/meta.interface";

interface IApplicationProps {}

const EventListPage: FC<IApplicationProps> = () => {
  const repo = new EventRepository();
  const { ref } = useInView();
  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});

  const [params,] = useState({});
  const [event, setEvent] = useState<Event>();

  const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
    queryKey: ["events", params],
    queryFn: async ({ pageParam = 1 }) => {
     return repo.getAll({
        perPage: 20,
        page: pageParam,
        ...params,
      });
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  useEffect(() => {
    if (!state.isOpen) {
      setEvent(undefined);
    }
  }, [state.isOpen]);

  useEffect(() => {
    if (!deleteState.isOpen && event) {
      setEvent(undefined);
      refetch();
    }
  }, [deleteState.isOpen]);

  return (
    <PaginationAwareContainer
      meta={meta}
      onLoad={fetchNextPage}
      fetching={isLoading}
    >
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">Event List</div>
          <div>
            <AddButton label="Add Event" onPress={state.open} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <span></span>
          <div className="col-span-2">Name</div>
          <div className="col-span-7">Description</div>
          <div className="col-span-2 hidden md:block"></div>
        </div>
        <div className="flex-grow">
          {error ? (
            <span>Error: {JSON.stringify(error)}</span>
          ) : (
            <PaginatedDataContainer meta={meta}>
              <>
                {data?.pages
                  .flatMap((page) => page.data)
                  .map((item: Event, index: number) => (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      {item && (
                        <>
                          <div className="flex items-center">
                            <div
                              className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center bg-gray-100`}
                            >
                              {index + 1}
                            </div>
                          </div>
                          <div className="col-span-2 flex items-center">
                            <strong>{item.name}</strong>
                          </div>
                          <div className="col-span-7 flex items-center">
                            {item.description}
                          </div>
                          <div className="col-span-2 hidden md:flex flex-row items-center justify-end space-x-2 text-white">
                            <EditButton
                              label="Edit"
                              onPress={() => {
                                setEvent(event);
                                state.open();
                              }}
                            />
                            <DeleteButton
                              label="Delete"
                              onPress={() => {
                                setEvent(event);
                                deleteState.open();
                              }}
                            />
                          </div>
                        </>
                      )}
                    </motion.div>
                  ))}
              </>
            </PaginatedDataContainer>
          )}
        </div>
        <EventForm state={state} event={event} repo={repo} refetch={refetch} />
        <DeleteDialog
          state={deleteState}
          verifyText={event?.name}
          title={"Delete Event"}
          url={`/events/${event?.id}`}
        >
          <p className="text-sm">
            Are you sure you want to delete this event? This process is not
            reversible.
          </p>
        </DeleteDialog>
      </div>
    </PaginationAwareContainer>
  );
};

export default EventListPage;
