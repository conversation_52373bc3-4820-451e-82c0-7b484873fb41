import FormDialog from "@/components/dialogs/FormDialog";
import CheckBoxComponent from "@/components/forms/CheckBoxComponent";
import DateTimeComponent from "@/components/forms/DateTimeComponent";
import InputComponent from "@/components/forms/InputComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import EventRepository from "@/repositories/EventRepository";
import { Event } from "@/types/event.interface";
import { FormEvent, useEffect, useState } from "react";
import { OverlayTriggerState } from "react-stately";

type EventFormProps = {
  state: OverlayTriggerState;
  event?: Event;
  repo: EventRepository;
  refetch?: () => void;
};

type EventFormData = {
  //tagId?: string | number;
  name?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  isAllDay?: boolean;
  isRecurring?: boolean;
  recurringType?: string;
  location?: string;
};

const EventForm = ({ state, event, repo, refetch }: EventFormProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<EventFormData>({
    isAllDay: false,
    isRecurring: false,
  } as EventFormData);

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    let result;
    setLoading(true);
    if (event?.id) {
      result = await repo.update(event.id, formData);
    } else {
      result = await repo.create(formData);
    }
    setLoading(false);
    if (result) {
      refetch && refetch();
      setFormData({
        isAllDay: false,
        isRecurring: false,
      } as EventFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({
      isAllDay: false,
      isRecurring: false,
    } as EventFormData);
    state.close();
  };

  useEffect(() => {
    if (event) {
      setFormData({
        name: event.name,
        description: event.description,
        startDate: event.startDate,
        endDate: event.endDate,
        //tagId: event.tagId,
        location: event.location,
        isAllDay: event.isAllDay,
        isRecurring: event.isRecurring,
      });
    }
  }, [event]);

  return (
    <FormDialog aria-label="Event Form"
      title="Event Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel="Save"
      loading={loading}
      state={state}
      width="w-[420px]"
    >
      <div className="flex flex-col space-y-4">
        <InputComponent
          label={"Name"}
          id="name"
          onChange={(value) => setFormData({ ...formData, name: value })}
          value={formData.name || ""}
          isRequired
        />
        <InputComponent
          label={"Location"}
          id="location"
          onChange={(value) => setFormData({ ...formData, location: value })}
          value={formData.location || ""}
        />
        <TextAreaComponent
          label={"Description"}
          id="description"
          onChange={(value) => setFormData({ ...formData, description: value })}
          value={formData.description || ""}
        />
        <div className="grid grid-cols-2 gap-4">
          <div>
            <DateTimeComponent
              label={"Start Date"}
              id="start-date"
              onChange={(value) =>
                setFormData({ ...formData, startDate: value })
              }
              value={formData.startDate || ""}
              isRequired
            />
          </div>
          <div>
            <DateTimeComponent
              label={"End Date"}
              id="end-date"
              onChange={(value) => setFormData({ ...formData, endDate: value })}
              value={formData.endDate || ""}
              isRequired
            />
          </div>
        </div>
        <div className="grid grid-cols-3 gap-4">
          <CheckBoxComponent
            onChange={(value) => setFormData({ ...formData, isAllDay: value })}
            label={"Is all day?"}
            checked={formData.isAllDay || false}
          />
          <CheckBoxComponent
            onChange={(value) =>
              setFormData({ ...formData, isRecurring: value })
            }
            label={"Is recurring?"}
            checked={formData.isRecurring || false}
          />
        </div>
      </div>
    </FormDialog>
  );
};

export default EventForm;
