import { FC } from "react";
import { useInView } from "react-intersection-observer";
import CalendarExample from "@/components/calendar/CalendarExample";

const DashboardPage: FC = () => {
  const { ref } = useInView();

  return (
    <div className="px-16 h-full flex flex-col flex-grow" ref={ref} data-testid="dashboard-container">
      <div className="flex-grow">
        {/* <GoogleButton /> */}
        <CalendarExample />
        {/* <FullCalendar days={days} calendar={calendar} /> */}
      </div>
    </div>
  );
};

export default DashboardPage;
