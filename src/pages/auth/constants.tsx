export const EMAIL_REQUIRED_ERROR = "Email address is required.";
export const ATLEAST_8_CHARACTERS_ERROR = "at least 8 characters long";
export const ATLEAST_ONE_UPPERCASE_ERROR = "at least one uppercase letter";
export const ATLEAST_ONE_NUMBER_ERROR = "at least one number";
export const PASSWORD_DO_NOT_MATCH_ERROR = "Passwords do not match.";
export const ATLEAST_ONE_SPECIAL_CHARACTER_ERROR = "at least one special character";
export const CANT_START_WITH_ZERO = "NPI number cannot start with zero";
export const NPI_LENGTH_ERROR = "NPI number is invalid. Must be 10 digits";
export const NPI_NOT_ALL_ZEROS = "NPI number cannot contain all zeros";
export const INACTIVITY_STATUS = "⚠️ You will be logged out in 1 minute due to inactivity."
export const LOGO_PATH ="/logo/next_therapist.png";
export const YEAR = "2025";
export const PASSWORDS_DONT_MATCH_ERROR = "Passwords do not match";
export const US_TIMEZONES = [
    {
      label: "Hawaii-Aleutian Standard Time",
      value: "Pacific/Honolulu",
    },
    {
      label: "Aleutian Standard Time",
      value: "America/Adak",
    },
    {
      label: "Alaska Standard Time",
      value: "America/Anchorage",
    },
    {
      label: "Pacific Standard Time",
      value: "America/Los_Angeles",
    },
    {
      label: "Mountain Standard Time",
      value: "America/Denver",
    },
    {
      label: "Mountain Time (Arizona)",
      value: "America/Phoenix",
    },
    {
      label: "Central Standard Time",
      value: "America/Chicago",
    },
    {
      label: "Eastern Standard Time",
      value: "America/New_York",
    },
    {
      label: "Atlantic Standard Time (Puerto Rico, U.S. Virgin Islands)",
      value: "America/Puerto_Rico",
    },
    {
      label: "Samoa Standard Time (American Samoa)",
      value: "Pacific/Pago_Pago",
    },
    {
      label: "Chamorro Standard Time (Guam, Northern Mariana Islands)",
      value: "Pacific/Guam",
    },
  ]
export const UNDER_REVIEW = ' This process may take a few business days. You will receive an email notification once a decision has been made, whether your application is accepted or declined.'
export const ASSISTANCE = 'If you need any assistance in the meantime, feel free to contact us at'
export const EMAIL_SUPPORT = '<EMAIL>'
export const TRY_AGAIN = 'Try again.'
export const INCOMPLETE_REGISTRATION = 'We detected an incomplete registration associated with your email address from your last visit.'
export const CLICK = 'Click';
export const CONTINUE = 'Continue';
export const RESUME = ' to resume your registration where you left off, or click';
export const CANCEL = 'Cancel';
export const START = ' to start a new registration with a different account.';
export const VERIFICATION_EMAIL_SENT = 'An email will be sent to provided email account for verification.'
export const ACCOUNT_DELETED = 'Your account with this email has been deleted. If you want to restore it then click on continue.'
export const VERIFICATION_CODE = 'A verification code has been sent to your email address'
export const VERIFICATION_CODE_INPUT = 'Please input the code to verify.'
export const VERIFICATION_CODE_DIDNT_RECEIVED = 'Didn\'t receive verification code?'
export const ACCOUNT_DELETED_ADMIN = 'Your account has been deleted by an administrator.'
export const CONTACT_US = 'For more information, please contact us at'
export const EMAIL_SUPPORT_ADMIN = '<EMAIL>'
export const DELETED_AT = 'User Deleted At:'
export const DELETED_BY = 'Deleted By:'
export const RESTORE_SUCCESS_MESSAGE = 'User account restored successfully'
export const RESTORE_ERROR_MESSAGE = 'Account restoration failed'
export const LOGIN_ERROR = 'Login failed. Please try again.'
export const DELETE_MESSAGE = 'Are you sure you want to delete this therapist profile? Account will be deactivated immediately but can be restored within 30 days. After 30 days, this therapist profile and all associated data will be permanently deleted and cannot be recovered.'
export const ACCEPT_PRIVACY_POLICY = "You must accept the Privacy Policy to continue"
export const ACCEPT_TERMS_OF_SERVICE = "Please ensure that all required fields are completed before submitting your application.Incomplete sections are indicated with a red dot on the left navigation panel."
export const ACCEPT_TERMS_OF_SERVICE_ERROR = "You must accept the Terms of Service to continue";
export const PATIENT_DEACTIVATION_SUCCESS = "Patient deactivated successfully";
export const PATIENT_DELETION_SUCCESS = "Patient deleted successfully";