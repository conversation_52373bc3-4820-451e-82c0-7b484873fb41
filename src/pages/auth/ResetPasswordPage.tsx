import { FC, useEffect, useRef, useState } from "react";
import { useApiClient } from "@/utils/api.util";
import { useNavigate } from "react-router-dom";
import { HttpStatusCode } from "axios";
import { AnimatePresence, motion } from "framer-motion";
import { 
  ATLEAST_8_CHARACTERS_ERROR, 
  ATLEAST_ONE_UPPERCASE_ERROR, 
  ATLEAST_ONE_NUMBER_ERROR, 
  PASSWORD_DO_NOT_MATCH_ERROR, 
  ATLEAST_ONE_SPECIAL_CHARACTER_ERROR 
} from './constants';
import { LOGO_PATH, YEAR } from "../../../src/pages/auth/constants";

// Separate types to enhance readability
type CredentialState = {
  password: string;
  confirmPassword: string;
};

type ValidationResult = {
  isValid: boolean;
  errorMessage: string;
};

const ResetPasswordPage: FC = () => {
  const passwordRef = useRef<HTMLInputElement>(null);
  const [processing, setProcessing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [showLoginOption, setShowLoginOption] = useState<boolean>(true);
  const navigate = useNavigate();
  const client = useApiClient();
  
  const [credential, setCredential] = useState<CredentialState>({
    password: "",
    confirmPassword: "",
  });
  
  const [passwordError, setPasswordError] = useState<string>("");

  useEffect(() => {
    passwordRef.current?.focus();
  }, []);

  // Extracted password validation to reduce complexity
  const validatePassword = (password: string): string => {
    const errors: string[] = [];
  
    if (password.length < 8) {
      errors.push(ATLEAST_8_CHARACTERS_ERROR);
    }
    if (!/[A-Z]/.test(password)) {
      errors.push(ATLEAST_ONE_UPPERCASE_ERROR);
    }
    if (!/\d/.test(password)) {
      errors.push(ATLEAST_ONE_NUMBER_ERROR);
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push(ATLEAST_ONE_SPECIAL_CHARACTER_ERROR);
    }
  
    return errors.length > 0
      ? `* Password must be ${errors.join(", ")}.`
      : "";
  };

  // Extracted password handling to reduce complexity
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setCredential(prev => ({ ...prev, password: newPassword }));
    setPasswordError(validatePassword(newPassword));
  };

  // Extracted confirmation password handling
  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCredential(prev => ({
      ...prev,
      confirmPassword: e.target.value,
    }));
  };

  // Extracted form validation logic
  const validateForm = (): ValidationResult => {
    const validationError = validatePassword(credential.password);
    
    if (validationError) {
      setPasswordError(validationError);
      return { isValid: false, errorMessage: validationError };
    }
    
    setPasswordError("");
    return { isValid: true, errorMessage: "" };
  };

  // Extracted API call to separate function
  const resetPassword = async () => {
    setProcessing(true);
    
    try {
      const token = new URLSearchParams(window.location.search).get("token");
      const response = await client.post(`/auth/reset-password`, {
        ...credential,
        token,
      });
      
      handleApiResponse(response);
    } catch (err: any) {
      setErrorMessage(err.response?.data?.message ?? "Something went wrong.");
    } finally {
      setProcessing(false);
    }
  };

  // Extracted API response handling
  const handleApiResponse = (res: any) => {
    if (res.status === HttpStatusCode.Created) {
      const successMsg = res?.data?.message ?? "Password has been updated successfully.";
      
      if (res.data.role === 'therapist') {
        handleTherapistSuccess(successMsg);
      } else if (res.data.role === 'patient') {
        handlePatientSuccess(successMsg);
      }
    } else {
      setErrorMessage("Something went wrong. Please try again.");
    }
  };

  // Further extracted therapist-specific handling
  const handleTherapistSuccess = (message: string) => {
    setSuccessMessage(message + " Redirecting to login page...");
    resetCredentials();
    setTimeout(() => {
      navigate("/auth/login");
    }, 2000);
  };

  // Further extracted patient-specific handling
  const handlePatientSuccess = (message: string) => {
    setSuccessMessage(message + " You can now login to your account from mobile.");
    resetCredentials();
    setShowLoginOption(false);
  };

  const resetCredentials = () => {
    setCredential({
      password: "",
      confirmPassword: "",
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSuccessMessage("");
    
    if (processing) return;

    const { isValid } = validateForm();
    if (!isValid) return;

    await resetPassword();
  };

  // Extracted to determine if submit button should be disabled
  const isSubmitDisabled = () => {
    return (
      processing ||
      !credential.password ||
      !credential.confirmPassword ||
      credential.password !== credential.confirmPassword ||
      !!passwordError
    );
  };

  return (
    <section className="h-full w-full">
      <div className="flex flex-col md:grid md:grid-cols-2 h-full w-full">
        <LogoSection />
        <FormSection 
          credential={credential}
          passwordRef={passwordRef}
          passwordError={passwordError}
          handlePasswordChange={handlePasswordChange}
          handleConfirmPasswordChange={handleConfirmPasswordChange}
          handleSubmit={handleSubmit}
          processing={processing}
          successMessage={successMessage}
          errorMessage={errorMessage}
          showLoginOption={showLoginOption}
          isSubmitDisabled={isSubmitDisabled()}
        />
      </div>
    </section>
  );
};

// Extracted components to reduce complexity

const LogoSection: FC = () => (
  <div className="text-sm bg-white h-[30%] md:h-full text-white flex items-center relative justify-center flex-col">
    <span className="bg-transparent text-blue-600 w-[80%] rounded-full flex items-center justify-center">
    <img src={LOGO_PATH} alt="Next Therapists" className="w-[50%] md:w-[30%]" />
    </span>
  </div>
);

type FormSectionProps = {
  credential: CredentialState;
  passwordRef: React.RefObject<HTMLInputElement>;
  passwordError: string;
  handlePasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleConfirmPasswordChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  processing: boolean;
  successMessage: string;
  errorMessage: string;
  showLoginOption: boolean;
  isSubmitDisabled: boolean;
};

const FormSection: FC<FormSectionProps> = ({
  credential,
  passwordRef,
  passwordError,
  handlePasswordChange,
  handleConfirmPasswordChange,
  handleSubmit,
  processing,
  successMessage,
  errorMessage,
  showLoginOption,
  isSubmitDisabled
}) => (
  <div className="flex-grow w-full relative flex flex-col items-start justify-center bg-orange-100 rounded-lg shadow" style={{background:'#e9d5ed'}}>
    <div className="px-[10%] md:px-[20%] w-full flex flex-col space-y-6">
      <div className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">
        Update your password.
      </div>
      <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
        <PasswordField 
          password={credential.password}
          passwordRef={passwordRef}
          passwordError={passwordError}
          handleChange={handlePasswordChange}
        />
        <ConfirmPasswordField 
          password={credential.password}
          confirmPassword={credential.confirmPassword}
          handleChange={handleConfirmPasswordChange}
        />
        <StatusMessages 
          successMessage={successMessage}
          errorMessage={errorMessage}
        />
        {showLoginOption && <LoginOptionSection />}
        <SubmitButton 
          isDisabled={isSubmitDisabled}
          processing={processing}
        />
      </form>
    </div>
    <small className="absolute bottom-[50px] right-[20%] md:bottom-[20px] sm:bottom-[10px]">&copy; NextTherapist, {YEAR}</small>
  </div>
);

type PasswordFieldProps = {
  password: string;
  passwordRef: React.RefObject<HTMLInputElement>;
  passwordError: string;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const PasswordField: FC<PasswordFieldProps> = ({ password, passwordRef, passwordError, handleChange }) => (
  <div>
    <label htmlFor="password" className="block mb-2 text-sm font-bold text-gray-900">
      Password
    </label>
    <input
      type="password"
      id="password"
      required
      ref={passwordRef}
      value={password}
      autoComplete="off"
      className={`bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5 ${
        passwordError && "border-red-500"
      }`}
      placeholder="your new password"
      onChange={handleChange}
    />
    {passwordError && <small className="text-red-800">{passwordError}</small>}
  </div>
);

type ConfirmPasswordFieldProps = {
  password: string;
  confirmPassword: string;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

const ConfirmPasswordField: FC<ConfirmPasswordFieldProps> = ({ password, confirmPassword, handleChange }) => (
  <div>
    <label htmlFor="confirm-password" className="block mb-2 text-sm font-bold text-gray-900">
      Confirm Password
    </label>
    <input
      type="password"
      id="confirm-password"
      required
      disabled={!password}
      autoComplete="off"
      className={`bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5 ${
        confirmPassword && password !== confirmPassword && "border-red-500"
      }`}
      value={confirmPassword}
      placeholder="confirm password"
      onChange={handleChange}
    />
    {confirmPassword && password !== confirmPassword && (
      <small className="text-red-800">{PASSWORD_DO_NOT_MATCH_ERROR}</small>
    )}
  </div>
);

type StatusMessagesProps = {
  successMessage: string;
  errorMessage: string;
};

const StatusMessages: FC<StatusMessagesProps> = ({ successMessage, errorMessage }) => (
  <AnimatePresence>
    {successMessage ? (
      <motion.div
        key={0}
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: 50 }}
        className="flex items-center justify-between"
      >
        <small className="text-green-800 font-medium">{successMessage}</small>
      </motion.div>
    ) : (
      errorMessage && (
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 50 }}
          className="flex items-center justify-between"
        >
          <small className="text-red-800 font-medium">{errorMessage}</small>
        </motion.div>
      )
    )}
  </AnimatePresence>
);

const LoginOptionSection: FC = () => (
  <div className="flex items-center justify-between">
    <small className="font-medium text-stone-800 cursor-pointer hover:underline"></small>
  </div>
);

type SubmitButtonProps = {
  isDisabled: boolean;
  processing: boolean;
};

const SubmitButton: FC<SubmitButtonProps> = ({ isDisabled, processing }) => (
  <button
    type="submit"
    className="flex flex-row items-center justify-center disabled:bg-gray-500 disabled:cursor-not-allowed w-full text-white bg-therapy-blue-dark focus:ring-0 focus:outline-none font-medium rounded-lg text-sm px-5 py-2 text-center"
    disabled={isDisabled}
  >
    {processing && <i className="fa-duotone fa-loader animate-spin mr-2"></i>}
    Update Password
  </button>
);

export default ResetPasswordPage;