import SaveButton from "@/components/buttons/SaveButton"
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent"
import ToolbarComponent from "@/components/ToolbarComponent"
import { specialities } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer"
import { updateSidebarState } from "@/store/slicers/sidebar.slicer"
import { registerTherapistAction } from "@/store/slicers/therapist.slicer"
import notification from "@/utils/notification.util"
import { type FC, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util"

interface SpecialtiesState {
  [key: string]: string[]
}

const Page: FC = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()
  const location = useLocation()
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop()
  const authStore = useSelector((state: RootState) => state.auth)
  const initialPageContent = authStore.registration.formContent[pageId!] || {}
  const sidebarState = useSelector((state: RootState) => state.sidebar)
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

  const [specialtiesState, setSpecialtiesState] = useState<SpecialtiesState>(initialPageContent)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const [showError, setShowError] = useState<boolean>(invalidMenus.has(pageId!));

  const updatedSpecialties = specialities.map((specialty) => {
    const isPresent = Object.keys(specialtiesState).includes(specialty.value.toString())
    const shouldDisable = Object.keys(specialtiesState).length === 5 && !isPresent
    const hasNoSubSpecSelected = specialtiesState[specialty.value.toString()]?.length === 0

    return {
      ...specialty,
      disabled: shouldDisable,
      errorMessage: hasNoSubSpecSelected ? "Must choose at least 1 sub-specialty" : undefined,
    }
  })

  const isValidSpecialties = () => {
    if (Object.keys(specialtiesState).length === 0) {
      return false
    }

    // Check if each specialty has at least one sub-specialty
    for (const specialtyKey in specialtiesState) {
      if (!specialtiesState[specialtyKey] || specialtiesState[specialtyKey].length === 0) {
        return false
      }
    }

    return true
  }

  const handleChildChange = (value: string, parentValue: string) => {
    setSpecialtiesState((prevSpecialtiesState) => {
      const newSpecialtiesState = { ...prevSpecialtiesState }

      if (Object.prototype.hasOwnProperty.call(newSpecialtiesState, parentValue)) {
        const parentArray = newSpecialtiesState[parentValue] as string[]
        if (Array.isArray(parentArray)) {
          if (parentArray.includes(value)) {
            newSpecialtiesState[parentValue] = parentArray.filter((item) => item !== value)
          } else {
            newSpecialtiesState[parentValue] = [...parentArray, value]
          }
        }
      } else {
        newSpecialtiesState[parentValue] = [value]
      }

      return newSpecialtiesState
    })
  }

  const handleSingleChange = (value: string) => {
    setSpecialtiesState((prevSpecialtiesState) => {
      const newSpecialtiesState = { ...prevSpecialtiesState }

      if (Object.prototype.hasOwnProperty.call(newSpecialtiesState, value)) {
        delete newSpecialtiesState[value]
      } else {
        newSpecialtiesState[value] = []
      }

      return newSpecialtiesState
    })
  }

  const handlePageSubmit = async () => {
    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken
    if (!userToken) {
      notification.error("User Token is required")
      return
    }
    const isPageValid = isValidSpecialties();
    setIsLoading(true);

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }

    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));

    const res = await dispatch(registerTherapistAction(specialtiesState, pageId!, userToken))

    setIsLoading(false);
    if (res?.status === 200 || res?.status === 201) {
      ["rank-specialties", "rank-sub-specialties"].forEach((rankPage) => {
        const rankedData = authStore.registration.formContent[rankPage] || {};

        const selectedItems =
          rankPage === "rank-specialties"
            ? Object.keys(specialtiesState)
            : Object.values(specialtiesState).flat();

        // Filter out old/stale ranked items and re-index from 1
        const filteredRankedData = Object.fromEntries(
          Object.entries(rankedData)
            .filter(([, value]) => selectedItems.includes(value as string))
            .map(([, value], index) => [index + 1, value])
        );

        dispatch(updateRegistrationForm({ pageId: rankPage, values: filteredRankedData }));

        // Check if there are any unranked selected items
        const rankedValues = Object.values(filteredRankedData);
        const unrankedItems = selectedItems.filter(item => !rankedValues.includes(item));

        if (unrankedItems.length > 0) {
          invalidMenus.add(rankPage);
        } else {
          invalidMenus.delete(rankPage);
        }
        dispatch(updateSidebarState({
          key: "registerTherapist",
          value: {
            ...sidebarState.registerTherapist,
            invalidMenus: Array.from(invalidMenus)
          }
        }));
      });

      dispatch(updateRegistrationForm({ pageId, values: specialtiesState }))

      if (editFlag) {
        navigate("/auth/register/rank-specialties");
        return;
      }

      if (navToInvalidPage) {
        const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
        
        if (nextInvalidPage) {
          navigate(`/auth/register/${nextInvalidPage}`);
        } else {
          // No invalid pages left, check if registration is complete
          const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
          
          if (registrationComplete && shouldShowRegCompleteDialog) {
            // Show completion dialog when all pages are valid
            dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
          } else {
            // Registration not complete but no specific invalid page identified
            navigate("/auth/register/rank-specialties");
          }
        }
      } else {
        navigate("/auth/register/rank-specialties");
      }
    }
  }

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-4 sm:py-6 md:py-8">
        <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">Your Specialties</h1>
        <h1 className="text-base sm:text-lg font-medium text-therapy-blue-dark">(select up to 5 major specialties and at least 1 sub-specialty under each specialty selected)</h1>
        <div className="mt-8">
          <div className="flex flex-col md:flex-row">
            <form role="form" className="flex-none flex flex-col gap-2">
              {showError && (Object.keys(specialtiesState).length === 0) && (
                <span className="text-danger text-sm">Please select at least one specialty</span>
              )}

              <AppCheckBoxListComponent
                checked={Object.keys(specialtiesState)}
                checkedChildren={Object.values(specialtiesState).flat()}
                onChildChange={handleChildChange}
                onSingleChange={(value: string) => {
                  if (!showError) setShowError(true)
                  handleSingleChange(value)
                }}
                checkList={updatedSpecialties}
              />

              <div className="mt-5">
                <SaveButton
                  disabled={editFlag && !isValidSpecialties()}
                  value={`${editFlag ? "Save" : "Save & Continue"}`}
                  loading={isLoading}
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageSubmit();
                  }}
                />
              </div>

              <br />
            </form>
          </div>
        </div>
      </div>
    </>
  )
}

export default Page

