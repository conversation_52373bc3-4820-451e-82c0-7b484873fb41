import "react-phone-number-input/style.css";
import PhoneInput, { isPossiblePhoneNumber } from "react-phone-number-input";
import SaveButton from "@/components/buttons/SaveButton";
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent";
import ToolbarComponent from "@/components/ToolbarComponent";
import { appointmentNotifications, sessionScheduled } from "@/configs/registration.configs";
import type { AppDispatch, RootState } from "@/store/index";
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer";
import { updateSidebarState } from "@/store/slicers/sidebar.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import notification from "@/utils/notification.util";
import { Formik } from "formik";
import type { FC } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util";

const WaitlistNotificationsPage: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const pageContent = authStore.registration.formContent[pageId!] || {};
	const sidebarState = useSelector((state: RootState) => state.sidebar);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog } = authStore.registration;

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showErrors = invalidMenus.has(pageId!);

  const handleNavigation = () => {
    if (navToInvalidPage) {
      const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
      
      if (nextInvalidPage) {
        navigate(`/auth/register/${nextInvalidPage}`);
      } else {
        // No invalid pages left, check if registration is complete
        const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
        
        if (registrationComplete && shouldShowRegCompleteDialog) {
          // Show completion dialog when all pages are valid
          dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
        } else {
          // Registration not complete but no specific invalid page identified
          navigate("/auth/register/calendar-add");
        }
      }
    } else {
      navigate("/auth/register/calendar-add");
    }
  }

  const handlePageSubmit = async ({
    values,
    isPageValid,
    setSubmitting,
  }: {
    values: any;
    isPageValid: boolean;
    setSubmitting: (isSubmitting: boolean) => void;
  }) => {
    if (!userRegistrationToken) {
      notification.error("User Token is required")
      return;
    }

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }
    setSubmitting(true);
    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));

    const res = await dispatch(registerTherapistAction(values, pageId!, userRegistrationToken))
    setSubmitting(false);
    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values }))

      handleNavigation()
    }
  }

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-4 sm:py-6 md:py-8">
        <div className="mt-2">
          <Formik
            validateOnMount={true}
            initialValues={{
              booking_time_hour: pageContent.booking_time_hour || "1",
              notification_preferences: pageContent.notification_preferences || [],
              phone_number: pageContent.phone_number || "",
              week_visibility: pageContent.week_visibility?.toString() || "2",
            }}
            validate={(values) => {
              const errors = {} as any;            
              if (!values.notification_preferences || values.notification_preferences.length === 0)
                errors.notification_preferences = "Notification Preferences is required";
            
              if (values.notification_preferences.includes("text")) {
                if (!values.phone_number) errors.phone_number = "Phone number is required";
                else if (!isPossiblePhoneNumber(values.phone_number)) errors.phone_number = "Input a valid phone number";
              }
            
              if (!values.week_visibility) errors.week_visibility = "Week visibility is required";
            
              return errors;
            }}
            onSubmit={() => { /* needed for Formik, submit handled elsewhere */ }}
          >
            {({ values, errors, handleChange, isValid, isSubmitting, isValidating, setSubmitting, setValues, setFieldValue, setFieldTouched, touched }) => {
              return (
                <form className="max-w-full space-y-8">
                  {/* Waitlists & Notifications Section */}
                  <div className="space-y-2">
                    <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">
                      Waitlists & Notifications
                    </h1>
                    <h1 className="text-2xl font-medium text-therapy-blue-dark">
                      Advance Notice
                      <span className="hidden sm:inline">
                        <br />
                      </span>{" "}
                    </h1>

                    <div className="space-y-2">
                      <p className="text-sm text-gray-400">
                        <span className="text-red-500">*</span>Require at least [X] hours' notice to book an appointment
                      </p>
                      <div className="w-full sm:w-60">
                        <select
                          name="booking_time_hour"
                          value={values.booking_time_hour || "1"}
                          onChange={handleChange}
                          onBlur={() => setFieldTouched("booking_time_hour")}
                          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-therapy-blue-light"
                        >
                          {sessionScheduled.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <div className="text-red-500 text-xs mt-2">
                          {(showErrors || touched.booking_time_hour) && errors.booking_time_hour && errors.booking_time_hour.toString()}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Calendar Visibility Section */}
                  <div className="space-y-2">
                    <h1 className="text-xl sm:text-2xl md:text-2xl font-medium text-therapy-blue-dark">
                      Calendar Visibility
                    </h1>
                    <p className="text-sm text-gray-500">
                    <span className="text-red-500 font-bold ml-1">*</span>
                      Choose how many weeks into the future your availability will be visible to patients.
                    </p>

                    <div className="w-full sm:w-60">
                      <select
                        name="week_visibility"
                        value={values.week_visibility || "2"}
                        onChange={handleChange}
                        onBlur={() => setFieldTouched("week_visibility")}
                        className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-therapy-blue-light"
                      >
                        <option value="1">1 week</option>
                        <option value="2">2 weeks</option>
                        <option value="3">3 weeks</option>
                        <option value="4">4 weeks</option>
                      </select>
                      <div className="text-red-500 text-xs mt-2">
                        {(showErrors || touched.week_visibility) && errors.week_visibility && errors.week_visibility.toString()}
                      </div>
                    </div>
                  </div>

                  {/* Notifications Section */}
                  <div className="space-y-2">
                    <h1 className="text-xl sm:text-2xl md:text-2xl font-medium text-therapy-blue-dark">
                      Notification Method
                      <span className="hidden sm:inline">
                        <br />
                      </span>{" "}
                    </h1>
                    <label className="text-sm text-gray-400 block">
                      <span className="text-red-500 font-bold ml-1">*</span>
                      How would you like to be notified of newly scheduled appointments?
                    </label>
                    <div className="mt-3">
                      <AppCheckBoxListComponent
                        checked={values.notification_preferences}
                        onChange={(val) => {
                          setFieldTouched("notification_preferences")
                          setValues({
                            ...values,
                            notification_preferences: val,
                            phone_number: val.includes("text") ? values.phone_number : "",
                          })
                        }}
                        checkList={appointmentNotifications}
                      />
                      <div className="text-red-500 text-xs mt-2">
                        {(showErrors || touched.notification_preferences) &&
                          errors.notification_preferences && errors.notification_preferences.toString()}
                      </div>
                      {values.notification_preferences.includes("text") && (
                        <div className="w-full ml-5 sm:w-[300px] mt-4 mb-4">
                          <p className="mb-2 text-sm text-gray-400">
                            <span className="text-red-500">*</span>Mobile Phone Number
                          </p>
                          <PhoneInput
                            international
                            countryCallingCodeEditable={false}
                            defaultCountry="US"
                            value={values.phone_number}
                            onChange={(val) => {
                              setFieldTouched("phone_number")
                              setFieldValue("phone_number", val)
                            }}
                            onBlur={() => setFieldTouched("phone_number")}
                            className="register-phone-input w-full"
                          />
                          {(showErrors || touched.phone_number) && errors.phone_number && (
                            <div className="text-red-500 text-xs mt-2">{errors.phone_number.toString()}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Save Button */}
                  <div className="mt-8">
                    <SaveButton
                      disabled={isValidating}
                      loading={isSubmitting}
                      value="Save & Continue"
                      onClick={async (e) => {
                        e.preventDefault();
                        handlePageSubmit({
                          values,
                          isPageValid: isValid,
                          setSubmitting,
                        });
                      }}
                    />
                  </div>
                </form>
              )
            }}
          </Formik>
        </div>
      </div>
    </>
  );
};

export default WaitlistNotificationsPage
