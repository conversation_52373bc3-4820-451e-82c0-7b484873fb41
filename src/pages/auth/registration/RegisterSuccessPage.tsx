import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const RegisterSuccessPage: React.FC = () => {
	const navigate = useNavigate();
	
	const success = new URLSearchParams(window.location.search).get('success');
	const email = new URLSearchParams(window.location.search).get('email');

  useEffect(() => {
    if (!email || success !== "true") {
      navigate("/auth/signin", { replace: true });
    }
  }, [email, success, navigate]);

  if (!email || success !== "true") {
    return null; // Rendering nothing while redirecting
  }

	return (
		<>
			<div className="flex-grow px-16 py-8">
				<div className="mt-8">
					<h1 className="text-4xl font-medium text-therapy-blue-dark">Registration was successful</h1>
					<br />
					<hr />
					<br />
					<br />
					<p className="text-lg text-gray-700">
						Thank you for completing your registration with <span className="font-semibold text-therapy-blue">NextTherapist</span>.
						Your account with the email <strong className="text-1xl font-medium text-therapy-blue-dark">{email}</strong> is currently under review. 
						<br /><br />
						You will be notified via email if any additional information is required or if there are any issues with your account.  
						<br /><br />
						In the meantime, feel free to check back later to log in once your account has been approved.
					</p>
				</div>
			</div>
		</>
	);
};

export default RegisterSuccessPage;