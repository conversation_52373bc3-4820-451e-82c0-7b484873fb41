import InputComponent from "@/components/forms/InputComponent";
import { useState, type FC } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import SaveButton from "@/components/buttons/SaveButton";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch, RootState } from "@/store/index";
import {
  updateRegistrationForm,
  updateRegistrationState
} from "@/store/slicers/auth.slicer";
import { Formik } from "formik";
import ToolbarComponent from "@/components/ToolbarComponent";
import {
  registerTherapistAction,
  sendVerificationEmailAction,
  verifyEmailAction
} from "@/store/slicers/therapist.slicer";
import { sanitizeInputValue } from "@/utils/app.util";
import {
  PASSWORDS_DONT_MATCH_ERROR,
  ACCEPT_PRIVACY_POLICY,
  ACCEPT_TERMS_OF_SERVICE_ERROR
} from "../../../../src/pages/auth/constants";
import { useOverlayTriggerState } from "react-stately";
import NormalDialog from "@/components/dialogs/NormalDialog";
import AccountStatusDialog from "@/components/dialogs/AccountStatusDialog";

const CreateAccountPage: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();

  const authStore = useSelector((state: RootState) => state.auth);
  const formContent = authStore.registration.formContent[pageId!] || {};
  const { userRegistrationToken } = authStore.registration;
  
  const password_validation = '* Password must be at least 8 characters long, contain at least one uppercase letter, one number, and one special character.';

  const accountUnderReviewState = useOverlayTriggerState({});
  const accountRejectedState = useOverlayTriggerState({});
  const verificationCodeState = useOverlayTriggerState({});
  const accountIncompleteState = useOverlayTriggerState({});
  const accountExistState = useOverlayTriggerState({});

  const [userId, setUserId] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isResending, setIsResending] = useState<boolean>(false);
  const [code, setCode] = useState<string | null>(null);
  const [countdown, setCountdown] = useState<number>(0);

  const startCountdown = () => {
    setCountdown(60)
    const timer = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          clearInterval(timer)
          return 0
        }
        return prevCount - 1
      })
    }, 1000)
  }

  const sendVerificationCode = async (email: string) => {
    setIsResending(true);
    const res = await dispatch(sendVerificationEmailAction({ email, noLink: true }))
    if (res?.status === 200) {
      startCountdown()
    }
    setIsResending(false);
  }

  const handleVerifyEmail = async (email: string) => {
    setLoading(true)
    const res = await dispatch(verifyEmailAction({ email, code }))
    if (res?.status === 201) {
      verificationCodeState.close();
      setCode(null);
      navigate("/auth/register/license");
    }
    setLoading(false);
  };

  const handlePrivacyPolicyClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open("/NT_PrivacyPolicy.html","_blank");
  };

  const handleTermsAndCondition = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open("/Terms_and_conditions.html", "_blank");
  };

  return (
    <>
      <ToolbarComponent />

      <div className="flex-grow px-4 sm:px-8 md:px-12 lg:px-16 py-4 sm:py-6 md:py-8">
        <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">Create Your Account</h1>
        <div className="mt-4 sm:mt-6 md:mt-8 w-full max-w-md">
          <Formik
            validateOnMount={true}
            initialValues={{
              first_name: formContent.first_name || "",
              last_name: formContent.last_name || "",
              email: formContent.email || "",
              password: formContent.password || "",
              confirm_password: formContent.confirm_password || "",
              acceptPrivacyPolicy: formContent.acceptPrivacyPolicy || false,
               acceptTerms: false,
            }}
            validate={(values) => {
              const errors: any = {};
              if (!values.first_name)
                errors.first_name = "First Name is a required";
              else if (!/^[a-zA-Z\s]+$/.test(values.first_name)) {
                errors.first_name = "First Name should only contain letters";
              }

              if (!values.last_name)
                errors.last_name = "Last Name is a required";
              else if (!/^[a-zA-Z\s]+$/.test(values.last_name)) {
                errors.last_name = "Last Name should only contain letters";
              }

              if (!values.email) errors.email = "Email is a required field";
              else if (
                !/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(values.email)
              ) {
                errors.email = "Input a valid email address";
              }

              if (!values.password) {
                errors.password = password_validation;
              } else {
                const hasUpperCase = /[A-Z]/.test(values.password);
                const hasNumber = /\d/.test(values.password);
                const hasSpecial = /[\W_]/.test(values.password);
                const isLongEnough = values.password.length >= 8;
                
                if (hasUpperCase && hasNumber && hasSpecial && isLongEnough) {
                  // Password meets all requirements
                } else {
                  let errorMsg = '* Password must be';
                  
                  if (!isLongEnough) {
                    errorMsg += ' at least 8 characters long';
                  }
                  
                  if (!hasUpperCase) {
                    errorMsg += (errorMsg !== '* Password must be' ? ',' : '') + 
                                ' contain at least one uppercase letter';
                  }
                  
                  if (!hasNumber) {
                    errorMsg += (errorMsg !== '* Password must be' ? ',' : '') + 
                                ' contain at least one number';
                  }
                  
                  if (!hasSpecial) {
                    errorMsg += (errorMsg !== '* Password must be' ? ',' : '') + 
                                ' contain at least one special character';
                  }
                  
                  errorMsg += '.';
                  errors.password = errorMsg;
                }
              }
              
              if (values.password !== values.confirm_password) errors.confirm_password = PASSWORDS_DONT_MATCH_ERROR;
              
              if (!values.acceptPrivacyPolicy) {
                errors.acceptPrivacyPolicy = ACCEPT_PRIVACY_POLICY;
              }
              if (!values.acceptTerms) {
                errors.acceptTerms = ACCEPT_TERMS_OF_SERVICE_ERROR;
              }

              return errors;
            }}
            onSubmit={async (values) => {
              const res = userRegistrationToken
                ? await dispatch(registerTherapistAction(values, pageId!, userRegistrationToken))
                : await dispatch(registerTherapistAction(values, pageId!));

              if (res?.status === 201 || res?.status === 200) {
                dispatch(updateRegistrationForm({ pageId, values }));
                if (res.data?.data?.userToken) {
                  dispatch(updateRegistrationState({ key: "userRegistrationToken", value: res.data.data.userToken }));
                }
                if (res.data?.data?.verificationEmailSent) {
                  verificationCodeState.open();
                  startCountdown();
                  return;
                }
                dispatch(updateRegistrationState({ key: "currentPage", value: "license" }));
                navigate("/auth/register/license")
              } else if (res?.status === 403) {
                if (res.data?.data?.accountUnderReview) {
                  accountUnderReviewState.open();
                }
                else if (res.data?.data?.accountIncomplete) {
                  accountIncompleteState.open();
                  if (res.data?.data?.userId) setUserId(res.data.data.userId);
                }
                else if (res.data?.data?.accountRejected) {
                  accountRejectedState.open();
                  if (res.data?.data?.userId) setUserId(res.data.data.userId);
                }
                else if (res.data?.data?.accountExist) {
                  accountExistState.open();
                }
              }
            }}
          >
            {({ errors, handleChange, handleSubmit, values, isValid, isSubmitting, touched, setFieldTouched, setFieldValue }) => {
              return (
                <>
                  <form onSubmit={handleSubmit} id="create-account-form" autoComplete="off">
                    <InputComponent
                      value={values.first_name}
                      errorMessage={errors.first_name && touched.first_name && (errors.first_name as string)}
                      onChange={(value) => {
                        setFieldTouched("first_name")
                        const sanitizedValue = sanitizeInputValue(value);
                        handleChange({ target: { value: sanitizedValue, name: "first_name" } });
                      }}
                      id="first_name"
                      class="mb-4 sm:mb-5"
                      label={"First Name"}
                      isRequired
                      placeholder="Type here ..."
                    />

                    <InputComponent
                      value={values.last_name}
                      errorMessage={errors.last_name && touched.last_name && (errors.last_name as string)}
                      onChange={(value) => {
                        setFieldTouched("last_name")
                        const sanitizedValue = sanitizeInputValue(value)
                        handleChange({ target: { value: sanitizedValue, name: "last_name" } })
                      }}
                      class="mb-4 sm:mb-5"
                      id="last_name"
                      label={"Last Name"}
                      isRequired
                      placeholder="Type here ..."
                    />

                    <InputComponent
                      value={values.email}
                      errorMessage={errors.email && touched.email && (errors.email as string)}
                      name="email"
                      onChange={(value) => {
                        setFieldTouched("email")
                        handleChange({ target: { value, name: "email" } })
                      }}
                      class="mb-4 sm:mb-5"
                      label={"Email Address"}
                      isRequired
                      placeholder="Type here ..."
                      type="email"
                    />

                    <InputComponent
                      value={values.password}
                      id="password"
                      name="password"
                      errorMessage={errors.password && touched.password && (errors.password as string)}
                      onChange={(value) => {
                        setFieldTouched("password")
                        handleChange({ target: { value, name: "password" } })
                      }}
                      class="mb-4 sm:mb-5"
                      label={"Password"}
                      isRequired
                      placeholder="Type here ..."
                      type="password"
                      showToggleIcon={true}
                      data-testid="password"
                    />

                    <InputComponent
                      value={values.confirm_password}
                      id="confirm_password"
                      errorMessage={
                        errors.confirm_password && touched.confirm_password && (errors.confirm_password as string)
                      }
                      onChange={(value) => {
                        setFieldTouched("confirm_password")
                        handleChange({ target: { value, name: "confirm_password" } })
                      }}
                      class="mb-4 sm:mb-5"
                      label={"Confirm Password"}
                      isRequired
                      placeholder="Type here ..."
                      type="password"
                      data-testid="confirm_password"
                    />

                <div className="mb-4 sm:mb-5">
  <div className="flex items-start space-x-2">
    <input
      type="checkbox"
      id="acceptTerms"
      name="acceptTerms"
      checked={values.acceptTerms}
      onChange={(e) => {
        setFieldTouched("acceptTerms");
        setFieldValue("acceptTerms", e.target.checked);
      }}
      className="mt-1 h-4 w-4 text-therapy-blue-dark border-gray-300 rounded focus:ring-therapy-blue-dark"
      data-testid="terms-checkbox"
    />
    <label htmlFor="acceptTerms" className="text-sm text-gray-700">
      <span className="text-red-500 font-bold">*</span> I have reviewed and agreed to the{" "}
      <span
        onClick={handleTermsAndCondition}
        className="text-therapy-blue-dark cursor-pointer underline font-medium"
        role="button"
        tabIndex={0}
      >
        Terms & Conditions
      </span>
    </label>
  </div>

  <div className="flex items-start space-x-2 mt-2">
    <input
      type="checkbox"
      id="acceptPrivacyPolicy"
      name="acceptPrivacyPolicy"
      checked={values.acceptPrivacyPolicy}
      onChange={(e) => {
        setFieldTouched("acceptPrivacyPolicy");
        setFieldValue("acceptPrivacyPolicy", e.target.checked);
      }}
      className="mt-1 h-4 w-4 text-therapy-blue-dark border-gray-300 rounded focus:ring-therapy-blue-dark"
      data-testid="privacy-policy-checkbox"
    />
    <label htmlFor="acceptPrivacyPolicy" className="text-sm text-gray-700">
      <span className="text-red-500 font-bold">*</span> I have reviewed and agreed to the{" "}
      <span
        onClick={handlePrivacyPolicyClick}
        className="text-therapy-blue-dark cursor-pointer underline font-medium"
        role="button"
        tabIndex={0}
      >
        Privacy Policy
      </span>
    </label>
  </div>

  {(errors.acceptTerms && touched.acceptTerms) && (
    <p style={{ color: "red", fontSize: "12px", marginTop: "5px" }} data-testid="terms-error">
      {errors.acceptTerms as string}
    </p>
  )}
  {(errors.acceptPrivacyPolicy && touched.acceptPrivacyPolicy) && (
    <p style={{ color: "red", fontSize: "12px", marginTop: "5px" }} data-testid="privacy-policy-error">
      {errors.acceptPrivacyPolicy as string}
    </p>
  )}
</div>


                    <div className="mt-2 sm:mt-4">
                      <SaveButton disabled={!isValid} loading={isSubmitting} value="Continue" />
                    </div>
                  </form>
                  <NormalDialog
                    state={verificationCodeState}
                    title="Verify your email address"
                    confirmLabel="Verify"
                    onCancel={() => {
                      setCode(null)
                      verificationCodeState.close()
                    }}
                    onAccept={() => {
                      handleVerifyEmail(values.email);
                    }}
                    marginTop={-100}
                    primaryButtonColor={true}
                    loading={loading}
                    btnDisabled={!code || code.length !== 6 || isResending}
                  >
                    <div>
                      <p className="text-sm">
                        A verification code has been sent to your email address
                        <strong className="block">({values.email})</strong>Please input the code to verify your email and
                        continue with the registration.
                      </p>
                      
                      <InputComponent
                        value={code || ''}
                        placeholder="Enter verification code"
                        onChange={(value) => setCode(value)}
                        id="verification_code"
                        class="mt-5 mb-5"
                        maxLength={6}
                      />

                      <div>
                        <p>
                          Didn't receive verification code? {!isResending && "You can "}
                          {countdown > 0 ? (
                            <span className="text-danger font-semibold">Try again in {countdown}s</span>
                          ) : isResending ? (
                            <span className="text-therapy-blue-dark font-semibold">Sending...</span>
                          ) : (
                            <span
                              className="text-therapy-blue-dark cursor-pointer font-semibold"
                              onClick={() => sendVerificationCode(values.email)}
                            >
                              Try again.
                            </span>
                          )}
                        </p>
                      </div>
                    </div>
                  </NormalDialog>

                  <AccountStatusDialog
                    underReviewState={accountUnderReviewState}
                    rejectedState={accountRejectedState}
                    incompleteState={accountIncompleteState}
                    accountExistState={accountExistState}
                    userId={userId}
                    email={values.email}
                  />
                </>
              )
            }}
          </Formik>
        </div>
      </div>
    </>
  )
}

export default CreateAccountPage

