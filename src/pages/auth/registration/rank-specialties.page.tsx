import SaveButton from '@/components/buttons/SaveButton'
import { specialities } from '@/configs/registration.configs'
import type { AppDispatch, RootState } from '@/store/index'
import {
	updateRegistrationForm,
	updateRegistrationState,
} from '@/store/slicers/auth.slicer'
import { type FC, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd'
import ToolbarComponent from '@/components/ToolbarComponent'
import notification from '@/utils/notification.util'
import { registerTherapistAction } from '@/store/slicers/therapist.slicer'
import { updateSidebarState } from '@/store/slicers/sidebar.slicer'
import { getNextInvalidPage, validateTherapistRegistration } from '@/utils/app.util'

const Page: FC = () => {
	const [specList, setSpecList] = useState<any[]>([])
	const navigate = useNavigate()
	const dispatch = useDispatch<AppDispatch>()
	const location = useLocation()
	const pageId = location.pathname.replace(/\/$/, '').split('/').pop()
	const authStore = useSelector((state: RootState) => state.auth)
	const selectedSpecialities = authStore.registration.formContent['specialization'] || {}
	const pageContent = authStore.registration.formContent[pageId!] || {}
	const sidebarState = useSelector((state: RootState) => state.sidebar)
	const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

	const [rankedSpecialities, setRankedSpecialities] = useState<Record<string, string>>(pageContent)
	const [isLoading, setIsLoading] = useState<boolean>(false)
	
	const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
	const showError = invalidMenus.has(pageId!);

	const handlePageSubmit = async () => {
		const isPageValid = Object.keys(rankedSpecialities).length === Object.keys(selectedSpecialities).length
		const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
		const navigateToPage = editFlag ? "/edit/rank-sub-specialties" : `/auth/register/rank-sub-specialties`;

		if (!userToken) {
			notification.error('User Token is required')
			return
		}
		setIsLoading(true);
		
		if (!isPageValid) {
			invalidMenus.add(pageId!);
		} else {
			invalidMenus.delete(pageId!);
		}

		dispatch(updateSidebarState({
			key: "registerTherapist",
			value: {
				...sidebarState.registerTherapist,
				invalidMenus: Array.from(invalidMenus)
			}
		}));

		const res = await dispatch(
			registerTherapistAction(rankedSpecialities, pageId!, userToken)
		)
		setIsLoading(false)
		if (res?.status === 200 || res?.status === 201) {
			dispatch(updateRegistrationForm({ pageId, values: rankedSpecialities }))

			if (editFlag) {
				navigate(navigateToPage);
				return;
			}

			if (navToInvalidPage) {
				const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
				
				if (nextInvalidPage) {
					navigate(`/auth/register/${nextInvalidPage}`);
				} else {
					// No invalid pages left, check if registration is complete
					const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
					
					if (registrationComplete && shouldShowRegCompleteDialog) {
						// Show completion dialog when all pages are valid
						dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
					} else {
						// Registration not complete but no specific invalid page identified
						navigate(navigateToPage);
					}
				}
			} else {
				navigate(navigateToPage);
			}
		}
	}

	const selectedSpecOptions = specialities.filter(
		(spc) =>
			Object.keys(selectedSpecialities).includes(spc.value.toString()) &&
			!Object.values(rankedSpecialities).includes(spc.value.toString())
	)

	useEffect(() => {
		setSpecList(selectedSpecOptions)
	}, [])

	const totalDroppableLoopNumbers = Array.from(
		{ length: Object.keys(selectedSpecialities).length },
		(_, index) => index + 1,
	)

	return (
		<>
			<ToolbarComponent />
			<div className=" px-4 sm:px-8 md:px-16 py-4 sm:py-8">
				<h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">
					Rank Specialties
				</h1>

				<h3 className="text-base sm:text-lg font-semibold text-therapy-blue-dark mt-2">
					Drag and drop the rankings to indicate your preferences, with 1 being
					the strongest.
				</h3>

				{showError && Object.keys(rankedSpecialities).length !== Object.keys(selectedSpecialities).length && (
					<span className="text-danger text-sm">Please rank the specialties in the available slots</span>
				)}

				{!selectedSpecialities ||
					Object.keys(selectedSpecialities).length === 0 ? (
					<div
						className="p-4 mb-4 mt-6 sm:mt-10 w-full max-w-[800px] text-therapy-blue border border-blue-300 rounded-lg bg-blue-50"
						role="alert"
					>
						<div className="flex items-center">
							<svg
								className="flex-shrink-0 w-4 h-4 me-2"
								aria-hidden="true"
								xmlns="http://www.w3.org/2000/svg"
								fill="currentColor"
								viewBox="0 0 20 20"
							>
								<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
							</svg>
							<h3 className="text-lg font-medium">Info</h3>
						</div>
						<div className="mt-2 mb-4 text-sm sm:text-md">
							Specialties should be selected first in order to rank them. Please
							select your preferred{' '}
							<Link
								to="/auth/register/specialization"
								className="font-bold hover:underline"
							>
								Specialties
							</Link>
							.
						</div>
					</div>
				) : (
					<div className='mt-6'>
						<div className="specialities-drop-container flex flex-col xl:flex-row mt-6 items-start">

							<DragDropContext
								onDragEnd={(val) => {
									const { destination, draggableId } = val
									if (
										!destination?.droppableId ||
										!draggableId ||
										destination?.droppableId === 'pre-selected-list'
									)
										return

									setRankedSpecialities((prevRankedSpecialities) => {
										const updatedSpecList = specList.filter(
											(opt) => opt.value !== draggableId
										)
										const existingValue =
											prevRankedSpecialities[destination?.droppableId]

										if (existingValue) {
											const selectedSpecOption = specialities.find(
												(spc) => spc.value.toString() === existingValue
											)
											if (selectedSpecOption) {
												updatedSpecList.push(selectedSpecOption)
											}
										}
										setSpecList([...updatedSpecList])

										return {
											...prevRankedSpecialities,
											[destination?.droppableId]: draggableId,
										}
									})
								}}
							>
								<Droppable direction="vertical" droppableId="pre-selected-list">
									{(provided) => {
										return (
											<ul
												ref={provided.innerRef}
												{...provided.droppableProps}
												// className="light-theme w-full md:w-1/3 lg:w-1/2 xl:w-1/4 border-gray-300 rounded-lg lg:p-3 min-h-[200px] bg-white sm:min-p-"
												className="spec-list grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2"
											>
												{provided.placeholder}
												{specList.map((spec, i) => (
													<Draggable
														key={spec.value}
														draggableId={spec.value}
														index={i}
													>
														{(provided) => (
														<li
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="dropped-item"
                          >
																<i className="fa-solid fa-grip-vertical ml-5"></i>{' '}
																<span>{spec.label}</span>
															</li>
														)}
													</Draggable>
												))}
											</ul>
										)
									}}
								</Droppable>

								{selectedSpecOptions.length > 0 && (
									<div className="md:flex items-center justify-center mx-2">
										<div className="transform rotate-90 xl:rotate-0 flex-shrink-0 lg:ml-6 lg:mr-[85px]">
											<img
												src="/spec-arrow.png"
												alt="Arrow"
												className="w-full h-auto"
											/>
										</div>
									</div>
								)}

								<div className="drop-here-placeholder">
									<div className="drop-title-bar text-white p-3 rounded-t-lg">
										<h4 className="font-semibold">Preferred Specialties</h4>
									</div>

									<ul className="drop-box-container">
										{[...totalDroppableLoopNumbers].map((item, index) => (
											<Droppable
												key={index}
												droppableId={item.toString()}
												direction="horizontal"
											>
												{(provided, snapshot) => (
													<li
														ref={provided.innerRef}
														{...provided.droppableProps}
														className={`${snapshot.isDraggingOver ? 'about-to-drop' : ''}`}
													>
														{rankedSpecialities &&
															rankedSpecialities[item.toString()] && (
																<p className="dropped-item">
																	<span>
																		{
																			specialities.find(
																				(opt) =>
																					opt.value ===
																					rankedSpecialities[item.toString()]
																			)?.label
																		}
																	</span>
																	<i
																		onClick={() => {
																			const currentSpec = specialities.find(
																				(opt) =>
																					opt.value ===
																					rankedSpecialities[item.toString()]
																			)
																			setRankedSpecialities(
																				(prevRankedSpecialities) => {
																					const newRankedSpecialities = {
																						...prevRankedSpecialities,
																					}
																					delete newRankedSpecialities[
																						item.toString()
																					]
																					setSpecList([
																						...specList,
																						currentSpec,
																					])
																					return newRankedSpecialities
																				}
																			)
																		}}
																		className="fa fa-times-square text-white end-icon"
																	></i>
																</p>
															)}
													</li>
												)}
											</Droppable>
										))}
									</ul>
								</div>
							</DragDropContext>
						</div>

						<div className="mt-6 ml-4">
							<SaveButton
								disabled={editFlag && Object.keys(rankedSpecialities).length !== Object.keys(selectedSpecialities).length}
								type='button'
								value={`${editFlag ? "Save" : "Save & Continue"}`}
								loading={isLoading}
								onClick={(e) => {
									e.preventDefault();
									handlePageSubmit();
								}}
							/>
						</div>
					</div>
				)}
			</div>
		</>
	)
}

export default Page
