import SaveButton from "@/components/buttons/SaveButton"
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent"
import InputComponent from "@/components/forms/InputComponent"
import ToolbarComponent from "@/components/ToolbarComponent"
import { insuranceTypes } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer"
import { updateSidebarState } from "@/store/slicers/sidebar.slicer"
import { registerTherapistAction } from "@/store/slicers/therapist.slicer"
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util"
import notification from "@/utils/notification.util"
import { Formik } from "formik"
import type { FC } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"

const PaymentFormsPage: FC = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()
  const location = useLocation()
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop()
  const authStore = useSelector((state: RootState) => state.auth)
  const pageContent = authStore.registration.formContent[pageId!] || ""
  const sidebarState = useSelector((state: RootState) => state.sidebar)
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog } = authStore.registration;

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showErrors = invalidMenus.has(pageId!);

  const handleNavigation = () => {
    if (navToInvalidPage) {
      const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
      
      if (nextInvalidPage) {
        navigate(`/auth/register/${nextInvalidPage}`);
      } else {
        // No invalid pages left, check if registration is complete
        const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
        
        if (registrationComplete && shouldShowRegCompleteDialog) {
          // Show completion dialog when all pages are valid
          dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
        } else {
          // Registration not complete but no specific invalid page identified
          navigate("/auth/register/next-therapist-plan");
        }
      }
    } else {
      navigate("/auth/register/next-therapist-plan");
    }
  }

  const handlePageSubmit = async ({
    values,
    isPageValid,
    setSubmitting,
  }: {
    values: any;
    isPageValid: boolean;
    setSubmitting: (isSubmitting: boolean) => void;
  }) => {
    if (!userRegistrationToken) {
      notification.error("User Token is required")
      return;
    }

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }
    setSubmitting(true);
    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));

    const res = await dispatch(registerTherapistAction(values, pageId!, userRegistrationToken))
    setSubmitting(false)
    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values }))

      handleNavigation()
    }
  }

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-4 sm:py-6 md:py-8">

        <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">Client Payment Options</h1>
        <div className="mt-2">
          <Formik
            validateOnMount={true}
            initialValues={{
              session_fee: pageContent.session_fee || "",
              payment_methods: pageContent.payment_methods || [],
              insurance_list: pageContent.insurance_list || {},
            }}
            validate={(values) => {
              const errors = {} as any

              if (!values.session_fee) errors.session_fee = "Session Fee is required"
              else if (!/^\d+$/.test(values.session_fee)) {
                errors.session_fee = "Input a valid fee"
              } else if (Number(values.session_fee) <= 0) {
                errors.session_fee = "Session Fee must be greater than 0"
              }

              if (!values.payment_methods || values.payment_methods.length === 0) {
                errors.payment_methods = "Select at least one payment method"
              } else if (values.payment_methods.includes("insurance")) {
                if (!values.insurance_list?.checked_list || values.insurance_list?.checked_list?.length === 0)
                  errors.insurance_list = "Insurance Provider is required"

                if (values.insurance_list?.checked_list?.includes("other") && !values.insurance_list?.other_insurance)
                  errors.insurance_list = "Specify other Insurance Provider"
              }

              return errors
            }}
            onSubmit={() => { /* needed for Formik, submit handled elsewhere */ }}
          >
            {({ errors, values, isValidating, isValid, isSubmitting, setValues, setFieldTouched, setFieldValue, setSubmitting, touched }) => {
              return (
                <form className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-base sm:text-lg font-semibold text-therapy-blue-dark">
                      Session fee
                    </h3>

                    <div className="ml-0 sm:ml-4 mt-4">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                        <div className="w-full sm:w-[200px]">
                          <InputComponent
                            label="60-min session fee"
                            value={values.session_fee}
                            onChange={(val) => {
                              setFieldTouched("session_fee")
                              const numericValue = val.replace(/[^0-9]/g, "")
                              if (!/^0/.test(numericValue)) {
                                setFieldValue("session_fee", numericValue)
                              }
                            }}
                            onBlur={() => setFieldTouched("session_fee")}
                            isRequired
                            placeholder="Enter #"
                            errorMessage={
                              (showErrors || touched.session_fee) &&
                              errors.session_fee && (errors.session_fee as string)
                            }
                            maxLength={4}
                          />
                        </div>
                        <div className="text-slate-400 sm:pt-8">dollars</div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-base sm:text-lg font-semibold text-therapy-blue-dark">
                      Accepted Payment Methods
                    </h3>

                    <div className="ml-0 sm:ml-2 mt-5">
                      <AppCheckBoxListComponent 
                        checked={values.payment_methods}
                        onChange={(val) => {
                          setFieldTouched("payment_methods")
                          const insuranceList = val.includes("insurance") ? values.insurance_list : []
                          setValues((prev) => ({
                            ...prev,
                            payment_methods: val,
                            insurance_list: insuranceList,
                          }));
                        }}                        
                        checkList={[
                          { label: "Self-Pay", value: "self_pay" },
                          { label: "Clergy Pay", value: "clergy_pay" },
                          { label: "HSA/FSA", value: "hsa_fsa" },
                          { label: "Insurance", value: "insurance" },
                        ]}
                      />
                      <div className="text-red-500 text-xs mt-1">
                        {(showErrors || touched.payment_methods) && errors.payment_methods && errors.payment_methods.toString()}
                        </div>
                    </div>

                    {values?.payment_methods?.includes("insurance") && (
                      <div className="ml-4 sm:ml-8">
                        <AppCheckBoxListComponent
                          checked={values.insurance_list.checked_list}
                          onChange={(val) => {
                            if (!val.includes("other"))
                              setFieldValue("insurance_list", { checked_list: [...val] })
                            else
                              setFieldValue("insurance_list", { ...values.insurance_list, checked_list: [...val] })
                          }}
                          checkList={insuranceTypes}
                        />

                        {values.insurance_list?.checked_list?.includes("other") && (
                            <div className="w-full sm:w-[300px] pl-0 sm:pl-5">
                              <InputComponent
                                value={values.insurance_list.other_insurance}
                                onChange={(val) => {
                                  setFieldValue("insurance_list", { ...values.insurance_list, other_insurance: val })
                                }}
                                class="mb-5"
                              />
                            </div>
                          )}
                        <div className="text-red-500 text-xs">
                          {(showErrors || touched.payment_methods) && errors.insurance_list && errors.insurance_list.toString()}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="pt-4">
                    <SaveButton
                      disabled={isValidating} 
                      loading={isSubmitting} 
                      value="Save & Continue"
                      onClick={async (e) => {
                        e.preventDefault()
                        handlePageSubmit({
                          values,
                          isPageValid: isValid,
                          setSubmitting,
                        })
                      }}
                    />
                  </div>
                </form>
              )
            }}
          </Formik>
        </div>
      </div>
    </>
  )
}

export default PaymentFormsPage;

