import SaveButton from "@/components/buttons/SaveButton";
import { specialities } from "@/configs/registration.configs";
import type { AppDispatch, RootState } from "@/store/index";
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import { type FC, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import ToolbarComponent from "@/components/ToolbarComponent";
import notification from "@/utils/notification.util";
import { updateSidebarState } from "@/store/slicers/sidebar.slicer";
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util";

const Page: FC = () => {
  const [subSpecList, setSubSpecList] = useState<any[]>([]);
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const selectedSpecialities = authStore.registration.formContent["specialization"] || {};
  const pageContent = authStore.registration.formContent[pageId!] || {};
  const sidebarState = useSelector((state: RootState) => state.sidebar);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

  const [rankedSubSpecialities, setRankedSubSpecialities] = useState<Record<string, string>>(pageContent)
  const [isLoading, setIsLoading] = useState<boolean>(false)
    
  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showError = invalidMenus.has(pageId!);

  const subSpecialitiesData = specialities.flatMap((spc) =>
    Object.keys(selectedSpecialities).includes(spc.value.toString())
      ? (spc.children || []).filter(
        (child) => Object.values(selectedSpecialities).flat().includes(child.value.toString())
      )
      : []
  );

  useEffect(() => {
    const selectedSubSpecOptions = subSpecialitiesData.filter(
      (subSpc) => !Object.values(rankedSubSpecialities).includes(subSpc.value.toString())
    )
    setSubSpecList(selectedSubSpecOptions.map((spec,i)=>({_id: i, ...spec}) ));
  }, []);

  const totalDroppableLoopNumbers = Array.from(
    { length: subSpecialitiesData.length >= 5 ? 5 : subSpecialitiesData.length },
    (_, index) => index + 1,
  );

  const handlePageSubmit = async () => {
    const isPageValid = subSpecialitiesData.length >= 5
      ? Object.keys(rankedSubSpecialities).length === 5
      : Object.keys(rankedSubSpecialities).length === subSpecialitiesData.length
    
    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
    const navigateToPage = editFlag ? "/profile" : "/auth/register/modalities";
    
    if (!userToken) {
      notification.error("User Token is required");
      return;
    }

    setIsLoading(true);

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }

    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));

    const res = await dispatch(registerTherapistAction(rankedSubSpecialities, pageId!, userToken));
    setIsLoading(false);
  
    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values: rankedSubSpecialities }));
  
      if (editFlag) {
        navigate(navigateToPage);
        return;
      }

      if (navToInvalidPage) {
        const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
        
        if (nextInvalidPage) {
          navigate(`/auth/register/${nextInvalidPage}`);
        } else {
          // No invalid pages left, check if registration is complete
          const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
          
          if (registrationComplete && shouldShowRegCompleteDialog) {
            // Show completion dialog when all pages are valid
            dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
          } else {
            // Registration not complete but no specific invalid page identified
            navigate(navigateToPage);
          }
        }
      } else {
        navigate(navigateToPage);
      }
    }
  };

  return (
    <>
      <ToolbarComponent />
      <div className="px-4 sm:px-8 md:px-16 py-4 sm:py-8">
        <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">
          Rank Sub-Specialties
				</h1>       
       
        <h3 className="text-base sm:text-lg font-semibold text-therapy-blue-dark mt-2">
          Drag and drop the rankings to indicate your preferences, with 1 being the strongest.
        </h3>
      

        {showError && (subSpecialitiesData.length >= 5
          ? Object.keys(rankedSubSpecialities).length !== 5
          : Object.keys(rankedSubSpecialities).length !== subSpecialitiesData.length
        ) && (
					<span className="text-danger text-sm">Please rank the sub-specialties in the available slots</span>
				)}

        {!selectedSpecialities || Object.keys(selectedSpecialities).length === 0 ? (
          <div
            className="p-4 mb-4 mt-10 w-full max-w-[800px] text-therapy-blue border border-blue-300 rounded-lg bg-blue-50"
            role="alert"
          >
            <div className="flex items-center">
              <svg
                className="flex-shrink-0 w-4 h-4 me-2"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
              </svg>
              <h3 className="text-lg font-medium">Info</h3>
            </div>
            <div className="mt-2 mb-4 text-sm sm:text-md">
              Specialties should be selected first in order to rank their respective sub-specialties. Please select your
              preferred{" "}
              <Link to="/auth/register/specialization" className="font-bold hover:underline">
                Specialties
              </Link>
              .
            </div>
          </div>
        ) : (
          <div className="mt-6">
            <div className="specialities-drop-container flex flex-col xl:flex-row mt-6 items-start">
              <DragDropContext
                onDragEnd={(val) => {
                  const { destination, draggableId } = val
                  if (!destination?.droppableId || !draggableId || destination?.droppableId === "pre-selected-list")
                    return

                  setRankedSubSpecialities((prevRankedSpecialities) => {
                    const updatedSubSpecList = subSpecList.filter((opt) => opt.value !== draggableId)
                    const existingValue = prevRankedSpecialities[destination?.droppableId]

                    if (existingValue) {
                      const selectedSpecOption = subSpecialitiesData.find(
                        (spc) => spc.value.toString() === existingValue
                      )
                      if (selectedSpecOption) {
                        updatedSubSpecList.push(selectedSpecOption)
                      }
                    }
                    setSubSpecList([...updatedSubSpecList])

                    return {
                      ...prevRankedSpecialities,
                      [destination?.droppableId]: draggableId,
                    }
                  })
                }}
              >
                <Droppable droppableId="pre-selected-list" direction="vertical">
                  {(provided) => (
                    <ul
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className="spec-list grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2"
                    >
                      {subSpecList.map((spec, index) => (
                        <Draggable key={spec.value} draggableId={spec.value} index={index}>
                          {(provided) => (
                            <li
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className="dropped-item"
                            >
                              <i className="fa-solid fa-grip-vertical ml-5"></i>
                              <span>{spec.label}</span>
                            </li>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </ul>
                  )}
                </Droppable>

                {subSpecList.length > 0 && (
                 <div className="md:flex items-center justify-center mx-2">
                 <div className="transform rotate-90 xl:rotate-0 flex-shrink-0 lg:ml-6 lg:mr-[85px]">
                   <img
                     src="/spec-arrow.png"
                     alt="Arrow"
                     className="w-full h-auto"
                   />
                 </div>
               </div>
                )}

                <div className="drop-here-placeholder">
                  <div className="drop-title-bar text-white p-3 rounded-t-lg">
                    <h4 className="font-semibold">Top 5 Preferred Sub-Specialties</h4>
                  </div>

                  <ul className="drop-box-container">
                    {[...totalDroppableLoopNumbers].map((item, index) => (
                      <Droppable key={index} droppableId={item.toString()} direction="horizontal">
                        {(provided, snapshot) => (
                          <li
                            className={`${snapshot.isDraggingOver ? "about-to-drop" : ""} w-full sm:w-auto mb-2 sm:mb-0 sm:mr-2`}
                            ref={provided.innerRef}
                            {...provided.droppableProps}
                          >
                            {/* {item} */}
                            {/* {provided.placeholder} */}
                            {rankedSubSpecialities && rankedSubSpecialities[item.toString()] && (
                              <p className="dropped-item">
                                <span>
                                  {
                                    subSpecialitiesData.find(
                                      (opt) => opt.value === rankedSubSpecialities[item.toString()],
                                    )?.label
                                  }
                                </span>

                                <i
                                  onClick={() => {
                                    const currentSubSpec = subSpecialitiesData.find(
                                      (opt) => opt.value === rankedSubSpecialities[item.toString()],
                                    )
                                    setRankedSubSpecialities((prevRankedSpecialities) => {
                                      const newRankedSpecialities = {
                                        ...prevRankedSpecialities,
                                      }
                                      delete newRankedSpecialities[item.toString()]
                                      setSubSpecList([...subSpecList, currentSubSpec])
                                      return newRankedSpecialities
                                    })
                                  }}
                                  className="fa fa-times-square text-white end-icon"
                                ></i>
                              </p>
                            )}
                          </li>
                        )}
                      </Droppable>
                    ))}
                  </ul>
                </div>
              </DragDropContext>
            </div>

            <div className="mt-6 ml-4">
              <SaveButton
                disabled={
                  editFlag && (subSpecialitiesData.length >= 5
                    ? Object.keys(rankedSubSpecialities).length !== 5
                    : Object.keys(rankedSubSpecialities).length !== subSpecialitiesData.length)
                }
                type="button"
                value={`${editFlag ? "Save" : "Save & Continue"}`}
                loading={isLoading}
                onClick={(e) => {
                  e.preventDefault();
                  handlePageSubmit();
                }}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Page;
