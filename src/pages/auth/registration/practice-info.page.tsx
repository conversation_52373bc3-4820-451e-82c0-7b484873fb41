import 'react-phone-number-input/style.css'
import PhoneInput, { isPossiblePhoneNumber } from 'react-phone-number-input'
import SaveButton from '@/components/buttons/SaveButton'
import InputComponent from '@/components/forms/InputComponent'
import ToolbarComponent from '@/components/ToolbarComponent'
import type { AppDispatch, RootState } from '@/store/index'
import {
	updateRegistrationForm,
	updateRegistrationState,
} from '@/store/slicers/auth.slicer'
import { registerTherapistAction } from '@/store/slicers/therapist.slicer'
import { Formik } from 'formik'
import { type FC, Fragment, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import GooglePlacesAutocomplete, {
	geocodeByPlaceId,
} from 'react-google-places-autocomplete'
import notification from '@/utils/notification.util'
import NormalDialog from '@/components/dialogs/NormalDialog'
import { useOverlayTriggerState } from 'react-stately'
import { updateSidebarState } from '@/store/slicers/sidebar.slicer'
import { getNextInvalidPage, validateTherapistRegistration } from '@/utils/app.util'
import { handleFileUpload } from '@/utils/file-upload.util'

const Page: FC = () => {
	const navigate = useNavigate()
	const dispatch = useDispatch<AppDispatch>()
	const [uploadingPatientInfoSheet, setUploadingPatientInfoSheet] = useState<boolean>(false)
	const telehealthInfoState = useOverlayTriggerState({})
	const patientInfoRef = useRef<HTMLInputElement>(null)
	const fileInputref = useRef<HTMLInputElement>(null)
	const location = useLocation()
	const pageId = location.pathname.replace(/\/$/, '').split('/').pop()
	const authStore = useSelector((state: RootState) => state.auth)
	const currentPageContent = authStore.registration.formContent[pageId!]
	const registrationInfo = authStore.registration.formContent['create-account']
	const sidebarState = useSelector((state: RootState) => state.sidebar)
	const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

	const [uploadingPhoto, setUploadingPhoto] = useState<boolean>(false)
	const [isLoading, setIsLoading] = useState<boolean>(false)

	const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showErrors = invalidMenus.has(pageId!);

	const handlePageSubmit = async ({
		values,
		isPageValid,
	}: {
		values: any
		isPageValid: boolean
	}) => {
    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
    const navigateToPage = editFlag ? "/profile" : "/auth/register/normal-office-hours";

		if (!userToken) {
			notification.error('User Token is required')
			return
		}

    setIsLoading(true);

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }

    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));

		const res = await dispatch(registerTherapistAction(values, pageId!, userToken))
		setIsLoading(false);
		if (res?.status === 200 || res?.status === 201) {
			dispatch(updateRegistrationForm({ pageId, values }))

			if (editFlag) {
				navigate(navigateToPage);
				return;
			}

			if (navToInvalidPage) {
				const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
				
				if (nextInvalidPage) {
					navigate(`/auth/register/${nextInvalidPage}`);
				} else {
					// No invalid pages left, check if registration is complete
					const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
					
					if (registrationComplete && shouldShowRegCompleteDialog) {
						// Show completion dialog when all pages are valid
						dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
					} else {
						// Registration not complete but no specific invalid page identified
						navigate(navigateToPage);
					}
				}
			} else {
				navigate(navigateToPage);
			}
		}
	}

	return (
		<Fragment>
			<ToolbarComponent />
			<div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-8 max-w-full overflow-x-hidden">
				<h1 className="text-3xl font-semibold text-therapy-blue-dark">
					Practice Information
				</h1>
				<div className="mt-8 w-full max-w-md">
					<Formik
						validateOnMount={true}
						initialValues={{
							practice_name:
								currentPageContent && currentPageContent.practice_name,
							business_phone:
								currentPageContent && currentPageContent.business_phone,
							business_email: registrationInfo
								? registrationInfo.email
								: currentPageContent && currentPageContent.business_email,
							business_address:
								currentPageContent && currentPageContent.business_address,
							unit_no: currentPageContent && currentPageContent.unit_no,
							location_description:
								currentPageContent && currentPageContent.location_description,
							parking_information:
								currentPageContent && currentPageContent.parking_information,
							practice_photos: currentPageContent
								? currentPageContent.practice_photos
								: [],
							
							patient_info_sheet: currentPageContent
							? Array.isArray(currentPageContent.patient_info_sheet)
								? currentPageContent.patient_info_sheet
								: currentPageContent.patient_info_sheet
								? [currentPageContent.patient_info_sheet]
								: []
							: [],

						}}
						validate={(values) => {
							const errors = {} as any

							if (!values.practice_name)
								errors.practice_name = 'Practice name is required'
							if (!values.business_phone)
								errors.business_phone = 'Business phone is required'
							else if (!isPossiblePhoneNumber(values.business_phone))
								errors.business_phone = 'Input a valid phone number'
							if (!values.business_email)
								errors.business_email = 'Business email is required'
							else if (
								!/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(
									values.business_email
								)
							) {
								errors.business_email = 'Input a valid email address'
							}
							if (!values.business_address)
								errors.business_address = 'Business address is required'
							else if (!values.business_address.city || !values.business_address?.state)
								errors.business_address = 'City and state are required'
              if  (values.unit_no && !/^[a-zA-Z0-9\s-]+$/.test(values.unit_no)) {
                errors.unit_no = 'Unit / suite no must be alphanumeric'
              }
							return errors
						}}
						onSubmit={() => {}}
					>
						{({
							errors,
							values,
							isValidating,
							isValid,
							touched,
							setFieldTouched,
							setFieldValue
						}) => {
							return (
								<form>
									<InputComponent
										value={values.practice_name}
										errorMessage={
											(showErrors || touched.practice_name) &&
											errors.practice_name &&
											(errors.practice_name as string)
										}
										onChange={(val) => {
											if (!touched.practice_name) setFieldTouched('practice_name')
											setFieldValue('practice_name', val)
										}}
										onBlur={() => {
											if (!touched.practice_name) setFieldTouched('practice_name')
										}}
										class="mt-6"
										label="Practice Name"
										isRequired
										placeholder="Type here ..."
									/>

									<div className="w-full max-w-md mt-6">
										<p className="mb-2 text-sm text-gray-400">
											<span className="text-danger">*</span>Business Phone
										</p>

										<PhoneInput
											international
											countryCallingCodeEditable={false}
											defaultCountry="US"
											value={values.business_phone}
											onChange={(val) => {
												if (!touched.business_phone) setFieldTouched('business_phone')
												setFieldValue('business_phone', val)
											}}
											onBlur={() => {
												if (!touched.business_phone) setFieldTouched('business_phone')
											}}
											className="register-phone-input"
										/>
										{errors.business_phone && (showErrors || touched.business_phone) && (
											<div style={{ color: 'red', fontSize: 12, marginTop: 5 }}>
												{errors.business_phone.toString()}
											</div>
										)}
									</div>

									<InputComponent
										value={values.business_email}
										errorMessage={
											(showErrors || touched.business_email) &&
											errors.business_email &&
											(errors.business_email as string)
										}
										onChange={(val) => {
											if (!touched.business_email) setFieldTouched('business_email')
											setFieldValue('business_email', val)
										}}
										onBlur={() => {
											if (!touched.business_email) setFieldTouched('business_email')
										}}
										class="mt-6"
										label="Business Email"
										isRequired
										placeholder="Type here ..."
									/>

									<section className={`w-full mt-6`}>
										<label
											aria-label={''}
											htmlFor={'business-address'}
											className="text-sm text-gray-400"
										>
											<span className="text-danger">*</span>{' '}
											{'Business Address'}
										</label>
										<div className="w-full max-w-md">
											<GooglePlacesAutocomplete
												selectProps={{
													defaultInputValue:
														values.business_address?.description,
													placeholder: 'Type here ...',
													isClearable: true,
													onChange: (value) => {
														if (value && value.value) {
															geocodeByPlaceId(value.value.place_id).then(
																(result) => {
																	const {
																		address_components,
																		formatted_address,
																		geometry,
																		place_id,
																	} = result[0]

																	const streetNumber =
																		address_components.find((component) =>
																			component.types.includes('street_number')
																		)?.long_name || ''

																	const route =
																		address_components.find((component) =>
																			component.types.includes('route')
																		)?.long_name || ''

																	const street =
																		`${streetNumber} ${route}`.trim()

																	const addressData = {
																		street,

																		city:
																			address_components.find((component) =>
																				component.types.includes('locality')
																			)?.long_name || '',

																		country:
																			address_components.find((component) =>
																				component.types.includes('country')
																			)?.long_name || '',

																		full_address: formatted_address,
																		description: value.value.description,

																		zipCode:
																			address_components.find((component) =>
																				component.types.includes('postal_code')
																			)?.long_name || '',

																		lat: geometry.location.lat(),
																		lng: geometry.location.lng(),

																		place_id,

																		state:
																			address_components.find((component) =>
																				component.types.includes(
																					'administrative_area_level_1'
																				)
																			)?.long_name || '',
																	}
																	setFieldValue('business_address', addressData)
																}
															)
														} else {
															setFieldValue('business_address', null)
														}
													},
													onBlur: () => {
														setFieldTouched('business_address')
													},
													styles: {
														control: (baseStyles, state) => ({
															...baseStyles,
															border: '1px solid #D1D5DB',
															borderRadius: '0.75rem',
															backgroundColor: 'white',
															padding: '0.2rem',
															fontSize: '0.875rem',
															boxShadow: state.isFocused
																? '0 0 0 1px #547191'
																: 'none',
															'&:hover': {
																borderColor: '#d1d5db',
															},
														}),
														placeholder: (base) => ({
															...base,
															color: '#9CA3AF',
														}),
														clearIndicator: (base) => ({
															...base,
															cursor: 'pointer',
															transition: 'color 0.2s ease-in-out',
															'&:hover': {
																color: 'red',
															},
														}),
													},
												}}
												apiKey={import.meta.env.VITE_GOOGLE_MAP_KEY}
											/>
										</div>
										{errors?.business_address && (showErrors || touched.business_address) && (
											<div style={{ color: 'red', fontSize: 12 }}>
												{errors.business_address.toString()}
											</div>
										)}
									</section>

									<InputComponent
										value={values.unit_no}
										errorMessage={
											(showErrors || touched.unit_no) &&
											errors.unit_no &&
											(errors.unit_no as string)
										}
										onChange={(val) => {
											if (!touched.unit_no) setFieldTouched('unit_no')
                      const alphanumeric = val.replace(/[^a-zA-Z0-9\s-]/g, '')
											setFieldValue('unit_no', alphanumeric)
										}}
										onBlur={() => {
											if (!touched.unit_no) setFieldTouched('unit_no')
										}}
										class="mt-6"
										label="Unit / Suite Number"
										placeholder="Type here ..."
									/>

									<InputComponent
										value={values.location_description}
										errorMessage={
											(showErrors || touched.location_description) &&
											errors.location_description &&
											(errors.location_description as string)
										}
										onChange={(val) => {
											if (!touched.location_description) setFieldTouched('location_description')
											const alphanumericValue = val.replace(
												/[^a-zA-Z0-9 ]/g,
												''
											)
											setFieldValue('location_description', alphanumericValue)
										}}
										onBlur={() => {
											if (!touched.location_description) setFieldTouched('location_description')
										}}
										class="mt-6"
										label="Description of Location"
										placeholder="Type here ..."
									/>

									<InputComponent
										value={values.parking_information}
										errorMessage={
											(showErrors || touched.parking_information) &&
											errors.parking_information &&
											(errors.parking_information as string)
										}
										onChange={(val) => {
											if (!touched.parking_information) setFieldTouched('parking_information')
											const alphanumericValue = val.replace(
												/[^a-zA-Z0-9 ]/g,
												''
											)
											setFieldValue('parking_information', alphanumericValue)
										}}
										onBlur={() => {
											if (!touched.parking_information) setFieldTouched('parking_information')
										}}
										class="mt-6"
										label="Parking Information"
										placeholder="Type here ..."
									/>

									<div className="mt-6 w-full max-w-2xl">
										<input
											data-testid="practice-photo-upload"
											accept="image/png, image/gif, image/jpeg"
											onChange={async (e) => {
												const files = Array.from(e.target.files || []);
												if (files.length === 0) return;
												if ((files.length + values.practice_photos.length) > 4) {
													notification.error(
														`You can only upload 4 photos max.${values.practice_photos.length > 0 ? ` You have already uploaded ${values.practice_photos.length} photos.` : ''}`
													);
													return;
												}
												setUploadingPhoto(true);
												
												try {
													const uploadedFiles = await handleFileUpload(files);
													if (uploadedFiles && uploadedFiles?.length > 0) {
														const newUrls = uploadedFiles.map((f) => f.Location);
														const updatedPhotos = [...(values.practice_photos || []), ...newUrls];
														setFieldValue('practice_photos', updatedPhotos);

														if (!isValid) {
															dispatch(updateRegistrationForm({
																pageId,
																values: {
																	...values,
																	practice_photos: updatedPhotos,
																	isInvalid: true
																}
															}));
														} else {
															dispatch(updateRegistrationForm({
																pageId,
																values: {
																	...values,
																	practice_photos: updatedPhotos,
																}
															}));
														}
													}
												} finally {
													if (!touched.practice_photos) setFieldTouched('practice_photos');
													setUploadingPhoto(false);
												}
											}}
											type="file"
											multiple
											style={{ display: 'none' }}
											ref={fileInputref}
										/>
										<label className="text-sm mb-4 text-gray-400">
											Photos of Practice (Max 4) - JPEG, PNG, or GIF
										</label>

										<div className="flex flex-wrap mt-3 gap-4">
											{values.practice_photos?.map((pic: string, i: number) => {
												return (
													<div
														key={i}
														className="relative flex flex-row mt-1 gap-4 mr-4"
													>
														<div className="h-24 w-24 rounded-md border-2 bg-gray-200 flex items-center justify-center user_registration_profile cursor-pointer">
															<img
																src={pic || '/placeholder.svg'}
																alt=""
																className="h-full w-full object-cover rounded-md"
															/>
														</div>

														<span
															className="absolute -top-4 -right-3"
															onClick={() => {
																const updatedPhotos =
																	values.practice_photos.filter(
																		(_: string, index: number) => index !== i
																	)
																setFieldValue('practice_photos', updatedPhotos)
															}}
														>
															<i className="fa-solid fa-circle-xmark text-lg cursor-pointer text-therapy-blue-dark hover:text-red-500 transition-colors duration-200"></i>
														</span>
													</div>
												)
											})}

											{values.practice_photos?.length < 4 && (
												<div className="flex flex-row mt-1 gap-2">
													<div
														onClick={() => {
															fileInputref.current?.click()
														}}
														className={`h-24 w-24 rounded-md border-2 bg-gray-200 flex items-center justify-center user_registration_profile ${uploadingPhoto ? 'pointer-events-none' : 'cursor-pointer'}`}
													>
														{uploadingPhoto ? (
															<small>Uploading..</small>
														) : (
															<i className="fas fa-camera text-gray-400 text-2xl"></i>
														)}
													</div>
												</div>
											)}
										</div>
									</div>

									<div className="mt-6 w-full max-w-2xl">
										<input
											data-testid="patient-info-sheet-upload"
											accept="*"
											max={4}
											onChange={async (e) => {
												const files = Array.from(e.target.files || []);
												if (files.length === 0) return;
												if ((files.length + values.patient_info_sheet.length) > 4) {
													notification.error(
														`You can only upload 4 files max.${values.patient_info_sheet.length > 0 ? ` You have already uploaded ${values.patient_info_sheet.length} files.` : ''}`
													);
													return;
												}

												setUploadingPatientInfoSheet(true);
												
												try {
													const uploadedFiles = await handleFileUpload(files);
													if (uploadedFiles && uploadedFiles?.length > 0) {
														const newData = uploadedFiles.map((f) => ({
															location: f.Location,
															upload_name: f.originalName,
														}));
														const updatedFiles = [...(values.patient_info_sheet || []), ...newData];
														setFieldValue('patient_info_sheet', updatedFiles);

														if (!isValid) {
															dispatch(updateRegistrationForm({
																pageId,
																values: {
																	...values,
																	patient_info_sheet: updatedFiles,
																	isInvalid: true
																}
															}));
														} else {
															dispatch(updateRegistrationForm({
																pageId,
																values: {
																	...values,
																	patient_info_sheet: updatedFiles,
																}
															}));
														}
													}
												} finally {
													if (!touched.patient_info_sheet) setFieldTouched('patient_info_sheet')
													setUploadingPatientInfoSheet(false)
												}
											}}
											type="file"
											multiple
											style={{ display: 'none' }}
											ref={patientInfoRef}
										/>

										<label className="text-sm mb-4 text-gray-400">
											Patient Info Sheet - Any document type (Max 5MB per file)
										</label>

										<div className="flex flex-wrap mt-3 gap-4" data-testid="patient-info-sheet-upload-container">
											{values.patient_info_sheet?.map(
												(file: any, i: number) => {
													const isImage = file.upload_name?.match(/\.(jpeg|jpg|png|gif|bmp|webp)$/i)

													return (
														<div key={i} className="relative flex flex-row mt-1 gap-4 mr-4">
															<div className="h-24 w-24 rounded-md border-2 bg-gray-200 flex items-center justify-center user_registration_profile cursor-pointer">
																{isImage ? (
																	<img
																		src={file.location}
																		alt={file.upload_name}
																		className="h-full w-full object-cover rounded-md"
																	/>
																) : (
																	<div className="flex flex-col items-center justify-center text-gray-500">
																		<i className="fa-solid fa-file text-3xl"></i>
																		<span
																			data-testid="uploaded-filename"
																			className="text-xs text-center mt-1 px-2 w-24 truncate"
																			title={file.upload_name} // Shows full name on hover
																		>
																			{file.upload_name}
																		</span>
																	</div>
																)}
															</div>
											
															<span
																className="absolute -top-4 -right-3"
																onClick={() => {
																	const updatedFiles =
																		values.patient_info_sheet.filter(
																			(_: any, index: number) => index !== i
																		)
																	setFieldValue('patient_info_sheet', updatedFiles)
																}}
															>
																<i className="fa-solid fa-circle-xmark text-lg cursor-pointer text-therapy-blue-dark hover:text-red-500 transition-colors duration-200"></i>
															</span>
														</div>
													)
											})}

											{values.patient_info_sheet?.length < 4 && (
												<div className="flex flex-row mt-1 gap-2">
													<div
														onClick={() => patientInfoRef.current?.click()}
														className={`h-24 w-24 rounded-md border-2 bg-gray-200 flex items-center justify-center user_registration_profile ${
															uploadingPatientInfoSheet
																? 'pointer-events-none'
																: 'cursor-pointer'
														}`}
													>
														{uploadingPatientInfoSheet ? (
															<small>Uploading...</small>
														) : (
															<i className="fas fa-upload text-gray-400 text-2xl"></i>
														)}
													</div>
												</div>
											)}
										</div>

										{(showErrors || touched.patient_info_sheet) && errors.patient_info_sheet && (
											<p className="text-red-500 mt-2 ml-4">
												{errors.patient_info_sheet as string}
											</p>
										)}
									</div>

									<div className="mt-10 mb-6">
										<SaveButton
											disabled={isValidating || uploadingPhoto || uploadingPatientInfoSheet || (editFlag && !isValid)}
											loading={isLoading}
											value={`${editFlag ? "Save" : "Save & Continue"}`}
											type="button"
											onClick={(e) => {
												e.preventDefault();
												handlePageSubmit({
													values,
													isPageValid: isValid,
												});
											}}
										/>
									</div>
								</form>
							)
						}}
					</Formik>
				</div>
			</div>
			<NormalDialog
				contentCentered={true}
				state={telehealthInfoState}
				title="Note:"
				onClose={() => telehealthInfoState.close()}
			>
				<p className="text-sm">
					If you provide telehealth appointments you will need to have your own
					video service provider and provide any patients with the video link
					prior to your session.
				</p>
			</NormalDialog>
		</Fragment>
	)
}

export default Page
