import SaveButton from "@/components/buttons/SaveButton"
import AppCheckBoxComponent from "@/components/forms/AppCheckBoxComponent"
import ToolbarComponent from "@/components/ToolbarComponent"
import { modalities } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer"
import { registerTherapistAction } from "@/store/slicers/therapist.slicer"
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util"
import notification from "@/utils/notification.util"
import React, { type FC, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"

const Page: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const pageContent = authStore.registration.formContent[pageId!] || {};
  const sidebarState = useSelector((state: RootState) => state.sidebar);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

  const [modalitiesState, setModalitiesState] = useState<Record<string, string[]>>(pageContent);
  const [isLoading, setIsLoading] = useState<boolean>(false)
  
  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);

  const handlePageSubmit = async () => {
    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
    const navigateToPage = editFlag ? "/profile" : "/auth/register/practice-focus";

    if (!userToken) {
      notification.error("User Token is required")
      return
    }

    const res = await dispatch(registerTherapistAction(modalitiesState, pageId!, userToken))
    setIsLoading(false);

    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values: modalitiesState }))

      if (editFlag) {
        navigate(navigateToPage);
        return;
      }

      if (navToInvalidPage) {
        const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
        
        if (nextInvalidPage) {
          navigate(`/auth/register/${nextInvalidPage}`);
        } else {
          // No invalid pages left, check if registration is complete
          const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
          
          if (registrationComplete && shouldShowRegCompleteDialog) {
            // Show completion dialog when all pages are valid
            dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
          } else {
            // Registration not complete but no specific invalid page identified
            navigate(navigateToPage);
          }
        }
      } else {
        navigate(navigateToPage);
      }
    }
  }

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-8 md:px-16 py-8 max-w-full overflow-x-hidden">
        <h1 className="text-3xl font-semibold text-therapy-blue-dark">Modalities:</h1>

        {/* {showError && !validateModalities() && (
          <span className="text-danger text-sm mt-2">Please select at least one modality</span>
        )} */}
        <div className="mt-6 w-full">
          <form>
            <div className="grid grid-cols-[auto_auto_1fr] gap-x-4 gap-y-2 w-full">
              <div className="text-sm font-medium">Certified</div>
              <div className="text-sm font-medium">Trained</div>
              <div className="text-sm font-medium">Modality</div>

              {modalities.map((modality) => {
                return (
                  <React.Fragment key={modality.label}>
                    <div className="flex items-center">
                      <AppCheckBoxComponent
                        checked={
                          modalitiesState &&
                          modalitiesState[modality.label] &&
                          modalitiesState[modality.label].includes(`${modality.value}_certified`)
                        }
                        onChange={(val) => {
                          // if (!showError) setShowError(true)
                          setModalitiesState((prevState: any) => {
                            const currentValues = prevState[modality.label] || []
                            const isChecked = currentValues.includes(val)

                            const updatedValues = isChecked
                              ? currentValues.filter((v: any) => v !== val)
                              : [...currentValues, val]

                            let newState = {
                              ...prevState,
                            }

                            if (
                              !updatedValues.includes(`${modality.value}_certified`) &&
                              !updatedValues.includes(`${modality.value}_trained`)
                            ) {
                              delete newState[modality.label]
                            } else {
                              newState = {
                                ...prevState,
                                [modality.label]: updatedValues,
                              }
                            }

                            return newState
                          })
                        }}
                        value={`${modality.value}_certified`}
                        id={`${modality.value}_certified`}
                        name={`${modality.label}`}
                        label={""}
                      />
                    </div>
                    <div className="flex items-center">
                      <AppCheckBoxComponent
                        checked={
                          modalitiesState &&
                          modalitiesState[modality.label] &&
                          modalitiesState[modality.label].includes(`${modality.value}_trained`)
                        }
                        onChange={(val) => {
                          // if (!showError) setShowError(true)
                          setModalitiesState((prevState: any) => {
                            const currentValues = prevState[modality.label] || []
                            const isChecked = currentValues.includes(val)

                            const updatedValues = isChecked
                              ? currentValues.filter((v: any) => v !== val)
                              : [...currentValues, val]

                            let newState = {
                              ...prevState,
                            }

                            if (
                              !updatedValues.includes(`${modality.value}_certified`) &&
                              !updatedValues.includes(`${modality.value}_trained`)
                            ) {
                              delete newState[modality.label]
                            } else {
                              newState = {
                                ...prevState,
                                [modality.label]: updatedValues,
                              }
                            }

                            return newState
                          })
                        }}
                        value={`${modality.value}_trained`}
                        id={`${modality.value}_trained`}
                        name={`${modality.label}`}
                        label={""}
                      />
                    </div>
                    <div className="flex items-center">{modality.name}</div>
                  </React.Fragment>
                )
              })}
            </div>

            <div className="mt-5">
              <SaveButton
                // disabled={editFlag && !validateModalities()}
                type="button"
                value={`${editFlag ? "Save" : "Save & Continue"}`}
                loading={isLoading}
                onClick={(e) => {
                  e.preventDefault();
                  handlePageSubmit();
                }}
              />
            </div>
            <br />
          </form>
        </div>
      </div>
    </>
  )
}

export default Page

