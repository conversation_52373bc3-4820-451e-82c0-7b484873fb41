import SaveButton from "@/components/buttons/SaveButton";
import ToolbarComponent from "@/components/ToolbarComponent";
import { type FC, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
// import { updateRegistrationForm } from "@/store/slicers/auth.slicer";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch, RootState } from "@/store/index";
import {
  removeGoogleCalendarAction,
  removeOutlookCalendarAction,
  syncGoogleCalendarAction,
  syncOutlookCalendarAction,
} from "@/store/slicers/therapist.slicer";
import { generateURLEncodedString, createCodeChallenge, getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util";
import notification from "@/utils/notification.util";
import { useGoogleLogin } from "@react-oauth/google";
import { useOverlayTriggerState } from "react-stately";
import NormalDialog from "@/components/dialogs/NormalDialog";
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer";
import type { Calendar } from "@/types/user.interface";
import { updateSidebarState } from "@/store/slicers/sidebar.slicer";
import { LOGO_PATH } from "../../../../src/pages/auth/constants";

const defaultScope = [
  "https://www.googleapis.com/auth/userinfo.email",
  "https://www.googleapis.com/auth/userinfo.profile",
  "https://www.googleapis.com/auth/calendar",
];

const Page: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const pageContent = authStore.registration.formContent[pageId!] || {};
  const sidebarState = useSelector((state: RootState) => state.sidebar);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog } = authStore.registration;

  const [calendar, setCalendar] = useState<{ email: string, type: string } | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Calculate if both calendars are connected
  const hasGoogleCalendar = pageContent?.google_calendar ? true : false;
  const hasOutlookCalendar = pageContent?.outlook_calendar ? true : false;
  const hasBothCalendars = hasGoogleCalendar && hasOutlookCalendar;

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showError = invalidMenus.has(pageId!);

  const removeCalendarState = useOverlayTriggerState({});
  const infoState = useOverlayTriggerState({});

  const googleLogin = useGoogleLogin({
    flow: "auth-code",
    scope: defaultScope.join(" "),
    onSuccess: async (tokens) => {
      if (!userRegistrationToken) {
        notification.error("User Token is required");
        return;
      }
      const res = await dispatch(
        syncGoogleCalendarAction({
          code: tokens.code,
          userToken: userRegistrationToken,
        }),
      )
      if (res && res.status === 201) {
        const googleEmail = res.data.userData.calendars.find((cal: Calendar) => cal.type === "google")?.email
        dispatch(updateRegistrationForm({ pageId, values: { ...pageContent, google_calendar: googleEmail } }))
      }
    },
    onError: (error) => {
      console.log(error)
      if (error.error === "access_denied") return
      notification.error(error.error || "Something went wrong. Please try again.")
    },
  })

  const loginToOutlook = async () => {
    const authorizationUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize`;
    const clientId = import.meta.env.VITE_AZURE_CLIENT_ID;
    const redirectUri = `${import.meta.env.VITE_BASE_URL}/auth/register/calendar-add`;
    const scopes = 'openid profile offline_access User.Read Calendars.ReadWrite';
    const responseType = 'code';
    const prompt = 'select_account';
    const state = await generateURLEncodedString();
    localStorage.setItem('state', state);

    // Generate code verifier and challenge for PKCE
    const codeVerifier = await generateURLEncodedString();
    const codeChallenge = await createCodeChallenge(codeVerifier);

    // Store the code verifier in localStorage to use later for token exchange
    localStorage.setItem('codeVerifier', codeVerifier);

    // Build the authorization URL with the code challenge
    const authUrl = `${authorizationUrl}?client_id=${clientId}&response_type=${responseType}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scopes)}&response_mode=query&state=${state}&code_challenge=${codeChallenge}&code_challenge_method=S256&prompt=${prompt}`;

    // Redirect to Azure AD for login
    window.location.href = authUrl;
  }

  const exchangeCodeForToken = async (authorizationCode: string, state: string) => {
    const codeVerifier = localStorage.getItem('codeVerifier');
    if (!codeVerifier) {
      notification.error('Something went wrong. Please try Logging in again.');
      return;
    }
    const storedState = localStorage.getItem('state');
    if (storedState && state !== storedState) {
      notification.error('Something went wrong. Please try Logging in again.');
      return;
    }
    if (!userRegistrationToken) {
      notification.error('User Token is required');
      return;
    }
    notification.info('Syncing Outlook Calendar. Please wait...');
    const res = await dispatch(syncOutlookCalendarAction({
      authorizationCode,
      codeVerifier,
      userToken: userRegistrationToken,
      redirectPath: '/auth/register/calendar-add'
    }))
    if (res && res.status === 201) {
      const outlookEmail = res.data.userData.calendars.find((cal: Calendar) => cal.type === 'outlook')?.email;
      dispatch(updateRegistrationForm({ pageId, values: { ...pageContent, outlook_calendar: outlookEmail } }));
    }
  };

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const authorizationCode = params.get('code');
    const state = params.get('state');
    window.history.replaceState({}, document.title, window.location.pathname);
    if (authorizationCode && state) exchangeCodeForToken(authorizationCode, state);
  }, []);
  
  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-8 md:px-16 py-8">
        <h1 className="text-2xl md:text-3xl font-semibold text-therapy-blue-dark">
          {!pageContent?.google_calendar && !pageContent?.outlook_calendar
            ? "Connect Your Calendar"
            : "Congratulations, Your Calendar Has Been Successfully Added!"}
        </h1>

        <h1 className="text-base sm:text-lg font-semibold text-therapy-blue-dark flex items-center gap-2 flex-wrap">
          <span>Select your Email Hosting Provider</span>
          <i
            className="fa-solid fa-info-circle cursor-pointer"
            style={{ fontSize: "20px" }}
            onClick={() => infoState.open()}
             data-testid="info-icon"
          ></i>
        </h1>

        {showError && (!pageContent?.google_calendar && !pageContent?.outlook_calendar) && (
          <span className="text-danger text-sm">Please connect one of your calendars</span>
        )}

<div
  className={`mt-8 md:mt-14 flex ${
    pageContent.google_calendar || pageContent.outlook_calendar
      ? "flex-col gap-8"
      : "flex-row gap-4"
  }`}
>
          {pageContent.google_calendar ? (
            <div className="flex flex-col md:flex-row items-start md:items-center pb-4 mb-4 w-full">
              <div className="w-full md:w-[500px] flex items-center justify-between space-x-4 md:space-x-8 pb-4 md:pb-0 md:border-r-2 md:border-therapy-blue-dark text-therapy-blue-dark">
                <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="80"
                          height="80"
                          viewBox="0 0 48 48"
                        >
                          <rect width="22" height="22" x="13" y="13" fill="#fff"></rect>
                          <polygon
                            fill="#1e88e5"
                            points="25.68,20.92 26.688,22.36 28.272,21.208 28.272,29.56 30,29.56 30,18.616 28.56,18.616"
                          ></polygon>
                          <path
                            fill="#1e88e5"
                            d="M22.943,23.745c0.625-0.574,1.013-1.37,1.013-2.249c0-1.747-1.533-3.168-3.417-3.168 c-1.602,0-2.972,1.009-3.33,2.453l1.657,0.421c0.165-0.664,0.868-1.146,1.673-1.146c0.942,0,1.709,0.646,1.709,1.44 c0,0.794-0.767,1.44-1.709,1.44h-0.997v1.728h0.997c1.081,0,1.993,0.751,1.993,1.64c0,0.904-0.866,1.64-1.931,1.64 c-0.962,0-1.784-0.61-1.914-1.418L17,26.802c0.262,1.636,1.81,2.87,3.6,2.87c2.007,0,3.64-1.511,3.64-3.368 C24.24,25.281,23.736,24.363,22.943,23.745z"
                          ></path>
                          <polygon fill="#fbc02d" points="34,42 14,42 13,38 14,34 34,34 35,38"></polygon>
                          <polygon fill="#4caf50" points="38,35 42,34 42,14 38,13 34,14 34,34"></polygon>
                          <path fill="#1e88e5" d="M34,14l1-4l-1-4H9C7.343,6,6,7.343,6,9v25l4,1l4-1V14H34z"></path>
                          <polygon fill="#e53935" points="34,34 34,42 42,34"></polygon>
                          <path fill="#1565c0" d="M39,6h-5v8h8V9C42,7.343,40.657,6,39,6z"></path>
                          <path fill="#1565c0" d="M9,42h5v-8H6v5C6,40.657,7.343,42,9,42z"></path>
                        </svg>

                <i className="fa-solid fa-arrows-rotate text-3xl" style={{ marginLeft: "20px" }}></i>

                <img
                  src={LOGO_PATH}
                  alt="logo"
                  className="w-[25%]"
                  style={{ marginLeft: "-10px" , marginRight: "10px" }}
                />
              </div>

              <div className="flex flex-col md:flex-row md:items-center mt-4 md:mt-0 md:ml-6 space-y-2 md:space-y-0 md:space-x-4">
                <span className="text-therapy-blue-dark text-center md:text-left break-all">{pageContent.google_calendar}</span>
                <div className="flex space-x-2">
                  <button
                    className="bg-[#4285F4] text-white hover:bg-[#4285F4]/90 px-3 py-1 rounded"
                    onClick={googleLogin}
                  >
                    Change
                  </button>
                  <button
                    className={`bg-red-500 text-white px-3 py-1 rounded ${!hasBothCalendars ? "opacity-50 cursor-not-allowed" : ""}`}
                    disabled={!hasBothCalendars}
                    onClick={() => {
                      if (!hasBothCalendars) return;
                      setCalendar({ type: "google", email: pageContent.google_calendar })
                      removeCalendarState.open()
                    }}
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div onClick={googleLogin} className="w-fit cursor-pointer" data-testid="google-login-btn">
               <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="100"
                          height="100"
                          viewBox="0 0 48 48"
                          className="mx-auto lg:mx-0"
                        >
                          <rect width="22" height="22" x="13" y="13" fill="#fff"></rect>
                          <polygon
                            fill="#1e88e5"
                            points="25.68,20.92 26.688,22.36 28.272,21.208 28.272,29.56 30,29.56 30,18.616 28.56,18.616"
                          ></polygon>
                          <path
                            fill="#1e88e5"
                            d="M22.943,23.745c0.625-0.574,1.013-1.37,1.013-2.249c0-1.747-1.533-3.168-3.417-3.168 c-1.602,0-2.972,1.009-3.33,2.453l1.657,0.421c0.165-0.664,0.868-1.146,1.673-1.146c0.942,0,1.709,0.646,1.709,1.44 c0,0.794-0.767,1.44-1.709,1.44h-0.997v1.728h0.997c1.081,0,1.993,0.751,1.993,1.64c0,0.904-0.866,1.64-1.931,1.64 c-0.962,0-1.784-0.61-1.914-1.418L17,26.802c0.262,1.636,1.81,2.87,3.6,2.87c2.007,0,3.64-1.511,3.64-3.368 C24.24,25.281,23.736,24.363,22.943,23.745z"
                          ></path>
                          <polygon fill="#fbc02d" points="34,42 14,42 13,38 14,34 34,34 35,38"></polygon>
                          <polygon fill="#4caf50" points="38,35 42,34 42,14 38,13 34,14 34,34"></polygon>
                          <path fill="#1e88e5" d="M34,14l1-4l-1-4H9C7.343,6,6,7.343,6,9v25l4,1l4-1V14H34z"></path>
                          <polygon fill="#e53935" points="34,34 34,42 42,34"></polygon>
                          <path fill="#1565c0" d="M39,6h-5v8h8V9C42,7.343,40.657,6,39,6z"></path>
                          <path fill="#1565c0" d="M9,42h5v-8H6v5C6,40.657,7.343,42,9,42z"></path>
                        </svg>
            </div>
          )}

          {pageContent.outlook_calendar ? (
            <div className="flex flex-col md:flex-row items-start md:items-center pb-4 mb-4 w-full">
              <div className="w-full md:w-[500px] flex items-center justify-between space-x-4 md:space-x-8 pb-4 md:pb-0 md:border-r-2 md:border-therapy-blue-dark text-therapy-blue-dark">
                <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="80" height="80" viewBox="0 0 48 48">
                  <path
                    fill="#1976d2"
                    d="M28,13h14.533C43.343,13,44,13.657,44,14.467v19.066C44,34.343,43.343,35,42.533,35H28V13z"
                  ></path>
                  <rect width="14" height="15.542" x="28" y="17.958" fill="#fff"></rect>
                  <polygon fill="#1976d2" points="27,44 4,39.5 4,8.5 27,4"></polygon>
                  <path
                    fill="#fff"
                    d="M15.25,16.5c-3.176,0-5.75,3.358-5.75,7.5s2.574,7.5,5.75,7.5S21,28.142,21,24	S18.426,16.5,15.25,16.5z M15,28.5c-1.657,0-3-2.015-3-4.5s1.343-4.5,3-4.5s3,2.015,3,4.5S16.657,28.5,15,28.5z"
                  ></path>
                  <rect width="2.7" height="2.9" x="28.047" y="29.737" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="31.448" y="29.737" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="34.849" y="29.737" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="28.047" y="26.159" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="31.448" y="26.159" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="34.849" y="26.159" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="38.25" y="26.159" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="28.047" y="22.706" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="31.448" y="22.706" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="34.849" y="22.706" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="38.25" y="22.706" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="31.448" y="19.112" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="34.849" y="19.112" fill="#1976d2"></rect>
                  <rect width="2.7" height="2.9" x="38.25" y="19.112" fill="#1976d2"></rect>
                </svg>

                <i className="fa-solid fa-arrows-rotate text-3xl" style={{ marginLeft: "20px" }}></i>

                <img
                  src={LOGO_PATH}
                  alt="logo"
                  className="w-[25%]"
                  style={{ marginLeft: "-10px", marginRight: "10px" }}
                />
              </div>

              <div className="flex flex-col md:flex-row md:items-center mt-4 md:mt-0 md:ml-6 space-y-2 md:space-y-0 md:space-x-4">
                <span className="text-therapy-blue-dark text-center md:text-left break-all">{pageContent.outlook_calendar}</span>
                <div className="flex space-x-2">
                  <button
                    className="bg-[#4285F4] text-white hover:bg-[#4285F4]/90 px-3 py-1 rounded"
                    onClick={loginToOutlook}
                  >
                    Change
                  </button>
                  <button
                    className={`bg-red-500 text-white px-3 py-1 rounded ${!hasBothCalendars ? "opacity-50 cursor-not-allowed" : ""}`}
                    disabled={!hasBothCalendars}
                    onClick={() => {
                      if (!hasBothCalendars) return;
                      setCalendar({ type: "outlook", email: pageContent.outlook_calendar })
                      removeCalendarState.open()
                    }}
                  >
                    Remove
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div onClick={loginToOutlook} className="w-fit cursor-pointer outlook-login-selector">
              <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="100" height="100" viewBox="0 0 48 48">
                <path
                  fill="#103262"
                  d="M43.255,23.547l-6.81-3.967v11.594H44v-6.331C44,24.309,43.716,23.816,43.255,23.547z"
                ></path>
                <path fill="#0084d7" d="M13,10h10v9H13V10z"></path>
                <path fill="#33afec" d="M23,10h10v9H23V10z"></path>
                <path fill="#54daff" d="M33,10h10v9H33V10z"></path>
                <path fill="#027ad4" d="M23,19h10v9H23V19z"></path>
                <path fill="#0553a4" d="M23,28h10v9H23V28z"></path>
                <path fill="#25a2e5" d="M33,19h10v9H33V19z"></path>
                <path fill="#0262b8" d="M33,28h10v9H33V28z"></path>
                <polygon points="13,37 43,37 43,24.238 28.99,32.238 13,24.238" opacity=".019"></polygon>
                <polygon points="13,37 43,37 43,24.476 28.99,32.476 13,24.476" opacity=".038"></polygon>
                <polygon points="13,37 43,37 43,24.714 28.99,32.714 13,24.714" opacity=".057"></polygon>
                <polygon points="13,37 43,37 43,24.952 28.99,32.952 13,24.952" opacity=".076"></polygon>
                <polygon points="13,37 43,37 43,25.19 28.99,33.19 13,25.19" opacity=".095"></polygon>
                <polygon points="13,37 43,37 43,25.429 28.99,33.429 13,25.429" opacity=".114"></polygon>
                <polygon points="13,37 43,37 43,25.667 28.99,33.667 13,25.667" opacity=".133"></polygon>
                <polygon points="13,37 43,37 43,25.905 28.99,33.905 13,25.905" opacity=".152"></polygon>
                <polygon points="13,37 43,37 43,26.143 28.99,34.143 13,26.143" opacity=".171"></polygon>
                <polygon points="13,37 43,37 43,26.381 28.99,34.381 13,26.381" opacity=".191"></polygon>
                <polygon points="13,37 43,37 43,26.619 28.99,34.619 13,26.619" opacity=".209"></polygon>
                <polygon points="13,37 43,37 43,26.857 28.99,34.857 13,26.857" opacity=".229"></polygon>
                <polygon points="13,37 43,37 43,27.095 28.99,35.095 13,27.095" opacity=".248"></polygon>
                <polygon points="13,37 43,37 43,27.333 28.99,35.333 13,27.333" opacity=".267"></polygon>
                <polygon points="13,37 43,37 43,27.571 28.99,35.571 13,27.571" opacity=".286"></polygon>
                <polygon points="13,37 43,37 43,27.81 28.99,35.81 13,27.81" opacity=".305"></polygon>
                <polygon points="13,37 43,37 43,28.048 28.99,36.048 13,28.048" opacity=".324"></polygon>
                <polygon points="13,37 43,37 43,28.286 28.99,36.286 13,28.286" opacity=".343"></polygon>
                <polygon points="13,37 43,37 43,28.524 28.99,36.524 13,28.524" opacity=".362"></polygon>
                <polygon points="13,37 43,37 43,28.762 28.99,36.762 13,28.762" opacity=".381"></polygon>
                <polygon points="13,37 43,37 43,29 28.99,37 13,29" opacity=".4"></polygon>
                <linearGradient
                  id="Qf7015RosYe_HpjKeG0QTa_ut6gQeo5pNqf_gr1"
                  x1="38.925"
                  x2="32.286"
                  y1="24.557"
                  y2="36.024"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#31abec"></stop>
                  <stop offset="1" stopColor="#1582d5"></stop>
                </linearGradient>
                <path
                  fill="url(#Qf7015RosYe_HpjKeG0QTa_ut6gQeo5pNqf_gr1)"
                  d="M15.441,42h26.563c1.104,0,1.999-0.889,2-1.994C44.007,35.485,44,24.843,44,24.843	s-0.007,0.222-1.751,1.212S14.744,41.566,14.744,41.566S14.978,42,15.441,42z"
                ></path>
                <linearGradient
                  id="Qf7015RosYe_HpjKeG0QTb_ut6gQeo5pNqf_gr2"
                  x1="13.665"
                  x2="41.285"
                  y1="6.992"
                  y2="9.074"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset=".042" stopColor="#076db4"></stop>
                  <stop offset=".85" stopColor="#0461af"></stop>
                </linearGradient>
                <path
                  fill="url(#Qf7015RosYe_HpjKeG0QTb_ut6gQeo5pNqf_gr2)"
                  d="M43,10H13V8c0-1.105,0.895-2,2-2h26c1.105,0,2,0.895,2,2V10z"
                ></path>
                <linearGradient
                  id="Qf7015RosYe_HpjKeG0QTc_ut6gQeo5pNqf_gr3"
                  x1="28.153"
                  x2="23.638"
                  y1="33.218"
                  y2="41.1"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#33acee"></stop>
                  <stop offset="1" stopColor="#1b8edf"></stop>
                </linearGradient>
                <path
                  fill="url(#Qf7015RosYe_HpjKeG0QTc_ut6gQeo5pNqf_gr3)"
                  d="M13,25v15c0,1.105,0.895,2,2,2h15h12.004c0.462,0,0.883-0.162,1.221-0.425L13,25z"
                ></path>
                <path
                  d="M21.319,13H13v24h8.319C23.352,37,25,35.352,25,33.319V16.681C25,14.648,23.352,13,21.319,13z"
                  opacity=".05"
                ></path>
                <path
                  d="M21.213,36H13V13.333h8.213c1.724,0,3.121,1.397,3.121,3.121v16.425	C24.333,34.603,22.936,36,21.213,36z"
                  opacity=".07"
                ></path>
                <path
                  d="M21.106,35H13V13.667h8.106c1.414,0,2.56,1.146,2.56,2.56V32.44C23.667,33.854,22.52,35,21.106,35z"
                  opacity=".09"
                ></path>
                <linearGradient
                  id="Qf7015RosYe_HpjKeG0QTd_ut6gQeo5pNqf_gr4"
                  x1="3.53"
                  x2="22.41"
                  y1="14.53"
                  y2="33.41"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop offset="0" stopColor="#1784d8"></stop>
                  <stop offset="1" stopColor="#0864c5"></stop>
                </linearGradient>
                <path
                  fill="url(#Qf7015RosYe_HpjKeG0QTd_ut6gQeo5pNqf_gr4)"
                  d="M21,34H5c-1.105,0-2-0.895-2-2V16c0-1.105,0.895-2,2-2h16c1.105,0,2,0.895,2,2v16	C23,33.105,22.105,34,21,34z"
                ></path>
                <path
                  fill="#fff"
                  d="M13,18.691c-3.111,0-4.985,2.377-4.985,5.309S9.882,29.309,13,29.309	c3.119,0,4.985-2.377,4.985-5.308C17.985,21.068,16.111,18.691,13,18.691z M13,27.517c-1.765,0-2.82-1.574-2.82-3.516
	s1.06-3.516,2.82-3.516s2.821,1.575,2.821,3.516S14.764,27.517,13,27.517z"
                ></path>
              </svg>
            </div>
          )}
        </div>

        {calendar && (
          <NormalDialog
            state={removeCalendarState}
            title={`Remove ${calendar.type === 'google' ? 'Google' : 'Outlook'} Calendar`}
            confirmLabel={'Confirm'}
            verifyText={'CONFIRM'}
            loading={loading}
            onCancel={() => {
              removeCalendarState.close();
              setCalendar(null);
            }}
            onAccept={async () => {
              if (!userRegistrationToken) {
                notification.error('User Token is required');
                return;
              }
              setLoading(true);
              const res = calendar.type === 'google' ?
                await dispatch(removeGoogleCalendarAction(userRegistrationToken))
                : await dispatch(removeOutlookCalendarAction(userRegistrationToken));
              if (res && res.status === 200) {
                removeCalendarState.close();

                const calKey = calendar.type === 'google' ? 'google_calendar' : 'outlook_calendar';
                dispatch(
                  updateRegistrationForm({
                    pageId,
                    values: {
                      ...pageContent,
                      [calKey]: null,
                    },
                  })
                );
                setCalendar(null);
              }
              setLoading(false);
            }}
            primaryButtonColor={true}
          >
            <p className="text-sm">
              Are you sure you want to <strong>remove</strong> this {calendar.type === 'google' ? 'Google' : 'Outlook'} Calendar?{' '}
              <strong>{calendar.email}</strong>
            </p>
          </NormalDialog>
        )}

        <div className="mt-16 md:mt-[200px]">
          <SaveButton
            // disabled={loading || !pageContent || (!pageContent?.google_calendar && !pageContent?.outlook_calendar)}
            loading={loading}
            onClick={async (e) => {
              e.preventDefault()

              if (!pageContent?.google_calendar && !pageContent?.outlook_calendar) {
                invalidMenus.add(pageId!);
              } else {
                invalidMenus.delete(pageId!);
              }

              dispatch(updateSidebarState({
                key: "registerTherapist",
                value: {
                  ...sidebarState.registerTherapist,
                  invalidMenus: Array.from(invalidMenus)
                }
              }));

              if (!userRegistrationToken) {
                notification.error("User Token is required")
                return
              }

              if (navToInvalidPage) {
                const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
                
                if (nextInvalidPage) {
                  navigate(`/auth/register/${nextInvalidPage}`);
                } else {
                  // No invalid pages left, check if registration is complete
                  const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
                  
                  if (registrationComplete && shouldShowRegCompleteDialog) {
                    // Show completion dialog when all pages are valid
                    dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
                  } else {
                    // Registration not complete but no specific invalid page identified
                    navigate("/auth/register/payment-forms");
                  }
                }
              } else {
                navigate("/auth/register/payment-forms");
              }
            }}
            value="Save & Continue"
          />
        </div>
      </div>

      <NormalDialog
        opacity={80}
        state={infoState}
        title="Note"
        onClose={() => infoState.close()}
        width="w-[90vw] sm:w-[500px] md:w-[600px] lg:w-[700px]"
        titleCentered={true}
      >
        <p className="text-xs sm:text-sm">
          Your work email (from your work domain, e.g. <strong>nexttherapist.com</strong>) is connected to an Email Hosting Provider.
          Most companies connect their work domain to either a <strong>Google</strong> Workspace or <strong>Microsoft</strong> 365/Outlook
          email hosting provider. In some cases therapy practices may simply use a <strong>@gmail.com</strong> or
          <strong>@outlook.com</strong> (<strong>@live.com</strong>, <strong>@hotmail.com</strong>, <strong>@msn.com</strong>).
          In any of these cases you can easily connect <strong>NextTherapist</strong> to your work calendar. If you do not use a
          <strong> Google</strong> or <strong>Microsoft</strong> email hosting provider <strong>NextTherapist</strong> currently
          cannot connect with your calendar. If you start using one of these email hosting providers in the future please come back!
          <br />
          <br />
          Remember that in order for us to allow clients to see your availability and book appointments with you, we need you to
          connect your calendar with <strong>NextTherapist</strong>. This process maintains your and your client's privacy and
          the integrity of your calendar as follows:
          <br />
          <br />
          <strong>NextTherapist</strong> will only use this access to see your availability and book appointments with clients via our platform
          <br />
          <br />
          <strong>NextTherapist</strong> can only see if time in your calendar is "busy" or "free", but does not allow us to see any of the details of your calendar appointments
          <br />
          <br />
          <strong>NextTherapist</strong> cannot delete or move any items in your calendar (other than those booked directly by us)
          <br />
          <br />
          <strong>NextTherapist</strong> will not have any administrative access to your calendar or calendar data 
        </p>
      </NormalDialog>
    </>
  );
};

export default Page;