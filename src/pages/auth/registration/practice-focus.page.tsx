import SaveButton from "@/components/buttons/SaveButton"
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent"
import AppRadioComponent from "@/components/forms/AppRadioComponent"
import InputComponent from "@/components/forms/InputComponent"
import ToolbarComponent from "@/components/ToolbarComponent"
import { areaOfFocus, genders, practiceFocus, religions } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer"
import { updateSidebarState } from "@/store/slicers/sidebar.slicer"
import { registerTherapistAction } from "@/store/slicers/therapist.slicer"
import notification from "@/utils/notification.util"
import { Formik } from "formik"
import { useState, type FC } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util"

const Page: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();

  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const currentPageContent = authStore.registration.formContent[pageId!];
  const sidebarState = useSelector((state: RootState) => state.sidebar);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showErrors = invalidMenus.has(pageId!);

  const handlePageSubmit = async ({
    values,
    isPageValid,
  }: {
    values: any;
    isPageValid: boolean;
  }) => {
    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
    const navigateToPage = editFlag ? "/profile" : "/auth/register/practice-info";

    if (!userToken) {
      notification.error("User Token is required")
      return
    }

    setIsLoading(true);

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }

    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));

    const res = await dispatch(registerTherapistAction(values, pageId!, userToken))
    setIsLoading(false);
    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values }))

      if (editFlag) {
        navigate(navigateToPage);
        return;
      }

      if (navToInvalidPage) {
        const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
        
        if (nextInvalidPage) {
          navigate(`/auth/register/${nextInvalidPage}`);
        } else {
          // No invalid pages left, check if registration is complete
          const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
          
          if (registrationComplete && shouldShowRegCompleteDialog) {
            // Show completion dialog when all pages are valid
            dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
          } else {
            // Registration not complete but no specific invalid page identified
            navigate(navigateToPage);
          }
        }
      } else {
        navigate(navigateToPage);
      }
    }
  }

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-8">
        <h1 className="text-3xl font-semibold text-therapy-blue-dark">Practice Focus</h1>
        <div className="mt-8">
          <Formik
            validateOnMount={true}
            initialValues={{
              client_served: currentPageContent?.client_served,
              client_preferred: currentPageContent?.client_preferred,
              genders: currentPageContent?.genders,
              religious_specialization: currentPageContent?.religious_specialization,
              additional_focus: currentPageContent?.additional_focus,
            }}
            validate={(values) => {
              const errors = {} as any
              if (!values.client_served || values.client_served.length === 0)
                errors.client_served = "Clients Served is required"
              if (!values.client_preferred) errors.client_preferred = "Preferred Client is required"
              if (!values.genders || values.genders.length === 0) errors.genders = "Genders You Work With is required"
              if (!values.religious_specialization || values.religious_specialization?.checked_list?.length === 0)
                errors.religious_specialization = "Religious Specialization is required"
              else if (
                values.religious_specialization?.checked_list?.includes("other") &&
                !values.religious_specialization?.other_religion
              )
                errors.religious_specialization = "Specify other Religion"
              if (!values.additional_focus || values.additional_focus?.checked_list?.length === 0)
                errors.additional_focus = "Additional Focus is required"
              else if (
                values.additional_focus?.checked_list?.includes("other") &&
                !values.additional_focus?.other_focus
              )
                errors.additional_focus = "Specify other area of focus"

              return errors
            }}
            onSubmit={() => {}}
          >
            {({ errors, values, isValidating, isValid, setValues, setFieldValue, setFieldTouched, touched }) => {
              const isNotSaySelected = values.religious_specialization?.checked_list.includes("not-say")
              const updatedReligions = religions
                .filter((g) => !["all", "other"].includes(g.value.toString()))
                .map((item) => ({
                  ...item,
                  disabled: isNotSaySelected,
                }))

              const handleReligionChange = (val: string[]) => {
                setFieldTouched("religious_specialization");
                let newList = val

                if (val.includes("not-say")) {
                  newList = ["not-say"]
                } else if (isNotSaySelected && !val.includes("not-say")) {
                  newList = val.filter((v) => v !== "not-say")
                }
                setFieldValue(
                  "religious_specialization",
                  {
                    ...values.religious_specialization,
                    checked_list: newList,
                    other_religion: newList.includes("other") ? values.religious_specialization?.other_religion : ""
                  }
                );
              }

              // Determine if "None of the Above" is selected in additional focus
              const isNoneSelected = values.additional_focus?.checked_list?.includes("none")

              // Create modified area of focus list with disabled property based on "None" selection
              const updatedAreaOfFocus = areaOfFocus.map(item => ({
                ...item,
                disabled: isNoneSelected && item.value !== "none"
              }))

              // Handle changes to the additional focus checkboxes
              const handleAdditionalFocusChange = (val: string[]) => {
                setFieldTouched("additional_focus");
                let newList = [...val]
                
                // If "None of the Above" was just selected
                if (newList.includes("none") && !values.additional_focus?.checked_list.includes("none")) {
                  newList = ["none"] // Only keep "None of the Above" selected
                } 
                // If any other option was selected while "None of the Above" was already selected
                else if (values.additional_focus?.checked_list.includes("none") && newList.length > 1) {
                  newList = newList.filter(v => v !== "none") // Remove "None of the Above"
                }

                setFieldValue(
                  "additional_focus",
                  { 
                    ...values.additional_focus,
                    checked_list: newList,
                    other_focus: newList.includes("other") ? values.additional_focus?.other_focus : ""
                  }
                );
              }

              return (
                <form>
                  <div className="radio-register mt-5 max-w-full overflow-x-hidden">
                    <h3 className="text-2xl font-semibold text-therapy-blue-dark mb-5">Clients Served</h3>
                    <AppCheckBoxListComponent
                      checked={values.client_served}
                      onChange={(val) => {
                        setFieldTouched("client_served")
                        // Determine if we need to update client_preferred
                        const newClientPreferred = val.includes(values.client_preferred)
                          ? values.client_preferred
                          : null

                        // Update both fields at once
                        setValues({
                          ...values,
                          client_served: val,
                          client_preferred: newClientPreferred,
                        })
                      }}
                      checkList={practiceFocus}
                    />
                    <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                      {(showErrors || touched.client_served) && errors.client_served && errors.client_served.toString()}
                    </div>
                  </div>

                  <br />
                  <div className="radio-register mt-5 max-w-full overflow-x-hidden">
                    <h3 className="text-2xl font-semibold text-therapy-blue-dark mb-5">Preferred Client</h3>
                    <AppRadioComponent
                      checked={values.client_preferred}
                      onChange={(val) => {
                        setFieldTouched("client_preferred")
                        setFieldValue("client_preferred", val)
                      }}
                      checkList={practiceFocus.map((item) => ({
                        ...item,
                        id: `${item.id}_radio`,
                        disabled: !values.client_served?.includes(item.value),
                      }))}
                    />
                    <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                      {(showErrors || touched.client_preferred) && errors.client_preferred && errors.client_preferred.toString()}
                    </div>
                  </div>

                  <br />
                  <div className="radio-register mt-5 max-w-full overflow-x-hidden">
                    <h3 className="text-2xl font-semibold text-therapy-blue-dark mb-5">Genders I Work With</h3>
                    <AppCheckBoxListComponent
                      checked={values.genders}
                      onChange={(val) => {
                        setFieldTouched("genders")
                        let updatedGenders: string[] = []
                        const allGenders = genders.map((item) => item.value) as string[]
                        if (val.includes("all")) {
                          updatedGenders = [...allGenders]
                          if (val.length < allGenders.length && val[val.length - 1] !== "all") {
                            updatedGenders = [...val]
                            updatedGenders.splice(updatedGenders.indexOf("all"), 1)
                          }
                        } else if (
                          val.length === allGenders.length - 1 &&
                          !val.includes("all") &&
                          values.genders.includes("all") &&
                          !val.includes("all")
                        ) {
                          updatedGenders = []
                        } else if (val.length + 1 === allGenders.length) {
                          updatedGenders = [...allGenders]
                        } else if (val.length < allGenders.length) {
                          updatedGenders = [...val]
                        }
                        setFieldValue("genders", updatedGenders)
                      }}
                      checkList={genders}
                    />
                    <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                      {(showErrors || touched.genders) && errors.genders && errors.genders.toString()}
                    </div>
                  </div>

                  <div className="radio-register mt-5 max-w-full overflow-x-hidden">
                    <h3 className="text-2xl font-semibold text-therapy-blue-dark mb-5">Religious Specialization</h3>

                    <AppCheckBoxListComponent
                      checked={values.religious_specialization?.checked_list}
                      onChange={handleReligionChange}
                      checkList={updatedReligions}
                    />

                    <AppCheckBoxListComponent
                      checked={values.religious_specialization?.checked_list}
                      onChange={handleReligionChange}
                      checkList={[
                        {
                          label: "Other (Please specify)",
                          value: "other",
                          id: "other-religion",
                          disabled: isNotSaySelected,
                        },
                      ]}
                    />

                    {/* Input below Other */}
                    {values?.religious_specialization?.checked_list.includes("other") && (
                      <div className="w-full max-w-[300px] pl-5">
                        <InputComponent
                          value={values.religious_specialization.other_religion}
                          errorMessage={(showErrors || touched.religious_specialization) && errors.religious_specialization as string}
                          onChange={(val) => {
                            setFieldValue("religious_specialization", { ...values.religious_specialization, other_religion: val })
                          }}
                        />
                      </div>
                    )}

                    <AppCheckBoxListComponent
                      checked={values.religious_specialization?.checked_list}
                      onChange={handleReligionChange}
                      checkList={[
                        { label: "None of the above", value: "not-say", id: "not-say" },
                      ]}
                    />

                    <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                      {(showErrors || touched.religious_specialization) && errors.religious_specialization &&
                        !values.religious_specialization?.checked_list.includes("other") &&
                        errors.religious_specialization.toString()}
                    </div>
                  </div>

                  {/* Additional Area of Focus */}
                  <div className="radio-register mt-5 max-w-full overflow-x-hidden">
                    <h3 className="text-2xl font-semibold text-therapy-blue-dark mb-5">Additional Areas of Focus</h3>
                    
                    {/* Non-None Options */}
                    <AppCheckBoxListComponent
                      checked={values.additional_focus?.checked_list}
                      onChange={handleAdditionalFocusChange}
                      checkList={updatedAreaOfFocus.filter(item => item.value !== "none")}
                    />

                    {/* Input for "Other" option */}
                    {values?.additional_focus?.checked_list.includes("other") && (
                      <div className="w-full max-w-[300px] pl-5">
                        <InputComponent
                          value={values.additional_focus.other_focus}
                          errorMessage={(showErrors || touched.additional_focus) && errors.additional_focus as string}
                          onChange={(val) => {
                            setFieldValue("additional_focus", { ...values.additional_focus, other_focus: val })
                          }}
                          class="mb-5"
                        />
                      </div>
                    )}

                    {/* None of the Above Option */}
                    <AppCheckBoxListComponent
                      checked={values.additional_focus?.checked_list}
                      onChange={handleAdditionalFocusChange}
                      checkList={updatedAreaOfFocus.filter(item => item.value === "none")}
                    />

                    <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                      {(showErrors || touched.additional_focus) && errors.additional_focus &&
                        !values.additional_focus?.checked_list.includes("other") &&
                        errors.additional_focus.toString()}
                    </div>
                  </div>

                  <br />
                  <div className="mt-5">
                    <SaveButton
                      disabled={isValidating || (editFlag && !isValid)}
                      type="button"
                      value={`${editFlag ? "Save" : "Save & Continue"}`}
                      loading={isLoading}
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageSubmit({
                          values,
                          isPageValid: isValid,
                        });
                      }}
                    />
                  </div>

                  <br />
                </form>
              )
            }}
          </Formik>
        </div>
      </div>
    </>
  )
}

export default Page

