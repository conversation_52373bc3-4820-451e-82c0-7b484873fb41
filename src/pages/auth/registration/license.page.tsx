import "react-phone-number-input/style.css";
import SaveButton from "@/components/buttons/SaveButton";
import DateComponent from "@/components/forms/DateComponent";
import InputComponent from "@/components/forms/InputComponent";
import SelectComponent from "@/components/forms/SelectComponent";
import ToolbarComponent from "@/components/ToolbarComponent";
import { licenseTypeIndependent } from "@/configs/registration.configs";
// import { states } from "@/configs/states.configs";
import type { AppDispatch, RootState } from "@/store/index";
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import notification from "@/utils/notification.util";
import { FieldArray, Formik } from "formik";
import { type FC, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import PhoneInput, { isPossiblePhoneNumber } from "react-phone-number-input";
import { updateSidebarState } from "@/store/slicers/sidebar.slicer";
import { NPI_LENGTH_ERROR, CANT_START_WITH_ZERO, NPI_NOT_ALL_ZEROS } from "../constants";
import { request } from "@/utils/request.utils";
import { useEffect } from "react";
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util";
import { handleFileUpload } from "@/utils/file-upload.util";

const LicensePage: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();

  const authStore = useSelector((state: RootState) => state.auth);
  const formContent = authStore.registration.formContent[pageId!] || {};
  const sidebarState = useSelector((state: RootState) => state.sidebar);
  const [states, setStates] = useState<{ name: string; abbrev: string }[]>([]);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog, editFlag } = authStore.registration;

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [uploadingLicense, setUploadingLicense] = useState<Record<number, boolean>>({});

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showErrors = invalidMenus.has(pageId!);

  const validateDate = (inputValue: string, allowFuture: boolean = false): string => {
    const [month, day, year] = inputValue.split("-").map(Number);
    const currentDate = new Date();

    if (month > 12 || month < 1) {
      return "Invalid date";
    }
    
    if (day > 31 || day < 1) {
      return "Invalid date";
    }

    if (!year || year.toString().length !== 4) {
      return "Invalid date";
    }

    // Create date object from input (month-1 because JS months are 0-based)
    const inputDate = new Date(year, month - 1, day);
    
    // Check if it's a future date
    if (inputDate > currentDate && !allowFuture) {
      return "Date cannot be in the future.";
    }

    const daysInMonth = new Date(year, month, 0).getDate();
    if (day > daysInMonth) {
      return "Invalid date";
    }

    return '';
  };

  const handlePageSubmit = async ({
    values,
    isPageValid,
  }: {
    values: any;
    isPageValid: boolean;
  }) => {
    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
    const navigateToPage = editFlag ? "/profile" : "/auth/register/specialization";

    if (!userToken) {
      notification.error("User Token is required");
      return;
    }
    setIsLoading(true);

    if (!isPageValid) {
      invalidMenus.add(pageId!);
    } else {
      invalidMenus.delete(pageId!);
    }

    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(invalidMenus)
      }
    }));
    const res = await dispatch(registerTherapistAction(values, pageId!, userToken));
    setIsLoading(false);
    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values }));
  
      if (editFlag) {
        navigate(navigateToPage);
        return;
      }

      if (navToInvalidPage) {
        const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
        
        if (nextInvalidPage) {
          navigate(`/auth/register/${nextInvalidPage}`);
        } else {
          // No invalid pages left, check if registration is complete
          const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
          
          if (registrationComplete && shouldShowRegCompleteDialog) {
            // Show completion dialog when all pages are valid
            dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
          } else {
            // Registration not complete but no specific invalid page identified
            navigate(navigateToPage);
          }
        }
      } else {
        navigate(navigateToPage);
      }
    }
  };

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const response = await request.get("/states");
        setStates(response.data);
      } catch (error) {
        console.error("Failed to fetch states:", error);
      }
    };

    fetchStates();
  }, []);

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-4 sm:py-6 md:py-8">
        <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark">Your License(s)</h1>
        <div className="mt-8">
          <Formik
            initialValues={{
              licenses: formContent.licenses || [
                {
                  license_type: "",
                  license_state: "",
                  issuing_board: "",
                  license_number: "",
                  issue_date: "",
                  expiry_date: "",
                  license_verification: [],
                  license_status: "",
                  clinical_supervisor_name: "",
                  clinical_supervisor_email: "",
                  clinical_supervisor_phone: "",
                  probation_or_disciplinary_action: "",
                  disciplinary_action_description: "",
                },
              ],
              npi_number: formContent.npi_number || "",
              compact_licenses: formContent.compact_licenses || [
                {
                  compact_privilege: "",
                  compact_name: "",
                  compact_identification_number: "",
                  compact_states: [],
                }
              ],
            }}
            validate={(values) => {
              const errors = {} as any;
              values.licenses.forEach((license: any, index: number) => {
                const licenseErrors: any = {};
                if (!license.license_type) licenseErrors.license_type = "License type is required";
                if (!license.license_state) licenseErrors.license_state = "State is required";
                if (!license.issuing_board) licenseErrors.issuing_board = "Board is required";
                if (!license.license_number) licenseErrors.license_number = "License number is required";
                if (!license.issue_date) licenseErrors.issue_date = "Issue date is required";
                else {
                  const dateError = validateDate(license.issue_date);
                  if (dateError) licenseErrors.issue_date = dateError;
                }
                if (!license.expiry_date) {
                  licenseErrors.expiry_date = "Expiry date is required";
                } else {
                  const dateError = validateDate(license.expiry_date, true);
                  if (dateError) {
                    licenseErrors.expiry_date = dateError;
                  } else {
                    const [month, day, year] = license.expiry_date.split("-").map(Number);
                    const currentDate = new Date();
                    const expiryDate = new Date(year, month - 1, day);
            
                    if (expiryDate < currentDate) {
                      licenseErrors.expiry_date = "Your license appears to be expired. Please renew before applying to NextTherapist";
                    }
                  }
                }                
                
                if (!license.license_status) licenseErrors.license_status = "License status is required";
                else if (license.license_status.value === "suspended") {
                  licenseErrors.license_status = "Must have a non-suspended license to apply to NextTherapist";
                }
                else if (license.license_status.value === "provisional") {
                  if (!license.clinical_supervisor_name) licenseErrors.clinical_supervisor_name = "Clinical supervisor name is required";
                  if (!license.clinical_supervisor_email) licenseErrors.clinical_supervisor_email = "Clinical supervisor email is required";
                  else if (!/^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/.test(license.clinical_supervisor_email)) {
                    licenseErrors.clinical_supervisor_email = "Clinical supervisor email is invalid";
                  }
                  if (!license.clinical_supervisor_phone) licenseErrors.clinical_supervisor_phone = "Clinical supervisor phone is required";
                  else if (!isPossiblePhoneNumber(license.clinical_supervisor_phone)) licenseErrors.clinical_supervisor_phone = "Clinical supervisor phone is invalid";
                }

                if (!license.probation_or_disciplinary_action.value) licenseErrors.probation_or_disciplinary_action = "Probation or disciplinary action is required";
                if (license.probation_or_disciplinary_action.value === "yes") {
                  if (!license.disciplinary_action_description) licenseErrors.disciplinary_action_description = "Disciplinary action description is required";
                }

                if (Object.keys(licenseErrors).length > 0) {
                  errors.licenses = errors.licenses || [];
                  errors.licenses[index] = licenseErrors;
                }
              }
              );

              if (!values.npi_number) {
                errors.npi_number = "NPI number is required";
              } else {
                const errorMessages: string[] = [];
              
                if (values.npi_number.startsWith("0")) {
                  errorMessages.push(CANT_START_WITH_ZERO);
                }
              
                if (/^0+$/.test(values.npi_number)) {
                  errorMessages.push(NPI_NOT_ALL_ZEROS);
                }
              
                if (values.npi_number.length !== 10) {
                  errorMessages.push(NPI_LENGTH_ERROR);
                }
              
                if (errorMessages.length > 0) {
                  errors.npi_number = errorMessages.join(", ");
                }
              }
              values.compact_licenses.forEach((compact: any, index: number) => {
                const compactErrors: any = {};
                if (compact.compact_privilege?.value === "yes") {
                  if (!compact.compact_name || compact.compact_name.trim() === "") {
                    compactErrors.compact_name = "Compact name is required";
                  }
                  if (!compact.compact_identification_number || compact.compact_identification_number.trim() === "") {
                    compactErrors.compact_identification_number = "Identification number is required";
                  }
                  if (!compact.compact_states || compact.compact_states.length === 0) {
                    compactErrors.compact_states = "Please select at least one state";
                  }
                }
              
                if (Object.keys(compactErrors).length > 0) {
                  errors.compact_licenses = errors.compact_licenses || [];
                  errors.compact_licenses[index] = compactErrors;
                }
              });
              
              return errors;
            }}
            onSubmit={() => {}}
            validateOnMount={true}
          >
            {({
              values,
              errors,
              isValid,
              isValidating,
              setFieldValue,
              touched,
              setFieldTouched,
            }) => (
              <form role="form" onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                }
              }}>
                <FieldArray name="licenses">
                  {({ push, remove }) => (
                    <>
                      {values.licenses.map((license: any, index: number) => (
                        <div key={index} className="mb-6 p-4 border rounded-lg w-full max-w-[700px]">
                          <h2 className="text-xl sm:text-2xl text-therapy-blue-dark font-semibold">
                            License #{index + 1}
                          </h2>

                          <div className="mt-6 w-full max-w-[300px]">
                            <SelectComponent
                              label="License Type"
                              id={`licenses[${index}].license_type`}
                              value={license.license_type}
                              onChange={(value) => {
                                setFieldValue(`licenses[${index}].license_type`, value)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].license_type`)}
                              options={licenseTypeIndependent}
                              required={true}
                              placeholder="Select a license type"
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.license_type) &&
                                (errors?.licenses as any)?.[index]?.license_type
                              }
                            />
                          </div>

                          <div className="w-full max-w-[200px] mt-6">
                            <SelectComponent
                              label="State"
                              id={`licenses[${index}].license_state`}
                              value={license.license_state}
                              onChange={(value) => {
                                setFieldValue(`licenses[${index}].license_state`, value)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].license_state`)}
                              options={states.map((state) => ({
                                label: state.name,
                                value: state.abbrev,
                                id: state.abbrev,
                              }))}
                              required={true}
                              placeholder="Select a state"
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.license_state) &&
                                (errors?.licenses as any)?.[index]?.license_state
                              }
                            />
                          </div>

                          <div className="w-full max-w-[300px]">
                            <InputComponent
                              value={license.issuing_board}
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.issuing_board) &&
                                (errors?.licenses as any)?.[index]?.issuing_board
                              }
                              onChange={(value) => {
                                setFieldTouched(`licenses[${index}].issuing_board`)
                                const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                setFieldValue(`licenses[${index}].issuing_board`, alphanumericValue)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].issuing_board`)}
                              id={`licenses[${index}].issuing_board`}
                              class="mt-6"
                              label="Issuing Board"
                              isRequired
                              placeholder="Type here ..."
                            />
                          </div>

                          <div className="w-full max-w-[300px]">
                            <InputComponent
                              value={license.license_number}
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.license_number) &&
                                (errors?.licenses as any)?.[index]?.license_number
                              }
                              onChange={(value) => {
                                setFieldTouched(`licenses[${index}].license_number`)
                                setFieldValue(`licenses[${index}].license_number`, value)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].license_number`)}
                              id={`licenses[${index}].license_number`}
                              class="mt-6"
                              label="License Number"
                              isRequired
                              placeholder="Type here ..."
                            />
                          </div>

                          <div className="w-full max-w-[300px] mt-6">
                            <DateComponent
                              data-testid={`issue-date-${index}`}
                              label="Issue Date"
                              value={license.issue_date || ""}
                              isRequired
                              minDate={undefined}
                              maxDate={new Date()}
                              outputFormat="us"
                              onChange={(value) => {
                                setFieldTouched(`licenses[${index}].issue_date`)
                                setFieldValue(`licenses[${index}].issue_date`, value)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].issue_date`)}
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.issue_date) &&
                                (errors?.licenses as any)?.[index]?.issue_date
                              }
                            />
                          </div>

                          <div className="w-full max-w-[300px] mt-6">
                            <DateComponent
                              data-testid={`expiry-date-${index}`}
                              label="Expiration Date"
                              value={license.expiry_date}
                              isRequired
                              minDate={new Date()}
                              maxDate={undefined}
                              outputFormat="us"
                              onChange={(value) => {
                                setFieldTouched(`licenses[${index}].expiry_date`)
                                setFieldValue(`licenses[${index}].expiry_date`, value)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].expiry_date`)}
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.expiry_date) &&
                                (errors?.licenses as any)?.[index]?.expiry_date
                              }
                            />
                          </div>

                          <div className="mt-6">
                            <div className="w-full max-w-[400px]">
                              <p className="mb-2 text-sm text-gray-400">
                                License Verification Upload (Max 5MB)
                              </p>

                              <>
                                {/* List existing uploaded files with clear option */}
                                {license.license_verification && license.license_verification.length > 0 && (
                                  <div className="space-y-2 mb-2">
                                    {license.license_verification.map((file: any, fileIndex: number) => (
                                      <div key={fileIndex} className="flex items-center gap-2">
                                        <div className="border mt-1 w-full max-w-[200px] border-1 bg-white border-gray-300 ring-0 focus:outline-neutral-300 rounded-xl py-2.5 px-2 text-sm overflow-hidden text-ellipsis">
                                          {file.upload_name}
                                        </div>
                                        <button
                                          onClick={(e) => {
                                            e.preventDefault();
                                            const updatedFiles = license.license_verification.filter(
                                              (_: any, i: number) => i !== fileIndex,
                                            )
                                            setFieldValue(`licenses[${index}].license_verification`, updatedFiles)

                                            const fileInput = document.getElementById(`fileInput-${index}`) as HTMLInputElement
                                            if (fileInput) fileInput.value = ""
                                          }}
                                          className="text-gray-500 hover:text-red-500 transition-colors duration-300"
                                        >
                                          <i className="fa fa-times"></i>
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                )}

                                {/* Show upload field if less than 4 files */}
                                {(!license.license_verification || license.license_verification.length < 4) && (
                                  <label
                                    htmlFor={`fileInput-${index}`}
                                    className={`flex items-center gap-2 cursor-pointer ${uploadingLicense[index] ? "pointer-events-none cursor-not-allowed" : ""}`}
                                  >
                                    <div className="border w-full max-w-[200px] border-1 bg-white border-gray-300 ring-0 focus:outline-neutral-300 rounded-xl py-5 px-2 text-sm" />
                                    <i className="fa-solid fa-upload"></i>
                                  </label>
                                )}
                              </>

                              <input
                                id={`fileInput-${index}`}
                                key={`fileInput-${index}`}
                                type="file"
                                multiple
                                onChange={async (e) => {
                                  const files = Array.from(e.target.files || []);
                                  if (files.length === 0) return;
                                  if ((files.length + license.license_verification.length) > 4) {
                                    notification.error(
                                      `You can only upload 4 files max.${license.license_verification.length > 0 ? ` You have already uploaded ${license.license_verification.length} files.` : ''}`
                                    );
                                    return;
                                  }
          
                                  setUploadingLicense((prev) => ({ ...prev, [index]: true }))
                                  
                                  try {
                                    const uploadedFiles = await handleFileUpload(files);
                                    if (uploadedFiles && uploadedFiles?.length > 0) {
                                      const newData = uploadedFiles.map((f) => ({
                                        location: f.Location,
                                        upload_name: f.originalName,
                                      }));
                                      const updatedLicenseVerifications = [
                                        ...(values.licenses[index].license_verification || []),
                                        ...newData,
                                      ];
                                    
                                      const updatedLicenses = [...values.licenses];
                                      updatedLicenses[index] = {
                                        ...updatedLicenses[index],
                                        license_verification: updatedLicenseVerifications,
                                      };
                                    
                                      setFieldValue(`licenses[${index}].license_verification`, updatedLicenseVerifications);

                                      if (!isValid) {
                                        dispatch(updateRegistrationForm({
                                          pageId,
                                          values: {
                                            ...values,
                                            licenses: updatedLicenses,
                                            isInvalid: true
                                          }
                                        }));
                                      } else {
                                        dispatch(updateRegistrationForm({
                                          pageId,
                                          values: {
                                            ...values,
                                            licenses: updatedLicenses,
                                          }
                                        }));
                                      }
                                    }
                                  } finally {
                                    setFieldTouched(`licenses[${index}].license_verification`);
                                    setUploadingLicense((prev) => ({ ...prev, [index]: false }));
                                  }
                                }}
                                style={{ display: "none" }}
                              />
                            </div>
                            {(errors?.licenses as any)?.[index]?.license_verification &&
                              (showErrors || (touched?.licenses as any)?.[index]?.license_verification) && (
                                <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                                  {(errors?.licenses as any)?.[index]?.license_verification}
                                </div>
                              )}
                            {uploadingLicense[index] && <p className="text-therapy-blue">Uploading.....</p>}
                          </div>

                          <div className="mt-6 w-full max-w-[200px]">
                            <SelectComponent
                              label="License Status"
                              id={`licenses[${index}].license_status`}
                              value={license.license_status}
                              onChange={(value) => {
                                const selectedValueString = (value as { label: string; value: string })?.value;

                                if (
                                  selectedValueString !== "provisional" &&
                                  license.license_status.value === "provisional"
                                ) {
                                  setFieldValue(`licenses[${index}].clinical_supervisor_name`, "");
                                  setFieldValue(`licenses[${index}].clinical_supervisor_email`, "");
                                  setFieldValue(`licenses[${index}].clinical_supervisor_phone`, "");
                                }
                                
                                setFieldValue(`licenses[${index}].license_status`, value);
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].license_status`)}
                              options={[
                                { label: "Active", value: "active" },
                                { label: "Inactive", value: "inactive" },
                                { label: "Pending", value: "pending" },
                                { label: "Provisional", value: "provisional" },
                                { label: "Suspended", value: "suspended" },
                              ]}
                              required={true}
                              placeholder="Select a status"
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.license_status) &&
                                (errors?.licenses as any)?.[index]?.license_status
                              }
                            />
                          </div>

                          {license.license_status.value === "provisional" && (
                            <>
                              <div className="w-full max-w-[300px]">
                                <InputComponent
                                  value={license.clinical_supervisor_name}
                                  errorMessage={
                                    (showErrors || (touched?.licenses as any)?.[index]?.clinical_supervisor_name) &&
                                    (errors?.licenses as any)?.[index]?.clinical_supervisor_name
                                  }
                                  onChange={(value) => {
                                    setFieldTouched(`licenses[${index}].clinical_supervisor_name`)
                                    const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                    setFieldValue(`licenses[${index}].clinical_supervisor_name`, alphanumericValue)
                                  }}
                                  onBlur={() => setFieldTouched(`licenses[${index}].clinical_supervisor_name`)}
                                  id={`licenses[${index}].clinical_supervisor_name`}
                                  class="mt-6"
                                  label="Full Name of Clinical Supervisor"
                                  isRequired
                                  placeholder="Type here..."
                                />
                              </div>

                              <div className="w-full max-w-[300px]">
                                <InputComponent
                                  value={license.clinical_supervisor_email}
                                  errorMessage={
                                    (showErrors || (touched?.licenses as any)?.[index]?.clinical_supervisor_email) &&
                                    (errors?.licenses as any)?.[index]?.clinical_supervisor_email
                                  }
                                  onChange={(value) => {
                                    setFieldTouched(`licenses[${index}].clinical_supervisor_email`)
                                    setFieldValue(`licenses[${index}].clinical_supervisor_email`, value)
                                  }}
                                  onBlur={() => setFieldTouched(`licenses[${index}].clinical_supervisor_email`)}
                                  id={`licenses[${index}].clinical_supervisor_email`}
                                  class="mt-6"
                                  label="Clinical Supervisor Email"
                                  isRequired
                                  placeholder="Type here..."
                                />
                              </div>

                              <div className="w-full max-w-[300px] mt-6">
                                <p className="mb-2 text-sm text-gray-400">
                                  <span className="text-danger">*</span>Clinical Supervisor Phone
                                </p>

                                <PhoneInput
                                  international
                                  countryCallingCodeEditable={false}
                                  defaultCountry="US"
                                  value={license.clinical_supervisor_phone}
                                  onChange={(value) => {
                                    setFieldTouched(`licenses[${index}].clinical_supervisor_phone`)
                                    setFieldValue(`licenses[${index}].clinical_supervisor_phone`, value)
                                  }}
                                  onBlur={() => setFieldTouched(`licenses[${index}].clinical_supervisor_email`)}
                                  className="register-phone-input"
                                />
                                {(errors?.licenses as any)?.[index]?.clinical_supervisor_phone &&
                                  (showErrors || (touched?.licenses as any)?.[index]?.clinical_supervisor_phone) && (
                                    <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                                      {(errors?.licenses as any)?.[index]?.clinical_supervisor_phone}
                                    </div>
                                  )}
                              </div>
                            </>
                          )}

                          <div className="mt-6">
                            <SelectComponent
                              label="Have you ever been put on probation or received disciplinary action by your state licensing board?"
                              id={`licenses[${index}].probation_or_disciplinary_action`}
                              value={license.probation_or_disciplinary_action}
                              onChange={(value) => {
                                if (value && (value as { label: string; value: string }).value === "no") {
                                  setFieldValue(`licenses[${index}].disciplinary_action_description`, "")
                                }
                                setFieldValue(`licenses[${index}].probation_or_disciplinary_action`, value)
                              }}
                              onBlur={() => setFieldTouched(`licenses[${index}].probation_or_disciplinary_action`)}
                              options={[
                                { label: "Yes", value: "yes" },
                                { label: "No", value: "no" },
                              ]}
                              selectClass="w-full max-w-[200px]"
                              placeholder="Select a value"
                              required={true}
                              errorMessage={
                                (showErrors || (touched?.licenses as any)?.[index]?.probation_or_disciplinary_action) &&
                                (errors?.licenses as any)?.[index]?.probation_or_disciplinary_action
                              }
                            />
                          </div>

                          {license.probation_or_disciplinary_action.value === "yes" && (
                            <div className="w-full max-w-[300px]">
                              <InputComponent
                                value={license.disciplinary_action_description}
                                errorMessage={
                                  (showErrors || (touched?.licenses as any)?.[index]?.disciplinary_action_description) &&
                                  (errors?.licenses as any)?.[index]?.disciplinary_action_description
                                }
                                onChange={(value) => {
                                  setFieldTouched(`licenses[${index}].disciplinary_action_description`)
                                  const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                  setFieldValue(`licenses[${index}].disciplinary_action_description`, alphanumericValue)
                                }}
                                onBlur={() => setFieldTouched(`licenses[${index}].disciplinary_action_description`)}
                                id={`licenses[${index}].disciplinary_action_description`}
                                class="mt-6"
                                label="If prior disciplinary action please explain below"
                                placeholder="Type here..."
                                isRequired
                              />
                            </div>
                          )}

                          {/* Remove button (only if more than 1 license exists) */}
                          {values.licenses.length > 1 && (
                            <p
                              onClick={(e) => {
                                e.preventDefault()
                                remove(index)
                              }}
                              className="text-danger underline cursor-pointer mt-4"
                            >
                              - Remove License
                            </p>
                          )}
                        </div>
                      ))}

                      {/* Add New License Button */}
                      <p
                        onClick={(e) => {
                          e.preventDefault()
                          push({
                            license_type: "",
                            license_state: "",
                            issuing_board: "",
                            license_number: "",
                            issue_date: "",
                            expiry_date: "",
                            license_verification: [],
                            license_status: "",
                            clinical_supervisor_name: "",
                            clinical_supervisor_email: "",
                            clinical_supervisor_phone: "",
                            probation_or_disciplinary_action: "",
                            disciplinary_action_description: "",
                          })
                        }}
                        className="text-therapy-blue-dark underline cursor-pointer mb-4 ml-4"
                      >
                        + Add Another License
                      </p>
                    </>
                  )}
                </FieldArray>

                <div>
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-5">
                    National Provider Identifier
                  </h3>

                  <div className="w-full max-w-[300px]">
                    <InputComponent
                      value={values.npi_number}
                      onChange={(value) => {
                        setFieldTouched("npi_number")
                        const numericValue = value.replace(/[^0-9]/g, "")
                        setFieldValue("npi_number", numericValue)
                      }}
                      onBlur={() => setFieldTouched("npi_number")}
                      isRequired
                      name="npi_number"
                      class="mb-5"
                      label={"NPI number"}
                      errorMessage={(showErrors || touched.npi_number) && errors.npi_number as string}
                    />
                  </div>
                </div>

                <FieldArray name="compact_licenses">
                  {({ remove, push }) => (
                    <>
                      <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-5">
                        Compact License(s)
                      </h3>
                      {values.compact_licenses.map((compact_license: any, index: number) => (
                        <div key={index} className="mb-6 p-4 border rounded-lg w-full max-w-[500px]">
                          <div className="flex justify-between items-center">
                            <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark">
                              Compact License #{index + 1}
                            </h3>
                          </div>

                          <div className="mt-6">
                            <SelectComponent
                              label="Are you licensed in additional states as part of a compact privilege?"
                              id={`compact_license[${index}].compact_privilege`}
                              value={compact_license.compact_privilege}
                              onChange={(value) => {
                                if (
                                  value &&
                                  (value as { label: string; value: string }).value === "no" &&
                                  values.compact_licenses.length > 1
                                ) {
                                  remove(index)
                                  return
                                }
                                if (
                                  value &&
                                  (value as { label: string; value: string }).value === "no" &&
                                  values.compact_licenses.length === 1
                                ) {
                                  setFieldValue(`compact_licenses[${index}].compact_name`, "")
                                  setFieldValue(`compact_licenses[${index}].compact_identification_number`, "")
                                  setFieldValue(`compact_licenses[${index}].compact_states`, [])
                                }
                                setFieldValue(`compact_licenses[${index}].compact_privilege`, value)
                              }}
                              options={[
                                { label: "Yes", value: "yes" },
                                { label: "No", value: "no" },
                              ]}
                              selectClass="w-full max-w-[200px]"
                              placeholder="Select a value"
                            />
                          </div>

                          {compact_license.compact_privilege.value === "yes" && (
                            <>
                              <div className="w-full max-w-[300px] mt-6">
                                <InputComponent
                                  value={compact_license.compact_name}
                                  onChange={(value) => {
                                    setFieldTouched(`compact_licenses[${index}].compact_name`)
                                    const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                    setFieldValue(`compact_licenses[${index}].compact_name`, alphanumericValue)
                                  }}
                                  onBlur={() => setFieldTouched(`compact_licenses[${index}].compact_name`)}
                                  name="compact_name"
                                  class="mb-5"
                                  isRequired
                                  label={"Compact Name"}
                                  errorMessage={
                                    (showErrors || (touched?.compact_licenses as any)?.[index]?.compact_name) &&
                                    (errors?.compact_licenses as any)?.[index]?.compact_name
                                  }
                                  placeholder="Type here..."
                                />
                              </div>

                              <div className="w-full max-w-[300px] mt-6">
                                <InputComponent
                                  value={compact_license.compact_identification_number}
                                  onChange={(value) => {
                                    setFieldTouched(`compact_licenses[${index}].compact_identification_number`)
                                    const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                    setFieldValue(
                                      `compact_licenses[${index}].compact_identification_number`,
                                      alphanumericValue,
                                    )
                                  }}
                                  onBlur={() => setFieldTouched(`compact_licenses[${index}].compact_identification_number`)}
                                  name="compact_identification_number"
                                  class="mb-5"
                                  isRequired
                                  label={"Compact Identification Number"}
                                  errorMessage={
                                    (showErrors || (touched?.compact_licenses as any)?.[index]?.compact_identification_number) &&
                                    (errors?.compact_licenses as any)?.[index]?.compact_identification_number
                                  }
                                  placeholder="Type here..."
                                />
                              </div>

                              <div className="mt-6">
                                <SelectComponent
                                  label="Compact States licensed in"
                                  required
                                  id={`compact_licenses[${index}].compact_states`}
                                  value={compact_license.compact_states}
                                  onChange={(value) => {
                                    setFieldValue(`compact_licenses[${index}].compact_states`, value)
                                  }}
                                  onBlur={() => setFieldTouched(`compact_licenses[${index}].compact_states`)}
                                  options={states.map((state) => ({
                                    label: state.name,
                                    value: state.abbrev,
                                    id: state.abbrev,
                                  }))}
                                  isMulti={true}
                                  placeholder="Select a state"
                                  errorMessage={
                                    (showErrors || (touched?.compact_licenses as any)?.[index]?.compact_states) &&
                                    (errors?.compact_licenses as any)?.[index]?.compact_states
                                  }
                                />
                              </div>
                            </>
                          )}
                          {values.compact_licenses.length > 1 && (
                            <p
                              onClick={(e) => {
                                e.preventDefault()
                                remove(index)
                              }}
                              className="text-danger underline cursor-pointer mt-4"
                            >
                              - Remove Compact License
                            </p>
                          )}
                        </div>
                      ))}
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          push({
                            compact_privilege: "",
                            compact_name: "",
                            compact_identification_number: "",
                            compact_states: [],
                          })
                        }}
                        className="text-therapy-blue-dark underline cursor-pointer mb-4 ml-4"
                      >
                      + Add another compact license
                      </button>
                    </>
                  )}
                </FieldArray>

                {/* Submit Button */}
                <div className="mt-8 mb-8">
                  <SaveButton
                    disabled={
                      isValidating || Object.values(uploadingLicense).some(Boolean) || (editFlag && !isValid)
                    }
                    type="button"
                    value={`${editFlag ? "Save" : "Save & Continue"}`}
                    loading={isLoading}
                    onClick={(e) => {
                      e.preventDefault();
                      handlePageSubmit({
                        values,
                        isPageValid: isValid,
                      });
                    }}
                  />
                </div>
              </form>
            )}
          </Formik>
        </div>
      </div>
    </>
  )
}

export default LicensePage

