import SaveButton from "@/components/buttons/SaveButton"
import ToolbarComponent from "@/components/ToolbarComponent"
import { type FC, useState, useEffect } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer"
import {
  createTherapistSubscriptionAction,
  registerTherapistAction,
  subscribeUsingStripeAction,
} from "@/store/slicers/therapist.slicer"
import { useDispatch, useSelector } from "react-redux"
import type { AppDispatch, RootState } from "@/store/index"
import notification from "@/utils/notification.util"
import { fetchSubscriptionPlansAction } from "@/store/slicers/admin.slicer"
import type { SubscriptionPlan } from "@/types/subscription-plan.interface"
import type { TherapistSubscription } from "@/types/therapist-subscription.interface"
import NormalDialog from "@/components/dialogs/NormalDialog"
import { useOverlayTriggerState } from "react-stately"
import { updateSidebarState } from "@/store/slicers/sidebar.slicer"
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util"

const Page: FC = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch<AppDispatch>()
  const location = useLocation()
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop()
  const authStore = useSelector((state: RootState) => state.auth)
  const pageContent = authStore.registration.formContent[pageId!] || {}
  const sidebarState = useSelector((state: RootState) => state.sidebar)
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog } = authStore.registration;

  const [plan, setPlan] = useState<Pick<TherapistSubscription, "subscriptionPlanId" | "subscriptionType"> | null>(null)
  const [subscriptionPlan, setSubscriptionPlan] = useState<SubscriptionPlan>()
  const [loading, setLoading] = useState<boolean>(false)
  const paymentSuccessState = useOverlayTriggerState({})

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showError = invalidMenus.has(pageId!);

  const fetchSubscriptionPlan = async () => {
    setLoading(true)
    const res = await dispatch(fetchSubscriptionPlansAction({ type: "active" }))
    setLoading(false)
    if (res.data && res.data.length > 0) {
      setSubscriptionPlan(res.data[0])
    } else {
      notification.error("No active subscription plans found")
    }
  }

  const handlePaymentSuccess = async (sessionId: string) => {
    if (!userRegistrationToken) {
      notification.error("User Token is required")
      return
    }
    const res = await dispatch(createTherapistSubscriptionAction(sessionId, userRegistrationToken))

    if (res && res.status === 201) {
      const { data } = res.data as { data: TherapistSubscription }
      setPlan({
        subscriptionPlanId: data.subscriptionPlan!.id,
        subscriptionType: data.subscriptionType,
      })
      dispatch(
        updateRegistrationForm({
          pageId,
          values: {
            subscriptionPlanId: data?.subscriptionPlan?.id,
            subscriptionType: data.subscriptionType,
            therapistSubscriptionId: data.id,
            subscriptionSuccess: true,
          },
        }),
      )
    }
    paymentSuccessState.close()
  }

  const validatePlanSelection = (): boolean => {
    if (pageContent && pageContent.subscriptionPlanId &&
      pageContent.subscriptionType && pageContent.therapistSubscriptionId &&
      pageContent.subscriptionSuccess
    ) {
      return true
    }
    return false
  }

  useEffect(() => {
    if (pageContent.subscriptionPlanId && pageContent.subscriptionType) {
      setPlan({
        subscriptionPlanId: pageContent.subscriptionPlanId,
        subscriptionType: pageContent.subscriptionType,
      })
    }
    fetchSubscriptionPlan()
    const success = new URLSearchParams(window.location.search).get("success")
    const sessionId = new URLSearchParams(window.location.search).get("session_id")

    if (success === "true" && sessionId) {
      const newUrl = window.location.origin + window.location.pathname
      window.history.replaceState({}, "", newUrl)
      paymentSuccessState.open()
      handlePaymentSuccess(sessionId)
    }
  }, [])

  return (
    <>
      {loading && (
        <div id="loader" className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
          <div role="status">
            <svg
              aria-hidden="true"
              className="inline w-10 h-10 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-8 md:px-16 py-4 sm:py-8">
        <h1 className="text-2xl md:text-3xl font-medium text-therapy-blue-dark">Select your NextTherapist plan:</h1>

        <div className="bg-red-600 text-white text-[18px] p-4 rounded-md mt-2">
          <strong>Demo Users:</strong> Please select a subscription type below (either is fine). This will take you to the
          Stripe Portal for payment. For this beta test please use the following credit card information when you arrive at Stripe:
          <br />
          <br />
          <strong>Credit Card number:</strong> 4242 4242 4242 4242
          <br />
          <strong>Expiration Date:</strong> any future MM/YY will work
          <br />
          <strong>CVC:</strong> any 3 numbers will work
        </div>

        {showError && !validatePlanSelection() && (
          <span className="text-danger text-sm">Please subscribe to your preferred plan</span>
        )}

        <div className="flex flex-col md:flex-row md:justify-start md:space-x-8 mt-8 w-full max-w-[900px]">
          <div className={`bg-white p-4 md:p-6 rounded-lg border-2 flex flex-col items-center w-full max-w-[350px] mx-auto md:mx-0 transition duration-100 ease-in-out
            ${plan?.subscriptionType === "monthly" ? "border-therapy-blue-dark shadow-lg transform scale-105" : "border-gray-300"}
            ${plan?.subscriptionType && plan?.subscriptionType !== "monthly" ? "opacity-40 pointer-events-none" : ""}
          `}>
            <h2 className="text-2xl md:text-3xl font-medium text-therapy-blue-dark mt-4 mb-4 font-weight-bold">
              Monthly Plan
              {plan?.subscriptionType === "monthly" && pageContent?.subscriptionSuccess
                && pageContent?.therapistSubscriptionId &&
                (
                  <sup>
                    <i className="fa-solid fa-circle-check" style={{ color: "#43a047" , fontSize: "1.25rem", marginLeft: 4 }} />
                  </sup>
                )}
            </h2>
            <p className="text-md mt-2 mb-2 text-therapy-blue-dark">
              $ <span className="font-bold">{subscriptionPlan?.monthlyPrice}</span>
            </p>
            <p className="text-md font-normal mt-2 mb-2 text-therapy-blue-dark">per month</p>

            <div className="mt-6 mb-6 ">
              <SaveButton
                onClick={async () => {
                  if (!userRegistrationToken) {
                    notification.error("User Token is required")
                    return
                  }
                  if (subscriptionPlan?.id) {
                    setLoading(true)
                    setPlan({
                      subscriptionType: "monthly",
                      subscriptionPlanId: subscriptionPlan.id,
                    })
                    const res = await dispatch(
                      subscribeUsingStripeAction({
                        subscriptionType: "monthly",
                        subscriptionPlanId: subscriptionPlan.id,
                        redirectPath: "/auth/register/next-therapist-plan",
                        userToken: userRegistrationToken,
                      }),
                    )

                    if (res && res.status === 200) {
                      const { stripeSessionUrl } = res.data
                      if (!stripeSessionUrl) notification.error("Something went wrong. Please try again.")
                      window.location.href = stripeSessionUrl
                    } else {
                      setLoading(false)
                      setPlan(null)
                    }
                  } else {
                    notification.error("Subscription Plan ID is missing")
                  }
                }}
                value={
                  plan?.subscriptionType === "monthly" &&
                    pageContent?.subscriptionSuccess &&
                      pageContent?.therapistSubscriptionId
                        ? "Subscribed"
                        : "Select"
                }
                disabled={
                  plan?.subscriptionType === "monthly" ||
                  (pageContent?.subscriptionSuccess && pageContent?.therapistSubscriptionId)
                }
                loading={loading}
                className="!w-[280px]"
              />
            </div>
          </div>

          <div className={`bg-white p-4 md:p-6 rounded-lg border-2 flex flex-col items-center w-full max-w-[350px] mx-auto md:mx-0 transition duration-100 ease-in-out
            ${plan?.subscriptionType === "yearly" ? "border-therapy-blue-dark shadow-lg transform scale-105" : "border-gray-300"}
            ${plan?.subscriptionType && plan?.subscriptionType !== "yearly" ? "opacity-40 pointer-events-none" : ""}
          `}>
            <h2 className="text-2xl md:text-3xl font-medium text-therapy-blue-dark mt-4 mb-4">
              Annual Plan
              { plan?.subscriptionType === "yearly" && pageContent?.subscriptionSuccess &&
                  pageContent?.therapistSubscriptionId &&
                  (
                    <sup>
                      <i className="fa-solid fa-circle-check" style={{ color: "#43a047" , fontSize: "1.25rem", marginLeft: 4 }} />
                    </sup>
                  )}
            </h2>
            <p className="text-md font-bold mt-2 mb-2 text-therapy-blue-dark">
              $ <span className="font-bold">{subscriptionPlan?.annualPrice}</span>
            </p>
            <p className="text-md font-normal mt-2 mb-2 text-therapy-blue-dark">per year</p>

            <div className="mt-6 mb-6">
              <SaveButton
                onClick={async () => {
                  if (!userRegistrationToken) {
                    notification.error("User Token is required")
                    return
                  }
                  if (subscriptionPlan?.id) {
                    setLoading(true)
                    setPlan({
                      subscriptionType: "yearly",
                      subscriptionPlanId: subscriptionPlan.id,
                    })
                    const res = await dispatch(
                      subscribeUsingStripeAction({
                        subscriptionType: "yearly",
                        subscriptionPlanId: subscriptionPlan.id,
                        redirectPath: "/auth/register/next-therapist-plan",
                        userToken: userRegistrationToken,
                      }),
                    )

                    if (res && res.status === 200) {
                      const { stripeSessionUrl } = res.data
                      if (!stripeSessionUrl) notification.error("Something went wrong. Please try again.")
                      window.location.href = stripeSessionUrl
                    } else {
                      setLoading(false)
                      setPlan(null)
                    }
                  } else {
                    notification.error("Subscription Plan ID is missing")
                  }
                }}
                value={
                  plan?.subscriptionType === "yearly" &&
                    pageContent?.subscriptionSuccess &&
                      pageContent?.therapistSubscriptionId
                        ? "Subscribed"
                        : "Select"
                }
                disabled={
                  plan?.subscriptionType === "yearly" ||
                  (pageContent?.subscriptionSuccess && pageContent?.therapistSubscriptionId) ||
                  loading
                }
                loading={loading}
                className="!w-[280px]"
              />
            </div>
          </div>

          <NormalDialog title="Payment Successful!" state={paymentSuccessState}>
            <p className="text-sm font-medium">
              Stripe Payment was successful! Please wait for the payment to be verified by us. It should take few
              minutes to process the payment. Once the payment is verified, you will be able to access the services.
            </p>
          </NormalDialog>
        </div>

        <br />
        <div className="mt-5">
          <SaveButton
            onClick={async (e) => {
              e.preventDefault()

              if (!validatePlanSelection()) {
                invalidMenus.add(pageId!);
              } else {
                invalidMenus.delete(pageId!);
              }

              dispatch(updateSidebarState({
                key: "registerTherapist",
                value: {
                  ...sidebarState.registerTherapist,
                  invalidMenus: Array.from(invalidMenus)
                }
              }));
              if (!userRegistrationToken) {
                notification.error("User Token is required")
                return
              }
              const res = await dispatch(registerTherapistAction(pageContent || {}, pageId!, userRegistrationToken))
              if (res?.status === 200 || res?.status === 201) {
                if (navToInvalidPage) {
                  const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
                  
                  if (nextInvalidPage) {
                    navigate(`/auth/register/${nextInvalidPage}`);
                  } else {
                    // No invalid pages left, check if registration is complete
                    const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
                    
                    if (registrationComplete && shouldShowRegCompleteDialog) {
                      // Show completion dialog when all pages are valid
                      dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
                    } else {
                      // Registration not complete but no specific invalid page identified
                      navigate("/auth/register/profile");
                    }
                  }
                } else {
                  navigate("/auth/register/profile");
                }
              }
            }}
            value="Save & Continue"
            disabled={loading}
          />
        </div>
      </div>
    </>
  )
}

export default Page

