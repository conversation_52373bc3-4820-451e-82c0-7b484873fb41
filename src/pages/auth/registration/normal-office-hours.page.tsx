import ToolbarComponent from "@/components/ToolbarComponent";
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer";
import { type FC, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import type { AppDispatch, RootState } from "@/store/index";
import OfficeHoursComponent, { type ActiveTimes } from "@/components/office-hours/office-hours.component";
import { useOverlayTriggerState } from "react-stately";
import NormalDialog from "@/components/dialogs/NormalDialog";
import { updateSidebarState } from "@/store/slicers/sidebar.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import notification from "@/utils/notification.util";
import { getNextInvalidPage, validateTherapistRegistration } from "@/utils/app.util";

const Page: FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const pageId = location.pathname.replace(/\/$/, "").split("/").pop();
  const authStore = useSelector((state: RootState) => state.auth);
  const pageContent = authStore.registration.formContent[pageId!] || {};
  const sidebarState = useSelector((state: RootState) => state.sidebar);
  const { userRegistrationToken, navToInvalidPage, shouldShowRegCompleteDialog } = authStore.registration;
  const [activeTimes, setActiveTimes] = useState<ActiveTimes>(pageContent?.activeTimes || {});
  const [selectedTimezone, setSelectedTimezone] = useState<string | null>(pageContent?.timezone || null);
  const [appointmentMethod, setAppointmentMethod] = useState<string | null>(pageContent?.appointmentMethod || null);
  const infoState = useOverlayTriggerState({});
  const isFirstTimeVisit = Object.keys(pageContent).length === 0;
  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow overflow-hidden px-4 sm:px-8 md:px-12 lg:px-16 py-4 sm:py-6 md:py-8">
        <h1 className="text-2xl sm:text-3xl font-semibold text-therapy-blue-dark flex items-center gap-2 flex-wrap">
          <span>Set Your Normal Office Hours</span>
          <i
            className="fa-solid fa-info-circle cursor-pointer"
            style={{ fontSize: "20px" }}
            onClick={() => infoState.open()}
            data-testid="info-icon-open"
          ></i>
        </h1>
        
        <OfficeHoursComponent
        showAutoWalkthrough={isFirstTimeVisit}
          activeTimes={activeTimes}
          setActiveTimes={setActiveTimes}
          selectedTimezone={selectedTimezone}
          setSelectedTimezone={setSelectedTimezone}
          appointmentMethod={appointmentMethod}
          setAppointmentMethod={setAppointmentMethod}
          btnLabel="Save & Continue"
          shouldDisableBtn={false}
          onSave={async ({ e, isPageValid }) => {
            e?.preventDefault()

            const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || [])
            if (!isPageValid) {
              invalidMenus.add(pageId!);
            } else {
              invalidMenus.delete(pageId!);
            }
        
            dispatch(updateSidebarState({
              key: "registerTherapist",
              value: {
                ...sidebarState.registerTherapist,
                invalidMenus: Array.from(invalidMenus)
              }
            }));

            if (!userRegistrationToken) {
              notification.error("User Token is required")
              return
            }
            const res = await dispatch(
              registerTherapistAction(
                { activeTimes, timezone: selectedTimezone, appointmentMethod },
                pageId!,
                userRegistrationToken,
              ),
            )
            if (res?.status === 201 || res?.status === 200) {
              dispatch(
                updateRegistrationForm({
                  pageId,
                  values: {
                    activeTimes,
                    timezone: selectedTimezone,
                    appointmentMethod,
                  }
                }),
              );
        
              if (navToInvalidPage) {
                const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
                
                if (nextInvalidPage) {
                  navigate(`/auth/register/${nextInvalidPage}`);
                } else {
                  // No invalid pages left, check if registration is complete
                  const registrationComplete = validateTherapistRegistration(authStore.registration.formContent);
                  
                  if (registrationComplete && shouldShowRegCompleteDialog) {
                    // Show completion dialog when all pages are valid
                    dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
                  } else {
                    // Registration not complete but no specific invalid page identified
                    navigate("/auth/register/waitlist-notifications");
                  }
                }
              } else {
                navigate("/auth/register/waitlist-notifications");
              }
            }
          }}
        />
      </div>
      <NormalDialog
        opacity={80}
        state={infoState}
        title="Note"
        onClose={() => infoState.close()}
        width="w-[90vw] sm:w-[500px] md:w-[600px] lg:w-[700px]"
        titleCentered={true}
      >
        <p className="text-xs sm:text-sm">
          Your normal working hours should reflect your typical working days and times. If you schedule changes on a
          1-off basis do not change your normal working hours with NextTherapist. Instead ensure that your work calendar
          has blocked these hours as "busy" so that our system will not book clients during those normal working hours.
          <br />
          <br />
          Examples:
          <br />
          Going on vacation – block the entire day during your trip,
          <br />
          Leaving office early to attend a child's event – block the hours you will be out of the office,
          <br />
          Coming into the office late due to a dentist appointment – block the hours you will not be in office.
        </p>
      </NormalDialog>
    </>
  );
};

export default Page;