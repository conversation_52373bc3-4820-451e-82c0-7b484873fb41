import SaveButton from "@/components/buttons/SaveButton"
import AppCheckBoxInputComponent from "@/components/forms/AppCheckBoxInputComponent"
import AppCheckBoxListComponent from "@/components/forms/AppCheckBoxListComponent"
import InputComponent from "@/components/forms/InputComponent"
import ToolbarComponent from "@/components/ToolbarComponent"
import { fluentLanguage, genders, race, socialMedias } from "@/configs/registration.configs"
import type { AppDispatch, RootState } from "@/store/index"
import { updateRegistrationForm, updateRegistrationState } from "@/store/slicers/auth.slicer"
// import { useApiClient } from "@/utils/api.util"
import { FieldArray, Formik } from "formik"
import { type FC, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
// import Compressor from "compressorjs"
import notification from "@/utils/notification.util"
import { registerTherapistAction } from "@/store/slicers/therapist.slicer"
import { updateSidebarState } from "@/store/slicers/sidebar.slicer"
import { useAppSelector } from "@/store/hooks"
import { THERAPIST_REGISTRATION_PAGES } from "@/configs/therapist-registration.configs"
import AppRadioComponent from "@/components/forms/AppRadioComponent"
import { getNextInvalidPage } from "@/utils/app.util"
import { useOverlayTriggerState } from "react-stately"
import NormalDialog from "@/components/dialogs/NormalDialog"
import DateComponent from "@/components/forms/DateComponent"
import {ACCEPT_TERMS_OF_SERVICE} from "../constants"
import ImageCropDialog from "@/components/dialogs/ImageCropDialog"
import { handleFileUpload } from "@/utils/file-upload.util"

const currentYear = new Date().getFullYear()
const urlRegex =
  /^(https?|ftp):\/\/(([a-z\d]([a-z\d-]*[a-z\d])?\.)+[a-z]{2,}|localhost)(\/[-a-z\d%_.~+]*)*(\?[;&a-z\d%_.~+=-]*)?(#[-a-z\d_]*)?$/i

type EducationInfoType = {
  college: string
  degree_type: string
  year_graduated: string
}

const ProfilePage: FC = () => {
	const dispatch = useDispatch<AppDispatch>()
	const location = useLocation()
	const navigate = useNavigate()
	const pageId = location.pathname.replace(/\/$/, '').split('/').pop()
	const prfilePicRef = useRef<HTMLInputElement>(null)

	const authStore = useSelector((state: RootState) => state.auth)


	const formContent = authStore.registration.formContent[pageId!] || {}
	const registrationInfo = authStore.registration.formContent['create-account']
	const { userRegistrationToken } = authStore.registration
	const sidebarState = useAppSelector((state) => state.sidebar)
	const editFlag = useSelector(
		(state: RootState) => state.auth.registration.editFlag
	)
	// const client = useApiClient()
  const navToInvalidPgState = useOverlayTriggerState({})

  const [uploading, setUploading] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const imageCropState = useOverlayTriggerState({});

  const invalidMenus = new Set(sidebarState.registerTherapist?.invalidMenus || []);
  const showErrors = invalidMenus.has(pageId!);

  const validateTherapistRegistration = (): { isRegValid: boolean, newInvalidMenus: Set<string> } => {
    const formData = authStore.registration.formContent
    let isRegValid = true

    if (invalidMenus.size > 0) {
      isRegValid = false
    }

    for (const page of THERAPIST_REGISTRATION_PAGES) {
      const pageData = formData[page]

      const isMissingOrEmpty = !pageData || Object.keys(pageData).length === 0
      const hasInvalidFlag = pageData?.isInvalid === true

      // not checking modalities because it is optional page
      // not checking profile because it's validation is already controlled by Formik in this page and we have "isPageValid"
      if (page !== "modalities" && page !== "profile" && (isMissingOrEmpty || hasInvalidFlag)) {
        isRegValid = false
        invalidMenus.add(page)
      }
    }

    return { isRegValid, newInvalidMenus: invalidMenus }
  }

  const handlePageSubmit = async ({
    values,
    isPageValid
  }: {
    values: any
    isPageValid: boolean
  }) => {
    const { isRegValid: allowRegister, newInvalidMenus } = validateTherapistRegistration();

    if (!isPageValid) {
      newInvalidMenus.add(pageId!);
    } else {
      newInvalidMenus.delete(pageId!);
    }

    const userToken = editFlag ? authStore.user?.userToken : userRegistrationToken;
    const navigateToPage = editFlag ? "/profile" : `/auth/register/success?success=true&email=${values.email}`;

    if (!userToken) {
      notification.error("User Token is required")
      return
    }

    setIsLoading(true)

    dispatch(updateSidebarState({
      key: "registerTherapist",
      value: {
        ...sidebarState.registerTherapist,
        invalidMenus: Array.from(newInvalidMenus)
      }
    }));

    const res = await dispatch(registerTherapistAction(
      { values, isRegComplete: isPageValid && allowRegister },
      pageId!, 
      userToken
    ))

    setIsLoading(false)
    if (res?.status === 200 || res?.status === 201) {
      dispatch(updateRegistrationForm({ pageId, values }));

      if (!editFlag) {
        if (!isPageValid) {
          return;
        } else if (!allowRegister && isPageValid){
          navToInvalidPgState.open();
        } else if (isPageValid && allowRegister) {
          dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: true }));
          // dispatch(resetRegistration());
          // dispatch(resetSidebarState());
          // navigate(navigateToPage)
        }
      } else {
        navigate(navigateToPage)
      }
    }
  }

  return (
    <>
      <ToolbarComponent />
      <div className="flex-grow px-4 sm:px-6 md:px-8 lg:px-16 py-6 md:py-8">
        <h1 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark">Profile</h1>
        <div className="mt-6 md:mt-8">
          <Formik
            validateOnMount={true}
            initialValues={{
              first_name: editFlag
                ? authStore.user?.firstname || ""
                : registrationInfo?.first_name || formContent.first_name || "",
              last_name: editFlag
                ? authStore.user?.lastname || ""
                : registrationInfo?.last_name || formContent.last_name || "",
              email: editFlag
                ? authStore.user?.email || ""
                : registrationInfo?.email || formContent.email || "",            
              password: registrationInfo ? registrationInfo.password : formContent.password || "",
              dob: formContent.dob || "",
              user_profile: formContent.user_profile || "",
              educations: formContent.educations || [
                {
                  college: "",
                  degree_type: "",
                  year_graduated: "",
                },
              ],
              experience_years: formContent.experience_years || "",
              identify: formContent.identify || null,
              race: formContent.race || null,
              language: formContent.language || null,
              profile_statement: formContent.profile_statement || "",
              social_media: formContent.social_media || {},
              video_links: formContent.video_links || [
                {
                  url: "",
                },
              ],
            }}
            validate={(values) => {
              const errors: any = {}

              values.educations.forEach((education: EducationInfoType, index: number) => {
                const eduErrors: any = {}
                if (!education.college) eduErrors.college = "College name is required"
                if (!education.degree_type) eduErrors.degree_type = "Degree type is required"

                const year = Number(education.year_graduated)
                if (!education.year_graduated) {
                  eduErrors.year_graduated = "Graduation year is required"
                } else if (isNaN(year) || year < 1900 || year > currentYear) {
                  eduErrors.year_graduated = "Please enter a valid year"
                }

                if (Object.keys(eduErrors).length > 0) {
                  errors.educations = errors.educations || []
                  errors.educations[index] = eduErrors
                }
              })

              if (values.experience_years && isNaN(Number(values.experience_years)))
                errors.experience_years = "Please enter a valid number"

              if (!values.identify || !values.identify.checked) errors.identify = "Identify is a required"
              else if (values.identify.checked === "other" && !values.identify.other_identify)
                errors.identify = "Specify other Identify"

              if (!values.race || !values.race.checked) errors.race = "Race is a required"
              else if (values.race.checked === "other" && !values.race.other_race) errors.race = "Specify other Race"

              if (!values.language || values.language.checked_list.length === 0)
                errors.language = "Language is a required"
              else if (values.language.checked_list.includes("other") && !values.language.other_language)
                errors.language = "Specify other Language"

              if (values.profile_statement && values.profile_statement.length > 1500)
                errors.profile_statement = "Profile Statement should be less than 1500 characters"

              if (values.social_media && Object.keys(values.social_media).length > 0) {
                Object.keys(values.social_media).forEach((key) => {
                  if (!values.social_media[key]) {
                    errors.social_media = errors.social_media || {}
                    errors.social_media[key] = `Please enter ${key} profile link`
                  } else {
                    // Add http:// prefix if no protocol is specified
                    let url = values.social_media[key]
                    if (!/^https?:\/\//i.test(url)) {
                      url = `http://${url}`
                    }
                    
                    // Test the URL with the protocol
                  if (!urlRegex.test(url)) {
                      errors.social_media = errors.social_media || {}
                      errors.social_media[key] = `${key.charAt(0).toUpperCase() + key.slice(1)} profile link is invalid`
                    }
                  }
                })
              }

              if (values.video_links && values.video_links.length > 0) {
                if (values.video_links.length === 1) {
                  if (values.video_links[0].url && !urlRegex.test(values.video_links[0].url)) {
                    errors.video_links = errors.video_links || []
                    errors.video_links[0] = "Invalid URL"
                  }
                } else {
                  values.video_links.forEach((videoLink: any, index: number) => {
                    if (!urlRegex.test(videoLink.url)) {
                      errors.video_links = errors.video_links || []
                      errors.video_links[index] = "Invalid URL"
                    }
                  })
                }
              }

              return errors
            }}
            onSubmit={() => {}}
          >
            {({ values, errors, isValid, isValidating, touched, setFieldTouched, setFieldValue }) => (
              <form>
                <div className="w-full max-w-md">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    Enter Your Profile Information
                  </h3>

                  <InputComponent
                    value={values.first_name}
                    isDisabled
                    id="first_name"
                    class="mb-4 sm:mb-5"
                    label={"First Name"}
                    isRequired
                    placeholder="Type here ..."
                    inputClassName="bg-gray-100 text-gray-500 cursor-not-allowed"
                  />

                  <InputComponent
                    value={values.last_name}
                    isDisabled
                    class="mb-4 sm:mb-5"
                    id="last_name"
                    label={"Last Name"}
                    isRequired
                    placeholder="Type here ..."
                    inputClassName="bg-gray-100 text-gray-500 cursor-not-allowed"
                  />

                  <InputComponent
                    value={values.email}
                    name="email"
                    isDisabled
                    class="mb-4 sm:mb-5"
                    label={"Email Address"}
                    isRequired
                    placeholder="Type here ..."
                    type="email"
                    inputClassName="bg-gray-100 text-gray-500 cursor-not-allowed"
                  />

                  {registrationInfo && values.password && (
                    <InputComponent
                      value={values.password}
                      id="password"
                      errorMessage={errors.password && (errors.password as string)}
                      isDisabled
                      class="mb-4 sm:mb-5"
                      label={"Password"}
                      isRequired
                      placeholder="Type here ..."
                      type="password"
                      inputClassName="bg-gray-100 text-gray-500 cursor-not-allowed"
                      showToggleIcon={true}
                    />
                  )}

                  <section className="w-full mb-4 sm:mb-5">                    
                  <DateComponent
                      id="dob"
                      label="Date of Birth"
                      isRequired={false}
                      value={values.dob || ""}
                      maxDate={new Date()}
                      outputFormat="iso"
                      onChange={(dateOnly) => {
                        const isoDate = dateOnly ? new Date(dateOnly).toISOString() : null;
                        setFieldValue("dob", isoDate);
                      }}
                      />
                    {errors.dob && typeof errors.dob === "string" && (
                      <p style={{ color: "red", fontSize: 12 }} className="text-error">
                        {errors.dob}
                      </p>
                    )}
                  </section>

                  <div className="flex flex-row gap-2 mb-4 sm:mb-5">
                    <section className="w-full relative">
                      <input
                        accept="image/png, image/gif, image/jpeg"
                        type="file"
                        ref={prfilePicRef}
                        onChange={(e) => {
                          const file = e.target.files![0]

                          if (file == null) return;
                          if (file.size > 5 * 1024 * 1024) {
                            notification.error("File size should be less than 5MB")
                            return
                          }
                          setProfileImageFile(file);
                          imageCropState.open();
                        }}
                        style={{ display: "none" }}
                      />
                      <label className="text-sm block mb-3 text-gray-400">Profile Photo - JPEG, PNG, or GIF</label>

                      <div className="relative w-24 h-24">
                        {/* Profile Image */}
                        <span
                          onClick={() => {
                            if (uploading) return;
                            prfilePicRef.current!.click()
                          }}
                          className="h-24 w-24 cursor-pointer rounded-full border-4 border-gray-300 bg-gray-200 flex items-center justify-center user_registration_profile overflow-hidden relative"
                        >
                          {values.user_profile && !uploading ? (
                            <img
                              src={values.user_profile || `https://ui-avatars.com/api?name=${values.first_name}+${values.last_name}`}
                              className="h-full w-full object-cover rounded-full"
                              alt="Profile"
                            />
                          ) : uploading ? (
                            <i>uploading...</i>
                          ) : (
                            <i className="fas fa-camera text-white text-2xl"></i>
                          )}
                        </span>

                        {/* Edit Icon - Exactly at Bottom Right */}
                        <span
                          onClick={() => {
                            if (uploading) return;
                            prfilePicRef.current!.click()
                          }}
                          className="absolute bottom-0 right-0 bg-therapy-blue-dark text-white p-1.5 rounded-full shadow-md transition flex items-center justify-center border-2 border-white cursor-pointer"
                          style={{
                            width: "30px",
                            height: "30px",
                            transform: "translate(25%, 25%)",
                          }}
                        >
                          <i className="fas fa-edit text-xs"></i>
                        </span>
                      </div>
                    </section>

                    <ImageCropDialog
                      selectedFile={profileImageFile}
                      imageCropState={imageCropState}
                      isCircular={true}
                      aspectRatio={1}
                      onCropComplete={async (file) => {
                        try {
                          setUploading(true);
                          const uploadedFile = await handleFileUpload([file]);
                          if (uploadedFile && uploadedFile?.length > 0) {
                            const updatedProfile = uploadedFile[0].Location;
                            setFieldValue("user_profile", updatedProfile);

                            if (!isValid) {
                              dispatch(updateRegistrationForm({
                                pageId,
                                values: {
                                  ...values,
                                  user_profile: updatedProfile,
                                  isInvalid: true
                                }
                              }));
                            } else {
                              dispatch(updateRegistrationForm({
                                pageId,
                                values: {
                                  ...values,
                                  user_profile: updatedProfile,
                                }
                              }));
                            }
                          }
                        } finally {
                          setUploading(false);
                        }
                      }}
                    />
                  </div>
                </div>

                <div className="w-full max-w-md mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    Enter Your Education Experience
                  </h3>
                  <FieldArray name="educations">
                    {({ push, remove }) => (
                      <>
                        {values.educations.map((education: EducationInfoType, index: number) => (
                          <div key={index} className="mb-6 p-4 border rounded-lg">
                            <h2 className="text-lg font-semibold mb-3">Education #{index + 1}</h2>

                            <InputComponent
                              value={education.college}
                              errorMessage={
                                (showErrors || (touched.educations as any)?.[index]?.college) &&
                                (errors.educations as any)?.[index]?.college
                              }
                              onChange={(value) => {
                                setFieldTouched(`educations[${index}].college`)
                                const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                setFieldValue(`educations[${index}].college`, alphanumericValue)
                              }}
                              onBlur={() => setFieldTouched(`educations[${index}].college`)}
                              id={`educations[${index}].college`}
                              class="mb-3"
                              label="College/University"
                              isRequired
                              placeholder="Enter college name..."
                            />

                            <InputComponent
                              value={education.degree_type}
                              errorMessage={
                                (showErrors || (touched.educations as any)?.[index]?.degree_type) &&
                                (errors.educations as any)?.[index]?.degree_type
                              }
                              onChange={(value) => {
                                setFieldTouched(`educations[${index}].degree_type`)
                                const alphanumericValue = value.replace(/[^a-zA-Z0-9 ]/g, "")
                                setFieldValue(`educations[${index}].degree_type`, alphanumericValue)
                              }}
                              onBlur={() => setFieldTouched(`educations[${index}].degree_type`)}
                              id={`educations[${index}].degree_type`}
                              class="mb-3"
                              label="Degree Type"
                              isRequired
                              placeholder="Enter degree..."
                            />

                            <InputComponent
                              value={education.year_graduated}
                              errorMessage={
                                (showErrors || (touched.educations as any)?.[index]?.year_graduated) &&
                                (errors.educations as any)?.[index]?.year_graduated
                              }
                              onChange={(value) => {
                                setFieldTouched(`educations[${index}].year_graduated`)
                                setFieldValue(`educations[${index}].year_graduated`, value)
                              }}
                              onBlur={() => setFieldTouched(`educations[${index}].year_graduated`)}
                              id={`educations[${index}].year_graduated`}
                              class="mb-3"
                              label="Year Graduated"
                              isRequired
                              placeholder="YYYY"
                            />

                            {/* Remove button (only if more than 1 education exists) */}
                            {values.educations.length > 1 && (
                              <p onClick={() => remove(index)} className="text-danger underline cursor-pointer">
                                - Remove Education
                              </p>
                            )}
                          </div>
                        ))}

                        {/* Add New Education Button */}
                        <p
                          onClick={() =>
                            push({
                              college: "",
                              degree_type: "",
                              year_graduated: "",
                            })
                          }
                          className="text-therapy-blue-dark underline cursor-pointer mb-4 ml-4"
                        >
                          + Add Another Education
                        </p>
                      </>
                    )}
                  </FieldArray>
                </div>

                <div className="w-full max-w-md">
                  <InputComponent
                    value={values.experience_years}
                    errorMessage={
                      (showErrors || touched.experience_years) && errors.experience_years && (errors.experience_years as string)
                    }
                    onChange={(value) => {
                      setFieldTouched("experience_years")
                      const numericValue = value.replace(/[^0-9]/g, "")
                      setFieldValue("experience_years", numericValue)
                    }}
                    onBlur={() => setFieldTouched("experience_years")}
                    class="mt-6"
                    label="Experience Years"
                    placeholder="Type here ..."
                    maxLength={2}
                  />
                </div>

                <div className="mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    <span className="text-red-500 font-bold ml-1">*</span>How do you identify?
                  </h3>

                  <AppRadioComponent
                    checked={values.identify?.checked}
                    onChange={(val) => {
                      setFieldTouched("identify")
                      // handleChange({
                      //   target: { value: { checked: val }, name: "identify" },
                      // })
                      setFieldValue("identify", { checked: val })
                    }}
                    checkList={[
                      ...genders.filter((g) => g.value !== "all"),
                      {
                        label: "Prefer not to say",
                        value: "not-say",
                        id: "not-say",
                      },
                      {
                        label: "Other (Please specify)",
                        value: "other",
                        id: "other-identify",
                      },
                    ]}
                  />
                  {values.identify && values.identify.checked && values.identify.checked === "other" && (
                    <div className="w-full max-w-[300px] pl-5">
                      <InputComponent
                        value={values.identify.other_identify}
                        errorMessage={
                          (showErrors && touched.identify) && errors.identify as string
                        }
                        onChange={(val) => {
                          setFieldValue("identify", { ...values.identify, other_identify: val })
                        }}
                      />
                    </div>
                  )}
                  <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                    {(showErrors || touched.identify) && errors.identify && values.identify?.checked !== "other" && errors.identify.toString()}
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    <span className="text-red-500 font-bold ml-1">*</span>What Race Are You?
                  </h3>

                  <AppRadioComponent
                    checked={values.race?.checked}
                    onChange={(val) => {
                      setFieldTouched("race")
                      setFieldValue("race", { checked: val })
                    }}
                    checkList={race}
                  />
                  {values.race && values.race.checked && values.race.checked === "other" && (
                    <div className="w-full max-w-[300px] pl-5">
                      <InputComponent
                        value={values.race.other_race}
                        errorMessage={
                          (showErrors || touched.race) && errors.race as string
                        }
                        onChange={(val) => {
                          setFieldValue("race", { ...values.race, other_race: val })
                        }}
                      />
                    </div>
                  )}
                  <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                    {(showErrors || touched.race) && errors.race && values.race?.checked !== "other" && errors.race.toString()}
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    <span className="text-red-500 font-bold ml-1">*</span>What languages are you fluent in / use in your therapy practice?
                  </h3>

                  <AppCheckBoxListComponent
                    checked={values.language?.checked_list}
                    onChange={(val) => {
                      setFieldTouched("language")
                      if (!val.includes("other"))
                        setFieldValue("language", { checked_list: [...val] })
                      else
                        setFieldValue("language", { ...values.language, checked_list: [...val] })
                    }}
                    checkList={fluentLanguage}
                  />
                  {values?.language?.checked_list?.includes("other") && (
                      <div className="w-full max-w-[300px] pl-5">
                        <InputComponent
                          value={values.language.other_language}
                          errorMessage={
                            (showErrors || touched.language) && errors.language as string
                          }
                          onChange={(val) => {
                            setFieldValue("language", { ...values.language, other_language: val })
                          }}
                        />
                      </div>
                    )}
                  <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                    {(showErrors || touched.language) && errors.language && !values.language?.checked_list.includes("other") && errors.language.toString()}
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    Provide a Profile Statement
                  </h3>

                  <div>
                    <textarea
                      name="profile_statement"
                      value={values.profile_statement}
                      onChange={(e) => {
                        setFieldValue("profile_statement", e.target.value)
                      }}
                      className="registration_textarea w-full max-w-full"
                      maxLength={1500}
                      placeholder={
                        `Feel free to share information about yourself including, but not limited to:\n\n-Your personal journey to becoming a therapist\n-Your treatment approach\n-Your typical goals for clients\n-Your specialties, goals, or areas of focus\n`
                      }
                    />
                  </div>
                  <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
                    {errors.profile_statement && (
                      <div className="text-red-500 mt-2 ml-4">{errors.profile_statement as string}</div>
                    )}
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    Connect Social Media Accounts to Your Profile
                  </h3>

                  <AppCheckBoxInputComponent
                    checked={values.social_media}
                    onChange={(val) => {
                      setFieldTouched("social_media")
                      setFieldValue("social_media", val)
                    }}
                    itemList={[...socialMedias]}
                  />

                  {(showErrors || touched.social_media) && errors.social_media && Object.keys(errors.social_media).length > 0 && (
                    <div className="text-red-500 mt-2 ml-4">
                      {Object.keys(errors.social_media).map((key) => {
                        return <div key={key}>{(errors.social_media as any)[key]}</div>
                      })}
                    </div>
                  )}
                </div>

                <div className="w-full max-w-md mt-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-therapy-blue-dark mb-4 sm:mb-5">
                    Add Video Links to Your Profile
                  </h3>

                  <FieldArray name="video_links">
                    {({ push, remove }) => (
                      <>
                        {values.video_links.map((videoLink: any, index: number) => (
                          <div key={index} className="mb-6 p-4 border rounded-lg">
                            <InputComponent
                              value={videoLink.url}
                              errorMessage={
                                (showErrors || (touched.video_links as any)?.[index]) &&
                                (errors.video_links as any)?.[index]
                              }
                              onChange={(value) => {
                                setFieldTouched(`video_links[${index}].url`)
                                setFieldValue(`video_links[${index}].url`, value)
                              }}
                              onBlur={() => setFieldTouched(`video_links[${index}].url`)}
                              id={`video_links[${index}].url`}
                              class="mb-3"
                              placeholder="Paste video link here"
                            />

                            {/* Remove button (only if more than 1 video link exists) */}
                            {values.video_links.length > 1 && (
                              <p onClick={() => remove(index)} className="text-danger underline cursor-pointer">
                                - Remove Video Link
                              </p>
                            )}
                          </div>
                        ))}

                        {/* Add New Video Link Button */}
                        <p
                          onClick={() => push({ url: "" })}
                          className="text-therapy-blue-dark underline cursor-pointer mb-4 ml-4"
                        >
                          + Add Another Video Link
                        </p>
                      </>
                    )}
                  </FieldArray>
                </div>

                {/* Submit Button */}
                <div className="mt-6">
                  <SaveButton
                    disabled={isValidating}
                    value={`${editFlag ? "Save" : "Save & Continue"}`}
                    type="button"
                    loading={isLoading}
                    onClick={(e) => {
                      e.preventDefault();
                      handlePageSubmit({
                        values,
                        isPageValid: isValid,
                      });
                    }}
                  />
                </div>
              </form>
            )}
          </Formik>
        </div>
      </div>
      <NormalDialog
        opacity={80}
        state={navToInvalidPgState}
        title="Navigate to Invalid Page"
        confirmLabel="Ok"
        width="w-[500px]"
        primaryButtonColor={true}
        onAccept={async () => {
          dispatch(updateRegistrationState({ key: "navToInvalidPage", value: true }));
          dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: false }));
          dispatch(updateRegistrationState({ key: "shouldShowRegCompleteDialog", value: true }));
          const nextInvalidPage = getNextInvalidPage(pageId!, invalidMenus);
          nextInvalidPage && navigate(`/auth/register/${nextInvalidPage}`);
          navToInvalidPgState.close();
        }}
      >
        <p className="text-sm">
  {ACCEPT_TERMS_OF_SERVICE}
</p>

      </NormalDialog>
    </>
  )
}

export default ProfilePage

