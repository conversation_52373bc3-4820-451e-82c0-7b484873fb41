import { FC, useEffect, useRef, useState } from "react";
import { useApiClient } from "@/utils/api.util";
import { useNavigate, Link } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import { EMAIL_REQUIRED_ERROR,LOGO_PATH,YEAR } from './constants';

const ForgotPassword: FC = () => {
  const emailRef = useRef<HTMLInputElement>(null);
  const [processing, setProcessing] = useState(false);
  const navigate = useNavigate();
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");

  const client = useApiClient();
  const [credential, setCredential] = useState({ email: "" });

  useEffect(() => {
    emailRef.current?.focus();
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (processing) return;
  
    if (!credential.email.trim()) {
      setErrorMessage(EMAIL_REQUIRED_ERROR);
      return;
    }
  
    setProcessing(true);
    setErrorMessage("");
  
    client
      .post(`/auth/forgot-password`, credential)
      .then((res) => {
        setSuccessMessage(res?.data?.message ?? "Magic link sent to your email.");
        setCredential({ email: "" });
        setTimeout(() => {
          setSuccessMessage("");
          navigate("/auth/login");
        }, 3000);
      })
      .catch((err) => {
        setErrorMessage(err.response?.data?.message ?? "Something went wrong.");
      })
      .finally(() => {
        setProcessing(false);
      });
  };

  return (
    <section className="h-full w-full">
      <div className="flex flex-col md:grid md:grid-cols-2 h-full w-full">
        <div className="text-sm bg-white h-[30%] md:h-full text-white flex items-center relative justify-center flex-col">
          <span className="bg-transparent text-blue-600 w-[100%] rounded-full flex items-center justify-center">
          <img src={LOGO_PATH} alt="Next Therapists" className="w-[50%] md:w-[30%]" />

          </span>
        </div>
        <div className="flex-grow w-full relative flex flex-col items-start justify-center bg-orange-100 rounded-lg shadow" style={{background:'#e9d5ed'}}>
          <div className="px-[10%] md:px-[20%] w-full flex flex-col space-y-6">
            <div className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">
              Forgot your password?
            </div>
            <small className="tracking-tight leading-tight block w-[320px]">
              Enter your email address below and we'll send you a magic link to reset your password.
            </small>
            <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="email" className="block mb-6 text-sm font-bold text-gray-900">
                  E-mail
                </label>
                <input
                  type="email"
                  id="email"
                  ref={emailRef}
                  autoComplete="off"
                  className="bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5"
                  placeholder="your email address"
                  value={credential.email}
                  onChange={(e) => setCredential({ ...credential, email: e.target.value })}
                />
              </div>
              <AnimatePresence>
                {successMessage ? (
                  <motion.div
                    key={0}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 50 }}
                    className="flex items-center justify-between"
                  >
                    <small className="text-green-800 font-medium">{successMessage}</small>
                  </motion.div>
                ) : (
                  errorMessage && (
                    <motion.div
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 50 }}
                      className="flex items-center justify-between"
                    >
                      <small className="text-red-800 font-medium">{errorMessage}</small>
                    </motion.div>
                  )
                )}
              </AnimatePresence>
              <div className="flex items-center justify-between">
                <small className="tracking-tight leading-tight block w-[320px]">
                  Go back to{" "}
                  <Link to="/auth/login" className="font-semibold hover:cursor-pointer hover:underline">
                    Log In Page
                  </Link>
                </small>
                <small className="font-medium text-stone-800 cursor-pointer hover:underline"></small>
              </div>
              <button
                type="submit"
                className="flex flex-row items-center justify-center disabled:bg-gray-500 bg-therapy-blue-dark disabled:cursor-stop w-full text-white bg-therapy-blue-dark focus:ring-0 focus:outline-none font-medium rounded-lg text-sm px-5 py-2 text-center"
              >
                {processing && <i className="fa-duotone fa-loader animate-spin mr-2"></i>}
                Send Magic Link
              </button>
            </form>
          </div>
          <small className="absolute bottom-[64px] right-[20%]">&copy; NextTherapist, {YEAR}</small>
        </div>
      </div>
    </section>
  );
};

export default ForgotPassword;
