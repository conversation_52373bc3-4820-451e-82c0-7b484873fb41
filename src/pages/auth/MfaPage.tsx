import { FC, useEffect, useRef, useState } from "react";
import { useApiClient } from "@/utils/api.util";
import { Navigate } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import { LOGO_PATH } from "./constants";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { signIn } from "@/store/slicers/auth.slicer";
import notification from "@/utils/notification.util";

const MfaPage: FC = () => {
	const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
	const client = useApiClient();
	const dispatch = useAppDispatch();
	const email = new URLSearchParams(window.location.search).get("email");

  const codeRef = useRef<HTMLInputElement>(null);
  const [processing, setProcessing] = useState(false);
  const [resending, setResending] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [credential, setCredential] = useState({
    code: "",
  });
	const [shouldNavigate, setShouldNavigate] = useState(isAuthenticated);
	const [resendTimer, setResendTimer] = useState(60);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    codeRef.current?.focus();
  }, []);

	useEffect(() => {
		setShouldNavigate(isAuthenticated)
	}, [isAuthenticated]);

	useEffect(() => {
    const clearTimerInterval = () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }
    };

    if (resendTimer > 0) {
      clearTimerInterval();
      timerIntervalRef.current = setInterval(() => {
        setResendTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else {
      clearTimerInterval();
    }

    return () => {
      clearTimerInterval();
    };
  }, [resendTimer]);

	if (shouldNavigate) {
    return <Navigate to="/dashboard" />
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (processing || !credential.code || !email) {
      return;
    }
    setProcessing(true);
    client
      .post(`/auth/verify-mfa`, {
        ...credential,
        email,
      })
      .then(({ data }) => {
        dispatch(
          signIn({
            token: data.accessToken,
            refreshToken: data.refreshToken,
            user: data.user,
          }),
        );
      })
      .catch((err) => {
        setErrorMessage(err.response?.data?.message ?? "Unable to signin, please try again.");
        setTimeout(() => {
          setErrorMessage("");
        }, 3000);
      })
      .finally(() => {
        setProcessing(false);
      });
  };

	const handleResendCode = () => {
		if (processing || resending || resendTimer > 0 || !email) {
      return;
    }

		setResending(true);
		client
      .post(`/auth/resend-mfa`, {
        email,
      })
      .then((res) => {
				notification.success(res.data.message);
				setResendTimer(60);
      })
      .catch((err) => {
        setErrorMessage(err.response?.data?.message ?? "Unable to send code, please try again.");
        setTimeout(() => {
          setErrorMessage("");
        }, 3000);
      })
      .finally(() => {
        setResending(false);
      });
	}

  return (
    <section className="h-full w-full">
      <div className="flex flex-col md:grid md:grid-cols-2 h-full w-full">
        <div className="text-sm bg-white h-[30%] md:h-full text-white flex items-center relative justify-center flex-col">
          <span className="w-full max-w-xs sm:max-w-sm md:max-w-md px-4">
          <img src={LOGO_PATH} alt="Next Therapists" className="w-[50%] md:w-[30%]" />
          </span>
        </div>
        <div className="flex-grow w-full relative flex flex-col items-start justify-center rounded-lg shadow" style={{background:'#e9d5ed'}}
 >
          <div className="px-[10%] md:px-[20%] w-full flex flex-col space-y-6">
            <div className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">
              Multi Factor Authentication
            </div>
            <small className="tracking-tight leading-tight block w-[320px]">
              Enter the 6-digit code sent to your email address and 
              then click on Sign In to signin to your account.
            </small>
            <MfaForm
              credential={credential}
              setCredential={setCredential}
              processing={processing}
              resending={resending}
              resendTimer={resendTimer}
              errorMessage={errorMessage}
              handleSubmit={handleSubmit}
              handleResendCode={handleResendCode}
            />
          </div>
          <small className="absolute bottom-[64px] right-[20%]">&copy; NextTherapist, 2025</small>
        </div>
      </div>
    </section>
  );
};

export default MfaPage;

interface MfaFormProps {
  credential: { code: string };
  setCredential: (val: { code: string }) => void;
  processing: boolean;
  resending: boolean;
  resendTimer: number;
  errorMessage: string;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  handleResendCode: () => void;
}

const MfaForm: FC<MfaFormProps> = ({
  credential,
  setCredential,
  processing,
  resending,
  resendTimer,
  errorMessage,
  handleSubmit,
  handleResendCode
}) => {
  const codeRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    codeRef.current?.focus();
  }, []);

  const renderResendLabel = () => {
    if (resending) return (<><i className="fa-duotone fa-loader animate-spin mr-1"></i>Resending...</>);
    if (resendTimer > 0) return `Resend in ${resendTimer}`;
    return "Resend Code";
  };

  return (
    <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
      <div>
        <label htmlFor="code" className="block mb-2 text-sm font-bold text-gray-900">
          6-Digit Code
        </label>
        <input
          type="text"
          id="code"
          required
          maxLength={6}
          ref={codeRef}
          value={credential.code}
          autoComplete="off"
          className="bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5"
          placeholder="your 6-digit code"
          onChange={(e) => setCredential({ ...credential, code: e.target.value })}
        />
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <p className="text-xs md:text-[12px] tracking-tight leading-tight block">
          Don't receive the code?{" "}
          <button
            type="button"
            className={`font-semibold inline-flex items-center ${
              resending || resendTimer > 0
                ? "text-gray-500 cursor-not-allowed"
                : "text-stone-800 cursor-pointer hover:underline"
            }`}
            onClick={handleResendCode}
          >
            {renderResendLabel()}
          </button>
        </p>
      </div>

      <AnimatePresence>
        {errorMessage && (
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 50 }}
            className="flex items-center justify-between"
          >
            <small className="text-red-800 font-medium">{errorMessage}</small>
          </motion.div>
        )}
      </AnimatePresence>

      <button
        type="submit"
        className="flex flex-row items-center justify-center disabled:bg-gray-500 disabled:cursor-stop w-full text-white bg-therapy-blue-dark focus:ring-0 focus:outline-none font-medium rounded-lg text-sm px-5 py-2 text-center"
        disabled={processing || resending}
      >
        {processing && <i className="fa-duotone fa-loader animate-spin mr-2"></i>}
        Sign In
      </button>
    </form>
  );
};
