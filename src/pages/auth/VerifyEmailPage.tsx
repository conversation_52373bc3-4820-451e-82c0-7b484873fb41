import { FC, useEffect, useRef, useState } from "react";
import { useApiClient } from "@/utils/api.util";
import { Link, useNavigate } from "react-router-dom";
import { HttpStatusCode } from "axios";
import { AnimatePresence, motion } from "framer-motion";
import { LOGO_PATH ,YEAR } from "./constants";

const MessageBanner: FC<{ success: string; error: string }> = ({ success, error }) => {
  const message = success || error;
  const color = success ? "text-green-800" : "text-red-800";

  return (
    <AnimatePresence>
      {message && (
        <motion.div
          key={0}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 50 }}
          className="flex items-center justify-between"
        >
          <small className={`${color} font-medium`}>{message}</small>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const LoginRedirectMessage: FC = () => (
  <div className="flex items-center justify-between">
    <small className="text-stone-800">
      Go back to{" "}
      <Link to="/auth/login" className="font-semibold hover:cursor-pointer hover:underline">
        Log In Page
      </Link>
    </small>
  </div>
);

const VerifyEmailPage: FC = () => {
  const codeRef = useRef<HTMLInputElement>(null);
  const [processing, setProcessing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [isVerified, setIsVerified] = useState(false);
  const [showLoginButton, setShowLoginButton] = useState(false);
  
  const navigate = useNavigate();
  const client = useApiClient();
  const [credential, setCredential] = useState({
    code: "",
  });
  
  useEffect(() => {
    codeRef.current?.focus();
  }, []);

  const handleSuccess = (message: string, role: string) => {
    const fullMessage = `${message || "Email verified successfully."}`;

    setCredential({ code: "" });
    setIsVerified(true);

    if (role === "therapist") {
      setSuccessMessage(`${fullMessage} Redirecting to next page...`);
      setShowLoginButton(true);
      redirectWithDelay("/auth/login", 2000, 3000);
    } else if (role === "patient") {
      setSuccessMessage(`${fullMessage} Please log in from the mobile app.`);
      setShowLoginButton(false);
    }
  };

  const redirectWithDelay = (path: string, firstDelay: number, secondDelay: number) => {
    setTimeout(() => {
      setTimeout(() => {
        navigate(path);
      }, secondDelay);
    }, firstDelay);
  };

  const handleError = (err: any) => {
    setErrorMessage(err.response?.data?.message || "Unable to verify, try again later.");
    setTimeout(() => setErrorMessage(""), 3000);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (processing || !credential.code) return;
    setProcessing(true);

   try {
      const token = new URLSearchParams(window.location.search).get("token");
      const email = new URLSearchParams(window.location.search).get("email");
      const res = await client.post(`/auth/verify-email`, { ...credential, token, email });

      if (res.status === HttpStatusCode.Created) {
        handleSuccess(res.data.message, res.data.role);
      } else {
        setErrorMessage("Something went wrong. Please try again.");
      }
    } catch (err: any) {
      handleError(err);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <section className="h-full w-full">
      <div className="flex flex-col md:grid md:grid-cols-2 h-full w-full">
        <div className="text-sm bg-white h-[30%] md:h-full text-white flex items-center relative justify-center flex-col">
          <span className="w-full max-w-xs sm:max-w-sm md:max-w-md px-4">
          <img src={LOGO_PATH} alt="Next Therapists" className="w-[50%] md:w-[30%]" />
          </span>
        </div>
        <div className="flex-grow w-full relative flex flex-col items-start justify-center bg-orange-100 rounded-lg shadow"style={{background:'#e9d5ed'}}>
          <div className="px-[10%] md:px-[20%] w-full flex flex-col space-y-6">
            <div className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">
              Verify your email address.
            </div>
            <small className="tracking-tight leading-tight block w-[320px]">
              Enter the 6-digit code sent to your email address and then click on verify email button to verify your email.
            </small>
            <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="code" className="block mb-2 text-sm font-bold text-gray-900">
                  6-Digit Code
                </label>
                <input
                  type="text"
                  id="code"
                  required
                  maxLength={6}
                  ref={codeRef}
                  value={credential.code}
                  autoComplete="off"
                  className="bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5"
                  placeholder="your 6-digit code"
                  onChange={(e) => setCredential({ ...credential, code: e.target.value })}
                  disabled={isVerified}
               />
              </div>
              <MessageBanner success={successMessage} error={errorMessage} />
              {showLoginButton && <LoginRedirectMessage />}
              <button
                type="submit"
                className="flex flex-row items-center justify-center disabled:bg-gray-500 disabled:cursor-stop w-full text-white bg-therapy-blue-dark focus:ring-0 focus:outline-none font-medium rounded-lg text-sm px-5 py-2 text-center"
                disabled={isVerified || processing}
              >
                {processing && <i className="fa-duotone fa-loader animate-spin mr-2"></i>}
                Verify Email
              </button>
            </form>
          </div>
          <small className="absolute bottom-[64px] right-[20%]">&copy; NextTherapist, {YEAR}</small>
        </div>
      </div>
    </section>
  );
};

export default VerifyEmailPage;
