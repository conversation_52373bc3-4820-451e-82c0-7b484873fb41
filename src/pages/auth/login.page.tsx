import type React from "react"
import { type FC, useEffect, useRef, useState } from "react"
import { useApiClient } from "@/utils/api.util"
import { Navigate, useNavigate } from "react-router-dom"
import { Link } from "react-router-dom"
import { resetRegistration, signIn } from "@/store/slicers/auth.slicer"
import toast from "react-hot-toast"
import notification from "@/utils/notification.util"
import { LOGO_PATH,YEAR } from "../../../src/pages/auth/constants";
import { useAppSelector, useAppDispatch } from "@/store/hooks"
import { useOverlayTriggerState } from "react-stately"
import AccountStatusDialog from "@/components/dialogs/AccountStatusDialog"
import AccountDeleteDialog from "@/components/dialogs/AccountDeleteDialog"
import EmailVerificationDialog from "@/components/dialogs/EmailVerificationDialog"
import { resetSidebarState } from "@/store/slicers/sidebar.slicer"

const Page: FC = () => {
  const emailRef = useRef<HTMLInputElement>(null)
  const passwordRef = useRef<HTMLInputElement>(null)
  const [processing, setProcessing] = useState(false)

  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const [error, setError] = useState<string | null>(null);
  const [emailVerificationError, setEmailVerificationError] = useState(false);
  const client = useApiClient();
  const [credential, setCredential] = useState({
    email: "",
    password: "",
  })
  const [shouldNavigate, setShouldNavigate] = useState(isAuthenticated)
  const [userId, setUserId] = useState<number | null>(null);
  const [isDeletedByAdmin, setIsDeletedByAdmin] = useState<boolean>(false);

  const accountUnderReviewState = useOverlayTriggerState({});
  const accountRejectedState = useOverlayTriggerState({});
  const accountDeletedState = useOverlayTriggerState({});
  const accountIncompleteState = useOverlayTriggerState({});
  const emailVerificationState = useOverlayTriggerState({});

  useEffect(() => {
    emailRef.current?.focus()
  }, [])

  useEffect(() => {
    setShouldNavigate(isAuthenticated)
  }, [isAuthenticated])

  if (shouldNavigate) return <Navigate to="/dashboard" />

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setProcessing(true)
    client
      .post("/auth/login", credential)
      .then((res) => {
        setProcessing(false);
        if (res && res.status === 200) {
          if (res.data.accessToken && res.data.refreshToken && res.data.user) {
            dispatch(
              signIn({
                token: res.data.accessToken,
                refreshToken: res.data.refreshToken,
                user: res.data.user,
              }),
            );
          } else if (res.data.mfaCodeSent) {
            notification.success(res.data.message);
            navigate(`/auth/mfa?email=${credential.email}`);
          }
        }
      })
      .catch((err) => {
        setProcessing(false);
        if (err.response && err.response.data) {
          if (err.response.status === 403) {
            if (err.response.data?.data?.accountUnderReview) {
              accountUnderReviewState.open();
            }
            else if (err.response.data?.data?.emailNotVerified) {
              setEmailVerificationError(true);
              if (err.response.data?.data?.userId) {
                const id = err.response.data.data.userId;
                setUserId(id);
                emailVerificationState.open();
              } else {
                notification.error("Cannot verify email: User ID not found in response");
              }
            }
            else if (err.response.data?.data?.accountIncomplete) {
              accountIncompleteState.open();
              if (err.response.data?.data?.userId) setUserId(err.response.data.data.userId);
            }
            else if (err.response.data?.data?.accountRejected) {
              accountRejectedState.open();
              if (err.response.data?.data?.userId) setUserId(err.response.data.data.userId);
            }
            else if(err.response.data?.data?.accountDeleted) {
              setIsDeletedByAdmin(false);
              accountDeletedState.open();
              if (err.response.data?.data?.userId) setUserId(err.response.data.data.userId);
            }
            else if(err.response.data?.data?.accountDeletedByAdmin) {
              setIsDeletedByAdmin(true);
              accountDeletedState.open();
              if (err.response.data?.data?.userId) setUserId(err.response.data.data.userId);
            }
            else {
              setError(err.response.data.message);
              setTimeout(() => {
                setError(null);
              }, 4000);
            }
          } else {
            setError(err.response.data.message);
            setTimeout(() => {
              setError(null);
            }, 4000);
          }          
        }
      });
  };

  useEffect(() => {
    toast.dismiss();
  }, []);
  
  return (
    <>
      <section className="h-full w-full">
      <div className="flex flex-col md:grid md:grid-cols-2 h-full w-full">
  <div className="bg-white py-8 md:py-0 md:h-full flex items-center justify-center">
    <div className="flex justify-center items-center w-full px-4">
      <img
        src={LOGO_PATH}
        alt="Next Therapists"
        className="w-2/3 md:w-3/4 max-w-[280px]"
      />
    </div>
  </div>

        <div className="flex-1 w-full flex flex-col items-center justify-center p-6 md:p-8 lg:p-10" style={{background:'#e9d5ed'}}>
          <div className="w-full max-w-md space-y-6">
            <div>
              <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl">
                Get started today.
              </h1>
              <p className="mt-2 text-sm text-gray-700">Empathy, Support, and Healing—Right at Your Fingertips</p>
            </div>

              <form className="space-y-4 md:space-y-6" onSubmit={handleSubmit}>
                <div>
                  <label htmlFor="email" className="block mb-4 text-sm font-bold text-gray-900">
                    Login Details
                  </label>

                  <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-3">
                    <input
                      type="email"
                      id="email"
                      required
                      ref={emailRef}
                      autoComplete="off"
                      className="bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5"
                      placeholder="your email address"
                      onChange={(e) => setCredential({ ...credential, email: e.target.value })}
                    />
                    <input
                      type="password"
                      id="password"
                      ref={passwordRef}
                      required
                      autoComplete="off"
                      className="bg-gray-50 border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:outline-none border-2 focus:ring-stone-700 focus:border-stone-700 block w-full p-2.5"
                      placeholder="your password"
                      onChange={(e) => setCredential({ ...credential, password: e.target.value })}
                    />
                  </div>
                  {error && (
                    <div className="flex items-center px-1 mt-2">
                      <p className="text-sm text-red-800 font-medium">{error}</p>
                    </div>
                  )}

                  {emailVerificationError && (
                    <div className="flex flex-col sm:flex-row sm:items-center px-1 mt-2 gap-1">
                      <p className="text-sm text-red-800 font-medium">
                        Email not verified. Please verify your email address.
                      </p>
                      <button
                        onClick={() => emailVerificationState.open()}
                        className="text-sm font-semibold text-stone-800 hover:underline text-left"
                        type="button"
                      >
                        Verify Email
                      </button>
                    </div>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <p className="text-xs md:text-[12px] text-gray-700">
                Don't have an account?{" "}
                <Link
                  to="/auth/register/create-account"
                  className="font-semibold text-stone-800 hover:underline"
                  onClick={() => {
                    dispatch(resetRegistration());
                    dispatch(resetSidebarState());
                  }}
                >
                  Sign Up
                </Link>
                </p>
                
                  <Link to="/auth/forgot-password" className="text-sm md:text-[12px] font-semibold text-stone-800 hover:underline">
                    Forgot Password?
                  </Link>
                </div>

                <button
                  type="submit"
                  disabled={processing}
                  className="flex flex-row items-center justify-center w-full text-white bg-therapy-blue-dark focus:ring-0 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 text-center disabled:bg-gray-500 disabled:cursor-not-allowed"
                >
                  {processing && (
                    <span className="mr-2">
                      <i className="fa-duotone fa-loader animate-spin"></i>
                    </span>
                  )}
                  Sign In
                </button>
              </form>
            </div>

            <div className="w-full max-w-md mt-8 text-right">
              <p className="text-xs text-gray-600">&copy; NextTherapist, {YEAR}</p>
            </div>
          </div>
        </div>
      </section>

      <AccountStatusDialog
        underReviewState={accountUnderReviewState}
        rejectedState={accountRejectedState}
        incompleteState={accountIncompleteState}
        userId={userId}
        email={credential.email}
      />
      <AccountDeleteDialog
        underReviewState={accountUnderReviewState}
        deletedState={accountDeletedState}
        incompleteState={accountIncompleteState}
        userId={userId}
        email={credential.email}
        password={credential.password}
        isDeletedByAdmin={isDeletedByAdmin}
      />
      <EmailVerificationDialog
        verificationState={emailVerificationState}
        userId={userId}
        email={credential.email}
        password={credential.password}
        onVerificationSuccess={() => {
          setEmailVerificationError(false)
        }}
      />
    </>
  )
}

export default Page

