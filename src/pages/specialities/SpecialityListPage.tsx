import AddButton from "@/components/buttons/AddButton";
import PaginatedDataContainer from "@/components/PaginatedDataContainer";
import SpecialityRepository from "@/repositories/SpecialityRepository";
import { Speciality } from "@/types/speciality.interface";
import { useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { FC, useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { useOverlayTriggerState } from "react-stately";
import SpecialityForm from "./SpecialityForm";
import DeleteDialog from "@/components/dialogs/DeleteDialog";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import ActionButton from "@/components/buttons/ActionButton";
import Meta from "@/types/meta.interface";

interface IApplicationProps {}

const SpecialityListPage: FC<IApplicationProps> = () => {
  const repo = new SpecialityRepository();
  const { ref } = useInView();
  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});

  const [params,] = useState({});
  const [speciality, setSpeciality] = useState<Speciality>();

  const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
    queryKey: ["specialities", params],    
    queryFn: ({ pageParam = 1 }) => repo.getAll({
      perPage: 20,
      page: pageParam,
      ...params,
    }),    
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  useEffect(() => {
    if (!state.isOpen) {
      setSpeciality(undefined);
    }
  }, [state.isOpen]);

  useEffect(() => {
    if (!deleteState.isOpen && speciality) {
      setSpeciality(undefined);
    }
  }, [deleteState.isOpen]);

  return (
    <PaginationAwareContainer
      meta={meta}
      onLoad={fetchNextPage}
      fetching={isLoading}
    >
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">Speciality List</div>
          <div>
            <AddButton label="Add Speciality" onPress={state.open} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <div></div>
          <div className="col-span-2">Name</div>
          <div className="col-span-7">Description</div>
          <div className="col-span-2 hidden md:block"></div>
        </div>
        <div className="flex-grow">
          {error ? (
            <span>Error: {JSON.stringify(error)}</span>
          ) : (
            <PaginatedDataContainer meta={meta}>
              <>
                {data?.pages
                  .flatMap((page) => page.data)
                  .map((specialityData: Speciality, index: number) => (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      {specialityData && (
                        <>
                          <div className="flex items-center">
                            <div
                              className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center bg-gray-100`}
                            >
                              {index + 1}
                            </div>
                          </div>
                          <div className="col-span-2 flex items-center">
                            <strong>{specialityData.name}</strong>
                          </div>
                          <div className="col-span-7">
                            {specialityData.description}
                          </div>
                          <div className="col-span-2 hidden md:flex flex-row items-center justify-end space-x-2 text-white">
                            <ActionButton
                              onEdit={() => {
                                setSpeciality(speciality);
                                state.open();
                              }}
                              onDelete={() => {
                                setSpeciality(speciality);
                                deleteState.open();
                              }}
                            />
                          </div>
                        </>
                      )}
                    </motion.div>
                  ))}
              </>
            </PaginatedDataContainer>
          )}
        </div>
        <SpecialityForm
          state={state}
          speciality={speciality}
          repo={repo}
          refetch={refetch}
        />
        <DeleteDialog
          refetch={refetch}
          state={deleteState}
          verifyText={speciality?.name}
          title={"Delete Speciality"}
          url={`/specialities/${speciality?.id}`}
        >
          <p className="text-sm">
            Are you sure you want to delete this speciality? This process is not
            reversible.
          </p>
        </DeleteDialog>
      </div>
    </PaginationAwareContainer>
  );
};

export default SpecialityListPage;
