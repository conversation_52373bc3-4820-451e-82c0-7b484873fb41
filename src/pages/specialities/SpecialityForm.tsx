import FormDialog from "@/components/dialogs/FormDialog";
import InputComponent from "@/components/forms/InputComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import SpecialityRepository from "@/repositories/SpecialityRepository";
import { Speciality } from "@/types/speciality.interface";
import { FormEvent, useEffect, useState } from "react";
import { OverlayTriggerState } from "react-stately";

type SpecialityFormProps = {
  state: OverlayTriggerState;
  speciality?: Speciality;
  repo: SpecialityRepository;
  refetch?: () => void;
};

type SpecialityFormData = {
  name?: string;
  description?: string;
};

const SpecialityForm = ({
  state,
  speciality,
  repo,
  refetch,
}: SpecialityFormProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<SpecialityFormData>(
    {} as SpecialityFormData
  );

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    let result;
    setLoading(true);
    if (speciality?.id) {
      result = await repo.update(speciality.id, formData);
    } else {
      result = await repo.create(formData);
    }
    setLoading(false);
    if (result) {
      refetch && refetch();
      setFormData({} as SpecialityFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({} as SpecialityFormData);
    state.close();
  };

  useEffect(() => {
    if (speciality) {
      setFormData({
        name: speciality.name,
        description: speciality.description,
      });
    } else {
      setFormData({} as SpecialityFormData);
    }
  }, [speciality]);

  return (
    <FormDialog
      title="Speciality Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel="Save"
      loading={loading}
      width="w-[420px]"
      state={state}
    >
      <div className="flex flex-col space-y-4">
        <InputComponent
          label={"Name"}
          id="name"
          onChange={(value) => setFormData({ ...formData, name: value })}
          value={formData.name || ""}
          isRequired
        />
        <TextAreaComponent
          label={"Description"}
          id="description"
          onChange={(value) => setFormData({ ...formData, description: value })}
          value={formData.description || ""}
        />
      </div>
    </FormDialog>
  );
};

export default SpecialityForm;
