import { FC } from "react";
import { useInView } from "react-intersection-observer";

interface IApplicationProps {}

const Setting: FC<IApplicationProps> = () => {
  const { ref } = useInView();

  return (
    <div
      className="pr-3 pt-8 pb-4 px-16 h-full flex flex-col flex-grow"
      ref={ref}
    >
      <div className="flex-none flex flex-row justify-between items-center mb-4">
        <div className="font-thin text-3xl">Settings</div>
        <div className="flex flex-row space-x-2"></div>
      </div>
    </div>
  );
};

export default Setting;
