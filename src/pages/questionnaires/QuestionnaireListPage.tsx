import AddButton from "@/components/buttons/AddButton";
import PaginatedDataContainer from "@/components/PaginatedDataContainer";
import QuestionnaireRepository from "@/repositories/QuestionnaireRepository";
import { Questionnaire } from "@/types/questionnaire.interface";
import { useInfiniteQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { FC, useEffect, useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { useOverlayTriggerState } from "react-stately";
import DeleteDialog from "@/components/dialogs/DeleteDialog";
import PaginationAwareContainer from "@/components/PaginationAwareContainer";
import ActionButton from "@/components/buttons/ActionButton";
import Meta from "@/types/meta.interface";
import QuestionForm from "./QuestionForm";
import InputComponent from "@/components/forms/InputComponent";
import { debounce } from "lodash";

interface IApplicationProps {}

const QuestionnaireListPage: FC<IApplicationProps> = () => {
  const questionnaireRepo = new QuestionnaireRepository();
  const { ref } = useInView();
  const state = useOverlayTriggerState({});
  const deleteState = useOverlayTriggerState({});

  const [params, setParams] = useState({});
  const [questionnaire, setQuestionnaire] = useState<Questionnaire>();

  const { error, data, isLoading, fetchNextPage, refetch } = useInfiniteQuery({
    queryKey: ["questionnaires", params],    
    queryFn: ({ pageParam = 1 }) => questionnaireRepo.getAll({
      perPage: 20,
      page: pageParam,
      ...params,
    }),   
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.meta.nextPage,
    refetchOnWindowFocus: false,
  });

  const debouncedSearch = debounce((value: string) => {
    setParams({ ...params, search: value });
  }, 500);

  const meta = useMemo(() => {
    if (!data?.pages?.length) return {} as Meta;
    return data.pages[data.pages.length - 1].meta;
  }, [data]);

  useEffect(() => {
    if (!state.isOpen) {
      setQuestionnaire(undefined);
    }
  }, [state.isOpen, state.isOpen]);

  useEffect(() => {
    if (!deleteState.isOpen && questionnaire) {
      setQuestionnaire(undefined);
    }
  }, [deleteState.isOpen]);

  return (
    <PaginationAwareContainer meta={meta} onLoad={fetchNextPage} fetching={isLoading}>
      <div className="px-16 h-full flex flex-col flex-grow" ref={ref}>
        <div className="flex-none flex flex-row justify-between items-center mb-4">
          <div className="font-thin text-3xl">Questionnaire List</div>
          <div className="flex flex-row space-x-2 overflow-hidden">
            <AddButton label="Add Question" onPress={state.open} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12">
          <div className="col-span-3">
            <InputComponent id="search" placeholder="Search..." aria-labelledby="search" onChange={debouncedSearch} />
          </div>
        </div>
        <div className="flex-none grid grid-cols-12 mt-8 pb-2 px-2 text-sm text-gray-400">
          <span></span>
          <span className="col-span-7">Question</span>
          <span className="col-span-2"></span>
          <span></span>
        </div>
        <div className="flex-grow">
          {error ? (
            <span>Error: {JSON.stringify(error)}</span>
          ) : (
            <PaginatedDataContainer meta={meta}>
              <>
                {data?.pages
                  .flatMap((page) => page.data)
                  .map((questionnaireData: Questionnaire, index: number) => (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      <>
                        <div className="flex items-center">
                          <div>
                            <div
                              className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center ${
                                questionnaireData.required ? "bg-red-100 text-red-700" : "bg-gray-100"
                              }`}
                            >
                              {index + 1}
                            </div>
                          </div>
                        </div>
                        <div className="col-span-7 flex items-center">
                          <strong>{questionnaireData.question}</strong>
                        </div>
                        <div className="col-span-2 flex text-green-700 flex-row items-center justify-start"></div>
                        <div className="col-span-2 flex flex-row items-center justify-end space-x-4 text-white">
                          <ActionButton
                            onEdit={() => {
                              setQuestionnaire(questionnaire);
                              state.open();
                            }}
                            onDelete={() => {
                              deleteState.open();
                              setQuestionnaire({
                                ...questionnaireData,
                              });
                            }}
                          />
                        </div>
                      </>
                    </motion.div>
                  ))}
              </>
            </PaginatedDataContainer>
          )}
        </div>
        <QuestionForm state={state} questionnaire={questionnaire} refetch={refetch} />
        <DeleteDialog
          state={deleteState}
          verifyText={"DELETE"}
          title={"Delete Question"}
          url={`/questionnaires/${questionnaire?.id}`}
          refetch={refetch}
        >
          <p className="text-sm">Are you sure you want to delete this question? This process is not reversible.</p>
        </DeleteDialog>
      </div>
    </PaginationAwareContainer>
  );
};

export default QuestionnaireListPage;
