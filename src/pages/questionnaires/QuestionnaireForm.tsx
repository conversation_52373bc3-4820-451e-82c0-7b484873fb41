import FormDialog from "@/components/dialogs/FormDialog";
import CheckBoxComponent from "@/components/forms/CheckBoxComponent";
import InputComponent from "@/components/forms/InputComponent";
import NormalSelectComponent from "@/components/forms/NormalSelectComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import QuestionnaireRepository from "@/repositories/QuestionnaireRepository";
import { Questionnaire } from "@/types/questionnaire.interface";
import { questionnaireTypeList } from "@/utils/enum.util";
import { FormEvent, useEffect, useState } from "react";
import { toast } from "react-hot-toast";
import { OverlayTriggerState } from "react-stately";
import { v4 as uuid4 } from "uuid";

type QuestionnaireFormProps = {
  state: OverlayTriggerState;
  questionnaire?: Questionnaire;
  repo: QuestionnaireRepository;
  refetch?: () => void;
};

type QuestionnaireFormData = {
  question: string;
  type: string;
  patientSort: number;
  therapistSort: number;
  required: boolean;
  category?: string[];
  answers?: any[];
  info?: string;
  removedAnswers?: string[];
};

const QuestionnaireForm = ({
  state,
  questionnaire,
  repo,
  refetch,
}: QuestionnaireFormProps) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<QuestionnaireFormData>(
    {} as QuestionnaireFormData
  );

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (loading) return;
    if (!formData.type) {
      toast.error("Please select question type", {
        position: "top-right",
      });
      return;
    }
    if (!formData.category?.length) {
      toast.error("Please select at least one category", {
        position: "top-right",
      });
      return;
    }
    const empty = formData.answers?.filter((answer) => !answer.answer);

    if (empty?.length) {
      toast.error("Please fill all or remove empty answer", {
        position: "top-right",
      });
      return;
    }

    let result;
    setLoading(true);
    if (questionnaire?.id) {
      result = await repo.update(questionnaire.id, formData);
    } else {
      result = await repo.create(formData);
    }
    setLoading(false);
    if (result) {
      refetch && refetch();
      setFormData({} as QuestionnaireFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({} as QuestionnaireFormData);
    state.close();
  };

  useEffect(() => {
    if (questionnaire) {
      setFormData({
        question: questionnaire.question,
        type: questionnaire.type,
        category: questionnaire.category,
        patientSort: questionnaire.patientSort,
        therapistSort: questionnaire.therapistSort,
        required: questionnaire.required,
        answers: questionnaire.answers.map((answer) => ({
          id: answer.id,
          answer: answer.answer,
          info: answer.info,
          new: false,
        })),
        info: questionnaire.info,
      });
    } else {
      setFormData({
        ...formData,
        type: "checkbox",
      });
    }
  }, [questionnaire]);

  const addAnswer = () => {
    const answers = formData.answers || [];
    const id = uuid4();
    answers.push({ id, answer: "", new: true });
    setFormData({ ...formData, answers });
  };

  return (
    <FormDialog
      aria-label="questionnaire-form"
      title="Questionnaire Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel="Save"
      loading={loading}
      state={state}
      width="w-[680px]"
    >
      <div className="grid grid-cols-4 gap-4">
        <div className="col-span-3">
          <InputComponent
            label={"Question"}
            id="question"
            onChange={(value) => setFormData({ ...formData, question: value })}
            value={formData.question || ""}
            isRequired
          />
        </div>
        <NormalSelectComponent
          placeholder="Select Question Type"
          label="Question Type"
          required
          id="question-type"
          value={formData.type}
          onChange={(value) => {
            setFormData({ ...formData, type: value });
          }}
          options={questionnaireTypeList}
        />
      </div>

      <TextAreaComponent
        label={"Info"}
        id="info"
        onChange={(value) => setFormData({ ...formData, info: value })}
        value={formData.info || ""}
      />

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="category" className="text-sm block mb-2">
            Category <span className="text-red-500 font-bold ml-1">*</span>
          </label>
          <div className="flex flex-row space-x-4">
            <CheckBoxComponent
              checked={formData.category?.includes("self") || false}
              label={"MySelf"}
              onChange={(isSelected) => {
                let category = formData.category || [];
                if (isSelected) {
                  category.push("self");
                } else {
                  category = category.filter((c) => c !== "self");
                }
                setFormData({
                  ...formData,
                  category,
                });
              }}
            />
            <CheckBoxComponent
              checked={formData.category?.includes("couple") || false}
              label={"MySelf & Partner"}
              onChange={(isSelected) => {
                let category = formData.category || [];
                if (isSelected) {
                  category.push("couple");
                } else {
                  category = category.filter((c) => c !== "couple");
                }
                setFormData({
                  ...formData,
                  category,
                });
              }}
            />
            <CheckBoxComponent
              checked={formData.category?.includes("therapist") || false}
              label={"Therapist"}
              onChange={(isSelected) => {
                let category = formData.category || [];
                if (isSelected) {
                  category.push("therapist");
                } else {
                  category = category.filter((c) => c !== "therapist");
                }
                setFormData({
                  ...formData,
                  category,
                });
              }}
            />
          </div>
        </div>
        <div className="flex flex-col justify-end">
          <CheckBoxComponent
            checked={formData.required || false}
            label={"Is question required?"}
            onChange={(isSelected) =>
              setFormData({
                ...formData,
                required: isSelected,
              })
            }
          />
        </div>
      </div>
      <div className="border-2 border-neutral-700 mt-4 p-2 rounded-md shadow-lg">
        <div className="font-semibold text-sm flex flex-row items-center justify-between">
          <span>Answers</span>
          <span
           data-testid="add-answer-button"
            onClick={addAnswer}
            className="bg-green-100 px-2 rounded text-green-700 border border-green-700 cursor-pointer active:scale-95"
          >
            <i className="fa fa-add fa-sm"></i>
            <span className="font-normal ml-2">Add</span>
          </span>
        </div>
        <div className="mt-2 text-sm flex flex-col space-y-2">
          {(formData.answers?.length || 0) === 0 && (
            <small>No answers added</small>
          )}
          {formData.answers?.map((answer, i) => (
            <div
              key={answer.id}
              className="py-1 px-2 bg-gray-200 rounded-md flex flex-row justify-between"
            >
              <span className="w-full">
                <input
                  value={answer.answer || ""}
                  onChange={(e) => {
                    const answers = formData.answers || [];
                    answers[i].answer = e.target.value;
                    setFormData({ ...formData, answers });
                  }}
                  type="text"
                  placeholder={`answer ${i + 1}`}
                  required
                  className="w-full bg-white rounded-md focus:outline-none mb-1 py-1 px-2"
                />
                <textarea
                  value={answer.info || ""}
                  onChange={(e) => {
                    const answers = formData.answers || [];
                    answers[i].info = e.target.value;
                    setFormData({ ...formData, answers });
                  }}
                  placeholder="info"
                  className="w-full bg-white rounded-md focus:outline-none mb-1 py-1 px-2"
                />
              </span>
              <span
                data-testid="remove-answer-button"
                className="cursor-pointer ml-1 px-1"
                onClick={() => {
                  const answers = formData.answers || [];
                  const removed = answers.splice(i, 1);
                  if (!removed[0].new) {
                    setFormData({
                      ...formData,
                      answers,
                      removedAnswers: [
                        ...(formData.removedAnswers || []),
                        removed[0].id,
                      ],
                    });
                    return;
                  }
                  setFormData({ ...formData, answers });
                }}
              >
                <i className="fa fa-minus fa-sm" />
              </span>
            </div>
          ))}
        </div>
      </div>
    </FormDialog>
  );
};

export default QuestionnaireForm;
