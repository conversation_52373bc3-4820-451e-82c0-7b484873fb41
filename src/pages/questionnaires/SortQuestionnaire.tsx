import QuestionnaireRepository from "@/repositories/QuestionnaireRepository";
import { Questionnaire } from "@/types/questionnaire.interface";
import { getQuestionnaireType } from "@/utils/enum.util";
import { useQuery } from "@tanstack/react-query";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";

type SortQuestionnaireProps = {
  repo: QuestionnaireRepository;
  sort: string;
};

const SortQuestionnaire = ({ repo, sort }: SortQuestionnaireProps) => {
  const { error, data, isLoading, refetch } = useQuery({
    queryKey: [`${sort}-questionnaires`, sort],
    queryFn: async () => {
      const { result } = await repo.getAll({
        perPage: 100,
        page: 1,
        category: sort,
      });
      return result;
    },
    refetchOnWindowFocus: false,
    enabled: !!sort,
  });

  const onDragEnd = async (result: any) => {
    const { source, destination } = result;
    const items = Array.from(data);
    const [reorderedItem] = items.splice(source.index, 1);
    items.splice(destination.index, 0, reorderedItem);

    const sorted = {
      category: sort,
    } as any;

    items.forEach(async (item: any, index) => {
      sorted[item.id] = index;
    });

    const status = await repo.sortOrder(sorted);

    if (status) {
      refetch();
    }
  };

  if (isLoading) {
    return (
      <div className="flex-grow">
        <div className="mt-4 flex flex-row items-center justify-center">
          <i className="fas fa-spinner animate-spin fa-xl inline-block my-2" role="status"
  aria-label="loading"></i>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-grow">
        <div className="mt-4 flex flex-row items-center justify-center">
          <i className="fas fa-exclamation-triangle fa-xl inline-block my-2"></i>
          <span className="ml-2">Error fetching data</span>
        </div>
      </div>
    );
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div>
        <Droppable key="droppable" droppableId="droppable">
          {(provided) => (
            <div
              className="flex-grow"
              ref={provided.innerRef}
              {...provided.droppableProps}
            >
              {data?.map((questionnaire: Questionnaire, index: number) => (
                <Draggable
                  key={questionnaire.id}
                  draggableId={questionnaire.id.toString()}
                  index={index}
                >
                  {(providedInnerRef) => (
                    <div
                      ref={providedInnerRef.innerRef}
                      {...providedInnerRef.draggableProps}
                      {...providedInnerRef.dragHandleProps}
                      className="grid grid-cols-12 border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out"
                      key={index}
                    >
                      <>
                        <div className="flex items-center">
                          <div>
                            <div
                              className={`w-8 h-8 rounded-full font-semibold flex items-center justify-center ${
                                questionnaire.required
                                  ? "bg-red-100 text-red-700"
                                  : "bg-gray-100"
                              }`}
                            >
                              {index + 1}
                            </div>
                          </div>
                        </div>
                        <div className="col-span-5 flex items-center">
                          <strong>{questionnaire.question}</strong>
                        </div>
                        <div className="col-span-2 flex text-green-700 flex-row items-center justify-start">
                          <small className="font-bold uppercase">
                            {questionnaire.answers?.length} Answer(s)
                          </small>
                        </div>
                        <div className="col-span-2 text-sm flex items-center">
                          {getQuestionnaireType(questionnaire.type)}
                        </div>
                        <div className="col-span-2 flex flex-row items-center justify-end space-x-4 text-white"></div>
                      </>
                    </div>
                  )}
                </Draggable>
              ))}
            </div>
          )}
        </Droppable>
      </div>
    </DragDropContext>
  );
};

export default SortQuestionnaire;
