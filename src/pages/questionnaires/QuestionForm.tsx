import FormDialog from "@/components/dialogs/FormDialog";
import InputComponent from "@/components/forms/InputComponent";
import TextAreaComponent from "@/components/forms/TextAreaComponent";
import QuestionRepository from "@/repositories/QuestionnaireRepository";
import { Questionnaire } from "@/types/questionnaire.interface";
import { FormEvent, useEffect, useState } from "react";
import { OverlayTriggerState } from "react-stately";

type QuestionFormProps = {
  state: OverlayTriggerState;
  questionnaire?: Questionnaire;
  refetch?: () => void;
};

type QuestionFormData = {
  question?: string;
  info?: string;
};

const QuestionForm = ({ state, questionnaire, refetch }: QuestionFormProps) => {
  const repository = new QuestionRepository();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<QuestionFormData>(
    {} as QuestionFormData
  );

  const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    let result;
    const questionsList = await repository.getAll();

    if (questionsList) {
      const trimmedFormQuestion = formData.question?.trim().toLowerCase();
      const questionExists = questionsList.data.some(
        (q: QuestionFormData) => q.question?.trim().toLowerCase() === trimmedFormQuestion
      );

      if (questionExists) {
        repository.errorToast("Matching question found! Enter different question.");
        return;
      }
    }

    setLoading(true);
    if (questionnaire?.id) {
      result = await repository.update(questionnaire.id, formData);
    } else {
      result = await repository.create(formData);
    }
    setLoading(false);
    if (result) {
      refetch && refetch();
      setFormData({} as QuestionFormData);
      state.close();
    }
  };

  const onCancel = () => {
    setFormData({} as QuestionFormData);
    state.close();
  };

  useEffect(() => {
    if (questionnaire) {
      setFormData({
        question: questionnaire.question,
        info: questionnaire.info,
      });
    } else {
      setFormData({} as QuestionFormData);
    }
  }, [questionnaire]);

  return (
    <FormDialog
      title="Question Form"
      onCancel={onCancel}
      onSubmit={onSubmit}
      confirmLabel="Save"
      loading={loading}
      width="w-[420px]"
      state={state}
    >
      <div className="flex flex-col space-y-4">
        <InputComponent
          label={"Question"}
          id="questionnaire" data-testid="question-input"
          onChange={(value) => setFormData({ ...formData, question: value })}
          value={formData.question || ""}
          isRequired
        />
        <TextAreaComponent
          label={"Info"}
          id="info"
          onChange={(value) => setFormData({ ...formData, info: value })}
          value={formData.info || ""}
        />
      </div>
    </FormDialog>
  );
};

export default QuestionForm;
