import dotenv from 'dotenv'

const { initializeCryptoUtils } = require('./cryptoUtil');
// comment for now to test on dev or demo env
// if (process.env.NODE_ENV !== 'development') {
	require('module-alias/register')
// }

try {
	dotenv.config()
} catch (e) {
	console.error(e)
}

import SetupApplication from '@/src/app'
const port = process.env.PORT || 3000

const app = SetupApplication()

app.listen(port, async () => {
	await initializeCryptoUtils();
	console.log(`Server is running on port ${port}`)
	console.log(`TWILIO: ${process.env.TWILIO_ACCOUNT_SID} ${process.env.TWILIO_AUTH_TOKEN} ${process.env.TWILIO_PHONE_NUMBER}`)
})
