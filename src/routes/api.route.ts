import { Application } from 'express'

import AuthRoute from '@/src/application/controllers/api/auth.controller'
import UserSettingRoute from '@/src/application/controllers/api/settings'
import TherapistRoute from '@/src/application/controllers/api/therapist.controller'
import TempRoute from '@/src/application/controllers/api/temp.controller'
import OfficeHoursRoute from '@/src/application/controllers/api/office-hours'
import PatientRoute from '@/src/application/controllers/api/patients'
import SubscriptionPlanRoute from '@/src/application/controllers/api/subscription-plan'
import TherapistSubscriptionRoute from '@/src/application/controllers/api/therapist-subscription'
import CalendarRoute from '@/src/application/controllers/web/calendar.controller'
import AppointmentRoute from '@/src/application/controllers/api/appointment'
import UserDeviceRoute from '@/src/application/controllers/api/user-devices'
import MinorPatientRoute from '../application/controllers/api/patients/minor.post'
import CalendarPatientRoute from '../application/controllers/api/patients/calendar.get'
import CalendarPatientUpdateRoute from '../application/controllers/api/patients/calendar.put'
import GenerateOtp from '../application/controllers/api/otp/otp.post'
import TherapistWaitlistRoute from '../application/controllers/api/therapist-waitlist'
import PatientPaymentInfoRoute from '../application/controllers/api/patient-payment-info'

const routes = (app: Application, version: string) => {
	app.use(`/api/${version}/auth`, AuthRoute)
	app.use(`/api/${version}/therapists`, TherapistRoute)
	app.use(`/api/${version}/temps`, TempRoute)
	app.use(`/api/${version}`, UserSettingRoute)
	app.use(`/api/${version}`, OfficeHoursRoute)
	app.use(`/api/${version}`, PatientRoute)
	app.use(`/api/${version}`, SubscriptionPlanRoute)
	app.use(`/api/${version}`, TherapistSubscriptionRoute)
	app.use(`/api/${version}/calendars`, CalendarRoute)
	app.use(`/api/${version}`, AppointmentRoute)
	app.use(`/api/${version}`, UserDeviceRoute)
	app.use(`/api/${version}`, MinorPatientRoute)
	app.use(`/api/${version}`, CalendarPatientRoute)
	app.use(`/api/${version}`, CalendarPatientUpdateRoute)
	app.use(`/api/${version}`, TherapistWaitlistRoute)
	app.use(`/api/${version}`, GenerateOtp)
	app.use(`/api/${version}`, PatientPaymentInfoRoute)
}

export default routes
