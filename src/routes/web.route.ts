import { Application } from 'express'

import DefaultRoute from '@/src/application/controllers/default.controller'
import AuthRoute from '@/src/application/controllers/web/auth.controller'
import UserRoute from '@/src/application/controllers/web/user.controller'
import PatientRoute from '@/src/application/controllers/web/patient.controller'
import TherapistRoute from '@/src/application/controllers/web/therapist.controller'
import QuestionnaireRoute from '@/src/application/controllers/web/questionnaire.controller'
import AnswerRoute from '@/src/application/controllers/web/answer.controller'
import SpecialityRoute from '@/src/application/controllers/web/speciality.controller'
import EventTagRoute from '@/src/application/controllers/web/event-tag.controller'
import EventRoute from '@/src/application/controllers/web/event.controller'
import CalendarRoute from '@/src/application/controllers/web/calendar.controller'
import PageRoute from '@/src/application/controllers/web/page.controller'
import TherapistPagesRoute from '@/src/application/controllers/web/therapist-pages.controller'
import MatchingAlgoRoute from '@/src/application/controllers/web/matching-algo.controller'
import TherapistBlockedListRoute from '@/src/application/controllers/web/therapist-blocked-list.controller'
import AppointmentRoute from '@/src/application/controllers/web/appointment.controller'
import ContactUsRoute from '@/src/application/controllers/web/contact-us.controller'
import TempRoute from '@/src/application/controllers/api/temp.controller'
import SubscriptionPlanRoute from '@/src/application/controllers/api/subscription-plan'
import TherapistSubscriptionRoute from '@/src/application/controllers/api/therapist-subscription'
import StatesRoute from '@/src/application/controllers/web/states.controller'
import UserRegistrationInfoRoute from '@/src/application/controllers/api/user-registration-info'
import TherapistPaymentRoute from '@/src/application/controllers/api/therapist-payment'

const routes = (app: Application) => {
	app.use('/', DefaultRoute)
	app.use('/auth', AuthRoute)
	app.use('/users', UserRoute)
	app.use('/patients', PatientRoute)
	app.use('/therapists', TherapistRoute)
	app.use('/questionnaires', QuestionnaireRoute)
	app.use('/answers', AnswerRoute)
	app.use('/specialities', SpecialityRoute)
	app.use('/event-tags', EventTagRoute)
	app.use('/events', EventRoute)
	app.use('/calendars', CalendarRoute)
	app.use('/pages', PageRoute)
	app.use('/therapist-pages', TherapistPagesRoute)
	app.use('/matching-algo', MatchingAlgoRoute)
	app.use('/therapist-blocked-list', TherapistBlockedListRoute)
	app.use('/appointment', AppointmentRoute)
	app.use('/contact', ContactUsRoute)
	app.use('/temps', TempRoute)
	app.use('/', SubscriptionPlanRoute)
	app.use('/', TherapistSubscriptionRoute)
	app.use('/', StatesRoute)
	app.use('/', UserRegistrationInfoRoute)
	app.use('/', TherapistPaymentRoute)
}

export default routes
