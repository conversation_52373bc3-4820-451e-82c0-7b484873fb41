import { useAppSelector } from "./store/hooks";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Sidebar from "./components/layouts/Sidebar";
import { Toaster } from "react-hot-toast";
import { AuthRoutes, AppRoutes, TherapyAppRoutes } from "./components/routes";
import TherapySidebar from "./components/layouts/TherapySidebar";
import SessionTimeout from "./sessionTimeout";

const queryClient = new QueryClient();
const Application = () => {
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);
  if (!isAuthenticated) {
    return <>
      <Toaster
        gutter={0}
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            padding: "0",
            background: "white",
            color: "#fff",
          },
        }}
      />      
        <AuthRoutes />    
    </>;
  }  
  return (
    <div className="flex flex-row h-full w-full transition-all duration-300">
      <QueryClientProvider client={queryClient}>
        {user?.role === "therapist" ? (
          <>
            <TherapySidebar/>
            <main className="flex-grow h-full overflow-x-auto">
              <TherapyAppRoutes/>
            </main>
          </>
        ) : (
          <>
            <Sidebar/>
            <main className="flex-grow h-full overflow-x-auto"  data-testid="app-routes">
              <AppRoutes/>
            </main>
          </>
        )}
        <SessionTimeout/>
      </QueryClientProvider>
      <Toaster
        gutter={0}
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            padding: "0",
            background: "white",
            color: "#fff",
          },
        }}
      />
    </div>
  );
};

export default Application;
