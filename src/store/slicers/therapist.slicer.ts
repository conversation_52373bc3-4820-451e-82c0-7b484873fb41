import { request } from "@/utils/request.utils";
import { AppDispatch } from '..';
import { setUser, updateRegistrationState } from './auth.slicer';
import { createSlice } from '@reduxjs/toolkit';

interface TherapistState {
  fetchingInfo: boolean;
}

const initialState: TherapistState = {
  fetchingInfo: false,
};

const TherapistSlice = createSlice({
  name: "therapist",
  initialState,
  reducers: {
    updateTherapistState: (state, { payload }) => {
			const { key, value }: {key: keyof TherapistState, value: any} = payload;
			(state as Record<keyof TherapistState, any>)[key] = value;
		}
  },
});

export const { updateTherapistState } = TherapistSlice.actions;
export default TherapistSlice.reducer;

export const registerTherapistAction = <T>(payload: T | any, page: string, token?: string) =>
  async (dispatch: AppDispatch) => {
    try {
      dispatch(updateRegistrationState({key: 'savingTherapistInfo', value: true}))
      return await request.post(`/auth/register?page=${page}&token=${token}`, payload);
    } catch (error: any) {
      return error.response;
    } finally {
      dispatch(updateRegistrationState({key: 'savingTherapistInfo', value: false}))
    }
  };

export const contactUsAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/contact', { ...payload });
  } catch (error: any) {
    return error.response;
  }
};

export const changePasswordAction = <T>(payload: T | any) => async (dispatch: AppDispatch) => {
  try {
    const response = await request.post('/auth/change-password', { ...payload });
    if (response && response.status === 201) dispatch(setUser(response.data.user));
    return response;
  } catch (error: any) {
    return error.response;
  }
};

export const sendVerificationEmailAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/auth/email-verification', { ...payload });
  } catch (error: any) {
    return error.response;
  }
}

export const verifyEmailAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/auth/verify-email', { ...payload });
  } catch (error: any) {
    return error.response;
  }
}

export const sendTokenAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/auth/send-token', { ...payload });
  } catch (error: any) {
    return error.response;
  }
}

export const resetEmailAction = <T>(payload: T | any) => async (dispatch: AppDispatch) => {
  try {
    const response = await request.post('/auth/reset-email', { ...payload });
    if (response && response.status === 200) dispatch(setUser(response.data.user));
    return response;
  } catch (error: any) {
    return error.response;
  }
};

export const sendSmsAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/auth/token-sms', { ...payload });
  } catch (error: any) {
    return error.response;
  }
};

export const resetPhoneAction = <T>(payload: T | any) => async (dispatch: AppDispatch) => {
  try {
    const response = await request.post('/auth/reset-phone', { ...payload });
    if (response && response.status === 200) dispatch(setUser(response.data.user));
    return response;
  } catch (error: any) {
    return error.response;
  }
};

export const enableDisableMfaAction = (enable: boolean) => async (dispatch: AppDispatch) => {
  try {
    const response = await request.patch('/therapists/mfa', { enable });
    if (response && response.status === 200) dispatch(setUser(response.data.data.user));
    return response;
  } catch (error: any) {
    return error.response;
  }
};

export const fetchRegInfoAction = ({ pageName, pages }: {
  pageName?: string, pages?: string[],
}) => async () => {
  try {
    let response;
    if (pages && pages.length > 0) {
      // If pages array is provided, use POST request with pages in the body
      response = await request.post(`/therapists/reg-info`, { pages });
    } else if (pageName) {
      // If pageName is provided, use GET request with pageName as a query parameter
      response = await request.get(`/therapists/reg-info?page=${pageName}`);
    } else {
      // If neither pages nor pageName is provided
      return
    }

    return response;
  } catch (error: any) {
    return error.response;
  }
};

export const getTherapistSubscriptionAction = () => async () => {
  try {
    return await request.get(`/therapist-subscription`);
  } catch (error: any) {
    return error.response;
  }
};

export const getTherapistSubscriptionActionById = (therapistId:number) => async () => {
  try {
    return await request.get(`/therapist-subscription/${therapistId}`, {
      suppressError: true,
    });    
  } catch (error: any) {
    return error.response;
  }
};

export const createTherapistSubscriptionAction = (sessionId: string, userToken: string) => async () => {
  try {
    return await request.post('/therapist-subscription', { sessionId, userToken });
  } catch (error: any) {
    return error.response;
  }
};

export const updateTherapistSubscriptionAction = <T>(payload: T | any) => async () => {
  try {
    return await request.put(`/therapist-subscription`, { ...payload });
  } catch (error: any) {
    return error.response;
  }
};

export const subscribeUsingStripeAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/stripe/subscribe', { ...payload });
  } catch (error: any) {
    return error.response;
  }
};

export const cancelOrRenewSubscriptionAction = (stripeInfoId: number) => async () => {
  try {
    return await request.post('/stripe/billing', { stripeInfoId });
  } catch (error: any) {
    return error.response;
  }
};

export const syncGoogleCalendarAction = (payload: any) => async () => {
  try {
    return await request.post('/calendars/google', payload)
  } catch (error: any) {
    return error.response;
  }
}

export const syncOutlookCalendarAction = (payload: any) => async () => {
  try {
    return await request.post('/calendars/outlook', payload)
  } catch (error: any) {
    return error.response;
  }
}

export const removeGoogleCalendarAction = (userToken: string) => async () => {
  try {
    return await request.delete('/calendars/google', { data: { userToken } })
  } catch (error: any) {
    return error.response;
  }
}

export const removeOutlookCalendarAction = (userToken: string) => async () => {
  try {
    return await request.delete('/calendars/outlook', { data: { userToken } })
  } catch (error: any) {
    return error.response;
  }
}

export const fetchAllRegInfoAction = (userId: number, code: string) => async () => {
  try {
    return await request.get(`/registration-info/${userId}?code=${code}`);
  } catch (error: any) {
    return error.response;
  }
}

export const fetchLoginInfo = (userId: number, code: string) => async () => {
  try {
    return await request.get(`/login-info/${userId}?code=${code}`);
  } catch (error: any) {
    return error.response;
  }
}

export const confirmPaymentAction = (appointmentId: number) => async () => {
  try {
    return await request.post('/api/v1/confirm-payment', {
      appointmentId,
    });
  } catch (error: any) {
    return error.response;
  }
}