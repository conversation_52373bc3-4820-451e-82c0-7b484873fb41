import { request } from "@/utils/request.utils";

export const fetchSubscriptionPlansAction = (params: any) => async () => {
  try {
    const response = await request.get('/subscription-plan', { params });
    return response.data;
  } catch (error: any) {
    return error.response;
  }
};

export const createSubscriptionPlanAction = <T>(payload: T | any) => async () => {
  try {
    return await request.post('/subscription-plan', { ...payload });
  } catch (error: any) {
    return error.response;
  }
};

export const updateSubscriptionPlanAction = <T>(payload: T | any, id: number) => async () => {
  try {
    return await request.put(`/subscription-plan/${id}`, { ...payload });
  } catch (error: any) {
    return error.response;
  }
};

export const activateSubscriptionPlanAction = (id: number) => async () => {
  try {
    return await request.put(`/subscription-plan/activate/${id}`);
  } catch (error: any) {
    return error.response;
  }
};

export const deleteSubscriptionPlanAction = (id: number) => async () => {
  try {
    return await request.delete(`/subscription-plan/${id}`);
  } catch (error: any) {
    return error.response;
  }
};
