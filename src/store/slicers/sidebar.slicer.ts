import { createSlice } from "@reduxjs/toolkit";

interface SidebarState {
  on: boolean;
  registerTherapist: {
    openSubMenu: { [key: string]: boolean }
    invalidMenus: string[]
  };
}

const initialState: SidebarState = {
  on: true,
  registerTherapist: {
    openSubMenu: {},
    invalidMenus: [],
  }
};

const SidebarSlice = createSlice({
  name: "sidebar",
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.on = !state.on;
    },
    hideSidebar: (state) => {
      state.on = false;
    },
    updateSidebarState: (state, { payload }) => {
      const { key, value }: {key: keyof SidebarState, value: any} = payload;
      (state as Record<keyof SidebarState, any>)[key] = value;
    },
    resetSidebarState: (state) => {
      state.registerTherapist = {
        ...initialState.registerTherapist,
      };
    }
  },
});

export const { toggleSidebar, hideSidebar, updateSidebarState, resetSidebarState } = SidebarSlice.actions;
export default SidebarSlice.reducer;
