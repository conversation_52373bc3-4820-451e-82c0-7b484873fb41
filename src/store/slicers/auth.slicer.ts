import { createSlice } from "@reduxjs/toolkit";
import { User } from "@/types/user.interface";
import { Page } from "@/types/page.interface";
import { request } from "@/utils/request.utils";
import { AppDispatch } from "..";

export type FormContentType = {
  [pageId: string]: any;
};

interface UserState {
  token?: string;
  refreshToken?: string;
  user?: User;
  isAuthenticated: boolean;
  registration: {
    fetchingPage: boolean;
    formContent: FormContentType;
    therapistMatchKey: string | null;
    matchedTherapists: any[];
    page?: Partial<Page>;
    patientAnswers: FormContentType;
    userRegistrationToken: string | null;
    savingTherapistInfo: boolean;
    currentPage?: string | null;
    editFlag: boolean;
    navToInvalidPage?: boolean;
    showRegCompleteDialog?: boolean;
    shouldShowRegCompleteDialog?: boolean;
  };
}

const initialState: UserState = {
  token: undefined,
  user: {} as User,
  isAuthenticated: false,
  registration: {
    fetchingPage: false,
    formContent: {} as FormContentType,
    therapistMatchKey: null,
    matchedTherapists: [],
    page: undefined,
    patientAnswers: {} as FormContentType,
    userRegistrationToken: null,
    savingTherapistInfo: false,
    currentPage: null,
    editFlag: false,
    navToInvalidPage: false,
    showRegCompleteDialog: false,
    shouldShowRegCompleteDialog: true,
  },
};

const UserSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
    signOut: (state) => {
      return {
        ...state,
        user: undefined,
        isAuthenticated: false,
        refreshToken: undefined,
        token: undefined
      }
    },
    
    signIn: (state, action) => {
      const { token, refreshToken, user } = action.payload;
      state.token = token;
      state.refreshToken = refreshToken;
      state.user = user;
      state.isAuthenticated = true;
    },
    setTokens: (state, action) => {
      const { accessToken, refreshToken } = action.payload;
      state.token = accessToken;
      state.refreshToken = refreshToken;
    },
    resetRegistration: (state) => {
      state.registration = {
        ...initialState.registration,
      };
    },
    updateRegistrationState: (state, action) => {
      return {
        ...state,
        registration: {
          ...state.registration,
          [action.payload.key]: action.payload.value
        }
      }
    },
    updateRegistrationForm: (state, action) => {
      const clonedRegistration = JSON.parse(JSON.stringify(state.registration || {}));

      const newState = {
        ...state,
        registration: {
          ...clonedRegistration,
          formContent: {
            ...clonedRegistration.formContent,
            [action.payload.pageId]: action.payload.values
          }
        }
      };

      return {
        ...newState,
      };
    },
    resetRegistrationForm: (state, action) => {
      const clonedRegistration = JSON.parse(JSON.stringify(state.registration || {}));
      const newState = {
        ...state,
        registration: {
          ...clonedRegistration,
          formContent: {
            ...clonedRegistration.formContent,
            [action.payload.pageId]: {},
          },
        },
      };
      return { ...newState };
    },
    setEditFlag: (state, action) => {
      state.registration.editFlag = action.payload;
    },
    setFormContent: (state, action) => {
      state.registration.formContent = action.payload;
    },
    setNewRegistrationState: (state, action) => {
      state.registration = {
        ...initialState.registration,
        formContent: action.payload.newFormContent,
        userRegistrationToken: action.payload.useToken,
      };
    },
    
    setPatientRegistrationPage: (state, action) => {
      const queryParams = new URLSearchParams(window.location.search);
      queryParams.set("currentPage", action.payload.id);
      history.replaceState(null, '', "?"+queryParams.toString());

      return {
        ...state,
        registration : {
          page: action.payload,
          fetchingPage: state.registration.fetchingPage,
          therapistMatchKey: state.registration.therapistMatchKey,
          matchedTherapists: state.registration.matchedTherapists,
          formContent: state.registration.formContent,
          patientAnswers: state.registration.patientAnswers,
          userRegistrationToken: state.registration.userRegistrationToken,
          savingTherapistInfo: state.registration.savingTherapistInfo,
          editFlag: state.registration.editFlag,
        }
      }
    },
    updatePatientAnswers: (state, action) => {
      const clonedPatientAnswers = JSON.parse(JSON.stringify(state.registration.patientAnswers || {}));

      const newState = {
        ...state,
        registration: {
          ...state.registration,
          patientAnswers: {
            ...clonedPatientAnswers,
            [action.payload.pageId]: action.payload.values
          }
        }
      };

      return {
        ...newState,
      };
    },
  },
});

export const getPatientIntialPage = <T>(payload?: T | any) => async (dispatch: AppDispatch) => {
  try {
    dispatch(updateRegistrationState({key: 'fetchingPage', value: true}))
    const category = payload?.category;
    const {data} =  await request.get(`/pages/register?category=${category}&initial=true`);
    dispatch(setPatientRegistrationPage({...data[0] }))
    dispatch(updateRegistrationState({key: 'therapistMatchKey', value: data[0].therapistMatchKey}))
  } catch (error) {
    //  
  } finally {
    dispatch(updateRegistrationState({key: 'fetchingPage', value: false}))
  }
};

export const getPatientRegistrationPage = <T>(payload?: T | any) => async (dispatch: AppDispatch) => {
  try {
    dispatch(updateRegistrationState({key: 'fetchingPage', value: true}))
    const { pageId, category, prevQuestionId, prevSelectedAnswer, therapistMatchKey } = payload;
    const { data } = await request.get(`/pages/register/${pageId}?category=${category}&prevQuestionId=${prevQuestionId || ''}&prevSelectedAnswer=${prevSelectedAnswer || ''}&therapistMatchKey=${therapistMatchKey || null}`);
    dispatch(setPatientRegistrationPage({ ...data }));
    return data;
  } catch (error: any) {
    return error.response;
  } finally {
    dispatch(updateRegistrationState({ key: 'fetchingPage', value: false } ))
  }
};


export const getPatientRegPageAndMatch = <T>(payload?: T | any) => async (dispatch: AppDispatch) => {
  try {
    dispatch(updateRegistrationState({key: 'fetchingPage', value: true}))
    
    const {
      pageId, category, prevQuestionId, prevSelectedAnswer,
      therapistMatchKey, matcherInvolvement, answers, currentPageId
    } = payload;
    
    let requestBody;

    if (matcherInvolvement) {
      let formattedAnswers;
      if (Array.isArray(answers)) {
        formattedAnswers = answers.map(a => ({ id: a.id, slug: a.value }));
      } else if (answers.gender) {
        formattedAnswers = { gender: answers.gender };
      } else {
        formattedAnswers = { id: answers.id, slug: answers.value };
      }

      requestBody = {
        pageId: currentPageId,
        matcherInvolvement,
        answers: formattedAnswers,
      };
    }

    const { data } = await request.post(`/pages/register/${pageId}?category=${category}&prevQuestionId=${prevQuestionId || ''}&prevSelectedAnswer=${prevSelectedAnswer || ''}&therapistMatchKey=${therapistMatchKey || null}`,
      requestBody);

    dispatch(setPatientRegistrationPage({ ...data }));
    if (data.matchedTherapists) dispatch(updateRegistrationState({key: 'matchedTherapists', value: data.matchedTherapists}))
    return data;
  } catch (error: any) {
    return error.response;
  } finally {
    dispatch(updateRegistrationState({ key: 'fetchingPage', value: false }))
  }
};

/**
 * Email verification during registration
 * @param payload 
 * @returns response
 */
export const verifyEmail = <T>(payload?: T | any) => async () => {
  try {
    return await request.post('/auth/pre-register-verification', { email: payload.email, phone: '' });
  } catch (error: any) {
    return error.response;
  }
};

export const registerPatient = <T>(payload?: T | any) => async () => {
  const { ansObject, category } = payload;
  try {
    return await request.post(`/auth/register/patient?category=${category}`, { ...ansObject });
  } catch (error: any) {
    return error.response;
  }
};

export const { 
  setUser, 
  signIn, 
  signOut, 
  setTokens,
  resetRegistration,
  setEditFlag,
  setFormContent,
  updateRegistrationForm, 
  setPatientRegistrationPage, 
  updatePatientAnswers, 
  updateRegistrationState,
  resetRegistrationForm,
  setNewRegistrationState
} = UserSlice.actions;
export default UserSlice.reducer;

