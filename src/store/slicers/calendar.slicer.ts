import { TherapyDate, TherapyEvent } from "@/plugins/calendar/types";
import { createSlice } from "@reduxjs/toolkit";

interface CalendarState {
  selectedDate: string;
  events: TherapyEvent[];
  days: TherapyDate[];
}

const initialState: CalendarState = {
  selectedDate: new Date().toISOString(),
  events: [],
  days: [],
};

const CalendarSlice = createSlice({
  name: "calendar",
  initialState,
  reducers: {
    setSelectedDate: (state, action) => {
      state.selectedDate = action.payload;
    },
    setEvents: (state, action) => {
      state.events = action.payload;
    },
    setDays: (state, action) => {
      state.days = [...action.payload];
    },
  },
});

export const { setSelectedDate, setEvents, setDays } = CalendarSlice.actions;
export default CalendarSlice.reducer;
