<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml" style="width: 100%; background-color: #fcfcfc; height: 100%">

<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <style>
    td,th,div,p,a,h1,h2,h3,h4,h5,h6 {font-family: "Segoe UI", sans-serif; mso-line-height-rule: exactly;}
  </style>
  <![endif]-->
  <style>
    html {
      height: 100%;
      width: 100%;
      background-color: #fcfcfc;
    }

    @media (max-width: 600px) {
      .sm-px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important;
      }
    }
  </style>
</head>

<body
  style="margin: 0; height: 100%; width: 100%; padding: 0; -webkit-font-smoothing: antialiased; word-break: break-word">
  <div role="article" aria-roledescription="email" aria-label lang="en" style="height: 100%;">
    <div class="sm-px-4"
      style="height: 100%; background-color: #f8fafc; font-family: ui-sans-serif, system-ui, -apple-system, 'Segoe UI', sans-serif">
      <table align="center" cellpadding="0" cellspacing="0" role="presentation">
        <tr>
          <td style="text-align: center; padding: 24px 0;">
						<img src="https://dev.app.nexttherapist.com/logo/next_therapist.png" alt="Next Therapist Logo" style="max-width: 120px; height: 60px;">
              <defs>
                <style>
                  .cls-1 {
                    fill: #547191;
                  }
            
                  .cls-1, .cls-2 {
                    stroke-width: 0px;
                  }
            
                  .cls-2 {
                    fill: #d5942f;
                  }
                </style>
              </defs>
              <g>
                <path class="cls-1" d="M163.69,258.75h15.87v6.28l.66.22c3.75-4.41,10.14-7.6,18.41-7.6,10.91,0,19.95,7.82,19.95,23.14v33.06h-15.87v-32.18c0-7.71-4.85-11.02-10.69-11.02-4.96,0-9.48,1.54-12.46,4.85v38.35h-15.87v-55.11Z"/>
                <path class="cls-1" d="M284.57,284.32v6.61h-39.13c1,4.37,3.46,10.98,11.52,10.98s10.06-4.49,10.06-4.49h16.27c-2.99,11.2-12.62,16.97-21.54,17.53-19.8,1.25-32.4-10.8-32.4-28.55,0-15.1,10.36-28.54,28.22-28.77,15.76,0,26.67,9.26,27,26.67ZM245.88,280.58h22.7c-.66-7.16-4.96-9.92-10.47-9.92-6.94,0-10.8,3.53-12.23,9.92Z"/>
                <path class="cls-1" d="M317.49,297.77l-12.45,16.09h-18.19l21.49-27.99-20.83-27.11h18.41l11.57,15.1,11.57-15.1h18.41l-20.83,27.11,21.49,27.99h-18.18l-12.46-16.09Z"/>
                <path class="cls-1" d="M386.44,314.41h-9.04c-11.79,0-20.5-7.38-20.5-20.5v-52.79h15.87v17.63h13.67v12.12h-13.67v22.15c0,5.29,2.97,7.49,7.27,7.49h6.4v13.89Z"/>
                <path class="cls-1" d="M398.91,258.75h8.93v-17.63h8.82v17.63h16.31v7.71h-16.31v29.21c0,7.72,4.08,11.02,9.92,11.02,3.31,0,6.17-.33,8.71-1.43l2.2,6.83c-3.31,1.65-7.05,2.31-10.91,2.31-10.69,0-18.74-6.72-18.74-18.74v-29.21h-8.93v-7.71Z"/>
                <path class="cls-1" d="M446.3,233.4h8.82v32.51l.66.22c4.08-4.96,11.13-8.49,20.17-8.49,10.91,0,19.95,7.82,19.95,23.14v33.06h-8.82v-33.06c0-10.8-5.95-15.43-13.33-15.43s-14.77,3.42-18.63,10.36v38.14h-8.82v-80.46Z"/>
                <path class="cls-1" d="M556.06,280.8v6.61h-41.99c.66,12.68,7.72,19.62,20.94,19.84,5.84,0,11.46-1.1,16.2-3.31l1.87,7.16c-5.62,2.54-11.79,3.86-18.07,3.86-17.19,0-29.76-10.8-29.76-28.55,0-15.1,10.69-28.54,26.45-28.77,14.22,0,22.71,8.27,24.36,23.14ZM514.74,279.7h32.4c-.44-9.04-6.94-14.33-15.32-14.33-9.04,0-15.1,6.39-17.08,14.33Z"/>
                <path class="cls-1" d="M567.63,258.75h8.82v6.17l.66.33c3.75-4.41,9.7-7.6,16.86-7.6h1.54v7.71h-2.65c-6.72,0-12.45,3.2-16.42,8.27v40.34h-8.82v-55.22Z"/>
                <path class="cls-1" d="M642.57,263.16l.55-.22v-4.19h8.82v55.11h-8.82v-6.06l-.66-.33c-3.75,4.41-10.03,7.5-17.96,7.5-14.77,0-26.56-12.67-26.56-28.66s12.23-28.65,27.67-28.65c7.82,0,13.11,2.31,16.97,5.51ZM643.12,297.99v-27.11c-3.97-3.42-9.81-5.51-16.42-5.51-10.91,0-19.95,9.37-19.95,20.94s9.04,20.94,19.95,20.94c6.72,0,13.01-2.54,16.42-9.26Z"/>
                <path class="cls-1" d="M675.63,310.11l-.55.33v28.77h-8.82v-80.46h8.82v6.06l.66.22c3.64-4.3,9.92-7.38,17.97-7.38,14.77,0,26.56,12.67,26.56,28.65s-12.23,28.66-27.67,28.66c-7.82,0-13.22-1.87-16.97-4.85ZM675.08,274.63v28.22c3.97,2.76,9.7,4.41,16.42,4.41,10.91,0,19.95-9.37,19.95-20.94s-9.04-20.94-19.95-20.94c-6.72,0-13.01,2.54-16.42,9.26Z"/>
                <path class="cls-1" d="M735.03,235.94c2.98,0,5.4,2.42,5.4,5.4s-2.42,5.29-5.4,5.29-5.29-2.42-5.29-5.29,2.42-5.4,5.29-5.4ZM730.73,258.75h8.82v55.11h-8.82v-55.11Z"/>
                <path class="cls-1" d="M753.54,303.5c5.29,2.09,11.02,3.75,16.42,3.75,6.06,0,11.79-2.65,11.79-8.71,0-4.3-2.86-6.94-11.9-9.26-10.36-2.65-17.85-7.38-17.85-15.87,0-10.47,9.26-15.76,19.51-15.76,6.28,0,12.23,1.76,17.74,4.19l-2.42,7.27c-4.96-1.98-10.25-3.75-15.32-3.75-5.62,0-10.69,2.76-10.69,8.05,0,3.75,2.98,6.39,11.02,8.27,11.57,2.65,18.74,7.83,18.74,16.86,0,11.24-9.92,16.42-20.61,16.42-6.5,0-13-1.43-18.85-3.64l2.42-7.82Z"/>
                <path class="cls-1" d="M797.73,258.75h8.93v-17.63h8.82v17.63h16.31v7.71h-16.31v29.21c0,7.72,4.08,11.02,9.92,11.02,3.31,0,6.17-.33,8.71-1.43l2.2,6.83c-3.31,1.65-7.05,2.31-10.91,2.31-10.69,0-18.74-6.72-18.74-18.74v-29.21h-8.93v-7.71Z"/>
              </g>
              <path class="cls-2" d="M584.81,99.45h-18.33v-18.84c0-3.9-3.16-7.07-7.07-7.07h-118.81c-3.91,0-7.07,3.17-7.07,7.07v18.84h-18.33c-3.91,0-7.07,3.17-7.07,7.07v37.29c0,10.7,4.07,23.46,23.46,23.46h1.65v13h13.71v-13h104.88v13h13.71v-13h2.88c19.39,0,23.46-12.76,23.46-23.46v-37.29c0-3.9-3.16-7.07-7.07-7.07ZM447.67,113.59h4.29v14.13h-4.29v-14.13ZM577.74,143.8c0,7.58-1.74,9.32-9.32,9.32h-136.84c-7.58,0-9.32-1.74-9.32-9.32v-30.22h11.26v21.2c0,3.9,3.16,7.07,7.07,7.07h18.44c3.91,0,7.07-3.17,7.07-7.07v-28.27c0-3.9-3.16-7.07-7.07-7.07h-11.36v-11.77h104.67v11.77h-11.36c-3.91,0-7.07,3.17-7.07,7.07v28.27c0,3.9,3.16,7.07,7.07,7.07h18.44c3.91,0,7.07-3.17,7.07-7.07v-21.2h11.26v30.22ZM552.33,113.59v14.13h-4.29v-14.13h4.29Z"/>
            </svg>
          </td>
        </tr>
        <tr>
          <td style="width: 552px; max-width: 100%">
            <table style="width: 100%;" cellpadding="0" cellspacing="0" role="presentation">
              <tr>
                <td
                  style="overflow: hidden; border-radius: 4px; background-color: #fff; font-size: 16px; color: #334155; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)">
                  <div style="background-color: #dab9da; padding: 48px; color: #fff">
                    NextTherapist (Therapist Portal) | {{subject}} Subject<br>
                  </div>
                  <div style="padding: 30px;">
                    <p><strong style="color: #431d42;">From:</strong> {{reply_email}}</p>
                    <p>{{email_content}}</p>
                  </div>
                  <div
                    style="display: flex; flex-direction: row; justify-content: space-between; background-color: #dab9da; padding: 8px 48px; font-size: 12px; color: #fff">
                    <span style="color: #fff !important; text-decoration: none;">************, <EMAIL></span>
                    <span>&copy; NextTherapist 2025</span>
                  </div>
                </td>
              </tr>
              <tr role="separator">
                <td style="line-height: 48px">&zwj;</td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>

</html>
