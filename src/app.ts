import express, { Application, NextFunction, Request, Response } from 'express'
import compression from 'compression'
import bodyParser from 'body-parser'
import { SetupCors } from '@/src/configs/cors.config'
import SetupRoutes from '@/src/routes'
import MorganMiddleware from '@/src/application/middlewares/morgan.middleware'
import sequelize from '@/src/configs/database.config'
// import redis from './configs/redis.config'
import logger from '@/src/configs/logger.config'
import { useJob } from './application/helpers/loader.helper'
import { response } from './application/helpers/response.helper'
import { hashData } from './cryptoUtil'

const unless = function (path: string, middleware: Function) {
	return function (req: Request, res: Response, next: NextFunction) {
		if (path === req.path) {
			return next()
		} else if (req.path === '/api/v1/stripe/webhook') {
			return express.raw({ type: 'application/json' })(req, res, next)
		} else {
			return middleware(req, res, next)
		}
	}
}

const SetupApplication = () => {
	const app: Application = express()

	app.use(unless('/status', bodyParser.urlencoded({ extended: true })))
	app.use(unless('/status', bodyParser.json({limit: '50mb'})))

	app.use(compression())
	app.use(express.static('public'))
	app.use(express.static('uploads'))
	app.use(response);
	// Setup Middlewares
	app.use(MorganMiddleware)

	app.get('/email-hash', (req: Request, res: Response) => {
		console.log('Hashed Email:', hashData(req.query.email));
		res.sendStatus(200);
	});	

	// Setup Configurations and Services
	SetupCors(app)
	SetupRoutes(app)
	useJob();

	sequelize
		.authenticate()
		.then(() => {
			console.log(
				'Database connection has been established successfully.'
			)
		})
		.catch((err) => {
			logger.error(err)
		});

	// redis
	// 	.connect()
	// 	.then(() => {
	// 		console.log('Redis connection has been established successfully.')
	// 	})
	// 	.catch((err) => {
	// 		logger.error(err)
	// 	});

	

	return app
}

export default SetupApplication
