import ReactDOM from "react-dom/client";
import "./index.css";
import "@/styles/app.scss";
import "react-datepicker/dist/react-datepicker.css";

import "@/styles/fa/css/all.min.css";
import { BrowserRouter } from "react-router-dom";
import "nprogress/nprogress.css";
import { Provider } from "react-redux";
import store, { persistor } from "@/store/index";
import { PersistGate } from "redux-persist/integration/react";
import Application from "./Application";
import { GoogleOAuthProvider } from "@react-oauth/google";
import "react-perfect-scrollbar/dist/css/styles.css";

const { VITE_GOOGLE_CLIENT_ID } = import.meta.env;

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  // <StrictMode>
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <GoogleOAuthProvider clientId={VITE_GOOGLE_CLIENT_ID}>
        <BrowserRouter>
          <Application />
        </BrowserRouter>
      </GoogleOAuthProvider>
    </PersistGate>
  </Provider>,
  // </StrictMode>
);
