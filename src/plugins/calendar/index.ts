import dayjs from "dayjs";
import WeekOfYear from "dayjs/plugin/weekOfYear";
import IsToday from "dayjs/plugin/isToday";
import Duration from "dayjs/plugin/duration";
import RelativeTime from "dayjs/plugin/relativeTime";
import { TherapyDate, TherapyEvent } from "./types";
import { Dispatch } from "@reduxjs/toolkit";
import { setDays, setEvents } from "@/store/slicers/calendar.slicer";

dayjs.extend(WeekOfYear);
dayjs.extend(IsToday);
dayjs.extend(Duration);
dayjs.extend(RelativeTime);

class Calendar {
  date: Date | undefined;
  events: TherapyEvent[] = [];

  constructor(date?: Date) {
    this.date = date ?? new Date();
  }

  private currentDate = (date?: Date) => {
    return date ?? this.date ?? new Date();
  };

  private generateDayObjects(baseDate: Date, totalDays: number, isCurrentMonth: boolean): TherapyDate[] {
    return Array(totalDays)
      .fill(0)
      .map((_, i) => {
        const d = dayjs(baseDate).add(i, "day").toDate();
        return {
          key: dayjs(d).format("DD-MM-YYYY"),
          date: d,
          isCurrentMonth: isCurrentMonth
            ? d.getMonth() === baseDate.getMonth()
            : false,
          isToday: dayjs(d).isToday(),
          dayOfMonth: dayjs(d).format("DD"),
          time: dayjs(d).format("HH:mm A"),
          isSelected: false,
          events: this.getEvents(d),
        } as TherapyDate;
      });
  }
  

  /** function that sets date */
  setDate(date: Date, dispatch?: Dispatch) {
    this.date = date;
    if (dispatch) {
      dispatch(setDays(this.getMonthDayList()));
    }
  }

  /** function that returns current date */
  getDate(): Date {
    return this.date ?? new Date();
  }

  humanize(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).fromNow();
  }

  readable(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).format("MMMM DD, YYYY");
  }

  /** function that returns current month */
  month(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).format("MM");
  }

  /** function that returns current month name */
  monthName(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).format("MMMM");
  }

  /** function that returns current year */
  year(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).format("YYYY");
  }

  /** function that returns current day */
  day(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).format("D");
  }

  /** function that returns current day name */
  dayName(date?: Date): string {
    const currentDate = this.currentDate(date);
    return dayjs(currentDate).format("dddd");
  }

  /** function that sets the list of events */
  setEvents(events: TherapyEvent[], dispatch?: Dispatch) {
    this.events = events.map((event) => {
      return {
        ...event,
        time: dayjs(event.date).format("HH:mm A"),
      };
    });
    if (dispatch) {
      dispatch(setEvents(this.events));
      dispatch(setDays(this.getMonthDayList()));
    }
  }

  /** function that returns events */
  getEvents(date?: Date): TherapyEvent[] {
    if (date) {
      return this.events.filter(
        (event) =>
          dayjs(event.date).format("DD-MM-YYYY") ===
          dayjs(date).format("DD-MM-YYYY")
      );
    }
    return this.events;
  }

  /** function that returns all day list of current month */
  getMonthDayList(date?: Date): TherapyDate[] {
    const currentDate = this.currentDate(date);
    const startWeek = dayjs(currentDate).startOf("month").week();
    const endWeek =
      dayjs(currentDate).endOf("month").week() === 1
        ? 53
        : dayjs(currentDate).endOf("month").week();
    let dates: any = [];
    for (let week = startWeek; week <= endWeek; week++) {
      dates = dates.concat(
        this.generateDayObjects(
          dayjs(currentDate).week(week).startOf("week").toDate(),
          7,
          true
        )
      );
    }

    // Check if the total number of dates is less than 42
    if (dates.length < 42) {
      const nextMonth = dayjs(currentDate).add(1, "month").toDate();
      const nextMonthDates = Array(42 - dates.length)
        .fill(0)
        .map((n, i) => {
          const d = dayjs(nextMonth)
            .startOf("month")
            .add(n + i, "day")
            .toDate();
          return {
            key: dayjs(d).format("DD-MM-YYYY"),
            date: d,
            isCurrentMonth: false, // Mark as not current month
            isToday: dayjs(d).isToday(),
            dayOfMonth: dayjs(d).format("DD"),
            time: dayjs(d).format("HH:mm A"),
            isSelected: false,
            events: this.getEvents(d),
          } as TherapyDate;
        });
      dates = [...dates, ...nextMonthDates];
    }
    return dates;
  }

  getWeekDayList(date?: Date): TherapyDate[] {
    const currentDate = this.currentDate(date);
    const startWeek = dayjs(currentDate).startOf("week").week();
    const endWeek =
      dayjs(currentDate).endOf("week").week() === 1
        ? 53
        : dayjs(currentDate).endOf("week").week();
    let dates: any = [];
    for (let week = startWeek; week <= endWeek; week++) {
      dates = dates.concat(
        this.generateDayObjects(
          dayjs(currentDate).week(week).startOf("week").toDate(),
          7,
          true
        )
      );
    }
    return dates;
  }

  /** function that updates current date to next month */
  next(dispatch?: Dispatch) {
    this.setDate(dayjs(this.currentDate()).add(1, "month").toDate());
    if (dispatch) {
      dispatch(setDays(this.getMonthDayList()));
    }
  }

  /** function that updates current date to previous month */
  previous(dispatch?: Dispatch) {
    this.setDate(dayjs(this.currentDate()).subtract(1, "month").toDate());
    if (dispatch) {
      dispatch(setDays(this.getMonthDayList()));
    }
  }

  /** function goto specific date */
  goto(date: Date, dispatch?: Dispatch) {
    this.setDate(date);
    if (dispatch) {
      dispatch(setDays(this.getMonthDayList()));
    }
  }
}

export default Calendar;
