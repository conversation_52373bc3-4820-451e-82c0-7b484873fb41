export type TherapyEvent = {
  id: string | number;
  date: Date | string;
  name: string;
  description?: string;
  color?: string;
  textColor?: string;
  isAllDay?: boolean;
  isRecurring?: boolean;
  recurrence?: {
    frequency: string;
    interval: number;
    endDate: Date | string;
  };
  isPrivate?: boolean;
  meta?: any;
  time?: string;
};

export type TherapyDate = {
  key: string;
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  day: string;
  dayOfMonth: string;
  time: string;
  isSelected: boolean;
  events: TherapyEvent[];
};
