import { expect } from 'chai';
import sinon from 'sinon';
import sgMail from '@sendgrid/mail';
import * as dotenv from 'dotenv';

dotenv.config();
delete require.cache[require.resolve('../configs/sendgrid.config')];

describe('NextTherapistEmail', () => {
  let sandbox: sinon.SinonSandbox;
  let sendStub: sinon.SinonStub;
  let originalEnv: NodeJS.ProcessEnv;
  let mail: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sendStub = sandbox.stub(sgMail, 'send').resolves();
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    sandbox.restore();
    process.env = { ...originalEnv };
    delete require.cache[require.resolve('../configs/sendgrid.config')];
  });

  it('should not throw error when NODE_ENV is test', () => {
    process.env.NODE_ENV = 'test';
    expect(() => {
      mail = require('../configs/sendgrid.config').mail;
    }).to.not.throw();
  });

  it('should throw if SENDGRID_API_KEY is not set', () => {
    process.env.NODE_ENV = 'production';
    delete process.env.SENDGRID_API_KEY;
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';

    expect(() => {
      require('../configs/sendgrid.config');
    }).to.throw('SENDGRID_API_KEY is not set in the environment variables.');
  });

  it('should throw if SENDGRID_FROM_EMAIL is not set', () => {
    process.env.NODE_ENV = 'production';
    process.env.SENDGRID_API_KEY = 'dummy-key';
    delete process.env.SENDGRID_FROM_EMAIL;

    expect(() => {
      require('../configs/sendgrid.config');
    }).to.throw('SENDGRID_FROM_EMAIL is not set in the environment variables.');
  });

  it('should call sgMail.send when forceSend is true', async () => {
    process.env.NODE_ENV = 'test';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    process.env.LOCAL_ENV = 'true';
    mail = require('../configs/sendgrid.config').mail;

    await mail.sendMail(
      {
        to: '<EMAIL>',
        subject: 'Hello',
        text: 'Plain text',
        html: '<p>Hello</p>',
      },
      true
    );

    expect(sendStub.calledOnce).to.be.true;
    expect(sendStub.args[0][0]).to.include({
      to: '<EMAIL>',
      subject: 'Hello',
      text: 'Plain text',
    });
  });

  it('should call sgMail.send when LOCAL_ENV is not true', async () => {
    process.env.NODE_ENV = 'test';
    process.env.LOCAL_ENV = 'false';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    mail = require('../configs/sendgrid.config').mail;

    await mail.sendMail({ to: '<EMAIL>' });

    expect(sendStub.calledOnce).to.be.true;
    expect(sendStub.firstCall.args[0].to).to.equal('<EMAIL>');
  });

  it('should not call sgMail.send when LOCAL_ENV is true and forceSend is false', async () => {
    process.env.NODE_ENV = 'test';
    process.env.LOCAL_ENV = 'true';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    mail = require('../configs/sendgrid.config').mail;

    await mail.sendMail({ to: '<EMAIL>' });

    expect(sendStub.called).to.be.false;
  });

  it('should use default values when email fields are missing', async () => {
    process.env.NODE_ENV = 'test';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    process.env.LOCAL_ENV = 'false';
    mail = require('../configs/sendgrid.config').mail;

    await mail.sendMail({}, true);

    const calledWith = sendStub.firstCall.args[0];
    expect(calledWith.to).to.equal('<EMAIL>');
    expect(calledWith.from).to.equal('<EMAIL>');
    expect(calledWith.subject).to.equal('Default Subject');
    expect(calledWith.text).to.equal('Default text content');
    expect(calledWith.html).to.equal('<p>Default HTML content</p>');
  });

  it('should throw and log when sgMail.send fails', async () => {
    const error = new Error('fail');
    sendStub.rejects(error);

    const logStub = sinon.stub(console, 'dir');

    process.env.NODE_ENV = 'test';
    process.env.SENDGRID_FROM_EMAIL = '<EMAIL>';
    process.env.LOCAL_ENV = 'false';
    mail = require('../configs/sendgrid.config').mail;

    try {
      await mail.sendMail({}, true);
      expect.fail('Expected error not thrown');
    } catch (err: any) {
      expect(err.message).to.equal('Failed to send email');
      expect(logStub.calledOnce).to.be.true;
    }

    logStub.restore();
  });
});
