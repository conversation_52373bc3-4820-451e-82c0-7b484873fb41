// import <PERSON><PERSON> from 'stripe'
// import { ForbiddenError } from '@/src/application/handlers/errors'

// class NextTherapistStripe {
// 	private static instance: NextTherapistStripe
// 	private readonly initStripe = new Stripe(process.env.STRIPE_KEY as string)

// 	public static getInstance(): NextTherapistStripe {
// 		if (!NextTherapistStripe.instance) {
// 			NextTherapistStripe.instance = new NextTherapistStripe()
// 		}
// 		return NextTherapistStripe.instance
// 	}

// 	public async createCustomer({ email, metadata = {} }: { email: string; metadata: any }) {
// 		return await this.initStripe.customers.create({
// 			email,
// 			metadata,
// 		})
// 	}

// 	public async deleteCustoemr(customerId: string) {
// 		return await this.initStripe.customers.del(customerId)
// 	}

// 	public async listCustomers(email: string) {
// 		return await this.initStripe.customers.list({
// 			email,
// 			limit: 1,
// 		})
// 	}

// 	public async listSubscriptions(customerId: string) {
// 		return await this.initStripe.subscriptions.list({
// 			customer: customerId,
// 			status: 'active',
// 			limit: 1,
// 		})
// 	}

// 	public async getSubscriptionById(subscriptionId: string) {
// 		return await this.initStripe.subscriptions.retrieve(subscriptionId)
// 	}

// 	public async createBillingSession(customerId: string, redirectPage: string) {
// 		const stripeSession = await this.initStripe.billingPortal.sessions.create({
// 			customer: customerId,
// 			return_url: `${process.env.FRONTEND_URL}${redirectPage}`,
// 		})
// 		return stripeSession.url
// 	}

// 	public async createCheckoutSession({ customerId, subscriptionPlan, redirectPage, metadata = {} }: { customerId: string; subscriptionPlan: string; redirectPage: string; metadata: any }) {
// 		let unit_amount: number = 0 // Initialize with a default value
// 		let recurring_interval: 'month' | 'year' = 'month' // Initialize with a default value

// 		if (subscriptionPlan === 'monthly') {
// 			unit_amount = 1000
// 			recurring_interval = 'month'
// 		} else if (subscriptionPlan === 'yearly') {
// 			unit_amount = 10000
// 			recurring_interval = 'year'
// 		} else {
// 			// Handle invalid subscription plan
// 			throw new ForbiddenError('Invalid subscription plan')
// 		}

// 		const session = await this.initStripe.checkout.sessions.create({
// 			// success_url: `${process.env.FRONTEND_URL}/auth/register/connect-payment-info?success=true`,
// 			success_url: `${process.env.BASE_URL}/stripe/success?session_id={CHECKOUT_SESSION_ID}&redirectPage=${redirectPage}`,
// 			cancel_url: `${process.env.FRONTEND_URL}${redirectPage}`,
// 			payment_method_types: ['card'],
// 			mode: 'subscription',
// 			billing_address_collection: 'auto',
// 			line_items: [
// 				{
// 					price_data: {
// 						currency: 'usd',
// 						product_data: {
// 							name: 'Next Therapist',
// 							description: `${subscriptionPlan === 'monthly' ? 'Monthly' : subscriptionPlan === 'yearly' ? 'Yearly' : ''} Next Therapist Subscription`,
// 						},
// 						unit_amount,
// 						recurring: {
// 							interval: recurring_interval,
// 						},
// 					},
// 					quantity: 1,
// 				},
// 			],
// 			metadata,
// 			customer: customerId,
// 		})
// 		// const priceIds: Record<string, string> = {
// 		// 	monthly: 'price_1QNxT0RqKf2XeUgcmdftLrcP',
// 		// 	yearly: 'price_1QNxYCRqKf2XeUgcBKMoQl9T',
// 		// }

// 		// const priceId = priceIds[subscriptionPlan]
// 		// if (!priceId) {
// 		// 	throw new ForbiddenError('Invalid subscription plan')
// 		// }

// 		// const session = await this.initStripe.checkout.sessions.create({
// 		// 	success_url: `${process.env.BASE_URL}/stripe/success?session_id={CHECKOUT_SESSION_ID}`,
// 		// 	cancel_url: `${process.env.FRONTEND_URL}/auth/register/connect-payment-info`,
// 		// 	payment_method_types: ['card'],
// 		// 	mode: 'subscription',
// 		// 	billing_address_collection: 'auto',
// 		// 	line_items: [
// 		// 		{
// 		// 			price: priceId,
// 		// 			quantity: 1,
// 		// 		},
// 		// 	],
// 		// 	metadata,
// 		// 	customer: customerId,
// 		// })
// 		console.log('Session created:', session)
// 		return session.id
// 	}

// 	public async getSessionById(sessionId: string, params?: any) {
// 		return await this.initStripe.checkout.sessions.retrieve(sessionId, params)
// 	}

// 	public async getSessionByCustomerId(customerId: string) {
// 		return await this.initStripe.checkout.sessions.list({
// 			customer: customerId,
// 		})
// 	}
// }

// export default NextTherapistStripe.getInstance()
