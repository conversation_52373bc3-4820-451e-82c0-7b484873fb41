import { expect } from 'chai'
import { SMSQueue, EmailQueue, AppointmentQueue } from '../configs/queue.config'
import { QueueConnection } from '../configs/redis.config'

describe('Queue Initialization', () => {
	it('should initialize SMSQueue with correct name and connection', () => {
		expect(SMSQueue.name).to.equal('ApplicationQueue')
		expect(SMSQueue.opts.connection).to.deep.equal(QueueConnection)
	})

	it('should initialize EmailQueue with correct name and connection', () => {
		expect(EmailQueue.name).to.equal('ApplicationQueue')
		expect(EmailQueue.opts.connection).to.deep.equal(QueueConnection)
	})

	it('should initialize AppointmentQueue with correct name and connection', () => {
		expect(AppointmentQueue.name).to.equal('AppointmentQueue')
		expect(AppointmentQueue.opts.connection).to.deep.equal(QueueConnection)
	})
})
