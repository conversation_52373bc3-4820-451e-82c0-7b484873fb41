import cors from 'cors'
import { Application, Request } from 'express'

const allowedDomains = [
	'localhost:5173',
	'localhost:5174',
	'127.0.0.1:5173',
	'127.0.0.1:5174',
	'localhost:3000',
	'dev.app.nexttherapist.com',
	'app.nexttherapist.com',
	'localhost:52564',
]

export const corsOptionsDelegate = function (req: Request, callback: Function) {
	let corsOptions = { origin: false }
	const origin = req.header('Origin')

	if (
		origin !== undefined &&
		allowedDomains.some((domain) => origin.includes(domain)) &&
		(corsOptions = { origin: true })
	) {
		corsOptions = { origin: true }
	}

	callback(null, corsOptions) // callback expects two parameters: error and options
}

export const SetupCors = (app: Application) => {
	app.use(cors(corsOptionsDelegate))
}
