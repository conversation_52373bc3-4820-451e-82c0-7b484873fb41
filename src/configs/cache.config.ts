import { Request } from 'express'
import client from './redis.config'

export const isCacheable = (req: Request) => {
	const { method } = req
	if (method !== 'GET') return false
	return true
}

export const remember = <T>(
	key: string,
	getData: () => Promise<T>
): Promise<T> => {
	return new Promise((resolve, reject) => {
		// Check if the data exists in the cache
		client
			.get(key)
			.then((data) => {
				if (data) {
					// Resolve with the cached data
					resolve(JSON.parse(data))
				} else {
					// Get the data from the original source
					getData()
						.then((data) => {
							// Save the data to the cache
							client.set(key, JSON.stringify(data))
							// Resolve with the original data
							resolve(data)
						})
						.catch((err) => reject(err))
				}
			})
			.catch(reject)
	})
}

export const resetKey = (prefix: string) => {
	client
		.keys(`${prefix}*`)
		.then((keys) => {
			keys.forEach((key) => {
				client.del(key)
			})
		})
		.catch((err) => {
			console.log(err)
		})
}

const CacheMiddleware = (prefix: string = '') => {
	return async (req: Request, res: any, next: any) => {
		const key = prefix + (req.originalUrl || req.url)

		if (!isCacheable(req)) {
			if (res.statusCode < 400) {
				resetKey(prefix)
			}
			return next()
		}

		const data = await client.get(key)
		if (data) {
			return res.send(JSON.parse(data))
		}
		res.sendResponse = res.send
		res.send = (body: any) => {
			client.set(key, JSON.stringify(body))
			res.sendResponse(body)
		}
		next()
	}
}

export default CacheMiddleware
