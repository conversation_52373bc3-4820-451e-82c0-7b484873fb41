import { Sequelize } from 'sequelize-typescript'
import logger from './logger.config'
import * as models from '@/src/models'

let dbName = process.env.DB_NAME as string
let dbUser = process.env.DB_USER as string
let dbHost = process.env.DB_HOST as string
let dbHostWriter = process.env.DB_HOST_WRITER as string
let dbPort = process.env.DB_PORT as string
let dbPassword = process.env.DB_PASSWORD as string

const logging =
	process.env.DEBUG === 'True'
		? (message: any) => logger.info(message)
		: false

if (process.env.NODE_ENV === 'test') {
	dbName = process.env.TEST_DB_NAME as string
	dbHost = process.env.TEST_DB_HOST as string
	dbHostWriter = process.env.TEST_DB_HOST_WRITER as string
	dbUser = process.env.TEST_DB_USER as string
	dbPort = process.env.TEST_DB_PORT as string
	dbPassword = process.env.TEST_DB_PASSWORD as string
}

const sequelize = new Sequelize(dbName, dbUser, dbPassword, {
	dialect: 'postgres',
	host: dbHost,
	port: parseInt(dbPort),
	replication: {
		read: [
			{
				host: dbHost,
				username: dbUser,
				password: dbPassword,
			},
		],
		write: {
			host: dbHostWriter,
			username: dbUser,
			password: dbPassword,
		},
	},
	dialectOptions: {
		supportBigNumbers: true,
		parseInt: true,
		decimalNumbers: true,
	},
	logging: false,
	// logging,
})

// sequelize = new Sequelize(dbName, dbUser, dbPassword, {
// 	dialect: 'postgres',
// 	host: dbHost,
// 	port: parseInt(dbPort),
// 	dialectOptions: {
// 		supportBigNumbers: true,
// 		parseInt: true,
// 		decimalNumbers: true,
// 	},
// 	logging,
// 	pool: {
// 		max: 5,
// 		min: 0,
// 		idle: 10000,
// 		acquire: 30000,
// 	},
// })

sequelize.addModels(Object.values(models))

export default sequelize
