import { expect } from 'chai'
import sinon from 'sinon'
import { SetupCors, corsOptionsDelegate } from '../configs/cors.config'


describe('CORS Config', () => {
  describe('corsOptionsDelegate', () => {
    it('should allow requests from allowed domain', (done) => {
      const req = {
        header: (key: string) => {
          if (key === 'Origin') return 'http://localhost:5173'
        }
      } as any

      corsOptionsDelegate(req, (err: Error | null, options: { origin: boolean }) => {
        expect(err).to.be.null
        expect(options.origin).to.be.true
        done()
      })
    })

    it('should disallow requests from disallowed domain', (done) => {
      const req = {
        header: (key: string) => {
          if (key === 'Origin') return 'http://evil.com'
        }
      } as any

      corsOptionsDelegate(req, (err: Error | null, options: { origin: boolean }) => {
        expect(err).to.be.null
        expect(options.origin).to.be.false
        done()
      })
    })

    it('should disallow requests with no origin header', (done) => {
      const req = {
        header: () => undefined
      } as any

      corsOptionsDelegate(req, (err: Error | null, options: { origin: boolean }) => {
        expect(err).to.be.null
        expect(options.origin).to.be.false
        done()
      })
    })
  })

describe('SetupCors', () => {
  it('should call app.use with a middleware function', () => {
    const useStub = sinon.stub()
    const app = { use: useStub } as any

    SetupCors(app)

    expect(useStub.calledOnce).to.be.true
    expect(typeof useStub.firstCall.args[0]).to.equal('function')
  })
})
})
