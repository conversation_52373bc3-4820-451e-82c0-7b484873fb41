import twilio, { <PERSON>wi<PERSON> } from 'twilio';

const accountSid: string = process.env.TWILIO_ACCOUNT_SID || '';
const authToken: string = process.env.TWILIO_AUTH_TOKEN || '';
const fromNumber: string = process.env.TWILIO_PHONE_NUMBER || '';


const client: Twilio = twilio(accountSid, authToken);

export async function sendSms(to: string, messageBody: string): Promise<void> {
  try {
    const message = await client.messages.create({
      body: messageBody,
      from: fromNumber,
      to,
    });
    console.log(`Message sent! SID: ${message.sid}`);
  } catch (error) {
    console.error('Error sending SMS:', error);
		throw new Error('Failed to send SMS');
  }
}
