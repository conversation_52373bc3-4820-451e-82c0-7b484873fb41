import { expect } from 'chai'
import sinon from 'sinon'
import { Request } from 'express'
import * as Cache from '../configs/cache.config'
import client from '../configs/redis.config'

describe('Redis Cache Utilities', () => {
	let sandbox: sinon.SinonSandbox

	beforeEach(() => {
		sandbox = sinon.createSandbox()
	})

	afterEach(() => {
		sandbox.restore()
	})

	describe('isCacheable', () => {
		it('should return true for GET method', () => {
			const req = { method: 'GET' } as Request
			expect(Cache.isCacheable(req)).to.be.true
		})

		it('should return false for POST method', () => {
			const req = { method: 'POST' } as Request
			expect(Cache.isCacheable(req)).to.be.false
		})
	})

	describe('remember', () => {
		it('should return cached data if present', async () => {
			const key = 'test-key'
			const data = { msg: 'cached' }

			sandbox.stub(client, 'get').resolves(JSON.stringify(data))
			const setStub = sandbox.stub(client, 'set')

			const result = await Cache.remember(key, () => Promise.resolve({}))

			expect(result).to.deep.equal(data)
			expect(setStub.notCalled).to.be.true
		})

		it('should call getData and cache it if data not found', async () => {
			const key = 'new-key'
			const data = { msg: 'fresh' }

			sandbox.stub(client, 'get').resolves(null)
			const setStub = sandbox.stub(client, 'set')
			const getData = sandbox.stub().resolves(data)

			const result = await Cache.remember(key, getData)

			expect(result).to.deep.equal(data)
			expect(getData.calledOnce).to.be.true
			expect(setStub.calledOnceWith(key, JSON.stringify(data))).to.be.true
		})

		it('should reject if client.get fails', async () => {
			const key = 'fail-key'
			const err = new Error('Redis error')
			sandbox.stub(client, 'get').rejects(err)

			try {
				await Cache.remember(key, () => Promise.resolve({}))
				throw new Error('Expected error not thrown')
			} catch (error: any) {
				expect(error.message).to.equal('Redis error')
			}
		})

		it('should reject if getData fails', async () => {
			const key = 'fail-data'
			const getData = sandbox.stub().rejects(new Error('Data error'))

			sandbox.stub(client, 'get').resolves(null)

			try {
				await Cache.remember(key, getData)
				throw new Error('Expected error not thrown')
			} catch (error: any) {
				expect(error.message).to.equal('Data error')
			}
		})
	})

	describe('resetKey', () => {
		it('should delete all keys matching prefix', async () => {
			const keys = ['prefix:1', 'prefix:2']
			sandbox.stub(client, 'keys').resolves(keys)
			const delStub = sandbox.stub(client, 'del')

			await Cache.resetKey('prefix:')

			expect(delStub.callCount).to.equal(keys.length)
		})

		it('should log error if client.keys fails', async () => {
			const error = new Error('fail')
			const consoleSpy = sandbox.stub(console, 'log')
			sandbox.stub(client, 'keys').rejects(error)

			// Call resetKey (which starts async chain) but does not return a promise
			Cache.resetKey('prefix:')

			// Wait for asynchronous handlers to run
			await new Promise((resolve) => setImmediate(resolve))

			expect(consoleSpy.calledOnce).to.be.true
			expect(consoleSpy.calledWith(error)).to.be.true
		})

	})

	describe('CacheMiddleware', () => {
		const buildMockRes = () => {
			return {
				send: sinon.stub(),
				statusCode: 200,
			}
		}

		it('should skip caching for non-GET and call next', async () => {
			const req = { method: 'POST', originalUrl: '/submit' } as Request
			const res = buildMockRes()
			const next = sandbox.stub()

			await Cache.default('')(req, res, next)

			expect(next.calledOnce).to.be.true
		})

		it('should reset keys if statusCode < 400 for non-GET', async () => {
			const req = { method: 'PUT', originalUrl: '/update' } as Request
			const res = buildMockRes()
			const next = sandbox.stub()
			sandbox.stub(client, 'keys').resolves(['k1'])
			sandbox.stub(client, 'del')

			await Cache.default('pre:')(req, res, next)

			expect(next.calledOnce).to.be.true
		})

		it('should respond with cached data if present', async () => {
			const req = { method: 'GET', originalUrl: '/cache' } as Request
			const res = buildMockRes()
			const data = { value: 'cached' }

			sandbox.stub(client, 'get').resolves(JSON.stringify(data))

			await Cache.default('')(req, res, sinon.stub())

			expect(res.send.calledOnceWith(data)).to.be.true
		})

		it('should store new data and call res.send if no cache', async () => {
			const req = { method: 'GET', originalUrl: '/new' } as Request
			const res = buildMockRes()
			sandbox.stub(client, 'get').resolves(null)
			const setStub = sandbox.stub(client, 'set')
			const next = sandbox.stub()

			await Cache.default('')(req, res, next)

			expect(next.calledOnce).to.be.true

			const body = { msg: 'fresh' }
			res.send(body)

			expect(setStub.calledOnceWith('/new', JSON.stringify(body))).to.be.true
		})
	})

	describe('dayjs plugin setup', () => {
		it('should run plugin setup code', () => {			
			require('../configs/global.config')
		})
		})
})
