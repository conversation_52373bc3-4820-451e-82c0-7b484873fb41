import { expect } from 'chai'
import sinon from 'sinon'
import * as outlookUtils from '../configs/outlook.config'
import { Client } from '@microsoft/microsoft-graph-client'
import { BadRequestError } from '../application/handlers/errors'
import logger from '../configs/logger.config'
import { TokenAuthProvider } from '../configs/outlook.config'

const assertRejected = async (fn: () => Promise<any>, errorType: any) => {
  try {
    await fn()
  } catch (err) {
    expect(err).to.be.instanceOf(errorType)
    return
  }
  throw new Error('Expected function to throw')
}

describe('Microsoft Graph Utilities', () => {
  let sandbox: sinon.SinonSandbox
  let mockClient: any

  beforeEach(() => {
    sandbox = sinon.createSandbox()

    mockClient = {
      api: sandbox.stub().returnsThis(),
      get: sandbox.stub(),
      post: sandbox.stub(),
      delete: sandbox.stub(),
      orderby: sandbox.stub().returnsThis(),
      filter: sandbox.stub().returnsThis(),
    }
  })

  afterEach(() => {
    sandbox.restore()
  })

  it('TokenAuthProvider returns access token', async () => {
    const provider = new TokenAuthProvider('abc123')
    const token = await provider.getAccessToken()
    expect(token).to.equal('abc123')
  })

  it('getAuthenticatedClient returns a Client instance', () => {
    const client = outlookUtils.getAuthenticatedClient('xyz')
    expect(client).to.be.instanceOf(Client)
  })

  it('getProfile throws error when client not passed', async () => {
    await assertRejected(() => outlookUtils.getProfile(null as any), BadRequestError)
  })

  it('getProfile returns profile data', async () => {
    mockClient.api.withArgs('/me').returns(mockClient)
    mockClient.get.resolves({ displayName: 'John' })

    const result = await outlookUtils.getProfile(mockClient)
    expect(result.displayName).to.equal('John')
  })

  it('getOutlookCalendar throws error when client not passed', async () => {
    await assertRejected(() => outlookUtils.getOutlookCalendar(null as any), BadRequestError)
  })

  it('getOutlookCalendar returns calendar list', async () => {
    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.resolves({ value: [{ id: '1' }] })

    const result = await outlookUtils.getOutlookCalendar(mockClient)
    expect(result.value).to.have.lengthOf(1)
  })

  it('getOutlookCalendarEvents returns filtered events from target calendars', async () => {
    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.onFirstCall().resolves({
      value: [
        { id: '1', name: 'Calendar' },
        { id: '2', name: 'SimplePractice' },
      ],
    })

    mockClient.api.withArgs('/me/calendars/1/events').returns(mockClient)
    mockClient.api.withArgs('/me/calendars/2/events').returns(mockClient)
    mockClient.get.onSecondCall().resolves({ value: [{ id: 'a' }] })
    mockClient.get.onThirdCall().resolves({ value: [{ id: 'b' }] })

    const result = await outlookUtils.getOutlookCalendarEvents(mockClient)
    expect(result).to.have.lengthOf(2)
  })

  it('getOutlookCalendarEventsForDate returns date-filtered events', async () => {
    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.onFirstCall().resolves({
      value: [
        { id: '1', name: 'Calendar' },
        { id: '2', name: 'SimplePractice' },
      ],
    })

    mockClient.api.withArgs('/me/calendars/1/events').returns(mockClient)
    mockClient.api.withArgs('/me/calendars/2/events').returns(mockClient)
    mockClient.get.onSecondCall().resolves({ value: [{ id: 'c' }] })
    mockClient.get.onThirdCall().resolves({ value: [{ id: 'd' }] })

    const result = await outlookUtils.getOutlookCalendarEventsForDate(
      mockClient,
      '2025-01-01T00:00:00Z',
      '2025-01-01T23:59:59Z'
    )
    expect(result).to.have.lengthOf(2)
  })

  it('createOutlookCalendarEvent creates event in simplepractice calendar if available', async () => {
    const event = { subject: 'Test', attendees: [] }

    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.resolves({
      value: [{ id: '123', name: 'SimplePractice' }],
    })
    mockClient.api.withArgs('/me/calendars/123/events').returns(mockClient)
    mockClient.post.resolves({ id: 'event123' })

    const result = await outlookUtils.createOutlookCalendarEvent(mockClient, event)
    expect(result.id).to.equal('event123')
  })

  it('createOutlookCalendarEvent falls back to primary calendar if simplepractice not found', async () => {
    const event = { subject: 'Test', attendees: [] }

    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.resolves({ value: [] })
    mockClient.api.withArgs('/me/events').returns(mockClient)
    mockClient.post.resolves({ id: 'fallbackEvent' })

    const result = await outlookUtils.createOutlookCalendarEvent(mockClient, event)
    expect(result.id).to.equal('fallbackEvent')
  })

  it('createOutlookCalendarEvent logs error if calendars fetch fails', async () => {
    const event = { subject: 'Meeting' }
    const loggerSpy = sandbox.stub(logger, 'error')

    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.rejects(new Error('Calendar fetch fail'))
    mockClient.api.withArgs('/me/events').returns(mockClient)
    mockClient.post.resolves({ id: 'createdInFallback' })

    const result = await outlookUtils.createOutlookCalendarEvent(mockClient, event)
    expect(result.id).to.equal('createdInFallback')
    expect(loggerSpy.called).to.be.true
  })

  it('createOutlookCalendarEvent throws error on post failure', async () => {
    const event = { subject: 'Meeting' }

    mockClient.api.withArgs('/me/calendars').returns(mockClient)
    mockClient.get.rejects(new Error('fail'))
    mockClient.api.withArgs('/me/events').returns(mockClient)
    mockClient.post.rejects(new Error('Post failed'))

    sandbox.stub(logger, 'error')

    await assertRejected(() => outlookUtils.createOutlookCalendarEvent(mockClient, event), Error)
  })

  it('deleteOutlookCalendarEvent deletes event and returns true', async () => {
    mockClient.api.withArgs('/me/events/abc').returns(mockClient)
    mockClient.delete.resolves()

    const result = await outlookUtils.deleteOutlookCalendarEvent(mockClient, 'abc')
    expect(result).to.be.true
  })

  it('deleteOutlookCalendarEvent throws error if no client', async () => {
    await assertRejected(() => outlookUtils.deleteOutlookCalendarEvent(null as any, 'abc'), BadRequestError)
  })
})
