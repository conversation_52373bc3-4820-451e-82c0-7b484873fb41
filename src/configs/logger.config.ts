import winston from "winston";
import Winston<PERSON>loudWatch from "winston-cloudwatch";
const { combine, timestamp, label, printf, json } = winston.format;

const CATEGORY = "application";

const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const colors = {
  error: "red",
  warn: "yellow",
  info: "green",
  http: "magenta",
  debug: "white",
};

winston.addColors(colors);

const customFormat = printf(({ level, message, label, timestamp }) => {
  return `${timestamp} [${label}] ${level}: ${message}`;
});

const fileFormat = combine(
  label({ label: CATEGORY }),
  timestamp(),
  customFormat
);

const consoleFormat = combine(
  // winston.format.colorize({ all: true }),
  label({ label: CATEGORY }),
  timestamp(),
  customFormat,
);

/* currently do not want save logs in a file
const transports = [
  // - Write to all logs with level `info` and below to `combined.log`
  // - Write all logs error (and below) to `error.log`.
  new winston.transports.File({
    filename: "logs/error.log",
    level: "error",
    format: combine(errorFilter(), timestamp()),
  }),
  new winston.transports.File({
    filename: "logs/info.log",
    level: "info",
    format: combine(infoFilter(), timestamp()),
  }),

  new winston.transports.File({ filename: "logs/combined.log" }),
];
*/

let cloudWatchTransport: WinstonCloudWatch | null = null;
if (
  process.env.LOG_GROUP_NAME &&
  process.env.LOG_STREAM &&
  process.env.LOG_REGION &&
  process.env.AWS_ACCESS_KEY &&
  process.env.AWS_SECRET_KEY
) {
  cloudWatchTransport = new WinstonCloudWatch({
    logGroupName: process.env.LOG_GROUP_NAME,
    logStreamName: process.env.LOG_STREAM,
    awsRegion: process.env.LOG_REGION,
    messageFormatter: ({ level, message, timestamp }) =>
      `[${timestamp || new Date().toISOString()}] ${level}: ${message}`,
  });
}

const logger = winston.createLogger({
  level: "info",
  levels,
  format: combine(label({ label: CATEGORY }), timestamp(), json()),
});

if (process.env.NODE_ENV === "production" && cloudWatchTransport) {
  logger.add(cloudWatchTransport);
} else {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
    })
  );
}

export default logger;
