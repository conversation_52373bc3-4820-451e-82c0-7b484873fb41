import { AuthenticationProvider, Client, ClientOptions } from '@microsoft/microsoft-graph-client';
import { BadRequestError } from '../application/handlers/errors';
import logger from './logger.config';

// Custom AuthenticationProvider class
export class TokenAuthProvider implements AuthenticationProvider {
  private accessToken: string;

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  async getAccessToken(): Promise<string> {
    return this.accessToken; // Return the token
  }
}

// Function to initialize the Microsoft Graph Client
export const getAuthenticatedClient = (accessToken: string): Client => {
  const clientOptions: ClientOptions = {
    authProvider: new TokenAuthProvider(accessToken), // Use custom auth provider
  };

  return Client.initWithMiddleware(clientOptions);
};

export const getProfile = async (client: Client) => {
  if (!client) throw new BadRequestError('Graph client not initialized');

  const profile = await client.api('/me').get();
  return profile;
};

export const getOutlookCalendar = async (client: Client) => {
	if (!client) throw new BadRequestError('Graph client not initialized');

  const calendars = await client
    .api('/me/calendars')
    .get();

  return calendars;
};

export const getOutlookCalendarEvents = async (client: Client) => {
	if (!client) throw new BadRequestError('Graph client not initialized');

  const calendarsResponse = await client.api('/me/calendars').get();
  const calendars = calendarsResponse.value || [];

  const targetCalendars = calendars.filter((cal: any) =>
    cal.name.toLowerCase() === 'calendar' || cal.name.toLowerCase().includes('simplepractice')
  );

  const eventsPromises = targetCalendars.map(async (calendar: any) => {
    const eventsResponse = await client
      .api(`/me/calendars/${calendar.id}/events`)
      .orderby('start/dateTime')
      .get();

    return eventsResponse.value || [];
  });

  const eventsArrays = await Promise.all(eventsPromises);
  return eventsArrays.flat();

  /*
  const events = await client
    .api(`/me/events`)
    .orderby('start/dateTime')
    .get();

  return events;
  */
};

export const getOutlookCalendarEventsForDate = async (client: Client, startOfDay: string, endOfDay: string) => {
  // Check for graph client initialization
  if (!client) throw new BadRequestError('Graph client not initialized');

  const calendarsResponse = await client.api('/me/calendars').get();
  const calendars = calendarsResponse.value || [];

  const targetCalendars = calendars.filter((cal: any) =>
    cal.name.toLowerCase() === 'calendar' || cal.name.toLowerCase().includes('simplepractice')
  );

  const eventsPromises = targetCalendars.map(async (calendar: any) => {
    const eventsResponse = await client
      .api(`/me/calendars/${calendar.id}/calendarView`)
      // .filter(`start/dateTime ge '${startOfDay}' and start/dateTime le '${endOfDay}'`)
      .query({
        startDateTime: startOfDay,
        endDateTime: endOfDay,
      })
      .orderby('start/dateTime')
      .get();

    return eventsResponse.value || [];
  });

  const eventsArrays = await Promise.all(eventsPromises);
  return eventsArrays.flat();

  /*
  const events = await client
    .api(`/me/events`)
    // .header('Prefer', 'outlook.timezone="Asia/Kathmandu"')
    .filter(`start/dateTime ge '${startOfDay}' and start/dateTime le '${endOfDay}'`)
    .orderby('start/dateTime')
    .get();

  return events;
  */
};

export const createOutlookCalendarEvent = async (client: Client, event: any) => {
  if (!client) throw new BadRequestError("Graph client not initialized");

  try {
    event.transactionId = `localevent:${Date.now()}`; 
    event.allowNewTimeProposals = false;
    event.isOrganizer =false;
    event.isAllDay = false;
    event.isDraft = false;
    event.responseRequested = true; 
    
    // Check if the user has a calendar named "simplepractice"
    try {
      const calendarsResponse = await client.api("/me/calendars").get();

      const simplePracticeCalendar = calendarsResponse.value.find(
        (cal: any) => cal.name.toLowerCase() === "simplepractice"
      );

      if (simplePracticeCalendar) {          
        // create event in simplePractice calendar if user has simplePractice calendar
        return await client.api(`/me/calendars/${simplePracticeCalendar.id}/events`).post(event);
      }
    } catch (error) {
      logger.error("Error checking for Simple Practice calendar:", error);
    }  

    // create event directly in primary calendar
    return await client.api("/me/events").post(event);
  } catch (err) {
    logger.error("Error adding event to Outlook calendar:", err);
    throw new Error("Failed to create calendar event");
  }
};


export const deleteOutlookCalendarEvent = async (client: Client, eventId: string) => {
  if (!client) throw new BadRequestError('Graph client not initialized');
  
  // DELETE request to remove the specified event
  await client
    .api(`/me/events/${eventId}`)
    .delete();

  return true;
};
