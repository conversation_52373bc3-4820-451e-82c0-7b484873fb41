import { expect } from 'chai'
import sinon from 'sinon'
import { google } from 'googleapis'
import * as googleUtils from '../configs/google.config'
import { Credentials } from 'google-auth-library'

describe('Google Calendar Utilities', () => {
	let sandbox: sinon.SinonSandbox
	let mockClient: any
	let calendarStub: any
	const tokens: Credentials = {
		access_token: 'token',
		refresh_token: 'refresh',
		expiry_date: 123456789,
		token_type: 'Bearer',
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox()

		// Mock OAuth2Client
		mockClient = {
			getToken: sandbox.stub().resolves({ tokens }),
			setCredentials: sandbox.stub(),
			refreshAccessToken: sandbox.stub().resolves({ credentials: tokens }),
			revokeCredentials: sandbox.stub().resolves(),
		}
		sandbox.stub(google.auth, 'OAuth2').returns(mockClient)

		// Mock Calendar API
		calendarStub = {
			calendarList: {
				list: sandbox.stub().resolves({
					data: {
						items: [
							{ id: 'primary', summary: 'primary' },
							{ id: 'other', summary: 'SimplePractice Calendar' },
						],
					},
				}),
			},
			events: {
				list: sandbox.stub().resolves({ data: { items: [{ id: 'event1' }] } }),
				insert: sandbox.stub().resolves({ data: { id: 'newEvent' } }),
				update: sandbox.stub().resolves({ data: { id: 'updatedEvent' } }),
				delete: sandbox.stub().resolves(),
				get: sandbox.stub().resolves({ data: { id: 'event1' } }),
				instances: sandbox.stub().resolves({ data: { items: [{ id: 'eventInstance1' }] } }),
			},
		}
		sandbox.stub(google, 'calendar').returns(calendarStub)

		// Mock OAuth2 Userinfo
		sandbox.stub(google, 'oauth2').returns({
			userinfo: {
				get: sandbox.stub().resolves({ data: { email: '<EMAIL>' } }),
			},
		} as any)
	})

	afterEach(() => {
		sandbox.restore()
	})

	it('createOAuth2Client should return client', () => {
		const client = googleUtils.createOAuth2Client()
		expect(client).to.exist
	})

	it('getTokenFromCode should return tokens', async () => {
		const result = await googleUtils.getTokenFromCode('code')
		expect(result).to.deep.equal(tokens)
		expect(mockClient.getToken.calledOnce).to.be.true
	})

	it('initializeOAuth2Client should set credentials and return client', () => {
		const client = googleUtils.initializeOAuth2Client(tokens)
		expect(mockClient.setCredentials.calledOnceWith(tokens)).to.be.true
		expect(client).to.equal(mockClient)
	})

	it('revokeAccess should call revokeCredentials', async () => {
		await googleUtils.revokeAccess(tokens)
		expect(mockClient.revokeCredentials.calledOnce).to.be.true
	})

	it('regenerateCredentials should return credentials', async () => {
		const result = await googleUtils.regenerateCredentials('refresh')
		expect(result).to.deep.equal(tokens)
		expect(mockClient.setCredentials.calledOnceWith({ refresh_token: 'refresh' })).to.be.true
		expect(mockClient.refreshAccessToken.calledOnce).to.be.true
	})

	it('getGoogleAccountFromCode should return user data', async () => {
		const result = await googleUtils.getGoogleAccountFromCode('code')
		expect(result.email).to.equal('<EMAIL>')
		expect(mockClient.setCredentials.called).to.be.true
	})

	it('getUserProfile should return user data', async () => {
		const result = await googleUtils.getUserProfile(tokens)
		expect(result.email).to.equal('<EMAIL>')
		expect(mockClient.setCredentials.called).to.be.true
	})

	it('getGoogleCalendar should return calendar list', async () => {
		const result = await googleUtils.getGoogleCalendar(tokens)
		expect(result.items).to.be.an('array').with.length(2)
	})

	it('getGoogleCalendarEvents should return filtered events', async () => {
		const result = await googleUtils.getGoogleCalendarEvents(tokens)
		expect(result).to.be.an('array').with.length(2)
		expect(result[0].id).to.equal('event1')
	})

	it('getCalendarEventsForDate should return events for date range', async () => {
		const result = await googleUtils.getCalendarEventsForDate(tokens, '2025-01-01', '2025-01-02')
		expect(result).to.be.an('array').with.length(2)
		expect(calendarStub.events.list.called).to.be.true
	})

	it('createGoogleCalendarEvent should insert and return event data', async () => {
		const event = { summary: 'Test Meeting', attendees: [{ email: '<EMAIL>' }] }
		const result = await googleUtils.createGoogleCalendarEvent(tokens, event)
		expect(result.id).to.equal('newEvent')
		expect(calendarStub.events.insert.calledOnce).to.be.true
	})

	it('updateGoogleCalendarEvent should update and return event data', async () => {
		const result = await googleUtils.updateGoogleCalendarEvent(tokens, 'eventId', { summary: 'Updated Event' })
		expect(result.id).to.equal('updatedEvent')
		expect(calendarStub.events.update.calledOnce).to.be.true
	})

	it('deleteGoogleCalendarEvent should delete and return true', async () => {
		const result = await googleUtils.deleteGoogleCalendarEvent(tokens, 'eventId')
		expect(result).to.be.true
		expect(calendarStub.events.delete.calledOnce).to.be.true
	})

	it('getGoogleCalendarEvent should return single event', async () => {
		const result = await googleUtils.getGoogleCalendarEvent(tokens, 'eventId')
		expect(result.id).to.equal('event1')
		expect(calendarStub.events.get.calledOnce).to.be.true
	})

	it('getGoogleCalendarEventInstances should return event instances', async () => {
  calendarStub.events.instances.resolves({
    data: {
      items: [{ id: 'eventInstance1' }],
    },
  });

    const result = await googleUtils.getGoogleCalendarEventInstances(tokens, 'eventId');

    expect(result.items).to.be.an('array').with.length(1);
    expect(result.items?.[0].id).to.equal('eventInstance1');
    expect(calendarStub.events.instances.calledOnce).to.be.true;
  });

})
