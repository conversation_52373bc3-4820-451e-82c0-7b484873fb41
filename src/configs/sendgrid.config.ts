import sgMail from '@sendgrid/mail';

class NextTherapistEmail {
    constructor() {
        if(process.env.NODE_ENV !== 'test') {
            if (!process.env.SENDGRID_API_KEY) {
                throw new Error('SENDGRID_API_KEY is not set in the environment variables.');
            }
            if (!process.env.SENDGRID_FROM_EMAIL) {
                throw new Error('SENDGRID_FROM_EMAIL is not set in the environment variables.');
            }
            sgMail.setApiKey(process.env.SENDGRID_API_KEY as string);
        }
    }

    async sendMail(emailData: {
        to?: string;
        from?: string;
        subject?: string;
        text?: string;
        html?: string;
    }, forceSend: boolean = false) {
        // Apply defaults for missing email fields
        const mailData = {
            to: emailData.to || '<EMAIL>', // Default recipient
            from: emailData.from || process.env.SENDGRID_FROM_EMAIL as string, // Default sender from environment variable
            subject: emailData.subject || 'Default Subject', // Default subject
            text: emailData.text || 'Default text content', // Default plain text
            html: emailData.html || '<p>Default HTML content</p>', // Default HTML content
        };

        try {
            if (forceSend || process.env.LOCAL_ENV !== 'true') {
                await sgMail.send(mailData);
            }
            // console.log('Email sent successfully');
        } catch (error) {
            // console.error('Error sending email:', error);
            console.dir(error, { depth: null, colors: true });
            throw new Error('Failed to send email');
        }
    }
}

const mail = new NextTherapistEmail();

export { mail };
