import { Credentials } from 'google-auth-library'
import { google } from 'googleapis'

export const createOAuth2Client = () => {
  return new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URI
  )
}

export const getTokenFromCode = async (code: string) => {
  const client = createOAuth2Client()
  const { tokens } = await client.getToken({
    code,
    redirect_uri: process.env.GOOGLE_REDIRECT_URI,
  })
  return tokens
}

export const initializeOAuth2Client = (tokens: Credentials) => {
  const client = createOAuth2Client()
  client.setCredentials(tokens)
  return client
}

export const revokeAccess = async (tokens: Credentials) => {
  const client = initializeOAuth2Client(tokens)
  await client.revokeCredentials()
}

export const regenerateCredentials = async (refreshToken: string) => {
  const client = createOAuth2Client()
  client.setCredentials({ refresh_token: refreshToken })
  const { credentials } = await client.refreshAccessToken()
  return credentials
}

export const getGoogleAccountFromCode = async (code: string) => {
  const client = createOAuth2Client()
  const { tokens } = await client.getToken(code)
  client.setCredentials(tokens)
  const oauth2 = google.oauth2({ version: 'v2', auth: client })
  const { data } = await oauth2.userinfo.get()
  return data
}

export const getUserProfile = async (tokens: Credentials) => {
  const client = initializeOAuth2Client(tokens)
  const oauth2 = google.oauth2({ version: 'v2', auth: client })
  const { data } = await oauth2.userinfo.get()
  return data
}

export const getGoogleCalendar = async (tokens: Credentials) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })
  const { data } = await calendar.calendarList.list({
    minAccessRole: 'owner',
  })
  return data
}

export const getGoogleCalendarEvents = async (tokens: Credentials) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })

  const calendarList = await calendar.calendarList.list();
  const calendars = calendarList.data.items || [];

  const targetCalendars = calendars.filter(cal =>
    cal.primary ||
    cal.summary?.toLowerCase().includes('simplepractice')
  );

  const eventsPromises = targetCalendars.map(async (cal) => {
    const response = await calendar.events.list({
      calendarId: cal.id!,
      timeMin: new Date().toISOString(),
      singleEvents: true,
      orderBy: 'startTime',
    });
    return response.data.items || [];
  });

  const eventsArrays = await Promise.all(eventsPromises);
  return eventsArrays.flat(); 

  /* prev code for reference
  const { data } = await calendar.events.list({
    calendarId: 'primary',
    timeMin: new Date().toISOString(),
    singleEvents: true,
    orderBy: 'startTime',
  })
  return data
  */
}

export const getCalendarEventsForDate = async (tokens: Credentials, startDate: string, endDate: string) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })

  // Fetch list of calendars
  const calendarList = await calendar.calendarList.list();
  const calendars = calendarList.data.items || [];

  const targetCalendars = calendars.filter(cal =>
    cal.primary ||
    cal.summary?.toLowerCase().includes('simplepractice')
  );

  const eventsPromises = targetCalendars.map(async (cal) => {
    const response = await calendar.events.list({
      calendarId: cal.id!,
      timeMin: startDate,
      timeMax: endDate,
      singleEvents: true,
      orderBy: 'startTime',
    });
    return response.data.items || [];
  });

  const eventsArrays = await Promise.all(eventsPromises);
  return eventsArrays.flat(); 

  /* prev code for reference
  const { data } = await calendar.events.list({
    calendarId: 'primary',
    timeMin: startDate,
    timeMax: endDate,
    singleEvents: true,
    orderBy: 'startTime',
  })
  return data
  */
}

export const createGoogleCalendarEvent = async (tokens: Credentials, event: any) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })
  if (event.attendees && Array.isArray(event.attendees)) {
    event.attendees = event.attendees.map((attendee: any) => ({
      ...attendee,
      responseStatus: 'accepted',
    }))
  }
  const response = await calendar.events.insert({
    calendarId: 'primary',
    requestBody: event,
  })
  return response.data
}

export const updateGoogleCalendarEvent = async (tokens: Credentials, eventId: string, data: any) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })
  
  const response = await calendar.events.update({
    calendarId: 'primary',
    eventId,
    requestBody: data,
  })
  return response.data
}

export const deleteGoogleCalendarEvent = async (tokens: Credentials, eventId: string) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })
  await calendar.events.delete({
    calendarId: 'primary',
    eventId,
  })
  return true;
}

export const getGoogleCalendarEvent = async (tokens: Credentials, eventId: string) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })
  const { data } = await calendar.events.get({
    calendarId: 'primary',
    eventId,
  })
  return data
}

export const getGoogleCalendarEventInstances = async (tokens: Credentials, eventId: string) => {
  const client = initializeOAuth2Client(tokens)
  const calendar = google.calendar({ version: 'v3', auth: client })
  const { data } = await calendar.events.instances({
    calendarId: 'primary',
    eventId,
  })
  return data
}
