import Redis, { RedisOptions } from 'ioredis'
import logger from './logger.config'

const { REDIS_URL } = process.env
let hasLoggedError = false;

const redisConfig:RedisOptions = {
    host: REDIS_URL,
	retryStrategy: (times:any) => {
        if (times > 5) { 
            logger.error('Redis: Max reconnection attempts reached.');
            return null;
        }
        const delay = Math.min(times * 500, 5000);
        return delay; 
    }
};
if(process.env.LOCAL_ENV !== 'true') {
    redisConfig.tls = {rejectUnauthorized: false};
}

const client = new Redis(redisConfig)

client.on('connect', () => {
	logger.info('Redis connection has been established successfully.')
})


client.on('error', (err: Error) => {
    if (!hasLoggedError) {  
        logger.error(`Redis Error: ${err.message}`, { error: err });
        hasLoggedError = true;
    }
})

export const QueueConnection = {
	host: REDIS_URL,	
}

export default client

