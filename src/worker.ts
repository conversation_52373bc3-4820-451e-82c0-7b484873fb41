import dotenv from 'dotenv'
if (process.env.NODE_ENV === 'production') {
	require('module-alias/register')
}
import { Job, Worker } from 'bullmq'
try {
	dotenv.config()
} catch (error) {
	console.log('No .env file found')
}

// import { WorkerJob } from '@/src/application/jobs'
import { QUEUE } from '@/src/application/helpers/constant.helper'
import sequelize from '@/src/configs/database.config'
import logger from './configs/logger.config'

sequelize.authenticate().then((r) => console.log('DB Connected'))

export const connection = {
	host: process.env.REDIS_HOST,
	port: parseInt(process.env.REDIS_PORT as string),
}

// const handler = async (job: Job<WorkerJob>) => {
// 	switch (job.data.type) {
// 		case QUEUE.SMS:
// 			logger.info('SMS Job', job.data)
// 			job.updateProgress(100)
// 			return true
// 		case QUEUE.EMAIL:
// 			logger.info('Email Job', job.data)
// 			job.updateProgress(100)
// 			return true
// 		case QUEUE.EVENT:
// 			logger.info('Event Job', job.data)
// 			job.updateProgress(100)
// 			return true
// 		default:
// 			console.log('Unknown job', job.data)
// 			return 'Done'
// 	}
// }

// const worker = new Worker('ApplicationQueue', handler, {
// 	connection,
// })

// worker.on('completed', (job: Job, returnvalue: any) => {
// 	console.log('🚀 ~ file: worker.ts:46 ~ worker.on ~ returnvalue:', returnvalue)
// 	console.log('🚀 ~ file: worker.ts:46 ~ worker.on ~ job:', job)
// })

// worker.on('failed', (job: any, err: Error) => {
// 	switch (job.data.type) {
// 		case QUEUE.SMS:
// 			logger.error('SMS Job', job.data)
// 			break
// 		case QUEUE.EMAIL:
// 			logger.info('Email Job', job.data)
// 			break
// 		case QUEUE.EVENT:
// 			logger.info('Event Job', job.data)
// 			break
// 		default:
// 			console.log('Unknown job', job.data)
// 	}
// })

// worker.on('error', (err: Error) => {
// 	console.log('Error in worker', err)
// })
