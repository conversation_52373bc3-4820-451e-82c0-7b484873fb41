import { useEffect, useRef, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { signOut } from "@/store/slicers/auth.slicer";
import toast from "react-hot-toast";
import {INACTIVITY_STATUS} from "../src/pages/auth/constants";

const INACTIVITY_LIMIT = 15 * 60 * 1000;
const WARNING_TIME = 14 * 60 * 1000;


const SessionTimeout = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningRef = useRef<NodeJS.Timeout | null>(null);
  const warningToastId = useRef<string | null>(null);

  const resetTimer = useCallback(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningRef.current) clearTimeout(warningRef.current);

    if (warningToastId.current) {
      toast.dismiss(warningToastId.current);
      warningToastId.current = null;
    }

    warningRef.current = setTimeout(() => {
      warningToastId.current = toast(
        <span style={{ color: "#000", fontWeight: "bold" }}>
        {INACTIVITY_STATUS}
        </span>,
        {
          duration: 60000,
          style: {
            background: "#fff",
            color: "#000",
            border: "1px solid #ffcc00",
            padding: "10px",
            fontSize: "14px",
          },
        }
      );
    }, WARNING_TIME);

    timeoutRef.current = setTimeout(() => {
      dispatch(signOut());
    }, INACTIVITY_LIMIT);
  }, [dispatch]);

  useEffect(() => {
    if (user?.role === "therapist") {
      resetTimer();
      window.addEventListener("mousemove", resetTimer);
      window.addEventListener("keydown", resetTimer);
      window.addEventListener("click", resetTimer);
    }

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (warningRef.current) clearTimeout(warningRef.current);
      if (warningToastId.current) toast.dismiss(warningToastId.current);

      window.removeEventListener("mousemove", resetTimer);
      window.removeEventListener("keydown", resetTimer);
      window.removeEventListener("click", resetTimer);
    };
  }, [user, resetTimer]);

  return null;
};

export default SessionTimeout;