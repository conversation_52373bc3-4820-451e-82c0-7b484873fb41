@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-MediumItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-MediumItalic.woff") format("woff");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Medium.woff2") format("woff2"),
    url("/fonts/muller/Muller-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-ThinItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-ThinItalic.woff") format("woff");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Thin.woff2") format("woff2"),
    url("/fonts/muller/Muller-Thin.woff") format("woff");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-RegularItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-RegularItalic.woff") format("woff");
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Regular.woff2") format("woff2"),
    url("/fonts/muller/Muller-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-LightItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-LightItalic.woff") format("woff");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-ExtraBoldItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-ExtraBoldItalic.woff") format("woff");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Bold.woff2") format("woff2"),
    url("/fonts/muller/Muller-Bold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Heavy.woff2") format("woff2"),
    url("/fonts/muller/Muller-Heavy.woff") format("woff");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-BoldItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-BoldItalic.woff") format("woff");
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-HeavyItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-HeavyItalic.woff") format("woff");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-ExtraBold.woff2") format("woff2"),
    url("/fonts/muller/Muller-ExtraBold.woff") format("woff");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-BlackItalic.woff2") format("woff2"),
    url("/fonts/muller/Muller-BlackItalic.woff") format("woff");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Light.woff2") format("woff2"),
    url("/fonts/muller/Muller-Light.woff") format("woff");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Muller";
  src:
    url("/fonts/muller/Muller-Black.woff2") format("woff2"),
    url("/fonts/muller/Muller-Black.woff") format("woff");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Feeling Passionate Swash";
  src:
    url("/fonts/passionate/Rondetta-Swash.woff2") format("woff2"),
    url("/fonts/passionate/Rondetta-Swash.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Feeling Passionate";
  src:
    url("/fonts/passionate/FeelingPassionate-Regular.woff2")
      format("woff2"),
    url("/fonts/passionate/FeelingPassionate-Regular.woff")
      format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.muller {
  font-family: "Muller", sans-serif;
}

.passionate {
  font-family: "Feeling Passionate", sans-serif;
}

.react-datepicker__current-month {
  display: none !important;
}