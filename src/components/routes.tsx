import { Navigate, Route, Routes, useLocation } from "react-router-dom";
import loadable from "@loadable/component";
import { lazy, Suspense } from "react";
import PerfectScrollbar from "react-perfect-scrollbar";
import "react-perfect-scrollbar/dist/css/styles.css";
import EditTherapistPage from "./layouts/EditTherapistPage";
import NotificationSettingPage from "@/pages/domain/therapy/profile/notification-settings.page";
const LogInPage = loadable(() => import("@/pages/auth/login.page"));

const RegisterLayout = lazy(() => import("./layouts/RegisterLayout"));

const ForgotPasswordPage = loadable(() => import("@/pages/auth/ForgotPassword"));
const ResetPasswordPage = loadable(() => import("@/pages/auth/ResetPasswordPage"));
const VerifyEmailPage = loadable(() => import("@/pages/auth/VerifyEmailPage"));
const EmailVerificationPage = loadable(() => import("@/pages/auth/EmailVerificationPage"));
const MfaPage = loadable(() => import("@/pages/auth/MfaPage"));

const DashboardPage = loadable(() => import("@/pages/DashboardPage"));
const UserListPage = loadable(() => import("@/pages/users/UserListPage"));
const PatientListPage = loadable(() => import("@/pages/patients/PatientListPage"));
const TherapistListPage = loadable(() => import("@/pages/therapists/TherapistListPage"));
const TherapistProfile = loadable(() => import("@/pages/therapists/TherapistProfilePage"));
const QuestionnaireListPage = loadable(() => import("@/pages/questionnaires/QuestionnaireListPage"));
const AnswerListPage = loadable(() => import("@/pages/answers/AnswerListPage"));
const SpecialityPage = loadable(() => import("@/pages/specialities/SpecialityListPage"));
const EventPage = loadable(() => import("@/pages/events/EventListPage"));
const SettingPage = loadable(() => import("@/pages/setting.page"));
const PageListPage = loadable(() => import("@/pages/pages/PageListPage"));
const SortPage = loadable(() => import("@/pages/pages/sort.page"));
const TherapistPageListPage = loadable(() => import("@/pages/therapist-pages/PageListPage"));
const TherapistPageFormPage = loadable(() => import("@/pages/therapist-pages/PageForm"));
const RegisterModalities = lazy(() => import("@/pages/auth/registration/modalities.page"));

const SubscriptionPlanPage = loadable(() => import("@/pages/subscription-plans/SubscriptionListPage"));
const MatchingAlgoPage = loadable(() => import("@/pages/therapist-pages/MatchingAlgoPage"));

// Therapist Imports
const TherapistDashboardPage = loadable(() => import("@/pages/domain/therapy/dashboard.page"));
const TherapistPersonalDetailsPage = loadable(() => import("@/pages/domain/therapy/personal-details/index.page"));
const TherapistCalendarsAndAppointmentsPage = loadable(() => import("@/pages/domain/therapy/calendars-appointments/index.page"));
const TherapistProfilePage = loadable(() => import("@/pages/domain/therapy/profile/index.page"));
const TherapistSubscriptionsAndPaymentsPage = loadable(() => import("@/pages/domain/therapy/subscriptions-payments/index.page"));
const TherapistReportingPage = loadable(() => import("@/pages/domain/therapy/reporting/index.page"));
const TherapistPasswordSecurityPage = loadable(() => import("@/pages/domain/therapy/password-security/index.page"));
const TherapistNotificationsPage = loadable(() => import("@/pages/domain/therapy/notifications/index.page"));
const TherapistHelpCenterPage = loadable(() => import("@/pages/domain/therapy/help-center/index.page"));
const TherapistOfficeHoursPage = loadable(() => import("@/pages/auth/registration/normal-office-hours.page"));
const TherapistEditSpecialitiesPage = loadable(() => import("@/pages/domain/therapy/edit-specialitis"));
const TherapistEditAdditionalFocusPage = loadable(() => import("@/pages/domain/therapy/edit-additional-focus"));

const AuthRoutes = () => {
  const location = useLocation();
  return (
    <Suspense fallback={<div className="loading-suspence"></div>}>
      <Routes location={location} key={location.pathname}>
        <Route path="/auth/login" key="auth-login" element={<LogInPage />} />
        <Route path="/auth/register/*" key="auth-register" element={<RegisterLayout />} />
        <Route path="/auth/forgot-password" key="auth-forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/auth/reset-password" key="auth-reset-password" element={<ResetPasswordPage />} />
        <Route path="/auth/verify-email" key="auth-verify-email" element={<VerifyEmailPage />} />
        <Route path="/auth/email-verification" key="auth-email-verification" element={<EmailVerificationPage />} />
        <Route path="/auth/mfa" key="auth-mfa" element={<MfaPage />} />
        <Route path="*" element={<Navigate to="/auth/login" />} />
      </Routes>
    </Suspense>
  );
};

const AppRoutes = () => {
  const location = useLocation();
  return (
    <Routes location={location} key={location.pathname}>
      <Route path="/" key="dashboard" element={<DashboardPage />} />
      <Route path="/users" key="users" element={<UserListPage />} />
      <Route path="/pages" key="pages" element={<PageListPage />} />
      <Route path="/therapist-pages" key="therapist-pages" element={<TherapistPageListPage />} />
      <Route path="/therapist-pages/form" key="add-therapist-page-form" element={<TherapistPageFormPage />} />
      <Route path="/pages/sort/:category" key="sort-page-list" element={<SortPage />} />
      <Route path="/patients" key="patients" element={<PatientListPage />} />
      <Route path="/therapists" key="therapists" element={<TherapistListPage />} />
      <Route path="/therapists/:id/profile" key="therapists-profile" element={<TherapistProfile />} />
      <Route path="/questionnaires" key="questionnaires" element={<QuestionnaireListPage />} />
      <Route path="/answers" key="answers" element={<AnswerListPage />} />
      <Route path="/specialities" key="specialities" element={<SpecialityPage />} />
      <Route path="/events" key="events" element={<EventPage />} />
      <Route path="/settings" key="profile" element={<SettingPage />} />
      <Route path="/subscription-plans" key="subscription-plans" element={<SubscriptionPlanPage />} />
      <Route path="/matching-algo" key="matching-algo" element={<MatchingAlgoPage />} />  
      <Route path="/notification-settings/:therapistId" key="notification-settings" element={<NotificationSettingPage />} />    
      <Route path="*" element={<Navigate to="/" />} />
    </Routes>
  );
};

const TherapyAppRoutes = () => {
  const location = useLocation();
  const noScrollbarRoutes = ["/reporting"];
  const shouldUseScrollbar = !noScrollbarRoutes.includes(location.pathname);

  const routes = (
    <Routes location={location} key={location.pathname}>
      <Route path="/" key="dashboard" element={<TherapistDashboardPage />} />
      <Route path="/personal-details" key="personal-details" element={<TherapistPersonalDetailsPage />} />
      <Route path="/calendars-appointments" key="calendars-appointments" element={<TherapistCalendarsAndAppointmentsPage />} />
      <Route path="/profile" key="profile" element={<TherapistProfilePage />} />
      <Route path="/subscriptions-payments" key="subscriptions-payments" element={<TherapistSubscriptionsAndPaymentsPage />} />
      <Route path="/reporting" key="reporting" element={<TherapistReportingPage />} />
      <Route path="/security" key="password-security" element={<TherapistPasswordSecurityPage />} />
      <Route path="/notifications" key="notifications" element={<TherapistNotificationsPage />} />
      <Route path="/help-center" key="help-center" element={<TherapistHelpCenterPage />} />
      <Route path="/office-hours" key="office-hours" element={<TherapistOfficeHoursPage />} />
      <Route path="/edit-specialities" key="edit-specialities" element={<TherapistEditSpecialitiesPage />} />
      <Route path="/edit-additionalfocus" key="edit-specialities" element={<TherapistEditAdditionalFocusPage />} />
      <Route path="/auth/register/*" key="auth-register" element={<RegisterLayout />} />
      <Route path="modalities" key="register:modalities" element={<RegisterModalities />} />
      <Route path="edit/:section" key="edit-section" element={<EditTherapistPage />} />
      <Route path="*" element={<Navigate to="/" />} />
    
     
    </Routes>
  );

  return shouldUseScrollbar ? <PerfectScrollbar id="scroller-div" data-testid="therapy-app-routes">{routes}</PerfectScrollbar> : routes;
 
};

export { AuthRoutes, AppRoutes, TherapyAppRoutes };
