import { classNames } from "@/utils/app.util";
import { useAppDispatch } from "@/store/hooks";
import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import { TherapyDate } from "@/plugins/calendar/types";
import Calendar from "@/plugins/calendar";

type CalendarProps = {
  days: TherapyDate[];
  calendar: Calendar;
  filters?: boolean;
};

const views = ["Day view", "Week view", "Month view"];

const FullCalendar = ({ days, calendar, filters = true }: CalendarProps) => {
  const dispatch = useAppDispatch();
  const [viewEnabled, setViewEnabled] = useState(false);
  const [view, setView] = useState("Month View");

  return (
    <div className="h-full flex flex-col">
      {filters && (
        <div className="flex-none flex flex-row justify-between items-center">
          <div></div>
          <div className="flex items-center gap-4">
            <div className="flex items-center rounded-md shadow-sm md:items-stretch">
              <button
                onClick={() => calendar.previous(dispatch)}
                type="button"
                className="flex nm-inset-gray-100 items-center justify-center rounded-l-md border border-r-0 border-gray-300 bg-white py-2 pl-3 pr-4 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:px-2 md:hover:bg-gray-50"
              >
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
              <button
                onClick={() => calendar.goto(new Date(), dispatch)}
                type="button"
                className="hidden nm-inset-gray-100 border-t border-b border-gray-300 bg-white px-3.5 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 focus:relative md:block"
              >
                Today
              </button>
              <span className="relative -mx-px h-5 w-px bg-gray-300 md:hidden"></span>
              <button
                onClick={() => calendar.next(dispatch)}
                type="button"
                className="flex nm-inset-gray-100 items-center justify-center rounded-r-md border border-l-0 border-gray-300 bg-white py-2 pl-4 pr-3 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:px-2 md:hover:bg-gray-50"
              >
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
            <div className="hidden md:flex flex-row gap-4 md:items-center">
              <div className="relative">
                <button
                  onClick={() => setViewEnabled(!viewEnabled)}
                  type="button"
                  className="flex nm-inset-gray-100 items-center rounded-md border border-gray-300 bg-white py-2 pl-3 pr-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
                  id="menu-button"
                  aria-expanded="false"
                  aria-haspopup="true"
                >
                  {view}
                  <svg
                    className="ml-2 h-5 w-5 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                <AnimatePresence>
                  {viewEnabled && (
                    <motion.div
                      animate={{ opacity: 1, scale: 1 }}
                      initial={{ opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.1 }}
                      exit={{ opacity: 0, scale: 0.95 }}
                      className="focus:outline-none absolute right-0 mt-3 w-36 origin-top-right overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-10"
                      role="menu"
                      aria-orientation="vertical"
                      aria-labelledby="menu-button"
                      tabIndex={-1}
                    >
                      <div className="py-1" role="none">
                      {views.map((viewOption, index) => (
                        <span
                          onClick={() => {
                            setView(viewOption);
                            setViewEnabled(false);
                          }}
                          className="text-gray-700 block px-4 py-2 text-sm cursor-pointer"
                          role="menuitem"
                          tabIndex={-1}
                          id={`menu-item-${index}`}
                        >
                          {viewOption}
                        </span>
                      ))}
                    </div>

                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
              <button
                type="button"
                className="focus:outline-none nm-inset-gray-100 rounded-md border border-transparent py-2 px-4 text-sm font-medium shadow-sm border border-yellow-500 active:scale-95 transition-all text-yellow-600"
              >
                Add event
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="flex-none grid grid-cols-7 bg-gray-200 text-center text-xs font-semibold leading-6 text-gray-700 lg:flex-none">
        <div className="bg-white py-2">Sun</div>
        <div className="bg-white py-2">Mon</div>
        <div className="bg-white py-2">Tue</div>
        <div className="bg-white py-2">Wed</div>
        <div className="bg-white py-2">Thu</div>
        <div className="bg-white py-2">Fri</div>
        <div className="bg-white py-2">Sat</div>
      </div>
      <div className="flex-grow flex border bg-gray-200 text-xs text-gray-700">
        <div className="w-full grid grid-cols-7 grid-rows-6 gap-px">
          {days.map((day: TherapyDate, i) => (
            <div
              key={`${day.key}-${i}`}
              className={classNames(
                day.isCurrentMonth ? "bg-white" : "bg-gray-50 text-gray-500",
                "relative py-2 px-3"
              )}
            >
              <time
                dateTime={day.time}
                className={
                  day.isToday
                    ? "flex h-6 w-6 items-center justify-center rounded-full bg-yellow-600 font-semibold text-white"
                    : undefined
                }
              >
                {day.dayOfMonth}
              </time>
              {day.events.length > 0 && (
                <ol className="mt-2">
                  {day.events.slice(0, 2).map((event) => (
                    <li key={event.id}>
                      <span className="group flex">
                        <p className="flex-auto truncate font-medium text-gray-900 group-hover:text-yellow-600">
                          {event.name}
                        </p>
                        <time
                          dateTime={event.date.toLocaleString()}
                          className="ml-3 hidden flex-none text-gray-500 group-hover:text-yellow-600 xl:block"
                        >
                          {event.time}
                        </time>
                      </span>
                    </li>
                  ))}
                  {day.events.length > 2 && (
                    <li className="text-gray-500">
                      + {day.events.length - 2} more
                    </li>
                  )}
                </ol>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FullCalendar;
