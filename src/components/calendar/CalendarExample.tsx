import CalendarRepository from "@/repositories/CalendarRepository";
import { useGoogleLogin } from "@react-oauth/google";
import { useEffect, useState } from "react";
import notification from "@/utils/notification.util";
import { createCode<PERSON>hallenge, generateURLEncodedString } from "@/utils/app.util";

const defaultScope = [
  "https://www.googleapis.com/auth/userinfo.email",
  "https://www.googleapis.com/auth/userinfo.profile",
  "https://www.googleapis.com/auth/calendar",
];

const CalendarExample = () => {
  const repository = new CalendarRepository();
  const [events] = useState<any>([]);
  
  const googleLogin = useGoogleLogin({
    flow: "auth-code",
    scope: defaultScope.join(" "),
    onSuccess: async (tokens) => {

      await repository.syncGoogleCalendar({
        code: tokens.code
      });
    },
    onError: (error) => {
      console.log(error)
      if (error.error === 'access_denied') return;
      notification.error(error.error || 'Something went wrong. Please try again.');
    },
  });


  const loginToOutlook = async () => {
    const authorizationUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize`;
    const clientId = import.meta.env.VITE_AZURE_CLIENT_ID;
    const redirectUri = 'http://localhost:5173/';
    const scopes = 'openid profile offline_access User.Read Calendars.ReadWrite';
    const responseType = 'code';
    const prompt = 'select_account';
    const state = await generateURLEncodedString();
    localStorage.setItem('state', state);

    // Generate code verifier and challenge for PKCE
    const codeVerifier = await generateURLEncodedString();
    const codeChallenge = await createCodeChallenge(codeVerifier);

    // Store the code verifier in localStorage to use later for token exchange
    localStorage.setItem('codeVerifier', codeVerifier);

    // Build the authorization URL with the code challenge
    const authUrl = `${authorizationUrl}?client_id=${clientId}&response_type=${responseType}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scopes)}&response_mode=query&state=${state}&code_challenge=${codeChallenge}&code_challenge_method=S256&prompt=${prompt}`;

    // Redirect to Azure AD for login
    window.location.href = authUrl;
  }

  const exchangeCodeForToken = async (authorizationCode: string, state: string) => {
    const codeVerifier = localStorage.getItem('codeVerifier');
    if (!codeVerifier) {
      notification.error('Something went wrong. Please try Logging in again.');
      return;
    }
    const storedState = localStorage.getItem('state');
    if (storedState && state !== storedState) {
      notification.error('Something went wrong. Please try Logging in again.');
      return;
    }
    notification.info('Syncing Outlook Calendar. Please wait...');
    await repository.syncOutlookCalendar({
      authorizationCode,
      codeVerifier
    })
  };

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const authorizationCode = params.get('code');
    const state = params.get('state');
    window.history.replaceState({}, document.title, window.location.pathname);
    if (authorizationCode && state) exchangeCodeForToken(authorizationCode, state);
  }, []);

  return (
    <div>
      <button className="border border-black rounded-md mt-4 mr-4 px-4 py-2 bg-white text-black" onClick={googleLogin}>
        Sync Google Calendar
      </button>
      <button className="border border-black rounded-md mt-4 mr-4 px-4 py-2 bg-white text-black" onClick={loginToOutlook}>
        Sync Outlook Calendar
      </button>
      {events.length > 0 && (
        <div>
          <h2>Upcoming Events</h2>
          <ul>
            {events.map((event: any) => (
              <li key={event.id} style={{ border: "1px solid #ccc", margin: "10px", padding: "10px", borderRadius: "5px" }}>
                <h3>{event.summary}</h3>
                <h4>{event?.description}</h4>
                <p><strong>Event Link:</strong> <a href={event.htmlLink} target="_blank" rel="noopener noreferrer">View Event</a></p>
                <p><strong>Status:</strong> {event.status}</p>
                <p><strong>Organizer:</strong> {event.organizer.email}</p>
                <p>
                  <strong>Start Date:</strong> {new Date(event.start.date || event.start.dateTime).toLocaleString()}
                </p>
                <p>
                  <strong>End Date:</strong> {new Date(event.end.date || event.end.dateTime).toLocaleString()}
                </p>
                <p><strong>Created At:</strong> {new Date(event.created).toLocaleString()}</p>
                <p><strong>Updated At:</strong> {new Date(event.updated).toLocaleString()}</p>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )
}

export default CalendarExample