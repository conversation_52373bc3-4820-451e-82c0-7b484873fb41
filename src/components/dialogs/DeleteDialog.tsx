import { FormEvent, Fragment, ReactNode, useState, useEffect } from "react";
import { OverlayTriggerState } from "react-stately";
import BaseRepository from "@/repositories/BaseRepository";
import TherapistRepository from "@/repositories/TherapistRepository";
import { HttpStatusCode } from "axios";
import { Dialog, Transition } from "@headlessui/react";

interface ReasonGroup {
  id: number;
  heading: string;
  subheadings: {
    id: number;
    subheading: string;
  }[];
}

type DeleteDialogProps = {
  state: OverlayTriggerState;
  children: ReactNode;
  title: string;
  verifyText?: string;
  url: string;
  refetch?: () => void;
  deletePassword?: boolean;
  setGlobalLoading?: (loading: boolean) => void;
  isDeactivate?: boolean;
};

const DeleteDialog = ({
  state,
  children,
  title,
  verifyText = "DELETE",
  url,
  refetch,
  deletePassword = false,
  setGlobalLoading,
  isDeactivate = false,
}: DeleteDialogProps) => {
  const repository = new BaseRepository();
  const therapistRepo = new TherapistRepository();
  const [verification, setVerification] = useState("");
  const [processing, setProcessing] = useState(false);
  const [selectedReasonId, setSelectedReasonId] = useState<number | null>(null);
  const [selectedReasonText, setSelectedReasonText] = useState("");
  const [reasonError, setReasonError] = useState("");
  const [selectedHeading, setSelectedHeading] = useState<number | null>(null);
  const [reasonGroups, setReasonGroups] = useState<ReasonGroup[]>([]);

  
  // Initialize dialog when opened
  useEffect(() => {
    if (state.isOpen) {
      initializeDialog();
    }
  }, [state.isOpen, isDeactivate]);
  
  // Initialize dialog state
  const initializeDialog = () => {
    fetchReasons();
    resetFormFields();
  };
  
  // Reset form fields
  const resetFormFields = () => {
    setVerification("");
    setReasonError("");
  };
  
  // Reset all state and close dialog
  const resetAndClose = () => {
    setVerification("");
    setSelectedReasonId(null);
    setSelectedReasonText("");
    setReasonError("");
    state.close();
  };
  
  // Fetch reasons based on action type
  const fetchReasons = async () => {
    try {
      const data = await fetchReasonData();
      handleReasonData(data);
    } catch (error) {
      resetReasonData();
    }
  };
  
  // Fetch reason data from API
  const fetchReasonData = async () => {
    if (isDeactivate) {
      return therapistRepo.getDeactivateReasons();
    } else {
      return therapistRepo.getDeleteReasons();
    }
  };
  
  // Handle reason data response
  const handleReasonData = (data: any) => {
    if (data && data.reasons && data.reasons.length > 0) {
      setReasonGroups(data.reasons);
      setSelectedHeading(data.reasons[0].id);
      if (data.reasons[0].subheadings?.length > 0) {
        setSelectedReasonId(data.reasons[0].subheadings[0].id);
        setSelectedReasonText(data.reasons[0].subheadings[0].subheading);
      }
    }
  };
  
  // Reset reason data
  const resetReasonData = () => {
    setSelectedReasonId(null);
    setSelectedReasonText("");
  };
  
  // Handle form submission
  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleDelete();
  };
  
  // Handle delete/deactivate action
  const handleDelete = async () => {
    if (!validateReason()) {
      return;
    }
    
    setLoadingState(true);
    
    try {
      const response = await performAction();
      handleActionSuccess(response);
    } catch (err: any) {
      handleActionError(err);
    } finally {
      setLoadingState(false);
    }
  };
  
  // Validate reason selection
  const validateReason = () => {
    if (selectedReasonId === null || !selectedReasonText) {
      setReasonError("Please select a reason");
      return false;
    }
    return true;
  };
  
  // Set loading state
  const setLoadingState = (isLoading: boolean) => {
    setProcessing(isLoading);
    if (setGlobalLoading) {
      setGlobalLoading(isLoading);
    }
  };
  
  // Build URL with parameters
  const buildActionUrl = () => {
    if (isRejectAction()) {
      return url;
    }
    
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}reason=${encodeURIComponent(selectedReasonText)}&reasonId=${selectedReasonId}`;
  };
  
  // Check if this is a reject action
  const isRejectAction = () => {
    return url.includes('/reject/');
  };
  
  // Perform the API action
  const performAction = async () => {
    const urlWithParams = buildActionUrl();
    
    // Handle reject action differently
    if (isRejectAction()) {
      // Extract the therapist ID from the URL
      const idMatch = url.match(/\/reject\/(\d+)/);
      const therapistId = idMatch ? parseInt(idMatch[1]) : null;
      
      if (!therapistId || !selectedReasonId) {
        throw new Error("Invalid therapist ID or reason not selected");
      }
      
      return therapistRepo.rejectWithReasonId(therapistId, selectedReasonId);
    } else if (isDeactivate) {
      return repository.patch(urlWithParams, {});
    } else {
      return repository.delete(urlWithParams);
    }
  };
  
  // Handle successful action
  const handleActionSuccess = (res: any) => {
    // Handle both response object and boolean success values
    if (res === true || res.status === HttpStatusCode.Ok || res.status === HttpStatusCode.Created) {
      showSuccessMessage(res);
      resetAndClose();
      handleRefetch();
    }
  };
  
  // Show success message
  const showSuccessMessage = (res: any) => {
    let successMessage;
    if (isRejectAction()) {
      successMessage = "Therapist account rejected successfully!";
    } else if (isDeactivate) {
      successMessage = "Deactivated successfully!";
    } else {
      successMessage = "Deleted successfully!";
    }
    repository.successToast(res.data?.message || successMessage);
  };
  
  // Handle refetch after successful action
  const handleRefetch = () => {
    if (refetch) {
      executeRefetch();
    }
  };
  
  // Execute refetch with retry
  const executeRefetch = () => {
    try {
      refetch!();
      
      // Only schedule a delayed refetch for non-reject actions
      // This prevents double API calls for reject actions
      if (!isRejectAction()) {
        scheduleDelayedRefetch();
      }
    } catch (error) {
      // Silent error handling for the immediate refetch
    }
  };
  
  // Schedule delayed refetch
  const scheduleDelayedRefetch = () => {
    setTimeout(() => {
      try {
        refetch!();
      } catch (error) {
        // Silent error handling for the delayed refetch
      }
    }, 500);
  };
  
  // Handle action error
  const handleActionError = (err: any) => {
    repository.errorToast(
      err?.response?.data?.message ?? "Something went wrong, try again later!"
    );
  };
  
  // Check if delete button should be disabled
  const isDeleteButtonDisabled = () => {
    if (isDeactivate) {
      return verification !== "DEACTIVATE";
    } 
    if (verifyText) {
      return verification !== verifyText;
    }
    return false;
  };
  
  // Render verification message
  const renderVerificationMessage = () => {
    if (isDeactivate) {
      return (
        <>Type <span className="font-semibold text-xs italic text-red-800">DEACTIVATE </span></>
      );
    } 
    if (deletePassword) {
      return (
        <>Enter the <span className="font-bold text-xs text-red-800">DELETE </span></>
      );
    }
    return (
      <>Type <span className="font-semibold text-xs italic text-red-800">{verifyText} </span></>
    );
  };
  
  // Handle heading selection change
  const handleHeadingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const headingId = parseInt(e.target.value);
    setSelectedHeading(headingId);
    
    // Find the selected heading group
    const selectedGroup = reasonGroups.find(group => group.id === headingId);
    
    // Set the first subheading as selected by default
    if (selectedGroup && selectedGroup.subheadings && selectedGroup.subheadings.length > 0) {
      setSelectedReasonId(selectedGroup.subheadings[0].id);
      setSelectedReasonText(selectedGroup.subheadings[0].subheading);
    } else {
      setSelectedReasonId(null);
      setSelectedReasonText("");
    }
    
    setReasonError('');
  };
  
  // Handle subheading selection change
  const handleSubheadingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const subheadingId = parseInt(e.target.value);
    updateSelectedSubheading(subheadingId);
    setReasonError('');
  };
  
  // Update selected subheading
  const updateSelectedSubheading = (subheadingId: number) => {
    if (!isNaN(subheadingId)) {
      setSelectedReasonId(subheadingId);
      
      // Find the selected heading group
      const selectedGroup = reasonGroups.find(group => group.id === selectedHeading);
      
      // Find the selected subheading
      if (selectedGroup && selectedGroup.subheadings) {
        const subheading = selectedGroup.subheadings.find(item => item.id === subheadingId);
        if (subheading) {
          setSelectedReasonText(subheading.subheading);
        }
      }
    } else {
      resetReasonSelection();
    }
  };
  
  // Get available subheadings for the selected heading
  const getSubheadingsForSelectedHeading = () => {
    const selectedGroup = reasonGroups.find(group => group.id === selectedHeading);
    return selectedGroup?.subheadings || [];
  };
  
  // Reset reason selection
  const resetReasonSelection = () => {
    setSelectedReasonId(null);
    setSelectedReasonText('');
  };

  // Render the dialog UI
  const renderDialog = () => {
    return (
      <Transition appear show={state.isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-10" onClose={resetAndClose}>
          {renderBackdrop()}
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              {renderDialogContent()}
            </div>
          </div>
        </Dialog>
      </Transition>
    );
  };
  
  // Render backdrop transition
  const renderBackdrop = () => {
    return (
      <Transition.Child
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="fixed inset-0 bg-black bg-opacity-25" />
      </Transition.Child>
    );
  };
  
  // Render dialog content with transition
  const renderDialogContent = () => {
    return (
      <Transition.Child
        as={Fragment}
        enter="ease-out duration-300"
        enterFrom="opacity-0 scale-95"
        enterTo="opacity-100 scale-100"
        leave="ease-in duration-200"
        leaveFrom="opacity-100 scale-100"
        leaveTo="opacity-0 scale-95"
      >
        <Dialog.Panel
          className="transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all w-[600px] max-w-[90vw]"
        >
          <Dialog.Title
            as="h3"
            className="text-lg font-medium leading-6 text-gray-900"
          >
            {title}
          </Dialog.Title>
          {renderForm()}
        </Dialog.Panel>
      </Transition.Child>
    );
  };
  
  // Render form content
  const renderForm = () => {
    return (
      <form onSubmit={onSubmit}>
        <div className="mt-4">
          {children}
          {renderReasonDropdown()}
        </div>
        {renderVerificationInput()}
        {renderActionButtons()}
      </form>
    );
  };
  
  // Render reason dropdowns
  const renderReasonDropdown = () => {
    return (
      <div className="mt-3">
        <div className="space-y-3">
          {/* Category dropdown */}
          <div>
            <label htmlFor="heading" className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              id="heading"
              className={`bg-gray-50 border ${reasonError ? 'border-red-500' : 'border-primary'} text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5`}
              value={selectedHeading !== null ? String(selectedHeading) : ''}
              onChange={handleHeadingChange}
              required
            >
              {reasonGroups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.heading}
                </option>
              ))}
            </select>
          </div>
          
          {/* Reason dropdown */}
          <div>
            <label htmlFor="subheading" className="block text-sm font-medium text-gray-700 mb-1">
              Reason
            </label>
            <select
              id="subheading"
              className={`bg-gray-50 border ${reasonError ? 'border-red-500' : 'border-primary'} text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5`}
              value={selectedReasonId !== null ? String(selectedReasonId) : ''}
              onChange={handleSubheadingChange}
              required
            >
              {getSubheadingsForSelectedHeading().map((subheading) => (
                <option key={subheading.id} value={subheading.id} className="whitespace-normal">
                  {subheading.subheading}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        {reasonError && (
          <p className="text-red-500 text-xs mt-1">{reasonError}</p>
        )}
      </div>
    );
  };
  
  // Render verification input
  const renderVerificationInput = () => {
    return (
      <div className="text-sm mt-2">
        {renderVerificationMessage()}
        to confirm {isDeactivate ? "deactivation" : "deletion"}.
        <input
          type="text"
          required
          className="bg-gray-50 border border-primary text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5 mt-2"
          value={verification}
          onChange={(e) => setVerification(e.target.value)}
        />
      </div>
    );
  };
  
  // Render action buttons
  const renderActionButtons = () => {
    return (
      <div className="mt-4 flex items-center justify-end space-x-2">
        {renderCancelButton()}
        {renderSubmitButton()}
      </div>
    );
  };
  
  // Render cancel button
  const renderCancelButton = () => {
    return (
      <button
        type="button"
        onClick={resetAndClose}
        className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
      >
        Cancel
      </button>
    );
  };
  
  // Render submit button
const renderSubmitButton = () => {
  let buttonLabel = "";

  if (isRejectAction()) {
    buttonLabel = "Reject";
  } else if (isDeactivate) {
    buttonLabel = "Deactivate";
  } else {
    buttonLabel = "Delete";
  }

  return (
    <button
      disabled={isDeleteButtonDisabled()}
      type="submit"
      className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-700"
    >
      {processing && (
        <i className="fa fa-spinner animate-spin mr-1"></i>
      )}{" "}
      {buttonLabel}
    </button>
  );
};

  
  return renderDialog();
};

export default DeleteDialog;
