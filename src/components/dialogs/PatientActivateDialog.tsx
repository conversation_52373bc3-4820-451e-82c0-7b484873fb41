import { FormEvent, Fragment, ReactNode, useState, useEffect } from "react";
import { OverlayTriggerState } from "react-stately";
import BaseRepository from "@/repositories/BaseRepository";
import { HttpStatusCode } from "axios";
import { Dialog, Transition } from "@headlessui/react";

type PatientActivateDialogProps = {
  state: OverlayTriggerState;
  children: ReactNode;
  title: string;
  url: string;
  refetch?: () => void;
  setGlobalLoading?: (loading: boolean) => void;
};

const VERIFICATION_TEXT = "ACTIVATE";
const VERIFICATION_ERROR_MESSAGE = "Please type 'ACTIVATE' to confirm";

// Custom hook to handle form logic
const usePatientActivation = (
  url: string,
  refetch?: () => void,
  setGlobalLoading?: (loading: boolean) => void
) => {
  const [verification, setVerification] = useState("");
  const [verificationError, setVerificationError] = useState("");
  const [processing, setProcessing] = useState(false);
  const repository = new BaseRepository();

  const resetForm = () => {
    setVerification("");
    setVerificationError("");
  };

  const clearError = () => setVerificationError("");

  const validateInput = () => {
    const isValid = verification.toUpperCase() === VERIFICATION_TEXT;
    if (!isValid) {
      setVerificationError(VERIFICATION_ERROR_MESSAGE);
    }
    return isValid;
  };

  const performActivation = async () => {
    setProcessing(true);
    setGlobalLoading?.(true);

    try {
      const response = await repository.patch(url, {});
      
      if (response.status === HttpStatusCode.Ok) {
        repository.successToast("Patient activated successfully");
        await refetch?.();
        return true;
      }
      return false;
    } catch (error: any) {
      console.error("Error activating patient:", error);
      repository.errorToast(
        error?.response?.data?.message || "Failed to activate patient"
      );
      return false;
    } finally {
      setProcessing(false);
      setGlobalLoading?.(false);
    }
  };

  return {
    verification,
    setVerification,
    verificationError,
    processing,
    resetForm,
    clearError,
    validateInput,
    performActivation,
  };
};

// Simplified button component
const ActionButton = ({ 
  onClick, 
  disabled, 
  variant, 
  children, 
  type = "button" 
}: {
  onClick?: () => void;
  disabled: boolean;
  variant: "cancel" | "submit";
  children: ReactNode;
  type?: "button" | "submit";
}) => {
  const baseClasses = "inline-flex w-full justify-center rounded-md px-4 py-2 text-base font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:text-sm";
  
  const variantClasses = {
    cancel: "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 sm:col-start-1 sm:mt-0",
    submit: disabled 
      ? "border border-transparent bg-indigo-300 text-white sm:col-start-2"
      : "border border-transparent bg-indigo-600 text-white hover:bg-indigo-700 sm:col-start-2"
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]}`}
    >
      {children}
    </button>
  );
};

// Input field component
const VerificationInput = ({
  value,
  onChange,
  hasError,
  disabled,
}: {
  value: string;
  onChange: (value: string) => void;
  hasError: boolean;
  disabled: boolean;
}) => {
  const baseClasses = "block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm";
  const errorClasses = hasError ? "border-red-500" : "";

  return (
    <input
      type="text"
      id="verification"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={`${baseClasses} ${errorClasses}`}
      placeholder={VERIFICATION_TEXT}
      disabled={disabled}
    />
  );
};

const PatientActivateDialog = (props: PatientActivateDialogProps) => {
  const { state, children, title, url, refetch, setGlobalLoading } = props;
  
  const {
    verification,
    setVerification,
    verificationError,
    processing,
    resetForm,
    clearError,
    validateInput,
    performActivation,
  } = usePatientActivation(url, refetch, setGlobalLoading);

  useEffect(() => {
    if (state.isOpen) {
      resetForm();
    }
  }, [state.isOpen]);

  const handleClose = () => {
    if (!processing) {
      resetForm();
      state.close();
    }
  };

  const handleVerificationChange = (value: string) => {
    setVerification(value);
    if (verificationError) {
      clearError();
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validateInput()) return;
    
    const success = await performActivation();
    if (success) {
      handleClose();
    }
  };

  return (
    <Transition.Root show={state.isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={handleClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <form onSubmit={handleSubmit}>
                  <div>
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-medium leading-6 text-gray-900"
                      >
                        {title}
                      </Dialog.Title>
                      <div className="mt-2">
                        <div className="text-sm text-gray-500">
                          {children}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <div className="space-y-2">
                      <label htmlFor="verification" className="block text-sm font-medium text-gray-700">
                        Type <span className="font-bold">{VERIFICATION_TEXT}</span> to confirm
                      </label>
                      <VerificationInput
                        value={verification}
                        onChange={handleVerificationChange}
                        hasError={!!verificationError}
                        disabled={processing}
                      />
                      {verificationError && (
                        <p className="mt-1 text-sm text-red-600">
                          {verificationError}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                    <ActionButton
                      onClick={handleClose}
                      disabled={processing}
                      variant="cancel"
                    >
                      Cancel
                    </ActionButton>
                    <ActionButton
                      type="submit"
                      disabled={processing}
                      variant="submit"
                    >
                      {processing ? "Activating..." : "Activate"}
                    </ActionButton>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default PatientActivateDialog;