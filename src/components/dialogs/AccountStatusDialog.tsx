import React, { useState } from "react";
import NormalDialog from "./NormalDialog";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store/index";
import { fetchAllRegInfoAction, sendVerificationEmailAction } from "@/store/slicers/therapist.slicer";
import { setNewRegistrationState } from "@/store/slicers/auth.slicer";
import { resetSidebarState } from "@/store/slicers/sidebar.slicer";
import { generateRegFormContent } from "@/utils/app.util";
import { useNavigate } from "react-router-dom";
import { OverlayTriggerState, useOverlayTriggerState } from "react-stately";
import InputComponent from "../forms/InputComponent";
import notification from "@/utils/notification.util";

interface AccountStatusDialogProps {
  underReviewState: OverlayTriggerState;
  rejectedState: OverlayTriggerState;
	incompleteState: OverlayTriggerState;
	accountExistState?: OverlayTriggerState;
  userId: number | null;
	email: string;
}

const AccountStatusDialog: React.FC<AccountStatusDialogProps> = ({
  underReviewState,
  rejectedState,
	incompleteState,
	accountExistState,
  userId,
  email,
}) => {
	const dispatch = useDispatch<AppDispatch>();
	const navigate = useNavigate();
	const verificationCodeState = useOverlayTriggerState({});

	const [loading, setLoading] = useState<boolean>(false);
	const [isSendingCode, setIsSendingCode] = useState<boolean>(false);
	const [code, setCode] = useState<string | null>(null);
	const [countdown, setCountdown] = useState<number>(0);

	const startCountdown = () => {
		setCountdown(60);
		const timer = setInterval(() => {
			setCountdown((prevCount) => {
				if (prevCount <= 1) {
					clearInterval(timer)
					return 0
				}
				return prevCount - 1
			});
		}, 1000);
	}

	const sendVerificationCode = async (): Promise<boolean> => {
		if (!email) {
			notification.error("Email is required to send verification code.");
			return false;
		}

		setIsSendingCode(true);
		const res = await dispatch(sendVerificationEmailAction({ email, noLink: true }));
		setIsSendingCode(false);

		if (res?.status === 200) {
			startCountdown();
			return true;
		}
	
		return false;
	};
	

	const handleRegistrationInfoFetch = async () => {
		if (!userId || !code) return;

		setLoading(true);
		const res = await dispatch(fetchAllRegInfoAction(userId, code));
		setLoading(false);

		if (res && res.status === 200) {
			const userRegInfo = res.data;
			const newFormContent = generateRegFormContent(userRegInfo);
			dispatch(setNewRegistrationState({ newFormContent, useToken: userRegInfo.userToken}));
			dispatch(resetSidebarState());
			navigate('/auth/register/license');
		}
	};

	const renderResendLabel = () => {
		if (countdown > 0) {
			return <span className="text-danger font-semibold">Try again in {countdown}s</span>;
		}
	
		if (isSendingCode) {
			return <span className="text-therapy-blue-dark font-semibold">Sending...</span>;
		}
	
		return (
			<span
				className="text-therapy-blue-dark cursor-pointer font-semibold"
				onClick={sendVerificationCode}
			>
				Try again.
			</span>
		);
	};	

  return (
    <>
      <NormalDialog
        opacity={80}
        state={underReviewState}
        title="Account Under Review"
        onClose={() => underReviewState.close()}
        width="w-[80vw] sm:w-[300px] md:w-[400px] lg:w-[500px]"
        titleCentered={true}
      >
        <p className="text-xs sm:text-sm">
					This process may take a few business days. You will receive an email notification once a
					decision has been made, whether your application is accepted or declined.
					<br />
					<br />
					If you need any assistance in the meantime, feel free to contact us at &nbsp;
          <strong>
            <a className="underline" href="mailto:<EMAIL>">
              <EMAIL>
            </a>
          </strong>
        </p>
      </NormalDialog>

			<NormalDialog
        opacity={80}
        state={incompleteState}
        title="Registration Incomplete"
        onClose={() => incompleteState.close()}
        width="w-[80vw] sm:w-[300px] md:w-[400px] lg:w-[500px]"
        titleCentered={true}
        onCancel={() => incompleteState.close()}
        onAccept={async () => {
					const success = await sendVerificationCode();
					if (success) {
						incompleteState.close();
						verificationCodeState.open();
					}
				}}				
        primaryButtonColor={true}
        loading={isSendingCode}
				confirmLabel="Continue"
      >
        <p className="text-xs sm:text-sm">
					<strong>We detected an incomplete registration associated with your email address from your last visit.</strong>
          <br />
          <br />
          Click <strong>Continue</strong> to resume your registration where you left off, or click <strong>Cancel</strong> to start a new registration with a different account.
					<br />
					<br />
					<small>An email will be sent to provided email account for verification.</small>
        </p>
      </NormalDialog>

      <NormalDialog
        opacity={80}
        state={rejectedState}
        title="Account Rejected"
        onClose={() => rejectedState.close()}
        width="w-[80vw] sm:w-[300px] md:w-[400px] lg:w-[500px]"
        titleCentered={true}
        onCancel={() => rejectedState.close()}
        onAccept={async () => {
					const success = await sendVerificationCode();
					if (success) {
						rejectedState.close();
						verificationCodeState.open();
					}
				}}				
        primaryButtonColor={true}
        loading={isSendingCode}
				confirmLabel="Continue"
      >
        <p className="text-xs sm:text-sm">
					<strong>Your account with this email has been rejected. Please complete the registration with correct details.</strong>
          <br />
          <br />
					Click <strong>Continue</strong> to fetch the previous details you provided and apply the correct changes as required.
					<br />
					<br />
					<small>An email will be sent to provided email account for verification.</small>
        </p>
      </NormalDialog>

			{accountExistState && (
				<NormalDialog
					opacity={80}
					state={accountExistState}
					title="Account Exists"
					onClose={() => accountExistState.close()}
					width="w-[80vw] sm:w-[300px] md:w-[400px] lg:w-[500px]"
					titleCentered={true}
					onCancel={() => accountExistState.close()}
					onAccept={async () => {
						navigate('/auth/login');
					}}				
					primaryButtonColor={true}
					confirmLabel="Go to Login"
				>
					<p className="text-xs sm:text-sm">
						<strong>An account with this email already exists. Please try logging in instead.</strong>
						<br />
						<br />
						Click <strong>Go to Login</strong> to navigate to Login Page , or click <strong>Cancel</strong> to continue the registration with a different account.
					</p>
				</NormalDialog>
			)}

			<NormalDialog
				state={verificationCodeState}
				title="Verify your email address"
				confirmLabel="Verify"
				onCancel={() => {
					setCode(null)
					verificationCodeState.close()
				}}
				onAccept={handleRegistrationInfoFetch}
				marginTop={-100}
				primaryButtonColor={true}
				loading={loading}
				btnDisabled={!code || code.length !== 6 || isSendingCode}
			>
				<div>
					<p className="text-sm">
						A verification code has been sent to your email address <strong className="block">({email})</strong>
						Please input the code to verify your email and continue the registration with previous details.
					</p>
					
					<InputComponent
						value={code || ''}
						placeholder="Enter verification code"
						onChange={(value) => setCode(value)}
						id="verification_code"
						class="mt-5 mb-5"
						maxLength={6}
					/>

					<p>
						Didn't receive verification code? {!isSendingCode && "You can "}
						{renderResendLabel()}
					</p>
				</div>
			</NormalDialog>
    </>
  );
};

export default AccountStatusDialog;
