"use client"

/**
 * EmailVerificationDialog Component
 * Shows when a verification email is sent and handles the verification flow.
 */

import type React from "react"
import { useState, useCallback, useEffect } from "react"
import NormalDialog from "./NormalDialog"
import { useDispatch } from "react-redux"
import type { AppDispatch } from "@/store/index"
import { fetchLoginInfo, sendVerificationEmailAction } from "@/store/slicers/therapist.slicer"
import { setNewRegistrationState, signIn } from "@/store/slicers/auth.slicer"
import { resetSidebarState } from "@/store/slicers/sidebar.slicer"
import { generateRegFormContent } from "@/utils/app.util"
import { useNavigate } from "react-router-dom"
import { type OverlayTriggerState } from "react-stately"
import InputComponent from "../forms/InputComponent"
import notification from "@/utils/notification.util"
import { useApiClient } from "@/utils/api.util"
import {
  TRY_AGAIN,
  VERIFICATION_CODE_DIDNT_RECEIVED,
  VERIFICATION_CODE_INPUT,
  VERIFICATION_CODE,
  LOGIN_ERROR,
} from "@/pages/auth/constants"

interface EmailVerificationDialogProps {
  verificationState: OverlayTriggerState
  userId: number | null
  email: string
  password?: string
  onVerificationSuccess?: () => void
}

const EmailVerificationDialog: React.FC<EmailVerificationDialogProps> = ({
  verificationState,
  userId,
  email,
  password,
  onVerificationSuccess,
}) => {
  const dispatch = useDispatch<AppDispatch>()
  const navigate = useNavigate()
  const client = useApiClient()

  const [loading, setLoading] = useState<boolean>(false)
  const [isSendingCode, setIsSendingCode] = useState<boolean>(false)
  const [code, setCode] = useState<string | null>(null)
  const [countdown, setCountdown] = useState<number>(0)

  // Extract countdown logic to a separate effect
  useEffect(() => {
    if (countdown <= 0) return

    const timer = setInterval(() => {
      setCountdown((prevCount) => Math.max(0, prevCount - 1))
    }, 1000)

    return () => clearInterval(timer)
  }, [countdown])

  // Extract verification code sending logic
  const sendVerificationCode = useCallback(async (): Promise<boolean> => {
    if (!email) {
      notification.error("Email is required to send verification code.")
      return false
    }

    setIsSendingCode(true)
    try {
      const res = await dispatch(sendVerificationEmailAction({ email, noLink: true }))
      if (res?.status === 200) {
        setCountdown(60)
        return true
      }
      return false
    } finally {
      setIsSendingCode(false)
    }
  }, [email, dispatch])

  // Send verification code when dialog opens
  useEffect(() => {
    if (verificationState.isOpen) {
      sendVerificationCode()
    }
  }, [verificationState.isOpen, sendVerificationCode])

  // Extract auto-login logic
  const attemptAutoLogin = useCallback(
    async (userEmail: string, userPassword?: string) => {
      if (!userEmail || !userPassword) return

      try {
        const loginRes = await client.post("/auth/login", {
          email: userEmail,
          password: userPassword,
        })

        if (loginRes?.status === 200) {
          const { accessToken, refreshToken, user, mfaCodeSent } = loginRes.data

          if (accessToken && refreshToken && user) {
            dispatch(signIn({ token: accessToken, refreshToken, user }))
            notification.success("Login successful!")
          } else if (mfaCodeSent) {
            notification.success(loginRes.data.message)
            navigate(`/auth/mfa?email=${userEmail}`)
          }
        }
      } catch (error) {
        notification.error(LOGIN_ERROR)
      }
    },
    [client, dispatch, navigate],
  )

  // Extract registration info fetching logic
  const handleRegistrationInfoFetch = useCallback(async () => {
    if (!code) {
      notification.error("Please enter the verification code")
      return
    }
    
    // Check if userId is available from login page
    if (!userId) {
      notification.error("User ID is required for verification. Please try logging in again.")
      return
    }

    setLoading(true)
    try {
      // Both userId and code are required for the API
      const res = await dispatch(fetchLoginInfo(userId, code as string))

      if (res?.status === 200) {
        notification.success("Email verified successfully!")
        const userRegInfo = res.data
        const newFormContent = generateRegFormContent(userRegInfo)

        dispatch(
          setNewRegistrationState({
            newFormContent,
            useToken: userRegInfo.userToken,
          }),
        )
        dispatch(resetSidebarState())

        // Call the onVerificationSuccess callback if provided
        if (onVerificationSuccess) {
          onVerificationSuccess()
        }

        // Close the dialog
        verificationState.close()

        if (password) {
          await attemptAutoLogin(email, password)
        } else {
          navigate("/auth")
        }
      } else {
        notification.error(res?.data?.message || "Verification failed. Please try again.")
      }
    } catch (error) {
      notification.error("An error occurred during verification. Please try again.")
    } finally {
      setLoading(false)
    }
  }, [userId, code, dispatch, email, password, attemptAutoLogin, navigate, verificationState, onVerificationSuccess])

  // Extract resend label rendering
  const renderResendLabel = useCallback(() => {
    if (countdown > 0) {
      return <span className="text-danger font-semibold">Try again in {countdown}s</span>
    }

    if (isSendingCode) {
      return <span className="text-therapy-blue-dark font-semibold">Sending...</span>
    }

    return (
      <span className="text-therapy-blue-dark cursor-pointer font-semibold" onClick={sendVerificationCode}>
        {TRY_AGAIN}
      </span>
    )
  }, [countdown, isSendingCode, sendVerificationCode])

  return (
    <NormalDialog
      state={verificationState}
      title="Verify your email address"
      confirmLabel="Verify"
      onCancel={() => {
        setCode(null)
        verificationState.close()
      }}
      onAccept={handleRegistrationInfoFetch}
      marginTop={-100}
      primaryButtonColor={true}
      loading={loading}
      btnDisabled={!code || code.length !== 6 || isSendingCode}
    >
      <div>
        <p className="text-sm">
          {VERIFICATION_CODE} <strong className="block">({email})</strong>
          {VERIFICATION_CODE_INPUT}
        </p>

        <InputComponent
          value={code || ""}
          placeholder="Enter verification code"
          onChange={(value) => setCode(value)}
          id="verification_code"
          class="mb-5"
          maxLength={6}
        />

        <p>
          {VERIFICATION_CODE_DIDNT_RECEIVED} {!isSendingCode && "You can "}
          {renderResendLabel()}
        </p>
      </div>
    </NormalDialog>
  )
}

export default EmailVerificationDialog
