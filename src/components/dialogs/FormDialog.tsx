import { Dialog, Transition } from "@headlessui/react";
import { FormEvent, Fragment, ReactNode } from "react";
import { OverlayTriggerState } from "react-stately";

interface FormDialogProps {
  children: ReactNode;
  title: string;
  confirmLabel: string;
  onCancel: () => void;
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  loading?: boolean;
  state: OverlayTriggerState;
  width?: string;
  disabled?: boolean;
}

const FormDialog = (props: FormDialogProps) => {
  const {
    children,
    onCancel,
    onSubmit,
    confirmLabel,
    loading,
    state,
    title,
    width,
    disabled
  } = props;

  return (
    <Transition appear show={state.isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onCancel}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all ${
                  width ?? "w-[480px]"
                }`}
              >
                <Dialog.Title
                  as="h3"
                  className="text-lg font-medium leading-6 text-gray-900"
                >
                  {title}
                </Dialog.Title>
                <form onSubmit={onSubmit}>
                  <div className="mt-6">{children}</div>

                  <div className="mt-4 flex items-center justify-end space-x-2">
                    <button
                      type="button"
                      onClick={onCancel}
                      className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-1.5 text-sm font-medium text-white hover:bg-primary active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="inline-flex justify-center rounded-md border border-transparent bg-therapy-blue-dark px-4 py-1.5 text-sm font-medium text-white hover:bg-therapy-blue-dark active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                      disabled={disabled || loading}
                    >
                      {loading && (
                        <i className="fa fa-spinner animate-spin mr-1"></i>
                      )}{" "}
                      {confirmLabel}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default FormDialog;
