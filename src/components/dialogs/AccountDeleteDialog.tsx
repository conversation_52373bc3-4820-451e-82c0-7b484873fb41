"use client"

/**
 * AccountDeleteDialog Component
 * Supports verification code flow and auto-login.
 */

import type React from "react"
import { useState, useCallback, useEffect } from "react"
import NormalDialog from "./NormalDialog"
import { useDispatch } from "react-redux"
import type { AppDispatch } from "@/store/index"
import { fetchLoginInfo, sendVerificationEmailAction } from "@/store/slicers/therapist.slicer"
import { setNewRegistrationState, signIn } from "@/store/slicers/auth.slicer"
import { resetSidebarState } from "@/store/slicers/sidebar.slicer"
import { generateRegFormContent } from "@/utils/app.util"
import { useNavigate } from "react-router-dom"
import { type OverlayTriggerState, useOverlayTriggerState } from "react-stately"
import InputComponent from "../forms/InputComponent"
import notification from "@/utils/notification.util"
import { useApiClient } from "@/utils/api.util"
import {
  TRY_AGAIN,
  VERIFICATION_CODE_DIDNT_RECEIVED,
  VERIFICATION_CODE_INPUT,
  VERIFICATION_CODE,
  ACCOUNT_DELETED,
  ACCOUNT_DELETED_ADMIN,
  CONTACT_US,
  EMAIL_SUPPORT_ADMIN,
  LOGIN_ERROR,
} from "@/pages/auth/constants"

interface AccountDeleteDialogProps {
  underReviewState: OverlayTriggerState
  deletedState: OverlayTriggerState
  incompleteState: OverlayTriggerState
  userId: number | null
  email: string
  password?: string
  isDeletedByAdmin?: boolean
}

const AccountDeleteDialog: React.FC<AccountDeleteDialogProps> = ({
  deletedState,
  userId,
  email,
  password,
  isDeletedByAdmin = false,
}) => {
  const dispatch = useDispatch<AppDispatch>()
  const navigate = useNavigate()
  const verificationCodeState = useOverlayTriggerState({})
  const client = useApiClient()

  const [loading, setLoading] = useState<boolean>(false)
  const [isSendingCode, setIsSendingCode] = useState<boolean>(false)
  const [code, setCode] = useState<string | null>(null)
  const [countdown, setCountdown] = useState<number>(0)

  // Extract countdown logic to a separate effect
  useEffect(() => {
    if (countdown <= 0) return

    const timer = setInterval(() => {
      setCountdown((prevCount) => Math.max(0, prevCount - 1))
    }, 1000)

    return () => clearInterval(timer)
  }, [countdown])

  // Extract verification code sending logic
  const sendVerificationCode = useCallback(async (): Promise<boolean> => {
    if (!email) {
      notification.error("Email is required to send verification code.")
      return false
    }

    setIsSendingCode(true)
    try {
      const res = await dispatch(sendVerificationEmailAction({ email, noLink: true }))
      if (res?.status === 200) {
        setCountdown(60)
        return true
      }
      return false
    } finally {
      setIsSendingCode(false)
    }
  }, [email, dispatch])

  // Extract auto-login logic
  const attemptAutoLogin = useCallback(
    async (userEmail: string, userPassword?: string) => {
      if (!userEmail || !userPassword) return

      try {
        const loginRes = await client.post("/auth/login", {
          email: userEmail,
          password: userPassword,
        })

        if (loginRes?.status === 200) {
          const { accessToken, refreshToken, user, mfaCodeSent } = loginRes.data

          if (accessToken && refreshToken && user) {
            dispatch(signIn({ token: accessToken, refreshToken, user }))
            notification.success("Login successful!")
          } else if (mfaCodeSent) {
            notification.success(loginRes.data.message)
            navigate(`/auth/mfa?email=${userEmail}`)
          }
        }
      } catch (error) {
        notification.error(LOGIN_ERROR)
      }
    },
    [client, dispatch, navigate],
  )

  // Extract registration info fetching logic
  const handleRegistrationInfoFetch = useCallback(async () => {
    if (!userId || !code) return

    setLoading(true)
    try {
      const res = await dispatch(fetchLoginInfo(userId, code))

      if (res?.status === 200) {
        const userRegInfo = res.data
        const newFormContent = generateRegFormContent(userRegInfo)

        dispatch(
          setNewRegistrationState({
            newFormContent,
            useToken: userRegInfo.userToken,
          }),
        )
        dispatch(resetSidebarState())

        if (password) {
          await attemptAutoLogin(email, password)
        } else {
          navigate("/auth")
        }
      }
    } finally {
      setLoading(false)
    }
  }, [userId, code, dispatch, email, password, attemptAutoLogin, navigate])

  // Extract dialog acceptance handler
  const handleDeletedDialogAccept = useCallback(async () => {
    const success = await sendVerificationCode()
    if (success) {
      deletedState.close()
      verificationCodeState.open()
    }
  }, [sendVerificationCode, deletedState, verificationCodeState])

  // Extract resend label rendering
  const renderResendLabel = useCallback(() => {
    if (countdown > 0) {
      return <span className="text-danger font-semibold">Try again in {countdown}s</span>
    }

    if (isSendingCode) {
      return <span className="text-therapy-blue-dark font-semibold">Sending...</span>
    }

    return (
      <span className="text-therapy-blue-dark cursor-pointer font-semibold" onClick={sendVerificationCode}>
        {TRY_AGAIN}
      </span>
    )
  }, [countdown, isSendingCode, sendVerificationCode])

  // Extract admin message content
  const renderAdminMessage = () => (
    <>
      <strong>{ACCOUNT_DELETED_ADMIN}</strong>
      <br />
      <br />
      {CONTACT_US}{" "}
      <a className="underline text-blue-600 hover:text-blue-800" href="mailto:<EMAIL>">
        {EMAIL_SUPPORT_ADMIN}
      </a>
    </>
  )

  // Extract user message content
  const renderUserMessage = () => (
    <>
      <strong>{ACCOUNT_DELETED}</strong>
      <br />
    </>
  )

  return (
    <>
      {/* Deleted Account Dialog */}
      <NormalDialog
        opacity={80}
        state={deletedState}
        title="Account Deleted"
        onClose={() => deletedState.close()}
        width="w-[80vw] sm:w-[300px] md:w-[400px] lg:w-[500px]"
        titleCentered={true}
        onCancel={!isDeletedByAdmin ? () => deletedState.close() : undefined}
        onAccept={!isDeletedByAdmin ? handleDeletedDialogAccept : undefined}
        primaryButtonColor={!isDeletedByAdmin ? true : undefined}
        loading={!isDeletedByAdmin ? isSendingCode : undefined}
        confirmLabel={!isDeletedByAdmin ? "Continue" : undefined}
      >
        <p className="text-xs sm:text-sm">{isDeletedByAdmin ? renderAdminMessage() : renderUserMessage()}</p>
      </NormalDialog>

      {/* Verification Code Dialog */}
      <NormalDialog
        state={verificationCodeState}
        title="Verify your email address"
        confirmLabel="Verify"
        onCancel={() => {
          setCode(null)
          verificationCodeState.close()
        }}
        onAccept={handleRegistrationInfoFetch}
        marginTop={-100}
        primaryButtonColor={true}
        loading={loading}
        btnDisabled={!code || code.length !== 6 || isSendingCode}
      >
        <div>
          <p className="text-sm">
            {VERIFICATION_CODE} <strong className="block">({email})</strong>
            {VERIFICATION_CODE_INPUT}
          </p>

          <InputComponent
            value={code || ""}
            placeholder="Enter verification code"
            onChange={(value) => setCode(value)}
            id="verification_code"
            class="mt-5 mb-5"
            maxLength={6}
          />

          <p>
            {VERIFICATION_CODE_DIDNT_RECEIVED} {!isSendingCode && "You can "}
            {renderResendLabel()}
          </p>
        </div>
      </NormalDialog>
    </>
  )
}

export default AccountDeleteDialog
