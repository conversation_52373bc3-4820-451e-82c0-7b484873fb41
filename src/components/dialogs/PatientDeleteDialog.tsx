import { FormEvent, Fragment, ReactNode, useState, useEffect } from "react";
import { OverlayTriggerState } from "react-stately";
import BaseRepository from "@/repositories/BaseRepository";
import PatientRepository from "@/repositories/PatientRepository";
import { HttpStatusCode } from "axios";
import { Dialog, Transition } from "@headlessui/react";
import { PATIENT_DELETION_SUCCESS } from "../../../src/pages/auth/constants"
interface ReasonGroup {
  id: number;
  heading: string;
  subheadings: {
    id: number;
    subheading: string;
  }[];
}

type PatientDeleteDialogProps = {
  state: OverlayTriggerState;
  children: ReactNode;
  title: string;
  url: string;
  refetch?: () => void;
  setGlobalLoading?: (loading: boolean) => void;
  isDeactivate?: boolean;
};

const PatientDeleteDialog = ({
  state,
  children,
  title,
  url,
  refetch,
  setGlobalLoading,
  isDeactivate = false,
}: PatientDeleteDialogProps) => {
  const repository = new BaseRepository();
  const patientRepo = new PatientRepository();
  
  const [verification, setVerification] = useState("");
  const [processing, setProcessing] = useState(false);
  const [selectedReasonId, setSelectedReasonId] = useState<number | null>(null);
  const [reasonError, setReasonError] = useState("");
  const [selectedHeading, setSelectedHeading] = useState<number | null>(null);
  const [reasonGroups, setReasonGroups] = useState<ReasonGroup[]>([]);

  // Effect to initialize dialog when opened
  useEffect(() => {
    if (state.isOpen) {
      initializeDialog();
    }
  }, [state.isOpen, isDeactivate]);

  // Initialize dialog state
  const initializeDialog = () => {
    fetchReasons();
    resetFormFields();
    setSelectedHeading(null);
    setSelectedReasonId(null);
  };

  // Reset form input fields
  const resetFormFields = () => {
    setVerification("");
    setReasonError("");
  };

  // Reset all state and close dialog
  const resetAndClose = () => {
    setSelectedReasonId(null);
    setReasonError("");
    setVerification("");
    state.close();
  };

  // Fetch reasons from API
  const fetchReasons = async () => {
    try {
      const data = await patientRepo.getDeleteDeactivateReasons();
      handleSuccessfulReasonFetch(data);
    } catch (error) {
      setSelectedReasonId(null);
    }
  };

  // Handle successful reason data fetch
  const handleSuccessfulReasonFetch = (data: any) => {
    const hasValidReasons = data?.reasons?.length > 0;
    if (hasValidReasons) {
      setReasonGroups(data.reasons);
      setSelectedHeading(data.reasons[0].id);
      setSelectedReasonId(data.reasons[0].id);
    }
  };

  // Form submission handler
  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    handleDelete();
  };

  // Handle delete/deactivate action
  const handleDelete = async () => {
    setLoadingState(true);
    try {
      const response = await performAction();
      handleActionSuccess(response);
    } catch (err: any) {
      handleActionError(err);
    } finally {
      setLoadingState(false);
    }
  };

  // Set loading state for UI and global loading
  const setLoadingState = (isLoading: boolean) => {
    setProcessing(isLoading);
    setGlobalLoading?.(isLoading);
  };

  // Build URL with parameters for API call
  const buildActionUrl = () => {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}reasonId=${selectedReasonId}`;
  };
  
  // Perform the delete/deactivate API call
  const performAction = async () => {
    const urlWithParams = buildActionUrl();
    return repository.delete(urlWithParams);
  };

  // Handle successful action response
const handleActionSuccess = (res: any) => {
  const isSuccess = res === true || 
                   res.status === HttpStatusCode.Ok || 
                   res.status === HttpStatusCode.Created;

  if (isSuccess) {
        const successMessage = PATIENT_DELETION_SUCCESS;

    repository.successToast(successMessage);
    resetAndClose();
    refetch?.();
  }
};
  // Handle action error
  const handleActionError = (err: any) => {
    setReasonError(err?.message || "Something went wrong");
  };

  // Check if delete button should be disabled
  const isDeleteButtonDisabled = () => {
    const requiredText = isDeactivate ? "DEACTIVATE" : "DELETE";
    return verification !== requiredText;
  };

  // Handle heading dropdown change
  const handleHeadingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const headingId = Number(e.target.value);
    setSelectedHeading(headingId);
    
    const group = reasonGroups.find((g) => g.id === headingId);
    setSelectedReasonId(group ? group.id : null);
    setReasonError('');
  };

  // Render category dropdown
  const renderCategoryDropdown = () => (
    <div className="mb-4">
      <div>
        <label htmlFor="heading" className="block text-sm font-medium text-gray-700 mb-1 mt-4">
          Category
        </label>
        <select
          id="heading"
          className={`bg-gray-50 border ${reasonError ? 'border-red-500' : 'border-primary'} text-gray-900 rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5`}
          value={selectedHeading !== null ? String(selectedHeading) : ''}
          onChange={handleHeadingChange}
          required
        >
          {reasonGroups.map((group) => (
            <option key={group.id} value={group.id}>
              {group.heading}
            </option>
          ))}
        </select>
      </div>
    </div>
  );

  // Render verification input
  const renderVerificationInput = () => {
    const actionText = isDeactivate ? 'DEACTIVATE' : 'DELETE';
    const actionVerb = isDeactivate ? 'deactivation' : 'deletion';
    
    return (
      <div className="mb-4">
        <div className="text-smDELETE mt-2">
          Type <span className="font-semibold text-xs italic text-red-800">
            {actionText}
          </span> to confirm {actionVerb}.
          <input
            type="text"
            required
            className="bg-gray-50 border border-primary text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5 mt-2"
            value={verification}
            onChange={(e) => setVerification(e.target.value)}
          />
        </div>
      </div>
    );
  };

  // Render action buttons
  const renderActionButtons = () => {
    const actionText = isDeactivate ? 'Deactivate' : 'Delete';
    
    return (
      <div className="mt-4 flex items-center justify-end space-x-2">
        <button
          type="button"
          onClick={resetAndClose}
          className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
        >
          Cancel
        </button>
        <button
          disabled={isDeleteButtonDisabled()}
          type="submit"
          className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-700"
        >
          {processing && (
            <i className="fa fa-spinner animate-spin mr-1"></i>
          )} {actionText}
        </button>
      </div>
    );
  };

  return (
    <Transition.Root show={state.isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={resetAndClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 transition-opacity" />
        </Transition.Child>
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="relative w-full max-w-[38rem] transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                  {title}
                </Dialog.Title>
                <form onSubmit={onSubmit} className="mt-4">
                  {children}
                  {renderCategoryDropdown()}
                  {reasonError && (
                    <p className="text-red-500 text-xs mt-1">{reasonError}</p>
                  )}
                  {renderVerificationInput()}
                  {renderActionButtons()}
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default PatientDeleteDialog;