import React, { useEffect, useRef, useState } from "react";
import ReactCrop, {
  centerCrop,
  makeAspectCrop,
  Crop,
  PixelCrop,
} from "react-image-crop";
import 'react-image-crop/src/ReactCrop.scss';
import NormalDialog from "@/components/dialogs/NormalDialog";
import { OverlayTriggerState } from "react-stately";

type ImageCropDialogProps = {
	selectedFile: File | null;
	imageCropState: OverlayTriggerState;
	aspectRatio?: number;
	isCircular?: boolean;
	onCropComplete?: (file: File) => void;
};

const ASPECT_RATIO = 2 / 1;

const setCanvasPreview = (
  image: HTMLImageElement,
  canvas: HTMLCanvasElement,
  crop: PixelCrop
) => {
  const ctx = canvas.getContext("2d");
  if (!ctx) throw new Error("No 2d context");

  const pixelRatio = window.devicePixelRatio;
  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;

  canvas.width = Math.floor(crop.width * scaleX * pixelRatio);
  canvas.height = Math.floor(crop.height * scaleY * pixelRatio);

  ctx.scale(pixelRatio, pixelRatio);
  ctx.imageSmoothingQuality = "high";
  ctx.save();

  const cropX = crop.x * scaleX;
  const cropY = crop.y * scaleY;

	const cropWidth = crop.width * scaleX
  const cropHeight = crop.height * scaleY

  ctx.drawImage(
    image,
    cropX,
		cropY,
		cropWidth,
		cropHeight,
    0,
		0,
		cropWidth,
		cropHeight
  );

	ctx.restore();
};

const ImageCropDialog: React.FC<ImageCropDialogProps> = ({
  selectedFile,
  imageCropState,
  aspectRatio = ASPECT_RATIO,
  isCircular = false,
  onCropComplete
}) => {
  const imgRef = useRef<HTMLImageElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const [imageDataUrl, setImageDataUrl] = useState<string>("");
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();

  useEffect(() => {
    if (!selectedFile) return;

    const reader = new FileReader();
    reader.onload = () => {
      const imageUrl = reader.result?.toString() || "";
      const img = new Image();
      img.src = imageUrl;
      img.onload = () => {
        setImageDataUrl(imageUrl);
      };
    };
    reader.readAsDataURL(selectedFile);
  }, [selectedFile]);

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;

    const iCrop = makeAspectCrop({ unit: "%", width: 90 }, aspectRatio, width, height);
    const centeredCrop = centerCrop(iCrop, width, height);

    setCrop(centeredCrop);
    setCompletedCrop({
      unit: "px",
      x: (centeredCrop.x / 100) * width,
      y: (centeredCrop.y / 100) * height,
      width: (centeredCrop.width / 100) * width,
      height: (centeredCrop.height / 100) * height,
    });
  };

  const confirmCrop = () => {
    if (previewCanvasRef.current) {
      previewCanvasRef.current.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], selectedFile?.name || "cropped_image.png", {
            type: selectedFile?.type || "image/png",
          });
          onCropComplete?.(file);
          imageCropState.close();
        }
      }, selectedFile?.type, 1);
    }
  };

  useEffect(() => {
    if (completedCrop?.width && completedCrop?.height) {
      const timeout = setTimeout(() => {
        if (imgRef.current && previewCanvasRef.current) {
          setCanvasPreview(imgRef.current, previewCanvasRef.current, completedCrop);
        }
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [completedCrop]);

  return (
    <NormalDialog
      opacity={80}
      state={imageCropState}
      title="Crop Image"
      width="w-[500px] max-w-[95vw]"
      titleCentered
			onAccept={confirmCrop}
			onCancel={() => imageCropState.close()}
			confirmLabel="Crop"
			primaryButtonColor
    >
      {imageDataUrl && (
        <div className="image-container">
          <ReactCrop
            crop={crop}
            onChange={(_, percentCrop) => setCrop(percentCrop)}
            onComplete={(c) => setCompletedCrop(c)}
            aspect={aspectRatio}
            circularCrop={isCircular}
          >
            <img
              ref={imgRef}
              src={imageDataUrl}
              alt="Selected"
              onLoad={onImageLoad}
              style={{ width: "100%" }}
            />
          </ReactCrop>
        </div>
      )}

      <canvas
        ref={previewCanvasRef}
        style={{ display: "none" }}
      />
    </NormalDialog>
  );
};

export default ImageCropDialog;
