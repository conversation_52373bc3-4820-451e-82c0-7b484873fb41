import { Dialog, Transition } from "@headlessui/react";
import { Fragment, ReactNode, useState } from "react";
import DialogButtons from "../common/DialogButtons";
import VerifyInput from "../common/VerifyInput";

interface NormalDialogV2Props {
  children: ReactNode;
  title?: string;
  confirmLabel?: string;
  onCancel?: (() => void) | null;
  onClose?: (() => void) | null;
  onAccept?: () => void;
  loading?: boolean;
  open: boolean;
  contentCentered?: boolean;
  opacity?: number;
  primaryButtonColor?: boolean;
  marginTop?: number;
  verifyText?: string;
  btnDisabled?: boolean;
  width?: string;
  titleCentered?: boolean;
}

const NormalDialogV2 = ({
  onCancel = null,
  onAccept,
  children,
  title,
  confirmLabel,
  loading,
  open,
  contentCentered = false,
  titleCentered = false,
  opacity = 25,
  primaryButtonColor = false,
  onClose,
  marginTop = 0,
  verifyText,
  width,
  btnDisabled,
}: NormalDialogV2Props) => {
  const [verification, setVerification] = useState("");

  return (
    <Transition appear show={open} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10 app-dialogue-modal"
        onClose={() => {
          onClose?.();
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className={`fixed inset-0 bg-black bg-opacity-${opacity}`} />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div style={{ marginTop: marginTop }} className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className={`${width ?? "w-[500px]"} transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all`}>
                {title && <Dialog.Title
                  as="h3"
                  className={`text-lg font-bold leading-6 text-gray-900 ${titleCentered ? "text-center" : ""}`}
                >
                  {title}
                </Dialog.Title>}
                {onClose && (
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-gray-500 hover:text-gray-700 focus:outline-none closable-btn"
                  >
                    <i className="fa fa-times"></i>
                  </button>
                )}

                <div className={`mt-4 ${contentCentered ? "text-center" : ""}`}>{children}</div>

                <VerifyInput
                  verifyText={verifyText}
                  verification={verification}
                  onChange={(e) => setVerification(e.target.value)}
                />

                <DialogButtons
                  onCancel={onCancel}
                  onAccept={onAccept}
                  loading={loading}
                  confirmLabel={confirmLabel}
                  disabled={verifyText ? verification !== verifyText : false}
                  btnDisabled={btnDisabled}
                  contentCentered={contentCentered}
                  primaryButtonColor={primaryButtonColor}
                />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default NormalDialogV2;
