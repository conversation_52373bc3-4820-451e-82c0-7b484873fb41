import type React from "react"
import { type Form<PERSON><PERSON>, Fragment, type ReactNode, useEffect, useReducer } from "react"
import type { OverlayTriggerState } from "react-stately"
import BaseRepository from "@/repositories/BaseRepository"
import PatientRepository from "@/repositories/PatientRepository"
import { HttpStatusCode } from "axios"
import { Dialog, Transition } from "@headlessui/react"
import { PATIENT_DEACTIVATION_SUCCESS } from "../../../src/pages/auth/constants"
interface ReasonGroup {
  id: number
  heading: string
  subheadings: {
    id: number
    subheading: string
  }[]
}

type PatientDeactivateDialogProps = {
  state: OverlayTriggerState
  children: ReactNode
  title: string
  url: string
  refetch?: () => void
  setGlobalLoading?: (loading: boolean) => void
}

type State = {
  processing: boolean
  reasonGroups: ReasonGroup[]
  selectedReason: {
    headingId: number | null
    reasonId: number | null
    reasonText: string
  }
  verification: string
  error: string
}

type Action =
  | { type: "SET_PROCESSING"; payload: boolean }
  | { type: "SET_REASON_GROUPS"; payload: ReasonGroup[] }
  | { type: "SET_HEADING"; payload: number }
  | { type: "SET_REASON"; payload: { id: number; text: string } }
  | { type: "SET_VERIFICATION"; payload: string }
  | { type: "SET_ERROR"; payload: string }
  | { type: "RESET_FORM" }

const initialState: State = {
  processing: false,
  reasonGroups: [],
  selectedReason: {
    headingId: null,
    reasonId: null,
    reasonText: "",
  },
  verification: "",
  error: "",
}

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case "SET_PROCESSING":
      return { ...state, processing: action.payload }
    case "SET_REASON_GROUPS":
      return { ...state, reasonGroups: action.payload }
    case "SET_HEADING":
      return {
        ...state,
        selectedReason: {
          ...state.selectedReason,
          headingId: action.payload,
        },
        error: "",
      }
    case "SET_REASON":
      return {
        ...state,
        selectedReason: {
          ...state.selectedReason,
          reasonId: action.payload.id,
          reasonText: action.payload.text,
        },
        error: "",
      }
    case "SET_VERIFICATION":
      return { ...state, verification: action.payload }
    case "SET_ERROR":
      return { ...state, error: action.payload }
    case "RESET_FORM":
      return {
        ...initialState,
        reasonGroups: [], // Clear reason groups when resetting
      }
    default:
      return state
  }
}

const PatientDeactivateDialog = ({
  state,
  children,
  title,
  url,
  refetch,
  setGlobalLoading,
}: PatientDeactivateDialogProps) => {
  const repository = new BaseRepository()
  const patientRepo = new PatientRepository()
  const [dialogState, dispatch] = useReducer(reducer, initialState)

  useEffect(() => {
    if (state.isOpen) {
      fetchReasons()
    }
  }, [state.isOpen])

  const fetchReasons = async () => {
    try {
      const data = await patientRepo.getDeleteDeactivateReasons()
      if (data?.reasons?.length > 0) {
        dispatch({ type: "SET_REASON_GROUPS", payload: data.reasons })

        // Set default selections
        const firstGroup = data.reasons[0]
        dispatch({ type: "SET_HEADING", payload: firstGroup.id })

        if (firstGroup.subheadings?.length > 0) {
          const firstSubheading = firstGroup.subheadings[0]
          dispatch({
            type: "SET_REASON",
            payload: {
              id: firstSubheading.id,
              text: firstSubheading.subheading,
            },
          })
        } else {
          dispatch({
            type: "SET_REASON",
            payload: {
              id: firstGroup.id,
              text: firstGroup.heading,
            },
          })
        }
      }
    } catch (error) {
      dispatch({ type: "SET_ERROR", payload: "Failed to load reasons" })
    }
  }

  const resetAndClose = () => {
    dispatch({ type: "RESET_FORM" })
    state.close()
  }

  const onSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    handleDeactivate()
  }

  const handleDeactivate = async () => {
    // Validate form
    if (!dialogState.selectedReason.reasonId) {
      dispatch({ type: "SET_ERROR", payload: "Please select a reason" })
      return
    }

    if (dialogState.verification !== "DEACTIVATE") {
      dispatch({ type: "SET_ERROR", payload: "Please type DEACTIVATE to confirm" })
      return
    }

    // Set loading state
    dispatch({ type: "SET_PROCESSING", payload: true })
    if (setGlobalLoading) {
      setGlobalLoading(true)
    }

    try {
      // Build URL with reason parameters
      const separator = url.includes("?") ? "&" : "?"
      const actionUrl = `${url}${separator}reason=${encodeURIComponent(dialogState.selectedReason.reasonText)}&reasonId=${dialogState.selectedReason.reasonId}`

      // Perform API call
      const response = await repository.patch(actionUrl, {})

      // Handle success
    if (response.status === HttpStatusCode.Ok || response.status === HttpStatusCode.Created) {
        repository.successToast(PATIENT_DEACTIVATION_SUCCESS);
        resetAndClose();
        if (refetch) {
          refetch();
        }
      }
    } catch (err: any) {
      dispatch({ type: "SET_ERROR", payload: err?.message || "Something went wrong" })
    } finally {
      dispatch({ type: "SET_PROCESSING", payload: false })
      if (setGlobalLoading) {
        setGlobalLoading(false)
      }
    }
  }

  const handleHeadingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const headingId = Number(e.target.value)
    dispatch({ type: "SET_HEADING", payload: headingId })

    const group = dialogState.reasonGroups.find((g) => g.id === headingId)
    if (group) {
      if (group.subheadings?.length > 0) {
        const firstSubheading = group.subheadings[0]
        dispatch({
          type: "SET_REASON",
          payload: {
            id: firstSubheading.id,
            text: firstSubheading.subheading,
          },
        })
      } else {
        dispatch({
          type: "SET_REASON",
          payload: {
            id: group.id,
            text: group.heading,
          },
        })
      }
    }
  }

  const handleSubheadingChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const subheadingId = Number(e.target.value)

    const group = dialogState.reasonGroups.find((g) => g.id === dialogState.selectedReason.headingId)
    const subheading = group?.subheadings.find((sh) => sh.id === subheadingId)

    if (subheading) {
      dispatch({
        type: "SET_REASON",
        payload: {
          id: subheadingId,
          text: subheading.subheading,
        },
      })
    }
  }

  const getSubheadingsForSelectedHeading = () => {
    const group = dialogState.reasonGroups.find((g) => g.id === dialogState.selectedReason.headingId)
    return group?.subheadings || []
  }

  const isDeactivateButtonDisabled = () => {
    return dialogState.verification !== "DEACTIVATE" || !dialogState.selectedReason.reasonId
  }

  return (
    <Transition.Root show={state.isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={resetAndClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25 transition-opacity" />
        </Transition.Child>
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="relative w-full max-w-[38rem] transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                  {title}
                </Dialog.Title>
                <form onSubmit={onSubmit} className="mt-4">
                  {children}
                  <div className="mb-4">
                    {/* Category dropdown */}
                    <div>
                      <label htmlFor="heading" className="block text-sm font-medium text-gray-700 mb-1 mt-4">
                        Category
                      </label>
                      <select
                        id="heading"
                        className={`bg-gray-50 border ${dialogState.error ? "border-red-500" : "border-primary"} text-gray-900 rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5`}
                        value={
                          dialogState.selectedReason.headingId !== null
                            ? String(dialogState.selectedReason.headingId)
                            : ""
                        }
                        onChange={handleHeadingChange}
                        required
                      >
                        {dialogState.reasonGroups.length === 0 && <option value="">Loading...</option>}
                        {dialogState.reasonGroups.map((group) => (
                          <option key={group.id} value={group.id}>
                            {group.heading}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Subheading dropdown - only show if there are subheadings */}
                    {getSubheadingsForSelectedHeading().length > 0 && (
                      <div className="mt-2">
                        <label htmlFor="subheading" className="block text-sm font-medium text-gray-700 mb-1">
                          Specific Reason
                        </label>
                        <select
                          id="subheading"
                          className={`bg-gray-50 border ${dialogState.error ? "border-red-500" : "border-primary"} text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5`}
                          value={
                            dialogState.selectedReason.reasonId !== null
                              ? String(dialogState.selectedReason.reasonId)
                              : ""
                          }
                          onChange={handleSubheadingChange}
                        >
                          {getSubheadingsForSelectedHeading().map((subheading) => (
                            <option key={subheading.id} value={subheading.id}>
                              {subheading.subheading}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                  </div>

                  {dialogState.error && <p className="text-red-500 text-xs mt-1">{dialogState.error}</p>}

                  <div className="mb-4">
                    <div className="text-sm mt-2">
                      Type <span className="font-semibold text-xs italic text-red-800">DEACTIVATE </span>to confirm
                      deactivation.
                      <input
                        type="text"
                        required
                        className="bg-gray-50 border border-primary text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-1.5 mt-2"
                        value={dialogState.verification}
                        onChange={(e) => dispatch({ type: "SET_VERIFICATION", payload: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-end space-x-2">
                    <button
                      type="button"
                      onClick={resetAndClose}
                      className="inline-flex justify-center rounded-md border border-transparent bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
                    >
                      Cancel
                    </button>
                    <button
                      disabled={isDeactivateButtonDisabled()}
                      type="submit"
                      className="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-red-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-700"
                    >
                      {dialogState.processing && <i className="fa fa-spinner animate-spin mr-1"></i>} Deactivate
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}

export default PatientDeactivateDialog
