import { Menu, Transition } from "@headlessui/react";
import { Fragment } from "react";

type ActionButtonProps = {
  onEdit?: () => void;
  onDelete?: () => void;
  onDeactivate?: () => void;
  onActivate?: () => void;
  onViewProfile?: () => void;
  onResetPassword?: () => void;
  onAccept?: () => void;
  onReject?: () => void;
  onRejectedReason?: () => void;
  onViewHistory?: () => void;
  onRestore?: () => void;
  onClick?: () => void;
  children?: React.ReactNode;
  variant?: string;
  size?: string;
  isResetPasswordDisabled?: boolean;
  acceptLabel?: string;
};

const ActionButton = ({ 
  onEdit, 
  onDelete, 
  onActivate, 
  onDeactivate, 
  onViewProfile, 
  onResetPassword, 
  onAccept, 
  onReject,
  onRejectedReason,
  onViewHistory,
  onRestore,
  onClick,
  children,
  variant,
  size,
  isResetPasswordDisabled,
  acceptLabel = "Accept"
}: ActionButtonProps) => {
  
  // Helper function to render menu item - reduces repetitive code
  const renderMenuItem = (
    handler: (() => void) | undefined,
    icon: string,
    label: string,
    testId: string,
    disabled = false
  ) => {
    if (!handler) return null;
    
    return (
      <Menu.Item>
        {({ active }) => (
          <button
            onClick={handler}
            data-testid={testId}
            disabled={disabled}
            className={`${
              active ? "bg-primary text-white" : "text-gray-900"
            } group flex w-full items-center rounded-md px-2 py-2 text-xs`}
          >
            <i className={icon}></i>
            {label}
          </button>
        )}
      </Menu.Item>
    );
  };

  // If onClick is provided, render a simple button
  if (onClick) {
    return (
      <button
        onClick={onClick}
        className={`inline-flex items-center justify-center rounded-md px-3 py-1.5 text-sm font-medium focus:outline-none ${
          variant === 'danger' 
            ? 'bg-red-600 text-white hover:bg-red-700' 
            : 'bg-blue-600 text-white hover:bg-blue-700'
        } ${size === 'sm' ? 'text-xs px-2 py-1' : ''}`}
      >
        {children}
      </button>
    );
  }

  // Original menu button implementation
  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <Menu.Button className="inline-flex w-full justify-center rounded-md px-4 py-1 text-sm font-medium focus:outline-none text-black">
          <i className="fad fa-cogs"></i>
        </Menu.Button>
      </div>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items className="absolute z-10 right-0 mb-2 w-36 origin-top-right divide-y divide-gray-100 rounded-md bg-secondary shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="px-1 py-1">
            {renderMenuItem(onEdit, "fad fa-edit mr-2", "Edit", "edit-button")}
            {renderMenuItem(onViewProfile, "fad fa-user mr-2", "View Profile", "view-profile-button")}
            {renderMenuItem(onAccept, "fa-solid fa-check mr-2", acceptLabel, "accept-button")}
            {renderMenuItem(onReject, "fa-solid fa-times mr-2", "Reject", "reject-button")}
            {renderMenuItem(onRejectedReason, "fad fa-ban mr-2 -ml-0.5", "Rejected Reasons", "rejected-reason-button")}
            {renderMenuItem(onViewHistory, "fad fa-ban mr-2 -ml-0.5", "View History", "view-history-button")}
            {renderMenuItem(onRestore, "fad fa-undo mr-2", "Restore", "restore-button")}
            {renderMenuItem(onDelete, "fad fa-trash mr-2", "Delete", "delete-button")}
            {renderMenuItem(onDeactivate, "fad fa-lock mr-2", "Deactivate", "deactivate-button")}
            {renderMenuItem(onActivate, "fad fa-unlock mr-2", "Reactivate", "activate-button")}
            {renderMenuItem(onResetPassword, "fad fa-key mr-2", "Reset Password", "reset-password-button", isResetPasswordDisabled)}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export default ActionButton;