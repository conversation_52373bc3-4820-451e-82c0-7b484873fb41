import { useRef } from "react";
import { AriaButtonProps, useButton } from "react-aria";

type ButtonProps = {
  label: string;
} & AriaButtonProps;

const DeleteButton = (props: ButtonProps) => {
  const ref = useRef(null);
  const { buttonProps } = useButton(props, ref);

  return (
    <>
      <button
        {...buttonProps}
        ref={ref}
        className="text-black border rounded border-black px-2 active:scale-95 transition cursor-pointer text-xs"
      >
        <i className="fa-duotone fa-trash fa-sm"></i> {props.label}
      </button>
    </>
  );
};

export default DeleteButton;
