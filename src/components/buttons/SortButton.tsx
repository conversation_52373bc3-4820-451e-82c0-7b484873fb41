import { useRef } from "react";
import { AriaButtonProps, useButton } from "react-aria";

type ButtonProps = {
  label: string;
  active?: boolean;
} & AriaButtonProps;

const SortButton = (props: ButtonProps) => {
  const ref = useRef(null);
  const { buttonProps } = useButton(props, ref);

  return (
    <button
      {...buttonProps}
      ref={ref}
      className={`border rounded-lg py-1 px-2 active:scale-95 transition cursor-pointer text-sm ${
        props.active
          ? "bg-quaternary text-white border-quaternary"
          : "text-quaternary hover:text-white hover:bg-quaternary/80 border-quaternary"
      }`}
    >
      <i className="fa-duotone fa-sort fa-sm"></i> {props.label}
    </button>
  );
};

export default SortButton;
