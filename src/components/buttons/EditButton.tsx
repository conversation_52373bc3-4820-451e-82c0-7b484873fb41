import { useRef } from "react";
import { AriaButtonProps, useButton } from "react-aria";

type ButtonProps = {
  label: string;
} & AriaButtonProps;

const EditButton = (props: ButtonProps) => {
  const ref = useRef(null);
  const { buttonProps } = useButton(props, ref);

  return (
    <button
      {...buttonProps}
      ref={ref}
      className="text-quaternary border rounded border-quaternary px-2 active:scale-95 transition cursor-pointer text-xs"
    >
      <i className="fa-duotone fa-edit fa-sm"></i> {props.label}
    </button>
  );
};

export default EditButton;
