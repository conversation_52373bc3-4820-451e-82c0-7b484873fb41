type AppButtonProps = {
  onClick?: (e: any) => void;
  value: string;
  disabled?: boolean;
  width?: any;
  type?: 'submit' | 'button' | 'reset';
  loading?: boolean;
};

const AppButton = ({ onClick, value, disabled = false, width = 350, type = 'submit', loading }: AppButtonProps) => {
  return (
    <button
      type={type}
      disabled={disabled || loading}
      onClick={onClick}
      className={`bg-therapy-blue-dark px-6 inline-block text-center w-[${width}px] py-3.5 rounded-full text-white text-sm disabled`}
    >
      {loading && <i className="fa fa-spinner animate-spin mr-1"></i>} {value}
    </button>
  );
};

export default AppButton;
