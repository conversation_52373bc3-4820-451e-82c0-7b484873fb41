import { useEffect, useState } from "react";
 
type SaveButtonProps = {
  onClick?: (e: any) => void;
  value: string;
  disabled?: boolean;
  type?: 'submit' | 'button' | 'reset';
  loading?: boolean;
  className?: string;
};
 
const SaveButton = ({
  onClick,
  value,
  disabled = false,
  type = 'submit',
  loading,
  className = "",
}: SaveButtonProps) => {
  const [width, setWidth] = useState(350);
 
  useEffect(() => {
    const handleResize = () => {
        if (window.innerWidth === 320) {
            setWidth(300);
        }
        else if (window.innerWidth <= 280) {
            setWidth(250);
        }
    
        
        else {
        setWidth(350);
      }
    };
 
    window.addEventListener("resize", handleResize);
 
    handleResize();
 
    return () => window.removeEventListener("resize", handleResize);
  }, []);
 
  return (
    <button
      type={type}
      disabled={disabled || loading}
      onClick={onClick}
      className={`bg-therapy-blue-dark px-6 inline-block text-center w-[${width}px] py-3.5 rounded-full text-white text-sm disabled ${className}`}
    >
      {loading && <i className="fa fa-spinner animate-spin mr-1"></i>} {value}
    </button>
  );
};
 
export default SaveButton;
 
 