import { useRef } from "react";
import { AriaButtonProps, useButton } from "react-aria";

type ButtonProps = {
  label: string;
} & AriaButtonProps;

const AddButton = (props: ButtonProps) => {
  const ref = useRef(null);
  const { buttonProps } = useButton(props, ref);

  return (
    <button
      {...buttonProps}
      ref={ref}
      className="text-white border rounded-lg py-1.5 bg-neutral-500 px-2 active:scale-95 transition cursor-pointer text-sm"
    >
      <i className="fa-duotone fa-plus-circle fa-sm"></i> {props.label}
    </button>
  );
};

export default AddButton;
