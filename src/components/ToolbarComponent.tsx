import { FC } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { getAllConditionalPageIds, getMainPageId, getPreviousPageId, updateConditionalPageData } from "@/utils/storage.util";
import { AppDispatch } from "../store";
import { getPatientRegistrationPage } from "@/store/slicers/auth.slicer";
import { useAppSelector } from "@/store/hooks";

const ToolbarComponent: FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const auth = useAppSelector((state) => state.auth);
  const editFlag = useAppSelector((state) => state.auth.registration.editFlag);
  const navigate = useNavigate();
  const location = window.location;

  const getQueryParam = (key: string) => new URLSearchParams(location.search).get(key);

  const isOnPatientPage = () => location.pathname.endsWith("patient");

  const handleConditionalPage = (pageId: number) => {
    if (getAllConditionalPageIds().includes(pageId)) {
      updateConditionalPageData(pageId, true);
    }
  };

  const dispatchPreviousPage = async (
    prevPageId: number,
    category: string | null,
    prevQuestionId?: number,
    prevSelectedAnswer?: number
  ) => {
    await dispatch(
      getPatientRegistrationPage<{ pageId: number; category: string; prevQuestionId?: number; prevSelectedAnswer?: number }>({
        pageId: prevPageId,
        category,
        prevQuestionId,
        prevSelectedAnswer,
      })
    );
  };

  const handleBack = async () => {
    if (!isOnPatientPage()) {
      return navigate(-1);
    }

    const currentPageId = Number(getQueryParam("currentPage"));
    const category = getQueryParam("category");

    if (!currentPageId) return navigate(-1);

    const prevPageId = getPreviousPageId(currentPageId);
    if (!prevPageId) return navigate(-1);

    handleConditionalPage(currentPageId);

    // check if the previous page is a follow through page
    // if we get the main page id, then it is a follow through page
    const mainPageId = getMainPageId(prevPageId);
    if (mainPageId) {
      const prevSelectedAnswer = auth.registration.patientAnswers[mainPageId]?.answers?.id;
      // get previous page of the previous page
      const secondPrevPageId = getPreviousPageId(prevPageId);
      const prevQuestionId = secondPrevPageId
        ? auth.registration.patientAnswers[secondPrevPageId]?.page?.questionnaireId
        : undefined;

      return dispatchPreviousPage(prevPageId, category, prevQuestionId, prevSelectedAnswer);
    }

    return dispatchPreviousPage(prevPageId, category);
  };

  return (
    <div
      style={{ borderBottom: "1px solid #ccc" }}
      className="cursor-pointer flex-none w-full h-16 flex flex-row items-center px-8 text-white gap-4"
    >
      <span className="flex flex-row gap-5 items-center" onClick={handleBack}>
        <span>
          <i style={{ color: "#888", fontSize: 22 }} className="fa fa-chevron-left text-white"></i>
        </span>
        <span style={{ color: "#555", fontWeight: 500, fontSize: 18 }} className="font-thin">
          {editFlag ? "Back" : "Previous"}
        </span>
      </span>
    </div>
  );
};

export default ToolbarComponent;
