import dayjs from "dayjs";

const VerificationDateComponent = (verifiedAt: Date | undefined) => {
  return (
    <>
      {verifiedAt ? (
        <div
          className="tooltip text-green-800 ml-1 cursor-pointer tooltip-info"
          data-tip={dayjs.utc(verifiedAt).format("lll")}
        >
          <i className="fa-duotone fa-shield-check fa-sm"></i>
        </div>
      ) : (
        <div className="ml-1 text-red-700">
          <i className="fa-duotone fa-shield-xmark fa-sm"></i>
        </div>
      )}
    </>
  );
};

export default VerificationDateComponent;
