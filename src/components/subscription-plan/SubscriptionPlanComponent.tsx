"use client"

import type React from "react"

import type { SubscriptionPlan } from "@/types/subscription-plan.interface"
import AppButton from "../buttons/AppButton"
import type { TherapistSubscription } from "@/types/therapist-subscription.interface"

type SubscriptionPlanProps = {
  plan: TherapistSubscription | null
  setPlanType?: React.Dispatch<React.SetStateAction<string>>
  subscriptionPlan?: SubscriptionPlan | null
  onCLick?: () => void
}

const SubscriptionPlanComponent: React.FC<SubscriptionPlanProps> = ({
  plan,
  setPlanType,
  subscriptionPlan,
  onCLick,
}) => {
  return (
    <div className="flex flex-col md:flex-row justify-around mt-8 w-[800px] max-w-full mx-auto px-4 gap-6 md:gap-4">
      <div
        className={`bg-white p-6 rounded-lg border-2 flex flex-col items-center w-full md:w-[350px] transition duration-100 ease-in-out
					${plan?.subscriptionType === "monthly" ? "border-therapy-blue shadow-lg transform scale-105" : "border-gray-300"}`}
      >
        <h2 className="text-3xl font-medium text-therapy-blue mb-4 font-weight-bold">Monthly Plan
        {plan?.subscriptionType === "monthly" &&
          (
            <i className="fa-solid fa-circle-check" style={{ color: "#43a047" , fontSize:"1.25rem"}} />
          )}

        </h2>
        <p className="text-md mb-2 text-therapy-blue font-bold">$ {subscriptionPlan?.monthlyPrice}</p>
        <p className="text-md font-normal mb-2 text-therapy-blue">per month</p>
        <br />
        <AppButton
          width={250}
          onClick={() => {
            setPlanType && setPlanType("monthly")
            onCLick && onCLick()
          }}
          value={plan?.subscriptionType === "monthly" ? "Selected" : "Select"}
          disabled={plan?.subscriptionType === "monthly"}
        />
      </div>

      <div
        className={`bg-white p-6 rounded-lg border-2 flex flex-col items-center w-full md:w-[350px] transition duration-100 ease-in-out
					${plan?.subscriptionType === "yearly" ? "border-therapy-blue shadow-lg transform scale-105" : "border-gray-300"}`}
      >
        <h2 className="text-3xl font-medium text-therapy-blue mb-4">Annual Plan</h2>
        <p className="text-md mb-2 text-therapy-blue font-bold">$ {subscriptionPlan?.annualPrice}</p>
        <p className="text-md font-normal mb-2 text-therapy-blue">per year</p>
        <br />
        <AppButton
          width={250}
          onClick={() => {
            setPlanType && setPlanType("yearly")
            onCLick && onCLick()
          }}
          value={plan?.subscriptionType === "yearly" ? "Selected" : "Select"}
          disabled={plan?.subscriptionType === "yearly"}
        />
      </div>
    </div>
  )
}

export default SubscriptionPlanComponent

