import React from 'react';

interface SkeletonProps {
  width?: string;
  height?: string;
  className?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({ 
  width = '100%', 
  height = '1rem',
  className = ''
}) => {
  return (
    <div 
      data-testid="skeleton"
      className={`animate-pulse bg-gray-200 rounded ${className}`}
      style={{ width, height }}
    />
  );
};

interface TherapistRowSkeletonProps {
  count?: number;
}

export const TherapistRowSkeleton: React.FC<TherapistRowSkeletonProps> = ({ count = 5 }) => {
  return (
    <>
      {Array(count).fill(0).map((_, index) => (
        <tr key={index} className="border-t gap-2 text-sm py-3 px-2 transition duration-200 ease-in-out">
          <td className="px-4 py-3">
            <div className="w-8 h-8 rounded-full flex items-center justify-center bg-gray-200 animate-pulse"></div>
          </td>
          <td className="px-4 py-3">
            <div className="space-y-2">
              <Skeleton height="1.25rem" width="150px" className="mb-1" />
              <Skeleton height="0.75rem" width="200px" />
            </div>
          </td>
          <td className="px-4 py-3">
            <Skeleton height="1rem" width="120px" />
          </td>
          <td className="px-4 py-3">
            <Skeleton height="1rem" width="150px" />
          </td>
          <td className="px-4 py-3">
            <Skeleton height="1rem" width="180px" />
          </td>
          <td className="px-4 py-3">
            <Skeleton height="1rem" width="160px" />
          </td>
          <td className="px-4 py-3">
            <div className="flex space-x-2">
              <Skeleton height="2rem" width="2rem" className="rounded-md" />
              <Skeleton height="2rem" width="2rem" className="rounded-md" />
            </div>
          </td>
        </tr>
      ))}
    </>
  );
};
