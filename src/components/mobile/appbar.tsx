import { useMemo } from "react";

type Props = {
  backgroundColor?: string;
  title?: string;
  type?: any;
};

const AppBar = ({ backgroundColor, title, type = "default" }: Props) => {
  return useMemo(() => {
    if (type === "default") {
      return (
        <div
        role="presentation-new"
          className={`titlebar text-white flex flex-row items-center px-2 h-10 ${
            backgroundColor ?? "bg-therapy-blue"
          }`}
        >
          <span className="w-4 flex-none">
            <i className="fa-light fa-chevron-left" />
          </span>
          {title && <span className="-ml-4 flex-grow text-center">{title}</span>}
        </div>
      );
    }

    if (type === "info") {
      return (
        <div className="titlebar text-white flex flex-row items-center px-2 h-10 bg-white">
          <span className="w-4 flex-none text-black h-full mt-8 flex items-center">
            <i className="fa-light fa-chevron-left" />
          </span>
          <span className="-ml-4 flex-grow flex items-center justify-center mt-8 py-4">
            <img src="/logo-sm.min.svg" alt="logo" className="h-[50px]" />
          </span>
        </div>
      );
    }

    if (type === "questionnaire") {
      return (
        <div
          role="presentation-new"
          className={`titlebar text-white flex flex-row items-center px-2 h-10 ${
            backgroundColor ?? "bg-therapy-blue"
          }`}
        >
          <span className="w-4 flex-none">
            <i className="fa-light fa-chevron-left" />
          </span>
        </div>
      );
    }

    return (
      <div
        role="presentation-new"
        className={`titlebar text-white flex flex-row items-center px-2 h-10 ${
          backgroundColor ?? "bg-therapy-blue"
        }`}
      >
        <span className="w-4 flex-none">
          <i className="fa-light fa-chevron-left" />
        </span>
      </div>
    );
  }, [backgroundColor, title, type]);
};

export default AppBar;
