import PerfectScrollbar from "react-perfect-scrollbar";
import StatusBar from "./statusbar";
import { ReactNode } from "react";
import AppBar from "./appbar";

type DeviceProps = {
  children: ReactNode;
  statusBarColor?: string;
  title?: string;
  type?: any;
};

const Device = ({ title, type, children, statusBarColor }: DeviceProps) => {
  return (
    <div className="relative scale-[.9] mx-auto border-gray-800 dark:border-gray-800 bg-gray-800 border-[14px] rounded-[2.5rem] h-[600px] w-[300px] shadow-xl">
      <div className="w-[148px] h-[18px] bg-gray-800 top-0 rounded-b-[1rem] left-1/2 -translate-x-1/2 absolute"></div>
      <div className="h-[46px] w-[3px] bg-gray-800 absolute -start-[17px] top-[124px] rounded-s-lg"></div>
      <div className="h-[46px] w-[3px] bg-gray-800 absolute -start-[17px] top-[178px] rounded-s-lg"></div>
      <div className="h-[64px] w-[3px] bg-gray-800 absolute -end-[17px] top-[142px] rounded-e-lg"></div>
      <div className="rounded-[2rem] overflow-hidden w-[272px] h-[572px] bg-white flex flex-col">
        <StatusBar
          backgroundColor={`${
            statusBarColor ?? type === "info" ? "bg-white" : "bg-therapy-blue"
          }`}
        />
        <AppBar title={title} type={type} />
        <PerfectScrollbar
          className="-mr-[2px]"
          style={{
            overflowX: "hidden",
          }}
        >
          <div className="px-4 py-3 muller">{children}</div>
        </PerfectScrollbar>
      </div>
    </div>
  );
};

export default Device;
