import {
  ReactNode,
  Suspense,
  createContext,
  useCallback,
  useMemo,
  useState,
} from "react";

export type FallbackType = NonNullable<ReactNode> | null;

export interface FallbackContextType {
  updateFallback: (fallback: FallbackType) => void;
}

//no-op function to avoid undefined errors
const noop = () => {
  // intentionally empty
};

export const FallbackContext = createContext<FallbackContextType>({
  updateFallback: noop
});

interface FallbackProviderProps {
  children: ReactNode;
}

export const FallbackProvider: React.FC<FallbackProviderProps> = ({
  children,
}) => {
  const [fallback, setFallback] = useState<FallbackType>(null);

  const updateFallback = useCallback((newFallback: FallbackType) => {
    setFallback(() => newFallback);
  }, []);

  const renderChildren = useMemo(() => {
    return children;
  }, [children]);

  return (
    <FallbackContext.Provider value={{ updateFallback }}>
      <Suspense fallback={fallback}>{renderChildren}</Suspense>
    </FallbackContext.Provider>
  );
};
