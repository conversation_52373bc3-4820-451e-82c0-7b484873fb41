import { OptionType } from "@/types/registration.interface";
import { useEffect, useState } from "react";

type Props = {
  onChange?: (value: string[], lastChecked?: string) => void;
  checkList: OptionType[];
  checked?: any[];
  checkedChildren?: any[];
  onSingleChange?: (value: string) => void;
  onChildChange?: (value: string, parentValue: string) => void;
  mainClass?: string;
  onInfoIconClick?: (value: number | string) => void;
};

const AppCheckBoxListComponent = ({
  checkList,
  onChange,
  onSingleChange,
  onChildChange,
  checked = [],
  checkedChildren = [],
  mainClass = "pl-5",
  onInfoIconClick,
}: Props) => {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);

  useEffect(() => {
    if (JSON.stringify(checkedItems) !== JSON.stringify(checked)) {
      setCheckedItems([...checked]);
    }
  }, [checked]);

  const renderChildCheckboxes = (option: OptionType) => {
    return option?.children?.map((child) => (
      <label
        key={`${option.id}-${child.id}`}
        className={`app_checkbox_regiter block ml-10 ${option.disabled ? " disabled" : ""}`}
        htmlFor={`${option.id}-${child.id}`}
      >
        <input
          disabled={option.disabled ?? false}
          checked={checkedChildren.includes(child.value)}
          id={`${option.id}-${child.id}`}
          name={`${option.name}-${child.name}`}
          value={child.value}
          type="checkbox"
          onChange={(e) => {
            onChildChange?.(e.target.value, option.value.toString());
          }}
        />{" "}
        <span className="checkmark_checkbox"></span>
        &nbsp; {child.label}
      </label>
    ));
  };

  return (
    <div className={`${mainClass}`}>
      {checkList?.map((option, index) => {
        return (
          <div
            className="mb-[5px]"
            key={`${option.id}-${index}`}
            style={{ cursor: option.disabled ? "not-allowed" : "pointer" }}
          >
            <div className="flex items-center">
              <label
                aria-label={option.value.toString()}
                className={`app_checkbox_regiter block mb-1 ${option.disabled ? " disabled" : ""}`}
                style={{
                  marginBottom: 10,
                  pointerEvents: option.disabled ? "none" : "auto",
                  cursor: option.disabled ? "not-allowed" : "pointer",
                }}
                htmlFor={option.id}
              >
                <input
                  checked={checked.includes(option.value)}
                  disabled={option.disabled ?? false}
                  onChange={(e) => {
                    onSingleChange?.(e.target.value);

                    const checkedList = [...checkedItems];
                    if (!checkedList.includes(e.target.value)) {
                      checkedList.push(e.target.value);
                    } else {
                      checkedList.splice(checkedList.indexOf(e.target.value), 1);
                    }
                    setCheckedItems([...checkedList]);

                    onChange?.(checkedList, e.target.value);
                  }}
                  id={option.id}
                  name={option.name}
                  value={option.value}
                  type="checkbox"
                />{" "}
                <span className="checkmark_checkbox"></span>
                &nbsp; {option.label} &nbsp;
                {option.errorMessage && (
                  <span className="text-danger text-sm">({option.errorMessage})</span>
                )}
              </label>
              {option.info?.info?.show_on_demand === true && (
                <i
                  className="fa-solid fa-info-circle"
                  onClick={() => {
                    if (option.id) {
                      onInfoIconClick?.(option.id);
                    }
                  }}
                ></i>
              )}
            </div>
            {checked.includes(option.value) && renderChildCheckboxes(option)}
          </div>
        );
      })}
    </div>
  );
};

export default AppCheckBoxListComponent;
