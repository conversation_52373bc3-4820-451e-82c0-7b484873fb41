import { OptionType } from "@/types/registration.interface";

type Props = {
  onChange?: (value: string) => void;
  checkList: OptionType[];
  checked?: string | number | null;
  selectedAnsInfo?: Record<string, Record<string, any>>;
  onEditClick?: (value: number | string) => void;
};

const AppRadioComponent = ({ checkList, onChange, checked, selectedAnsInfo, onEditClick }: Props) => {
  return (
    <div className="pl-5">
      {checkList?.map((option) => {
        return (
          <div key={option.id} className="flex gap-2 mb-[5px] w-[250px] md:w-[500px]">
            <label
              aria-label={option.name}
              className={`app_radio_regiter block mb-5 ${option.disabled ? " disabled" : ""}`}
              key={option.value}
              htmlFor={option.id}
            >
              <input
                checked={checked === option.value}
                disabled={option.disabled || false}
                aria-label={option.name}
                onChange={(e) => {
                  if (onChange) onChange(e.target.value);
                }}
                id={option.id || option.value.toString()}
                name={option.name}
                value={option.value}
                type="radio"
              />{" "}
              &nbsp; {option.label}
              <span className="checkmark"></span>
            </label>
            {selectedAnsInfo && selectedAnsInfo[option.value] &&
              <span className="text-gray-400">
                ({selectedAnsInfo[option.value].label})&nbsp;&nbsp;
                <span className="text-sm underline cursor-pointer" onClick={() => {
                  if (onEditClick && option.id) onEditClick(option.id);
                }}>
                  <i className="fas fa-pencil"></i>
                  Edit
                </span>
              </span>           
            }
          </div>
        );
      })}
    </div>
  );
};

export default AppRadioComponent;
