import { useRef } from "react";
import { AriaTextFieldProps, useTextField } from "react-aria";

type Props = {
  rows?: number;
} & AriaTextFieldProps;

const TextAreaComponent = ({ rows = 4, ...props }: Props) => {
  const ref = useRef(null);
  const { labelProps, inputProps, errorMessageProps } = useTextField(
    {
      ...props,
      inputElementType: "textarea",
    } as AriaTextFieldProps,
    ref
  );

  return (
    <section>
      <label {...labelProps} htmlFor={props.id} className="text-sm text-gray-400">
        {props.label}
        {props.isRequired && (
          <span className="text-red-500 font-bold ml-1">*</span>
        )}
      </label>
      <textarea
        ref={ref}
        {...(inputProps as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
        id={props.id}
        required={props.isRequired}
        placeholder={props.placeholder}
        className="border mt-1 w-full border-1 bg-white border-gray-300 ring-0 focus:outline-neutral-300 rounded-xl py-2.5 px-2 text-sm"
        onChange={(e) => {
          props.onChange && props.onChange(e.target.value);
        }}
        rows={rows}
        value={props.value || ""}
      />

      {props.errorMessage && (
        <div {...errorMessageProps} style={{ color: "red", fontSize: 12 }}>
          {props.errorMessage.toString()}
        </div>
      )}
    </section>
  );
};

export default TextAreaComponent;
