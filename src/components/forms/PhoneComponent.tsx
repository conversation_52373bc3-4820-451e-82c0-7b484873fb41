import Cleave from "cleave.js/react";
import { ChangeEvent } from "react";

type Props = {
  disabled?: boolean;
  placeholder?: string;
  id?: string;
  label: string;
  onChange?: (value: string) => void;
  required?: boolean;
  value?: string | number;
  inputClassName?: string;
};

const PhoneComponent = (props: Props) => {
  const a = props.value?.toString().replace("+1", "");
  return (
    <section>
      <label htmlFor={props.id} className="text-sm">
        {props.label}
        {props.required && <span className="text-red-500 font-bold ml-1">*</span>}
      </label>
      <Cleave
        disabled={props.disabled}
        id={props.id}
        required={props.required}
        placeholder={props.placeholder}
        className={`border-2 bg-gray-50 mt-1 w-full ring-0 focus:outline-none rounded-lg py-2 px-2 text-sm ${props.inputClassName}`}
        value={a ? parseInt(a, 10) : ""}
        options={{
          prefix: "+1",
          blocks: [2, 3, 3, 4],
          numericOnly: true,
        }}
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
          props.onChange && props.onChange(e.target.value.replaceAll(" ", ""));
        }}
      />
    </section>
  );
};

export default PhoneComponent;
