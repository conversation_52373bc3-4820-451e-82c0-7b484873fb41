import React from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";


/**
 * @desc Custom input to prevent typing but allow calendar opening
 */
const CustomInput = React.forwardRef(
  ({ value, onClick, placeholder,'data-testid': dataTestId }: any, ref: any) => (
    <input
      type="text"
      onClick={onClick}
      value={value}
      placeholder={placeholder}
      readOnly
      ref={ref}
      data-testid={dataTestId}
      className="block border w-[200px] border-gray-300 bg-white ring-0 focus:outline-none focus:border-therapy-blue rounded-xl py-2.5 px-3 text-sm"
    />
  )
);

type Props = {
  id?: string;
  label: string;
  value: string;
  isDisabled?: boolean;
  isRequired?: boolean;
  placeholder?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  class?: string;
  errorMessage?: string;
  minDate?: Date;
  maxDate?: Date;
  ['data-testid']?: string;
  outputFormat?: "iso" | "us"; 
};

const parseDate = (dateStr: string): Date | null => {
  if (!dateStr) return null;

  const isoDate = new Date(dateStr);
  if (!isNaN(isoDate.getTime())) {
    return isoDate;
  }

  const [month, day, year] = dateStr.split("-").map(Number);
  const formattedDate = new Date(year, month - 1, day);
  return isNaN(formattedDate.getTime()) ? null : formattedDate;
};


const formatDate = (
  date: Date | null,
  format: "iso" | "us" = "iso"
): string => {
  if (!date) return "";

  const yyyy = date.getFullYear();
  const mm = String(date.getMonth() + 1).padStart(2, "0");
  const dd = String(date.getDate()).padStart(2, "0");

  return format === "us"
    ? `${mm}-${dd}-${yyyy}`
    : date.toISOString();
};


const DateComponent = (props: Props) => {
  return (
    <section className={`w-full ${props.class}`}>
      <label htmlFor={props.id} className="text-sm text-gray-400">
        {props.label}
        {props.isRequired && (
          <span className="text-red-500 font-bold ml-1">*</span>
        )}
      </label>
      <div className="mt-2">
        <DatePicker
          id={props.id}         
          selected={parseDate(props.value)}
           onChange={(date: Date) =>
            props.onChange?.(formatDate(date, props.outputFormat || "iso"))
          } 
          onBlur={props.onBlur}
          disabled={props.isDisabled}
          placeholderText={props.placeholder || "MM-DD-YYYY"}
          dateFormat="MM-dd-yyyy"
          showMonthDropdown
          showYearDropdown
          dropdownMode="select"
          minDate={props.minDate}
          maxDate={props.maxDate}
          customInput={ <CustomInput
            data-testid={props['data-testid']}
            placeholder="MM-DD-YYYY"
            value={formatDate(parseDate(props.value), "us")} // Always display as MM-DD-YYYY
          />}
        />
      </div>
      {props.errorMessage && (
        <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
          {props.errorMessage.toString()}
        </div>
      )}
    </section>
  );
};

export default DateComponent;