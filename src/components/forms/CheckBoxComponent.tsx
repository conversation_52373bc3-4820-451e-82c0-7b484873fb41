type Props = {
  onChange?: (value: boolean) => void;
  value?: string;
  label?: string;
  checked: boolean;
};

const CheckBoxComponent = ({ checked, onChange, label, value }: Props) => {
  return (
    <section className="flex flex-row items-center justify-start text-sm relative group">
      <span
        className={`h-4 w-4 flex items-center justify-center border rounded mr-2 cursor-pointer`}
        onClick={() => onChange && onChange(!checked)}
      >
        <svg className="stroke-current w-2 h-2" viewBox="0 0 16 16">
          <polyline
            points="1 9 7 14 15 4"
            fill="none"
            strokeWidth={3}
            strokeDasharray={22}
            strokeDashoffset={checked ? 44 : 66}
            style={{
              transition: "all 200ms",
            }}
          />
        </svg>
      </span>
      {label && (
        <span className="cursor-pointer" onClick={() => onChange && onChange(!checked)}>
          {label}
        </span>
      )}
      {value && (
        <span className="absolute bottom-full bg-white text-gray-900 text-xs rounded-lg mb-1 py-1 px-2 border border-gray-300 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          {value}
        </span>
      )}
    </section>
  );
};

export default CheckBoxComponent;
