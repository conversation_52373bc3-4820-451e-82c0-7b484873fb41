import { ChangeEvent, useRef, useState } from "react";

type Props = {
  disabled?: boolean;
  id?: string;
  label?: string;
  onChange: (file?: File) => void;
  inputClassName?: string;
  preview?: string;
};
const FileInputComponent = (props: Props) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [image, setImage] = useState<File>();

  const onFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImage(file);
      props.onChange(file);
    } else {
      props.onChange();
    }
  };

  return (
    <div
      onClick={() => {
        if (inputRef.current) {
          inputRef.current.click();
        }
      }}
      className={`group min-h-[120px] relative overflow-hidden bg-gray-50 hover:bg-gray-100 rounded-lg border flex flex-col items-center justify-center cursor-pointer`}
    >
      <span>{props.label ?? "Upload file"}</span>
      <input
        ref={inputRef}
        type="file"
        className="hidden"
        onChangeCapture={onFileChange}
        data-testid="file-input"
      />
      {(image || props.preview) && (
        <>
          <img
            src={image ? URL.createObjectURL(image) : props.preview}
            alt="preview"
            className="absolute h-full w-full object-cover"
          />
          <span className="absolute animation duration-300 hidden group-hover:flex text-white bg-black bg-opacity-30 h-full w-full flex flex-col items-center justify-center">
            <span>Click to change</span>
          </span>
        </>
      )}
    </div>
  );
};

export default FileInputComponent;
