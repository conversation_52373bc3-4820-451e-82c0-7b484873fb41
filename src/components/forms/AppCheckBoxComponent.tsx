type Props = {
  onChange?: (value: string) => void;
  value: string;
  id?: string;
  name?: string;
  label: string;
  checked?: boolean;
};

const AppCheckBoxComponent = ({ value, id, name, label, onChange, checked = false }: Props) => {
  return (
    <label className="app_checkbox_regiter block mb-5" style={{ marginBlock: 10 }} key={value} htmlFor={id}>
      <input
        checked={checked}
        onChange={(e) => {
          if (onChange) {
            onChange(e.target.value);
          }
        }}
        id={id}
        name={name}
        value={value}
        type="checkbox"
        aria-label={value}
      />{" "}
      <span className="checkmark_checkbox"></span>
      &nbsp; {label}
    </label>
  );
};

export default AppCheckBoxComponent;
