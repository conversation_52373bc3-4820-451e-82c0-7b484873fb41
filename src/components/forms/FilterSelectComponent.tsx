type Props = {
  placeholder?: string;
  id?: string;
  label: string;
  onChange?: (value: string) => void;
  regex?: RegExp;
  required?: boolean;
  options: string[] | number[] | { label: string; value: string | number }[];
  value?: string | number;
};
const FilterSelectComponent = (props: Props) => {
  return (
    <section>
      <label htmlFor={props.id} className="text-sm">
        {props.label}
        {props.required && <span className="text-red-500 font-bold ml-1">*</span>}
      </label>
      <select
        id={props.id}
        required={props.required}
        className="border mt-1 w-full ring-0 focus:outline-blue-500 rounded py-2 px-2 text-sm"
        value={props.value || ""}
        onChange={(e) => {
          // check if regex is provided
          if (props.regex) {
            // check if regex matches
            if (props.regex.test(e.target.value)) {
              // if matches, then set the value
              props.onChange && props.onChange(e.target.value);
            }
          } else {
            props.onChange && props.onChange(e.target.value);
          }
        }}
      >
        {props.options.map((option, index) => {
          if (typeof option === "string" || typeof option === "number") {
            return (
              <option key={index} value={option}>
                {option}
              </option>
            );
          } else {
            return (
              <option key={index} value={option.value}>
                {option.label}
              </option>
            );
          }
        })}
      </select>
    </section>
  );
};

export default FilterSelectComponent;
