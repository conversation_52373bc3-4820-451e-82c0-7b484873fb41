import type { OptionType } from "@/types/registration.interface"
import { useEffect, useState } from "react";

type Props = {
    onChange?: (value: { [key: string]: string }) => void;
    itemList: OptionType[];
    checked?: { [key: string]: string };
}

const AppCheckBoxInputComponent = ({ itemList, onChange, checked = {} }: Props) => {
    const [checkedItems, setCheckedItems] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    setCheckedItems({ ...checked })
  }, []);

  return (
    <div className="pl-0 sm:pl-5">
      {itemList?.map((option) => {
        return (
          <div key={option.value} className="mb-6">
            <label
              aria-label={option.value.toString()}
              className={`app_checkbox_regiter block mb-3 ${option.disabled ? " disabled" : ""}`}
              htmlFor={option.id}
            >
              <input
                checked={Object.keys(checkedItems).includes(option.value.toString())}
                disabled={option.disabled || false}
                onChange={(e) => {
                  const checkedList = { ...checkedItems }
                  const itemName = e.target.value
                  if (!Object.keys(checkedItems).includes(itemName)) {
                    checkedList[itemName] = "";
                  } else {
                    delete checkedList[itemName];
                  }

                  setCheckedItems({ ...checkedList });

                  if (onChange) {
                    onChange(checkedList);
                  }
                }}
                id={option.id}
                name={option.name}
                value={option.value}
                type="checkbox"
              />{" "}
              <span className="checkmark_checkbox"></span>
              &nbsp; {option.label}
            </label>
            {Object.keys(checkedItems).includes(option.value.toString()) && (
              <div className="w-full max-w-full sm:max-w-md pl-0 sm:pl-5">
                <input
                  type="text"
                  className="border w-full border-1 bg-white border-gray-300 ring-0 focus:outline-none focus:border-therapy-blue rounded-xl py-2.5 px-2 text-sm"
                  placeholder={`Paste ${option.label} profile link`}
                  value={checkedItems[option.value] || ""}
                  onChange={(e) => {
                    const itemName = option.value;
                    const value = e.target.value;
                    setCheckedItems((prevState) => ({
                      ...prevState,
                      [itemName]: value
                    }))
                    if (onChange) {
                      onChange({ ...checkedItems, [itemName]: value })
                    }
                  }}
                />
              </div>
            )}
          </div>
        )
      })}
    </div>
  );
};

export default AppCheckBoxInputComponent;
