import { useRef, useState } from "react";
import type { AriaTextFieldProps } from "react-aria";
import { useTextField } from "react-aria";

type Props = object & AriaTextFieldProps & { class?: string, showToggleIcon?: boolean; inputClassName?: string; };

const InputComponent = (props: Props) => {
  const ref = useRef(null);
  const [isPasswordVisible, setIsPasswordVisible] = useState<boolean>(false);
  const { labelProps, inputProps, descriptionProps, errorMessageProps } = useTextField(props, ref);

  const inputType = () => {
    if (props.type === "password") {
      return isPasswordVisible ? "text" : "password";
    }
    return props.type || "text";
  };

  return (
    <section className={`w-full ${props.class}`}>
      <label aria-label={""} {...labelProps} htmlFor={props.id} className="text-sm text-gray-400">
        {props.isRequired && <span className="text-red-500 font-bold ml-1">*</span>}
        {props.label}
      </label>
      <div className="relative">
        {(props.type === 'password' || props.type === 'email') && (
          <input 
            type={props.type}
            tabIndex={-1}
            aria-hidden="true"
            style={{ 
              opacity: 0, 
              position: "absolute", 
              pointerEvents: "none", 
              height: 0,
              width: 0,
              overflow: "hidden"
            }}
          />
        )}
        <input
          aria-label={props.id}
          ref={ref}
          {...inputProps}
          type={inputType()}
          // required={props.isRequired}
          placeholder={props.placeholder}
          autoComplete="off"
          aria-autocomplete="none"
          name={props.name}
          // className={`border mt-1 w-full border-1 bg-white border-gray-300 ring-0 focus:outline-neutral-300 rounded-xl py-2.5 px-2 text-sm`}
          className={`border mt-2 w-full border-1 border-gray-300 
            focus:outline-none focus:border-therapy-blue 
            rounded-xl py-2.5 px-2 text-sm ${props.inputClassName ?? "bg-white"}`}
        />
        {props.type === "password" && props.showToggleIcon && (
          <span
            onClick={() => setIsPasswordVisible(!isPasswordVisible)}
            className="absolute mt-1 top-1/2 transform -translate-y-1/2 right-3 text-gray-400 cursor-pointer"
          >
            {isPasswordVisible ? <i className="fa-solid fa-eye-slash"></i> : <i className="fa-solid fa-eye"></i>}
          </span>
        )}
      </div>
      {props.description && (
        <div {...descriptionProps} style={{ fontSize: 12 }}>
          {props.description}
        </div>
      )}
      {props.errorMessage && (
        <div {...errorMessageProps} style={{ color: "red", fontSize: 12, marginTop: 5 }}>
          {props.errorMessage.toString()}
        </div>
      )}
    </section>
  );
};

export default InputComponent;
