import { debounce } from "lodash";
import { ChangeEvent, useEffect, useMemo, useRef } from "react";

type Props = {
  disabled?: boolean;
  placeholder?: string;
  id?: string;
  onChange?: (value: string) => void;
  regex?: RegExp;
  required?: boolean;
  type?: "text" | "email" | "password" | "number" | "tel";
  value?: string | number;
  inputClassName?: string;
};

const FilterInputComponent = (props: Props) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    props.onChange && props.onChange(e.target.value);
  };

  const debouncedResults = useMemo(() => {
    return debounce(handleChange, 500);
  }, []);

  useEffect(() => {
    return () => {
      debouncedResults.cancel();
    };
  });

  return (
    <section className="w-full group border rounded-md flex items-center mt-1 bg-white">
      <input
        ref={inputRef}
        disabled={props.disabled}
        id={props.id}
        type={props.type || "text"}
        required={props.required}
         value={props.value ?? undefined}
        placeholder={props.placeholder}
        onChange={debouncedResults}
        className={`w-full bg-transparent ring-0 focus:outline-none py-2 px-4 text-sm ${props.inputClassName}`}
      />
      <span
      data-testid="clear-button"
        className="pr-3 group-hover:text-red-700 cursor-pointer"
        onClick={() => {
          inputRef.current && (inputRef.current.value = "");
          props.onChange && props.onChange("");
        }}
      >
        <i className="fa-duotone fa-close"></i>
      </span>
    </section>
  );
};

export default FilterInputComponent;
