import Cleave from "cleave.js/react";

type Props = {
  isDisabled?: boolean;
  isRequired?: boolean;
  placeholder?: string;
  id?: string;
  label: string;
  onChange?: (value: string) => void;
  value?: string | number;
  cls?: string;
};

const DateTimeComponent = (props: Props) => {
  const onDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (props.onChange) {
      try {
        let [date, time] = e.target.value.split(" ");
        let [hour, minute] = time.split(":").map((x) => parseInt(x || "0"));
        if (hour > 24) hour = 24;
        if (minute > 59) minute = 59;
        props.onChange(`${date} ${hour}:${minute}`);
      } catch (error) {
        props.onChange(e.target.value);
      }
    }
  };
  return (
    <section>
      <label htmlFor={props.id} className="text-sm">
        {props.label}
        {props.isRequired && (
          <span className="text-red-500 font-bold ml-1">*</span>
        )}
      </label>
      <Cleave
        disabled={props.isDisabled}
        id={props.id}
        required={props.isRequired}
        placeholder={props.placeholder || "yyyy-mm-dd hh:mm"}
        className={`border mt-1 w-full ring-0 focus:outline-neutral-500 rounded py-2 px-2 text-sm ${props.cls}`}
        value={props.value || ""}
        options={{
          delimiters: ["-", "-", " ", ":"],
          blocks: [4, 2, 2, 2, 2],
          numericOnly: true,
        }}
        onChange={onDateChange}
      />
    </section>
  );
};

export default DateTimeComponent;
