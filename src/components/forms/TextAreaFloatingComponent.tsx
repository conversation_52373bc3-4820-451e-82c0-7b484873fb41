type Props = {
  placeholder?: string;
  id?: string;
  label: string;
  onChange?: (value: string) => void;
  required?: boolean;
  value?: string | number;
  inputClassName?: string;
};
const TextAreaFloatingComponent = (props: Props) => {
  return (
    <section className="relative mt-2">
      <textarea
        id={props.id}
        required={props.required}
        placeholder={props.placeholder}
        className={`peer border w-full ring-0 rounded py-2 px-3 text-sm placeholder-transparent focus:outline-sky-700 ${props.inputClassName}`}
        onChange={(e) => {
          props.onChange && props.onChange(e.target.value);
        }}
        value={props.value || ""}
      />
      <label
        htmlFor={props.id}
        className="absolute left-3 cursor-text -top-2.5 text-xs transition-all text-gray-600 peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-2.5 peer-focus:-top-2.5 bg-white px-1 peer-focus:bg-white peer-focus:text-sky-700 peer-empty:not(:focus):text-sm"
      >
        {props.label}
        {props.required && (
          <span className="text-red-500 font-bold ml-1">*</span>
        )}
      </label>
    </section>
  );
};

export default TextAreaFloatingComponent;
