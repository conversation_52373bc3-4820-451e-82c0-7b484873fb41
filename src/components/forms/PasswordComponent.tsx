import { useRef, useState } from "react";
import type { AriaTextFieldProps } from "react-aria";
import { useTextField } from "react-aria";

type Props = {
  cls?: string;
} & AriaTextFieldProps;

const PasswordComponent = (props: Props) => {
  const ref = useRef(null);
  const { labelProps, inputProps, descriptionProps, errorMessageProps } =
    useTextField(props, ref);
  const [type, setType] = useState("password");

  const togglePassword = () => {
    setType(type === "password" ? "text" : "password");
  };

  return (
    <section>
      <label {...labelProps} htmlFor={props.id} className="text-sm">
        {props.label}
        {props.isRequired && (
          <span className="text-red-500 font-bold ml-1">*</span>
        )}
      </label>
      <div className="relative">
        <input
          ref={ref}
          {...inputProps}
          type={type}
          className={`border mt-1 w-full ring-0 focus:outline-neutral-500 rounded py-2 px-2 text-sm ${props.cls}`}
        />
        {!props.isDisabled && (
          <span
          data-testid="toggle-password"
            className="absolute cursor-pointer right-2 top-1 h-[90%] flex items-center justify-center"
            onClick={togglePassword}
          >
            <i
              className={`fa-duotone ${
                type === "password" ? "fa-eye-slash" : "fa-eye"
              }`}
            ></i>
          </span>
        )}
      </div>
      {props.description && (
        <div {...descriptionProps} style={{ fontSize: 12 }}>
          {props.description}
        </div>
      )}
      {props.errorMessage && (
        <div {...errorMessageProps} style={{ color: "red", fontSize: 12 }}>
          {props.errorMessage.toString()}
        </div>
      )}
    </section>
  );
};

export default PasswordComponent;
