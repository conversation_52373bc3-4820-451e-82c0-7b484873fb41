type Props = {
  placeholder?: string;
  id?: string;
  label?: string;
  onChange?: (value: string) => void;
  regex?: RegExp;
  required?: boolean;
  options: string[] | number[] | { label: string; value: string | number }[];
  value?: string | number;
  disabled?: boolean;
  selectClass?: string;
  labelClass?: string;
};
const NormalSelectComponent = (props: Props) => {
  return (
    <section>
      {props.label && (
        <label htmlFor={props.id} className={`${props.labelClass || "text-sm text-gray-400"}`}>
          {props.required && <span className="text-red-500 font-bold ml-1">* </span>}
          {props.label}
        </label>
      )}
      <select
        disabled={props.disabled}
        id={props.id}
        required={props.required}
        className={`border mt-2 ${props.selectClass || "w-full"} border-1 bg-white border-gray-300 ring-0 focus:outline-neutral-300 rounded-xl py-2.5 px-2 text-sm overflow-ellipsis`}
        value={props.value || ""}
        onChange={(e) => {
          // check if regex is provided
          if (props.regex) {
            // check if regex matches
            if (props.regex.test(e.target.value)) {
              // if matches, then set the value
              props.onChange && props.onChange(e.target.value);
            }
          } else {
            props.onChange && props.onChange(e.target.value);
          }
        }}
      >
         {/* Placeholder Option */}
        <option value="" disabled hidden>
          {props.placeholder || "Select an option"}
        </option>
        {props.options.map((option, index) => {
          if (typeof option === "string" || typeof option === "number") {
            return (
              <option className={`${index} === 0 ? "bg-blue-50 text-md" : "text-md"`} key={index} value={option}>
                {option}
              </option>
            );
          } else {
            return (
              <option className={`${index} === 0 ? "bg-blue-50 text-md" : "text-md"`} key={index} value={option.value}>
                {option.label}
              </option>
            );
          }
        })}
      </select>
    </section>
  );
};

export default NormalSelectComponent;