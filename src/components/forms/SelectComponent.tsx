import Select from 'react-select';

type Option = {
  label: string;
  value: string | number;
};

type Props = {
  placeholder?: string;
  id?: string;
  label?: string;
  onChange?: (value: Option | Option[] | null) => void;
  regex?: RegExp;
  required?: boolean;
  options: Option[];
  value?: Option | Option[];
  disabled?: boolean;
  selectClass?: string;
  isMulti?: boolean;
  errorMessage?: string;
  onBlur?: () => void;
};

const SelectComponent = (props: Props) => {
  return (
    <section>
      {props.label && (
        <label htmlFor={props.id} className="text-sm text-gray-400">
          {props.required && <span className="text-red-500 font-bold ml-1">*</span>}
          {props.label}
        </label>
      )}
      <Select
        isDisabled={props.disabled}
        id={props.id}
        // required={props.required}
        className={props.selectClass}
        options={props.options}
        value={props.value}
        onChange={(newValue) => {
          if (props.regex && newValue) {
            const stringValue = String((newValue as Option).value);
            if (props.regex.test(stringValue)) {
              props.onChange?.(newValue as Option | Option[]);
            }
          } else {
            props.onChange?.(newValue as Option | Option[]);
          }
        }}
        onBlur={props.onBlur}
        placeholder={props.placeholder || "Select an option"}
        isMulti={props.isMulti}
        styles={{
          control: (baseStyles, state) => ({
            ...baseStyles,
            borderRadius: '0.75rem', // rounded-xl
            borderColor: state.isFocused ? '#547191' : '#e5e7eb', // border-gray-300
            padding: '0.2rem',
            marginTop: '0.5rem',
            '&:hover': {
              borderColor: '#d1d5db'
            },
          }),
          option: (baseStyles, state) => {
            let backgroundColor = 'white';
            if (state.isSelected) {
              backgroundColor = '#eff6ff';
            } else if (state.isFocused) {
              backgroundColor = '#f9fafb';
            }

            return {
              ...baseStyles,
              backgroundColor,
              color: state.isSelected ? '#547191' : '#374151',
            };
          },
          menuPortal: (base) => ({ ...base, zIndex: 9999 }),
          menu: (base) => ({ ...base, zIndex: 9999 })
        }}
        menuPortalTarget={document.body}
        menuPosition="fixed"
      />
      {props.errorMessage && (
        <div style={{ color: "red", fontSize: 12, marginTop: 5 }}>
          {props.errorMessage.toString()}
        </div>
      )}
    </section>
  );
};

export default SelectComponent;