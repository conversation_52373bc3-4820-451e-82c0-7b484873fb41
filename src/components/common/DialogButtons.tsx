interface DialogButtonsProps {
  onCancel?: (() => void) | null;
  onAccept?: () => void;
  loading?: boolean;
  confirmLabel?: string;
  disabled?: boolean;
  btnDisabled?: boolean;
  contentCentered?: boolean;
  primaryButtonColor?: boolean;
}

const DialogButtons = ({
  onCancel,
  onAccept,
  loading,
  confirmLabel,
  disabled,
  btnDisabled,
  contentCentered,
  primaryButtonColor,
}: DialogButtonsProps) => (
  <div
    className={`mt-4 flex items-center ${
      contentCentered ? "justify-center" : "justify-end"
    } space-x-2`}
  >
    {onCancel && (
      <button
        type="button"
        onClick={onCancel}
        className="bg-primary hover:opacity-75 inline-flex justify-center rounded-md border border-transparent px-4 py-1.5 text-sm font-medium text-white active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2"
        disabled={loading}
      >
        Cancel
      </button>
    )}
    {onAccept && (
      <button
        onClick={onAccept}
        className={`${
          primaryButtonColor ? "bg-therapy-blue-dark" : "bg-primary"
        } inline-flex justify-center rounded-md border border-transparent px-4 py-1.5 text-sm font-medium text-white active:scale-95 transition focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 hover:opacity-75`}
        disabled={disabled || loading || btnDisabled}
      >
        {loading && <i className="fa fa-spinner animate-spin mr-1" />}
        {confirmLabel || "Confirm"}
      </button>
    )}
  </div>
);

export default DialogButtons;
