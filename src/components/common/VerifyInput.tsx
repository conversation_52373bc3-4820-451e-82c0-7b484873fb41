interface VerifyInputProps {
  verifyText?: string;
  verification: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const VerifyInput = ({ verifyText, verification, onChange }: VerifyInputProps) => {
  if (!verifyText) return null;

  return (
    <div className="text-sm mt-4">
      Type{" "}
      <span data-testid="verify-text" className="font-semibold text-xs italic text-red-800">
        {verifyText}
      </span>{" "}
      to confirm.
      <input
        type="text"
        required
        className="bg-gray-50 border border-primary text-gray-900 sm:text-sm rounded-lg focus:outline-none focus:ring-primary focus:border-primary/80 block w-full p-2.5 mt-2"
        value={verification}
        onChange={onChange}
      />
    </div>
  );
};

export default VerifyInput;
