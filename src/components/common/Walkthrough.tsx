import { TooltipRenderProps } from 'react-joyride';
import '@/styles/WalkThrough.css';

interface CustomTooltipProps extends TooltipRenderProps {
  closeButtonHandler?: () => void;
  arrowProps?: React.HTMLAttributes<HTMLDivElement>;
}

const WalkThrough = ({
  step,
  backProps,
  primaryProps,
  tooltipProps,
  arrowProps,
  index,
  size,
  closeButtonHandler,
}: CustomTooltipProps) => {
  const isFirst = index === 0;
  const isLast = index === size - 1;

  return (
    <div {...tooltipProps} className="custom-tooltip">
      {/* Default Joyride Arrow */}
      <div
        {...arrowProps}
        className="react-joyride__tooltip__arrow"
      />

      {/* Close Button */}
      <button
        aria-label="Close"
        onClick={(e) => {
          e.stopPropagation();
          closeButtonHandler?.();
        }}
        className="tooltip-close-btn"
      >
        &times;
      </button>

      {/* Main Tooltip Text */}
      <div dangerouslySetInnerHTML={{ __html: step.content as string }} />

      {/* Footer Navigation */}
      <div className="tooltip-footer">
        <button
          {...backProps}
          disabled={isFirst}
          className="tooltip-back-btn"
          style={{
            color: isFirst ? '#ccc' : 'rgb(67 29 66)',
            cursor: isFirst ? 'not-allowed' : 'pointer',
            pointerEvents: isFirst ? 'none' : 'auto',
            opacity: isFirst ? 0.6 : 1,
            backgroundColor: 'transparent',
          }}
        >
          <i className="fa-solid fa-arrow-left"></i> {`${index + 1}/${size}`}
        </button>

        <button
          {...primaryProps}
          onClick={(e) => {
            e.stopPropagation();
            primaryProps.onClick?.(e);
          }}
          className="tooltip-next-btn"
        >
          {isLast ? 'Finish' : 'Next'}
        </button>
      </div>
    </div>
  );
};

export default WalkThrough;
