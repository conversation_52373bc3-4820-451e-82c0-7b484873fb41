import Meta from "@/types/meta.interface";
import { InfiniteQueryObserverResult } from "@tanstack/react-query";
import React, { FC } from "react";
import PerfectScrollbar from "react-perfect-scrollbar";

type Props = {
  children: React.JSX.Element;
  meta: Meta;
  fetching?: boolean;
  onLoad: () =>
    | void
    | Promise<void>
    | Promise<InfiniteQueryObserverResult<unknown, unknown>>;
  className?: string;
  emptyText?: string;
};

const PaginationAwareContainer: FC<Props> = ({
  children,
  meta,
  onLoad,
  fetching,
  className,
}) => {
  const handleScroll = (e: any) => {
    const target = e.target as HTMLDivElement;
    if (
      target.scrollTop + target.clientHeight >= target.scrollHeight &&
      !fetching &&
      meta.nextPage !== null
    ) {
      onLoad();
    }
  };

  return (
    <>
      <PerfectScrollbar
        onScroll={handleScroll}
        className={`${className} pr-3 pt-8 pb-4`}
      >
        {children}
        {fetching && (
          <div className="mt-4 flex flex-row items-center justify-center">
            <i className="fas fa-spinner animate-spin fa-xl inline-block my-2"></i>
          </div>
        )}
      </PerfectScrollbar>
    </>
  );
};

export default PaginationAwareContainer;
