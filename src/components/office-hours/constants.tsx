import { Step } from 'react-joyride'

export const walkthroughSteps: Step[] = [
	{
		target: 'body',
		placement: 'center',
		disableBeacon: true,
		isFixed: true,
		spotlightClicks: false,
		content: `<h2 style="margin: 0 0 12px 0; font-size: 20px; color: #431d42;">How To Set Up Your Schedule</h2>
		<p>
		Use this page to set your weekly recurring availability. These hours reflect when you're typically available to see clients.
		</p>
		<p>
		<strong>Important:</strong> This page is not for scheduling one-off changes. To block off time for vacations, appointments, or personal commitments, 
		simply create a “busy” block in your calendar as you would when scheduling any appointment.
		</p>
		<p>
		NextT<PERSON>apist will regularly scan your calendar and compare it to your office hours to identify gaps—automatically offering those times to fill 
		last-minute openings or cancellations that might otherwise go unbooked.
		</p>`,
	},
	{
		content: `<h3 style="margin: 0 0 12px 0; font-size: 18px; color: #431d42;">Select Appointment Type(s)</h3>
		<p>
		Choose the type(s) of appointments you offer. You can set different options for different days or
		time blocks.
		</p>
		<ul style="padding-left: 20px; margin-bottom: 0;">
		<li><strong>Both In-Person & Telehealth:</strong> You offer both. For each time block, you can choose to allow
		In-Person Only, Telehealth Only, or let clients choose their preference.</li>
		<li><strong>In-Person Only:</strong> All sessions are held in your physical office.</li>
		<li><strong>Telehealth Only:</strong> All sessions are held virtually, and you'll provide a video link.</li>
		</ul>
		<p>
		<strong>Example:</strong> You might offer both options Monday–Thursday, but only Telehealth on Fridays.
		</p>`,
		target: '.appointment-type',
		placement: 'right',
		disableBeacon: true,
		spotlightPadding: 0,
		styles: {
			spotlight: {
				backgroundColor: 'transparent',
				boxShadow: 'none',
			},
		},
	},
	{
		content: `<h3 style="margin: 0 0 12px 0; font-size: 18px; color: #431d42;">Select Your Time Zone</h3>
		<p>
		Select your local time zone to ensure all appointments are shown accurately in your calendar and
		to your clients.
		</p>
		`,
		target: '.timezone-select',
		placement: 'left',
		disableBeacon: true,
		spotlightPadding: 0,
		styles: {
			spotlight: {
				backgroundColor: 'transparent',
				boxShadow: 'none',
			},
		},
	},
	{
		content: `<h3 style="margin: 0 0 12px 0; font-size: 18px; color: #431d42;">Select Available Slots Individually</h3>
		<p>
		To select a time slot, click the desired start time. The next half-hour block will be automatically
		included to allow for a 50-minute session and a 10-minute transition.
		</p>
		<p>
		<strong>Note:</strong> Appointments can only start on the hour or half-hour, and all sessions are booked in
		50-minute increments.
		</p>`,
		target: 'div.time[data-day="monday"][data-order="18"]',
		placement: 'right',
		disableBeacon: true,
		disableOverlay: false,
		spotlightPadding: 0,
		styles: {
			spotlight: { display: 'none' },
		},
	},
	{
		content: `<h3 style="margin: 0 0 12px 0; font-size: 18px; color: #431d42;">To Select Multiple Slots</h3>
      <p>To select multiple time slots or days at once, click and drag across the calendar.
		This will automatically select consecutive 50-minute sessions with transition time included.</p>`,
		target: 'div.time[data-day="monday"][data-order="18"]',
		placement: 'right',
		disableBeacon: true,
		disableOverlay: false,
		spotlightPadding: 0,
		styles: {
			spotlight: { display: 'none' },
		},
	},
	
]
