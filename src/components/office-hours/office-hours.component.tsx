import { useCallback, useEffect, useRef, useState } from "react"
// import moment from "moment-timezone";
import NormalSelectComponent from "../forms/NormalSelectComponent"
import { useOverlayTriggerState } from "react-stately"
import NormalDialog from "../dialogs/NormalDialog"
import notification from "@/utils/notification.util"
import SaveButton from "../buttons/SaveButton"
import type { RootState } from "@/store/index"
import { useSelector } from "react-redux"
import DragSelect, { type DSInputElement } from "dragselect"
import type React from "react"
import NormalDialogV2 from "../dialogs/NormalDialogV2"
import FormDialog from "../dialogs/FormDialog"
import { US_TIMEZONES } from "@/pages/auth/constants"
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import Joyride, { CallBackProps,STATUS, ACTIONS, EVENTS } from 'react-joyride';
import WalkThrough from "../common/Walkthrough"
import '@/styles/WalkThrough.css';
import { walkthroughSteps } from './constants';

dayjs.extend(timezone);

const defaultTimezone = dayjs.tz.guess();

type OfficeHoursProps = {
  activeTimes: { [day: string]: { time: string; appointmentMethod: string; disabled: boolean }[] }
  setActiveTimes: React.Dispatch<React.SetStateAction<any>>
  selectedTimezone: string | null
  setSelectedTimezone: React.Dispatch<React.SetStateAction<string | null>>
  appointmentMethod: string | null
  setAppointmentMethod: React.Dispatch<React.SetStateAction<string | null>>
  onSave: ({ e, isPageValid }: { e?: React.MouseEvent<HTMLButtonElement>; isPageValid?: boolean }) => void;
  btnLabel?: string
  shouldDisableBtn?: boolean
  showAutoWalkthrough?:boolean
}

export type ActiveTimes = {
  [day: string]: { time: string; appointmentMethod: string; disabled: boolean; related: string; order: number }[]
}

let ds: DragSelect | null = null

const steps = walkthroughSteps;

const OfficeHoursComponent: React.FC<OfficeHoursProps> = ({
  setActiveTimes,
  activeTimes,
  setSelectedTimezone,
  selectedTimezone,
  appointmentMethod,
  setAppointmentMethod,
  onSave,
  btnLabel = "Save",
  shouldDisableBtn = true,
  showAutoWalkthrough,
}) => {

   const [stepIndex, setStepIndex] = useState(0);
   const [run, setRun] = useState(true);
    const [showBtn, setShowBtn] = useState(!showAutoWalkthrough); 
       
useEffect(() => {
  const highlightSelectors = [
    '.timezone-select',
    '.appointment-type',
    '.office-hours'
  ];

  // Remove highlight from all known selectors
  highlightSelectors.forEach((selector) => {
    document.querySelectorAll(selector)?.forEach((el) => {
      el.classList.remove('select-highlighted');
    });
  });

  const currentTarget = steps[stepIndex]?.target;

  if (typeof currentTarget === 'string') {
    if (highlightSelectors.includes(currentTarget)) {
      document.querySelectorAll(currentTarget)?.forEach((el) => {
        el.classList.add('select-highlighted');
      });
    }
  } else if (currentTarget instanceof HTMLElement) {
    currentTarget.classList.add('select-highlighted');
  }
}, [stepIndex]);



    useEffect(() => {
    if (showAutoWalkthrough) {
      setStepIndex(0);
      setRun(true); // auto-show
    } else {
      setShowBtn(true); // show manual start button
      setRun(false);
    }
  }, [showAutoWalkthrough]);

 const handleJoyrideCallback = (data: CallBackProps) => {
    const { type, status, action } = data;

    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      setRun(false);
      return;
    }

    if (type === EVENTS.STEP_AFTER) {
      if (action === ACTIONS.NEXT) {
        setStepIndex(prev => prev + 1);
      } else if (action === ACTIONS.PREV) {
        setStepIndex(prev => Math.max(prev - 1, 0));
      }
    }

    if (type === EVENTS.TARGET_NOT_FOUND) {
      setStepIndex(prev => prev + 1);
    }
  };

 const handleClose = () => {
  setRun(false);

  if (loopTimerRef.current) {
    clearInterval(loopTimerRef.current);
    loopTimerRef.current = null;
  }

  const finger = document.getElementById("walkthrough-finger");
  if (finger) finger.style.display = 'none';

  document.querySelectorAll(".time.highlighted-demo").forEach(el => {
    el.classList.remove("highlighted-demo");
  });
};

  const [showEarlierTimes, setShowEarlierTimes] = useState<boolean>(false)
  const [showLaterTimes, setShowLaterTimes] = useState<boolean>(false)
  const [earlierTimeSlice, setEarlierTimeSlice] = useState<number>(18) // Start at 9am (index 18)
  const [laterTimeIncrement, setLaterTimeIncrement] = useState<number>(0) // Track how many increments of 'Show later times' have been clicked
  const [daysWithError, setDaysWithError] = useState<{ [day: string]: { invalidTimes: string[] } }>({})
  const authStore = useSelector((state: RootState) => state.auth)
  // const selectAppointmentMethodState = useOverlayTriggerState({ isOpen: false });
  const [openConfirmation, setOpenConfirmation] = useState<boolean>(false)
  const noHoursState = useOverlayTriggerState({})
  const appointmentMethodRef = useRef(appointmentMethod)
  const x = useRef<any[]>([])
  const removeAllSlotFlagRef = useRef<boolean>(false)
  const infoState = useOverlayTriggerState({})
  const changeAppointmentMethodState = useOverlayTriggerState({})
  const [newAppMethod, setNewAppMethod] = useState<string | null>(null)
  const appointmentMethodsInfoState = useOverlayTriggerState({});
  const invalidHoursState = useOverlayTriggerState({});
  const [isNoHoursErrorShown, setIsNoHoursErrorShown] = useState<boolean>(false);
  const [isInvalidHoursErrorShown, setIsInvalidHoursErrorShown] = useState<boolean>(false);
  const activeTimesRef = useRef(activeTimes);

  const loopTimerRef = useRef<NodeJS.Timeout | null>(null);
  const usTimezones = US_TIMEZONES

  const days = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"]

const tableRef = useRef<HTMLTableElement | null>(null);
  function generateTimeArray(startTime: any, endTime: any) {
    const result = []
    const currentTime = new Date(startTime)

    while (currentTime <= endTime) {
      const formattedTime = currentTime.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      })
      result.push(formattedTime)
      currentTime.setMinutes(currentTime.getMinutes() + 30)
    }
    return result
  }

  const startTime = new Date()
  startTime.setHours(0, 0, 0) // Set start time to 5:00 AM
  const endTime = new Date()
  endTime.setHours(23, 59, 59) // Set end time to just before midnight

  const timeArray = generateTimeArray(startTime, endTime)
  
  const getDefaultEndTimeIndex = () => {
    const index = timeArray.findIndex(time => time.includes('5:30 PM'));
    return index !== -1 ? index : 35; // Default to index 35 if not found (5:30 PM)
  }

  const getActiveClass = (day: string, time: string) => {
    if (!activeTimes[day]?.map((t) => t.time)?.includes(time)) return ""

    const acTime = activeTimes[day]?.find((t) => t.time === time)

    const timeAppointmentMethod = activeTimes[day]?.find((t) => t.time === time)?.appointmentMethod
    let className = ""

    const hasError = daysWithError[day] && daysWithError[day].invalidTimes.includes(time)

    switch (timeAppointmentMethod) {
      case "in-person":
        className = `active-in-person ${acTime && acTime.disabled === true && !hasError ? "disabled-related-time" : ""} `
        break
      case "telehealth":
        className = `active-telehealth ${acTime && acTime.disabled === true && !hasError ? "disabled-related-time" : ""} `
        break
      case "in-person-and-telehealth":
        className = `active-both ${acTime && acTime.disabled === true && !hasError ? "disabled-related-time" : ""} `
        break
    }

    // Add error class if needed
    if (hasError) {
      className += " error"
    }
    return `${className}`
  }

  // Helper function to convert 12-hour AM/PM time to minutes
  function timeToMinutes(time: string) {
    const parts = time ? time.split(/[: ]/) : []
    const hour = Number.parseInt(parts[0], 10)
    const minute = Number.parseInt(parts[1], 10)
    const isPM = parts[2] === "PM"

    let convertedHour = hour
    if (isPM && hour !== 12) convertedHour += 12
    if (!isPM && hour === 12) convertedHour = 0

    return convertedHour * 60 + minute
  }

  function validateOfficeHourTimes() {
    const daysWithErrors: { [key: string]: { invalidTimes: string[] } } = {}
    const days = Object.keys(activeTimes)

    days.forEach((day) => {
      const dayAppointments = [...activeTimes[day]]
      const invalidTimes: string[] = []

      // Sort by time
      dayAppointments.sort((a: any, b: any) => timeToMinutes(a.time) - timeToMinutes(b.time))

      let i = 0
      while (i < dayAppointments.length) {
        const currentTime = timeToMinutes(dayAppointments[i].time)
        const nextTime = i + 1 < dayAppointments.length ? timeToMinutes(dayAppointments[i + 1].time) : null
        const currentMethod = dayAppointments[i].appointmentMethod
        const nextMethod = i + 1 < dayAppointments.length ? dayAppointments[i + 1].appointmentMethod : null

        if (nextTime !== null && nextTime - currentTime === 30) {
          // 30-minute gap is valid, check appointment method
          if (currentMethod === nextMethod) {
            i += 2 // Valid pair, skip both
          } else {
            // invalidTimes.push(dayAppointments[i + 1].time) // Mark the 2nd time as invalid
            invalidTimes.push(dayAppointments[i].time, dayAppointments[i + 1].time) // Mark both as invalid
            i += 2
          }
        } else {
          invalidTimes.push(dayAppointments[i].time) // Mark the 1st time as invalid
          i += 1
        }
      }

      if (invalidTimes.length > 0) {
        daysWithErrors[day] = { invalidTimes }
      }
    })

    setDaysWithError(daysWithErrors)
  }

  const handleTimeSelection = (
    selectedTimes: { time: string; day: string; related: string; order: number; currentAM: string }[],
    appointmentMethod: string,
  ) => {
    if (!appointmentMethod || appointmentMethod.trim() === "") {
      notification.error("Please select an appointment type first")
      ds?.setSelection([])
      return
    }

    const selectedTimesOrdered = selectedTimes.sort((a, b) => a.order - b.order)

    // const appointmentMethod = "telehealth";
    const newState: ActiveTimes = selectedTimesOrdered.reduce((acc, { time, day, related, currentAM, order }) => {
      if (!acc[day]) {
        acc[day] = []
      }
      acc[day].push({
        time,
        appointmentMethod: currentAM ? currentAM : appointmentMethod,
        disabled: false,
        related,
        order: order,
      })

      return acc
    }, {} as ActiveTimes)

    // const data = {};

    const grouped: any = {}

    Object.keys(newState).map((item) => {
      let currentGroup = [newState[item][0]]
      let sequenceNumber = 1

      for (let i = 1; i < newState[item].length; i++) {
        const prev = newState[item][i - 1]
        const curr = newState[item][i]

        if (prev.related === curr.time) {
          currentGroup.push(curr)
        } else {
          grouped[`seq${sequenceNumber}`] = currentGroup
          sequenceNumber++
          currentGroup = [curr]
        }
      }

      if (currentGroup.length > 0) {
        grouped[`seq${sequenceNumber}`] = currentGroup
      }

      for (let i = 0; i < newState[item].length; i++) {
        const current = newState[item][i]

        // Look for any previous item that has current.time as its related
        for (let j = 0; j < i; j++) {
          const prev = newState[item][j]

          if (prev.related === current.time && !prev.disabled) {
            current.disabled = true
          }
        }
      }

    })

    setActiveTimes(newState)
  }

  function getInvalidSelectedTimes(
    selectedTimes: { time: string; day: string; related: string; order: number; currentAM: string }[],
    appointmentMethod: string
  ): { time: string; day: string; related: string; validMethod?: string }[] {
    if (!appointmentMethod || appointmentMethod.trim() === "") {
      return [];
    }
  
    const selectedTimesOrdered = selectedTimes.sort((a, b) => a.order - b.order);
  
    const newState: ActiveTimes = selectedTimesOrdered.reduce((acc, { time, day, related, currentAM, order }) => {
      if (!acc[day]) {
        acc[day] = [];
      }
      acc[day].push({
        time,
        appointmentMethod: currentAM ? currentAM : appointmentMethod,
        disabled: false,
        related,
        order: order,
      });
  
      return acc;
    }, {} as ActiveTimes);

    const invalidSelections: { time: string; day: string; related: string; validMethod?: string }[] = [];
  
    Object.keys(newState).forEach((day) => {
      const dayAppointments = [...newState[day]];
  
      dayAppointments.sort((a, b) => timeToMinutes(a.time) - timeToMinutes(b.time));
  
      let i = 0;
      while (i < dayAppointments.length) {
        const current = dayAppointments[i];
        const next = i + 1 < dayAppointments.length ? dayAppointments[i + 1] : null;
  
        const currentTime = timeToMinutes(current.time);
        const nextTime = next ? timeToMinutes(next.time) : null;
  
        if (next && nextTime !== null && nextTime - currentTime === 30) {
          if (current.appointmentMethod === next.appointmentMethod) {
            i += 2; // valid pair
          } else {
            // Return both as invalid with the assumed valid method (fallback to current's method)
            invalidSelections.push(
              { time: current.time, day, related: current.related, validMethod: current.appointmentMethod },
              { time: next.time, day, related: next.related, validMethod: current.appointmentMethod }
            );
            i += 2;
          }
        } else {
          invalidSelections.push({ time: current.time, day, related: current.related, validMethod: current.appointmentMethod });
          i += 1;
        }
      }
    });

    return invalidSelections;
  }  

  const handleAppointmentMethodChange = (value: string) => {
    if (Object.keys(activeTimes).length > 0) {
      if (value === "in-person" || value === "telehealth") {
        setNewAppMethod(value as string)
        changeAppointmentMethodState.open()
      } else {
        setAppointmentMethod(value as string)
      }
    } else {
      setAppointmentMethod(value as string)
    }
  }

  function getDateForWeekday(weekday: string, timezone: string): string {
    const targetDayIndex = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
      .indexOf(weekday.toLowerCase());
  
    if (targetDayIndex === -1) {
      throw new Error(`Invalid weekday: ${weekday}`);
    }
  
    // Get the start of the week in the specified timezone
    const base = dayjs().tz(timezone).startOf('week'); // Sunday in that timezone
    return base.add(targetDayIndex, 'day').format('YYYY-MM-DD');
  }  

  function convertActiveTimes(currentTimezone: string, newTimezone: string) {
    const newActiveTimes: Record<string, any[]> = {};

    if (activeTimes && Object.keys(activeTimes).length > 0) {
      Object.entries(activeTimes).forEach(([day, slots]) => {
        const dateStr = getDateForWeekday(day, currentTimezone);

        slots.forEach(slot => {
          const parsed = dayjs(`${dateStr} ${slot.time}`, 'YYYY-MM-DD h:mm A');
          const originalDateTime = parsed.tz(currentTimezone, true);
          const converted = originalDateTime.tz(newTimezone);
  
          const newDay = converted.format('dddd').toLowerCase();
          const newTime = converted.format('h:mm A');
          const newRelatedTime = converted.add(30, 'minute').format('h:mm A');
  
          const newSlot = {
            ...slot,
            time: newTime,
            related: newRelatedTime
          };
  
          if (!newActiveTimes[newDay]) {
            newActiveTimes[newDay] = [];
          }
  
          newActiveTimes[newDay].push(newSlot);
        });
      });

      setActiveTimes(newActiveTimes);
  
      const timeElements = document.getElementsByClassName("time");
      const timesToSelect: HTMLElement[] = [];
  
      for (let i = 0; i < timeElements.length; i++) {
        const timeElement = timeElements[i] as HTMLElement;
        const time = timeElement.textContent?.trim();
        const day = timeElement.dataset.day;
  
        if (
          day &&
          time &&
          newActiveTimes[day] &&
          newActiveTimes[day].some((timeObj) => timeObj.time === time)
        ) {
          timesToSelect.push(timeElement);
        }
      }
  
      ds?.setSelection(timesToSelect);
      x.current = [];
    }
  }

  const parseSelectedValues = (selections: DSInputElement[]) => {
    return selections.map((item) => {
      const timeElement = item as HTMLElement
      const time = timeElement.textContent as string
      const day = timeElement.dataset.day as string
      const related = timeElement.dataset.related as string
      const currentAM = timeElement.dataset.apm as string
      const order = Number.parseInt(timeElement.dataset.order!) as number
      return { time, day, related, order, currentAM }
    })
  }

  function getSingleOfPairedSlots(
    pick: 'first' | 'second'
  ): { time: string; day: string; related: string }[] {
    const slotsList: { time: string; day: string; related: string }[] = [];
    const currentActiveTimes = activeTimesRef.current;
  
    Object.entries(currentActiveTimes).forEach(([day, slots]) => {
      const sorted = [...slots].sort(
        (a, b) => timeToMinutes(a.time) - timeToMinutes(b.time)
      );
  
      let i = 0;
      while (i < sorted.length) {
        const current = sorted[i];
        const next = i + 1 < sorted.length ? sorted[i + 1] : null;
  
        if (
          next &&
          timeToMinutes(next.time) - timeToMinutes(current.time) === 30 &&
          current.appointmentMethod === next.appointmentMethod
        ) {
          const picked = pick === 'first' ? current : next;
          const related = pick === 'first' ? next.time : current.time;
          slotsList.push({ day, time: picked.time, related });
          i += 2;
        } else {
          i += 1;
        }
      }
    });
  
    return slotsList;
  }

  const processAndSetTimeSelections = (selections: DSInputElement[], appointmentMethod: string) => {
    let selectedTimes = parseSelectedValues(selections);
  
    // Index selected times for quick lookup
    let selectedKeySet = new Set(selectedTimes.map(st => `${st.day}-${st.time}`));
  
    // Index all time elements by day and time text
    const timeElements = Array.from(document.getElementsByClassName("time")) as HTMLElement[];
    const timeElementMap = new Map<string, HTMLElement>();
    timeElements.forEach(el => {
      const key = `${el.dataset.day}-${el.textContent?.trim()}`;
      timeElementMap.set(key, el);
    });
  
    // Collect related keys to remove and elements to unselect
    const toRemoveKeys = new Set<string>();
    const removedSelections: HTMLElement[] = [];

    const firstSlots = getSingleOfPairedSlots('first');
    const secondSlots = getSingleOfPairedSlots('second');
  
    // Helper: Unselect related times if first slot is unselected
    firstSlots.forEach(({ day, time, related }) => {
      const dayTimeKey = `${day}-${time}`;
      const relatedKey = `${day}-${related}`;
      if (!selectedKeySet.has(dayTimeKey)) {
        toRemoveKeys.add(relatedKey);
        const el = timeElementMap.get(relatedKey);
        if (el) removedSelections.push(el);
      }
    });
  
    // Helper: Unselect first slot if related second slot is unselected
    secondSlots.forEach(({ day, time }) => {
      const dayTimeKey = `${day}-${time}`;
      if (!selectedKeySet.has(dayTimeKey)) {
        firstSlots.forEach(({ day: fDay, time: fTime, related }) => {
          if (fDay === day && related === time) {
            const relatedKey = `${fDay}-${fTime}`;
            toRemoveKeys.add(relatedKey);
            const el = timeElementMap.get(relatedKey);
            if (el) removedSelections.push(el);
          }
        });
      }
    });
  
    // Remove from selectedTimes
    selectedTimes = selectedTimes.filter(st => !toRemoveKeys.has(`${st.day}-${st.time}`));
    // Update selectedKeySet
    selectedKeySet = new Set(selectedTimes.map(st => `${st.day}-${st.time}`));
    // Unselect in UI
    if (removedSelections.length > 0) {
      ds?.removeSelection(removedSelections);
    }
  
    // Handle auto-selecting related slot
    const additionalSelections: HTMLElement[] = [];
    const invalidSelectedTimes = getInvalidSelectedTimes(selectedTimes, appointmentMethod);
  
    invalidSelectedTimes.forEach(({ related, day, validMethod, time }) => {
      const dayTimeKey = `${day}-${time}`;
      if (selectedKeySet.has(dayTimeKey)) {
        const found = selectedTimes.find(st => st.day === day && st.time === time);
        if (found && validMethod) {
          found.currentAM = validMethod;
        }
      }
  
      const relatedKey = `${day}-${related}`;
      if (!selectedKeySet.has(relatedKey)) {
        const el = timeElementMap.get(relatedKey);
        if (el) {
          if (validMethod) el.dataset.apm = validMethod;
          additionalSelections.push(el);
        }
      }
    });
  
    if (additionalSelections.length > 0) {
      ds?.addSelection(additionalSelections);
      const additionalSelectedTimes = parseSelectedValues(additionalSelections);
      selectedTimes = [...selectedTimes, ...additionalSelectedTimes];
    }
  
    handleTimeSelection(selectedTimes, appointmentMethod);
  };  

  const relatedTime = (currentTime: string) => {
    return timeArray[timeArray.indexOf(currentTime) + 1] || null
  }

  const getIndex = (currentTime: string) => {
    return timeArray.indexOf(currentTime)
  }

  const getCurrentAppointmentMethod = useCallback(
    (day: string, currentTime: string) => {
      const record = activeTimes[day] ? activeTimes[day].find((item) => item.time === currentTime) : null
      return record ? record?.appointmentMethod : null
    },
    [activeTimes],
  )

  const normalizeBothStateToOne = (selectionType: string) => {
    if (x.current) {
      ds?.addSelection(x.current)
      const selections = ds?.getSelection() as DSInputElement[]
      processAndSetTimeSelections(selections, selectionType)
      setOpenConfirmation(false)
      x.current = []
    }
  }

  useEffect(() => {
    appointmentMethodRef.current = appointmentMethod
  }, [appointmentMethod])

  useEffect(() => {
    activeTimesRef.current = activeTimes;
  }, [activeTimes])

  useEffect(() => {
    validateOfficeHourTimes()
  }, [activeTimes])

  
useEffect(() => {
  const finger = document.getElementById("walkthrough-finger");
  let clickAnimation: Animation | null = null;
  let step6Interval: NodeJS.Timeout | null = null;
  let isCancelled = false;

const updateFingerPosition = (rect: DOMRect) => {
  if (!finger) return;

  // Get finger dimensions
  const fingerWidth = finger.offsetWidth;
  const fingerHeight = finger.offsetHeight;

  // Center the finger horizontally and vertically over the slot
  const offsetTop = window.scrollY + rect.top + rect.height / 2 - fingerHeight / 2;
  const offsetLeft = window.scrollX + rect.left + rect.width / 2 - fingerWidth / 2;

  finger.style.top = `${offsetTop}px`;
  finger.style.left = `${offsetLeft}px`;
};



  const getExactSlot = (day: string, time: string): HTMLElement | null => {
    const days = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
    const dayIndex = days.indexOf(day.toLowerCase());
    const rows = document.querySelectorAll("tr.selector");

    for (const row of rows) {
      const cells = row.querySelectorAll("td");
      const cell = cells[dayIndex];
      if (!cell) continue;

      const timeDiv = cell.querySelector(".time") as HTMLElement | null;
      if (
        timeDiv &&
        timeDiv.textContent?.trim() === time &&
        timeDiv.dataset.day?.toLowerCase() === day.toLowerCase()
      ) {
        return timeDiv;
      }
    }
    return null;
  };

  const clearDemoHighlights = () => {
    document.querySelectorAll(".time.highlighted-demo").forEach(el => {
      el.classList.remove("highlighted-demo");
    });
  };

  const highlightStep5 = () => {
    const slot = getExactSlot("monday", "9:00 AM");
    const relatedSlot = getExactSlot("monday", "9:30 AM");

    if (slot && relatedSlot && finger) {
      clearDemoHighlights();
      const rect = slot.getBoundingClientRect();
      finger.style.display = 'block';
     updateFingerPosition(rect);

      clickAnimation = finger.animate(
        [{ transform: 'scale(1)' }, { transform: 'scale(0.8)' }, { transform: 'scale(1)' }],
        { duration: 500 }
      );

      clickAnimation.onfinish = () => {
        if (isCancelled) return;
        slot.classList.add("highlighted-demo");
        relatedSlot.classList.add("highlighted-demo");
      };
    }
  };

  const highlightStep6 = () => {
    const times = ["9:00 AM", "9:30 AM", "10:00 AM", "10:30 AM", "11:00 AM"];
    clearDemoHighlights();

    let i = 0;
    step6Interval = setInterval(() => {
      if (i >= times.length || isCancelled) {
        clearInterval(step6Interval!);
        step6Interval = null;
        return;
      }

      const el = getExactSlot("monday", times[i]);
      if (el) {
        el.classList.add("highlighted-demo");
        const rect = el.getBoundingClientRect();
        if (finger) {
          finger.style.display = 'block';
        updateFingerPosition(rect);
        }
      }
      i++;
    }, 250);
  };

  if (stepIndex === 3) {
    highlightStep5();
    loopTimerRef.current = setInterval(() => {
      if (!isCancelled) highlightStep5();
    }, 2500);
  }

  if (stepIndex === 4) {
    highlightStep6();
    loopTimerRef.current = setInterval(() => {
      if (!isCancelled) highlightStep6();
    }, 3000);
  }

  return () => {
    isCancelled = true;
    if (clickAnimation) clickAnimation.cancel();
    if (loopTimerRef.current) {
      clearInterval(loopTimerRef.current);
      loopTimerRef.current = null;
    }
    if (step6Interval) {
      clearInterval(step6Interval);
      step6Interval = null;
    }
    if (finger) finger.style.display = 'none';
    clearDemoHighlights();
  };
}, [stepIndex]);


  useEffect(() => {
    if (!selectedTimezone) {
      const exists = US_TIMEZONES.find(tz => tz.value === defaultTimezone);
      setSelectedTimezone(exists ? defaultTimezone : US_TIMEZONES[0].value);
    }
    if (activeTimes && Object.keys(activeTimes).length > 0) {
      // Check if any times are in the earlier range
      const hasEarlierTimes = Object.values(activeTimes).some((dayTimes) =>
        dayTimes.some((timeObj) => timeArray.slice(0, 10).includes(timeObj.time)),
      )
      setShowEarlierTimes(hasEarlierTimes)

      // Check if any times are in the later range
      const hasLaterTimes = Object.values(activeTimes).some((dayTimes) =>
        dayTimes.some((timeObj) => timeArray.slice(-11).includes(timeObj.time)),
      )
      setShowLaterTimes(hasLaterTimes)
    }
  }, [setSelectedTimezone, setShowEarlierTimes, setShowLaterTimes])

  useEffect(() => {
    const areaElement = document.getElementById("drag-select-area")
    if (areaElement) {
      // Make sure the button is not selectable by DragSelect
      const timeElements = Array.from(document.getElementsByClassName("time")).filter(el => {
        // Exclude any elements that are children of the show-earlier-times-btn
        return !el.closest('.show-earlier-times-btn');
      }) as DSInputElement[];
      
      ds = new DragSelect({
        selectables: timeElements,
        multiSelectMode: true,
        area: areaElement,
        multiSelectToggling: true,
      })

      ds.subscribe("DS:unselect", ({ items }) => {
        if (items.length === 1) {
          removeAllSlotFlagRef.current = true
        }
      })
      ds.subscribe("DS:select", ({ item }) => {
        if (appointmentMethodRef.current === "in-person-and-telehealth") {
          if (!x.current) {
            x.current = [item]
          } else {
            x.current = [...x.current, item]
          }
        }
      })

      ds.subscribe("DS:end", ({ items }) => {
        if (items.length === 0 && removeAllSlotFlagRef.current === false) return
        const filtered = items.filter((el) => !x.current.includes(el))

        if (x.current && x.current.length) {
          ds?.removeSelection(x.current)
          setOpenConfirmation(true)
        }
        processAndSetTimeSelections(filtered, appointmentMethodRef.current as string)
        removeAllSlotFlagRef.current = false
      })

      if (activeTimes && Object.keys(activeTimes).length > 0) {
        const timeElements = document.getElementsByClassName("time")
        const timesToSelect = []

        for (let i = 0; i < timeElements.length; i++) {
          const timeElement = timeElements[i] as HTMLElement
          const time = timeElement.textContent
          const day = timeElement.dataset.day

          // Check if this day and time combination exists in activeTimes
          if (day && time && activeTimes[day] && activeTimes[day].some((timeObj) => timeObj.time === time)) {
            timesToSelect.push(timeElement)
          }
        }

        ds.setSelection(timesToSelect)
        x.current = [];
      }

      return () => ds?.stop() // Clean up DragSelect instance
    }
  }, [])

  const useWheelLock = () => {
    const scrollRef = useRef<HTMLDivElement>(null);
  
    useEffect(() => {
      const el = scrollRef.current;
      if (!el) return;
  
      const handleWheel = (e: WheelEvent) => {
        const deltaY = e.deltaY;
        const scrollTop = el.scrollTop;
        const scrollHeight = el.scrollHeight;
        const clientHeight = el.clientHeight;
  
        const isScrollingDown = deltaY > 0;
        const isAtTop = scrollTop === 0;
        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1;
  
        if (
          (isScrollingDown && !isAtBottom) ||
          (!isScrollingDown && !isAtTop)
        ) {
          // Allow internal scroll but block outer page scroll
          e.preventDefault();
          el.scrollTop += deltaY;
        }
      };
  
      el.addEventListener("wheel", handleWheel, { passive: false });
  
      return () => {
        el.removeEventListener("wheel", handleWheel);
      };
    }, []);
  
    return scrollRef;
  };
  
  const scrollRef = useWheelLock();

  // const handleScroll: React.WheelEventHandler<HTMLDivElement> = (e) => {
  //   const el = e.currentTarget;
  //   const atTop = el.scrollTop === 0;
  //   const atBottom = el.scrollHeight - el.scrollTop === el.clientHeight;
  
  //   if ((e.deltaY < 0 && atTop) || (e.deltaY > 0 && atBottom)) {
  //     e.preventDefault();      
  //     e.stopPropagation();
  //   }
  // };

  return (
    <>
      <Joyride
      steps={steps}
      run={run}
      stepIndex={stepIndex}
      callback={handleJoyrideCallback}
     tooltipComponent={(props) => (
        <WalkThrough {...props} closeButtonHandler={handleClose} />
      )}
      showSkipButton={false}
      showProgress={false}
      disableOverlayClose
      continuous={true}
       scrollToFirstStep={true}
       disableScrolling={true} 
      styles={{
        options: {
          zIndex: 10000,
          arrowColor: '#fff'
        },
      }}    
      />
      <div className="office-hours-ui">
        <div className="flex flex-col md:flex-row md:items-start md:justify-between md:gap-8 mt-4">

  <div className="appointment-type w-full md:w-auto">
    <h1 className="text-base sm:text-lg font-semibold text-therapy-blue-dark flex items-center gap-2 flex-wrap">
      <span>Select Appointment Types Offered</span>
      <i
        className="fa-solid fa-info-circle cursor-pointer"
        style={{ fontSize: "20px" }}
        onClick={() => appointmentMethodsInfoState.open()}
      ></i>
    </h1>
    <div className="inline-flex gap-4 mt-2 p-5 border rounded-lg flex-wrap md:flex-nowrap md:w-auto">
      <button
        className={`${
          appointmentMethod === "in-person-and-telehealth"
            ? "bg-therapy-both-tele-and-in-person text-white"
            : "bg-gray-200 text-black"
        } px-6 py-2 text-sm md:text-base`}
        onClick={() => {
          if (appointmentMethod !== "in-person-and-telehealth") {
            handleAppointmentMethodChange("in-person-and-telehealth");
          }
        }}
      >
        Both In-Person & Telehealth
      </button>
      <button
        className={`${
          appointmentMethod === "in-person" || appointmentMethod === "in-person-and-telehealth"
            ? "bg-therapy-blue-dark text-white"
            : "bg-gray-200 text-black"
        } px-6 py-2 text-sm md:text-base`}
        onClick={() => {
          if (appointmentMethod !== "in-person") {
            handleAppointmentMethodChange("in-person");
          }
        }}
      >
        In-Person
      </button>
      <button
        className={`${
          appointmentMethod === "telehealth" || appointmentMethod === "in-person-and-telehealth"
            ? "bg-therapy-telehealth text-white"
            : "bg-gray-200 text-black"
        } px-6 py-2 text-sm md:text-base`}
        onClick={() => {
          if (appointmentMethod !== "telehealth") {
            handleAppointmentMethodChange("telehealth");
          }
        }}
      >
        Telehealth
      </button>
    </div>
  </div>

  <div className="timezone-select w-full md:w-[300px] mt-4 md:mt-0">
    <NormalSelectComponent
      label="Confirm Your Office Time Zone"
      labelClass="text-base sm:text-lg font-semibold text-therapy-blue-dark"
      required
      id="timezone-select"
      value={selectedTimezone || ""}
      onChange={(value) => {
        convertActiveTimes(selectedTimezone as string, value as string);
        setSelectedTimezone(value as string);
      }}
      options={usTimezones}
    />
  </div>

 
    <div className="w-full md:w-[200px] mt-6 md:mt-6 flex justify-start md:justify-end">
       {showBtn && (
      <SaveButton       
        value="How to use this page?"
        onClick={() => {
          setStepIndex(0);
          setRun(true);

        // Outer scroll reset
        const outer = document.getElementById("scroller-div");
        if (outer) {
          outer.scrollTo({ top: 0, behavior: 'auto' });
        } else {
          // Fallback: scroll window if outer container not found
          window.scrollTo({ top: 0, behavior: 'auto' });
        }

        // Inner scroll reset (time slot grid)
        const inner = document.getElementById("time-scroll-container");
        if (inner) {
          inner.scrollTo({ top: 0, behavior: 'auto' });
        }

        //Reset view
        setShowEarlierTimes(false);
        setEarlierTimeSlice(18);
        setShowLaterTimes(false);
        setLaterTimeIncrement(0);
      }}
      />
        )}
    </div>

</div>

        <hr className="mt-6" />
        <br />
        <br />
        <div className="time-select-options overflow-x-auto relative">
          {/* Floating button positioned absolutely - hidden when any modal is open */}
          <div 
            style={{
              position: 'absolute',
              top: '46px', // Position it where the original button was
              left: '0',
              width: '100%',
              textAlign: 'center',
              zIndex: 100, // Very high z-index
              pointerEvents: 'none', // Initially none to let events pass through
              display: (
                noHoursState.isOpen || 
                infoState.isOpen || 
                changeAppointmentMethodState.isOpen || 
                appointmentMethodsInfoState.isOpen || 
                invalidHoursState.isOpen || 
                openConfirmation
              ) ? 'none' : 'block', // Hide when any modal is open
            }}
          >
            <button
              onClick={() => {
                if (showEarlierTimes && earlierTimeSlice === 0) {
                  setShowEarlierTimes(false);
                  setEarlierTimeSlice(18); // Reset to 9am
                } else if (showEarlierTimes) {
                  setEarlierTimeSlice(Math.max(earlierTimeSlice - 6, 0));
                } else {
                  setShowEarlierTimes(true);
                  setEarlierTimeSlice(12); // Show 6am-9am (indices 12-17)
                }
              }}
              className="text-therapy-blue font-semibold cursor-pointer inline-block text-sm md:text-base px-3 py-1"
              style={{
                pointerEvents: 'auto', // Make only the button clickable
              }}
            >
              {showEarlierTimes && earlierTimeSlice === 0 ? (
                <>
                  Hide Earlier Times <i className="fa-solid fa-chevron-up" style={{ fontSize: 12 }}></i>
                </>
              ) : (
                <>
                  Show Earlier Times <i className="fa-solid fa-chevron-down" style={{ fontSize: 12 }}></i>
                </>
              )}
            </button>
          </div>
          
          <div
            id="time-scroll-container" 
            ref={scrollRef}
            className="max-h-[500px] overflow-y-auto overscroll-contain"
            // onWheel={handleScroll}
            // style={{ overscrollBehavior: 'contain' }}
          >
            <table  ref={tableRef} cellSpacing="20" className="min-w-full" id="drag-select-area">
              <thead className="office-hours">
                <tr>
                  {days.map((day) => (
                    <th key={day} className="text-xs sm:text-sm md:text-base sticky top-0 bg-white z-10 py-2">
                      {day.charAt(0).toUpperCase() + day.slice(1)}
                    </th>
                  ))}
                </tr>
                {/* Empty row for spacing where the button would be */}
                <tr>
                  <td colSpan={7} className="text-center py-2 sticky top-8 bg-white z-10 border-b">
                    <div style={{ height: '36px' }}></div>
                  </td>
                </tr>
              </thead>
              <tbody>
                        {/* Earlier times (before 9 AM) - only shown when "Show earlier times" is clicked */}
                        {timeArray.slice(0, 18).map((time, index) => (
                          <tr
                            key={time}
                            className="selector"
                            style={{ display: !showEarlierTimes || index < earlierTimeSlice ? "none" : "table-row" }}
                          >
                            {days.map((day, i) => (
                              <td key={i}>
                                <div
                                  className={`time ${getActiveClass(day, time)}`}
                                  data-day={day}
                                  data-related={relatedTime(time)}
                                  data-order={getIndex(time)}
                                  data-serial={getCurrentAppointmentMethod(day, time)}
                                  data-apm={getCurrentAppointmentMethod(day, time)}
                                >
                                  {time}
                                </div>
                                {daysWithError[day]?.invalidTimes?.includes(time) && (
                                  // <div className="text-danger text-sm text-center">Error - must select a full hour slot</div>
                                  <div className="text-danger text-sm text-center">Invalid Slot</div>
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}

                        {/* Default visible times (9 AM to 6:30 PM) */}
                        {timeArray.slice(18, getDefaultEndTimeIndex() + 1).map((time, index) => (
                          <tr key={time} id={`drag-select-area-${index}`} className={`selector ${index < 1 ? 'office-hours' : ''}`}>
                            {days.map((day, i) => (
                              <td key={i}>
                                <div
                                  className={`time ${getActiveClass(day, time)}`}
                                  data-day={day}
                                  data-related={relatedTime(time)}
                                  data-order={getIndex(time)}
                                  data-apm={getCurrentAppointmentMethod(day, time)}
                                >
                                  {time}
                                </div>
                              
                                {daysWithError[day]?.invalidTimes?.includes(time) && (
                                  <div className="text-danger text-xs text-center">Select Full Hour</div>                                
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}

                        {/* Later times (after 6:30 PM) - only shown when "Show later times" is clicked */}
                        {timeArray.slice(getDefaultEndTimeIndex() + 1).map((time, index) => (
                          <tr
                            key={time}
                            className="selector"
                            style={{ 
                              display: !showLaterTimes || index >= laterTimeIncrement * 6 ? "none" : "table-row" 
                            }}
                          >
                            {days.map((day, i) => (
                              <td key={i}>
                                <div
                                  className={`time ${getActiveClass(day, time)}`}
                                  data-day={day}
                                  data-related={relatedTime(time)}
                                  data-order={getIndex(time)}
                                  data-apm={getCurrentAppointmentMethod(day, time)}
                                >
                                  {time}
                                </div>
                                {daysWithError[day]?.invalidTimes?.includes(time) && (
                                  // <div className="text-danger text-sm text-center">Error - must select a full hour slot</div>
                                  <div className="text-danger text-sm text-center">Invalid Slot</div>
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
          
          <div className="text-center mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation();
                if (showLaterTimes) {
                  // If already showing later times, check which increment we're at
                  if (laterTimeIncrement >= 2) {
                    // If at maximum increment (11:30 PM), hide all later times
                    setShowLaterTimes(false);
                    setLaterTimeIncrement(0);
                  } else {
                    // Otherwise, increment by 3 hours (6 slots)
                    setLaterTimeIncrement(laterTimeIncrement + 1);
                    
                    // Auto-scroll to show newly displayed times
                    setTimeout(() => {
                      const container = document.querySelector('.max-h-[500px]');
                      if (container) {
                        container.scrollTop = container.scrollTop + 180; // Scroll down to show new times
                      }
                    }, 100);
                  }
                } else {
                  // First click: Show times up to 9:30 PM
                  setShowLaterTimes(true);
                  setLaterTimeIncrement(1);
                  
                  // Auto-scroll to show newly displayed times
                  setTimeout(() => {
                    const container = document.querySelector('.max-h-[500px]');
                    if (container) {
                      container.scrollTop = container.scrollTop + 180; // Scroll down to show new times
                    }
                  }, 100);
                }
              }}
              onTouchStart={(e) => {
                e.stopPropagation();
                if (showLaterTimes) {
                  // If already showing later times, check which increment we're at
                  if (laterTimeIncrement >= 2) {
                    // If at maximum increment (11:30 PM), hide all later times
                    setShowLaterTimes(false);
                    setLaterTimeIncrement(0);
                  } else {
                    // Otherwise, increment by 3 hours (6 slots)
                    setLaterTimeIncrement(laterTimeIncrement + 1);
                    
                    // Auto-scroll to show newly displayed times
                    setTimeout(() => {
                      const container = document.querySelector('.max-h-[500px]');
                      if (container) {
                        container.scrollTop = container.scrollTop + 180; // Scroll down to show new times
                      }
                    }, 100);
                  }
                } else {
                  // First click: Show times up to 9:30 PM
                  setShowLaterTimes(true);
                  setLaterTimeIncrement(1);
                  
                  // Auto-scroll to show newly displayed times
                  setTimeout(() => {
                    const container = document.querySelector('.max-h-[500px]');
                    if (container) {
                      container.scrollTop = container.scrollTop + 180; // Scroll down to show new times
                    }
                  }, 100);
                }
              }}
              className="text-therapy-blue font-semibold cursor-pointer inline-block text-sm md:text-base px-3 py-1 relative"
              style={{ border: 'none', background: 'transparent', zIndex: 20 }}
            >
              {showLaterTimes ? (
                laterTimeIncrement >= 2 ? (
                  <>
                    Hide Later Times <i className="fa-solid fa-chevron-up" style={{ fontSize: 12 }}></i>
                  </>
                ) : (
                  <>
                    Show More Times <i className="fa-solid fa-chevron-down" style={{ fontSize: 12 }}></i>
                  </>
                )
              ) : (
                <>
                  Show Later Times <i className="fa-solid fa-chevron-down" style={{ fontSize: 12 }}></i>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    <div
      id="walkthrough-finger"
      style={{
        position: 'fixed',
        fontSize: '28px',
        zIndex: 999999,
        pointerEvents: 'none',
        transition: 'top 0.3s ease, left 0.3s ease',
        display: 'none',
      }}
    >
      👆
    </div>


      <div className="mt-10 mb-5">
        <SaveButton className="save-btn"
          loading={authStore.registration.savingTherapistInfo}
          disabled={shouldDisableBtn && (!activeTimes || Object.keys(activeTimes).length === 0 || Object.keys(daysWithError).length > 0)}
          value={btnLabel}
          onClick={(e) => {
            if ((!activeTimes || Object.keys(activeTimes).length === 0) && !isNoHoursErrorShown) {
              noHoursState.open()
              setIsNoHoursErrorShown(true)
              return
            }
            if (Object.keys(daysWithError).length > 0 && !isInvalidHoursErrorShown) {
              invalidHoursState.open()
              setIsInvalidHoursErrorShown(true)
              return
            }
            const isPageValid = appointmentMethod && activeTimes && Object.keys(activeTimes).length > 0 && Object.keys(daysWithError).length === 0 ? true : false;
            onSave({ e, isPageValid })
          }}
        />
      </div>

      <NormalDialogV2
        opacity={80}
        open={openConfirmation}
        title="My availability is..."
        onClose={() => {
          setOpenConfirmation(false)
          x.current = []
        }}
        width="w-[350px]"
      >
        <div className="flex flex-col gap-4 p-4 items-center">
          <button
            className="bg-therapy-blue-dark text-white px-8 py-3 rounded-full font-semibold w-64 hover:scale-105 hover:opacity-80 transition-all"
            onClick={() => {
              normalizeBothStateToOne("in-person")
            }}
          >
            In-Person
          </button>
          <button
            className="bg-therapy-telehealth text-white px-8 py-3 rounded-full font-semibold w-64 hover:scale-105 hover:opacity-80 transition-all"
            onClick={() => {
              normalizeBothStateToOne("telehealth")
            }}
          >
            Telehealth
          </button>
          <button
            className="bg-therapy-both-tele-and-in-person text-white px-8 py-3 rounded-full font-semibold w-64 hover:scale-105 hover:opacity-80 transition-all"
            onClick={() => {
              normalizeBothStateToOne("in-person-and-telehealth")
            }}
          >
            In-Person or Telehealth
          </button>
          <button
            className="border border-gray-400 text-gray-600 px-8 py-3 rounded-full font-semibold w-64 hover:scale-105 hover:opacity-80 transition-all"
            onClick={() => {
              setOpenConfirmation(false)
              x.current = []
            }}
          >
            None
          </button>
        </div>
      </NormalDialogV2>

      <NormalDialog
        opacity={80}
        state={noHoursState}
        title="Error"
        onClose={() => noHoursState.close()}
        width="w-[500px] max-w-[95vw]"
        titleCentered={true}
      >
        <p className="text-sm">
          Please select your office hours by interacting with the time slots on this page. You can
          either click individual slots to select them one by one, or click and drag across multiple slots to select a range at once.
        </p>
      </NormalDialog>

      <NormalDialog
        opacity={80}
        state={invalidHoursState}
        title="Error"
        onClose={() => invalidHoursState.close()}
        width="w-[400px] max-w-[95vw]"
        titleCentered={true}
      >
        <p className="text-sm">
          Available hours must be booked in 1 hour increments. Please ensure that all blocks are available in full hour
          increments.
          <br />
          <br />
          *Note: if you start your available block on the hour all appointments in that block will be booked from the{" "}
          top of the hour only. If the block starts on the half-hour, all appointments in that block will be booked from
          the bottom of the hour only.
          <br />
          <br />
          (e.g. Normal office hours blocked from 9-12pm will allow clients to schedule appointments at 9 am, 10am, and
          11am only. Normal office hours blocked from 2:30-5:30pm will allow clients to schedule appointments at 2:30pm,
          3:30pm and 4:30pm only.)
        </p>
      </NormalDialog>

      <NormalDialog
        opacity={80}
        state={infoState}
        title="Note"
        onClose={() => infoState.close()}
        width="w-[700px] max-w-[95vw]"
        titleCentered={true}
      >
        <p className="text-sm">
          Your normal working hours should reflect your typical working days and times. If you schedule changes on a
          1-off basis do not change your normal working hours with NextTherapist. Instead ensure that your work calendar
          has blocked these hours as "busy" so that our system will not book clients during those normal working hours.
          <br />
          <br />
          Examples:
          <br />
          Going on vacation – block the entire day during your trip,
          <br />
          Leaving office early to attend a child's event – block the hours you will be out of the office,
          <br />
          Coming into the office late due to a dentist appointment – block the hours you will not be in office.
        </p>
      </NormalDialog>

      <FormDialog
        state={changeAppointmentMethodState}
        title="Change appointment method"
        onCancel={() => {
          setNewAppMethod(null)
          changeAppointmentMethodState.close()
        }}
        onSubmit={(e) => {
          e.preventDefault()
          if (!newAppMethod) {
            changeAppointmentMethodState.close()
            return
          }
          setAppointmentMethod(newAppMethod as string)
          const updatedTimes = Object.keys(activeTimes).reduce(
            (acc, atKey) => {
              acc[atKey] = activeTimes[atKey].map((t) => ({
                ...t,
                appointmentMethod: newAppMethod as string,
              }))
              return acc
            },
            {} as typeof activeTimes,
          )

          setActiveTimes(updatedTimes)
          setNewAppMethod(null)
          changeAppointmentMethodState.close()
        }}
        confirmLabel="Continue"
        width="w-[500px] max-w-[95vw]"
      >
        <p className="text-sm">
          You have selected office hours for previous appointment method. Changing appointment method will update the
          appointment method of those office hours as well. Do you want to continue?
        </p>
      </FormDialog>

      <NormalDialog
        opacity={80}
        state={appointmentMethodsInfoState}
        title="Note"
        onClose={() => appointmentMethodsInfoState.close()}
        width="w-[90vw] sm:w-[500px] md:w-[600px] lg:w-[700px]"
        titleCentered={true}
      >
        <p className="text-xs sm:text-sm">
          Select the type of appointments that you allow in your practice as follows:
          <br />
          <br />
          <strong>Both In-Person & Telehealth</strong> - this indicates that you accept patients via In-Person AND Telehealth.
          You will be able to decide for each block of time you select in the calendar below if you want to allow
          either In-Person Only, Telehealth Only, or if you are flexible to allow the patient to choose if they want
          to do In-Person or Telehealth during that block of time.
          <br />
          <br />
          <strong>In-Person Only</strong> - this indicates that you only take patients In-Person in your physical office.
          <br />
          <br />
          <strong>Telehealth Only</strong> - this indicates that you only take patients via Telehealth and that you will provide
          them a link to join your appointments via video conference.
        </p>
      </NormalDialog>      
    </>
  )
}

export default OfficeHoursComponent

