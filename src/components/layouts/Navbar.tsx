import { FC } from "react";
import { useAppDispatch } from "@/store/hooks";
import { toggleSidebar } from "@/store/slicers/sidebar.slicer";

const Navbar: FC = () => {
  const dispatch = useAppDispatch();
  return (
    <div role="navigation" className="px-4 py-2 bg-yellow-500 flex-none flex justify-between items-center">
      <div className="flex flex-row">
        <div className="text-white">
          <i
            role="button"
            className="cursor-pointer fas fa-bars text-white md:invisible"
            onClick={() => dispatch(toggleSidebar())}
          ></i>
        </div>
        <div></div>
      </div>
      <div className="gap-2 flex items-center">
      </div>
    </div>
  );
};

export default Navbar;
