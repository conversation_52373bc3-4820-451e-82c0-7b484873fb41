import { Route, Routes, useNavigate } from "react-router-dom";
import PerfectScrollbar from "react-perfect-scrollbar";
import "react-perfect-scrollbar/dist/css/styles.css";
import { lazy, Suspense, useRef } from "react";
import { useAppSelector } from "@/store/hooks";
import RegisterTherapistSidebar from "./RegisterTherapistSidebar";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store/index";
import NormalDialogV2 from "../dialogs/NormalDialogV2";
import { resetRegistration, updateRegistrationState } from "@/store/slicers/auth.slicer";
import { registerTherapistAction } from "@/store/slicers/therapist.slicer";
import notification from "@/utils/notification.util";
import { resetSidebarState } from "@/store/slicers/sidebar.slicer";

const RegisterCreateAccountPage = lazy(() => import("@/pages/auth/registration/create-account.page"));
const RegisterLicensePage = lazy(() => import("@/pages/auth/registration/license.page"));
const RegisterProfilePage = lazy(() => import("@/pages/auth/registration/profile.page"));
const RegisterPracticeInformation = lazy(() => import("@/pages/auth/registration/practice-info.page"));
const RegisterYourSpecialties = lazy(() => import("@/pages/auth/registration/specialization.page"));
const RegisterRankSubSpecialties = lazy(() => import("@/pages/auth/registration/rank-sub-specialties.page"));
const RegisterModalities = lazy(() => import("@/pages/auth/registration/modalities.page"));
const RegisterPaymentOption = lazy(() => import("@/pages/auth/registration/PaymentFormsPage"));
const RegisterNormalOfficeHours = lazy(() => import("@/pages/auth/registration/normal-office-hours.page"));
const RegisterNextTherapistPlan = lazy(() => import("@/pages/auth/registration/next-therapist-plan.page"));
const RegisterCalendarAdd = lazy(() => import("@/pages/auth/registration/calendar-add.page"));
const RegisterRankSpecialties = lazy(() => import("@/pages/auth/registration/rank-specialties.page"));
const RegisterPracticeFocus = lazy(() => import("@/pages/auth/registration/practice-focus.page"));
const RegisterWaitlistNotifications = lazy(() => import("@/pages/auth/registration/WaitlistNotificationsPage"));
const RegisterSuccessPage = lazy(() => import("@/pages/auth/registration/RegisterSuccessPage"));


const RegisterLayout = () => {
  const navigate = useNavigate();
  const scrollbarRef = useRef<PerfectScrollbar | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const auth = useAppSelector((state) => state.auth);

  const editFlag = useSelector((state: RootState) => state.auth.registration.editFlag); 
  const showRegCompleteDialog = auth.registration?.showRegCompleteDialog;
  const createAccPageInfo = auth.registration.formContent['create-account'];

  return (
    <section className="h-full w-full flex flex-col lg:flex-row">
      {/* Loader */}
      {(auth.registration.savingTherapistInfo === true) && (
        <div id="loader" data-testid="loader" className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
          <div role="status">
            <svg
              aria-hidden="true"
              className="inline w-10 h-10 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )}
      {/* Sidebar */}

      {!editFlag && <RegisterTherapistSidebar />}

      <div className="flex-grow w-full h-full flex flex-col muller">
        <PerfectScrollbar id="scroller-div" ref={scrollbarRef}>
          <Suspense fallback={<div className="loading-suspence"></div>}>
            <Routes>
              <Route path="create-account" key="register:create-account" element={<RegisterCreateAccountPage />} />
              <Route path="license" key="register:license" element={<RegisterLicensePage />} />
              <Route path="waitlist-notifications" key="register:wn" element={<RegisterWaitlistNotifications />} />
              <Route
                path="practice-info"
                key="register:practice-info"
                element={<RegisterPracticeInformation />}
              />
              <Route path="specialization" key="register:specialization" element={<RegisterYourSpecialties />} />
              <Route path="rank-specialties" key="register:rank-specialties" element={<RegisterRankSpecialties />} />
              <Route
                path="rank-sub-specialties"
                key="register:rank-sub-specialties"
                element={<RegisterRankSubSpecialties />}
              />
              <Route
                path="sub-specialties"
                key="register:sub-specialties"
                element={
                  <Suspense fallback={<div>Loading...</div>}>
                    <RegisterRankSubSpecialties />
                  </Suspense>
                }
              />
              <Route path="modalities" key="register:modalities" element={<RegisterModalities />} />

              <Route path="practice-focus" key="register:practice-focus" element={<RegisterPracticeFocus />} />
              <Route path="payment-forms" key="register:payment-forms" element={<RegisterPaymentOption />} />
              <Route
                path="normal-office-hours"
                key="register:normal-office-hours"
                element={<RegisterNormalOfficeHours />}
              />
              <Route
                path="next-therapist-plan"
                key="register:next-therapist-plan"
                element={<RegisterNextTherapistPlan />}
              />
              <Route path="calendar-add" key="register:calendar-add" element={<RegisterCalendarAdd />} />
              <Route path="profile" key="register:profile" element={<RegisterProfilePage />} />
              <Route path="success" key="register:success" element={<RegisterSuccessPage />} />

            </Routes>
          </Suspense>
        </PerfectScrollbar>
      </div>

      <NormalDialogV2
        opacity={80}
        open={showRegCompleteDialog || false}
        title="Complete Registration?"
        confirmLabel="Submit"
        width="w-[500px]"
        primaryButtonColor={true}
        onAccept={async () => {
          const userToken = auth.registration?.userRegistrationToken;
          if (!userToken) {
            notification.error("User Token is required")
            return
          }

          const profileData = auth.registration.formContent['profile'];
          const res = await dispatch(registerTherapistAction(
            { values: profileData, isRegComplete: true },
            'profile', 
            userToken
          ));
          if (res?.status === 200 || res?.status === 201) {
            dispatch(resetRegistration());
            dispatch(resetSidebarState());
            navigate(`/auth/register/success?email=${createAccPageInfo?.email}&success=true`);
          }
        }}
        onCancel={() => {
          dispatch(updateRegistrationState({ key: "showRegCompleteDialog", value: false }));
          dispatch(updateRegistrationState({ key: "shouldShowRegCompleteDialog", value: false }));
        }}
      >
        <p className="text-sm">
          Congratulations! Your application is complete. If you are ready to submit please click the button below to
          move to your Therapist Portal.
        </p>
      </NormalDialogV2>
    </section>
  );
};

export default RegisterLayout;
