import { FC, useEffect } from "react";
import { Link, NavLink, useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { hideSidebar, updateSidebarState } from "@/store/slicers/sidebar.slicer";
import { Menu } from "@/types/menu.interface";
import { LOGO_PATH } from "../../../src/pages/auth/constants";

const RegisterTherapistSidebar: FC = () => {
	const dispatch = useAppDispatch();
  const location = useLocation();
	const open = useAppSelector((state) => state.sidebar.on);

  const sidebarState = useAppSelector((state) => state.sidebar);

  const toggleSubmenu = (menuLabel: string) => {
    const isOpen = sidebarState?.registerTherapist?.openSubMenu[menuLabel];
    dispatch(updateSidebarState({ key: 'registerTherapist', value: { ...sidebarState.registerTherapist, openSubMenu: { [menuLabel]: !isOpen } } }));
  };

	const menus = location.pathname === '/auth/register' || location.pathname === '/auth/register/create-account' || location.pathname === '/auth/register/create-account/'
    || location.pathname === '/auth/register/success' || location.pathname === '/auth/register/success/' ?
    [] : [
      {
        label: "License",
        pathName: "license",
        icon: {
          active: "fa-sharp fa-regular fa-shield-check",
          inactive: "fa-sharp fa-regular fa-shield-check",
        },
        link: "/auth/register/license",
      },
      {
        label: "Specialities",
        // pathName: "specialization",
        icon: {
          active: "fa-regular fa-user-gear",
          inactive: "fa-regular fa-user-gear",
        },
        link: "/auth/register/specialization",
        subMenu: [
          {
            label: "Your Specialties",
            pathName: "specialization",
            link: "/auth/register/specialization",
          },
          {
            label: "Rank Specialties",
            pathName: "rank-specialties",
            link: "/auth/register/rank-specialties"
          },
          {
            label: "Rank Sub-Specialties",
            pathName: "rank-sub-specialties",
            link: "/auth/register/rank-sub-specialties"
          },
          {
            label: "Modalities",
            pathName: "modalities",
            link: "/auth/register/modalities"
          },
          {
            label: "Practice Focus",
            pathName: "practice-focus",
            link: "/auth/register/practice-focus"
          },
        ],
      },
      {
        label: "Practice",
        icon: {
          active: "fa-solid fa-shop",
          inactive: "fa-solid fa-shop",
        },
        link: "/auth/register/practice-info",
        subMenu: [
          {
            label: "Practice Information",
            pathName: "practice-info",
            link: "/auth/register/practice-info",
          },
        ],
      },
      {
        label: "Calendar",
        icon: {
          active: "fa-solid fa-calendar",
          inactive: "fa-solid fa-calendar",
        },
        link: "/auth/register/normal-office-hours",
        subMenu: [
          {
            label: "Office Hours",
            pathName: "normal-office-hours",
            link: "/auth/register/normal-office-hours",
          },
          {
            label: "Waitlist & Notifications",
            pathName: "waitlist-notifications",
            link: "/auth/register/waitlist-notifications",
          },
          {
            label: "Connect your Calendar",
            pathName: "calendar-add",
            link: "/auth/register/calendar-add",
          }
        ],
      },
      {
        label: "Payment",
        icon: {
          active: "fa-solid fa-credit-card",
          inactive: "fa-solid fa-credit-card",
        },
        // link: "/auth/register/banking-information",
        link: "/auth/register/next-therapist-plan",
        subMenu: [
          // {
          //   label: "Banking Information",
          //   pathName: "banking-information",
          //   link: "/auth/register/banking-information",
          // },
          {
            label: "Client Payment Options",
            pathName: "payment-forms",
            link: "/auth/register/payment-forms"
          },
          {
            label: "Subscription",
            pathName: "next-therapist-plan",
            link: "/auth/register/next-therapist-plan",
          }
        ],
      },
      {
        label: "Profile",
        pathName: "profile",
        icon: {
          active: "fa-solid fa-user",
          inactive: "fa-regular fa-user",
        },
        link: "/auth/register/profile",
      },
    ];

  useEffect(() => {
    menus.forEach((menu) => {
      menu.subMenu?.forEach((sub) => {
        if (location.pathname.startsWith(sub.link)) {
          dispatch(updateSidebarState({
            key: 'registerTherapist',
            value: {
              ...sidebarState.registerTherapist,
              openSubMenu: { [menu.label]: true },
            },
          }));
        }
      });
    });
  }, []);

  return (
    <div data-testid="register-therapist-sidebar"
      style={{ borderRight: "0.07em solid rgb(204, 204, 204)" }}
      className={`bg-white flex-none flex flex-col fixed h-full lg:relative z-10 transition duration-300 md:w-[300px] ${
        open ? "w-[250px]" : "-translate-x-[250px] w-0 lg:translate-x-0"
      }`}
    >
      <div className="flex-none flex flex-col items-center py-4 justify-center relative">
        <img src={LOGO_PATH} alt="logo" className="w-[60%]" />
        <span data-testid="close-sidebar"
          className="absolute md:hidden top-1 -right-4 bg-white w-8 h-8 flex items-center justify-center rounded-full"
          onClick={() => dispatch(hideSidebar())}
        >
          <i className="fa-duotone fa-close fa-lg cursor-pointer"></i>
        </span>
   </div>
      <div className="flex flex-col flex-grow overflow-auto z-10">
      {menus.length > 0 && (
        <ul className="flex flex-col font-weight[300]">
          {menus.map((menu, index) => (
            <SidebarMenuItem
              key={index}
              menu={menu}
              locationPath={location.pathname}
              sidebarState={sidebarState}
              toggleSubmenu={toggleSubmenu}
              dispatch={dispatch}
            />
          ))}
        </ul>
      )}
      </div>
      <div className="flex flex-col items-center mb-8 text-gray-700 text-sm w-full px-4">
				<span>Already have an account?</span>
				<Link
					style={{ backgroundColor: "#431d42", color: "white" }}
					to="/auth/signin"
					className="mt-4 py-1 text-center border bg-white w-full rounded font-medium lg:w-3/4"
				>
					Log in
				</Link>
			</div>
    </div>
  );
};

interface SidebarMenuItemProps {
  menu: Menu;
  locationPath: string;
  sidebarState: any;
  toggleSubmenu: (label: string) => void;
  dispatch: any;
}

const SidebarMenuItem: FC<SidebarMenuItemProps> = ({
  menu,
  locationPath,
  sidebarState,
  toggleSubmenu,
  dispatch
}) => {
  const isSubmenuActive = menu.subMenu?.some((sub) => locationPath.startsWith(sub.link)) || false;
  const isOpen = sidebarState?.registerTherapist?.openSubMenu[menu.label] || false;
  const hasInvalidSubmenu = menu.subMenu
    ? menu.subMenu?.some((sub) =>
        sub.pathName && sidebarState?.registerTherapist?.invalidMenus?.includes(sub.pathName)
      )
    : false;

  return (
    <div className="flex flex-col">
      <MainNavLink
        menu={menu}
        isOpen={isOpen}
        isSubmenuActive={isSubmenuActive}
        hasInvalidSubmenu={hasInvalidSubmenu}
        toggleSubmenu={toggleSubmenu}
        dispatch={dispatch}
        sidebarState={sidebarState}
      />

      {menu.subMenu && isOpen && (
        <SubMenuList
          subMenu={menu.subMenu}
          sidebarState={sidebarState}
        />
      )}
    </div>
  );
};

const MainNavLink: FC<{
  menu: Menu;
  isOpen: boolean;
  isSubmenuActive: boolean;
  hasInvalidSubmenu: boolean;
  toggleSubmenu: (label: string) => void;
  dispatch: any;
  sidebarState: any;
}> = ({ menu, isOpen, isSubmenuActive, hasInvalidSubmenu, toggleSubmenu, dispatch, sidebarState }) => {
  const activeClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 bg-gray-100 rounded";
  const inactiveClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 hover:bg-gray-100";

  return (
    <div className="relative registration-therapy-sidebar">
      <NavLink
        to={menu.link || "#"}
        className={({ isActive }) =>
          (isActive || isOpen || isSubmenuActive ? activeClass : inactiveClass) + " py-5"
        }
        onClick={(e) => {
          if (menu.subMenu) {
            e.preventDefault();
            toggleSubmenu(menu.label);
          } else {
            dispatch(updateSidebarState({
              key: 'registerTherapist',
              value: { ...sidebarState.registerTherapist, openSubMenu: {} },
            }));
          }
        }}
      >
        {({ isActive }) => (
          <>
            <span
              className="w-8 text-center mr-2"
              style={{
                fontSize: "18px",
                color: isOpen || isActive || isSubmenuActive ? "black" : "#7E7778",
              }}
            >
              {menu.icon?.active && <i className={menu.icon.active}></i>}
            </span>
            <span
              style={{
                fontSize: "16px",
                fontWeight: 550,
                color: isOpen || isActive || isSubmenuActive ? "black" : "#7E7778",
              }}
            >
              {(hasInvalidSubmenu ||
                (menu.pathName &&
                  sidebarState?.registerTherapist?.invalidMenus?.includes(menu.pathName))) && (
                <i className="fa-solid fa-circle !text-red-500 mr-1 text-[8px]" />
              )}
              {menu.label}
            </span>
            {menu.subMenu && (
              <i
                className={`ml-auto mr-4 transition-transform ${
                  isOpen ? "rotate-180" : "rotate-0"
                } fa-solid fa-chevron-down`}
              ></i>
            )}
            
          </>
        )}
      </NavLink>
    </div>
  );
};

const SubMenuList: FC<{
  subMenu: Menu["subMenu"];
  sidebarState: any;
}> = ({ subMenu, sidebarState }) => {
  const activeClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 bg-gray-100 rounded";

  return (
    <div className="border-l border-gray-300">
      {subMenu?.map((sub, subIndex) => (
        <NavLink
          key={subIndex}
          to={sub.link}
          className={({ isActive }) =>
            activeClass + ` py-4 ${isActive ? " active-ts-sub-menu" : ""}`
          }
        >
          <>
            <span
              style={{
                fontSize: "14px",
                fontWeight: 550,
                color: "#7E7778",
                marginLeft: "40px",
              }}
            >
              {sub.pathName &&
                sidebarState?.registerTherapist?.invalidMenus?.includes(sub.pathName) && (
                  <i className="fa-solid fa-circle !text-red-500 mr-1 text-[8px]"></i>
                )}
              {sub.label}
            </span>
          </>
        </NavLink>
      ))}
    </div>
  );
};

export default RegisterTherapistSidebar;
