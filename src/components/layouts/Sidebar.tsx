import { Menu } from "@/types/menu.interface";
import { useEffect, FC, useState } from "react";
import { Link, NavLink } from "react-router-dom";
import { getFullName } from "@/utils/app.util";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { hideSidebar, resetSidebarState } from "@/store/slicers/sidebar.slicer";
import { resetRegistration, signOut } from "@/store/slicers/auth.slicer";
import TherapistRepository from "../../../src/repositories/TherapistRepository";
import toast from "react-hot-toast";
import { LOGO_PATH } from "../../../src/pages/auth/constants";
import { prepareMenus } from "@/utils/menu.util";

const Sidebar: FC = () => {
  const open = useAppSelector((state) => state.sidebar.on);
  const user = useAppSelector((state) => state.auth.user);
  const dispatch = useAppDispatch();
  const thRepo = new TherapistRepository();
  const [menus, setMenus] = useState<Menu[]>([]);

  const activeClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 bg-gray-100 rounded";
  const inactiveClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 hover:bg-gray-100";

  // Get count of unverified therapists
  const getUnverifiedCount = async (): Promise<number> => {
  try {
    const { meta } = await thRepo.getAll({ getUnverified: true });
    return meta?.total ?? 0;
  } catch (err) {
    console.error("Failed to fetch unverified count:", err);
    return 0;
  }
};

  useEffect(() => {
    const createMenus = async () => {
      const unverifiedCount = await getUnverifiedCount();
      const items = prepareMenus(user?.role || "", unverifiedCount);
      setMenus(items);
    };
    createMenus();
  }, [user]);

  const getIconClass = (isActive: boolean, icon: { active: string; inactive: string }) => {
    return isActive ? icon.active : icon.inactive;
  };

 const renderMenuTitle = (label: string, index: number) => (
  <span key={index} className="pl-2 border-l-2 border-transparent">
    <span className="text-sm font-thin uppercase tracking-wide">{label}</span>
  </span>
);

const renderMenuSpacer = (index: number) => (
  <span key={index} className="border-t border-gray-200 my-2"></span>
);

const renderMenuLink = (menu: Menu, index: number) => (
  <NavLink
    onClick={() => dispatch(hideSidebar())}
    key={index}
    to={menu.link!}
    className={({ isActive }) => (isActive ? activeClass : inactiveClass)}
  >
    {({ isActive }) => (
      <>
        <span className="w-8 text-center">
          {menu.icon?.active && menu.icon?.inactive && (
            <i className={getIconClass(isActive, menu.icon)}></i>
          )}
        </span>
        <span className="font-muller text-[16px] font-medium leading-[21.33px] text-left">
          {menu.label}
        </span>
      </>
    )}
  </NavLink>
);

const getNavigationLink = (menu: Menu, index: number) => {
  if (menu.isTitle) return renderMenuTitle(menu.label, index);
  if (menu.isSpacer) return renderMenuSpacer(index);
  return renderMenuLink(menu, index);
};


  const handleSignOut = () => {
    toast.dismiss();
    dispatch(signOut());
    dispatch(resetRegistration());
    dispatch(resetSidebarState());
  };

  const getSidebarClass = () =>
  `shadow-lg bg-white flex-none flex flex-col fixed h-full md:relative z-10 transition duration-300 md:w-[250px] ${
    open ? "w-[250px]" : "-translate-x-[250px] w-0 md:translate-x-0"
  }`;

  return (
    <div data-testid="sidebar" className={getSidebarClass()}>
      <div className="flex-none flex flex-col items-center py-4 justify-center relative">
         <Link to="/" className="inline-block w-[60%]"><img src={LOGO_PATH} alt="logo"/></Link>
        <span data-testid="close-button"
          className="absolute md:hidden top-1 -right-4 bg-white w-8 h-8 flex items-center justify-center rounded-full"
          onClick={() => dispatch(hideSidebar())}
        >
          <i className="fa-duotone fa-close fa-lg cursor-pointer"></i>
        </span>
      </div>
      <div className="flex flex-col flex-grow overflow-auto z-10">
        <ul className="px-1 flex flex-col space-y-1 py-4 font-weight[300] mx-8">
          {menus?.map((menu: Menu, index) => getNavigationLink(menu, index))}
        </ul>
      </div>
      <div className="flex-none flex text-sm flex-col items-start my-4 px-8">
        <div className="w-16 h-16 rounded-full border border-white overflow-hidden">
          <img
            src="https://image.lexica.art/full_jpg/1964206d-03f5-4255-8bbf-fbceecbbc180"
            alt="teacher"
          />
        </div>
        {user && <strong className="mt-2">{getFullName(user?.firstname, user?.lastname)}</strong>}
        <small className="mt-0">{user?.email}</small>
      </div>
      <div className="flex flex-row py-2 px-8 mb-4">
        <i aria-label="sign out"
          className="fa-duotone fa-arrow-right-from-bracket fa-lg cursor-pointer active:scale-95"
          onClick={handleSignOut}
        ></i>
      </div>
    </div>
  );
};

export default Sidebar;
