import { Menu } from "@/types/menu.interface";
import { useEffect,FC, useState } from "react";
import { Link, NavLink, useNavigate } from "react-router-dom";
import { getFullName } from "@/utils/app.util";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { hideSidebar, resetSidebarState } from "@/store/slicers/sidebar.slicer";
import { resetRegistration, signOut } from "@/store/slicers/auth.slicer";
import { LOGO_PATH } from "../../../src/pages/auth/constants";
import { prepareMenus } from "@/utils/menu.util";

const TherapySidebar: FC = () => {
  const open = useAppSelector((state) => state.sidebar.on);
  const user = useAppSelector((state) => state.auth.user);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [menus, setMenus] = useState([] as Menu[]);

  useEffect(() => {
    const createMenus = async () => {
      const items = prepareMenus(user?.role || "");
      setMenus(items);
    };

    createMenus();
  }, []);
  
  const handleSignOut = () => {
    dispatch(signOut()); // Log the user out
    dispatch(resetRegistration()); // Reset the calendar data to default state
    dispatch(resetSidebarState()); // Reset the sidebar state to default
    navigate('/auth/login');
  };

  return (
    <div data-testid="therapy-sidebar"
      style={{ borderRight: "0.07em solid rgb(204, 204, 204)" }}
      className={`bg-white flex-none flex flex-col fixed h-full md:relative z-10 transition duration-300 md:w-[300px] ${
        open ? "w-[250px]" : "-translate-x-[250px] w-0 md:translate-x-0"
      }`}
    >
      <div className="flex-none flex flex-col items-center py-4 justify-center relative">
        <Link to="/" className="inline-block w-[60%]">
            <img src={LOGO_PATH} alt="logo" />
        </Link>  
        <span data-testid="close-button"
          className="absolute md:hidden top-1 -right-4 bg-white w-8 h-8 flex items-center justify-center rounded-full"
          onClick={() => dispatch(hideSidebar())}
        >
          <i className="fa-duotone fa-close fa-lg cursor-pointer"></i>
        </span>
      </div>
      <div className="flex flex-col flex-grow overflow-auto z-10">
        <ul className="flex flex-col font-weight[300]">
          {menus.map((menu: Menu, index) => (
            <SidebarNavLink
              key={index}
              menu={menu}
              dispatch={dispatch}
            />
          ))}
        </ul>
      </div>
      <div className="flex-none flex text-sm flex-col items-start my-4 px-8">
        <div className="w-16 h-16 rounded-full border border-white overflow-hidden">        
          <img src={user?.userProfile || `https://ui-avatars.com/api?name=${user?.firstname}+${user?.lastname}`} alt="Profile Picture" />
        </div>
        {user && <strong className="mt-2">{getFullName(user?.firstname, user?.lastname)}</strong>}
        <small className="mt-0">{user?.email}</small>
      </div>
      <div className="flex flex-row py-2 px-8 mb-4">
        <i
          className="fa-duotone fa-arrow-right-from-bracket fa-lg cursor-pointer active:scale-95"
          onClick={() => handleSignOut()}>
        </i>
      </div>
    </div>
  );
};

export default TherapySidebar;

interface SidebarNavLinkProps {
  menu: Menu;
  dispatch: any;
}

const SidebarNavLink: FC<SidebarNavLinkProps> = ({
  menu,
  dispatch,
}) => {
  const activeClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 bg-gray-100 rounded";
  const inactiveClass =
    "transition-all duration-300 tracking-wide truncate text-sm pl-3 flex flex-row py-1 hover:bg-gray-100";

  return (
    <NavLink
      onClick={() => dispatch(hideSidebar())}
      to={menu.link!}
      className={({ isActive }) =>
        (isActive ? activeClass : inactiveClass) + ` py-4 ${isActive ? " active-menu" : ""}`
      }
    >
      {({ isActive }) => (
        <>
          <span className="w-8 text-center">
            {menu.icon && (
              <img
                src={isActive ? menu.icon.active : menu.icon.inactive}
                alt={menu.label}
                style={{ width: "21px", height: "21px" }}
                className="icon"
              />
            )}
          </span>
          <span
            style={{
              fontSize: "16px",
              color: isActive ? "black" : "#7E7778",
            }}
          >
            {menu.label}
          </span>
        </>
      )}
    </NavLink>
  );
};
