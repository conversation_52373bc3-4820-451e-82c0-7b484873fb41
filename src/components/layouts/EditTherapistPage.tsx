import loadable from "@loadable/component";
import { Navigate, useParams } from "react-router-dom";

const LicensePage = loadable(() => import("@/pages/auth/registration/license.page"));
const SpecialitiesPage = loadable(() => import("@/pages/auth/registration/specialization.page"));
const RankSpecialitiesPage = loadable(() => import("@/pages/auth/registration/rank-specialties.page"));
const RankSubSpecialitiesPage = loadable(() => import("@/pages/auth/registration/rank-sub-specialties.page"));
const ModalitiesPage = loadable(() => import("@/pages/auth/registration/modalities.page"));
const PracticeFocusPage = loadable(() => import("@/pages/auth/registration/practice-focus.page"));
const PracticeInfoPage = loadable(() => import("@/pages/auth/registration/practice-info.page"));
const ProfileInfoPage = loadable(() => import("@/pages/auth/registration/profile.page"));

const EditTherapistPage = () => {
  const { section } = useParams();

  const editComponents: Record<string, React.ComponentType> = {
    "license": LicensePage,
    "specialization": SpecialitiesPage,
    "rank-specialties": RankSpecialitiesPage,
    "rank-sub-specialties": RankSubSpecialitiesPage,
    "modalities": ModalitiesPage,
    "practice-focus": PracticeFocusPage,
    "practice-info": PracticeInfoPage,
    "profile": ProfileInfoPage,
  };

  const Component = section && editComponents[section];

  if (!Component) return <Navigate to="/" />;
  return <Component />;
};

export default EditTherapistPage;
