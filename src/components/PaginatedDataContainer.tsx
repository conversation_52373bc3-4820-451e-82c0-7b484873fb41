import Meta from "@/types/meta.interface";
import React, { FC } from "react";

type Props = {
  children: React.JSX.Element;
  meta: Meta;
  className?: string;
  emptyText?: string;
};

const PaginatedDataContainer: FC<Props> = ({ children, meta, emptyText }) => {
  return (
    <>
      {meta?.total === 0 ? (
        <div data-testid="empty-message" className="grid grid-cols-1 text-center border-t gap-2 text-sm hover:bg-gray-50 py-3 px-2 transition duration-200 ease-in-out">
          {emptyText || "No data available"}
        </div>
      ) : (
        <>{children}</>
      )}
    </>
  );
};

export default PaginatedDataContainer;
