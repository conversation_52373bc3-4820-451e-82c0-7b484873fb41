import { StripeInfo } from "./stripe-info.interface";
import { Therapist } from "./therapist.interface";

export interface User {
  id: number;
  firstname: string;
  lastname: string;
  email: string;
  emailVerifiedAt?: Date;
  phone: string;
  phoneVerifiedAt?: Date;
  dob: string;
  gender: string;
  role: string;
  profile?: Therapist;
  hasGoogleCalendar: boolean;
  hasOutlookCalendar: boolean;
  deactivatedAt?: Date;
  acceptedAt?: Date;
  rejectedAt?: Date;
  address: Record<string, any>;
  registrationInfo?: UserRegistrationInfo[];
  currentPage: string;
  userToken: string;
  passwordUpdatedAt: Date;
  calendars?: Calendar[];
  stripeInfo?: StripeInfo;
  userProfile?: string | null;
  mfaEnabled: boolean;
  deletedBy: string | null; 
  userDeletedAt: string | null;
  deletedAt?: string | null;
  isUnderReview?: boolean;
  removedAt: string | null;
  statusReason?:string
}

export interface Calendar {
  userId: number;
  email: string;
  type: string;
}

export interface UserRegistrationInfo {
  userId: number;
  pageName: string;
  payloadInfo: any;
}
