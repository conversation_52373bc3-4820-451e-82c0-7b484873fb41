import { User } from '@/src/models'
import { FunctionType } from '@/types/general.types';

declare global {
	namespace Express {
		export interface Request {
			role?: string
			user?: User
			countryCode?: string
		}

		export interface Response {
      success: FunctionType,
      created: FunctionType,
      updated: FunctionType,
      deleted: FunctionType,
      paginated: FunctionType,
      single: FunctionType,
      file: FunctionType,
      failure: FunctionType,
      invalid: FunctionType,
      forbidden: FunctionType,
      notFound: FunctionType,
      unauthorized: FunctionType
    }
	}
}
