export interface StepInterface {
  title: string;
  form: FormTypeInterface[];
  followed_by?: string[] | null;
  page_id: string;
}

export interface RegistrationStepInterface {
  [key: string]: StepInterface;
}

export interface OptionType {
  label: string;
  value: string | number;
  name?: string;
  id?: string;
  reserved_page?: string;
  prevent_navigation?: boolean;
  disabled?: boolean;
  children?: OptionType[];
  info?: Record<string, Record<string, any>>;
}

export interface FormTypeInterface {
  label: string;
  type: "email" | "password" | "text" | "date" | "image" | "radio" | "select" | "checkbox";
  id?: string;
  setMeta?: boolean;
  options?: OptionType[];
}

export interface PaymentForms {
  session_fee: string;
  insurance: boolean;
  insurance_list: string[];
}

export interface WaitlistNotifications {
  booking_time_hour: string;
  notification_preferences: string[];
  phone_number: string;
  week_visibility: string;
}

export interface PracticeInfo {
  practice_name: string;
  business_phone: string;
  business_email: string;
  business_address: AddressData;
  practice_photos: string[];
  patient_info_sheet: Record<string, string>;
}

export interface AddressData {
  street: string;
  city: string;
  country: string;
  full_address: string;
  description: string;
  zipCode: string;
  lat: number;
  lng: number;
  place_id: string;
  zone: string;
};

export interface OfficeHoursInfo {
  activeTimes: Record<string, any>;
  timezone: string;
  appointmentMethod: string;
}

export interface License {
  license_type: {
    label: string;
    value: string;
    id: string;
  };
  license_state: {
    label: string;
    value: string;
    id: string;
  };
  issuing_board: string;
  issue_date: string;
  expiry_date: string;
  license_verification: {
    location?: string;
    upload_name?: string;
  };
  license_status: {
    label: string;
    value: string;
  };
  clinical_supervisor_name: string;
  clinical_supervisor_email: string;
  clinical_supervisor_phone: string;
  probation_or_disciplinary_action: {
    label: string;
    value: string;
  };
  disciplinary_action_description: string;
  license_no: string;
  license_number: string;
}

export interface CompactLicense {
  compact_privilege: {
    label: string;
    value: string;
  };
  compact_name: string;
  compact_identification_number: string;
  compact_states: {
    label: string;
    value: string;
    id: string;
  }[];
}

export interface LicensePayload {
  licenses?: License[];
  compact_licenses?: CompactLicense[];
  npi_number?: string;
}
