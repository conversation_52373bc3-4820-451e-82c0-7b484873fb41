export interface StepInterface {
  title: string;
  form: FormTypeInterface[];
  followed_by?: string[] | null;
  page_id: string;
}

export interface RegistrationStepInterface {
  [key: string]: StepInterface;
}

export interface OptionType {
  label: string;
  value: string | number;
  name?: string;
  id?: string;
  reserved_page?: string;
  prevent_navigation?: boolean;
  disabled?: boolean;
  children?: OptionType[];
  info?: Record<string, Record<string, any>>;
  errorMessage?: string;
}

export interface FormTypeInterface {
  label: string;
  type: "email" | "password" | "text" | "date" | "image" | "radio" | "select" | "checkbox";
  id?: string;
  setMeta?: boolean;
  options?: OptionType[];
}
