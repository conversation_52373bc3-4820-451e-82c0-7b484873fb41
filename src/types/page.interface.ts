export interface Page {
  id: number;
  code: string;
  title: string;
  info: string;
  extra: string;
  button: string;
  type: string;
  category: string;
  sortOrder: number;
  questionnaireId: number;
  buttonAction: string;
  nextPageId?: number;
  skipToPageId?: number;
  nextPage?: Page;
  initialPage?: boolean;
  matcherInvolvement?: boolean;
  answers?: any[];
  questionnaireType?: string;
  required?: boolean;
  copiedFromPages?: number[];
  isFollowedThrough?: boolean;
  isPrimaryFollowThrough?: boolean;
  conditionalPages?: Page[];
  questionnaire?: Record<string, any>;
}
