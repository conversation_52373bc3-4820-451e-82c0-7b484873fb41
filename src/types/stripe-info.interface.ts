export interface StripeInfo {
  id: number;
  therapistId: number;
  therapistSubscriptionId: number;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  stripeCurrentPeriodStart: string;
  stripeCurrentPeriodEnd: string;
  stripeCancelAtPeriodEnd: boolean;
  status: string;
  currency: string;
  billedPrice: number;
  canceledAt?: string | null;
  stripeSubscriptionScheduleId?: string | null;
  nextScheduledSubscriptionType?: string | null;
}