import crypto from 'crypto';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

const ENVIRONMENT = {
    LOCAL: 'local',
    DEVELOPMENT: 'development',
    QA: 'qa',
};

let PUBLIC_KEY = process.env.PUBLIC_KEY || '';
let PRIVATE_KEY = process.env.PRIVATE_KEY || '';
const RSA_DEV_KEY = 'dev/rsa-keys';
const RSA_PROD_KEY = 'prod/rsa-keys';
interface HybridEncryptionResult {
    encryptedKey: string;
    iv: string;
    encryptedData: string;
}

export async function initializeCryptoUtils(): Promise<void> {
 
    if ([ENVIRONMENT.LOCAL].includes(process.env.NODE_ENV || '')) {
        PUBLIC_KEY = process.env.PUBLIC_KEY || '';
        PRIVATE_KEY = process.env.PRIVATE_KEY || '';
 
        if (!PUBLIC_KEY || !PRIVATE_KEY) {
            throw new Error('Missing PUBLIC_KEY or PRIVATE_KEY in environment variables');
        }
    } else {
        const key = process.env.NODE_ENV === ENVIRONMENT.DEVELOPMENT ? RSA_DEV_KEY : RSA_PROD_KEY;
        const secretData = await getAWSSecretById(key);     
        
        if (secretData?.SecretString) {
            const parsedSecret = JSON.parse(secretData?.SecretString);
            if (process.env.NODE_ENV === ENVIRONMENT.DEVELOPMENT) {
                PUBLIC_KEY = parsedSecret?.PUBLIC_KEY;
                PRIVATE_KEY = parsedSecret?.PRIVATE_KEY;
            } else {
                PUBLIC_KEY = parsedSecret?.PROD_PUBLIC_KEY;
                PRIVATE_KEY = parsedSecret?.PROD_PRIVATE_KEY;
            }
        }
    }
 
    PUBLIC_KEY = PUBLIC_KEY.replace(/\\n/g, '\n');
    PRIVATE_KEY = PRIVATE_KEY.replace(/\\n/g, '\n');
}

export function encryptData(data: any): string {
    const stringData = typeof data === 'object' ? JSON.stringify(data) : String(data);
    const buffer = Buffer.from(stringData, 'utf8');
 
    if (buffer.length <= 190) {
        const encrypted = crypto.publicEncrypt(
            {
                key: PUBLIC_KEY,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                oaepHash: 'sha256'
            },
            buffer
        ).toString('base64');
        return encrypted;
    }
 
    const aesKey: Buffer = crypto.randomBytes(32);
    const iv: Buffer = crypto.randomBytes(16);
 
 
    const cipher: crypto.Cipher = crypto.createCipheriv('aes-256-cbc', aesKey, iv);
    let encryptedData: string = cipher.update(stringData, 'utf8', 'base64');
    encryptedData += cipher.final('base64');

    const encryptedKey: string = crypto.publicEncrypt(
        {
            key: PUBLIC_KEY,
            padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            oaepHash: 'sha256'
        },
        aesKey
    ).toString('base64');
 
 
    const result: HybridEncryptionResult = {
        encryptedKey,
        iv: iv.toString('base64'),
        encryptedData
    };

    return JSON.stringify(result);
}

export function decryptData(encryptedData: string): any {
    try {
        const parsed: HybridEncryptionResult = JSON.parse(encryptedData);

        if (parsed.encryptedKey && parsed.iv && parsed.encryptedData) {
 
            const aesKey: Buffer = crypto.privateDecrypt(
                {
                    key: PRIVATE_KEY,
                    padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                    oaepHash: 'sha256'
                },
                Buffer.from(parsed.encryptedKey, 'base64')
            );
 
 
            const decipher: crypto.Decipher = crypto.createDecipheriv(
                'aes-256-cbc',
                aesKey,
                Buffer.from(parsed.iv, 'base64')
            );

            let decryptedData: string = decipher.update(parsed.encryptedData, 'base64', 'utf8');
            decryptedData += decipher.final('utf8');
 
 
            try {
                return JSON.parse(decryptedData);
            } catch {
                return decryptedData;
            }
        }
    } catch (error) {
 
        const buffer: Buffer = Buffer.from(encryptedData, 'base64');
        const decryptedData: string = crypto.privateDecrypt(
            {
                key: PRIVATE_KEY,
                padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
                oaepHash: 'sha256'
            },
            buffer
        ).toString('utf8');
 
 
        try {
            return JSON.parse(decryptedData);
        } catch {
            return decryptedData;
        }
    }

    throw new Error('Invalid encrypted data format');
}
 
export function hashData(data: any): any {
    const hashed = crypto.createHash('sha512').update(data).digest('hex');
    return hashed;
}

export async function getAWSSecretById(secretId: string): Promise<any> {
    const client = new SecretsManagerClient({
        region: process.env.AWS_REGION,
        credentials: {
            accessKeyId: process.env.AWS_ACCESS_KEY!,
            secretAccessKey: process.env.AWS_SECRET_KEY!,
        },
    });
    const command = new GetSecretValueCommand({ SecretId: secretId });
    const response = await client.send(command);
    return response;
}
