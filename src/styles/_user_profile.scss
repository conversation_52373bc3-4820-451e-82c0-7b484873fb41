.user-profile-page {
  font-family: "<PERSON>";


  .profile-header {
    position: relative;
    padding: 15px 50px;
    min-height: 150px;
    width: 100%;
    border-bottom: 1px solid #ccc;
    background-color: rgb(62, 31, 64);
  
    h3 {
      color: $primaryColor;
      font-size: 30px;
      font-weight: 500;
      text-align: center;
      margin: 0;
      line-height: 1;
    }
  
    .navigation,
    .stats-information {
      position: absolute;
      top: 15px;
    }
  
    .navigation {
      left: 20px;
  
      i {
        font-size: 30px;
        color: #fff;
      }
    }
  
    .stats-information {
      right: 20px;
  
      i {
        font-size: 24px;
        color: #fff;
      }
    }
  }

  
  .user-personal-info {
    position: relative;
    top: -90px;
    text-align: center;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .stats-information {
      width: 500px;
      position: absolute;
      top: 120px;
      display: flex;
      justify-content: flex-end;
      a {
        border: 0.1em solid black;
        width: 120px;
        border-radius: 5px;
        padding: 3px 0 2px 0;
        i {
          font-size: 16px;
          color: black;
        }
        &.active {
          color: $secondaryColor !important;
          border-color: $secondaryColor;
          i {
            color: $secondaryColor !important;
          }
        }
      }
    }
    .profile-picture {
      height: 170px;
      width: 170px;
      border-radius: 50%;
      overflow: hidden;
      border: 4px solid #e8e7e7;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    h4 {
      font-size: 30px;
      font-weight: 500;
      margin-top: 15px;
      line-height: 1.2;
    }
    i {
      font-size: 20px;
      color: $primaryColor;
    }
    p {
      font-size: 16px;
      font-weight: 300;
      width: 100%;
      padding: 0 20px;
      &.text-light {
        color: #999;
      }
      a {
        font-size: 16px;
      }
    }
    a.btn {
      margin-top: 15px;
      background-color: $colorSuccess;
      color: white;
      padding: 10px 80px;
      border-radius: 10px;
    }
  }
  a {
    color: $primaryColor;
  }

  .user-additional-info {
    min-height: 100px;
    border-top: 1px solid #999;
    margin-top: -30px;
    h4 {
      color: $primaryColor;
      font-size: 26px;
      font-weight: 500;
      text-align: center;
      margin-top: 0;
    }
    .content {
      max-width: 1000px;
      margin: 15px auto;
      .user-features-container {
        width: 100%;
        display: flex;
        justify-content: space-around;
        .user-feature {
          padding: 0 15px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;
          width: 150px;
          height: 160px;
          border: 0.1em solid $primaryColor;
          border-radius: 8px;
          i {
            font-size: 28px;
            color: $primaryColor;
          }
          p {
            font-size: 18px;
            line-height: 1.2;
            margin-top: 10px;
            color: $primaryColor;
          }
        }
      }
    }
    .education-section,
      .therapy-types-section,
      .specialties-section {
        border: 1px solid #999;
        border-radius: 5px;
        padding: 15px;
        margin: 20px 120px;

        h4 {
          margin-top: 0;
          color: #333;
          font-size: 1.1em;
          font-weight: 600;
        }

        p {
          display: flex;
          flex-direction: column;
          margin-bottom: 0;
          color: #666;
          font-size: 0.9em;
          text-align: center;
        }
      }
      .practice-images-section {
        display: flex;
        justify-content: center;
        
        .practice-images {
          width: 800px;
          display: flex;
          justify-content: center;
          gap: 25px;
          margin: 30px 0;
          flex-wrap: wrap;
        
          .image-container {
            flex: 0 0 auto;
            width: 180px;
            height: 180px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
      
              &:hover {
                transform: scale(1.05);
              }
            }
          }
        }
      }
  }
  .user-tab {
    width: 500px;
    height: 55px;
    margin: auto;
    position: relative;
    top: -27px;
    border-radius: 50px;
    display: flex;
    border: 0.1em solid $primaryColor;
    overflow: hidden;
    .tab {
      cursor: pointer;
      flex: 1;
      background-color: white;
      font-size: 18px;
      display: flex;
      align-items: center;
      color: $primaryColor;
      justify-content: center;
      font-weight: 500;
      transition: background-color 0.3s ease, transform 0.3s ease;
      &.active {
        color: white;
        background-color: $primaryColor;
      }
    }
  }
}


.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999; 
}


.modal-content {
  background: #f1f1f1;
  padding: 20px;
  border-radius: 8px;
  max-width: 500px;
  width: 90%; 
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  position: relative; 
  max-height: 80vh; 
}


.modal-content p {
  white-space: pre-line; 
  word-wrap: break-word;
  max-height: 400px; 
  overflow: auto;
  margin-bottom: 20px; 
}


.close-btn {
  position: absolute;
  top: 19px;
  right: 10px;
  border: none;
  background: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.close-btn:hover {
  color: #000;
}



