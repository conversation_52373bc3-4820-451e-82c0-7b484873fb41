.custom-tooltip {
  position: relative;
  background: white;
  color: #1a1a1a;
  padding: 20px;
  border-radius: 10px;
  max-width: 400px;
  border: 2px solid #b486d9;
  box-shadow:
    0 3px 8px rgba(180, 134, 217, 0.35),
    0 0 15px 4px rgba(180, 134, 217, 0.3),
    0 0 30px 7px rgba(180, 134, 217, 0.15);
  transition: box-shadow 0.3s ease-in-out;
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
}

.arrow-top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid white;
}

.arrow-bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid white;
}

.arrow-left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid white;
}

.arrow-right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 10px solid white;
}

.tooltip-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: transparent;
  border: none;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  color: #b486d9;
  padding: 4px;
}

.tooltip-footer {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tooltip-back-btn {
  background: transparent;
  border: none;
  font-weight: bold;
  font-size: 0.8rem;
  padding: 4px 8px;
}

.tooltip-next-btn {
  background: rgb(67 29 66);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: bold;
  cursor: pointer;
}




.walkthrough-highlight {
  animation: glow 0.8s ease-in-out infinite alternate;
  border-radius: 6px;
  box-shadow: 0 0 0 rgba(199, 156, 200, 0.5);
}

@keyframes glow {
  from {
    box-shadow: 0 0 0 rgba(199, 156, 200, 0.5);
  }
  to {
    box-shadow: 0 0 12px 6px rgba(199, 156, 200, 0.9);
  }
}



.time.highlighted-demo {
  position: relative; /* or absolute, depending on layout */
  z-index: 11000 !important; /* must be higher than Joyride overlay */
  outline: 2px dashed #9141ac;
  background-color: rgba(255, 255, 255, 0.85); /* higher contrast */
  animation: pulseHighlight 1s infinite;
  border-radius: 4px;
  pointer-events: none; /* prevent user interaction */
  opacity: 1 !important;
}

.time.highlighted-demo::after {
  content: "";
  position: absolute;
  top: -4px; left: -4px; right: -4px; bottom: -4px;
  border-radius: 6px;
  box-shadow: 0 0 12px 4px rgba(145, 65, 172, 0.5);
  z-index: -1;
}


@keyframes pulseHighlight {
  0% { outline-offset: 0; }
  50% { outline-offset: 2px; }
  100% { outline-offset: 0; }
}

.react-joyride__spotlight {
  position: absolute !important;
  background: rgba(255, 255, 255, 0.1) !important; /* semi-transparent white */
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.6) !important; /* dark overlay */
  border-radius: 8px !important;
  pointer-events: none !important;
  z-index: 999999 !important;
  transition: all 0.3s ease !important;
}

/* For all steps except when spotlight covers entire screen */
.react-joyride__overlay {
  background-color: rgba(0, 0, 0, 0.4) !important; /* faint dark */
}

/* When spotlight covers entire viewport (like targeting body) */
.react-joyride__spotlight--fixed {
  box-shadow: none !important; /* remove huge box-shadow */
}

/* For the first step, explicitly make overlay darker */
.react-joyride__overlay--open {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

.select-highlighted {
  position: relative;
  z-index: 11000 !important;
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 4px;
  pointer-events: none;
   padding: 8px;
}

.select-highlighted::after {
  content: "";
  position: absolute;
  top: -4px; left: -4px; right: -4px; bottom: -4px;
  border-radius: 6px; 
  z-index: -1;
}
