html,
body {
	height: 100%;
	width: 100%;
	margin: 0;
	padding: 0;
}

#root {
	height: 100%;
	width: 100%;
}

.ps__rail-x {
	display: none !important;
}
button[disabled] {
	opacity: 0.5;
	background-color: #888;
	cursor: not-allowed;
}

////////// RADIO at register page ////////////
.app_radio_regiter {
	display: block;
	position: relative;
	padding-left: 30px;
	margin-bottom: 12px;
	cursor: pointer;
	font-size: 18px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	&.disabled {
		opacity: 0.4;
		cursor: not-allowed;
	}
}

/* Hide the browser's default radio button */
.app_radio_regiter input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
}

/* Create a custom radio button */
.checkmark {
	position: absolute;
	top: 0;
	left: 0;
	height: 25px;
	width: 25px;
	background-color: #eee;
	border-radius: 50%;
}

/* On mouse-over, add a grey background color */
.app_radio_regiter:hover input ~ .checkmark {
	background-color: #ccc;
}

/* When the radio button is checked, add a blue background */
.app_radio_regiter input:checked ~ .checkmark {
	background-color: #ccc;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
	content: '';
	position: absolute;
	display: none;
}

/* Show the indicator (dot/circle) when checked */
.app_radio_regiter input:checked ~ .checkmark:after {
	display: block;
}

/* Style the indicator (dot/circle) */
.app_radio_regiter .checkmark:after {
	top: 0.35em;
	left: 0.4em;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: black;
}

.more-question {
	h3 {
		font-size: 23px !important;
		line-height: 1.2;
	}
}

.specialities-box {
	color: #547191;
	padding: 10px 20px;
	cursor: pointer;
	background-color: white;
	border: 2px solid #547191;
	border-radius: 10px;
	margin-right: 25px;
	font-weight: 500;
	box-shadow: 3px 3px 3px rgba(0, 0, 0, 0.2);
	i {
		&:hover {
			color: indianred;
		}
	}
}

// .app_checkbox_regiter {
//   display: flex;
//   font-size: 1.1em;
//   padding-left: 5px;
//   input {
//     width: 20px;
//     accent-color: #f0e2cf;
//   }
// }

.app_checkbox_regiter {
	display: block;
	position: relative;
	padding-left: 25px;
	margin-bottom: 12px;
	cursor: pointer;
	font-size: 18px;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	&.disabled {
		opacity: 0.4;
	}
}

ul.dragger-sub-spec {
	padding-left: 0;
	li {
		font-size: 30px;
		height: 80px;
		padding: 0 25px;
		border-radius: 15px;
		display: flex;
		align-items: center;
		.specialities-box {
			display: inline-block;
			margin-left: 20px;
			font-size: 16px;
		}
		&.about-to-dropped {
			background-color: #f1f1f1;
		}
	}
}

/* Hide the browser's default checkbox */
.app_checkbox_regiter input {
	position: absolute;
	opacity: 0;
	cursor: pointer;
	height: 0;
	width: 0;
}

/* Create a custom checkbox */
.checkmark_checkbox {
	position: absolute;
	top: 2.7px;
	left: 0;
	height: 20px;
	width: 20px;
	background-color: #eee;
}

/* On mouse-over, add a grey background color */
.app_checkbox_regiter:hover input ~ .checkmark_checkbox {
	background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.app_checkbox_regiter input:checked ~ .checkmark_checkbox {
	background-color: #f0e2cf;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark_checkbox:after {
	content: '';
	position: absolute;
	display: none;
}

/* Show the checkmark when checked */
.app_checkbox_regiter input:checked ~ .checkmark_checkbox:after {
	display: block;
}

/* Style the checkmark/indicator */
.app_checkbox_regiter .checkmark_checkbox:after {
	left: 8px;
	top: 5px;
	width: 5px;
	height: 10px;
	border: solid black;
	border-width: 0 3px 3px 0;
	-webkit-transform: rotate(45deg);
	-ms-transform: rotate(45deg);
	transform: rotate(45deg);
}

.registration_table_form {
	font-size: 18px;
}

.registration_textarea {
	width: 100%;
	height: 400px;
	border-radius: 15px;
	padding: 15px;
	border: 1px solid #ccc;
	&:focus {
		outline: none;
	}
}

textarea::-webkit-input-placeholder {
	/* Styles for Webkit (Chrome, Safari) */
	white-space: pre-wrap !important; /* This allows line breaks */
}

textarea::-webkit-input-placeholder {
	/* Styles for Webkit (Chrome, Safari) */
	white-space: pre-wrap; /* This allows line breaks */
}

textarea::-moz-placeholder {
	/* Styles for Firefox */
	white-space: pre-wrap;
}

textarea:-ms-input-placeholder {
	/* Styles for IE */
	white-space: pre-wrap;
}

textarea::-ms-fill-placeholder {
	/* Styles for Edge */
	white-space: pre-wrap;
}

.step_instruction {
	position: fixed;
	bottom: 40px;
	padding-right: 100px;
	font-size: 17px;
	color: #888;
}

.horiz_button {
	border-radius: 5px;
	background-color: $primaryColorDeeper;
	display: flex;
	width: 300px;
	justify-content: center;
	align-items: center;
	padding: 14px 15px;
	color: white;
	span:first-child {
		font-size: 16px;
		line-height: 1;
	}
}

.horiz_button_edit_therapist {
	border-radius: 9999px;
	background-color: $primaryColorDeeper;
	display: inline-block;
	text-align: center;
	width: #{width}px;
	padding: 14px 24px;
	color: white;
	font-size: 0.875rem;

	&.bg-therapy-blue-dark {
		background-color: var(--therapy-blue-dark);
	}

	span:first-child {
		font-size: 16px;
		line-height: 1;
	}
}

.about_me_radio {
	border-bottom: 1px solid #ccc;
	margin-bottom: 30px;
	padding-bottom: 20px;
	&:last-child {
		border-bottom: none;
	}
	.radio_label {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20px;
	}
	.radio_options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.radio-o {
			width: 100px;
			// background-color: red;
			height: 100px;
			padding-top: 5px;
			label {
				padding-left: 0;
				span {
					display: block;
					position: relative;
				}
				div {
					padding-top: 10px;
					font-weight: 300;
					line-height: 1.2;
				}
				align-items: center;
				text-align: center;
				justify-content: center;
				display: flex;
				flex-direction: column;
			}
		}
	}
}

.user_registration_profile {
	overflow: hidden;
	img {
		width: 6rem;
		height: 6rem;
		object-fit: cover;
	}
}

.app-dialogue-modal {
	.closable-btn {
		position: absolute;
		top: 20px;
		width: 20px;
		height: 20px;
		display: flex;
		justify-content: center;
		align-items: center;
		line-height: 1;
		display: block;
		right: 20px;
		background-color: black;
		color: white;
		border-radius: 50%;
		font-size: 12px;
	}
}

.loading-suspence {
	display: inline-block;
	width: 30px;
	height: 30px;
	border: 2px solid rgba(0, 0, 0, 0.2);
	border-radius: 50%;
	border-top-color: #547191;
	animation: spin 1s ease-in-out infinite;
	-webkit-animation: spin 1s ease-in-out infinite;
	left: calc(50% - 15px);
	top: calc(50% - 15px);
	position: fixed;
	z-index: 1;
}

@keyframes spin {
	to {
		-webkit-transform: rotate(360deg);
	}
}

@-webkit-keyframes spin {
	to {
		-webkit-transform: rotate(360deg);
	}
}

/******************************************************************************************/
/*************************OFFICE HOURS SCSS**************************************/
/******************************************************************************************/

.office-hours-ui {
	padding: 5px;
	.office-hours-options {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		gap: 10rem;
		.notify-selection {
			align-items: center;
			font-size: 18px;
			div {
				height: 40px;
				width: 40px;
				margin-right: 20px;
				justify-content: center;
				background-color: $primaryColor;
			}
		}
	}
	.time-select-options {
		user-select: none;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		table {
			width: 100%;
			.selector {
				td {
					padding: 8px;
					.time {
						cursor: pointer;
						border: 2px solid $primaryColorDeeper;
						height: 45px;
						display: flex;
						align-items: center;
						justify-content: center;
						position: relative;
						&.disabled {
							opacity: 0.4;
							background-color: #f0f0f0;
							cursor: not-allowed;
							pointer-events: none;
						}
						&.disabled-related-time {
							// cursor: not-allowed;
							opacity: 0.3;
						}
						&.active-in-person {
							background-color: $primaryColorDeeper;
							color: white;
							border: 2px solid $primaryColorDeeper;
						}
						&.active-telehealth {
							background-color: $telehealthColor;
							color: white;
							border: 2px solid $telehealthColor;
						}
						&.active-both {
							background-color: $bothTeleAndInPersonColor;
							color: white;
							border: 2px solid $bothTeleAndInPersonColor;
						}
						&:not(.active-in-person):not(.active-telehealth):not(.active-both) {
							&:hover {
								background-color: #d4e4f4;
							}
						}
						&.error {
							border: 2px solid red;

							// &::after {
							// 	content: '*';
							// 	color: red;
							// 	font-weight: bold;
							// 	font-size: 1.5rem;
							// 	position: absolute;
							// 	top: -12px;
							// 	right: -12px;
							// 	line-height: 1;
							// }
						}
					}
				}
			}
		}
	}
}

/***********************************************************************/
/**************************PATIENT PAGE***********************/
/***********************************************************************/

.patient-page-container {
	font-family: 'Muller', sans-serif;
	h1 {
		font-size: 37px;
		font-weight: 500;
		line-height: 1.2;
		margin-bottom: 15px;
	}
	height: 100%;
	.info-page-container {
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		max-width: 450px;
		margin: auto;
		padding-top: 12%;
		padding-bottom: 10%;
		h1 {
			text-align: center;
		}
		ol {
			color: #666;
			font-size: 18px;
			width: 220px;
			margin: auto;
			li {
				margin-bottom: 6px;
			}
		}
		a,
		button {
			display: flex;
			text-align: center;
			justify-content: center;
			margin: auto;
		}
		p.alert-info {
			text-align: center;
			color: #999;
			margin-top: auto;
			position: relative;
			bottom: 0;
		}
		p.page-info {
			text-align: center;
			position: relative;
			font-size: 20px;
			margin-top: 40px;
		}
	}

	.question-answer-container {
		padding: 3% 5%;
		h1 {
			color: #547191;
		}
		.bg-therapy-blue {
			&.with-icon {
				margin-bottom: 40px;
				display: flex;
				font-size: 17px;
				justify-content: space-between;
				i {
					font-size: 20px;
				}
			}
		}
	}
}

.end-page-container {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 2rem;

	> div {
		max-width: 600px;
		padding: 2rem;
		background-color: #ffffff;
		border-radius: 8px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
		text-align: center;

		h1 {
			font-size: 1.5rem;
			color: #333;
			margin-bottom: 1rem;
		}

		p {
			font-size: 1rem;
			color: #666;
			margin-bottom: 1rem;

			&.page-info {
				font-weight: bold;
			}
		}

		img {
			width: 200px;
			height: 200px;
			margin: 0 auto 1rem;
			display: block;
		}

		form {
			display: flex;
			flex-direction: column;
			gap: 1rem;
			margin-top: 1rem;

			select,
			input {
				padding: 0.5rem;
				font-size: 1rem;
				border: 1px solid #ccc;
				border-radius: 4px;
				width: 100%;
			}

			.error-message {
				color: red;
			}

			button {
				padding: 0.5rem 1rem;
				font-size: 1rem;
				color: #fff;
				background-color: $primaryColor;
				border: none;
				border-radius: 4px;
				cursor: pointer;
				transition: background-color 0.3s ease;

				&:hover {
					opacity: 0.8;
				}
			}
		}
	}
}

.remove-arrow::-webkit-inner-spin-button,
.remove-arrow::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

.remove-arrow {
	-moz-appearance: textfield;
	appearance: textfield;
}

.unregistered-user-list {
	border-left: 5px solid orange;
	margin-bottom: 5px;
}

a.active-menu {
	font-weight: bold;
}

.dashboard-banner-container {
	display: flex;
	font-family: 'Muller', sans-serif;

	.dashboard-banner {
		width: 100%;
		padding: 0 10%;
		margin-top: 100px;
		margin-bottom: auto;
		img {
			width: 400px;
			margin: 0 auto 30px auto;
		}
		.dashboard-box-container {
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;
		}
		.dashboard-box-item {
			cursor: pointer;
			min-height: 200px;
			width: calc(23% - 20px);

			border-radius: 5px;
			box-shadow: 0 3px 3px rgba(0, 0, 0, 0.2);
			border: 2px solid $primaryColorDeeper;
			margin-bottom: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
			text-align: center;
			&:hover {
				background-color: rgba(84, 113, 145, 0.06);
			}
			i {
				font-size: 50px;
				color: $primaryColorDeeper;
				line-height: 1;
			}
			h4 {
				color: $primaryColorDeeper;
				font-weight: 500;
				font-size: 20px;
				margin-top: 15px;
				padding: 0 10px;
				line-height: 1.2;
			}
		}
	}
}

.page-title-info {
	text-align: center;
	display: block;
	font-family: 'Muller', sans-serif;

	h3 {
		font-weight: 500;
		font-size: 24px;
		text-align: center;
		color: $primaryColor;
	}
}

.card-container {
	.card-header {
		&:first-child {
			border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
		}
		padding: 5px 15px;
		margin-bottom: 0;
		background-color: rgba(0, 0, 0, 0.03);
		border-bottom: 1px solid rgba(0, 0, 0, 0.125);
	}
	.card-body {
		display: flex;
		min-height: 1px;
		padding: 1.25rem;
		.icon-avatar {
			width: 30px;
		}
		.body-content {
			flex: 10 1;
			p {
				line-height: 1;
				strong {
					font-weight: 700;
					sup {
						padding-left: 5px;
						color: green;
						font-size: 14px;
					}
				}
			}
			small {
				font-size: 12px;
				color: #888;
			}
		}
		.body-action {
			width: 140px;
		}
	}
}

.custom-phone-input {
	border: 2px solid $primaryColor; /* Custom border color */
	border-radius: 6px; /* Rounded edges */
	padding: 8px; /* Padding */
	background-color: white; /* Outer background */
}

.custom-phone-input .PhoneInputInput {
	border: none !important; /* Removes border */
	background-color: #f3f4f6 !important; /* Light gray background */
	padding: 8px !important; /* Adds padding */
	outline: none !important; /* Removes focus outline */
	width: 100%; /* Ensures full width */
}

.register-phone-input .PhoneInputInput {
	border: 1px solid #d1d5db;
	width: 100%;
	background-color: #ffffff;
	border-radius: 0.75rem;
	padding: 0.625rem 0.5rem;
	font-size: 0.875rem;
	outline: none;
}

.register-phone-input .PhoneInputInput:focus {
	border-color: $primaryColor;
	outline: none;
}

.specialities-drop-container {
	display: flex;
	// gap: 40px;
	img {
		height: 50px;
	}
	margin-top: 50px;
	margin-bottom: 56px;

	align-items: center;

	.spec-list {
		@apply grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2;

		@media (max-width: 768px) {
			@apply grid-cols-1; /* Force single column layout for smaller screens */
		}
	}
	.spec-list {
		@apply grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2;

		@media (max-width: 1024px) {
			@apply grid-cols-1; /* Force single column layout for smaller screens */
		}
	}

	.spec-list {
		li {
			// cursor: pointer;
			i {
				font-size: 17px;
				font-weight: normal;
				// display: inline-block;
				margin-right: 15px;
			}
			display: flex;
			// justify-content: space-between;
			align-items: center;
			height: 60px;
			margin-bottom: 25px;
			background-color: $primaryColorDeeper;
			color: white;
			padding: 15px 30px;
			border-radius: 5px;
			font-weight: 500;
			span {
				width: 200px;
				display: flex;
				flex-wrap: wrap;
			}
		}
		&.light-theme {
			li {
				width: 270px;
				border: 2px solid #e2e2e2;
				background-color: white;
				color: $primaryColorDeeper;
			}
		}
	}

	@media (min-width: 1440px) {
		.drop-here-placeholder {
			width: 400px;
		}
	}
	//   @media (max-width: 320px) {
	// 	.drop-here-placeholder {
	// 	  width: 250px;
	// 	}
	//   }
	.drop-here-placeholder {
		min-height: 500px;
		border-radius: 10px;
		border: 1px dashed #ccc;
		// padding: 10px;
		padding: 25px 40px;
		.drop-title-bar {
			color: $primaryColorDeeper;
			font-size: 20px;
			font-weight: 500;
			font-style: italic;
		}
		@media (min-width: 1440px) {
			.drop-box-container {
				width: 300px;
			}
		}

		.drop-box-container {
			margin: 20px 0;
			display: flex;
			flex-direction: column;
			gap: 30px;
			li {
				list-style-type: decimal;
				background-color: #eee;
				border-radius: 5px;
				height: 60px;
				margin-left: 14px;
				// margin-bottom: 20px;
				list-style-position: outside;
				&:last-child {
					margin-bottom: 0 !important;
				}
				&.about-to-drop {
					border: 2px solid $primaryColorDeeper;
				}
				@media (max-width: 320px) {
					height: 80px;
				}
				.dropped-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 20px;
					height: 60px;
					background-color: $primaryColorDeeper;
					color: white;
					padding: 15px 30px;
					border-radius: 5px;
					font-weight: 500;
					span {
						width: 200px;
						display: flex;
						flex-wrap: wrap;
					}
					@media (max-width: 320px) {
						height: 90px;
					}

					// span {
					// 	white-space: nowrap;
					// 	overflow: scroll;
					// 	text-overflow: ellipsis;
					// 	max-width: 200px;
					// }
					.end-icon {
						&:hover {
							color: indianred;
							cursor: pointer;
						}
					}
				}
			}
		}
	}
}

.active-ts-sub-menu {
	background-color: #e6e7ed !important;
	font-weight: normal !important;
	span {
		color: black !important;
		font-weight: 500;
	}
}
.ds-selected.ds-hover {
	background-color: #eee;
}

.ReactCrop {
	width: 100% !important;
}
