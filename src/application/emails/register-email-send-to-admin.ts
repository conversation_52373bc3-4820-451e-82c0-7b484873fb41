import fs from 'fs'
import Handlebars from 'handlebars'
import { User } from '@/src/models'

type EmailArgs = {
	email: string
	user: User
}

const RegisterTherapistEmailSendToAdmin = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/register-therapist-send-to-admin.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
        To: '<EMAIL>',
			Subject: 'New Therapist Profile Submitted for Review',
			HtmlBody: template(data),
		}
	},
}

export { RegisterTherapistEmailSendToAdmin }
