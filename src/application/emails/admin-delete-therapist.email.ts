import fs from 'fs'
import Handlebars from 'handlebars'
import { User } from '@/src/models'

type EmailArgs = {
	email: string
	user: User
    reason: string
}

const AdminDeleteEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/admin-delete-therapist.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Account Deleted Notice',
			HtmlBody: template(data),
		}
	},
}

export { AdminDeleteEmail }
