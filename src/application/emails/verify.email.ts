import { User } from '@/src/models'
import fs from 'fs'
import Handlebars, { compile } from 'handlebars'

type EmailVerificationArgs = {
	email: string
	code: string
	url?: string
	user: User
}

const getVerificationEmailTemplate = (data: EmailVerificationArgs) => {
  const file = fs.readFileSync(
    'src/templates/emails/email-verification.html',
    'utf-8'
  )
  const template = Handlebars.compile(file)
  return template(data)
}

const VerificationEmail = {
  compile: (data: EmailVerificationArgs) => {
    return {
      From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Email Verification Magic Link',
			HtmlBody: getVerificationEmailTemplate(data),
    }
  },
}

export { VerificationEmail }