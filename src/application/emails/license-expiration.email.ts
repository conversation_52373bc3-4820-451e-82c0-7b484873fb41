import fs from 'fs'
import Handlebars, { compile } from 'handlebars'

type LicenseExpirationArgs = {
	email: string
  subject:string
	therapist_name: string
  license_state: string
  expiry:string
  daysLeft: number
}

const getLicenseExpirationTemplate = (data: LicenseExpirationArgs) => {
  const file = fs.readFileSync(
    'src/templates/emails/license-expiration.html',
    'utf-8'
  )
  const template = Handlebars.compile(file)
  return template(data)
}

const LicenseExpirationEmail = {
  compile: (data: LicenseExpirationArgs) => {
    return {
      From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: getLicenseExpirationTemplate(data),
    }
  },
}

export { LicenseExpirationEmail }