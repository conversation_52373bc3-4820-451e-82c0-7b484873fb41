import fs from 'fs'
import Handlebars from 'handlebars'

type EmailArgs = {
	subject: string
	email: string
	therapistName: string
	patientName: string
	appointmentSlots: {
		date: string
		time: string
	}[]
	remainingSlotsCount: number | null
	deepLinkUrl: string
}

Handlebars.registerHelper('ifCond', function (
	this: Record<string, any>,
	v1: any,
	operator: string,
	v2: any,
	options: Handlebars.HelperOptions
) {
	switch (operator) {
		case '===':
			return v1 === v2 ? options.fn(this) : options.inverse(this);
		case '!==':
			return v1 !== v2 ? options.fn(this) : options.inverse(this);
		default:
			return options.inverse(this);
	}
});

Handlebars.registerHelper('eq', function (
	this: Record<string, any>,
	v1: any,
	v2: any
) {
	return v1 === v2;
});

const WaitlistNotifyEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/waitlist-notify.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: template(data),
		}
	},
}

export { WaitlistNotifyEmail }
