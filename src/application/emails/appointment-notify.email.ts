import fs from 'fs'
import Handlebars from 'handlebars'

type EmailArgs = {
	subject: string
	email: string
	therapistName: string
	patientName: string
	appointmentDate: string
	appointmentTime: string
	location: string
	duration: string
	canceledAt?: string
	bookedAt?: string
	title?: string
	appointmentType: string
	confirmPaymentLink?: string
	header?: string
}

const TherapistAppointmentNotifyEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/therapist-appointment-notify.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: template({
				...data,
				isInPerson: data.appointmentType === "In-Person",
			}),
		}
	},
}

const TherapistAppointmentCancelEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/therapist-appointment-cancel.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: template({
				...data,
				isInPerson: data.appointmentType === "In-Person",
			}),
		}
	},
}

const PatientAppointmentNotifyEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/patient-appointment-notify.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: template({
				...data,
				isInPerson: data.appointmentType === "In-Person",
			}),
		}
	},
}

const TherapistConfirmPaymentNotifyEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/therapist-confirm-payment.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: template({
				...data,
				isInPerson: data.appointmentType === "In-Person",
			}),
		}
	},
}

export {
	TherapistAppointmentNotifyEmail,
	PatientAppointmentNotifyEmail, 
	TherapistAppointmentCancelEmail,
	TherapistConfirmPaymentNotifyEmail
}
