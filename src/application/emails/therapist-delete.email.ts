import fs from 'fs'
import Handlebars from 'handlebars'
import { User } from '@/src/models'

type EmailArgs = {
	email: string
	user: User
}

const TherapistDeleteEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/therapist-delete.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Your NextTherapist Account Has Been Deleted',
			HtmlBody: template(data),
		}
	},
}

export { TherapistDeleteEmail }
