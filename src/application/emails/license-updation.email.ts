import fs from 'fs'
import Handlebars, { compile } from 'handlebars'

type LicenseUpdationArgs = {
	email: string
  subject:string
	therapist_name: string
  therapist_email: string
}

const getLicenseUpdationTemplate = (data: LicenseUpdationArgs) => {
  const file = fs.readFileSync(
    'src/templates/emails/license-updation.html',
    'utf-8'
  )
  const template = Handlebars.compile(file)
  return template(data)
}

const LicenseUpdationEmail = {
  compile: (data: LicenseUpdationArgs) => {
    return {
      From: process.env.INFO_EMAIL_ADDRESS as string,
			To: '<EMAIL>',
			Subject: data.subject,
			HtmlBody: getLicenseUpdationTemplate(data),
    }
  },
}

export { LicenseUpdationEmail }