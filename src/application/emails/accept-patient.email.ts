import fs from 'fs'
import Handlebars from 'handlebars'
import { User } from '@/src/models'

type EmailArgs = {
	email: string
	user: User
	url: string
}

const AcceptPatientEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/accept-patient.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Account Verified Successfully',
			HtmlBody: template(data),
		}
	},
}

export { AcceptPatientEmail }
