import fs from 'fs'
import Handlebars from 'handlebars'

type EmailArgs = {
	reply_email: string
	subject: string
	email_content: string
}

const ContactUsEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/contact-us.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: process.env.CONTACT_EMAIL_ADDRESS as string,
			Subject: `NextTherapist (Therapist Portal) | ${data.subject} Subject`,
			HtmlBody: template(data),
		}
	},
}

export { ContactUsEmail }
