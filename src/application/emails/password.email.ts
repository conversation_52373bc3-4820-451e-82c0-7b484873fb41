import fs from 'fs'
import Handlebars from 'handlebars'
import { User } from '@/src/models'

type EmailArgs = {
	email: string
	user: User
	url: string
}

const PasswordResetEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/reset-password.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Password Reset Magic Link',
			HtmlBody: template(data),
		}
	},
}

export { PasswordResetEmail }
