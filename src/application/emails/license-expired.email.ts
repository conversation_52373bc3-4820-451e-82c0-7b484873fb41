import fs from 'fs'
import Handlebars, { compile } from 'handlebars'

type LicenseExpiredArgs = {
	email: string
  subject:string
	therapist_name: string
  license_state: string
  expiry:string
  daysLeft: number
}

const getLicenseExpirationTemplate = (data: LicenseExpiredArgs) => {
  const file = fs.readFileSync(
    'src/templates/emails/license-expired.html',
    'utf-8'
  )
  const template = Handlebars.compile(file)
  return template(data)
}

const LicenseExpiredEmail = {
  compile: (data: LicenseExpiredArgs) => {
    return {
      From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: getLicenseExpirationTemplate(data),
    }
  },
}

export { LicenseExpiredEmail }