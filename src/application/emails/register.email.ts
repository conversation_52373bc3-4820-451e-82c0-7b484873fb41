import { User } from '@/src/models'
import fs from 'fs'
import Handlebars from 'handlebars'

type TherapistEmailArgs = {
	email: string
	code: string
	url?: string
	user: User
}

type NewTherapistEmailArgs = {
	email: string
	user: User
}

type PatientEmailArgs = {
	email: string
	code: string
	url: string
	user: User
}

type MemberEmailArgs = {
	email: string
	url: string
	user: User
}

type MfaArgs = {
	email: string
	code: string
	user: User
}

type FailedLoginAttemptArgs = {
	email: string
	user: User
}

const getTherapistEmailTemplate = (data: TherapistEmailArgs) => {
	const file = fs.readFileSync(
		'src/templates/emails/register-therapist.html',
		'utf8'
	)
	const template = Handlebars.compile(file)
	return template(data)
}

const getNewTherapistEmailTemplate = (data: NewTherapistEmailArgs) => {
	const file = fs.readFileSync(
		'src/templates/emails/register-new-therapist.html',
		'utf8'
	)
	const template = Handlebars.compile(file)
	return template(data)
}

const getPatientEmailTemplate = (data: PatientEmailArgs) => {
	const file = fs.readFileSync(
		'src/templates/emails/register-patient.html',
		'utf8'
	)
	const template = Handlebars.compile(file)
	return template(data)
}

const getMemberEmailTemplate = (data: MemberEmailArgs) => {
	const file = fs.readFileSync(
		'src/templates/emails/register-member.html',
		'utf8'
	)
	const template = Handlebars.compile(file)
	return template(data)
}

const getMfaVerificationTemplate = (data: MfaArgs) => {
	const file = fs.readFileSync(
		'src/templates/emails/mfa-login.html',
		'utf8'
	)
	const template = Handlebars.compile(file)
	return template(data)
}


const getFailedLoginAttemptTemplate = (data: FailedLoginAttemptArgs) => {
	const file = fs.readFileSync(
		'src/templates/emails/failed-login-attempt-alert.html',
		'utf8'
	)
	const template = Handlebars.compile(file)
	return template(data)
}


const RegisterTherapistEmail = {
	compile: (data: TherapistEmailArgs) => {
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Welcome to Next Therapist',
			HtmlBody: getTherapistEmailTemplate(data),
		}
	},
}

const RegisterNewTherapistEmail = {
	compile: (data: NewTherapistEmailArgs) => {
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Welcome to Next Therapist',
			HtmlBody: getNewTherapistEmailTemplate(data),
		}
	},
}

const RegisterPatientEmail = {
	compile: (data: PatientEmailArgs) => {
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Welcome to Next Therapist',
			HtmlBody: getPatientEmailTemplate(data),
		}
	},
}

const RegisterMemberEmail = {
	compile: (data: MemberEmailArgs) => {
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Welcome to Next Therapist',
			HtmlBody: getMemberEmailTemplate(data),
		}
	},
}

const MfaVerificationEmail = {
	compile: (data: MfaArgs) => {
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Welcome to Next Therapist',
			HtmlBody: getMfaVerificationTemplate(data),
		}
	},
}

const FailedLoginAlertEmail = {
	compile: (data: FailedLoginAttemptArgs) => {
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Welcome to Next Therapist',
			HtmlBody: getFailedLoginAttemptTemplate(data),
		}
	},
}


export { RegisterTherapistEmail, RegisterPatientEmail, RegisterMemberEmail,MfaVerificationEmail,FailedLoginAlertEmail,RegisterNewTherapistEmail }
