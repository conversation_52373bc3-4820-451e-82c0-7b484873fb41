import fs from 'fs'
import Handlebars from 'handlebars'

type PlanChangeEmailArgs = {
	email: string
	fullName: string
	newMonthlyPrice: number
	newAnnualPrice: number
	oldMonthlyPrice: number
	oldAnnualPrice: number
}

type RenewalEmailArgs = {
	subject: string
	email: string
	therapistName: string
	autoRenewalDate: string
	amount: string
	subscriptionPlan: string
}

export type NewSubscriptionEmailArgs = {
	email: string
	therapistName: string
	nextRenewalDate: string
	amount: string
	subscriptionPlan: string
}

const PlanChangeEmail = {
	compile: (data: PlanChangeEmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/plan-change.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Next Therapist - Subscription Plan Change',
			HtmlBody: template(data),
		}
	},
}

const SubscriptionRenewalEmail = {
	compile: (data: RenewalEmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/subscription-renewal.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: data.subject,
			HtmlBody: template(data),
		}
	},
}

const NewSubscriptionEmail = {
	compile: (data: NewSubscriptionEmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/new-subscription.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Next Therapist - Subscription Confirmation',
			HtmlBody: template(data),
		}
	},
}

export { PlanChangeEmail, SubscriptionRenewalEmail, NewSubscriptionEmail }
