import fs from 'fs'
import Handlebars from 'handlebars'
import { User } from '@/src/models'

type EmailArgs = {
	email: string
	user: User
    reason: string
}

const AdminDeactivateEmail = {
	compile: (data: EmailArgs) => {
		const file = fs.readFileSync(
			'src/templates/emails/admin-deactivate-therapist.html',
			'utf8'
		)
		const template = Handlebars.compile(file)
		return {
			From: process.env.INFO_EMAIL_ADDRESS as string,
			To: data.email,
			Subject: 'Account Deactivation Notice',
			HtmlBody: template(data),
		}
	},
}

export { AdminDeactivateEmail }
