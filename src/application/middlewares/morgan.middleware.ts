import morgan from "morgan";
import logger from "@/src/configs/logger.config";

const stream = {
  write: (message: any) => logger.http(message),
};

const skip = () => {
  const env = process.env.NODE_ENV || "development";
  return env !== "development";
};

const MorganMiddleware = morgan(
  ":remote-addr :method :url :status :res[content-length] - :response-time ms",
  {
    stream,
    skip,
  }
);

export default MorganMiddleware;
