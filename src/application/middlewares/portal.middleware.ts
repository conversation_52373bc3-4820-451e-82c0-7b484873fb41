import jwt from 'jsonwebtoken'
import express, { Request } from 'express'
import { User } from '@/src/models'
import { HttpStatusCode } from 'axios'

declare module 'jsonwebtoken' {
	export interface CustomJWTPayload extends jwt.JwtPayload {
		id: string
	}
}

const PortalMiddleware = async (
	req: Request,
	res: express.Response,
	next: express.NextFunction
) => {
	if (req.headers.authorization) {
		try {
			const token = req.headers.authorization.split(' ')[1]
			const { id, type, iat, exp } = <jwt.CustomJWTPayload>(
				jwt.verify(token, process.env.JWT_SECRET_KEY as string)
			)
			// console.log(type);

			// if (type !== 'web') {
			// 	return res.status(HttpStatusCode.Unauthorized).send({
			// 		message: 'Invalid token',
			// 	})
			// }

			if (iat && exp) {
				const now = Math.floor(Date.now() / 1000)
				if (exp - now <= 0) {
					return res.status(HttpStatusCode.Unauthorized).send({
						message: 'Token expired',
					})
				}
			}
			const user = await User.findByPk(id)
			


			if (!user) {
				return res.status(HttpStatusCode.Unauthorized).send({
					message:
						'You are not authorized to perform this action...',
				})
			} else {
				req.user = user
				req.role = user.role
				if(user.version == 0)
					{
						return res.status(HttpStatusCode.Unauthorized).send({
							message:
								'You are not authorized to perform this action...',
						})
					}
				next()
			}

			// if (id === 'therapy') {
			// 	next()
			// } else {
				
			// }
		} catch (err) {
			console.log(err)
			return res.status(HttpStatusCode.Unauthorized).send({
				message: 'Invalid token',
			})
		}
	} else {
		return res.status(HttpStatusCode.Unauthorized).send({
			message: 'No token provided',
		})
	}
}

export default PortalMiddleware