import express, { Request, Response, NextFunction } from 'express'
import { User } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import { HttpStatusCode } from 'axios'

const CheckUserVerificationMiddleware = async (req: Request, res: Response, next: NextFunction) => {
	const user = await User.findByPk(req.user?.id)

	if (user?.emailVerifiedAt === null) {
		return res.status(HttpStatusCode.BadRequest).json({ message: 'Email not verified! Please verify your email to perform this action.' })
	}
	next()
}

export default CheckUserVerificationMiddleware
