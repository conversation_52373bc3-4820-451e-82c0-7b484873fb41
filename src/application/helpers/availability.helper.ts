import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import { NormalOfficeHours, Appointments, Calendar, TherapistWaitlist } from '@/src/models';
import { WaitlistNotifications } from '@/src/types/registration.interface';
import { getGoogleEvents, getOutlookEvents } from '@/src/application/repositories/calendar.repository';
import { FormattedOfficeHours } from './types.helper';

dayjs.extend(timezone);

export async function getAvailableOfficeHours({
	therapistId,
  officeHours,
  appointments,
  therapistTimeZone,
  calendars,
  waitlistNotifInfo,
  returnAvailableHours = false,
	returnHoursCount,
	therapistWaitlist,
}: {
	therapistId: number;
  officeHours: NormalOfficeHours[];
  appointments: Appointments[];
  therapistTimeZone: string;
  calendars: Calendar[];
  waitlistNotifInfo: WaitlistNotifications | null;
  returnAvailableHours?: boolean;
	returnHoursCount?: number;
	therapistWaitlist?: TherapistWaitlist | null;
}) {
  let maxLookaheadDays = 14; // Default to 14 days
  let timeAfterAdvanceNoticeAdjust: dayjs.Dayjs | undefined;
	const collectedSlots: FormattedOfficeHours[] = [];

  if (waitlistNotifInfo?.week_visibility) {
    const weekVisibility = Number(waitlistNotifInfo.week_visibility.trim());
    if (!isNaN(weekVisibility) && weekVisibility > 0) {
      maxLookaheadDays = weekVisibility * 7;
    }
  }

  if (waitlistNotifInfo?.booking_time_hour) {
    const advanceNoticeHour = Number(waitlistNotifInfo.booking_time_hour.trim());
    if (!isNaN(advanceNoticeHour) && advanceNoticeHour > 0) {
      timeAfterAdvanceNoticeAdjust = dayjs().tz(therapistTimeZone).add(advanceNoticeHour, 'hour');
    }
  }
  let currentDate = dayjs().tz(therapistTimeZone);

  for (let searchDay = 1; searchDay <= maxLookaheadDays; searchDay++) {
    const dayOfWeek = currentDate.format('dddd');
    const isToday = currentDate.isSame(dayjs().tz(therapistTimeZone), 'day');

    let dayOfficeHours = officeHours.filter(
      oh => oh.workingDay.toLowerCase() === dayOfWeek.toLowerCase()
    );

    if (isToday) {
      const nowTime = currentDate.format('HH:mm:00');
      dayOfficeHours = dayOfficeHours.filter(
        oh => oh.availabilityHours >= nowTime
      );
    }
		
    if (dayOfficeHours.length === 0) {
      currentDate = currentDate.add(1, 'day');
      continue;
    }

    const filteredAppointments = appointments.filter(app => {
      const appTime = dayjs(app.appointmentDate).tz(therapistTimeZone);
      const startOfDay = isToday ? currentDate.second(0).millisecond(0) : currentDate.startOf('day');
      const endOfDay = currentDate.endOf('day');
      return (
				appTime.isSame(startOfDay) ||
				(appTime.isAfter(startOfDay) && appTime.isBefore(endOfDay)) ||
				appTime.isSame(endOfDay)
			);
    });

		if (filteredAppointments.length > 0) {
			dayOfficeHours = dayOfficeHours.filter(oh => {
				const [hour, minute] = oh.availabilityHours.split(':').map(Number);
				const slotStart = currentDate.hour(hour).minute(minute).second(0).millisecond(0);
				const slotEnd = slotStart.add(1, 'hour');

				return !filteredAppointments.some(app => {
					const appStart = dayjs(app.appointmentDate).tz(therapistTimeZone);
					const appEnd = appStart.add(app.duration, 'minute');

					return appStart.isBefore(slotEnd) && appEnd.isAfter(slotStart);
				});
			});
		}

		if (dayOfficeHours.length === 0) {
			currentDate = currentDate.add(1, 'day');
			continue;
		}

		let availableSlots: FormattedOfficeHours[] = dayOfficeHours.map((officeHour) => {
			const [hour, minute] = officeHour.availabilityHours.split(':').map(Number);
			const dateWithTime = currentDate.hour(hour).minute(minute).second(0).millisecond(0);
			const dateTimeUTC = dateWithTime.toISOString();

			let isOnWaitlist = false;
			if (therapistWaitlist) {
				isOnWaitlist = true;
			}

			return {
				id: officeHour.id,
				day: officeHour.workingDay,
				dateTime: dateWithTime.format(), // therapist time zone
				dateTimeUTC,
				therapistTimeZone,
				appointmentMethod: officeHour.appointmentMethod,
				isOnWaitlist,
			};
		});


    // filter by advance notice time
		if (timeAfterAdvanceNoticeAdjust) {
			availableSlots = availableSlots.filter((slot) =>
				dayjs(slot.dateTime).isAfter(timeAfterAdvanceNoticeAdjust)
			);
		}

		if (availableSlots.length === 0) {
			currentDate = currentDate.add(1, 'day');
			continue;
		}

		const googleCalendar = calendars?.find(calendar => calendar.type === 'google');
		const outlookCalendar = calendars?.find(calendar => calendar.type === 'outlook');

    const startOfDay = isToday
      ? currentDate.millisecond(0).toISOString()
      : currentDate.startOf('day').millisecond(0).toISOString();
    const endOfDay = currentDate.endOf('day').millisecond(0).toISOString();

    // for google calendar
		if (googleCalendar) {
			const { tokens: googleTokens } = googleCalendar?.credentials as any;

			if (googleTokens) {
				const googleEvents = await getGoogleEvents({
					tokens: googleTokens,
					forDate: true,
					startDate: startOfDay,
					endDate: endOfDay,
				});

				if (googleEvents && googleEvents.length > 0) {
					const googleEventsList = googleEvents;

					const googleEventTimes =
						googleEventsList?.map((event: any) => ({
							start: event?.start?.timeZone === 'UTC'
								? dayjs.utc(event.start.dateTime).toISOString()
								: dayjs(event.start.dateTime).toISOString(),
							end: event?.end?.timeZone === 'UTC'
								? dayjs.utc(event.end.dateTime).toISOString()
								: dayjs(event.end.dateTime).toISOString(),
						})) || [];

					// Filter out conflicting office hours
					availableSlots = availableSlots.filter((officeHour) => {
						const officeHourStart = dayjs(officeHour.dateTimeUTC);
						const officeHourEnd = officeHourStart.add(1, 'hour');

						const isOverlapping = googleEventTimes.some((event) => {
							return (
								dayjs(event.start).isBefore(officeHourEnd) &&
								dayjs(event.end).isAfter(officeHourStart)
							);
						});

						return !isOverlapping;
					});
				}
			}
		}
		
		if (availableSlots.length === 0) {
			currentDate = currentDate.add(1, 'day');
			continue;
		}
		
		// for outlook calendar
		if (outlookCalendar) {
			const { tokens: outlookTokens } = outlookCalendar?.credentials as any;

			if (outlookTokens) {
				const outlookEvents = await getOutlookEvents({
					tokens: outlookTokens,
					userId: therapistId,
					forDate: true,
					startDate: startOfDay,
					endDate: endOfDay
				})

				if (outlookEvents && outlookEvents.length > 0) {
					const outlookEventList = outlookEvents;

					const outlookEventTimes =
						outlookEventList?.map((event: any) => ({
							start: event?.start?.timeZone === 'UTC'
								? dayjs.utc(event.start.dateTime).toISOString()
								: dayjs(event.start.dateTime).toISOString(),
							end: event?.end?.timeZone === 'UTC'
								? dayjs.utc(event.end.dateTime).toISOString()
								: dayjs(event.end.dateTime).toISOString(),
						})) || [];

					// Filter out conflicting office hours
					availableSlots = availableSlots.filter((officeHour) => {
						const officeHourStart = dayjs(officeHour.dateTimeUTC);
						const officeHourEnd = officeHourStart.add(1, 'hour');

						const isOverlapping = outlookEventTimes.some((event: any) => {
							return (
								dayjs(event.start).isBefore(officeHourEnd) &&
								dayjs(event.end).isAfter(officeHourStart)
							);
						});

						return !isOverlapping;
					});
				}
			}
		}

    if (availableSlots.length > 0) {
			if (returnAvailableHours) {
				collectedSlots.push(...availableSlots);

				if (returnHoursCount && collectedSlots.length >= returnHoursCount) {
          collectedSlots.sort((a, b) => dayjs(a.dateTime).valueOf() - dayjs(b.dateTime).valueOf());
          return collectedSlots.slice(0, returnHoursCount);
        }
			} else {
				return true;
			}
		}		

    currentDate = currentDate.add(1, 'day');
  }

  if (returnAvailableHours) {
    collectedSlots.sort((a, b) => dayjs(a.dateTime).valueOf() - dayjs(b.dateTime).valueOf());
    return collectedSlots;
  }

  return false;
}
