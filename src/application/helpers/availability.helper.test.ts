import sinon from 'sinon';
import { expect } from 'chai';
import * as CalendarRepository from '@/src/application/repositories/calendar.repository';
import { getAvailableOfficeHours } from '@/src/application/helpers/availability.helper';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(timezone);

const TEST_TIMEZONE = 'America/New_York';

describe('Availability Helper Test', function () {
	let sandbox: sinon.SinonSandbox;

	let getGoogleEventsStub: sinon.SinonStub;
	let getOutlookEventsStub: sinon.SinonStub;

	const day1 = dayjs().tz(TEST_TIMEZONE).add(1, 'day');
	const day2 = dayjs().tz(TEST_TIMEZONE).add(2, 'day');
	const day3 = dayjs().tz(TEST_TIMEZONE).add(3, 'day');
	const day4 = dayjs().tz(TEST_TIMEZONE).add(4, 'day');

	let dummyArguments = {
		therapistId: 1,
		officeHours: [
			{
				id: '2',
				workingDay: day1.format('dddd').toLowerCase(),
				availabilityHours: '09:00:00',
				appointmentMethod: 'telehealth',
			},
			{
				id: '3',
				workingDay: day2.format('dddd').toLowerCase(),
				availabilityHours: '10:00:00',
				appointmentMethod: 'in-person',
			},
			{
				id: '4',
				workingDay: day3.format('dddd').toLowerCase(),
				availabilityHours: '11:00:00',
				appointmentMethod: 'in-person',
			},
			{
				id: '5',
				workingDay: day4.format('dddd').toLowerCase(),
				availabilityHours: '12:00:00',
				appointmentMethod: 'in-person',
			},
		],
		appointments: [
			{
				duration: 60,
				appointmentDate: day1.hour(9).minute(0).second(0).millisecond(0).toISOString(),
			},
			{
				duration: 60,
				appointmentDate: day2.hour(10).minute(0).second(0).millisecond(0).toISOString(),
			}
		],
		therapistTimeZone: TEST_TIMEZONE,
		calendars: [
			{
				type: 'google',
				email: '<EMAIL>',
				credentials: {
					tokens: {
						access_token: 'google_access_token',
						refresh_token: 'google_refresh_token'
					}
				}
			},
			{
				type: 'outlook',
				email: '<EMAIL>',
				credentials: {
					tokens: {
						access_token: 'outlook_access_token',
						refresh_token: 'outlook_refresh_token'
					}
				}
			}
		],
		waitlistNotifInfo: {
			week_visibility: '1',
			booking_time_hour: '6',
		},
		returnAvailableHours: false,
		returnHoursCount: null,
		therapistWaitlist: {
			id: 1,
			patientId: 1,
			therapistId: 1,
			status: 'waiting',
		},
	} as any;

	let dummyGoogleEvents = [
		{
			start: {
				dateTime: day3.hour(11).minute(0).second(0).millisecond(0).toISOString(),
				timeZone: 'UTC'
			},
			end: {
				dateTime: day3.hour(12).minute(0).second(0).millisecond(0).toISOString(),
				timeZone: 'UTC'
			},
		}
	];

	let dummyOutlookEvents = [
		{
			start: {
				dateTime: day4.hour(12).minute(0).second(0).millisecond(0).toISOString(),
				timeZone: 'UTC',
			},
			end: {
				dateTime: day4.hour(13).minute(0).second(0).millisecond(0).toISOString(),
				timeZone: 'UTC',
			},
		}
	];

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		getGoogleEventsStub = sandbox.stub(CalendarRepository, 'getGoogleEvents');
		getOutlookEventsStub = sandbox.stub(CalendarRepository, 'getOutlookEvents');
	});

	afterEach(function() {
		sandbox.restore();
	});

	it('should return false if no available slots found', async function () {
		getGoogleEventsStub.resolves(dummyGoogleEvents);
		getOutlookEventsStub.resolves(dummyOutlookEvents);

		const result = await getAvailableOfficeHours(dummyArguments);

		expect(result).to.be.false;
	});

	it('should return empty array if no available slots found', async function () {
		getGoogleEventsStub.resolves(dummyGoogleEvents);
		getOutlookEventsStub.resolves(dummyOutlookEvents);

		const result = await getAvailableOfficeHours({
			...dummyArguments,
			returnAvailableHours: true,
			returnHoursCount: 2,
		});

		expect(result).to.be.an('array').that.is.empty;
	});

	it('should return true if available slots found', async function () {
		getGoogleEventsStub.resolves([]);
		getOutlookEventsStub.resolves([]);

		const result = await getAvailableOfficeHours(dummyArguments);

		expect(result).to.be.true;
	});

	it('should return array of available slots if available slots found', async function () {
		getGoogleEventsStub.resolves(dummyGoogleEvents);
		getOutlookEventsStub.resolves(dummyOutlookEvents);

		const result = await getAvailableOfficeHours({
			...dummyArguments,
			appointments: [],
			returnAvailableHours: true,
			returnHoursCount: 2,
		});

		expect(result).to.be.an('array').that.is.of.length(2);
	});
});