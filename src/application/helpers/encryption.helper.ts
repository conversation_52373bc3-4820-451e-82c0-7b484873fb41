import crypto from 'crypto'
const algorithm = 'aes-256-cbc'
const iv = crypto.randomBytes(16)
const { ENCRYPTION_KEY } = process.env

// Encrypting text
export function encrypt(text: string) {
	const cipher = crypto.createCipheriv(
		algorithm,
		Buffer.from(ENCRYPTION_KEY as string, 'hex'),
		iv
	)
	let encrypted = cipher.update(text)
	encrypted = Buffer.concat([encrypted, cipher.final()])
	return { iv: iv.toString('hex'), encryptedData: encrypted.toString('hex') }
}

// Decrypting text
export function decrypt(text: any) {
	const iv = Buffer.from(text.iv, 'hex')
	const encryptedText = Buffer.from(text.encryptedData, 'hex')
	const decipher = crypto.createDecipheriv(
		algorithm,
		Buffer.from(ENCRYPTION_KEY as string, 'hex'),
		iv
	)
	let decrypted = decipher.update(encryptedText)
	decrypted = Buffer.concat([decrypted, decipher.final()])
	return decrypted.toString()
}

export function encryptedB64(text: any) {
	const { iv, encryptedData } = encrypt(text)
	return Buffer.from(`${iv}:${encryptedData}`).toString('base64url')
}

export function decryptedB64(text: string) {
	const b64 = Buffer.from(text, 'base64url').toString('utf-8')
	const [iv, encryptedData] = b64.split(':')
	return decrypt({
		iv,
		encryptedData,
	})
}


export function createUniqueCharGenerator(length:number) {
	const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

	let result = '';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }