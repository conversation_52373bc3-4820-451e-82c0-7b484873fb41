import klawSync from "klaw-sync";
import { type Application } from "express";
import path from "path";


export const useJob = () => {
	const jobsPath = klawSync(path.join(__dirname, "..", "/jobs"), { 
		nodir: true,
		filter: (item) => {
			// Skip test files and source maps
			const baseName = path.basename(item.path);
			return !baseName.endsWith('.test.js') && 
			       !baseName.endsWith('.test.ts') && 
			       !baseName.endsWith('.map');
		}
	});
	let jobsCount = 0;
	jobsPath.forEach(async (file) => {
		try {
			// eslint-disable-next-line @typescript-eslint/no-var-requires
			const job = require(file.path);
			if (job.default) {
				job.default();
				jobsCount++;
			}
		} catch (error) {
			console.error(`Error loading job ${file.path}:`, error);
		}
	});
	console.log(`Loaded jobs: ${jobsCount}`);
};
