import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(timezone);

export const generateString = (length:number):string => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

    let result = '';
    const charactersLength = characters.length;
    for ( let i = 0; i < length; i++ ) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}

export const generateSlug = (str: string): string => {
    return str
        .replace(/\(.*?\)/g, '') // Remove any content inside parentheses
        .replace(/'/g, '') // Remove apostrophes
        .trim() // Remove leading and trailing whitespace
        .replace(/[^a-zA-Z0-9\s]/g, '-') // Replace non-word characters with hyphens
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .toLowerCase() // Convert to lowercase
        .replace(/^-+|-+$/g, '') // Trim leading/trailing hyphens
        .replace(/-+/g, '-'); // Replace consecutive hyphens with a single hyphen
}

export const splitAndGenerateTime = (timeString: string): string => {
    const [time, ampm] = timeString.split(' ');
    let [hours, minutes] = time.split(':');

    // Convert hours to a number, modify, then convert back to a string
    if (ampm.toLowerCase() === 'pm' && hours !== '12') {
        hours = (parseInt(hours, 10) + 12).toString();
    } else if (ampm.toLowerCase() === 'am' && hours === '12') {
        hours = '00'; // Convert 12 AM to 00:00 in 24-hour format
    }

    // Return only the time part in HH:mm:ss format
    return `${hours.padStart(2, '0')}:${minutes.padStart(2, '0')}:00`;
}

export const getTimezoneAbbr = (timezone: string) => {
  return Intl.DateTimeFormat('en', { 
    timeZone: timezone, 
    timeZoneName: 'short' 
  }).formatToParts(new Date())
    .find(part => part.type === 'timeZoneName')?.value;
};

export const formatDuration = (durationInMinutes: number): string => {
  const hours = Math.floor(durationInMinutes / 60);
  const minutes = durationInMinutes % 60;
  
  let formatted = '';
  if (hours) {
    formatted += `${hours} hour${hours > 1 ? 's' : ''}`;
  }
  if (minutes) {
    if (hours) {
      formatted += ' ';
    }
    formatted += `${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
  
  return formatted || '0 minutes';
}

export const isValidTimeZone = (timezone: string): boolean => {
  try {
    // Attempt to format using the timezone
    dayjs().tz(timezone).format();
    return true;
  } catch (e) {
    return false;
  }
};

export function capitalizeFirstLetter(text: string): string {
  if (!text) return "";
  return text
    .split(" ") // Split by spaces
    .map(word =>
      word
        .split("-") // Split by hyphen
        .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()) // Capitalize each part
        .join("-") // Rejoin hyphenated parts
    )
    .join(" "); // Join words back
}

export function maskEmail(email: string) {
  const [local, domain] = email.split("@");
  const maskedLocal = local[0] + "*".repeat(local.length - 2) + local.slice(-1);
  return maskedLocal + "@" + domain;
}

export function maskPhoneNumber(phone: string) {
  if (phone.length < 5) return "Invalid phone format";
  const maskedPart = "*".repeat(phone.length - 5);
  return phone.slice(0, 3) + maskedPart + phone.slice(-2);
}
