import logger from '@/src/configs/logger.config'
import { HttpStatusCode } from 'axios'
import { NextFunction, Request, Response } from 'express'
import { validationResult } from 'express-validator'

export const wrap = (callback: Function) => {
	return async (req: Request, res: Response, next: NextFunction) => {
		const errors = validationResult(req)
		if (!errors.isEmpty()) {
			const e = errors.mapped()
			Object.keys(e).forEach((key) => {
				e[key] = e[key].msg
			})
			const message = Object.values(e)[0] || 'Validation Error'
			return res.status(HttpStatusCode.BadRequest).send({
				message: message,
				errors: e,
			})
		} else {
			try {
				await callback(req, res, next)
			} catch (error: any) {
				if(process.env.NODE_ENV!=="test") {
					console.log(error);
					logger.error(error.stack)
				}

				res.status(
					error.statusCode || HttpStatusCode.InternalServerError
				).send({
					message: error.message,
					data: error.data,
				})
			}
		}
	}
}
