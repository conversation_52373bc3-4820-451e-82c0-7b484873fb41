import { Request } from 'express';

const getPage = (req: Request) => parseInt(req.query.page as string) || 1;
const getPerPage = (req: Request) =>
	parseInt(req.query.perPage as string) || 10;

/**
 *
 * @param req Express Request
 * @returns {}
 */
const paginated = (req: Request, perPage: number | null = null) => {
	const page = getPage(req);
	const limit = perPage ?? getPerPage(req);
	return {
		limit,
		offset: page * limit - limit
	};
};

/**
 *
 * @param data - data to paginate
 * @param req Express Request
 * @returns {PaginatedData<T>}
 */
// eslint-disable-next-line @typescript-eslint/ban-types, @typescript-eslint/no-explicit-any
const paginatedData = (data: {} | any, req: Request) => {
	const { count: total, rows: items } = data;
	const page = getPage(req);
	const perPage = getPerPage(req);
	const totalItems = Array.isArray(total)
		? total.length > 0
			? total[0].count
			: 0
		: total;
	return {
		data: items,
		meta: {
			currentPage: page,
			perPage,
			total: totalItems,
			lastPage: Math.ceil(totalItems / perPage),
			nextPage: Math.ceil(totalItems / perPage) > page ? page + 1 : null,
			prevPage: page > 1 ? page - 1 : null
		}
	};
};

export { paginated, paginatedData };
