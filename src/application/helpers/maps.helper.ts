import axios from 'axios'

export const getDistance = async (origin: string, destination: string) => {
	const url = `https://maps.googleapis.com/maps/api/distancematrix/json?units=imperial&origins=${origin}&destinations=${destination}&key=${process.env.GOOGLE_MAPS_API_KEY}`
	const response = await axios.get(url)
	const { data } = response
	const { rows } = data
	const { elements } = rows[0]
	const { distance } = elements[0]
	const { text } = distance
	return text
}

export const getDistanceInMiles = async (
	origin: string,
	destination: string
) => {
	const distance = await getDistance(origin, destination)
	const distanceInMiles = parseFloat(distance.split(' ')[0])
	return distanceInMiles
}

export const getCoordinates = async (address: string) => {
	const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${process.env.GOOGLE_MAPS_API_KEY}`
	const response = await axios.get(url)
	const { data } = response
	const { results } = data
	const { geometry } = results[0]
	const { location } = geometry
	return location
}
