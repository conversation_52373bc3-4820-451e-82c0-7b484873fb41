import { expect } from 'chai';
import sinon from 'sinon';

import * as paginationHelper from '@/src/application/helpers/pagination.helper';
import * as repository from '@/src/application/repositories/event-tag.repository';
import { EventTag } from '@/src/models';

describe('EventTag Repository', () => {
  const sandbox = sinon.createSandbox();

  const mockRequest = (query = {}, body = {}, params = {}) =>
    ({ query, body, params } as any);

  beforeEach(() => {
    sandbox.stub(paginationHelper, 'paginated').returns({ offset: 0, limit: 10 });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getTags', () => {
    it('should fetch paginated tags sorted by createdAt DESC', async () => {
      const req = mockRequest();
      const resultMock = { rows: [], count: 0 };

      const findStub = sandbox.stub(EventTag, 'findAndCountAll').resolves(resultMock as any);

      const result = await repository.getTags({ req, perPage: 10 });

      expect(findStub.calledOnce).to.be.true;
      const callArgs = findStub.firstCall.args[0];

      expect(callArgs.order).to.deep.equal([['createdAt', 'DESC']]);

		if (typeof callArgs.attributes === 'object' && 'include' in callArgs.attributes) {
		expect((callArgs.attributes as any).include).to.deep.equal([]);
		} else {
		// fallback if attributes isn't the object form
		expect(callArgs.attributes).to.be.undefined;
		}
    });
  });

  describe('createTag', () => {
    it('should be a defined function', () => {
      expect(repository.createTag).to.be.a('function');
    });

    it('should currently do nothing (noop)', async () => {
      const req = mockRequest({}, { name: 'Test Tag' });
      const result = await repository.createTag(req);
      expect(result).to.be.undefined;
    });
  });
});
