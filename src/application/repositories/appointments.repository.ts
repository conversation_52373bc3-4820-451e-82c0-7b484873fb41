import { Appointments, User,MinorPatient } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Order,
	WhereOptions,
	Op, fn, col
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { NotFoundError } from '../handlers/errors'
import sequelize, { Sequelize } from 'sequelize/lib/sequelize'
import dayjs from 'dayjs';
import logger from '@/src/configs/logger.config'

type EventFilter = {
	req: Request
	perPage?: number
}

type CategorizeFilter = {
	req: Request;
	month?: number;
	year?: number;
	perPage?: number;
};


export const getappointments = async ({ req, perPage }: EventFilter) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = []
	const relations: any = []
	const attributes: FindAttributeOptions = []

	order.push(['createdAt', 'DESC'])

	return Appointments.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req, perPage),
	})
}

export const createAppointments = async (req: Request) => {
	const {
		description,
		appointmentDate,
		appointmentTime,
		location,
		isAllDay,
		isRecurring,
		therapistId,
		patientId,
		name,
		workingHourId
	} = req.body;

	logger.info(`Creating appointment: Therapist=${therapistId}, Patient=${patientId}, Date=${appointmentDate}, Time=${appointmentTime}`);

	// Validate mandatory fields
	if (!therapistId || !patientId || !appointmentDate || !appointmentTime || !name) {
		throw new Error('Therapist ID, Patient ID, appointmentDate, and appointmentTime are required.');
	}

	const existingAppointment = await Appointments.findOne({
		where: {
			therapistId,
			patientId
		}
	});

	const initial_appointment = !existingAppointment;

	// Create the appointment
	const event = await Appointments.create({
		description,
		appointmentDate, // full date and time
		appointmentTime, // only time part (HH:mm)
		isAllDay,
		isRecurring,
		therapistId,
		patientId,
		name,
		workingHourId,
		initial_appointment
	});
	logger.info(`Appointment created successfully: ID=${event.id}`);
	return event;
};




export const getEventById = async (id: number | string) => {
	const event = await Appointments.findByPk(id)
	if (!event) throw new NotFoundError('Event not found')
	return event
}

export const updateEvent = async (req: Request) => {
	const event = await getEventById(req.params.id)
	const {
		name,
		description,
		startDate,
		endDate,
		tagId,
		isAllDay,
		isRecurring,
		location,
		attendees,
	} = req.body

	return event.update({
		name,
		description,
		startDate,
		endDate,
		tagId,
		isAllDay,
		isRecurring,
		location,
		attendees,
	})
}

export const deleteEvent = async (id: number | string) => {
	const event = await getEventById(id)
	return event.destroy()
}

export const getMonthlyEvents = async ({ req, month, year, perPage }: CategorizeFilter) => {
	const where = {
		[Op.and]: [
			// Filter by month and year if provided
			month && sequelize.where(
				sequelize.fn('EXTRACT', 'MONTH', sequelize.col('startDate')),
				month
			),
			year && sequelize.where(
				sequelize.fn('EXTRACT', 'YEAR', sequelize.col('startDate')),
				year
			),
		],
	};

	// Fetch events with pagination
	const events = await Appointments.findAndCountAll({
		where,
		order: [['startDate', 'ASC']], // Order by start date
		...paginated(req, perPage),
	});

	return events;
};

export const getAppointmentsByMonthYear = async ({ req, month, year, perPage }: CategorizeFilter) => {
    const userId = req.user?.id;
    const today = dayjs().startOf('day');
    const oneMonthFromToday = today.add(1, 'month');
    const where = {
        patientId: userId,
        [Op.or]: [
            {
                appointmentDate: {
                    [Op.lt]: today.toISOString(), // Past appointments
                },
            },
            {
                appointmentDate: {
                    [Op.between]: [today.toISOString(), oneMonthFromToday.toISOString()], // Future appointments within one month
                },
            },
        ],
    };

	logger.info(`Fetching appointments for UserID=${userId} for Month=${month}, Year=${year}`);

    // Fetch appointments with pagination and sorting by appointmentDate
    const appointmentsData = await Appointments.findAndCountAll({
        where,
        include: [
            {
                model: User,
                as: 'therapist',
                where: { role: 'therapist' },
                attributes: {
                    include: [
                        'id',
                        'firstname',
                        'lastname',
                        'email',
                        // Extract profile_picture from user_registration_informations table
                        [
                            Sequelize.literal(`(
                  SELECT "payloadInfo"->>'session_fee'
                  FROM "user_registration_informations"
                  WHERE "user_registration_informations"."userId" = "therapist"."id"
                    AND "user_registration_informations"."pageName" = 'payment-forms'
                  LIMIT 1
                )`),
                            'session_fee',
                        ],

                        [
                            Sequelize.literal(`(
                  SELECT json_build_object(
                    'lat', ("payloadInfo"->'business_address'->>'lat')::float,
                    'lng', ("payloadInfo"->'business_address'->>'lng')::float,
                    'city', "payloadInfo"->'business_address'->>'city',
                    'state', "payloadInfo"->'business_address'->>'state',
                    'street', "payloadInfo"->'business_address'->>'street',
                    'country', "payloadInfo"->'business_address'->>'country',
                    'zipCode', "payloadInfo"->'business_address'->>'zipCode',
                    'place_id', "payloadInfo"->'business_address'->>'place_id',
                    'description', "payloadInfo"->'business_address'->>'description',
                    'full_address', "payloadInfo"->'business_address'->>'full_address'
                  )
                  FROM "user_registration_informations"
                  WHERE "user_registration_informations"."userId" = "therapist"."id"
                    AND "user_registration_informations"."pageName" = 'practice-info'
                  LIMIT 1
                )`),
                            'address',
                        ],
                    ],
                },
            },
            {
                model: MinorPatient,
                as: 'minorPatient',
                required: false,
                attributes: ['firstName', 'lastName'],
            },
        ],
        order: [
            [
              Sequelize.literal(`
                CASE 
                  WHEN DATE("appointmentDate") = DATE('${today.toISOString()}') THEN 0
                  WHEN "appointmentDate" BETWEEN '${today.toISOString()}' AND '${oneMonthFromToday.toISOString()}' THEN 1
                  WHEN "appointmentDate" < '${today.toISOString()}' THEN 3
                  ELSE 2
                END
              `),
              'ASC'
            ],
            ['appointmentDate', 'ASC']
        ],
		
        ...paginated(req, perPage), // Pagination helper function
    });

    return appointmentsData;
};
