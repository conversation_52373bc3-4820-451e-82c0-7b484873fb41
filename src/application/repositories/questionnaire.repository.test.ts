import { createQuestionnaire, getQuestionnaireById, getQuestionnaires, updateQuestionnaire } from './questionnaire.repository';
import sinon from 'sinon';
import { Questionnaire } from '@/src/models';
import { expect } from 'chai';

describe('Questionnaire Repository Test', function () {
	let sandbox: sinon.SinonSandbox;

	let quesFindAndCountAllStub: sinon.SinonStub;
	let quesCreateStub: sinon.SinonStub;
	let quesFindByPkStub: sinon.SinonStub;

	let dummyQuestionnaire= {
		id: 1,
		question: 'question',
		info: 'info',
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		quesFindAndCountAllStub = sandbox.stub(Questionnaire, 'findAndCountAll');
		quesCreateStub = sandbox.stub(Questionnaire, 'create');
		quesFindByPkStub = sandbox.stub(Questionnaire, 'findByPk');
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('getQuestionnaires', function() {
		it('should return paginated questionnaire list successfully', async function () {
			quesFindAndCountAllStub.resolves({
				count: 1,
				rows: [dummyQuestionnaire]
			});

			const req = {
				query: {
					search: 'search',
				}
			} as any;

			const result = await getQuestionnaires(req);

			expect(result).to.have.property('count').to.be.equal(1);
			expect(result).to.have.property('rows').to.be.an('array').of.length(1);
		});
	});

	describe('createQuestionnaire', function() {
		it('should create questionnaire successfully', async function () {
			quesCreateStub.resolves(dummyQuestionnaire);

			const req = {
				body: {
					question: 'question',
					info: 'info',
				}
			} as any;

			const result = await createQuestionnaire(req);

			expect(result).to.be.an('object').to.include.keys('question', 'info');
		});
	});

	describe('getQuestionnaireById', function() {
		it('should throw error if questionnaire not found', async function () {
			quesFindByPkStub.resolves(null);

			try {
				await getQuestionnaireById('1');
				throw new Error('Test should have thrown error but did not');
			} catch (error: any) {
				expect(error).to.be.instanceOf(Error);
				expect(error.message).to.equal('Questionnaire not found');
				expect(error.statusCode).to.equal(404);
			};
		});

		it('should return questionnaire successfully', async function () {
			quesFindByPkStub.resolves(dummyQuestionnaire);

			const result = await getQuestionnaireById('1');

			expect(result).to.be.an('object').to.include.keys('question', 'info');
		});
	});

	describe('updateQuestionnaire', function() {
		it('should update questionnaire successfully', async function () {
			quesFindByPkStub.resolves({
				...dummyQuestionnaire,
				update: () => Promise.resolve(),
			});

			const req = {
				params: {
					id: '1',
				},
				body: {
					question: 'updated question',
					info: 'updated info',
				}
			} as any;

			const result = await updateQuestionnaire(req);

			expect(result).to.be.an('object').to.include.keys('question', 'info');
		});
	});
});