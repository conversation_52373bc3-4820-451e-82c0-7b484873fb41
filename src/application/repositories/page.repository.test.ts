import sinon from 'sinon';
import { AnswerFollowThrough, Page, PageAnswer } from '@/src/models';
import { expect } from 'chai';
import { createPage, getAllPages, getPageById, matcherInvolvement, startPage, updatePage } from './page.repository';
import { HttpStatusCode } from 'axios';

describe('Page Repository Test', function () {
	let sandbox: sinon.SinonSandbox;

	let pageFindAndCountAllStub: sinon.SinonStub;
	let pageFindByPkStub: sinon.SinonStub;
	let pageCreateStub: sinon.SinonStub;
	let pageFindOneStub: sinon.SinonStub;
	let pageUpdateStub: sinon.SinonStub;
	let pageAnswerFindAllStub: sinon.SinonStub;
	let pageAnswerBulkCreateStub: sinon.SinonStub;
	let pageAnswerDestroyStub: sinon.SinonStub;
	let pageAnswerUpdateStub: sinon.SinonStub;
	let pageAnswerCreateStub: sinon.SinonStub;
	let pageAnswerFindOrCreateStub: sinon.SinonStub;
	let ansFollowThroughFindAndCountAllStub: sinon.SinonStub;

	let dummyPage= {
		id: 1,
		code: 'code',
		title: 'title',
		info: 'info',
		category: 'category',
		isFollowedThrough: true,
	}

	let dummyPageAnswer = {
		answerId: 1,
		followedByAns: 2,
	}

	let dummyAnsFollowThrough = {
		followingBy: 1,
		followingTo: 2,
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		pageFindAndCountAllStub = sandbox.stub(Page, 'findAndCountAll');
		pageFindByPkStub = sandbox.stub(Page, 'findByPk');
		pageCreateStub = sandbox.stub(Page, 'create');
		pageFindOneStub = sandbox.stub(Page, 'findOne');
		pageUpdateStub = sandbox.stub(Page, 'update');
		pageAnswerFindAllStub = sandbox.stub(PageAnswer, 'findAll');
		pageAnswerBulkCreateStub = sandbox.stub(PageAnswer, 'bulkCreate');
		pageAnswerDestroyStub = sandbox.stub(PageAnswer, 'destroy');
		pageAnswerUpdateStub = sandbox.stub(PageAnswer, 'update');
		pageAnswerCreateStub = sandbox.stub(PageAnswer, 'create');
		pageAnswerFindOrCreateStub = sandbox.stub(PageAnswer, 'findOrCreate');
		ansFollowThroughFindAndCountAllStub = sandbox.stub(AnswerFollowThrough, 'findAndCountAll');
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('getAllPages', function() {
		it('should return paginated page list successfully', async function () {
			pageFindAndCountAllStub.resolves({
				count: 1,
				rows: [dummyPage]
			});

			const req = {
				query: {
					category: 'category',
					type: 'type',
					search: 'search',
					exclude: 'exclude',
				}
			} as any;

			const result = await getAllPages(req);

			expect(result).to.have.property('count').to.be.equal(1);
			expect(result).to.have.property('rows').to.be.an('array').of.length(1);
		});
	});

	describe('getPageById', function() {
		it('should throw error if page not found', async function () {
			pageFindByPkStub.resolves(null);

			try {
				await getPageById('1');
				throw new Error('Test should have thrown but did not');
			} catch (error: any) {
				expect(error).to.be.instanceOf(Error);
				expect(error.message).to.equal('Page not found');
			};
		});

		it('should return page successfully', async function () {
			pageFindByPkStub.resolves({
				...dummyPage,
				dataValues: {},
			});
			pageAnswerFindAllStub.resolves([dummyPageAnswer]);
			ansFollowThroughFindAndCountAllStub({
				count: 1,
				rows: [dummyAnsFollowThrough]
			});

			const result = await getPageById('1');

			expect(result).to.be.an('object');
		});
	});

	describe('createPage', function() {
		const createReq = {
			body: {
				button: 'register',
				category: 'category',
				code: 'code',
				extra: 'extra',
				info: 'info',
				title: 'title',
				type: 'type',
				questionnaireId: 1,
				questionnaireType: 'questionnaireType',
				buttonClickAction: 'end',
				required: true,
				skipToPageId: 1,
				answers: [
					{
						id: 1,
						conditions: 'conditions',
						answerGroup: 'answerGroup',
						position: 'position',
						copiedFromPage: 1,
						followedByAns: 1,
						ansOrder: 1,
						followedBy: 1,
					}
				]
			}
		} as any;

		it('should throw error if page with same category and code already exists', async function () {
			pageFindOneStub.resolves(dummyPage)

			try {
				await createPage(createReq);
				throw new Error('Test should have thrown but did not');
			} catch (error: any) {
				expect(error).to.be.instanceOf(Error);
				expect(error.statusCode).to.equal(HttpStatusCode.BadRequest);
				expect(error.message).to.equal('Page with same category and code already exists');
			};
		});

		it('should create page successfully', async function () {
			pageFindOneStub.resolves(null);
			pageCreateStub.resolves(dummyPage);
			pageAnswerBulkCreateStub.resolves();

			const result = await createPage(createReq);

			expect(result).to.be.an('object');
		});
	});

	describe('updatePage', function() {
		const createReq = {
			body: {
				button: 'register',
				category: 'category',
				code: 'code',
				extra: 'extra',
				info: 'info',
				title: 'title',
				type: 'value',
				questionnaireId: 1,
				questionnaireType: 'questionnaireType',
				buttonClickAction: 'end',
				required: true,
				skipToPageId: 1,
				sortOrder: 1,
				answers: [
					{
						id: 1,
						pageAnswerId: null,
						conditions: 'conditions',
						answerGroup: 'answerGroup',
						position: 'position',
						copiedFromPage: 1,
						followedBy: null,
						order: 1,
					}
				],
				removedAnswers: [1, 2],
			},
			params: {
				id: '1',
			}
		} as any;

		it('should update page successfully and create page answers when page type is value', async function () {
			pageFindByPkStub.resolves({
				...dummyPage,
				isFollowedThrough: false,
				update: () => Promise.resolve(),
			});
			pageAnswerDestroyStub.resolves();
			pageAnswerUpdateStub.resolves();

			const result = await updatePage(createReq);

			expect(result).to.be.an('object');
			expect(pageAnswerCreateStub.calledOnce).to.be.true;
		});

		it('should update page successfully and update page answers when page type is not value or selectable', async function () {
			pageFindByPkStub.resolves({
				...dummyPage,
				isFollowedThrough: false,
				update: () => Promise.resolve(),
			});
			pageAnswerDestroyStub.resolves();
			pageAnswerFindOrCreateStub.resolves([
				{
					update: () => Promise.resolve(),
				},
				false,
			])

			const result = await updatePage({
				body: {
					...createReq.body,
					type: 'questionnaire',
				},
				params: {
					id: '1',
				}
			} as any);

			expect(result).to.be.an('object');
			expect(pageAnswerCreateStub.notCalled).to.be.true;
			expect(pageAnswerFindOrCreateStub.calledOnce).to.be.true;
		});

		it('should update page successfully and update page answers when isFollowedThrough is true', async function () {
			pageFindByPkStub.resolves({
				...dummyPage,
				isFollowedThrough: false,
				update: () => Promise.resolve(),
			});
			pageAnswerDestroyStub.resolves();
			pageAnswerFindOrCreateStub.resolves([
				{
					update: () => Promise.resolve(),
				},
				false,
			])

			const result = await updatePage({
				body: {
					...createReq.body,
					answers: [
						{
							...createReq.body.answers[0],
							followedBy: 1,
						}
					]
				},
				params: {
					id: '1',
				}
			} as any);

			expect(result).to.be.an('object');
			expect(pageAnswerCreateStub.notCalled).to.be.true;
			expect(pageAnswerFindOrCreateStub.calledOnce).to.be.true;
		});
	});

	describe('startPage', function() {
		it('should set page as start page successfully', async function () {
			pageFindByPkStub.resolves({
				...dummyPage,
				isFollowedThrough: false,
				update: () => Promise.resolve(),
			});
			pageUpdateStub.resolves();

			const result = await startPage({
				params: {
					id: '1',
				}
			} as any);

			expect(result).to.be.an('object');
		});
	});

	describe('matcherInvolvement', function() {
		it('should update matcher involvement of page successfully', async function () {
			pageFindByPkStub.resolves({
				...dummyPage,
				isFollowedThrough: false,
				matcherInvolvement: false,
				update: () => Promise.resolve(),
			});

			const result = await matcherInvolvement({
				params: {
					id: '1',
				}
			} as any);

			expect(result).to.be.an('object');
		});
	});
});