import { Request } from 'express';
import sequelize from '@/src/configs/database.config'
import { UserType } from '../helpers/constant.helper'
import { Answer, MatchingAlgoResource, Page, Questionnaire, TherapistPage } from '@/src/models';
import { WhereOptions } from 'sequelize';

export const createMatchingAlgoResource = async (req: Request) => {
  const algoData = req.body;

  const algoDataValues = Object.values(algoData).filter(Boolean);

  if (req.role === UserType.SYSTEM) {
    const insertData = algoDataValues.map((data: any) => ({
      therapistPageId: data.therapistPage.value,
      patientPageId: data.patientPage.value,
      category: data.category.value,
      questionId: data.question.value,
      answerId: data.answer?.value || null,
    }));

    const transaction = await sequelize.transaction();
    try {
      for (const data of insertData) {
        await MatchingAlgoResource.findOrCreate({
          where: {
            therapistPageId: data.therapistPageId,
            category: data.category,
          },
          defaults: data,
          transaction, // Ensure each operation is part of the transaction
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error; // Re-throw the error after rollback
    }
  }
}

export const getAllMatchingAlgoResources = async (req: Request) => {
  const { category } = req.query;
  
  let where: WhereOptions = {}
  if (category) {
		where.category = category
	}
  
  const matchingAlgoResources = await MatchingAlgoResource.findAll({
    where,
    include: [
      {
        model: TherapistPage,
        as: 'therapistPage',
      },
      {
        model: Page,
        as: 'patientPage',
      },
      {
        model: Questionnaire,
        as: 'questionnaire',
      },
      {
        model: Answer,
        as: 'answer',
      },
    ],
  });

  return matchingAlgoResources;
}
