import sinon from 'sinon';
import { User, UserDeleteDeactivateReason, UserRegistrationInfo } from '@/src/models';
import { expect } from 'chai';
import { deleteUser, getUsers, getUserById, getUserWithCalendarInfo, activateUser, deactivateUser } from './user.repository';
import { NotFoundError } from '../handlers/errors';
import sequelize from '@/src/configs/database.config';
import { Calendar } from '@/src/models';
import dayjs from 'dayjs';
import { mail } from '@/src/configs/sendgrid.config';
import logger from '@/src/configs/logger.config';
import { AdminDeactivateEmail } from '@/src/application/emails/admin-deactivate-therapist.email';

// Additional imports for full coverage
import { AdminDeleteEmail } from '../emails/admin-delete-therapist.email';
import { removePatientsFromTherapistWaitlist } from './therapist-waitlist.repository';
import { TherapistProfile } from '@/src/models';

describe('User Repository Test', function () {
  let sandbox: sinon.SinonSandbox;
  let findAndCountAllStub: sinon.SinonStub;
  let findByPkStub: sinon.SinonStub;
  let findOneStub: sinon.SinonStub;
  let updateStub: sinon.SinonStub;
  let createReasonStub: sinon.SinonStub;
  let findReasonStub: sinon.SinonStub;
  let transactionStub: sinon.SinonStub;
  let fakeTransaction: any;
  let sendMailStub: sinon.SinonStub;
  let loggerStub: sinon.SinonStub;
  let adminDeactivateEmailStub: sinon.SinonStub;
  let removePatientsStub: sinon.SinonStub;
  let dayjsFormatStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    findAndCountAllStub = sandbox.stub(User, 'findAndCountAll');
    findByPkStub = sandbox.stub(User, 'findByPk');
    findOneStub = sandbox.stub(User, 'findOne');
    updateStub = sandbox.stub();
    sandbox.stub(User, 'update').resolves();
    createReasonStub = sandbox.stub(UserDeleteDeactivateReason, 'create');
    findReasonStub = sandbox.stub(UserDeleteDeactivateReason, 'findOne');
    fakeTransaction = {
      commit: sandbox.stub(),
      rollback: sandbox.stub(),
      afterCommit: sandbox.stub(),
      LOCK: {},
    };
    transactionStub = sandbox.stub(sequelize, 'transaction').resolves(fakeTransaction);
    if ((mail.sendMail as any).restore) (mail.sendMail as any).restore();
    sendMailStub = sandbox.stub(mail, 'sendMail').resolves();
    loggerStub = sandbox.stub(logger, 'info');
    adminDeactivateEmailStub = sandbox.stub(AdminDeactivateEmail, 'compile').returns({ From: '<EMAIL>', To: '<EMAIL>', Subject: 'sub', HtmlBody: 'body' });
    removePatientsStub = sandbox.stub(require('./therapist-waitlist.repository'), 'removePatientsFromTherapistWaitlist').resolves(0);
    dayjsFormatStub = sandbox.stub(dayjs.prototype, 'format').returns('2024-01-01 00:00:00');
    sandbox.stub(TherapistProfile, 'findOne').resolves({ id: 1, userId: 1 } as any);
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should throw NotFoundError if user not found', async function () {
    findByPkStub.resolves(null);
    try {
      await deleteUser('id', 'date', 'reasonId', 'reason');
      throw new Error('Should have thrown');
    } catch (err) {
      expect(err as any).to.be.instanceOf(NotFoundError);
    }
  });

  describe('getUsers', function () {
    it('should return users with count', async function () {
      findAndCountAllStub.resolves({ count: 1, rows: [{}] });
      const req: any = { user: { id: 1 }, query: {} };
      const result = await getUsers({ req });
      expect(result.count).to.equal(1);
      expect(result.rows).to.be.an('array');
    });

    it('should handle filter: pending', async function () {
      const req: any = { query: { filter: 'pending' } };
      await getUsers({ req });
      expect(findAndCountAllStub.called).to.be.true;
    });

    it('should handle filter: deactivated', async function () {
      const req: any = { query: { filter: 'deactivated' } };
      await getUsers({ req });
      expect(findAndCountAllStub.called).to.be.true;
    });

    it('should handle filter: deleted', async function () {
      const req: any = { query: { filter: 'deleted' } };
      await getUsers({ req });
      expect(findAndCountAllStub.called).to.be.true;
    });

    it('should handle filter: under_review', async function () {
      const req: any = { query: { filter: 'under_review' } };
      await getUsers({ req });
      expect(findAndCountAllStub.called).to.be.true;
    });

    it('should handle search query', async function () {
      findAndCountAllStub.resolves({ count: 1, rows: [{}] });
      const req: any = { user: { id: 1 }, query: { search: 'foo' } };
      const result = await getUsers({ req });
      expect(result.count).to.equal(1);
    });
  });

  describe('getUserById', function () {
    it('should return user if found', async function () {
      findByPkStub.resolves({});
      const result = await getUserById('id');
      expect(result).to.exist;
    });
    it('should throw NotFoundError if not found', async function () {
      findByPkStub.resolves(null);
      try {
        await getUserById('id');
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
  });

  describe('getUserWithCalendarInfo', function () {
    it('should return user with calendar info if found', async function () {
      findOneStub.resolves({});
      const result = await getUserWithCalendarInfo('id');
      expect(result).to.exist;
    });
    it('should throw NotFoundError if not found', async function () {
      findOneStub.resolves(null);
      try {
        await getUserWithCalendarInfo('id');
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
  });

  describe('activateUser', function () {
    it('should activate user if found', async function () {
      const update = sandbox.stub().resolves();
      findByPkStub.resolves({ update });
      await activateUser('id');
      expect(update.calledOnce).to.be.true;
    });
    it('should throw NotFoundError if not found', async function () {
      findByPkStub.resolves(null);
      try {
        await activateUser('id');
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
  });

  describe('deactivateUser', function () {
    it('should deactivate user and send email if therapist', async function () {
      const update = sandbox.stub().resolves();
      findByPkStub.resolves({ id: 'id', email: '<EMAIL>', role: 'therapist', update, toJSON: () => ({}) });
      findReasonStub.resolves(null);
      createReasonStub.resolves();
      const req: any = { query: { reasonId: '1', reason: 'test' } };
      await deactivateUser('id', req);
      expect(update.calledOnce).to.be.true;
      expect(sendMailStub.calledOnce).to.be.true;
    });
    it('should throw NotFoundError if user not found', async function () {
      findByPkStub.resolves(null);
      const req: any = { query: { reasonId: '1', reason: 'test' } };
      try {
        await deactivateUser('id', req);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
    it('should throw NotFoundError if reasonId not provided', async function () {
      findByPkStub.resolves({});
      const req: any = { query: {} };
      try {
        await deactivateUser('id', req);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
    it('should throw error if mail sending fails', async function () {
      const update = sandbox.stub().resolves();
      findByPkStub.resolves({ id: 'id', email: '<EMAIL>', role: 'therapist', update, toJSON: () => ({}) });
      findReasonStub.resolves(null);
      createReasonStub.resolves();
      sendMailStub.rejects(new Error('fail'));
      const req: any = { query: { reasonId: '1', reason: 'test' } };
      try {
        await deactivateUser('id', req);
        throw new Error('Should have thrown');
      } catch (err) {
        expect((err as any).message).to.equal('Failed to send email');
      }
    });
    it('should handle non-therapist user', async function () {
      const update = sandbox.stub().resolves();
      findByPkStub.resolves({ id: 'id', email: '<EMAIL>', role: 'user', update, toJSON: () => ({}) });
      findReasonStub.resolves(null);
      createReasonStub.resolves();
      const req: any = { query: { reasonId: '1', reason: 'test' } };
      await deactivateUser('id', req);
      expect(update.calledOnce).to.be.true;
      expect(sendMailStub.calledOnce).to.be.true;
    });
  });

  describe('deleteUser', function () {
    it('should throw NotFoundError if user not found', async function () {
      findByPkStub.resolves(null);
      try {
        await deleteUser('id', 'date', 'reasonId', 'reason');
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
    it('should throw NotFoundError if reasonId not provided', async function () {
      findByPkStub.resolves({ dataValues: { id: 'id', role: 'therapist' } });
      try {
        await deleteUser('id', 'date', undefined as any, 'reason');
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
    it('should delete therapist user successfully (success path)', async function () {
      // therapist branch, all sub-calls succeed
      const user = {
        dataValues: { id: 'id', role: 'therapist' },
        id: 'id',
        role: 'therapist',
        email: '<EMAIL>',
        destroy: sandbox.stub().resolves(),
        update: sandbox.stub().resolves(),
        toJSON: () => ({})
      };
      findByPkStub.resolves(user);
      findOneStub.resolves({}); // userWithInfo
      // Use top-level stubs for UserDeleteDeactivateReason.create and findOne
      createReasonStub.resetHistory();
      findReasonStub.resetBehavior();
      findReasonStub.resolves(null);
      sandbox.stub(AdminDeleteEmail, 'compile').returns({ From: '<EMAIL>', To: '<EMAIL>', Subject: 'sub', HtmlBody: 'body' });
      fakeTransaction.commit.resolves();
      fakeTransaction.rollback.resolves();
      await deleteUser('id', 'date', 'reasonId', 'reason');
      expect(fakeTransaction.commit.called).to.be.true;
    });
    it('should delete non-therapist user successfully (success path)', async function () {
      // non-therapist branch
      const user = {
        dataValues: { id: 'id', role: 'user' },
        id: 'id',
        role: 'user',
        email: '<EMAIL>',
        destroy: sandbox.stub().resolves(),
        update: sandbox.stub().resolves(),
        toJSON: () => ({})
      };
      findByPkStub.resolves(user);
      findOneStub.resolves(null); // userWithInfo
      // Use top-level stubs for UserDeleteDeactivateReason.create and findOne
      createReasonStub.resetHistory();
      findReasonStub.resetBehavior();
      findReasonStub.resolves(null);
      fakeTransaction.commit.resolves();
      fakeTransaction.rollback.resolves();
      await deleteUser('id', 'date', 'reasonId', 'reason');
      expect(fakeTransaction.commit.called).to.be.true;
    });
    it('should rollback transaction and throw if error occurs', async function () {
      // Simulate error in transaction
      const user = {
        dataValues: { id: 'id', role: 'therapist' },
        id: 'id',
        role: 'therapist',
        email: '<EMAIL>',
        destroy: sandbox.stub().resolves(),
        update: sandbox.stub().resolves(),
        toJSON: () => ({})
      };
      findByPkStub.resolves(user);
      findOneStub.throws(new Error('fail in findOne'));
      fakeTransaction.commit.resolves();
      fakeTransaction.rollback.resolves();
      try {
        await deleteUser('id', 'date', 'reasonId', 'reason');
        throw new Error('Should have thrown');
      } catch (err) {
        expect(fakeTransaction.rollback.called).to.be.true;
      }
    });
  });
});