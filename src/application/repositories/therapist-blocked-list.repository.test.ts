import sinon from 'sinon';
import { User, TherapistBlockedList } from '@/src/models';
import { expect } from 'chai';
import { blockPatient, getBlockedPatients, unblockPatient } from './therapist-blocked-list.repository';

describe('Therapist Blocked List Repository Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let thBlockedListFindOrCreateStub: sinon.SinonStub;
	let thBlockedListDestroyStub: sinon.SinonStub;
	let thBlockedListFindAndCountAllStub: sinon.SinonStub;

	let dummyUser= {
		id: 1,
		email: '<EMAIL>',
		role: 'therapist',
		firstname: '<PERSON>',
		lastname: '<PERSON><PERSON>',
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		thBlockedListFindOrCreateStub = sandbox.stub(TherapistBlockedList, 'findOrCreate');
		thBlockedListDestroyStub = sandbox.stub(TherapistBlockedList, 'destroy');
		thBlockedListFindAndCountAllStub = sandbox.stub(TherapistBlockedList, 'findAndCountAll');
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('blockPatient', function() {
		it('should throw error if patient is not found', async function () {
			userFindByPkStub.resolves(null);

			const req = {
				user: {
					id: 1,
				},
				params: {
					id: 2,
				}
			} as any;

			try {
				await blockPatient(req);
				throw new Error('Test should have thrown error but did not');
			} catch (error: any) {
				expect(error).to.be.instanceOf(Error);
				expect(error.message).to.equal('Patient not found');
				expect(error.statusCode).to.equal(404);
			};
		});

		it('should throw error if patient is already blocked', async function () {
			userFindByPkStub.resolves(dummyUser);
			thBlockedListFindOrCreateStub.resolves([null, false]);

			const req = {
				user: {
					id: 1,
				},
				params: {
					id: 2,
				}
			} as any;

			try {
				await blockPatient(req);
				throw new Error('Test should have thrown error but did not');
			} catch (error: any) {
				expect(error).to.be.instanceOf(Error);
				expect(error.message).to.equal('Patient is already blocked');
			};
		});
	});

	describe('unblockPatient', function() {
		it('should throw error if patient could not be unblocked', async function () {
			thBlockedListDestroyStub.resolves(0);

			const req = {
				user: {
					id: 1,
				},
				params: {
					id: 2,
				}
			} as any;

			try {
				await unblockPatient(req);
				throw new Error('Test should have thrown error but did not');
			} catch (error: any) {
				expect(error).to.be.instanceOf(Error);
				expect(error.message).to.equal('The patient was not found in the blocked list');
			};
		});
	});

	describe('getBlockedPatients', function() {
		it('should return paginated blocked patients list successfully', async function () {
			thBlockedListFindAndCountAllStub.resolves({
				count: 1,
				rows: [dummyUser]
			});

			const req = {
				user: {
					id: 1,
				},
				query: {
					page: 1,
					perPage: 20,
				}
			} as any;

			const result = await getBlockedPatients(req);

			expect(result).to.have.property('count').to.be.equal(1);
			expect(result).to.have.property('rows').to.be.an('array').of.length(1);
		});
	});
});