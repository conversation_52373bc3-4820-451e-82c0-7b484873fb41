import { expect } from 'chai';
import * as sinon from 'sinon';
import * as paginationHelper from '../helpers/pagination.helper';


import {
  getappointments,
  createAppointments,
  getEventById,
  updateEvent,
  deleteEvent,
  getMonthlyEvents,
  getAppointmentsByMonthYear,
} from '../repositories/appointments.repository';

import { Appointments, User, MinorPatient } from '@/src/models';
import { NotFoundError } from '@/src/application/handlers/errors';
import logger from '@/src/configs/logger.config';
import Sequelize from 'sequelize';

describe('Appointment Repository', () => {
  let sandbox: sinon.SinonSandbox;

  const mockRequest = (query = {}, body = {}, params = {}, user = {}) =>
    ({ query, body, params, user } as any);

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sandbox.stub(paginationHelper, 'paginated').returns({ offset: 0, limit: 10 });
    sandbox.stub(logger, 'info');
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getappointments', () => {
    it('should return paginated appointments', async () => {
      const req = mockRequest();
      const stub = sandbox.stub(Appointments, 'findAndCountAll').resolves({ rows: [], count: [] });

      const result = await getappointments({ req, perPage: 10 });

      expect(stub.calledOnce).to.be.true;
      expect(result).to.deep.equal({ rows: [], count: [] });
    });
  });

  describe('createAppointments', () => {
    it('should throw error if required fields are missing', async () => {
      const req = mockRequest({}, {});

      try {
        await createAppointments(req);
      } catch (err) {
        expect((err as Error).message).to.equal('Therapist ID, Patient ID, appointmentDate, and appointmentTime are required.');
      }
    });

    it('should create appointment and return it', async () => {
      const req = mockRequest({}, {
        description: 'desc',
        appointmentDate: '2025-06-25',
        appointmentTime: '10:00',
        therapistId: 1,
        patientId: 2,
        name: 'test',
      });

      sandbox.stub(Appointments, 'findOne').resolves(null as any);
      const createStub = sandbox.stub(Appointments, 'create').resolves({ id: 123 } as any);

      const result = await createAppointments(req);

      expect(createStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({ id: 123 });
    });
  });

  describe('getEventById', () => {
    it('should return event when found', async () => {
      const stub = sandbox.stub(Appointments, 'findByPk').resolves({ id: 1 } as any);

      const result = await getEventById(1);
      expect(stub.calledWith(1)).to.be.true;
      expect(result).to.deep.equal({ id: 1 });
    });

    it('should throw NotFoundError if not found', async () => {
      sandbox.stub(Appointments, 'findByPk').resolves(null);

      try {
        await getEventById(999);
      } catch (err) {
        expect(err).to.be.instanceOf(NotFoundError);
        expect((err as Error).message).to.equal('Event not found');
      }
    });
  });

  describe('updateEvent', () => {
    it('should update event', async () => {
      const updateStub = sandbox.stub().resolves({ updated: true });
      sandbox.stub(Appointments, 'findByPk').resolves({ update: updateStub } as any);

      const req = mockRequest({}, {
        name: 'n',
        description: 'd',
        startDate: 's',
        endDate: 'e',
        tagId: 1,
        isAllDay: true,
        isRecurring: false,
        location: 'loc',
        attendees: [],
      }, { id: 1 });

      const result = await updateEvent(req);
      expect(updateStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({ updated: true });
    });
  });

  describe('deleteEvent', () => {
    it('should delete the event', async () => {
      const destroyStub = sandbox.stub().resolves(true);
      sandbox.stub(Appointments, 'findByPk').resolves({ destroy: destroyStub } as any);

      const result = await deleteEvent(1);
      expect(destroyStub.calledOnce).to.be.true;
      expect(result).to.be.true;
    });
  });

  describe('getMonthlyEvents', () => {
    it('should return events filtered by month/year', async () => {
      const req = mockRequest();
      const findStub = sandbox.stub(Appointments, 'findAndCountAll').resolves({ rows: [], count: [] });

      const result = await getMonthlyEvents({ req, month: 6, year: 2025, perPage: 10 });

      expect(findStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({ rows: [], count: [] });
    });
  });

  describe('getAppointmentsByMonthYear', () => {
   it('should return appointments for user in date range', async () => {
		const req = mockRequest({}, {}, {}, { id: 5 });
		const stub = sandbox.stub(Appointments, 'findAndCountAll').resolves({ rows: [], count: [] });

		const result = await getAppointmentsByMonthYear({ req, month: 6, year: 2025, perPage: 10 });

		expect(stub.calledOnce).to.be.true;
		expect(result).to.deep.equal({ rows: [], count: [] });

		// Safe way to verify patientId was used in the query
		expect(stub.calledWithMatch(sinon.match.hasNested('where.patientId', 5))).to.be.true;
	});


  });
});
