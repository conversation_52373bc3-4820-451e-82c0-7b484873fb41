import { Answer, AnswerFollowThrough, PageAnswer } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Op,
	Order,
	WhereOptions,
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { NotFoundError } from '../handlers/errors'
import sequelize from '@/src/configs/database.config'
import { generateSlug } from '../helpers/general.helper'

type AnswerFilter = {
	req: Request
	perPage?: number
}

export const getAnswers = async ({ req, perPage }: AnswerFilter) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = []
	let relations: any = []
	const attributes: FindAttributeOptions = []

	const { search, pageId, type } = req.query
	const isFollowThrough = req.query.followThrough  === 'true';
	
	if (search) {
		where = {
			...where,
			answer: {
				[Op.iLike]: `${search}%`,
			},
		}
	}

	if (pageId) {
		relations.push({
			association: 'pages',
			where: {
				id: {
					[Op.in]: pageId.toString().split(','),
				},
			},
		})
	}

	order.push(['answer', 'ASC'])

	if(isFollowThrough) {

		const pageAnswers = await PageAnswer.findAll({
			attributes: ['pageId', 'answerId'],
			where: {
				pageId: pageId,
			}
		})


		return await AnswerFollowThrough.findAndCountAll({
			attributes: ['followingBy', 'followingTo'],
			where: {
				followingBy: {
					[Op.in] : pageAnswers.map((pa) => pa.answerId)
				},
			},
			include: [
				{
					model: Answer,
					as: 'followingToAnswer'
				},
				{
					model: Answer,
					as: 'followingByAnswer'
				}
			]
		})

	}


	return Answer.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req, perPage),
	})
}

export const createAnswer = async (req: Request) => {
	const { answer, info } = req.body
	const slug = generateSlug(answer);
	
	const event = await Answer.create({ answer, info, slug })
	return event
}

export const getAnswerById = async (id: number | string) => {
	const event = await Answer.findByPk(id)
	if (!event) throw new NotFoundError('Answer not found')
	return event
}

export const updateAnswer = async (req: Request) => {
	const ans = await getAnswerById(req.params.id)
	const { answer, info } = req.body
	const slug= generateSlug(answer)

	return ans.update({
		answer,
		info,
		slug
	})
}

export const updateAllSlug = async () => {
	const answers = await Answer.findAll();

	await Promise.all(
		answers.map(async (answer) => {
			const slug = generateSlug(answer.answer);
			await answer.update({ slug });
		})
	);
}

export const deleteAnswer = async (id: number | string) => {
	const answer = await getAnswerById(id)
	return answer.destroy()
}
