import sinon from 'sinon';
import { Speciality } from '@/src/models';
import { expect } from 'chai';
import { getSpecialities } from './speciality.repository';

describe('Speciality Repository Test', function () {
	let sandbox: sinon.SinonSandbox;

	let specialityFindAndCountAllStub: sinon.SinonStub;

	let dummySpeciality= {
		id: 1,
		name: 'name',
		description: 'description',
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		specialityFindAndCountAllStub = sandbox.stub(Speciality, 'findAndCountAll');
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('getSpecialities', function() {
		it('should return paginated speciality list successfully', async function () {
			specialityFindAndCountAllStub.resolves({
				count: 1,
				rows: [dummySpeciality]
			});

			const req = {
				query: {
					page: 1,
					perPage: 20,
				}
			} as any;

			const result = await getSpecialities({ req });

			expect(result).to.have.property('count').to.be.equal(1);
			expect(result).to.have.property('rows').to.be.an('array').of.length(1);
		});
	});
});