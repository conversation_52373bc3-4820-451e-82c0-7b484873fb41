import { Event } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Order,
	WhereOptions,
	Op,fn,col
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { NotFoundError } from '../handlers/errors'
import sequelize from 'sequelize/lib/sequelize'

type EventFilter = {
	req: Request
	perPage?: number
}

type CategorizeFilter = {
	req: Request;
	month?: number;
	year?: number;
	perPage?: number;
};


export const getEvents = async ({ req, perPage }: EventFilter) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = []
	const relations: any = []
	const attributes: FindAttributeOptions = []

	order.push(['createdAt', 'DESC'])

	return Event.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req, perPage),
	})
}

export const createEvent = async (req: Request) => {
	const {
		name,
		description,
		startDate,
		endDate,
		tagId,
		isAllDay,
		isRecurring,
		location,
		attendees,
	} = req.body
	const event = await Event.create({
		name,
		description,
		startDate,
		endDate,
		tagId,
		isAllDay,
		isRecurring,
		location,
		attendees,
	})
	return event
}

export const getEventById = async (id: number | string) => {
	const event = await Event.findByPk(id)
	if (!event) throw new NotFoundError('Event not found')
	return event
}

export const updateEvent = async (req: Request) => {
	const event = await getEventById(req.params.id)
	const {
		name,
		description,
		startDate,
		endDate,
		tagId,
		isAllDay,
		isRecurring,
		location,
		attendees,
	} = req.body

	return event.update({
		name,
		description,
		startDate,
		endDate,
		tagId,
		isAllDay,
		isRecurring,
		location,
		attendees,
	})
}

export const deleteEvent = async (id: number | string) => {
	const event = await getEventById(id)
	return event.destroy()
}

export const getMonthlyEvents = async ({ req, month, year, perPage }: CategorizeFilter) => {
	const where = {
		[Op.and]: [
			// Filter by month and year if provided
			month && sequelize.where(
				sequelize.fn('EXTRACT', 'MONTH', sequelize.col('startDate')),
				month
			),
			year && sequelize.where(
				sequelize.fn('EXTRACT', 'YEAR', sequelize.col('startDate')),
				year
			),
		],
	};

	// Fetch events with pagination
	const events = await Event.findAndCountAll({
		where,
		order: [['startDate', 'ASC']], // Order by start date
		...paginated(req, perPage),
	});

	return events;
};