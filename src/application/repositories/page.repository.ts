import { Op, Order, WhereOptions } from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { AnswerFollowThrough, Page, PageAnswer, Answer } from '@/src/models'
import sequelize from '@/src/configs/database.config'
import { BadRequestError } from '../handlers/errors'

export const getAllPages = async (req: Request) => {
	const order: Order = [['sortOrder', 'ASC']]
	let where: WhereOptions = {}

	const { exclude, category, type, search } = req.query
	if (exclude) {
		where.id = {
			[Op.notIn]: exclude,
		}
	}

	if (category) {
		where.category = category
	}

	if (type) {
		where.type = type
	}
	if(search) {
		where = {
			...where,
			[Op.or]: {
				title: {
					[Op.iLike] : `%${search}%`
				},
				code: {
					[Op.iLike] : `%${search}%`
				}
			}
			
		}
	}

	return Page.findAndCountAll({
		where,
		order,
		...paginated(req),
	})
}

export const getPageById = async (id: string) => {
	const page = await Page.findByPk(id, {
		include: [
			{
				association: 'answers',
				attributes: [
					'id',
					'info',
					'answer',
					[
						sequelize.literal(`(
							SELECT "conditions" from "page_answer" WHERE "page_answer"."answerId" = "answers"."id" AND "page_answer"."pageId" = '${id}' LIMIT 1
					)`),
						'conditions',
					],
					[
						sequelize.literal(`(
							SELECT "answerGroup" from "page_answer" WHERE "page_answer"."answerId" = "answers"."id" AND "page_answer"."pageId" = '${id}' LIMIT 1
					)`),
						'answerGroup',
					],
					[
						sequelize.literal(`(
							SELECT "id" from "page_answer" WHERE "page_answer"."answerId" = "answers"."id" AND "page_answer"."pageId" = '${id}' LIMIT 1
					)`),
						'pageAnswerId',
					],
					[
						sequelize.literal(`(
							SELECT "position" from "page_answer" WHERE "page_answer"."answerId" = "answers"."id" AND "page_answer"."pageId" = '${id}' LIMIT 1
					)`),
						'position',
					],
					[
						sequelize.literal(`(
							SELECT "ansOrder" from "page_answer" WHERE "page_answer"."answerId" = "answers"."id" AND "page_answer"."pageId" = '${id}' LIMIT 1
					)`),
						'order',
					],
					[
						sequelize.literal(`(
							SELECT JSON_BUILD_OBJECT('id',pages.id,'title', pages.title) FROM pages WHERE pages.id IN (SELECT "copiedFromPage" FROM "page_answer" WHERE "page_answer"."answerId" = "answers"."id" AND "page_answer"."pageId" = '${id}' LIMIT 1) LIMIT 1
					)`),
						'page',
					],
				],
				through: {
					attributes: [],
				},
				// order: [[Page.associations.answers.through, 'order', 'ASC']]
			},
			'questionnaire',
			'nextPage',
			'skipPage'
		],
		order: [
			[sequelize.literal(`
				CASE 
					WHEN "position" = 'left' THEN 1
					WHEN "position" = 'right' THEN 2
					ELSE 3
				END
			`), 'ASC'],
			[sequelize.literal(`"ansOrder"`), 'ASC'],
		]
	})
	if (!page) throw new Error('Page not found')

	if (page.isFollowedThrough) {
		const pageAnswers = await PageAnswer.findAll({
			where: {
				pageId: id,
			},
			attributes: ['answerId', 'followedByAns'],
		});

		const followThroughAnswers = await AnswerFollowThrough.findAndCountAll({
			attributes: ['followingBy', 'followingTo'],
			where: {
				followingTo: {
					[Op.in]: pageAnswers.map((pa) => pa.answerId),
				},
				followingBy: {
					[Op.in]: pageAnswers.map((pa) => pa.followedByAns),
				},
			},
			include: [
				{
					model: Answer,
					as: 'followingToAnswer'
				},
				{
					model: Answer,
					as: 'followingByAnswer'
				}
			]
		});
		page.dataValues.followThroughAnswers = followThroughAnswers;
	}
	// let answers = page.answers.map((ans)=>{
	// 	return {...ans.dataValues, order: ans.dataValues.PageAnswer.ansOrder};
	// });

	// page.setDataValue('answers', answers);
	
	return page;
}

export const createPage = async (req: Request) => {
	const {
		button,
		category,
		code,
		extra,
		info,
		title,
		type,
		questionnaireType,
		answers,
		questionnaireId,
		buttonClickAction,
		required,
		skipToPageId,
	} = req.body
	
	const buttonAction = ['register', 'end'].includes(buttonClickAction)
		? buttonClickAction
		: 'next-page'

	const oldPage = await Page.findOne({
		where: {
			category,
			code,
		},
	})

	if (oldPage) {
		throw new BadRequestError(
			'Page with same category and code already exists'
		)
	}

	const orderAsPer = await Page.findOne({
		where: {
			category,
		},
		order: [['sortOrder', 'DESC']]
	})

	const isFollowedThrough = !!answers.find((ans:any) => ans.followedBy)

	const page = await Page.create({
		button,
		category,
		code,
		extra,
		info,
		title,
		skipToPageId,
		type,
		sortOrder: orderAsPer && orderAsPer.sortOrder ? orderAsPer.sortOrder + 1 : 0,
		questionnaireId,
		questionnaireType,
		buttonAction,
		nextPageId: buttonAction === 'next-page' ? buttonClickAction : null,
		required: required || false,
		isFollowedThrough
	})

	if (questionnaireId) {
		await PageAnswer.bulkCreate(
			answers.map((answer: any) => ({
				answerId: answer.id,
				pageId: page.id,
				conditions: answer.conditions,
				answerGroup: answer.answerGroup,
				position: answer.position,
				copiedFromPage: answer.copiedFromPage || null,
				followedByAns: answer.followedBy || null,
				ansOrder: answer.order || null
			}))
		)
	}
	return page
}

export const updatePage = async (req: Request) => {
	const {
		button,
		category,
		code,
		extra,
		info,
		title,
		type,
		questionnaireType,
		answers,
		questionnaireId,
		removedAnswers,
		buttonClickAction,
		sortOrder,
		required,
		skipToPageId,
	} = req.body

	const buttonAction = ['register', 'end'].includes(buttonClickAction)
		? buttonClickAction
		: 'next-page'
	const page = await getPageById(req.params.id)
	const isFollowedThrough = !!answers.find((ans:any) => ans.followedBy)
	await page.update({
		button,
		category,
		code,
		extra,
		info,
		title,
		type,
		questionnaireType,
		questionnaireId,
		buttonAction,
		nextPageId: buttonAction === 'next-page' ? buttonClickAction : null,
		skipToPageId,
		sortOrder,
		required,
		isFollowedThrough
	})

	if (removedAnswers.length) {
		await PageAnswer.destroy({
			where: {
				pageId: page.id,
				answerId: {
					[Op.in]: removedAnswers,
				},
			},
		})
	}


	if(isFollowedThrough === false) {
		if (type === 'value' || type === 'selectable') {
			for (const answer of answers) {
				if (answer.pageAnswerId) {
					// Update existing PageAnswer
					await PageAnswer.update(
						{
							answerId: answer.id,
							conditions: answer.conditions,
						},
						{
							where: {
								id: answer.pageAnswerId,
								pageId: page.id,
							},
						}
					);
				} else {
					// Create new PageAnswer
					await PageAnswer.create({
						answerId: answer.id,
						pageId: page.id,
						answerGroup: answer.answerGroup,
						position: answer.position,
						copiedFromPage: answer.copiedFromPage || null,
						followedByAns: answer.followedBy || null,
						conditions: answer.conditions,
						ansOrder: answer.order || null
					});
				}
			}
		} else {

			for (const answer of answers) {
				const [qa, created] = await PageAnswer.findOrCreate({
					where: {
						answerId: answer.id,
						pageId: page.id,
					},
					defaults: {
						answerId: answer.id,
						pageId: page.id,
						answerGroup: answer.answerGroup,
						conditions: answer.conditions,
						copiedFromPage: answer.copiedFromPage || null,
						followedByAns: answer.followedBy || null,
						ansOrder: answer.order || null
					},
				})
				if (!created) {
					await qa.update({
						conditions: answer.conditions,
						ansOrder: answer.order || null
					})
				}
			}
		}
	} else {
		for (const answer of answers) {
			const [qa, created] = await PageAnswer.findOrCreate({
				where: {
					answerId: answer.id,
					pageId: page.id,
					followedByAns: answer.followedBy,
				},
				defaults: {
					answerId: answer.id,
					pageId: page.id,
					answerGroup: answer.answerGroup,
					conditions: answer.conditions,
					copiedFromPage: answer.copiedFromPage || null,
					followedByAns: answer.followedBy || null,
					ansOrder: answer.order || null
				},
			})
			if (!created) {
				await qa.update({
					conditions: answer.conditions,
				})
			}
		}
	}
	

	return page
}

export const deletePage = async (req: Request) => {
	const page = await getPageById(req.params.id)
	return page.destroy()
}

export const startPage = async (req: Request) => {
	const page = await getPageById(req.params.id)

	await Promise.all([
		Page.update(
			{
				initialPage: false,
			},
			{
				where: {
					category: page.category,
				},
			}
		),
		page.update({
			initialPage: true,
		}),
	])

	return page
}

export const matcherInvolvement = async (req: Request) => {
	const page = await getPageById(req.params.id)

	await page.update({
    matcherInvolvement: !page.matcherInvolvement,
  });

	return page
}

