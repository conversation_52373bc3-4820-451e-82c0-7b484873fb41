import { User, TherapistWaitlist} from '@/src/models'
import { Op } from 'sequelize';
import { NotFoundError } from '../handlers/errors'

/**
 * @desc Remove all patients from a therapist's waitlist when the therapist is unavailable
 * <AUTHOR>
 * @since 2024-08-22
 * @param {string} therapistId - ID of the therapist being deactivated/deleted
 * @returns {Promise<number>} Number of waitlist entries deleted
 * @throws {NotFoundError} If the therapist doesn't exist
 */

export const removePatientsFromTherapistWaitlist = async (therapistId: string): Promise<number> => {
	const user = await User.findByPk(therapistId);
	if (!user) throw new NotFoundError('Therapist not found');

	const deletedCount = await TherapistWaitlist.destroy({
		where: { therapistId },
	});

	return deletedCount;
};