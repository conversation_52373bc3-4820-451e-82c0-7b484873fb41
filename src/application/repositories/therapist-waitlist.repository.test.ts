import sinon from 'sinon';
import { User, TherapistWaitlist } from '@/src/models';
import { expect } from 'chai';
import { removePatientsFromTherapistWaitlist } from './therapist-waitlist.repository';
import { NotFoundError } from '../handlers/errors';

describe('Therapist Waitlist Repository Test', function () {
  let sandbox: sinon.SinonSandbox;
  let findByPkStub: sinon.SinonStub;
  let destroyStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    findByPkStub = sandbox.stub(User, 'findByPk');
    destroyStub = sandbox.stub(TherapistWaitlist, 'destroy');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should throw NotFoundError if therapist does not exist', async function () {
    findByPkStub.resolves(null);
    try {
      await removePatientsFromTherapistWaitlist('fake-id');
      throw new Error('Should have thrown');
    } catch (err) {
      expect(err).to.be.instanceOf(NotFoundError);
      expect(destroyStub.notCalled).to.be.true;
    }
  });

  it('should delete waitlist entries and return count if therapist exists', async function () {
    findByPkStub.resolves({ id: 'therapist-id' });
    destroyStub.resolves(3);
    const result = await removePatientsFromTherapistWaitlist('therapist-id');
    expect(destroyStub.calledOnce).to.be.true;
    expect(destroyStub.firstCall.args[0]).to.deep.equal({ where: { therapistId: 'therapist-id' } });
    expect(result).to.equal(3);
  });
}); 