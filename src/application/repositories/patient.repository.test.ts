import sinon from 'sinon';
import { User } from '@/src/models';
import { expect } from 'chai';
import { getPatients } from './patient.repository';

describe('Patient Repository Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindAndCountAllStub: sinon.SinonStub;

	let dummyUser= {
		id: 1,
		email: '<EMAIL>',
		role: 'therapist',
		firstname: '<PERSON>',
		lastname: '<PERSON><PERSON>',
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindAndCountAllStub = sandbox.stub(User, 'findAndCountAll');
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('getPatients', function() {
		it('should return paginated patients list successfully', async function () {
			userFindAndCountAllStub.resolves({
				count: 1,
				rows: [dummyUser]
			});

			const req = {
				user: {
					id: 1,
				},
				role: 'therapist',
				query: {
					search: 'john',
				}
			} as any;

			const result = await getPatients({
				req,
				perPage: 20,
			});

			expect(result).to.have.property('count').to.be.equal(1);
			expect(result).to.have.property('rows').to.be.an('array').of.length(1);
		});
	});
});