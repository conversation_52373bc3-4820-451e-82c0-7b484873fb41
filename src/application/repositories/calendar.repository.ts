import { Calendar } from '@/src/models'
import { getGoogleCalendarEvents, getCalendarEventsForDate, createGoogleCalendarEvent, deleteGoogleCalendarEvent } from '@/src/configs/google.config'
import { BadRequestError, ForbiddenError, InternalServerError } from '../handlers/errors'
import axios from 'axios'
import { createOutlookCalendarEvent, deleteOutlookCalendarEvent, getAuthenticatedClient, getOutlookCalendarEvents, getOutlookCalendarEventsForDate, getProfile } from '@/src/configs/outlook.config'
import logger from '@/src/configs/logger.config'
import { CalendarEventsProps } from '../helpers/types.helper'

/**
 * 
 * @param tokens, eventData
 * @returns created event
 */
export const createGoogleEvent = async ({
	tokens, eventData
}: CalendarEventsProps) => {
	if (!tokens) throw new ForbiddenError('Google tokens are required')
	if (!eventData) throw new ForbiddenError('Event data is required');

	let event;

	try {
		event = await createGoogleCalendarEvent(tokens, eventData);
	} catch (error: any) {
		logger.error('Error creating Google Calendar event', error);
		// throw new InternalServerError('Something went wrong. Please try again');
	}

	return event;
};

/**
 * 
 * @param tokens, forDate, startDate, endDate
 * @returns google events
 */
export const getGoogleEvents = async ({
	tokens, forDate = false, startDate, endDate
}: CalendarEventsProps) => {
	if (!tokens) throw new ForbiddenError('Google tokens are required')
	if (forDate && (!startDate || !endDate)) throw new BadRequestError('Start and end date is required');

	let events;

	try {
		events = forDate ? await getCalendarEventsForDate(tokens, startDate!, endDate!) : await getGoogleCalendarEvents(tokens);
	} catch (error: any) {
		logger.error('Error fetching google calendar events', error);
		// throw new InternalServerError('Something went wrong. Please try again');
	}

	return events;
};

/**
 * 
 * @param tokens, userId, eventData
 * @returns created event
 */
export const createOutlookEvent = async ({
	tokens, userId, eventData
}: CalendarEventsProps) => {
	if (!tokens) throw new ForbiddenError('Outlook tokens are required')
	if (!userId) throw new ForbiddenError('User Id is required')
	if (!eventData) throw new ForbiddenError('Event data is required');

	let event;
	let graphClient;
	try {
		if (tokens.access_token_expiry < Math.floor(Date.now() / 1000)) graphClient = await refreshMicrosoftAccessToken(tokens.refresh_token, userId);
		else graphClient = await initializeMicrosoftClient(tokens.access_token);

		if (graphClient) {
			event = await createOutlookCalendarEvent(graphClient, eventData);
		} else event = null;
	} catch (error: any) {
		logger.error('Error Creating Outlook calendar event:', error);
		// throw new InternalServerError('Something went wrong. Please try again');
	}

	return event;
};

/**
 * 
 * @param tokens, userId, forDate, startDate, endDate
 * @returns outlook events
 */
export const getOutlookEvents = async ({
	tokens, userId, forDate = false, startDate, endDate
}: CalendarEventsProps) => {
	if (!tokens) throw new ForbiddenError('Outlook tokens are required')
	if (!userId) throw new ForbiddenError('User Id is required')
	if (forDate && (!startDate || !endDate)) throw new BadRequestError('Start and end date is required');

	let events;
	let graphClient;
	try {
		if (tokens.access_token_expiry < Math.floor(Date.now() / 1000)) graphClient = await refreshMicrosoftAccessToken(tokens.refresh_token, userId);
		else graphClient = await initializeMicrosoftClient(tokens.access_token);

		if (graphClient) {
			events = forDate ? await getOutlookCalendarEventsForDate(graphClient, startDate!, endDate!) : await getOutlookCalendarEvents(graphClient);
		} else events = null;
	} catch (error: any) {
		logger.error('Error fetching Outlook calendar events:', error);
		// throw new InternalServerError('Something went wrong. Please try again');
	}

	return events;
};

/**
 * 
 * @param accessToken 
 * @param refreshToken 
 * @param userId 
 * @returns microsoft graph client
 */
export const initializeMicrosoftClient = async (accessToken: string) => {
	try {
		const client = await getAuthenticatedClient(accessToken);
	    return client
	} catch (error: any) {
		logger.error('Error initializing Microsoft client:', error);
		return null;
		// throw new InternalServerError(error?.response?.data?.message || error?.message);
	}
}

/**
 * 
 * @param refreshToken 
 * @param userId 
 * @returns microsoft graph client
 */
export const refreshMicrosoftAccessToken = async (refreshToken: string, userId: number) => {
	const tokenUrl = 'https://login.microsoftonline.com/common/oauth2/v2.0/token';
	const clientId = process.env.AZURE_CLIENT_ID;
	const clientSecret = process.env.AZURE_CLIENT_SECRET;

	const data = {
		client_id: clientId,
		refresh_token: refreshToken,
		grant_type: 'refresh_token',
		client_secret: clientSecret,
	};

	try {
		const response = await axios.post(tokenUrl, data, {
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		});
		if (response.data) {
			const graphClient = await getAuthenticatedClient(response.data.access_token);
			const profile = await getProfile(graphClient);
			const credentials = {
				tokens: {
					...response.data,
					access_token_expiry: Math.floor(Date.now() / 1000) + response.data.expires_in,
				},
				profile
			}
			await Calendar.update({
				credentials
			}, {
				where: {
					type: 'outlook',
					userId,
				}
			});
			return graphClient;
		}
	} catch (error: any) {
		logger.error('Error refreshing microsoft token:', error);
		return null;
		// throw new InternalServerError(error?.response?.data?.message || error?.message);
	}
}

/**
 * 
 * @param tokens
 * @param eventId
 */
export const deleteGoogleEvent = async ({ tokens, eventId }: CalendarEventsProps) => {
	if (!tokens) throw new ForbiddenError('Google tokens are required');
	if (!eventId) throw new ForbiddenError('Google Event Id is required');

	let success = false;
	try {
		success = await deleteGoogleCalendarEvent(tokens, eventId);
	} catch (error) {
		logger.error('Error deleting Google event:', error);
	}
	return success;
}

/**
 * 
 * @param tokens
 * @param eventId
 */
export const deleteOutlookEvent = async ({ tokens, eventId, userId }: CalendarEventsProps) => {
	if (!tokens) throw new ForbiddenError('Outlook tokens are required');
	if (!eventId) throw new ForbiddenError('Outlook Event Id is required');
	if (!userId) throw new ForbiddenError('User Id is required');

	let graphClient;
	let success = false;
	try {
		if (tokens.access_token_expiry < Math.floor(Date.now() / 1000)) graphClient = await refreshMicrosoftAccessToken(tokens.refresh_token, userId);
		else graphClient = await initializeMicrosoftClient(tokens.access_token);

		if (graphClient) {
			success = await deleteOutlookCalendarEvent(graphClient, eventId);
		} else {
			logger.error('graph client could not be initialized');
		}
	}	catch (error) {
		logger.error('Error deleting Outlook calendar event:', error);
	}

	return success;
}
