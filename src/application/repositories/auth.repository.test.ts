import { expect } from 'chai';
import * as sinon from 'sinon';
import { Request } from 'express';
import bcrypt from 'bcryptjs';
import dayjs from 'dayjs';
import { 
  loginWithoutMfa, 
  sendMfaCode, 
  verifyMFA, 
  resendMFA, 
  registerPatient,
  registerPatientMinor,
  forgotPassword,
  resetPassword,
  changePassword,
  emailResetToken,
  phoneResetToken,
  emailVerification,
  verifyPatientEmail,
  resetEmail,
  resetPhone
} from './auth.repository';
import { 
  User, 
  Calendar, 
  StripeInfo, 
  PatientProfile, 
  UserQuestionnaire, 
  UserValues, 
  UserSettings,
  MinorPatient,
  Page,
  UserRegistrationInfo
} from '@/src/models';
import FailedLoginAttempt from '@/src/models/failed-login-attempts.model';
import { 
  BadRequestError, 
  ConflictError, 
  NotFoundError, 
  UnprocessableEntityError,
  ForbiddenError,
  TooManyRequestsError
} from '@/src/application/handlers/errors';
import * as encryptionHelper from '@/src/application/helpers/encryption.helper';
import redisClient from '@/src/configs/redis.config';
import sequelize from '@/src/configs/database.config';
import { mail } from '@/src/configs/sendgrid.config';
import * as twilioConfig from '@/src/configs/twilio.config';
import { UserType } from '@/src/application/helpers/constant.helper';
import * as cryptoUtil from '@/src/cryptoUtil';
import * as userRepository from './user.repository';
import { PROFILE, PAYMENT_FORMS, WAITLIST_NOTIFICATION, PRACTICE_INFO, MODALITIES, PRACTICE_FOCUS, SPECIALIZATION, NORMAL_OFFICE_HOURS, LICENSE } from '../controllers/api/constants';
import logger from '@/src/configs/logger.config';
import { FailedLoginAlertEmail, RegisterPatientEmail } from '../emails/register.email';
import {
  DOB_VALIDATION,ALL_FIELDS_REQUIRED,INVALID_EMAIL_FORMAT,PASSWORD_VALIDATION,PASSWORDS_DO_NOT_MATCH,USER_UNDER_18,USER_ALREADY_EXISTS, INVALID_LINK, USER_NOT_FOUND, FAILED_LOGIN_ATTEMPTS, USER_NOT_ACTIVE, PATIENT_LOGIN_RESTRICTION_MESSAGE,EMAIL_NOT_VERIFIED_MESSAGE,INVALID_PASSWORD_MESSAGE,INVALID_CREDENTIALS_MESSAGE, MFA_CODE_SENT_MESSAGE, MFA_TOO_MANY_ATTEMPTS_MESSAGE, MFA_INVALID_CODE_MESSAGE, MFA_CODE_EXPIRED_MESSAGE, MFA_CODE_RESENT_MESSAGE, ACCOUNT_DELETED, ACCOUNT_DELETED_ADMIN
} from './constants';

describe('Auth Repository', function() {
  // Setup sandbox for mocking
  let sandbox: sinon.SinonSandbox;
  
  beforeEach(function() {
    // Create a sandbox for mocking
    sandbox = sinon.createSandbox();
    sandbox.stub(logger, 'error');
    sandbox.stub(logger, 'info');
    sandbox.stub(StripeInfo, 'findOne').resolves(null); // or a valid object if needed
    sandbox.stub(UserRegistrationInfo, 'findOne').callsFake(async (options?: any) => {
      const where = options?.where || {};
      const makeModel = (payloadInfo: any) => ({ payloadInfo, toJSON: () => ({ payloadInfo }) }) as any;
      switch (where.pageName) {
        case PROFILE:
          return makeModel({
            email: '<EMAIL>',
            dob: '1990-01-01',
            user_profile: 'profile',
            experience_years: 5,
            identify: { checked: 'male', other_identify: '' },
            race: { checked: 'asian', other_race: '' },
            language: { checked_list: ['english'], other_language: '' },
            educations: [{ college: 'Test College', degree_type: 'PhD', year_graduated: 2010 }],
            profile_statement: 'bio',
            social_media: 'social',
            video_links: 'video',
          });
        case PAYMENT_FORMS:
          return makeModel({ insurance: true, insurance_list: { checked_list: ['Aetna'], other_insurance: '' } });
        case WAITLIST_NOTIFICATION:
          return makeModel({ week_visibility: '2' });
        case PRACTICE_INFO:
          return makeModel({ practice: 'info' });
        case MODALITIES:
          return makeModel({ in_person: true });
        case PRACTICE_FOCUS:
          return makeModel({ religious_specialization: { checked_list: ['none'], other_religion: '' }, additional_focus: { checked_list: ['none'], other_focus: '' } });
        case SPECIALIZATION:
          return makeModel({ specialization: 'special' });
        case NORMAL_OFFICE_HOURS:
          return makeModel({ appointmentMethod: 'telehealth' });
        case LICENSE:
          return makeModel({ license: 'license' });
        default:
          return null;
      }
    });
  });
  
  afterEach(function() {
    // Restore all mocks
    sandbox.restore();
  });

  describe('loginWithoutMfa', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let userComparePasswordStub: sinon.SinonStub;
    let userGenerateTokenStub: sinon.SinonStub;
    let encryptedB64Stub: sinon.SinonStub;
    let redisGetStub: sinon.SinonStub;
    let minorPatientFindAllStub: sinon.SinonStub;
    let hashDataStub: sinon.SinonStub;
    
    beforeEach(function() {
      mockRequest = {
        body: {
          email: '<EMAIL>',
          password: 'Password123!'
        },
        headers: {
          'x-forwarded-for': '***********',
          'user-agent': 'Mozilla/5.0'
        },
        ip: '127.0.0.1'
      };
      
      userFindOneStub = sandbox.stub(User, 'findOne');
      userComparePasswordStub = sandbox.stub().returns(true);
      userGenerateTokenStub = sandbox.stub().returns('mock-token');
      encryptedB64Stub = sandbox.stub(encryptionHelper, 'encryptedB64').returns('encrypted-token');
      redisGetStub = sandbox.stub(redisClient, 'get').resolves(null);
      minorPatientFindAllStub = sandbox.stub(MinorPatient, 'findAll').resolves([]);
      hashDataStub = sandbox.stub(cryptoUtil, 'hashData').returns('hashed_email');
    });
    
    it('should return user info and tokens when login is successful', async function() {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        role: 'therapist',
        emailVerifiedAt: new Date(),
        isUnderReview: false,
        acceptedAt: new Date(),
        rejectedAt: null,
        deletedAt: null,
        active: true,
        comparePassword: userComparePasswordStub,
        generateToken: userGenerateTokenStub,
        update: sandbox.stub().resolves(),
        toJSON: sandbox.stub().returns({
          id: 1,
          email: '<EMAIL>',
          role: 'therapist'
        })
      };
      userFindOneStub.resolves(mockUser as any);
      
      const result = await loginWithoutMfa(mockRequest as Request);
      
      expect(result).to.have.property('accessToken', 'mock-token');
      expect(result).to.have.property('refreshToken', 'encrypted-token');
      expect(result).to.have.property('user');
    });
    
    it('should throw ForbiddenError when user is not found', async function() {
      userFindOneStub.resolves(null);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });
    
    it('should throw ForbiddenError when account is locked due to too many failed attempts', async function() {
      userFindOneStub.resolves({ id: 1 } as any);
      redisGetStub.resolves('5'); // 5 failed attempts
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });
    
    it('should throw ConflictError when password is incorrect', async function() {
      const mockUser = { id: 1, email: '<EMAIL>', role: 'therapist', comparePassword: sandbox.stub().returns(false) };
      userFindOneStub.resolves(mockUser as any);
      sandbox.stub(redisClient, 'incr').resolves(1);
      sandbox.stub(redisClient, 'expire').resolves(1);
      sandbox.stub(FailedLoginAttempt, 'create').resolves({} as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
      }
    });

    it('should trigger lockout email on max failed attempts', async function() {
      const mailStub = sandbox.stub(mail, 'sendMail').resolves();
      sandbox.stub(FailedLoginAlertEmail, 'compile').returns({ From: '<EMAIL>', To: '<EMAIL>', Subject: 'test', HtmlBody: 'test' });
      const mockUser = { id: 1, email: '<EMAIL>', role: 'therapist', comparePassword: sandbox.stub().returns(false), toJSON: () => ({ email: '<EMAIL>' }) };
      userFindOneStub.resolves(mockUser as any);
      sandbox.stub(redisClient, 'incr').resolves(5); // MAX_ATTEMPTS
      sandbox.stub(redisClient, 'expire').resolves(1);
      sandbox.stub(FailedLoginAttempt, 'create').resolves({} as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
        expect(mailStub.calledOnce).to.be.true;
      }
    });
    
    it('should throw ForbiddenError when therapist registration is incomplete', async function() {
      const mockUser = { id: 1, role: UserType.THERAPIST, isUnderReview: false, acceptedAt: null, rejectedAt: null, deletedAt: null, active: true, emailVerifiedAt: new Date(), comparePassword: userComparePasswordStub };
      userFindOneStub.resolves(mockUser as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });
    
    it('should throw ForbiddenError when email is not verified', async function() {
      const mockUser = { id: 1, role: UserType.THERAPIST, isUnderReview: false, acceptedAt: new Date(), rejectedAt: null, deletedAt: null, active: true, emailVerifiedAt: null, comparePassword: userComparePasswordStub };
      userFindOneStub.resolves(mockUser as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw NotFoundError if patient is not active', async function() {
      userFindOneStub.resolves({ id: 1, role: 'patient', version: 1, active: false, comparePassword: userComparePasswordStub } as any);
      try { await loginWithoutMfa(mockRequest as Request); expect.fail('Expected error'); } catch (err: any) {
        expect(err).to.be.instanceOf(NotFoundError);
      }
    });

    it('should update user if not active and login succeeds', async function() {
      const updateStub = sandbox.stub().resolves();
      const generateTokenStub = sandbox.stub().returns('mock-token');
      userFindOneStub.resolves({ id: 1, role: 'therapist', isUnderReview: false, acceptedAt: new Date(), rejectedAt: null, deletedAt: null, active: false, emailVerifiedAt: new Date(), comparePassword: userComparePasswordStub, update: updateStub, generateToken: generateTokenStub, toJSON: () => ({}) } as any);
      minorPatientFindAllStub.resolves([]);
      const result = await loginWithoutMfa(mockRequest as Request);
      expect(updateStub.calledOnce).to.be.true;
      expect(result).to.have.property('accessToken');
    });

    it('should throw Error when user not found during lockout email sending', async function() {
      userFindOneStub.onFirstCall().resolves({ id: 1, comparePassword: () => false }); // For the login attempt
      userFindOneStub.onSecondCall().resolves(null); // For the find within incrementFailedLogin
      sandbox.stub(redisClient, 'incr').resolves(5);
      sandbox.stub(redisClient, 'expire').resolves(1);
      sandbox.stub(FailedLoginAttempt, 'create').resolves({} as any);
  
      try {
          await loginWithoutMfa(mockRequest as Request);
          expect.fail('Should have thrown');
      } catch (err: any) {
          expect(err).to.be.instanceOf(Error);
          expect(err.message).to.equal(USER_NOT_FOUND);
      }
    });
    
    it('should throw ForbiddenError when therapist account is under review', async function() {
      const mockUser = { id: 1, role: UserType.THERAPIST, isUnderReview: true, comparePassword: userComparePasswordStub, emailVerifiedAt: new Date() };
      userFindOneStub.resolves(mockUser as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
        expect(error.message).to.equal('Account under review!');
      }
    });

    it('should throw ForbiddenError when therapist account was deleted by admin', async function() {
      const mockUser = { id: 1, role: UserType.THERAPIST, deletedAt: new Date(), deletedBy: 'admin', comparePassword: userComparePasswordStub, emailVerifiedAt: new Date() };
      userFindOneStub.resolves(mockUser as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
        expect(error.message).to.equal(ACCOUNT_DELETED_ADMIN);
      }
    });

    it('should throw ForbiddenError when therapist account was deleted by user', async function() {
      const mockUser = { id: 1, role: UserType.THERAPIST, deletedAt: new Date(), deletedBy: null, comparePassword: userComparePasswordStub, emailVerifiedAt: new Date() };
      userFindOneStub.resolves(mockUser as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
        expect(error.message).to.equal(ACCOUNT_DELETED);
      }
    });

    it('should throw ForbiddenError when therapist account was rejected', async function() {
      const mockUser = { id: 1, role: UserType.THERAPIST, acceptedAt: null, rejectedAt: new Date(), comparePassword: userComparePasswordStub, emailVerifiedAt: new Date() };
      userFindOneStub.resolves(mockUser as any);
      try {
        await loginWithoutMfa(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
        expect(error.message).to.equal('Account deleted!');
      }
    });

  });

  describe('sendMfaCode', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let redisGetStub: sinon.SinonStub;
    
    beforeEach(function() {
      mockRequest = { body: { email: '<EMAIL>', password: 'Password123!' }, headers: { 'x-forwarded-for': '***********' }, ip: '127.0.0.1' };
      userFindOneStub = sandbox.stub(User, 'findOne');
      redisGetStub = sandbox.stub(redisClient, 'get').resolves(null);
      sandbox.stub(bcrypt, 'hash').resolves('hashed-mfa-code');
      sandbox.stub(mail, 'sendMail').resolves();
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed_email');
    });
    
    it('should throw ConflictError when user is not found', async function() {
      userFindOneStub.resolves(null);
      try {
        await sendMfaCode(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
      }
    });
    
    it('should throw ConflictError when password is incorrect', async function() {
      userFindOneStub.resolves({ id: 1, comparePassword: sandbox.stub().returns(false) } as any);
      sandbox.stub(redisClient, 'incr').resolves(1);
      sandbox.stub(redisClient, 'expire').resolves(1);
      sandbox.stub(FailedLoginAttempt, 'create').resolves({} as any);
      try {
        await sendMfaCode(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
      }
    });
    
    it('should throw TooManyRequestsError when account is locked', async function() {
      userFindOneStub.resolves({ id: 1, comparePassword: () => true } as any);
      redisGetStub.resolves('5'); // 5 failed attempts
      try {
        await sendMfaCode(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(TooManyRequestsError);
      }
    });
    
    it('should throw ForbiddenError when therapist account is under review', async function() {
      userFindOneStub.resolves({ id: 1, role: UserType.THERAPIST, isUnderReview: true, comparePassword: () => true } as any);
      try {
        await sendMfaCode(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw NotFoundError if patient is not active', async function() {
      userFindOneStub.resolves({ id: 1, role: 'patient', version: 1, active: false, comparePassword: () => true } as any);
      try {
        await sendMfaCode(mockRequest as Request);
        expect.fail('Expected error');
      } catch (err) {
        expect(err).to.be.instanceOf(NotFoundError);
      }
    });

    it('should succeed and send MFA code', async function() {
      userFindOneStub.resolves({ id: 1, role: 'therapist', isUnderReview: false, acceptedAt: new Date(), rejectedAt: null, deletedAt: null, active: true, emailVerifiedAt: new Date(), comparePassword: () => true, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      redisGetStub.resolves(null);
      const result = await sendMfaCode(mockRequest as Request);
      expect(result).to.have.property('message');
    });


    it('should reactivate an inactive user', async () => {
      const updateStub = sandbox.stub().resolves();
      const mockUser = {
          id: 1,
          active: false,
          emailVerifiedAt: new Date(),
          comparePassword: () => true,
          update: updateStub,
          toJSON: () => ({})
      };
      userFindOneStub.resolves(mockUser as any);
      await sendMfaCode(mockRequest as Request);
      expect(updateStub.calledWith({ deletedAt: null, version: 1, active: true, deletedBy: null })).to.be.true;
    });
  });

  describe('verifyMFA', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let bcryptCompareStub: sinon.SinonStub;
    let redisGetStub: sinon.SinonStub;
    let userGenerateTokenStub: sinon.SinonStub;
    
    beforeEach(function() {
      mockRequest = { body: { email: '<EMAIL>', code: '123456' }, headers: {}, ip: '127.0.0.1' };
      userFindOneStub = sandbox.stub(User, 'findOne');
      bcryptCompareStub = sandbox.stub(bcrypt, 'compare').resolves(true);
      sandbox.stub(encryptionHelper, 'encryptedB64').returns('encrypted-token');
      redisGetStub = sandbox.stub(redisClient, 'get').resolves(null);
      sandbox.stub(MinorPatient, 'findAll').resolves([]);
      sandbox.stub(userRepository, 'getUserWithCalendarInfo').resolves({ id: 1 } as any);
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed_email');
      userGenerateTokenStub = sandbox.stub().returns('mock-token');
    });
    
    
    it('should throw ConflictError when user is not found', async function() {
      userFindOneStub.resolves(null);
      try { await verifyMFA(mockRequest as Request); expect.fail(); } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
      }
    });
    
    it('should throw ForbiddenError when account is locked', async function() {
      userFindOneStub.resolves({ id: 1 } as any);
      redisGetStub.withArgs(sinon.match.string).resolves('5');
      try { await verifyMFA(mockRequest as Request); expect.fail(); } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });
    
    it('should throw ConflictError when MFA code is expired', async function() {
      const mockUser = { id: 1, mfaCode: 'hashed', mfaExpiresAt: dayjs().subtract(1, 'hour').toDate() };
      userFindOneStub.resolves(mockUser as any);
      sandbox.stub(redisClient, 'incr').resolves(1);
      sandbox.stub(redisClient, 'expire').resolves(1);
      sandbox.stub(FailedLoginAttempt, 'create').resolves({} as any);
      try { await verifyMFA(mockRequest as Request); expect.fail(); } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
      }
    });
    
    it('should throw ConflictError when MFA code is invalid', async function() {
      const mockUser = { id: 1, mfaCode: 'hashed', mfaExpiresAt: dayjs().add(1, 'hour').toDate() };
      userFindOneStub.resolves(mockUser as any);
      bcryptCompareStub.resolves(false);
      sandbox.stub(redisClient, 'incr').resolves(1);
      sandbox.stub(redisClient, 'expire').resolves(1);
      sandbox.stub(FailedLoginAttempt, 'create').resolves({} as any);
      try { await verifyMFA(mockRequest as Request); expect.fail(); } catch (error: any) {
        expect(error).to.be.instanceOf(ConflictError);
      }
    });
  });

  describe('resendMFA', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let sendSmsStub: sinon.SinonStub;
    
    beforeEach(function() {
      mockRequest = { body: { email: '<EMAIL>' } };
      userFindOneStub = sandbox.stub(User, 'findOne').resolves({ id: 1, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      sendSmsStub = sandbox.stub(twilioConfig, 'sendSms').resolves();
      sandbox.stub(bcrypt, 'hash').resolves('hashed-code');
      sandbox.stub(mail, 'sendMail').resolves();
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed_email');
    });


    it('should throw ConflictError if user not found', async () => {
      userFindOneStub.resolves(null);
      try {
          await resendMFA(mockRequest as Request);
          expect.fail('Should have thrown');
      } catch (err: any) {
          expect(err).to.be.instanceOf(ConflictError);
      }
    });
  });

  describe('registerPatient', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let userCreateStub: sinon.SinonStub;
    let pageFindOneStub: sinon.SinonStub;
    let transactionStub: any;

    beforeEach(function() {
      const pageId = 'page-1';
      mockRequest = {
        body: {
          [pageId]: {
            page: { id: pageId, type: 'register', title: 'Registration' },
            answers: { first_name: 'Test', last_name: 'User', email: '<EMAIL>', dob: '1990-01-01', password: 'Password123!', gender: 'male', address: { lat: 1, lng: 1 } },
          },
        },
        query: { category: 'anxiety' }
      };
      userFindOneStub = sandbox.stub(User, 'findOne').resolves(null);
      pageFindOneStub = sandbox.stub(Page, 'findOne').resolves({ id: pageId, toJSON: () => ({ id: pageId }) } as any);
      userCreateStub = sandbox.stub(User, 'create').resolves({ id: 1, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      transactionStub = { commit: sandbox.stub().resolves(), rollback: sandbox.stub().resolves() };
      sandbox.stub(sequelize, 'transaction').resolves(transactionStub);
      sandbox.stub(UserSettings, 'create').resolves({} as any);
      sandbox.stub(PatientProfile, 'create').resolves({} as any);
      sandbox.stub(UserQuestionnaire, 'bulkCreate').resolves([] as any);
      sandbox.stub(UserValues, 'bulkCreate').resolves([] as any);
      sandbox.stub(mail, 'sendMail').resolves();
    });

    it('should throw UnprocessableEntityError if user already exists', async () => {
      userFindOneStub.resolves({} as any);
      try {
        await registerPatient(mockRequest as Request);
        expect.fail('Expected error');
      } catch (err) {
        expect(err).to.be.instanceOf(UnprocessableEntityError);
      }
    });

    it('should rollback transaction on database error', async () => {
      userCreateStub.rejects(new Error('DB Error'));
      try {
        await registerPatient(mockRequest as Request);
        expect.fail('Expected error');
      } catch (err) {
        expect(transactionStub.rollback.calledOnce);
      }
    });
  });

  describe('registerPatientMinor', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let userCreateStub: sinon.SinonStub;
    let transactionStub: any;

    beforeEach(function() {
      mockRequest = { body: { firstname: 'Parent', lastname: 'User', dob: '1980-01-01', email: '<EMAIL>', password: 'Password123!', confirmPassword: 'Password123!', is_minor: true, minor_details: [{ firstName: 'Minor', lastName: 'User', dob: '2010-01-01' }], address: { lat: 1, lng: 1 }, timeZone: 'UTC', gender: 'female', therapy_for: 'anxiety' } };
      userFindOneStub = sandbox.stub(User, 'findOne').resolves(null);
      userCreateStub = sandbox.stub(User, 'create').resolves({ id: 1, email: '<EMAIL>', update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      sandbox.stub(MinorPatient, 'create').resolves({} as any);
      sandbox.stub(bcrypt, 'hash').resolves('hashed-password');
      sandbox.stub(mail, 'sendMail').resolves();
      transactionStub = { commit: sandbox.stub().resolves(), rollback: sandbox.stub().resolves() };
      sandbox.stub(sequelize, 'transaction').resolves(transactionStub);
    });

    it('should register successfully', async () => {
      const result = await registerPatientMinor(mockRequest as Request, {} as any, null);
      expect(result).to.have.property('message', 'Registration successful');
    });

    it('should rollback transaction on error', async () => {
      userCreateStub.rejects(new Error('DB error'));
      try {
        await registerPatientMinor(mockRequest as Request, {} as any, null);
        expect.fail('Expected error');
      } catch (err) {
        expect(transactionStub.rollback.calledOnce).to.be.true;
      }
    });
  });

  describe('forgotPassword', function() {
    it('should send password reset link', async function() {
      const mockRequest = { body: { email: '<EMAIL>' } };
      sandbox.stub(User, 'findOne').resolves({ id: 1, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      sandbox.stub(encryptionHelper, 'encryptedB64').returns('encrypted-token');
      sandbox.stub(mail, 'sendMail').resolves();
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed_email');
      const result = await forgotPassword(mockRequest as Request);
      expect(result).to.have.property('message', 'Magic link sent to your email.');
    });
  });

  describe('resetPassword', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let decryptedB64Stub: sinon.SinonStub;
    
    beforeEach(function() {
      mockRequest = { body: { token: 'reset-token', password: 'NewPassword123!', confirmPassword: 'NewPassword123!' } };
      userFindOneStub = sandbox.stub(User, 'findOne');
      decryptedB64Stub = sandbox.stub(encryptionHelper, 'decryptedB64');
      sandbox.stub(bcrypt, 'hashSync').returns('hashed-password');
      sandbox.stub(redisClient, 'del').resolves(1);
    });

    it('should reset password successfully', async function() {
      decryptedB64Stub.returns('1:' + dayjs().unix());
      userFindOneStub.resolves({ id: 1, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      const result = await resetPassword(mockRequest as Request);
      expect(result).to.have.property('message', 'Password reset successfully.');
    });
    
    it('should throw BadRequestError when token is expired', async function() {
      decryptedB64Stub.returns('1:' + (dayjs().unix() - 3601));
      userFindOneStub.resolves({ id: 1 } as any);
      try {
        await resetPassword(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(BadRequestError);
      }
    });
    
    it('should throw BadRequestError when passwords do not match', async function() {
      mockRequest.body.confirmPassword = 'DifferentPassword123!';
      decryptedB64Stub.returns('1:' + dayjs().unix());
      userFindOneStub.resolves({ id: 1 } as any);
      try {
        await resetPassword(mockRequest as Request);
      } catch (error: any) {
      }
    });
  });

  describe('changePassword', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;
    let userComparePasswordStub: sinon.SinonStub;
    
    beforeEach(function() {
      mockRequest = { body: { old_password: 'OldPassword123!', new_password: 'NewPassword123!', confirm_password: 'NewPassword123!', email: '<EMAIL>' }, user: { id: 1 } as any };
      userFindOneStub = sandbox.stub(User, 'findOne');
      userComparePasswordStub = sandbox.stub().returns(true);
      sandbox.stub(bcrypt, 'hashSync').returns('hashed-password');
      sandbox.stub(userRepository, 'getUserWithCalendarInfo').resolves({} as any);
    });
    
    it('should change password and return success message', async function() {
      userFindOneStub.resolves({ id: 1, comparePassword: userComparePasswordStub, update: sandbox.stub().resolves() } as any);
      const result = await changePassword(mockRequest as Request);
      expect(result).to.have.property('message', 'Password changed successfully.');
    });
    
    it('should throw ForbiddenError when old password is incorrect', async function() {
      userFindOneStub.resolves({ id: 1, comparePassword: sandbox.stub().returns(false) } as any);
      try {
        await changePassword(mockRequest as Request);
        expect.fail('Expected an error to be thrown');
      } catch (error: any) {
        expect(error).to.be.instanceOf(ForbiddenError);
      }
    });
  });

  describe('emailResetToken', function() {
    let mockRequest: Partial<Request>;
    let userFindOneStub: sinon.SinonStub;

    beforeEach(function() {
      mockRequest = { body: { old_email: '<EMAIL>', new_email: '<EMAIL>' }, user: { id: 1 } as any };
      userFindOneStub = sandbox.stub(User, 'findOne');
      sandbox.stub(mail, 'sendMail').resolves();
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed-data');
    });

    it('should send email successfully', async () => {
      userFindOneStub.withArgs({ where: { email_hash: sinon.match.any, id: 1 } }).resolves({ id: 1, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      userFindOneStub.withArgs({ where: { email_hash: sinon.match.any } }).resolves(null);
      const result = await emailResetToken(mockRequest as Request);
      expect(result).to.have.property('message', 'Email Reset Token sent to your email.');
    });

    it('should throw ForbiddenError if new email is already in use', async () => {
      userFindOneStub.withArgs({ where: { email_hash: sinon.match.any, id: 1 } }).resolves({ id: 1 } as any);
      userFindOneStub.withArgs(sinon.match.any).resolves({ id: 2 } as any);
      try {
        await emailResetToken(mockRequest as Request);
        expect.fail('Expected error');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });
  });


  describe('verifyPatientEmail', function() {
    it('should succeed and return tokens', async () => {
      const mockRequest = { body: { email: '<EMAIL>', password: 'password' } };
      const user = { active: true, role: 'patient', emailVerifiedAt: new Date(), comparePassword: () => true, generateToken: () => 'token', toJSON: () => ({}), deletedAt: null };
      sandbox.stub(User, 'findOne').resolves(user as any);
      sandbox.stub(MinorPatient, 'findAll').resolves([]);
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed_email');
      sandbox.stub(encryptionHelper, 'encryptedB64').returns('encrypted_token');
      const result = await verifyPatientEmail(mockRequest as Request);
      expect(result).to.have.property('accessToken');
    });
  });

  describe('resetEmail', function() {
    it('should reset email successfully', async () => {
      const mockRequest = { body: { old_email: '<EMAIL>', new_email: '<EMAIL>', code: '123456' }, user: { id: 1 } };
      const validToken = encryptionHelper.encryptedB64(`1:${dayjs().unix() + 1000}:123456`);
      sandbox.stub(User, 'findOne').resolves({ passwordResetToken: validToken, id: 1, update: sandbox.stub().resolves() } as any);
      sandbox.stub(userRepository, 'getUserWithCalendarInfo').resolves({} as any);
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed-data');
      const result = await resetEmail(mockRequest as Request);
      expect(result).to.have.property('message', 'Email updated successfully.');
    });
  });

  describe('resetPhone', function() {
    it('should reset phone successfully', async () => {
      const mockRequest = { body: { new_phone: '1234567890', code: '123456' }, user: { id: 1 } };
      const validToken = encryptionHelper.encryptedB64(`1:${dayjs().unix() + 1000}:123456`);
      sandbox.stub(User, 'findOne').resolves({ passwordResetToken: validToken, id: 1, update: sandbox.stub().resolves() } as any);
      sandbox.stub(userRepository, 'getUserWithCalendarInfo').resolves({} as any);
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed-data');
      const result = await resetPhone(mockRequest as Request);
      expect(result).to.have.property('message', 'Phone number updated successfully.');
    });
  });

  describe('phoneResetToken', () => {
    it('should send an SMS with a verification code', async () => {
      const mockRequest = { body: { new_phone: '1234567890' }, user: { id: 1 } };
      sandbox.stub(User, 'findOne').resolves({ id: 1, update: sandbox.stub().resolves() } as any);
      const sendSmsStub = sandbox.stub(twilioConfig, 'sendSms').resolves();
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed-data');
      await phoneResetToken(mockRequest as Request);
      expect(sendSmsStub.calledOnce).to.be.true;
    });
  });

  describe('emailVerification', function() {
    it('should send email with link', async () => {
      const mockRequest = { body: { email: '<EMAIL>' } };
      sandbox.stub(User, 'findOne').resolves({ id: 1, update: sandbox.stub().resolves(), toJSON: () => ({}) } as any);
      sandbox.stub(encryptionHelper, 'encryptedB64').returns('encrypted-token');
      sandbox.stub(mail, 'sendMail').resolves();
      sandbox.stub(cryptoUtil, 'hashData').returns('hashed-data');
      const result = await emailVerification(mockRequest as Request);
      expect(result).to.have.property('message');
    });
  });
});
