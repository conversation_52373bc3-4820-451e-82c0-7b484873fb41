import { User, TherapistBlockedList } from '@/src/models'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { NotFoundError } from '../handlers/errors'
import logger from '@/src/configs/logger.config'

export const blockPatient = async (req: Request) => {
	const { id: patientId } = req.params;
	const therapistId = req.user?.id;

	logger.info(`TherapistID ${therapistId} is attempting to block PatientID ${patientId}.`);

	const patient = await User.findByPk(patientId)
	if (!patient) throw new NotFoundError('Patient not found')

	const [_, created] = await TherapistBlockedList.findOrCreate({
		where: {
			patientId,
			therapistId,
		},
		defaults: {
			patientId,
			therapistId,
		},
	});

	if (!created) {
		logger.info(`TherapistID ${therapistId} attempted to block PatientID ${patientId}, but they were already blocked.`);
		throw new Error('Patient is already blocked');
	}

	logger.info(`TherapistID ${therapistId} successfully blocked PatientID ${patientId}.`);
}

export const unblockPatient = async (req: Request) => {
	const { id: patientId } = req.params
	const therapistId = req.user?.id;

	logger.info(`TherapistID ${therapistId} is attempting to unblock PatientID ${patientId}.`);

	const rowsDeleted = await TherapistBlockedList.destroy({
		where: {
				patientId,
				therapistId,
		},
	});

	if (rowsDeleted === 0) {
		logger.info(`TherapistID ${therapistId} attempted to unblock PatientID ${patientId}, but they were not found in the blocked list.`);
		throw new Error('The patient was not found in the blocked list');
	}
	logger.info(`TherapistID ${therapistId} successfully unblocked PatientID ${patientId}.`);
}

export const getBlockedPatients = async (req: Request) => {
	logger.info(`TherapistID ${req.user?.id} is retrieving their blocked patients list.`);

	const patients = await TherapistBlockedList.findAndCountAll({
		where: {
			therapistId: req.user?.id,
		},
		include: [
			{
				model: User,
				as: 'patient',
				attributes: ['id', 'firstname', 'lastname', 'email', 'phone'],
			},
		],
		...paginated(req),
	})
	logger.info(`TherapistID ${req.user?.id} retrieved ${patients.count} blocked patients.`);
	return patients
}