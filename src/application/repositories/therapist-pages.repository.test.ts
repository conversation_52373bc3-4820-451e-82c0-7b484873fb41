import sinon from 'sinon';
import { TherapistPage } from '@/src/models';
import { expect } from 'chai';
import { getTherapistPages } from './therapist-pages.repository';
import { UserType } from '../helpers/constant.helper';

describe('Therapist Pages Repository Test', function () {
  let sandbox: sinon.SinonSandbox;
  let findAllStub: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    findAllStub = sandbox.stub(TherapistPage, 'findAll');
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should return active therapist pages for SYSTEM user', async function () {
    const dummyPages = [{ id: 1, is_active: true }];
    findAllStub.resolves(dummyPages);
    const req = { role: UserType.SYSTEM } as any;
    const result = await getTherapistPages(req);
    expect(findAllStub.calledOnce).to.be.true;
    expect(findAllStub.firstCall.args[0]).to.deep.equal({ where: { is_active: true } });
    expect(result).to.equal(dummyPages);
  });

  it('should not call findAll if role is not SYSTEM', async function () {
    const req = { role: 'PATIENT' } as any;
    const result = await getTherapistPages(req);
    expect(findAllStub.notCalled).to.be.true;
    expect(result).to.be.undefined;
  });
}); 