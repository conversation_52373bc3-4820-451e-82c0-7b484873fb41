import { Questionnaire } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Op,
	Order,
	WhereOptions,
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '@/src/application/helpers/pagination.helper'
import { NotFoundError } from '@/src/application/handlers/errors'

export const getQuestionnaires = async (req: Request) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = []
	const relations: any = []
	const attributes: FindAttributeOptions = []

	const { search } = req.query

	if (search) {
		where = {
			...where,
			question: {
				[Op.iLike]: `%${search}%`,
			},
		}
	}

	order.push(['question', 'ASC'])

	return Questionnaire.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req, 20),
	})
}

export const createQuestionnaire = async (req: Request) => {
	const { question, info } = req.body
	const questionnaire = await Questionnaire.create({
		question,
		info,
	})

	return questionnaire
}

export const getQuestionnaireById = async (id: number | string) => {
	const questionnaire = await Questionnaire.findByPk(id)
	if (!questionnaire) throw new NotFoundError('Questionnaire not found')
	return questionnaire
}

export const updateQuestionnaire = async (req: Request) => {
	const questionnaire = await getQuestionnaireById(req.params.id)

	const { question, info } = req.body

	await questionnaire.update({
		question,
		info,
	})

	return questionnaire
}

export const deleteQuestionnaire = async (id: number | string) => {
	const questionnaire = await getQuestionnaireById(id)
	return questionnaire.destroy()
}
