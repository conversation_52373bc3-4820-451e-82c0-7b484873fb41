import { expect } from 'chai';
import sinon from 'sinon';
import { Request } from 'express';
import * as eventRepo from '../repositories/event.respository';
import { Event } from '@/src/models';
import * as paginationHelper from '@/src/application/helpers/pagination.helper';
import { NotFoundError } from '@/src/application/handlers/errors';
import sequelize from 'sequelize/lib/sequelize';

const sandbox = sinon.createSandbox();

describe('Event Repository', () => {
  const paginatedResult = { offset: 0, limit: 10 };
  const mockEvent = {
    id: 1,
    update: sandbox.stub().resolves(true),
    destroy: sandbox.stub().resolves(true),
  };
  const body = {
    name: 'Test',
    description: 'Desc',
    startDate: '2025-01-01',
    endDate: '2025-01-02',
    tagId: 1,
    isAllDay: false,
    isRecurring: false,
    location: 'Online',
    attendees: []
  };

  beforeEach(() => {
    sandbox.stub(paginationHelper, 'paginated').returns(paginatedResult);
  });

  afterEach(() => sandbox.restore());

  describe('getEvents', () => {
    it('should return events', async () => {
      const findStub = sandbox.stub(Event, 'findAndCountAll').resolves({ rows: [], count: [] });
      const req = {} as Partial<Request> as Request;
      const result = await eventRepo.getEvents({ req, perPage: 10 });
      expect(findStub.calledOnce).to.be.true;
      expect(result).to.eql({ rows: [], count: [] });
    });
  });

  describe('createEvent', () => {
    it('should create an event', async () => {
      const createStub = sandbox.stub(Event, 'create').resolves(mockEvent as any);
      const req = { body } as Partial<Request> as Request;
      const result = await eventRepo.createEvent(req);
      expect(createStub.calledWith(body)).to.be.true;
      expect(result).to.eql(mockEvent);
    });
  });

  describe('getEventById', () => {
    it('should return event when found', async () => {
      sandbox.stub(Event, 'findByPk').resolves(mockEvent as any);
      const result = await eventRepo.getEventById(1);
      expect(result).to.eql(mockEvent);
    });

    it('should throw NotFoundError when not found', async () => {
      sandbox.stub(Event, 'findByPk').resolves(null);
      try {
        await eventRepo.getEventById(999);
      } catch (err) {
        if (err instanceof NotFoundError) {
          expect(err.message).to.equal('Event not found');
        } else {
          throw err;
        }
      }
    });
  });

  describe('updateEvent', () => {
    it('should update the event', async () => {
      sandbox.stub(eventRepo, 'getEventById').resolves(mockEvent as any);
      const req = { params: { id: '1' }, body } as Partial<Request> as Request;
      const result = await eventRepo.updateEvent(req);
      expect(mockEvent.update.calledWith(body)).to.be.true;
      expect(result).to.be.true;
    });
  });

  describe('deleteEvent', () => {
    it('should delete the event', async () => {
      sandbox.stub(eventRepo, 'getEventById').resolves(mockEvent as any);
      const result = await eventRepo.deleteEvent(1);
      expect(mockEvent.destroy.calledOnce).to.be.true;
      expect(result).to.be.true;
    });
  });

  describe('getMonthlyEvents', () => {
    it('should fetch events filtered by month/year', async () => {
      const findStub = sandbox.stub(Event, 'findAndCountAll').resolves({ rows: [], count: [] });
      const req = {} as Partial<Request> as Request;
      const result = await eventRepo.getMonthlyEvents({ req, month: 1, year: 2025, perPage: 10 });
      expect(findStub.calledOnce).to.be.true;
      expect(result).to.eql({ rows: [], count: [] });
    });
  });
});
