import { User } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Op,
	Order,
	WhereOptions,
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { UserType } from '../helpers/constant.helper'
import logger from '@/src/configs/logger.config'

type PatientFilter = {
	req: Request
	perPage?: number
}

export const getPatients = async ({ req, perPage }: PatientFilter) => {
	logger.info(`UserID ${req.user?.id} is fetching patient list.`);

	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = [['firstname', 'ASC']]
	const relations: any = []
	const attributes: FindAttributeOptions = [ 'id',
    'firstname', 
    'lastname',
    'email',
    'role',
    'active',
    ['deletedAt', 'removedAt'],
    'createdAt',
    'updatedAt',
		'phone',
		'deactivatedAt',
		'emailVerifiedAt'
	]

	where = {
		...where,
		role: UserType.PATIENT,
		id: {
			[Op.ne]: req.user?.id,
		},
	}

	if (req.role === UserType.THERAPIST) {
		logger.info(`TherapistID ${req.user?.id} is accessing their patients.`);
        
		relations.push({
			model: User,
			as: 'therapists',
			required: true,
			attributes: [],
			where: {
				id: req.user?.id,
			},
		})
	}
	
	if (req.query.search) {
		logger.info(`UserID ${req.user?.id} performed a patient search. Query=${req.query.search}`);
        
		where = {
			...where,
			[Op.or]: [
				{
					firstname: {
						[Op.iLike]: `%${req.query.search}%`,
					},
				},
				{
					lastname: {
						[Op.iLike]: `%${req.query.search}%`,
					},
				},
			],
		}
	}

	order.push(['createdAt', 'DESC'])

	return User.findAndCountAll({
		attributes,
		include: relations,
		where,
		group,
		order,
		...paginated(req, perPage),
	})
}
