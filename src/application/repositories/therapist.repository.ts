import { TherapistProfile, User, UserRegistrationInfo, UserSettings,ProfileRejectionReason,StripeInfo, UserDeleteDeactivateReason, ProfileDeleteDeactivateReason } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Includeable,
	Op,
	Order,
	WhereOptions,
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { UserType } from '../helpers/constant.helper'
import sequelize from '@/src/configs/database.config'
import dayjs from 'dayjs'
import { ForbiddenError, NotFoundError } from '../handlers/errors'
import { mail } from '@/src/configs/sendgrid.config'
import { AcceptUserEmail } from '../emails/accept.email'
import { RejectUserEmail } from '../emails/reject.email'
import { areaOfFocus, fluentLanguage, genders, insuranceTypes, licenseTypeIndependent, modalities, race, religions, specialities, states } from '@/src/constants/registration.constants'
import { Appointments } from '@/src/models';
import { TherapistDurationAndCost } from '@/src/models';
import { TELEHEALTH,IN_PERSON,IN_PERSON_TELEHEALTH,PROFILE,PAYMENT_FORMS,PRACTICE_INFO,MODALITIES,PRACTICE_FOCUS,SPECIALIZATION,NORMAL_OFFICE_HOURS,LICENSE,WAITLIST_NOTIFICATION,THERAPIST_NOT_AVAILABLE } from '../controllers/api/constants'
import logger from '@/src/configs/logger.config'
import { removePatientsFromTherapistWaitlist } from './therapist-waitlist.repository'


type TherapistFilter = {
	req: Request
	perPage?: number
}

export const getTherapists = async ({ req, perPage }: TherapistFilter) => {
	const where: WhereOptions = {
	  role: UserType.THERAPIST,
	  id: { [Op.ne]: req.user?.id },
	};
  
	if (req.query.getUnverified) where.acceptedAt = null;
  
	if (req.query.filter === 'accepted') {
	  where.acceptedAt = { [Op.not]: null };
	  where.rejectedAt = null;
		where.deletedAt = null;
		where.deactivatedAt = null;
	} else if (req.query.filter === 'rejected') {
	  where.rejectedAt = { [Op.not]: null };
	  where.acceptedAt = null;
		where.deletedAt = null;
		where.deactivatedAt = null;
	} else if (req.query.filter === 'pending') {
	 where.isUnderReview = false;
	  where.acceptedAt = null;
	  where.rejectedAt = null;
		where.deletedAt = null;
		where.deactivatedAt = null;
	}else if (req.query.filter === 'deactivated') {
		where.deactivatedAt= { [Op.not]: null };
	}else if(req.query.filter === 'deleted'){
		where.deletedBy= { [Op.not]: null };
		where.deletedAt= { [Op.not]: null };
	}else if(req.query.filter === 'under_review'){
		where.isUnderReview = true;		
	}

	if (req.query.search) {
	  Object.assign(where, {
		[Op.or]: [
		  { firstname: { [Op.iLike]: `%${req.query.search}%` } },
		  { lastname: { [Op.iLike]: `%${req.query.search}%` } },
		],
	  });
	}
  
	const include: Includeable[] = [
	  {
		model: UserRegistrationInfo,
		as: 'registrationInfo',
		required: false,
	  },
	  {
      model: ProfileRejectionReason,
      as: 'rejectionReasons',
      required: false,
      attributes: ['reason', 'rejectedAt'],
      order: [['rejectedAt', 'DESC']],
      separate: true,
      limit: 1,
    },
    {
      model: UserDeleteDeactivateReason,
      as: 'deleteDeactivateReasons',
      required: false,
      attributes: ['reasonId','type', 'createdAt'],
      include: [
        {
          model: ProfileDeleteDeactivateReason,
          attributes: ['subheading'],
        },
      ],
      order: [['createdAt', 'DESC']],
      separate: true,
      limit: 1,
    },
	];
  
	const order: Order = [
	  [sequelize.literal('CASE WHEN "acceptedAt" IS NULL THEN 0 ELSE 1 END'), 'ASC'],
	  ['firstname', 'ASC'],
	  ['createdAt', 'DESC']
	];
  
	const [count, rows] = await Promise.all([
	  User.count({
		distinct: true,
		col: 'id',
		where,
		include,
	  }),
	  User.findAll({
		attributes: {
		  include: [
			'id',
			'firstname',
			'lastname',
			'email',
			'role',
			'acceptedAt',
			'rejectedAt',
			['deletedAt','userDeletedAt'],
			'createdAt',
			'updatedAt'
		  ]
		},
		where,
		include,
		order,
		...paginated(req, perPage),
	  }),
	]);
	const formattedRows = rows.map((user: any) => {
  const json = user.toJSON();

  const rejection = json.rejectionReasons?.[0];
  const deleteDeactivate = json.deleteDeactivateReasons?.[0];

  let statusReason = null;
  let statusType = null;

  const rejectionDate = rejection?.rejectedAt ? new Date(rejection.rejectedAt) : null;
  const deleteDeactivateDate = deleteDeactivate?.createdAt ? new Date(deleteDeactivate.createdAt) : null;

  if (rejectionDate && (!deleteDeactivateDate || rejectionDate > deleteDeactivateDate)) {
    statusReason = rejection.reason;
    statusType = 'rejected';
  } else if (deleteDeactivateDate) {
  statusReason = deleteDeactivate?.reason?.subheading || 'Unknown reason';
    statusType = deleteDeactivate.type;
  }

  return {
    ...json,
    statusReason,
    statusType,
  };
});

	return { count, rows: formattedRows };
  };
  

export const getTherapistById = async (req: Request) => {
	const relations: Includeable[] = [
		{
			model: TherapistProfile,
			as: 'profile',
		},
		{
			model: UserRegistrationInfo,
			as: 'registrationInfo',
		}
	];

	const therapist = await User.findOne({
		attributes: ['id', "emailVerifiedAt","address","gender","firstname","lastname"],
		where: {
			id: req.params.id,
			role: UserType.THERAPIST,
		},
		include: relations,
	})
	if (!therapist) throw new NotFoundError('Therapist not found')

	return therapist
}

export const getTherapistDetails = async (id: number, patientId: number) => {
	
	const therapist = await User.findOne({
		where: {
			id: id,
			role: UserType.THERAPIST,
		},
	})
	if (!therapist) throw new NotFoundError('Therapist information not found');
	 const stripeInfo = await StripeInfo.findOne({
        where: {
            therapistId: id,
        },
        order: [['createdAt', 'DESC']],
    });

    if (stripeInfo && stripeInfo.status === 'canceled') {
        throw new ForbiddenError(THERAPIST_NOT_AVAILABLE);
    }

	if (therapist.role === UserType.THERAPIST) {
		if (therapist.acceptedAt === null && therapist.rejectedAt === null && therapist.deletedAt === null) {
		  throw new ForbiddenError(THERAPIST_NOT_AVAILABLE);
		}
		else if(therapist.deactivatedAt!=null){
			throw new ForbiddenError(THERAPIST_NOT_AVAILABLE);
		}
		else if (therapist.acceptedAt === null || therapist.rejectedAt !== null) {
		  throw new ForbiddenError(THERAPIST_NOT_AVAILABLE);
		}
		else if(therapist.deletedAt!==null && therapist.deletedBy === 'admin'){
		  throw new ForbiddenError(THERAPIST_NOT_AVAILABLE);
		}
		
		else if(therapist.deletedAt !== null) {
		  throw new ForbiddenError(THERAPIST_NOT_AVAILABLE);
		}
	  }
	
    const profileInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: PROFILE,
        },
    });

    if (!profileInfo) throw new NotFoundError('Therapist profile information not found');

    const paymentInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: PAYMENT_FORMS,
        },
    });

	const waitlistInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: WAITLIST_NOTIFICATION,
        },
    });

    if (!paymentInfo) throw new NotFoundError('Therapist payment information not found');
	if (!waitlistInfo) throw new NotFoundError('Therapist payment information not found');

    const practiceInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: PRACTICE_INFO,
        },
    });

    if (!practiceInfo) throw new NotFoundError('Therapist practice information not found');

    const modalitiesInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: MODALITIES,
        },
    });

    if (!modalitiesInfo) throw new NotFoundError('Therapist modalities information not found');

    const practiceFocusInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: PRACTICE_FOCUS,
        },
    });

    if (!practiceFocusInfo) throw new NotFoundError('Therapist practice focus information not found');

    const specializationInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: SPECIALIZATION,
        },
    });

    if (!specializationInfo) throw new NotFoundError('Therapist specialization information not found');

    const officeHoursInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: NORMAL_OFFICE_HOURS,
        },
    });

    if (!officeHoursInfo) throw new NotFoundError('Therapist office hours information not found');

    const licenseInfo = await UserRegistrationInfo.findOne({
        attributes: ['payloadInfo'],
        where: {
            userId: id,
            pageName: LICENSE,
        },
    });

    if (!licenseInfo) throw new NotFoundError('Therapist license information not found');

    const profilePayload = profileInfo.payloadInfo as any;
    const paymentPayload = paymentInfo.payloadInfo as any;
	const waitlistPayload = waitlistInfo.payloadInfo as any;
    const practicePayload = practiceInfo.payloadInfo as any;
    const modalitiesPayload = modalitiesInfo.payloadInfo as any;
    const practiceFocusPayload = practiceFocusInfo.payloadInfo as any;
    const specializationPayload = specializationInfo.payloadInfo as any;
    const officeHoursPayload = officeHoursInfo.payloadInfo as any;
    const licensePayload = licenseInfo.payloadInfo as any;

    const modalitiesKeys = Object.keys(modalitiesPayload);
    const appointmentMethod = officeHoursPayload.appointmentMethod;

    const teleHealthFlag = [TELEHEALTH, IN_PERSON_TELEHEALTH].includes(appointmentMethod);
    const inPersonFlag = [IN_PERSON, IN_PERSON_TELEHEALTH].includes(appointmentMethod);

    const transformedProfile = {
		...profilePayload,
		identify: profilePayload.identify.checked !== 'other' ? profilePayload.identify.checked : profilePayload.identify.other_identify,
		race: profilePayload.race.checked !== 'other' ? profilePayload.race.checked : profilePayload.race.other_race,
		language: profilePayload.language.checked_list.includes('other') 
			? [...profilePayload.language.checked_list.filter((item: string) => item !== 'other'), profilePayload.language.other_language] 
			: profilePayload.language.checked_list,
		educations: profilePayload.educations,
		profile_statement: profilePayload.profile_statement,
		social_media: profilePayload.social_media,
		video_links: profilePayload.video_links,
	};

    const transformedPracticeFocus = {
		...practiceFocusPayload,
        religious_specialization: [
            ...(practiceFocusPayload.religious_specialization?.checked_list.filter((item: string) => item !== 'other') || []),
            ...(practiceFocusPayload.religious_specialization?.other_religion ? [practiceFocusPayload.religious_specialization.other_religion] : [])
        ],
        additional_focus: [
			...(practiceFocusPayload.additional_focus?.checked_list.filter((item: string) => item !== 'other') || []),
            ...(practiceFocusPayload.additional_focus?.other_focus ? [practiceFocusPayload.additional_focus.other_focus] : [])
        ],
    };
	
	let transformedPaymentForms: any = {
		...paymentPayload,
		insurance: false,
		insurance_list: []
	};
	let transformedWaitlistForms: any = {
		...waitlistPayload,
	};
	
	if (paymentPayload.insurance) {
		transformedPaymentForms = {
			...paymentPayload,
			insurance_list: [
				...(paymentPayload.insurance_list?.checked_list.filter((item: string) => item !== 'other') || []),
				...(paymentPayload.insurance_list?.other_insurance ? [paymentPayload.insurance_list.other_insurance] : [])
			],
		};
	}
	
    const educationDetails = profilePayload.educations[0];

    return {
        id: id,
        firstName: therapist.firstname||'',
        lastName: therapist.lastname,
        email: profilePayload.email,
        dob: profilePayload.dob,
        userProfile: profilePayload.user_profile,
        experienceYears: profilePayload.experience_years,
        gender: transformedProfile.identify,
        race: transformedProfile.race,
        language: transformedProfile.language,
        bio: profilePayload.profile_statement,
        socialMedia: profilePayload.social_media,
        videoLinks: profilePayload.video_links,
        paymentForms: transformedPaymentForms,
		week_visibility: parseInt(transformedWaitlistForms.week_visibility, 10),
        practiceInfo: practicePayload,
        modalities: modalitiesKeys,
        practiceFocus: transformedPracticeFocus,
        specialization: specializationPayload,
        appointmentMethod: appointmentMethod,
        license: licensePayload,
        college: educationDetails.college,
        degreeType: educationDetails.degree_type,
        yearGraduated: educationDetails.year_graduated,
        teleHealthFlag,
        inPersonFlag,
    };
};
export const acceptTherapist = async (req: Request) => {
	const user = await User.findOne({
		where: {
			id: req.params.id,
			role: UserType.THERAPIST,
		},
	})
	if (!user) throw new NotFoundError('User not found')
	
	await user.update({
		acceptedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
		rejectedAt: null,
		isUnderReview: false,
	})

	const emailData = AcceptUserEmail.compile({
		email: user.email,
		user: user.toJSON(),
		url: `${process.env.FRONTEND_URL}/auth/login`,
	  })
	  
	  const mailData = {
		to: emailData.To,
		subject: emailData.Subject,
		html: emailData.HtmlBody,
	  }
	  
	  await mail.sendMail(mailData)
	  
}

export const rejectTherapist = async (req: Request) => {
	const user = await User.findOne({
		where: {
			id: req.params.id,
			role: UserType.THERAPIST,
		},
	})
	if (!user) throw new NotFoundError('User not found')
	
	await user.update({
		acceptedAt: null,
		rejectedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
		isUnderReview: false,
	})

	if (user.role === 'therapist') {
		const removedCount = await removePatientsFromTherapistWaitlist(user.id);
		logger.info(`Successfully removed ${removedCount} patient(s) from the waitlist of therapist ID: ${user.id} during profile rejection.`);
	}


	await ProfileRejectionReason.create({
		userId: user.id,
		reason:  req.body.reason,
		rejectedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	});

	const emailData = RejectUserEmail.compile({
		email: user.email,
		user: user.toJSON(),
		reason:req.body.reason,
	});
	  
	  const mailData = {
		to: emailData.To,
		from: emailData.From,
		subject: emailData.Subject,
		html: emailData.HtmlBody,
	  };
	  
	  await mail.sendMail(mailData);	  
}
