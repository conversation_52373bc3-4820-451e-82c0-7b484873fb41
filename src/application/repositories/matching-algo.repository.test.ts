import { expect } from 'chai';
import sinon from 'sinon';
import { Request } from 'express';
import { Transaction } from 'sequelize';
import * as sequelizeInstance from '@/src/configs/database.config';
import * as matchingRepo from '../repositories/matching-algo.repository';
import { MatchingAlgoResource } from '@/src/models';

import { UserType } from '@/src/application/helpers/constant.helper';

describe('MatchingAlgoResource Repository', () => {
  const sandbox = sinon.createSandbox();
  const mockTherapistPage = { value: 1 };
  const mockPatientPage = { value: 2 };
  const mockCategory = { value: 'categoryA' };
  const mockQuestion = { value: 3 };
  const mockAnswer = { value: 4 };

  afterEach(() => sandbox.restore());

  describe('createMatchingAlgoResource', () => {
    it('should insert data and commit transaction for SYSTEM role', async () => {
      const req = {
        body: {
          one: {
            therapistPage: mockTherapistPage,
            patientPage: mockPatientPage,
            category: mockCategory,
            question: mockQuestion,
            answer: mockAnswer,
          },
        },
        role: UserType.SYSTEM,
      } as unknown as Request;

      const transactionStub = sandbox.createStubInstance(Transaction);
      sandbox.stub(sequelizeInstance.default, 'transaction').resolves(transactionStub as unknown as Transaction);

      const findOrCreateStub = sandbox.stub(MatchingAlgoResource, 'findOrCreate').resolves([
      {
        dataValues: {},
        isNewRecord: true,
        get: () => ({}),
        toJSON: () => ({}),
      } as any,
      true,
    ]);

      await matchingRepo.createMatchingAlgoResource(req);

      expect(findOrCreateStub.calledOnce).to.be.true;
      expect(transactionStub.commit.calledOnce).to.be.true;
    });

    it('should rollback transaction on error', async () => {
      const req = {
        body: {
          one: {
            therapistPage: mockTherapistPage,
            patientPage: mockPatientPage,
            category: mockCategory,
            question: mockQuestion,
            answer: mockAnswer,
          },
        },
        role: UserType.SYSTEM,
      } as unknown as Request;

      const transactionStub = sandbox.createStubInstance(Transaction);
      sandbox.stub(sequelizeInstance.default, 'transaction').resolves(transactionStub as unknown as Transaction);
      sandbox.stub(MatchingAlgoResource, 'findOrCreate').throws();

      try {
        await matchingRepo.createMatchingAlgoResource(req);
      } catch (e) {
        expect(transactionStub.rollback.calledOnce).to.be.true;
      }
    });
  });

  describe('getAllMatchingAlgoResources', () => {
    it('should fetch all matching algo resources without category', async () => {
      const findAllStub = sandbox.stub(MatchingAlgoResource, 'findAll').resolves([]);
      const req = { query: {} } as unknown as Request;

      const result = await matchingRepo.getAllMatchingAlgoResources(req);

      expect(findAllStub.calledOnce).to.be.true;
      expect(result).to.eql([]);
    });

    it('should fetch all matching algo resources with category', async () => {
      const findAllStub = sandbox.stub(MatchingAlgoResource, 'findAll').resolves([]);
      const req = { query: { category: 'therapy' } } as unknown as Request;

      const result = await matchingRepo.getAllMatchingAlgoResources(req);

      expect(findAllStub.calledOnce).to.be.true;
      expect(result).to.eql([]);
    });
  });
});
