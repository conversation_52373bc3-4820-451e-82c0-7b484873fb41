import sinon from 'sinon';
import { expect } from 'chai';
import * as TherapistRepo from './therapist.repository';
import { User, UserRegistrationInfo, ProfileRejectionReason, UserDeleteDeactivateReason, ProfileDeleteDeactivateReason, TherapistProfile } from '@/src/models';
import { NotFoundError, ForbiddenError } from '../handlers/errors';
import sequelize from '@/src/configs/database.config';
import { Request } from 'express';
import {
  PROFILE, PAYMENT_FORMS, WAITLIST_NOTIFICATION, PRACTICE_INFO, MODALITIES, PRACTICE_FOCUS, SPECIALIZATION, NORMAL_OFFICE_HOURS, LICENSE,
  TELEHEALTH, IN_PERSON_TELEHEALTH, IN_PERSON
} from '../controllers/api/constants';

describe('Therapist Repository', function () {
  let sandbox: sinon.SinonSandbox;
  let req: Partial<Request>;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    req = { user: { id: 1 } as any, query: {}, params: {} };
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getTherapists', function () {
    let countStub: sinon.SinonStub, findAllStub: sinon.SinonStub;
    beforeEach(() => {
      countStub = sandbox.stub(User, 'count').resolves(1);
      findAllStub = sandbox.stub(User, 'findAll').resolves([
        {
          toJSON: () => ({
            id: 2, firstname: 'A', lastname: 'B', rejectionReasons: [], deleteDeactivateReasons: []
          })
        } as any
      ]);
    });
    it('should return therapists with formatted rows', async function () {
      const result = await TherapistRepo.getTherapists({ req: req as Request });
      expect(result.count).to.equal(1);
      expect(result.rows).to.be.an('array');
    });
    it('should handle search and filters', async function () {
      req.query = { search: 'A', filter: 'accepted' };
      await TherapistRepo.getTherapists({ req: req as Request });
      expect(countStub.called).to.be.true;
      expect(findAllStub.called).to.be.true;
    });

    it('should handle various filters correctly', async function () {
      const filters = ['rejected', 'pending', 'deactivated', 'deleted', 'under_review'];
      for (const filter of filters) {
        req.query = { filter };
        await TherapistRepo.getTherapists({ req: req as Request });
        const whereArg = findAllStub.lastCall.args[0].where;
        expect(whereArg).to.have.property('role', 'therapist');
        switch (filter) {
          case 'rejected':
            expect(whereArg).to.have.property('rejectedAt').that.is.not.null;
            break;
          case 'pending':
            expect(whereArg).to.have.property('acceptedAt', null);
            break;
          case 'deactivated':
            expect(whereArg).to.have.property('deactivatedAt').that.is.not.null;
            break;
          case 'deleted':
            expect(whereArg).to.have.property('deletedBy').that.is.not.null;
            break;
          case 'under_review':
            expect(whereArg).to.have.property('isUnderReview', true);
            break;
        }
      }
    });

    it('should handle getUnverified filter', async function () {
        req.query = { getUnverified: 'true' };
        await TherapistRepo.getTherapists({ req: req as Request });
        const whereArg = findAllStub.lastCall.args[0].where;
        expect(whereArg).to.have.property('acceptedAt', null);
    });

    it('should correctly format status reason from rejection reason', async function() {
        findAllStub.resolves([{
            toJSON: () => ({
                id: 2,
                firstname: 'A',
                lastname: 'B',
                rejectionReasons: [{ reason: 'Bad profile', rejectedAt: new Date() }],
                deleteDeactivateReasons: []
            })
        } as any]);
        const result = await TherapistRepo.getTherapists({ req: req as Request });
        expect(result.rows[0].statusReason).to.equal('Bad profile');
        expect(result.rows[0].statusType).to.equal('rejected');
    });

    it('should correctly format status reason from delete/deactivate reason', async function() {
        findAllStub.resolves([{
            toJSON: () => ({
                id: 2,
                firstname: 'A',
                lastname: 'B',
                rejectionReasons: [],
                deleteDeactivateReasons: [{ type: 'deleted', createdAt: new Date(), reason: { subheading: 'Account deleted' } }]
            })
        } as any]);
        const result = await TherapistRepo.getTherapists({ req: req as Request });
        expect(result.rows[0].statusReason).to.equal('Account deleted');
        expect(result.rows[0].statusType).to.equal('deleted');
    });

    it('should prioritize rejection reason if it is newer', async function() {
        findAllStub.resolves([{
            toJSON: () => ({
                id: 2,
                firstname: 'A',
                lastname: 'B',
                rejectionReasons: [{ reason: 'Newer rejection', rejectedAt: new Date('2023-01-02') }],
                deleteDeactivateReasons: [{ type: 'deactivated', createdAt: new Date('2023-01-01'), reason: { subheading: 'Older deactivation' } }]
            })
        } as any]);
        const result = await TherapistRepo.getTherapists({ req: req as Request });
        expect(result.rows[0].statusReason).to.equal('Newer rejection');
        expect(result.rows[0].statusType).to.equal('rejected');
    });
  });

  describe('getTherapistById', function () {
    let findOneStub: sinon.SinonStub;
    beforeEach(() => {
      findOneStub = sandbox.stub(User, 'findOne');
    });
    it('should return therapist if found', async function () {
      findOneStub.resolves({});
      req.params = { id: '2' };
      const result = await TherapistRepo.getTherapistById(req as Request);
      expect(result).to.exist;
    });
    it('should throw NotFoundError if not found', async function () {
      findOneStub.resolves(null);
      req.params = { id: '2' };
      try {
        await TherapistRepo.getTherapistById(req as Request);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
  });

  describe('getTherapistDetails', function () {
    let findOneStub: sinon.SinonStub;
    let stripeFindOneStub: sinon.SinonStub;
    let userRegFindOneStub: sinon.SinonStub;

    beforeEach(() => {
      findOneStub = sandbox.stub(User, 'findOne');
      stripeFindOneStub = sandbox.stub(require('@/src/models').StripeInfo, 'findOne');
      userRegFindOneStub = sandbox.stub(UserRegistrationInfo, 'findOne');
    });

    const baseTherapist = {
      id: 1,
      role: 'therapist',
      acceptedAt: new Date(),
      rejectedAt: null,
      deletedAt: null,
      deactivatedAt: null,
      deletedBy: null,
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
    };

    const basePayload = {
      identify: { checked: 'male' },
      race: { checked: 'asian' },
      language: { checked_list: ['en'] },
      educations: [{ college: 'A', degree_type: 'B', year_graduated: '2020' }],
      profile_statement: 'bio',
      social_media: 'sm',
      video_links: 'vl',
      user_profile: 'profile',
      experience_years: 5,
      email: '<EMAIL>',
      dob: '1990-01-01',
    };

    function stubAllProfileInfo() {
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PROFILE } })).resolves({ payloadInfo: basePayload });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PAYMENT_FORMS } })).resolves({ payloadInfo: { insurance: false, insurance_list: [] } });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: WAITLIST_NOTIFICATION } })).resolves({ payloadInfo: { week_visibility: '2' } });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PRACTICE_INFO } })).resolves({ payloadInfo: {} });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: MODALITIES } })).resolves({ payloadInfo: {} });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PRACTICE_FOCUS } })).resolves({ payloadInfo: { religious_specialization: { checked_list: [] }, additional_focus: { checked_list: [] } } });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: SPECIALIZATION } })).resolves({ payloadInfo: {} });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: NORMAL_OFFICE_HOURS } })).resolves({ payloadInfo: { appointmentMethod: TELEHEALTH } });
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: LICENSE } })).resolves({ payloadInfo: {} });
    }

    it('should throw ForbiddenError if stripeInfo is canceled', async function () {
      findOneStub.resolves({ ...baseTherapist });
      stripeFindOneStub.resolves({ status: 'canceled' });
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw ForbiddenError for therapist status: not accepted/rejected/deleted', async function () {
      findOneStub.resolves({ ...baseTherapist, acceptedAt: null, rejectedAt: null, deletedAt: null });
      stripeFindOneStub.resolves(null);
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw ForbiddenError for therapist status: deactivated', async function () {
      findOneStub.resolves({ ...baseTherapist, deactivatedAt: new Date() });
      stripeFindOneStub.resolves(null);
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw ForbiddenError for therapist status: acceptedAt null or rejectedAt not null', async function () {
      findOneStub.resolves({ ...baseTherapist, acceptedAt: null, rejectedAt: new Date() });
      stripeFindOneStub.resolves(null);
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw ForbiddenError for therapist status: deletedBy admin', async function () {
      findOneStub.resolves({ ...baseTherapist, deletedAt: new Date(), deletedBy: 'admin' });
      stripeFindOneStub.resolves(null);
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw ForbiddenError for therapist status: deletedAt not null', async function () {
      findOneStub.resolves({ ...baseTherapist, deletedAt: new Date(), deletedBy: null });
      stripeFindOneStub.resolves(null);
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw NotFoundError if profileInfo not found', async function () {
      findOneStub.resolves({ ...baseTherapist });
      stripeFindOneStub.resolves(null);
      userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PROFILE } })).resolves(null);
      try {
        await TherapistRepo.getTherapistDetails(1, 2);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(NotFoundError);
      }
    });

    // Repeat for each required info
    [
      { page: PAYMENT_FORMS, error: 'Therapist payment information not found' },
      { page: WAITLIST_NOTIFICATION, error: 'Therapist payment information not found' },
      { page: PRACTICE_INFO, error: 'Therapist practice information not found' },
      { page: MODALITIES, error: 'Therapist modalities information not found' },
      { page: PRACTICE_FOCUS, error: 'Therapist practice focus information not found' },
      { page: SPECIALIZATION, error: 'Therapist specialization information not found' },
      { page: NORMAL_OFFICE_HOURS, error: 'Therapist office hours information not found' },
      { page: LICENSE, error: 'Therapist license information not found' },
    ].forEach(({ page, error }) => {
      it(`should throw NotFoundError if ${page} not found`, async function () {
        findOneStub.resolves({ ...baseTherapist });
        stripeFindOneStub.resolves(null);
        stubAllProfileInfo();
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: page } })).resolves(null);
        try {
          await TherapistRepo.getTherapistDetails(1, 2);
          throw new Error('Should have thrown');
        } catch (err) {
          expect(err).to.be.instanceOf(NotFoundError);
        }
      });
    });

    it('should return successfully with transformed data', async function () {
        findOneStub.resolves({ ...baseTherapist });
        stripeFindOneStub.resolves({ status: 'active' });

        const profilePayload = {
            ...basePayload,
            identify: { checked: 'other', other_identify: 'Non-binary' },
            race: { checked: 'other', other_race: 'Mixed' },
            language: { checked_list: ['en', 'other'], other_language: 'fr' },
        };
        const paymentPayload = { insurance: true, insurance_list: { checked_list: ['Aetna', 'other'], other_insurance: 'Cigna' } };
        const practiceFocusPayload = {
            religious_specialization: { checked_list: ['Christian', 'other'], other_religion: 'Spiritual' },
            additional_focus: { checked_list: ['Anxiety', 'other'], other_focus: 'Trauma' }
        };
        const officeHoursPayload = { appointmentMethod: IN_PERSON_TELEHEALTH };

        stubAllProfileInfo();
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PROFILE } })).resolves({ payloadInfo: profilePayload });
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PAYMENT_FORMS } })).resolves({ payloadInfo: paymentPayload });
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PRACTICE_FOCUS } })).resolves({ payloadInfo: practiceFocusPayload });
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: NORMAL_OFFICE_HOURS } })).resolves({ payloadInfo: officeHoursPayload });
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: MODALITIES } })).resolves({ payloadInfo: { cbt: true, dbt: true } });

        const result = await TherapistRepo.getTherapistDetails(1, 2);

        expect(result).to.exist;
        expect(result.gender).to.equal('Non-binary');
        expect(result.race).to.equal('Mixed');
        expect(result.language).to.deep.equal(['en', 'fr']);
        expect(result.paymentForms.insurance).to.be.true;
        expect(result.paymentForms.insurance_list).to.deep.equal(['Aetna', 'Cigna']);
        expect(result.practiceFocus.religious_specialization).to.deep.equal(['Christian', 'Spiritual']);
        expect(result.practiceFocus.additional_focus).to.deep.equal(['Anxiety', 'Trauma']);
        expect(result.teleHealthFlag).to.be.true;
        expect(result.inPersonFlag).to.be.true;
        expect(result.modalities).to.deep.equal(['cbt', 'dbt']);
    });

    it('should return successfully with alternative transformed data', async function() {
        findOneStub.resolves({ ...baseTherapist });
        stripeFindOneStub.resolves({ status: 'active' });

        const profilePayload = {
            ...basePayload,
            identify: { checked: 'male' },
            race: { checked: 'asian' },
            language: { checked_list: ['en'] },
        };
        const paymentPayload = { insurance: false };
        const officeHoursPayload = { appointmentMethod: IN_PERSON };
        
        stubAllProfileInfo();
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PROFILE } })).resolves({ payloadInfo: profilePayload });
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: PAYMENT_FORMS } })).resolves({ payloadInfo: paymentPayload });
        userRegFindOneStub.withArgs(sinon.match({ where: { pageName: NORMAL_OFFICE_HOURS } })).resolves({ payloadInfo: officeHoursPayload });

        const result = await TherapistRepo.getTherapistDetails(1, 2);

        expect(result).to.exist;
        expect(result.gender).to.equal('male');
        expect(result.race).to.equal('asian');
        expect(result.language).to.deep.equal(['en']);
        expect(result.paymentForms.insurance).to.be.false;
        expect(result.teleHealthFlag).to.be.false;
        expect(result.inPersonFlag).to.be.true;
    });
  });

  describe('acceptTherapist', function () {
    let findOneStub: sinon.SinonStub, updateStub: sinon.SinonStub, sendMailStub: sinon.SinonStub;
    beforeEach(() => {
      findOneStub = sandbox.stub(User, 'findOne');
      updateStub = sandbox.stub().resolves();
      sendMailStub = sandbox.stub(require('@/src/configs/sendgrid.config').mail, 'sendMail').resolves();
      sandbox.stub(require('@/src/application/repositories/therapist-waitlist.repository'), 'removePatientsFromTherapistWaitlist').resolves();
      sandbox.stub(require('@/src/application/emails/accept.email'), 'AcceptUserEmail').value({ compile: () => ({ To: 'to', Subject: 'sub', HtmlBody: 'body' }) });
    });
    it('should throw NotFoundError if not found', async function () {
      findOneStub.resolves(null);
      req.params = { id: '2' };
      try {
        await TherapistRepo.acceptTherapist(req as Request);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });
    it('should handle mail sending failure', async function () {
      findOneStub.resolves({ update: updateStub, email: 'a', toJSON: () => ({}) });
      sendMailStub.rejects(new Error('fail'));
      req.params = { id: '2' };
      req.body = { reason: 'reason' };
      try {
        await TherapistRepo.acceptTherapist(req as Request);
        throw new Error('Should have thrown');
      } catch (err) {
        expect((err as any).message).to.exist;
      }
    });
  });

  describe('rejectTherapist', function () {
    let findOneStub: sinon.SinonStub, updateStub: sinon.SinonStub, sendMailStub: sinon.SinonStub, createStub: sinon.SinonStub, removePatientsStub: sinon.SinonStub;

    beforeEach(() => {
      findOneStub = sandbox.stub(User, 'findOne');
      updateStub = sandbox.stub().resolves();
      sendMailStub = sandbox.stub(require('@/src/configs/sendgrid.config').mail, 'sendMail').resolves();
      createStub = sandbox.stub(ProfileRejectionReason, 'create').resolves();
      sandbox.stub(require('@/src/application/emails/reject.email'), 'RejectUserEmail').value({ compile: () => ({ To: 'to', From: 'from', Subject: 'sub', HtmlBody: 'body' }) });
      removePatientsStub = sandbox.stub(require('@/src/application/repositories/therapist-waitlist.repository'), 'removePatientsFromTherapistWaitlist').resolves(5);
    });

    it('should throw NotFoundError if not found', async function () {
      findOneStub.resolves(null);
      req.params = { id: '2' };
      try {
        await TherapistRepo.rejectTherapist(req as Request);
        throw new Error('Should have thrown');
      } catch (err) {
        expect(err as any).to.be.instanceOf(NotFoundError);
      }
    });

    it('should handle mail sending failure', async function () {
      findOneStub.resolves({ update: updateStub, email: 'a', toJSON: () => ({}), id: 2, role: 'therapist' });
      sendMailStub.rejects(new Error('fail'));
      req.params = { id: '2' };
      req.body = { reason: 'reason' };
      try {
        await TherapistRepo.rejectTherapist(req as Request);
        throw new Error('Should have thrown');
      } catch (err) {
        expect((err as any).message).to.exist;
      }
    });
  });

  // Add similar describe blocks for all other exported functions in therapist.repository.ts
  // For each, cover success, error, and edge cases, mocking all dependencies
});
