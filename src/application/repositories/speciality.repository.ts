import { Speciality } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Order,
	WhereOptions,
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'

type PatientFilter = {
	req: Request
}

export const getSpecialities = async ({ req }: PatientFilter) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = [['name', 'ASC']]
	const relations: any = []
	const attributes: FindAttributeOptions = []

	return Speciality.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req),
	})
}
