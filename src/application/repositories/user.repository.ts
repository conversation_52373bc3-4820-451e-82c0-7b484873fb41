import { User, UserRegistrationInfo, Calendar,UserDeleteDeactivateReason,ProfileDeleteDeactivateReason} from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Op,
	Order,
	WhereOptions,Sequelize
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'
import { UserType } from '../helpers/constant.helper'
import { NotFoundError } from '../handlers/errors'
import dayjs from 'dayjs'
import sequelize from '@/src/configs/database.config'
import { USER_DELETE } from '../controllers/web/constants';
import logger from '@/src/configs/logger.config'
import { removePatientsFromTherapistWaitlist } from './therapist-waitlist.repository'
import { AdminDeactivateEmail } from '@/src/application/emails/admin-deactivate-therapist.email'
import { mail } from '@/src/configs/sendgrid.config'
import { AdminDeleteEmail } from '../emails/admin-delete-therapist.email'
type UserFilter = {
	req: Request
	perPage?: number
}

export const getUsers = async ({ req, perPage }: UserFilter) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = [['firstname', 'ASC']]
	const relations: any = []
	const attributes: FindAttributeOptions = []

	where = {
		...where,
		role: UserType.SYSTEM,
		id: {
			[Op.ne]: req.user?.id,
		},
	}

	if (req.query.search) {
		where = {
			...where,
			[Op.or]: [
				{
					firstname: {
						[Op.iLike]: `%${req.query.search}%`,
					},
				},
				{
					lastname: {
						[Op.iLike]: `%${req.query.search}%`,
					},
				},
			],
		}
	}

	order.push(['createdAt', 'DESC'])

	return User.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req, perPage),
	})
}

export const getUserById = async (id: string) => {
	const user = await User.findByPk(id, {
		include: ['calendars'],
	})
	if (!user) throw new NotFoundError('User not found')
	return user
}

export const getUserWithCalendarInfo = async (id: string) => {
	const user = await User.findOne({
		attributes: {
			include: [
				[
					sequelize.literal(`
						EXISTS (
							SELECT 1
							FROM "calendars"
							WHERE "calendars"."userId" = "User"."id"
								AND "calendars"."type" = 'google'
						)
					`),
					'hasGoogleCalendar',
				],
				[
					sequelize.literal(`
						EXISTS (
							SELECT 1
							FROM "calendars"
							WHERE "calendars"."userId" = "User"."id"
								AND "calendars"."type" = 'outlook'
						)
					`),
					'hasOutlookCalendar',
				],
			],
		},
		include: [
			{
				model: Calendar,
				attributes: ['userId', 'email', 'type'],
			},
		],
		where: {
			id,
		},
	});
	if (!user) throw new NotFoundError('User not found')
	return user;
}

export const activateUser = async (id: string) => {
	const user = await User.findByPk(id)
	if (!user) throw new NotFoundError('User not found')
	await user.update({
		deactivatedAt: null,
		version:1,
		active:true,
	})	
}

export const deactivateUser = async (id: string, req:any) => {
	const {reasonId} = req.query;
	const reason = req.query.reason;
	const user = await User.findByPk(id);
	if (!user) throw new NotFoundError('User not found');
	if(!reasonId) throw new NotFoundError('Reason not found');
	await user.update({
		deactivatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
	})

	const email = user.email;
	if (user.role === 'therapist') {
	const removedCount = await removePatientsFromTherapistWaitlist(user.id);
	logger.info(`Successfully removed ${removedCount} patient(s) from the waitlist of therapist ID: ${user.id} during account deactivation.`);
	}
	const existingReason = await UserDeleteDeactivateReason.findOne({ where: { userId: id } });
	  const emailData = AdminDeactivateEmail.compile({
    	  email,
		  user: user.toJSON(),
		  reason
		});

		const mailData = {
		to: emailData.To , 
		subject: emailData.Subject,
		html: emailData. HtmlBody || `<p>Click <a href="${emailData. HtmlBody}">here</a> to deactivate your account.</p>`,
		};

		try {
			await mail.sendMail(mailData, true);
		} catch (error) {
		logger.error(`Failed to send deactivation email. Error: ${error}`);
		throw new Error('Failed to send email');
		}
    
        await UserDeleteDeactivateReason.create({
            reasonId,
            userId: id,
			type:'deactivated'
        });
    
}

export const deleteUser = async (id: string,currentDate:string,reasonId:string,reason:string) => {
	const user = await User.findByPk(id);
	if (!user) throw new NotFoundError('User not found'); 	
	if(!reasonId) throw new NotFoundError('Reason not found');
  
	const { id: userId , role} = user.dataValues;
	const transaction = await sequelize.transaction();
  
	try {		
		if (role === UserType.THERAPIST) {
	  		// Find user with additional profile data
			const userWithInfo = await User.findOne({
				where: { id: userId },
				include: [
				{
					model: UserRegistrationInfo,
					as: 'registrationInfo',
					where: { pageName: 'profile-info' },
					attributes: {
					exclude: ['payloadInfo'],
					include: [
						[
						Sequelize.literal(`(
							SELECT jsonb_strip_nulls(
							jsonb_build_object(
								'user_profile', "payloadInfo"->>'user_profile'
							)
							)
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "registrationInfo"."userId"
							AND "user_registration_informations"."pageName" = 'profile-info'
							LIMIT 1
						)`),
						'payload_info',
						],
					],
					},
					required: false,
				},
				],
				transaction,
			});
		
			if (!userWithInfo) {
				throw new NotFoundError('User not found');
			}
		} 	

	const existingReason = await UserDeleteDeactivateReason.findOne({ where: { userId: id } });
		await UserDeleteDeactivateReason.create({
			reasonId,
			userId: id,
			type: 'deleted',
		});
		
		await User.update({ active: false, version: 0,deletedAt: currentDate,
            emailVerifiedAt: null ,deletedBy:"admin" }, { where: { id: userId }, transaction });   

		if (user.role === 'therapist') {
			const removedCount = await removePatientsFromTherapistWaitlist(user.id);
			logger.info(`Successfully removed ${removedCount} patient(s) from the waitlist of therapist ID: ${user.id} by admin.`);
		}
	  await transaction.commit();	  
  
	  const email = user.email;
	  const emailData = AdminDeleteEmail.compile({
		email,
		user: user.toJSON(),
		reason
	  });

	  const mailData = {
	  to: emailData.To , 
	  subject: emailData.Subject,
	  html: emailData. HtmlBody || `<p>Click <a href="${emailData. HtmlBody}">here</a> to deactivate your account.</p>`,
	  };

	  try {
		  await mail.sendMail(mailData, true);
	  } catch (error) {
	  logger.error(`Failed to send deactivation email. Error: ${error}`);
	  throw new Error('Failed to send email');
	  }
	  return {
		message: USER_DELETE,
		user: {
		  id: user.id,
		  email: user.email,
		  firstname: user.firstname,
		  lastname: user.lastname,
		  active: false,
		  profile: user.profile,
		},
	  };
	} catch (error) {
	  await transaction.rollback();	 	 
	  throw error;
	} 
}	
