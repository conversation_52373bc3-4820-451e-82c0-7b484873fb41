import { expect } from 'chai';
import * as sinon from 'sinon';

import {
  getAnswers,
  createAnswer,
  getAnswerById,
  updateAnswer,
  updateAllSlug,
  deleteAnswer,
} from '../repositories/answer.repository';

import { Answer, AnswerFollowThrough, PageAnswer } from '@/src/models';
import { NotFoundError } from '../handlers/errors';
import * as paginationHelper from '../helpers/pagination.helper';
import * as generalHelper from '../helpers/general.helper';

describe('Answer Controller', () => {
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    sandbox.stub(paginationHelper, 'paginated').returns({ offset: 0, limit: 10 });
    sandbox.stub(generalHelper, 'generateSlug').returns('mock-slug');
  });

  afterEach(() => {
    sandbox.restore();
  });

  const mockRequest = (query = {}, body = {}, params = {}) =>
    ({ query, body, params } as any);

  describe('getAnswers', () => {
    it('should return paginated answers with search and pageId', async () => {
      const req = mockRequest({ search: 'test', pageId: '1,2' });
      const findStub = sandbox.stub(Answer, 'findAndCountAll').resolves({ rows: [], count: [] });

      const result = await getAnswers({ req, perPage: 10 });

      expect(findStub.calledOnce).to.be.true;
      expect(result).to.deep.equal({ rows: [], count: []});
    });

    it('should return followThrough answers when followThrough=true', async () => {
      const req = mockRequest({ pageId: '1', followThrough: 'true' });

      const pageAnswersMock = [{ answerId: 5 }];
      const resultMock = { rows: [], count: [] };

      sandbox.stub(PageAnswer, 'findAll').resolves(pageAnswersMock as any);
      const followThroughStub = sandbox
        .stub(AnswerFollowThrough, 'findAndCountAll')
        .resolves(resultMock);

      const result = await getAnswers({ req });

      expect(followThroughStub.calledOnce).to.be.true;
      expect(result).to.equal(resultMock);
    });
  });

	describe('createAnswer', () => {
  let createStub: sinon.SinonStub;
  let slugStub: sinon.SinonStub;

  it('should create a new answer', async () => {
    // Reuse sandbox
    const req = mockRequest({}, { answer: 'Test', info: 'Info' });

    slugStub = generalHelper.generateSlug as sinon.SinonStub;
    createStub = sandbox.stub(Answer, 'create').resolves({ id: 1 } as any);

    const result = await createAnswer(req);

    expect(slugStub.calledWith('Test')).to.be.true;
    expect(createStub.calledWith({
      answer: 'Test',
      info: 'Info',
      slug: 'mock-slug',
    })).to.be.true;
    expect(result).to.deep.equal({ id: 1 });
  });
});


  describe('getAnswerById', () => {
    it('should return answer when found', async () => {
      const mockAnswer = { id: 123 };
      const stub = sandbox.stub(Answer, 'findByPk').resolves(mockAnswer as any);

      const result = await getAnswerById(123);

      expect(stub.calledWith(123)).to.be.true;
      expect(result).to.equal(mockAnswer);
    });

    it('should throw NotFoundError if not found', async () => {
      sandbox.stub(Answer, 'findByPk').resolves(null);

      try {
        await getAnswerById(999);
      } catch (err) {
		if (err instanceof Error) {
			expect(err.message).to.equal('Answer not found');
		} else {
			throw err;
		}
		}
    });
  });

  describe('updateAnswer', () => {
    it('should update the answer', async () => {
      const req = mockRequest({}, { answer: 'New Answer', info: 'New Info' }, { id: 10 });

      const updateStub = sandbox.stub().resolves({ updated: true });
      sandbox.stub(Answer, 'findByPk').resolves({ update: updateStub } as any);

      const result = await updateAnswer(req);

      expect(updateStub.calledWith({
        answer: 'New Answer',
        info: 'New Info',
        slug: 'mock-slug',
      })).to.be.true;

      expect(result).to.deep.equal({ updated: true });
    });
  });

  describe('updateAllSlug', () => {
    it('should update slug for all answers', async () => {
      const answersMock = [
        { answer: 'One', update: sandbox.stub().resolves() },
        { answer: 'Two', update: sandbox.stub().resolves() },
      ];

      sandbox.stub(Answer, 'findAll').resolves(answersMock as any);

      await updateAllSlug();

      expect(answersMock[0].update.calledWith({ slug: 'mock-slug' })).to.be.true;
      expect(answersMock[1].update.calledWith({ slug: 'mock-slug' })).to.be.true;
    });
  });

  describe('deleteAnswer', () => {
    it('should delete the answer by ID', async () => {
      const destroyStub = sandbox.stub().resolves(true);
      sandbox.stub(Answer, 'findByPk').resolves({ destroy: destroyStub } as any);

      const result = await deleteAnswer(5);

      expect(destroyStub.calledOnce).to.be.true;
      expect(result).to.be.true;
    });
  });
});
