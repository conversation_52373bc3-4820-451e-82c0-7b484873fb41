import { Request,Response } from 'express'
import { <PERSON>, Page, PatientProfile, StripeInfo, User, UserQuestionnaire, UserSettings, UserValues,MinorPatient } from '@/src/models'
import {
	BadRequestError,
	ConflictError,
	NotFoundError,
	UnprocessableEntityError,
	ForbiddenError,
  TooManyRequestsError
} from '@/src/application/handlers/errors'
import {
	decryptedB64,
	encryptedB64,
} from '@/src/application/helpers/encryption.helper'
import dayjs from 'dayjs'
import { PasswordResetEmail } from '@/src/application/emails/password.email'
import { VerificationEmail } from '@/src/application/emails/verify.email';
import { AcceptPatientEmail } from '@/src/application/emails/accept-patient.email'
import { mail } from '@/src/configs/sendgrid.config'
import bcrypt from 'bcryptjs'
import sequelize from '@/src/configs/database.config'
import { UserType } from '../helpers/constant.helper'
import { WhereOptions, Op } from 'sequelize'
import { FailedLogin<PERSON>lertEmail, MfaVerificationEmail, RegisterPatientEmail } from '../emails/register.email'
import { EmailResetEmail } from '../emails/email-reset.email'
import { sendSms } from '@/src/configs/twilio.config'
import {DOB_VALIDATION,ALL_FIELDS_REQUIRED,INVALID_EMAIL_FORMAT,PASSWORD_VALIDATION,PASSWORDS_DO_NOT_MATCH,USER_UNDER_18,USER_ALREADY_EXISTS, INVALID_LINK, USER_NOT_FOUND, FAILED_LOGIN_ATTEMPTS, USER_NOT_ACTIVE, PATIENT_LOGIN_RESTRICTION_MESSAGE,EMAIL_NOT_VERIFIED_MESSAGE,INVALID_PASSWORD_MESSAGE,INVALID_CREDENTIALS_MESSAGE, MFA_CODE_SENT_MESSAGE, MFA_TOO_MANY_ATTEMPTS_MESSAGE, MFA_INVALID_CODE_MESSAGE, MFA_CODE_EXPIRED_MESSAGE, MFA_CODE_RESENT_MESSAGE} from './constants'
import logger from '@/src/configs/logger.config'
import redisClient from '@/src/configs/redis.config'
import FailedLoginAttempt from '@/src/models/failed-login-attempts.model'
import { maskPhoneNumber } from '../helpers/general.helper'
import { getUserWithCalendarInfo } from './user.repository'
import { hashData } from '@/src/cryptoUtil'
import { PATIENT,ACCOUNT_DELETED,ACCOUNT_DELETED_ADMIN } from './constants'

const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const passwordRegex = /^(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d])[A-Za-z\d\S]{8,}$/;
const MAX_ATTEMPTS = 5;
const LOCKOUT_TIME = 15 * 60;

//check if the user has been locked out due to failed login attempts
async function checkLockout(userid: number): Promise<boolean> {
  const attempts = await redisClient.get(`login_attempts:${userid}`);
  if (!attempts) return false;
  return parseInt(attempts) >= MAX_ATTEMPTS;
}

async function checkMfaLockout(email: string): Promise<boolean> {
  const attempts = await redisClient.get(`mfa_attempts:${email}`);
  if (!attempts) return false;
  return parseInt(attempts) >= MAX_ATTEMPTS;
}


async function incrementFailedLogin(userid:number | undefined, reason: string, ipAddress: string,type:string) {
  const attempts = await redisClient.incr(`login_attempts:${userid}`);
  if (attempts === 1) {
    await redisClient.expire(`login_attempts:${userid}`, LOCKOUT_TIME);
  }
  await FailedLoginAttempt.create({ userId : userid || undefined, reason, ipAddress: ipAddress, type: type });
  logger.error(`Failed ${type} attempt for user ${userid}. Reason: ${reason}.`);

  if (attempts === MAX_ATTEMPTS) {
    const user = await User.findOne({ where: { id:userid } });
    if (!user) {
      throw new Error(USER_NOT_FOUND);
  }
    const email = user?.email ?? '';

    logger.error(`User ${user.id} locked out due to multiple failed ${type} attempts.`);

    const emailData = FailedLoginAlertEmail.compile({
      email,
      user: user.toJSON(),
    });
    
    const mailData = {
      to: emailData.To,
      subject: emailData.Subject || 'Security Alert: Multiple Failed Login Attempts',
      html: emailData.HtmlBody,
    };


    
    await mail.sendMail(mailData);

 }
}

async function resetFailedLogin(userid: number) {
  await redisClient.del(`login_attempts:${userid}`);
}

export const loginWithoutMfa = async (req: Request, device: string|null = null) => {
  const ip = req.headers['x-forwarded-for'] 
    ? req.headers['x-forwarded-for'].toString().split(',')[0].trim() 
    : req.ip;
  const { password } = req.body
  const hash_email =  hashData(req.body.email);

  const user = await User.findOne({
    attributes: {
      include: [
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1
              FROM "calendars"
              WHERE "calendars"."userId" = "User"."id"
                AND "calendars"."type" = 'google'
            )
          `),
          'hasGoogleCalendar',
        ],
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1
              FROM "calendars"
              WHERE "calendars"."userId" = "User"."id"
                AND "calendars"."type" = 'outlook'
            )
          `),
          'hasOutlookCalendar',
        ],
      ],
    },
    include: [
      {
        model: Calendar,
        attributes: ['userId', 'email', 'type'],
      },
      {
        model: StripeInfo,
        attributes: ['status'],
      }
    ],
    where: {
      email_hash:hash_email,
    },
  })

  if (!user) throw new ForbiddenError(`User not found`);
  if(user?.role === PATIENT){
    if(user?.version === 0 || user?.active ===false){   
    throw new NotFoundError(USER_NOT_FOUND);
  }
}
  if (await checkLockout(user.id)) {
    throw new ForbiddenError(FAILED_LOGIN_ATTEMPTS);
  }

  if (!user.comparePassword(password)) {
    await incrementFailedLogin(user.id, INVALID_PASSWORD_MESSAGE, ip, "login");
    throw new ConflictError(INVALID_CREDENTIALS_MESSAGE);
  }
  // if (/Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(req.headers['user-agent'] || '')) {
  //   if (user.role === UserType.PATIENT) throw new ForbiddenError(PATIENT_LOGIN_RESTRICTION_MESSAGE);
  // }

  if (user.role === UserType.THERAPIST) {
    if (user.isUnderReview) throw new ForbiddenError('Account under review!', { accountUnderReview: true });
    else if (user.acceptedAt === null && user.rejectedAt === null && user.deletedAt === null) {
      throw new ForbiddenError('Registration Incomplete', { accountIncomplete: true, userId: user.id })
    }
    else if(user.deletedAt!==null && user.deletedBy === 'admin'){
      throw new ForbiddenError(ACCOUNT_DELETED_ADMIN, { accountDeletedByAdmin: true, userId: user.id });
    }
    else if(user.deletedAt !== null) {
      throw new ForbiddenError(ACCOUNT_DELETED, { accountDeleted: true, userId: user.id });
    }
    else if (user.acceptedAt === null || user.rejectedAt !== null) {
      throw new ForbiddenError('Account rejected!', { accountRejected: true, userId: user.id });
    }
  
  }
  if (user.emailVerifiedAt === null) throw new ForbiddenError(EMAIL_NOT_VERIFIED_MESSAGE, { emailNotVerified: true ,userId: user.id});
  if (!user.active) {
    await user.update({ deletedAt: null ,
      version: 1, 
      active:true ,
       deletedBy: null});
  }
  
  
  const minorPatients = await MinorPatient.findAll({
    where: { userId: user.id },
  });

  const accessToken = user.generateToken(device ? 'api' : 'web');
  const refreshToken = encryptedB64(`${accessToken}:${user.id}`);

  const response = {
    accessToken,
    refreshToken,
    user: {
      ...user.toJSON(),
      minorPatients: minorPatients || [],
    },
  };

  return response;
};

export const sendMfaCode = async (req: Request) => {
  const ip = req.headers['x-forwarded-for'] 
    ? req.headers['x-forwarded-for'].toString().split(',')[0].trim() 
    : req.ip;

  const { email, password } = req.body;
  const hash_email =  hashData(req.body.email);

  const user = await User.findOne({
    attributes: {
      include: [
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1
              FROM "calendars"
              WHERE "calendars"."userId" = "User"."id"
                AND "calendars"."type" = 'google'
            )
          `),
          'hasGoogleCalendar',
        ],
        [
          sequelize.literal(`
            EXISTS (
              SELECT 1
              FROM "calendars"
              WHERE "calendars"."userId" = "User"."id"
                AND "calendars"."type" = 'outlook'
            )
          `),
          'hasOutlookCalendar',
        ],
      ],
    },
    include: [
      {
        model: Calendar,
        attributes: ['userId', 'email', 'type'],
      },
      {
        model: StripeInfo,
        attributes: ['status'],
      }
    ],
    where: {
      email_hash:hash_email,
    },
  });
  
  
  if (!user) {
    throw new ConflictError(USER_NOT_FOUND);
  }
  if(user?.role === PATIENT){
    if(user?.version === 0 || user?.active ===false){   
    throw new NotFoundError(USER_NOT_FOUND);
  }
}
  if (!user.comparePassword(password)) {
    await incrementFailedLogin(user.id, INVALID_PASSWORD_MESSAGE, ip, "login");
    throw new ConflictError(INVALID_CREDENTIALS_MESSAGE);
  }
  if (user.role === UserType.THERAPIST) {
    if (user.isUnderReview) throw new ForbiddenError('Account under review!', { accountUnderReview: true });
    else if (user.acceptedAt === null && user.rejectedAt === null && user.deletedAt === null) {
      throw new ForbiddenError('Registration Incomplete', { accountIncomplete: true, userId: user.id })
    }
    else if (user.acceptedAt === null || user.rejectedAt !== null) {
      throw new ForbiddenError('Account rejected!', { accountRejected: true, userId: user.id });
    }
    else if(user.deletedAt!==null && user.deletedBy === 'admin'){
      throw new ForbiddenError(ACCOUNT_DELETED_ADMIN, { accountDeletedByAdmin: true, userId: user.id });
    }
    else if(user.deletedAt !== null) {
      throw new ForbiddenError(ACCOUNT_DELETED, { accountDeleted: true, userId: user.id });
    } 
  }

  if (await checkLockout(user.id)) {
    throw new TooManyRequestsError(FAILED_LOGIN_ATTEMPTS);
  }
  if (!user.active) {
    await user.update({ deletedAt: null ,
      version: 1 , 
      active:true ,
       deletedBy: null});
  }

  // if (/Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(req.headers['user-agent'] || '')) {
  //   if (user.role === UserType.PATIENT) throw new ForbiddenError(PATIENT_LOGIN_RESTRICTION_MESSAGE);
  // }



  if (user.emailVerifiedAt === null) throw new ForbiddenError(EMAIL_NOT_VERIFIED_MESSAGE, { emailNotVerified: true , userId: user.id});

  await resetFailedLogin(user.id);

  const mfaCode = Math.floor(100000 + Math.random() * 900000);
  const expiration = new Date(Date.now() + 5 * 60000)

  const hasedMfaCode  = await bcrypt.hash(mfaCode.toString(), 10);

  await user.update({
    mfaCode: hasedMfaCode,
    mfaExpiresAt: expiration,
  });

  const emailData = MfaVerificationEmail.compile({
    email,
    code: mfaCode.toString(),
    user: user.toJSON(),
  });
  
  const mailData = {
    to: emailData.To,
    subject: emailData.Subject || 'MFA Code',
    html: emailData.HtmlBody,
  };
  
  await mail.sendMail(mailData, true);

  const response = {
    message: MFA_CODE_SENT_MESSAGE,
    user
  };

  return response;
};

export const verifyMFA = async (req: Request,  device:string|null = null) => {
  const { code } = req.body;
  const hash_email =  hashData(req.body.email);
  const ip = req.headers['x-forwarded-for'] 
    ? req.headers['x-forwarded-for'].toString().split(',')[0].trim() 
    : req.ip;

  const user = await User.findOne({ where: { email_hash:hash_email, } });
  if (!user) throw new ConflictError(USER_NOT_FOUND);

  if (await checkLockout(user.id)) {
    throw new ForbiddenError(MFA_TOO_MANY_ATTEMPTS_MESSAGE);
  }

  // Check if the MFA code is expired
  if (!user.mfaCode || !user.mfaExpiresAt || new Date() > user.mfaExpiresAt) {
    await incrementFailedLogin(user.id, MFA_CODE_EXPIRED_MESSAGE, ip,"mfa");
    throw new ConflictError(MFA_CODE_EXPIRED_MESSAGE);
  }

  // Compare the provided code with the hashed code
  const isCodeValid = await bcrypt.compare(code, user.mfaCode);
  if (!isCodeValid) {
    await incrementFailedLogin(user.id, MFA_INVALID_CODE_MESSAGE, ip,"mfa");
    throw new ConflictError(MFA_INVALID_CODE_MESSAGE);
  }
  
  await user.update({
    mfaCode: null,
    mfaExpiresAt: null,
  });

  const minorPatients = await MinorPatient.findAll({
    where: { userId: user.id },
  });

  const accessToken = user.generateToken(device ? 'api' : 'web');
  const refreshToken = encryptedB64(`${accessToken}:${user.id}`);

  let userData = user;
  if (user.role === UserType.THERAPIST) {
    userData = await getUserWithCalendarInfo(user.id)
  }

  const response = {
    accessToken,
    refreshToken,
    user: {
      ...userData.toJSON(),
      minorPatients: minorPatients || [],
    },
  };

  return response;
};

export const resendMFA = async (req: Request) => {
  const { email } = req.body;
  const hash_email =  hashData(req.body.email);

  const user = await User.findOne({ where: {email_hash:hash_email, } });
  if (!user) throw new ConflictError(INVALID_CREDENTIALS_MESSAGE);

  const mfaCode = Math.floor(100000 + Math.random() * 900000);
  const expiration = new Date(Date.now() + 5 * 60000);

  const hashedMfaCode = await bcrypt.hash(mfaCode.toString(), 10);

  await user.update({
    mfaCode: hashedMfaCode,
    mfaExpiresAt: expiration,
  });

  const emailData = MfaVerificationEmail.compile({
    email,
    code: mfaCode.toString(),
    user: user.toJSON(),
  });

  const mailData = {
    to: emailData.To,
    subject: emailData.Subject || "MFA Code",
    html: emailData.HtmlBody,
  };

  await mail.sendMail(mailData, true);
  logger.info(`MFA code resent to user ${user.id}.`);
  return { message: MFA_CODE_SENT_MESSAGE };
};

export const registerPatient = async (req: Request) => {
  const registrationObject = req.body;

  const { category } = req.query;

  if (!category) throw new BadRequestError('Category is required');
  const regPage = await Page.findOne({
    where: {
      category,
      type: 'register',
    }
  })

  if (!regPage) throw new NotFoundError('Registration Page not found');
  let patientInfo = registrationObject[regPage.id]?.answers;

  const answers = Object.values(registrationObject)
    .filter((obj: any) => obj.page?.type === 'questionnaire' || obj.page?.type === 'selectable')
    .flatMap((obj: any) => {
      const baseAnswer = { pageId: obj.page.id, questionnaireId: obj.page.questionnaireId };
      return Array.isArray(obj.answers)
        ? obj.answers.map((answer: any) => ({ ...baseAnswer, answerId: answer.id, ...(obj.page?.type === 'selectable' && { rank: answer.rank }) }))
        : { ...baseAnswer, answerId: obj.answers.id };
    });

  const costAnswer = Object.values(registrationObject)
    .map((obj: any) => {
      return obj.page.title.toLowerCase().includes('cost') || obj.page.title.toLowerCase().includes('costs')
        ? obj.answers?.value
        : null;
    })
    .find((value) => value !== null); // Get the first non-null value

  const telehealthAnswer = Object.values(registrationObject)
    .map((obj: any) => {
      return obj.page.title.toLowerCase().includes('telehealth') ? obj.answers?.value : null;
    })
    .find((value) => value !== null);

  const distanceAnswer = Object.values(registrationObject)
    .map((obj: any) => {
      return obj.page.title.toLowerCase().includes('travel') ? obj.answers?.value : null;
    })
    .find((value) => value !== null)

  const valuePageAnswers = Object.values(registrationObject)
    .filter((obj: any) => obj.page?.type === 'value')
    .flatMap((obj: any) => obj.answers);

  const {
    first_name: firstname,
    last_name: lastname,
    email,
    dob,
    password,
    gender,
    address,
  } = patientInfo;

  let condition: any = {}
  // if (phone) condition.phone = phone
  if (email) condition.email = email

  const where: WhereOptions = { [Op.or]: condition }

  let user = await User.findOne({
    where,
  })
  if (user) throw new UnprocessableEntityError(`User already exists`)
  
  const hashedPassword = await bcrypt.hash(password, 10);
  const hash_email =  hashData(req.body.email);
  
  const transaction = await sequelize.transaction()
  try {
    user = await User.create({
      firstname,
      lastname,
      email,
      email_hash: hash_email,
      password: hashedPassword,
      dob,
      role: UserType.PATIENT,
      gender,
      coordinate: {
        type: 'Point',
        coordinates: [address.lat, address.lng],
      },
      address,
    }, { transaction })

    await UserSettings.create({
      userId: user.id,
      notificationSettings: {
        sms: false,
        email: true,
        fcm: true
      }
    }, { transaction })

    await PatientProfile.create({
      userId: user.id,
      seekingTherapyFor: category,
      paymentPreference: costAnswer,
      telehealth: telehealthAnswer === 'yes',
      preferredDistance: distanceAnswer && !isNaN(Number(distanceAnswer)) ? distanceAnswer.split('-')[0] : null,
    }, { transaction })

    await UserQuestionnaire.bulkCreate(
      answers.map((ans) => {
        return {
          pageId: ans.pageId,
          answerId: ans.answerId,
          questionnaireId: ans.questionnaireId,
          userId: user?.id,
          points: ans?.rank || null,
        }
      }),
      { transaction }
    )

    await UserValues.bulkCreate(
      valuePageAnswers.flatMap((ans) => {
        return Object.entries(ans).map(([answerGroup, selectedValue]) => {
          return {
            answerGroup,
            selectedValue,
            userId: user?.id,
          };
        });
      }),
      { transaction }
    );
    
    await transaction.commit();
    logger.info(`USER REGISTRATION SUCCESS: User ID: ${user.id}, Email: ${email}`);
  } catch (e) {
    await transaction.rollback();    
    logger.error(`USER REGISTRATION ERROR: ${e}`);
    throw e;
  }

  const code = Math.floor(100000 + Math.random() * 900000)
  const expiration = dayjs().add(2, 'hours').unix()
  const token = encryptedB64(`${user.id}:${expiration}:${code}`)

  await user.update({
    passwordResetToken: token,
  })

  const emailData = RegisterPatientEmail.compile({
    email,
    code: code.toString(),
    url: `${process.env.FRONTEND_URL}/auth/verify-email?token=${token}&email=${email}`,
    user: user.toJSON(),
  });
  
  const mailData = {
    to: emailData.To,
    subject: emailData.Subject || 'Verify your email',
    html: emailData.HtmlBody,
  };
  
  await mail.sendMail(mailData, true);
  
}
/**
 * Register patient with minor
 * @param req 
 * @returns
 */
export const registerPatientMinor = async (req: Request, res: Response, device: string | null) => {
  const { firstname, lastname, dob, email, password, confirmPassword, is_minor, minor_details, address,timeZone,gender,therapy_for } = req.body;

  if (!firstname || !lastname || !dob || !email || !password || !confirmPassword) {
    throw new BadRequestError(ALL_FIELDS_REQUIRED);
  }

  if (!emailRegex.test(email)) {
    throw new BadRequestError(INVALID_EMAIL_FORMAT);
  }

  if (!passwordRegex.test(password)) {
    throw new BadRequestError(PASSWORD_VALIDATION);
  }

  if (password !== confirmPassword) {
    throw new BadRequestError(PASSWORDS_DO_NOT_MATCH);
  }

  const userAge = dayjs().diff(dayjs(dob), 'year');
  if (userAge < 18 ) {
    throw new BadRequestError(USER_UNDER_18);
  }
  const hash_email =  hashData(req.body.email);

  const existingUser = await User.findOne({ where: {  email_hash:hash_email } });
  if (existingUser) {
    throw new UnprocessableEntityError(USER_ALREADY_EXISTS);
  }

  const hashedPassword = await bcrypt.hash(password, 10);
  const transaction = await sequelize.transaction();
  try {   
    logger.info(`Creating user: ${email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
    const user = await User.create({
      firstname,
      lastname,
      email,
      gender,
      therapy_for,
      password: hashedPassword,
      email_hash:hash_email,
      dob,
      role: UserType.PATIENT,
      address: address || null,
      coordinate: address ? {
        type: 'Point',
        coordinates: [address.lat, address.lng],
      } : null,
      timeZone
    }, { transaction });

    logger.info(`User created successfully: ${user.id}`);

    let minorPatients = [];
    if (is_minor && minor_details && Array.isArray(minor_details)) {
      for (const minor of minor_details) {
        const { firstName: firstName, lastName: lastName, dob: minorDob } = minor;
        const minorAge = dayjs().diff(dayjs(minorDob), 'year');
        if (minorAge >= 18) {
          throw new BadRequestError(DOB_VALIDATION);
        }
        logger.info(`Adding minor: ${firstName} ${lastName} (DOB: ${minorDob}) under user: ${user.id}`);
       
        const minorPatient = await MinorPatient.create({
          userId: user.id,
          firstName: firstName,
          lastName: lastName,
          dob: minorDob,
        }, { transaction });
        minorPatients.push(minorPatient);
      }
    }
    
    logger.info(`Registration successful for user: ${user.id}`);
    const code = Math.floor(100000 + Math.random() * 900000);
    const expiration = dayjs().add(2, 'hours').unix();
    const token = encryptedB64(`${user.id}:${expiration}:${code}`);

    await user.update({ passwordResetToken: token }, { transaction, validate: true });

    const emailData = RegisterPatientEmail.compile({
      email,
      code: code.toString(),
      url: `${process.env.FRONTEND_URL}/auth/verify-email?token=${token}&email=${email}`,
      user: user.toJSON(),
    });

    const mailData = {
      to: emailData.To,
      subject: emailData.Subject || 'Verify your email',
      html: emailData.HtmlBody,
    };
    
    await mail.sendMail(mailData, true);
    await transaction.commit();
    return({
      message: 'Registration successful',
        user,
        minorPatients,
		})
  } catch (error) {
    await transaction.rollback();
    logger.error(`Registration failed for user. Error: ${error instanceof Error ? error.message : error}`);
    throw error;
  }
};

export const forgotPassword = async (req: Request) => {
  const { email } = req.body
  const hash_email =  hashData(req.body.email);
  const user = await User.findOne({
    where: {
      email_hash:hash_email,
    },
  })

  if (!user) throw new NotFoundError('User not found')
  logger.info(`Password reset requested for user: ${user.id}`);
  const expiration = dayjs().add(2, 'hours').unix()
  const token = encryptedB64(`${user.id}:${expiration}`)

  await user.update({
    passwordResetToken: token,
  })

  const emailData = PasswordResetEmail.compile({
    email,
    user: user.toJSON(),
    url: `${process.env.FRONTEND_URL}/auth/reset-password?token=${token}`,
});

const mailData = {
  
  to: emailData.To , 
  subject: emailData.Subject || 'Password Reset Request',
  html: emailData. HtmlBody || `<p>Click <a href="${emailData. HtmlBody}">here</a> to reset your password.</p>`,
};

try {
    await mail.sendMail(mailData, true);
} catch (error) {
  logger.error(`Failed to send password reset email. Error: ${error}`);
  throw new Error('Failed to send email');
}

  logger.info(`Forgot password verification link sent to user ${user.id}`);

  return ({
    message: 'Magic link sent to your email.',
  })
}

export const resetPassword = async (req: Request) => {
  const { token, password, confirmPassword } = req.body
  logger.info(`Password reset requested with token: ${token}`);

  const [id, expiration] = decryptedB64(token).split(':')

  if (dayjs().unix() > Number(expiration)) {
    throw new BadRequestError('Token expired')
  }

  const user = await User.findOne({
    where: {
      passwordResetToken: token,
      id,
    },
    attributes: ['id', 'passwordResetToken', 'role','email'],
  })
  
  if (!user) throw new NotFoundError('This link has already been used, please generate new link')
    await user.update({ version: 0 });

  if (password !== confirmPassword) {
    throw new BadRequestError('Password does not match')
  }

  await user.update({
    password: bcrypt.hashSync(password, 10),
    passwordResetToken: null,
    passwordUpdatedAt: dayjs().toDate(),
  })
  const role = user.role;

  await resetFailedLogin(user.id);
  
  logger.info(`Password reset successfully for user ID: ${user.id}`);
  return ({
    message: 'Password reset successfully.',
    role, 
  });
}

export const changePassword = async (req: Request) => {
  const { old_password, new_password, confirm_password, email } = req.body;
  logger.info(`Password changed requested for email: ${email}`);
  const hash_email =  hashData(req.body.email);
  const user = await User.findOne({
    where: {
      id: req.user?.id,
      email_hash:hash_email,
    },
  })

  if (!user) throw new NotFoundError('User not found')

  if (!user.comparePassword(old_password)) throw new ForbiddenError('Old password is incorrect')

  if (new_password !== confirm_password) throw new ForbiddenError('Passwords do not match')

  await user.update({
    password: bcrypt.hashSync(new_password, 10),
    passwordResetToken: null,
    passwordUpdatedAt: dayjs().toDate(),
  })
  logger.info(`Password changed successfully for user ID: ${user.id}`);

  const userData = await getUserWithCalendarInfo(user.id)
  return ({
    message: 'Password changed successfully.',
    user: userData,
  })
}

export const emailResetToken = async (req: Request) => {
  const { old_email, new_email } = req.body
  const hash_email_old =  hashData(req.body.old_email);
  if (!old_email || !new_email) throw new BadRequestError('Required data are missing')

  const user = await User.findOne({
    where: {
      email_hash:hash_email_old,
      id: req.user?.id,
    },
  })

  const hash_email_new =  hashData(req.body.new_email);
  if (!user) throw new NotFoundError('User not found')

  const existingEmail = await User.findOne({
    where: {
      email_hash: hash_email_new,
    },
  })
  if (existingEmail) throw new ForbiddenError('The provided email is already in use')

  const code = Math.floor(100000 + Math.random() * 900000)
  const expiration = dayjs().add(2, 'hours').unix()
  const token = encryptedB64(`${user.id}:${expiration}:${code}`)

  await user.update({
    passwordResetToken: token,
  })

  const emailData = EmailResetEmail.compile({
    email: new_email,
    user: user.toJSON(),
    code: code.toString(),
  })

  const mailData = {
    to: emailData.To,                  
    subject: emailData.Subject || 'Reset Your Password',
    html: emailData.HtmlBody,
  }

  await mail.sendMail(mailData, true);

  return ({
    message: 'Email Reset Token sent to your email.',
  })
}

export const phoneResetToken = async (req: Request) => {
  const { new_phone } = req.body
  const hash_phone =  hashData(req.body.new_phone);

  const user = await User.findOne({
    where: {
      id: req.user?.id,
    },
  })
  
  if (!user) throw new NotFoundError('User not found')
  logger.info(`Validating the availability of the phone number: ${maskPhoneNumber(new_phone)}`);
  const existingUserWithPhone = await User.findOne({
    where: {
      phone_hash:hash_phone,
    },
  })
  if (existingUserWithPhone) {
    if (existingUserWithPhone.id !== user.id) {
      logger.info(`Phone number ${maskPhoneNumber(new_phone)} is already in use by another user`);
      throw new ForbiddenError('The provided phone number is already in use')
    }
  }
  logger.info(`Phone number ${maskPhoneNumber(new_phone)} is available. Sending verification code...`);
  const code = Math.floor(100000 + Math.random() * 900000)
  const expiration = dayjs().add(2, 'hours').unix()
  const token = encryptedB64(`${user.id}:${expiration}:${code}`)

  await user.update({
    passwordResetToken: token,
  })

  await sendSms(new_phone, `NextTherapist: Your verification code is ${code}`)
  logger.info(`Verification code sent to phone number ${maskPhoneNumber(new_phone)}`);
  return ({
    message: 'Verification code sent to your phone.',
  })
}

export const emailVerification = async (req: Request) => {
  const { email, noLink } = req.body; 
  const hash_email =  hashData(req.body.email);
  logger.info(`Email verification requested for: ${email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
  const user = await User.findOne({
    where: {
      email_hash:hash_email,
    },
  })

  if (!user) throw new NotFoundError('User not found')

  logger.info(`Email verification requested for: ${user.id}`);
  const code = Math.floor(100000 + Math.random() * 900000)

  const expiration = dayjs().add(2, 'hours').unix()
  const token = encryptedB64(`${user.id}:${expiration}:${code}`)
  
  await user.update({
    passwordResetToken: token,
  })

  const emailData = VerificationEmail.compile({
    email,
    code: code.toString(),
    ...(noLink ? {} : {
      url: `${process.env.FRONTEND_URL}/auth/verify-email?token=${token}&email=${email}`,
    }),
    user: user.toJSON(),
  });
  
  const mailData = {
    to: emailData.To,
    subject: emailData.Subject || 'Verify Your Email Address',
    html: emailData.HtmlBody,
  }
  
  await mail.sendMail(mailData, true);

  logger.info(`Email verification link sent to user ${user.id}`);

  return ({
    message: 'Magic link sent to your email.',
  })
}

export const verifyEmail = async (req: Request) => {
  const { token, code: receivedCode, email } = req.body

  let encryptedToken;
  let user;
  const email_hash =  hashData(email);

  if (token) {
    encryptedToken = token;
    user = await User.findOne({
      where: {
        passwordResetToken: token,
        email_hash,
      },
      attributes: ['id', 'passwordResetToken', 'role', 'email', 'firstname'],
    })
  } else {
    user = await User.findOne({
      where: {
        email_hash,
      },
      attributes: ['id', 'passwordResetToken', 'role', 'email', 'firstname'],
    })
    encryptedToken = user?.passwordResetToken;
  }

  if (!user || !encryptedToken) throw new NotFoundError(INVALID_LINK)

  const [id, expiration, code] = decryptedB64(encryptedToken).split(':')
  logger.info(`Email verification initiated by user ${id}`);

  if (dayjs().unix() > Number(expiration)) {
    throw new BadRequestError('Token expired')
  }

  if (receivedCode !== code) {
    throw new ConflictError('Code does not match')
  }

  await user.update({
    emailVerifiedAt: dayjs().toDate(),
    passwordResetToken: null,
    deletedAt: null,
  })
  const role = user.role;

  logger.info(`Email verified successfully by user ${user.id}`);
  if(user.role.toLowerCase() === PATIENT)
  {
    logger.info(user.role,"Patient role");
    const emailData = AcceptPatientEmail.compile({
      email: user.email,
      user: user.toJSON(),
      url: `${process.env.FRONTEND_URL}/auth/login`,
    })
    
    const mailData = {
      to: emailData.To,
      subject: emailData.Subject,
      html: emailData.HtmlBody,
    }
    
    await mail.sendMail(mailData)
  }

  return ({
    message: 'Email verified successfully.',
    role,
  })
}


export const verifyPatientEmail = async (req: Request, device: string | null = null) => {
    const { password } = req.body
    const hash_email =  hashData(req.body.email);
      const user = await User.findOne({
        attributes: {
          include: [
            [
              sequelize.literal(`
                EXISTS (
                  SELECT 1
                  FROM "calendars"
                  WHERE "calendars"."userId" = "User"."id"
                    AND "calendars"."type" = 'google'
                )
              `),
              'hasGoogleCalendar',
            ],
            [
              sequelize.literal(`
                EXISTS (
                  SELECT 1
                  FROM "calendars"
                  WHERE "calendars"."userId" = "User"."id"
                    AND "calendars"."type" = 'outlook'
                )
              `),
              'hasOutlookCalendar',
            ],
          ],
        },
        include: [
          {
            model: Calendar,
            attributes: ['userId', 'email', 'type'],
          },
          {
            model: StripeInfo,
            attributes: ['status'],
          }
        ],
        where: {
          email_hash:hash_email,
        },
      })
      if (!user) throw new NotFoundError(`User not found`);
      if (!user.active) {
        await user.update({ deletedAt: null });
      }
      // if (/Mozilla|Chrome|Safari|Firefox|Edge|Opera/i.test(req.headers['user-agent'] || '')) {
      //   if (user.role === UserType.PATIENT) throw new ForbiddenError(`Patients cannot login from the web! Please download the app.`);
      // }
  
    if (user.role === UserType.THERAPIST && user.acceptedAt === null) throw new ForbiddenError(`Account not activated!`);
    if (user.deletedAt !== null) throw new ForbiddenError('Account deleted!');
    if (user.emailVerifiedAt === null) throw new ForbiddenError(`Email not verified!`);
    if (!user.comparePassword(password)) throw new ConflictError(`Invalid Password!`);
  
    const minorPatients = await MinorPatient.findAll({
      where: { userId: user.id },
    });
  
    const accessToken = user.generateToken(device ? 'api' : 'web');
    const refreshToken = encryptedB64(`${accessToken}:${user.id}`);
  
    const response = {
      accessToken,
      refreshToken,
      user: {
        ...user.toJSON(),
        minorPatients: minorPatients || [],
      },
    };
  
    return response;
  };


export const resetEmail = async (req: Request) => {
  const { old_email, new_email, code: receivedCode } = req.body
  if (!old_email || !new_email || !receivedCode) throw new BadRequestError('Required data are missing')

    const hash_email_old =  hashData(req.body.old_email);
  const user = await User.findOne({
    where: {
      id: req.user?.id,
      email_hash: hash_email_old,
    },
  })

  if (!user) throw new NotFoundError('User not found')

  const [id, expiration, code] = decryptedB64(user.passwordResetToken).split(':')

  if (dayjs().unix() > Number(expiration)) {
    throw new BadRequestError('Token expired')
  }

  if (receivedCode !== code || id !== user.id.toString()) {
    throw new ConflictError('Code does not match')
  }

  const hash_email_new =  hashData(req.body.new_email);
  await user.update({
    email:req.body.new_email,
    email_hash: hash_email_new,
    emailVerifiedAt: dayjs().toDate(),
    passwordResetToken: null,
  })

  logger.info(`Email updated successfully by user ${user.id}`);
  const userData = await getUserWithCalendarInfo(user.id)
  return ({
    message: 'Email updated successfully.',
    user: userData,
  })
}

export const resetPhone = async (req: Request) => {
  const { new_phone, code: receivedCode } = req.body
  if (!new_phone || !receivedCode) throw new BadRequestError('Required data are missing')

  const user = await User.findOne({
    where: {
      id: req.user?.id,
    },
  })

  if (!user) throw new NotFoundError('User not found')

  const [id, expiration, code] = decryptedB64(user.passwordResetToken).split(':')

  if (dayjs().unix() > Number(expiration)) {
    throw new BadRequestError('Token expired')
  }

  if (receivedCode !== code || id !== user.id.toString()) {
    throw new ConflictError('Code does not match')
  }

  await user.update({
    phone: new_phone,
    phoneVerifiedAt: new Date(),
    passwordResetToken: null,
  })

  logger.info(`Phone no. updated successfully by user ${user.id}`);
  const userData = await getUserWithCalendarInfo(user.id)
  return ({
    message: 'Phone number updated successfully.',
    user: userData,
  })
}
