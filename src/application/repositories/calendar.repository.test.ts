import { expect } from 'chai';
import sinon from 'sinon';
import * as googleConfig from '@/src/configs/google.config';
import * as outlookConfig from '@/src/configs/outlook.config';
import * as calendarRepo from '@/src/application/repositories/calendar.repository';
import logger from '@/src/configs/logger.config';
import { Calendar } from '@/src/models';
import axios from 'axios';
import { Client } from '@microsoft/microsoft-graph-client';
import { ForbiddenError, BadRequestError } from '../handlers/errors';

const tokens = { access_token: 'token', refresh_token: 'refresh', access_token_expiry: Math.floor(Date.now() / 1000) + 1000 };
const expiredTokens = { ...tokens, access_token_expiry: Math.floor(Date.now() / 1000) - 100 };
const eventData = { summary: 'Test Event', start: { dateTime: '2025-06-26T10:00:00Z' }, end: { dateTime: '2025-06-26T11:00:00Z' } };
const mockUserId = 1;
const mockEventId = 'event123';

// Use a Proxy to mock class with private fields
const mockClient = {} as unknown as Client;

const sandbox = sinon.createSandbox();

describe('Calendar Repository', () => {
  beforeEach(() => {
    sandbox.stub(logger, 'error');
    sandbox.stub(logger, 'info');
  });

  afterEach(() => sandbox.restore());

  describe('createGoogleEvent', () => {
    it('should create a Google event', async () => {
      sandbox.stub(googleConfig, 'createGoogleCalendarEvent').resolves(eventData);
      const result = await calendarRepo.createGoogleEvent({ tokens, eventData });
      expect(result).to.eql(eventData);
    });

    it('should throw ForbiddenError if tokens are not provided', async () => {
      try {
        await calendarRepo.createGoogleEvent({ tokens: null, eventData });
        throw new Error('should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
        expect((err as Error).message).to.equal('Google tokens are required');
      }
    });

    it('should throw ForbiddenError if eventData is not provided', async () => {
      try {
        await calendarRepo.createGoogleEvent({ tokens, eventData: null });
        throw new Error('should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should log an error and return undefined when event creation fails', async () => {
      const createStub = sandbox.stub(googleConfig, 'createGoogleCalendarEvent').rejects(new Error('Google API Error'));
      const result = await calendarRepo.createGoogleEvent({ tokens, eventData });
      expect(result).to.be.undefined;
      expect((logger.error as sinon.SinonStub).called).to.be.true;
      expect(createStub.called).to.be.true;
    });
  });

  describe('getGoogleEvents', () => {
    it('should get all google events successfully', async () => {
      const getStub = sandbox.stub(googleConfig, 'getGoogleCalendarEvents').resolves([eventData]);
      const result = await calendarRepo.getGoogleEvents({ tokens });
      expect(result).to.deep.equal([eventData]);
      expect(getStub.calledWith(tokens)).to.be.true;
    });

    it('should get google events for a specific date range successfully', async () => {
      const getForDateStub = sandbox.stub(googleConfig, 'getCalendarEventsForDate').resolves([eventData]);
      const startDate = new Date();
      const endDate = new Date();
      const startIso = startDate.toISOString();
      const endIso = endDate.toISOString();

      const result = await calendarRepo.getGoogleEvents({ tokens, forDate: true, startDate: startIso, endDate: endIso });
      
      expect(result).to.deep.equal([eventData]);
      expect(getForDateStub.calledWith(tokens, startIso, endIso)).to.be.true;
    });

    it('should throw ForbiddenError if tokens are not provided', async () => {
      try {
        await calendarRepo.getGoogleEvents({ tokens: null });
        throw new Error('should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(ForbiddenError);
      }
    });

    it('should throw BadRequestError if forDate is true and dates are missing', async () => {
      try {
        await calendarRepo.getGoogleEvents({ tokens, forDate: true });
        throw new Error('should have thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(BadRequestError);
      }
    });

    it('should log an error and return undefined when fetching events fails', async () => {
        sandbox.stub(googleConfig, 'getGoogleCalendarEvents').rejects(new Error('Google API Error'));
        const result = await calendarRepo.getGoogleEvents({ tokens });
        expect(result).to.be.undefined;
        expect((logger.error as sinon.SinonStub).called).to.be.true;
    });
  });

  describe('createOutlookEvent', () => {
    let createStub: sinon.SinonStub;
    let refreshStub: sinon.SinonStub;
    
    beforeEach(() => {
        sandbox.stub(calendarRepo, 'initializeMicrosoftClient').resolves(mockClient);
        refreshStub = sandbox.stub(calendarRepo, 'refreshMicrosoftAccessToken').resolves(mockClient);
        createStub = sandbox.stub(outlookConfig, 'createOutlookCalendarEvent').resolves(eventData);
    });

    it('should create an outlook event successfully with a valid token', async () => {
      const result = await calendarRepo.createOutlookEvent({ tokens, eventData, userId: mockUserId });
      expect(result).to.eql(eventData);
      expect(createStub.calledWith(mockClient, eventData)).to.be.true;
    });
    
    it('should refresh token and create an outlook event successfully', async () => {
        const result = await calendarRepo.createOutlookEvent({ tokens: expiredTokens, userId: mockUserId, eventData: eventData });
        expect(result).to.eql(eventData);
        expect(refreshStub.calledWith(expiredTokens.refresh_token, mockUserId)).to.be.true;
        expect(createStub.calledWith(mockClient, eventData)).to.be.true;
    });

    it('should throw ForbiddenError for missing tokens, userId, or eventData', async () => {
        try { await calendarRepo.createOutlookEvent({ tokens: null, userId: mockUserId, eventData }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.createOutlookEvent({ tokens, userId: undefined, eventData }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.createOutlookEvent({ tokens, userId: mockUserId, eventData: null }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
    });

  });

  describe('getOutlookEvents', () => {
    it('should fetch Outlook events successfully', async () => {
      sandbox.stub(calendarRepo, 'initializeMicrosoftClient').resolves(mockClient);
      const getStub = sandbox.stub(outlookConfig, 'getOutlookCalendarEvents').resolves([eventData]);
      const result = await calendarRepo.getOutlookEvents({ tokens, userId: mockUserId });
      expect(result).to.eql([eventData]);
      expect(getStub.calledWith(mockClient)).to.be.true;
    });

    it('should fetch Outlook events for a date range successfully', async () => {
        sandbox.stub(calendarRepo, 'initializeMicrosoftClient').resolves(mockClient);
        const getForDateStub = sandbox.stub(outlookConfig, 'getOutlookCalendarEventsForDate').resolves([eventData]);
        const startDate = new Date().toISOString();
        const endDate = new Date().toISOString();
        const result = await calendarRepo.getOutlookEvents({ tokens, userId: mockUserId, forDate: true, startDate, endDate });
        expect(result).to.eql([eventData]);
        expect(getForDateStub.calledWith(mockClient, startDate, endDate)).to.be.true;
    });
    
    it('should throw error for missing tokens, userId or dates', async () => {
        try { await calendarRepo.getOutlookEvents({ tokens: null, userId: mockUserId }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.getOutlookEvents({ tokens, userId: undefined }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.getOutlookEvents({ tokens, userId: mockUserId, forDate: true }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(BadRequestError); }
    });
  });

  describe('initializeMicrosoftClient', () => {
    it('should return null on error', async () => {
        sandbox.stub(outlookConfig, 'getAuthenticatedClient').rejects(new Error('init fail'));
        const result = await calendarRepo.initializeMicrosoftClient('token');
        expect(result).to.be.null;
        expect((logger.error as sinon.SinonStub).called).to.be.true;
    });
  });

  describe('refreshMicrosoftAccessToken', () => {
    it('should refresh token, update calendar and return a client', async () => {
      const postStub = sandbox.stub(axios, 'post').resolves({ data: { access_token: 'new-token', expires_in: 3600 } });
      const getAuthStub = sandbox.stub(outlookConfig, 'getAuthenticatedClient').resolves(mockClient);
      const getProfileStub = sandbox.stub(outlookConfig, 'getProfile').resolves({ id: 'user-id' });
      const updateStub = sandbox.stub(Calendar, 'update').resolves([1]);
      
      const result = await calendarRepo.refreshMicrosoftAccessToken('refresh-token', mockUserId);
      
      expect(result).to.eql(mockClient);
      expect(postStub.called).to.be.true;
      expect(getAuthStub.called).to.be.true;
      expect(getProfileStub.called).to.be.true;
      expect(updateStub.called).to.be.true;
    });
    
    it('should return null on axios failure', async () => {
      sandbox.stub(axios, 'post').rejects(new Error('axios fail'));
      const result = await calendarRepo.refreshMicrosoftAccessToken('refresh_token', mockUserId);
      expect(result).to.be.null;
      expect((logger.error as sinon.SinonStub).called).to.be.true;
    });
  });

  describe('deleteGoogleEvent', () => {
    it('should delete a Google event successfully', async () => {
      sandbox.stub(googleConfig, 'deleteGoogleCalendarEvent').resolves(true);
      const result = await calendarRepo.deleteGoogleEvent({ tokens, eventId: mockEventId });
      expect(result).to.be.true;
    });
    
    it('should return false and log error on failure', async () => {
      sandbox.stub(googleConfig, 'deleteGoogleCalendarEvent').rejects(new Error('fail'));
      const result = await calendarRepo.deleteGoogleEvent({ tokens, eventId: mockEventId });
      expect(result).to.be.false;
      expect((logger.error as sinon.SinonStub).called).to.be.true;
    });

    it('should throw ForbiddenError for missing tokens or eventId', async () => {
        try { await calendarRepo.deleteGoogleEvent({ tokens: null, eventId: mockEventId }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.deleteGoogleEvent({ tokens, eventId: undefined }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
    });
  });

  describe('deleteOutlookEvent', () => {
    it('should delete an Outlook event successfully', async () => {
      sandbox.stub(calendarRepo, 'initializeMicrosoftClient').resolves(mockClient);
      const deleteStub = sandbox.stub(outlookConfig, 'deleteOutlookCalendarEvent').resolves(true);
      const result = await calendarRepo.deleteOutlookEvent({ tokens, userId: mockUserId, eventId: mockEventId });
      expect(result).to.be.true;
      expect(deleteStub.calledWith(mockClient, mockEventId)).to.be.true;
    });

    it('should refresh token and delete an outlook event successfully', async () => {
        sandbox.stub(calendarRepo, 'refreshMicrosoftAccessToken').resolves(mockClient);
        const deleteStub = sandbox.stub(outlookConfig, 'deleteOutlookCalendarEvent').resolves(true);
        const result = await calendarRepo.deleteOutlookEvent({ tokens: expiredTokens, userId: mockUserId, eventId: mockEventId });
        expect(result).to.be.true;
        expect(deleteStub.calledWith(mockClient, mockEventId)).to.be.true;
    });

    it('should throw ForbiddenError for missing tokens, userId or eventId', async () => {
        try { await calendarRepo.deleteOutlookEvent({ tokens: null, userId: mockUserId, eventId: mockEventId }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.deleteOutlookEvent({ tokens, userId: undefined, eventId: mockEventId }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
        try { await calendarRepo.deleteOutlookEvent({ tokens, userId: mockUserId, eventId: undefined }); throw new Error('should have thrown'); } catch (e) { expect(e).to.be.instanceOf(ForbiddenError); }
    });

    it('should return false if graph client is not initialized', async () => {
        sandbox.stub(calendarRepo, 'initializeMicrosoftClient').resolves(null);
        const result = await calendarRepo.deleteOutlookEvent({ tokens, userId: mockUserId, eventId: mockEventId });
        expect(result).to.be.false;
        expect((logger.error as sinon.SinonStub).calledWith('graph client could not be initialized')).to.be.true;
    });

    it('should return false and log error on failure', async () => {
        sandbox.stub(calendarRepo, 'initializeMicrosoftClient').resolves(mockClient);
        sandbox.stub(outlookConfig, 'deleteOutlookCalendarEvent').rejects(new Error('Outlook API Error'));
        const result = await calendarRepo.deleteOutlookEvent({ tokens, userId: mockUserId, eventId: mockEventId });
        expect(result).to.be.false;
        expect((logger.error as sinon.SinonStub).called).to.be.true;
    });
  });
});