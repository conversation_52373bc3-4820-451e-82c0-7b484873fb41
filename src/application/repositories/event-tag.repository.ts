import { EventTag } from '@/src/models'
import {
	FindAttributeOptions,
	GroupOption,
	Order,
	WhereOptions,
} from 'sequelize'
import { Request } from 'express'
import { paginated } from '../helpers/pagination.helper'

type EventFilter = {
	req: Request
	perPage?: number
}

export const getTags = async ({ req, perPage }: EventFilter) => {
	const group: GroupOption = []
	let where: WhereOptions = {}
	const order: Order = []
	const relations: any = []
	const attributes: FindAttributeOptions = []

	order.push(['createdAt', 'DESC'])

	return EventTag.findAndCountAll({
		attributes: {
			include: attributes,
		},
		include: relations,
		where,
		group,
		order,
		...paginated(req, perPage),
	})
}

export const createTag = async (req: Request) => {}