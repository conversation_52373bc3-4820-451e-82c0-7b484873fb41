import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import { Appointments } from '@/src/models';
import { expect } from 'chai';
import { mail } from '@/src/configs/sendgrid.config';
import { runConfirmPaymentNotifyJob } from './confirm-payment-notify.job';
import dayjs from 'dayjs';

describe('Confirm Payment Notify Job Test', function () {
	let sandbox: sinon.SinonSandbox;

	let sendEmailStub: sinon.SinonStub;

	let appointmentsFindAllStub: sinon.SinonStub;

	let dummyAppointment = {
		id: 1,
		therapistId: 1,
		appointmentDate: dayjs().subtract(5, 'minute').toISOString(),
		type: 'in-person',
		duration: 60,
		isMinor: true,
		minorId: 1,
		therapist: {
			id: 1,
			email: '<EMAIL>',
			timeZone: 'America/New_York',
			phone: '+**********',
			firstname: '<PERSON>',
			lastname: '<PERSON><PERSON>',
			registrationInfo: [
				{
					pageName: 'practice-info',
					payloadInfo: {
						business_address: {
							full_address: '123 Main St, New York, NY 10001',
						}
					}
				}
			]
		},
		patient: {
			id: 2,
			email: '<EMAIL>',
			timeZone: 'America/New_York',
			phone: '+**********',
			firstname: 'Jane',
			lastname: 'Doe',
		},
		minorPatient: {
			firstName: 'Child',
			lastName: 'Doe',
		},
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		sendEmailStub = sandbox.stub(mail, 'sendMail').resolves();

		appointmentsFindAllStub = sandbox.stub(Appointments, 'findAll');
	});

	afterEach(function() {
		process.env.BYPASS_JOBS = "false";
		sandbox.restore();
	});

	it('should not run if BYPASS_JOBS is true', async () => {
		process.env.BYPASS_JOBS = "true";

		await runConfirmPaymentNotifyJob();

		expect(appointmentsFindAllStub.notCalled).to.be.true;
	});

	it('should not send any notifications if no appointments found', async () => {
		appointmentsFindAllStub.resolves([]);

		await runConfirmPaymentNotifyJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should send email notifications successfully', async () => {
		appointmentsFindAllStub.resolves([dummyAppointment]);

		await runConfirmPaymentNotifyJob();

		expect(sendEmailStub.calledOnce).to.be.true;
	});

	it('should not send email notifications if error occurs during appointments fetch', async () => {
		appointmentsFindAllStub.rejects(new Error('Database error'));

		await runConfirmPaymentNotifyJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});
});