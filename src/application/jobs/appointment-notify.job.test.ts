import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import { Appointments } from '@/src/models';
import { expect } from 'chai';
import { mail } from '@/src/configs/sendgrid.config';
import { runAppointmentNotifyJob } from './appointment-notify.job';
import dayjs from 'dayjs';
import * as twilioConfig from '@/src/configs/twilio.config';
import admin from '@/src/configs/firebase.config';

describe('Appointment Notify Job Test', function () {
	let sandbox: sinon.SinonSandbox;

	let sendEmailStub: sinon.SinonStub;
	let sendSmsStub: sinon.SinonStub;
	let sendFCMStub: sinon.SinonStub;

	let appointmentsFindAllStub: sinon.SinonStub;

	let dummyAppointment = {
		id: 1,
		therapistId: 1,
		appointmentDate: dayjs().subtract(5, 'minute').toISOString(),
		type: 'in-person',
		duration: 60,
		isMinor: true,
		minorId: 1,
		therapist: {
			id: 1,
			email: '<EMAIL>',
			timeZone: 'America/New_York',
			phone: '+**********',
			firstname: '<PERSON>',
			lastname: 'Doe',
			registrationInfo: [
				{
					pageName: 'waitlist-notifications',
					payloadInfo: {
						notification_preferences: ['email', 'text'],
						phone_number: '+**********',
					}
				},
				{
					pageName: 'practice-info',
					payloadInfo: {
						business_address: {
							full_address: '123 Main St, New York, NY 10001',
						}
					}
				}
			]
		},
		patient: {
			id: 2,
			email: '<EMAIL>',
			timeZone: 'America/New_York',
			phone: '+**********',
			firstname: 'Jane',
			lastname: 'Doe',
			userDevices: [
				{
					deviceType: 'samsung',
					deviceId: 'device123',
				}
			]
		},
		minorPatient: {
			firstName: 'Child',
			lastName: 'Doe',
		},
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		sendEmailStub = sandbox.stub(mail, 'sendMail').resolves();
		sendSmsStub = sandbox.stub(twilioConfig, 'sendSms').resolves();
		sendFCMStub = sandbox.stub(admin.messaging(), 'send').resolves();

		appointmentsFindAllStub = sandbox.stub(Appointments, 'findAll');
	});

	afterEach(function() {
		process.env.BYPASS_JOBS = "false";
		sandbox.restore();
	});

	it('should not run if BYPASS_JOBS is true', async () => {
		process.env.BYPASS_JOBS = "true";

		await runAppointmentNotifyJob();

		expect(appointmentsFindAllStub.notCalled).to.be.true;
	});

	it('should not send any notifications if no appointments found', async () => {
		appointmentsFindAllStub.resolves([]);

		await runAppointmentNotifyJob();

		expect(sendEmailStub.notCalled).to.be.true;
		expect(sendSmsStub.notCalled).to.be.true;
		expect(sendFCMStub.notCalled).to.be.true;
	});

	it('should not send any notifications if error occurs during appointments fetch', async () => {
		appointmentsFindAllStub.rejects(new Error('Database error'));

		await runAppointmentNotifyJob();

		expect(sendEmailStub.notCalled).to.be.true;
		expect(sendSmsStub.notCalled).to.be.true;
		expect(sendFCMStub.notCalled).to.be.true;
	});

	it('should send notifications successfully', async () => {
		appointmentsFindAllStub.resolves([dummyAppointment]);

		await runAppointmentNotifyJob();

		expect(sendEmailStub.calledTwice).to.be.true;
		expect(sendSmsStub.calledTwice).to.be.true;
		expect(sendFCMStub.calledOnce).to.be.true;
	});
});