import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import { StripeInfo } from '@/src/models';
import { expect } from 'chai';
import { mail } from '@/src/configs/sendgrid.config';
import { runSubsAutoRenewalEmailJob } from './subscription-auto-renewal-email.job';
import { SubscriptionRenewalEmail } from '../emails/subscription.email';

describe('Stripe Subscription Auto Renewal Email Job Test', function () {
	let sandbox: sinon.SinonSandbox;

	let sendEmailStub: sinon.SinonStub;

	let stripeInfoFindAllStub: sinon.SinonStub;

	let subsRenewalEmailCompileStub: sinon.SinonStub;

	let dummyStripeInfo = {
		id: 1,
		therapistId: 1,
		stripeCustomerId: 'cus_123456',
		stripeCurrentPeriodEnd: new Date(),
		stripeSubscriptionId: 'sub_123456',
		billedPrice: 1000,
		therapist: {
			id: 1,
			firstname: '<PERSON>',
			lastname: '<PERSON><PERSON>',
			email: '<EMAIL>',
			timeZone: 'America/New_York',
		},
		therapistSubscription: {
			id: 1,
			therapistId: 1,
			subscriptionPlanId: 1,
			subscriptionType: 'monthly',
			subscriptionPlan: {
				id: 1,
				annualPrice: 120,
				monthlyPrice: 12,
				isActive: true,
			}
		}
	};

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		sendEmailStub = sandbox.stub(mail, 'sendMail').resolves();

		subsRenewalEmailCompileStub = sandbox.stub(SubscriptionRenewalEmail, 'compile');

		stripeInfoFindAllStub = sandbox.stub(StripeInfo, 'findAll');
	});

	afterEach(function() {
		process.env.BYPASS_JOBS = "false";
		sandbox.restore();
	});

	it('should not run if BYPASS_JOBS is true', async () => {
		process.env.BYPASS_JOBS = "true";

		await runSubsAutoRenewalEmailJob();

		expect(stripeInfoFindAllStub.notCalled).to.be.true;
	});

	it('should stop if no renewals is found', async () => {
		stripeInfoFindAllStub.resolves([]);

		await runSubsAutoRenewalEmailJob();

		expect(stripeInfoFindAllStub.calledOnce).to.be.true;
	});

	it('should send renewal notify emails successfully', async () => {
		stripeInfoFindAllStub.resolves([dummyStripeInfo]);
		subsRenewalEmailCompileStub.returns({
			To: '<EMAIL>',
			Subject: 'Subscription Renewal Reminder',
			HtmlBody: '<p>Your subscription is about to renew.</p>'
		});

		await runSubsAutoRenewalEmailJob();

		expect(stripeInfoFindAllStub.calledOnce).to.be.true;
		expect(subsRenewalEmailCompileStub.calledOnce).to.be.true;
		expect(sendEmailStub.calledOnceWith({
			to: '<EMAIL>',
			subject: 'Subscription Renewal Reminder',
			html: '<p>Your subscription is about to renew.</p>'
		})).to.be.true;
	});

	it('should not send email if subscription plan is not active', async () => {
		stripeInfoFindAllStub.resolves([
			{
				...dummyStripeInfo,
				therapistSubscription: {
					...dummyStripeInfo.therapistSubscription,
					subscriptionPlan: {
						...dummyStripeInfo.therapistSubscription.subscriptionPlan,
						isActive: false
					}
				}
			}
		]);

		await runSubsAutoRenewalEmailJob();

		expect(stripeInfoFindAllStub.calledOnce).to.be.true;
		expect(subsRenewalEmailCompileStub.notCalled).to.be.true;
		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should not send email if amount is not found', async () => {
		stripeInfoFindAllStub.resolves([
			{
				...dummyStripeInfo,
				therapistSubscription: {
					...dummyStripeInfo.therapistSubscription,
					subscriptionType: 'error',
				}
			}
		]);

		await runSubsAutoRenewalEmailJob();

		expect(stripeInfoFindAllStub.calledOnce).to.be.true;
		expect(subsRenewalEmailCompileStub.notCalled).to.be.true;
		expect(sendEmailStub.notCalled).to.be.true;
	});
});