import { mail } from '@/src/configs/sendgrid.config';
import { Appointments, MinorPatient, User, UserDevice, UserRegistrationInfo } from '@/src/models';
import cron from 'node-cron';
import dayjs from 'dayjs';
import { Op } from 'sequelize';
import { TherapistConfirmPaymentNotifyEmail } from '../emails/appointment-notify.email';
import timezone from "dayjs/plugin/timezone";
import { capitalizeFirstLetter, formatDuration, getTimezoneAbbr, isValidTimeZone, maskEmail } from '../helpers/general.helper';
import { PracticeInfo } from '@/src/types/registration.interface';
import logger from '@/src/configs/logger.config';

dayjs.extend(timezone);

// notify therapist to confirm payment for an appointment after it has passed
const ConfirmPaymentNotifyJob = () => {
	cron.schedule("*/5 * * * *", runConfirmPaymentNotifyJob);
};

export const runConfirmPaymentNotifyJob = async () => {
	try {
		if (process.env.BYPASS_JOBS === "true") return;
		logger.info(`Confirm Payment Notify Job - Checking for appointments to notify`);
		const now = dayjs().second(0).millisecond(0);
		const fiveMinuteBefore = now.subtract(5, 'minute').toISOString();

		const appointments = await Appointments.findAll({
			where: {
				appointmentDate: fiveMinuteBefore,
				isBilled: false,
				cancelledAt: null,
			},
			include: [
				{
					model: User,
					as: 'therapist',
					attributes: ['id', 'firstname', 'lastname', 'email', 'address', 'timeZone', 'phone'],
					include: [
						{
							model: UserRegistrationInfo,
							where: {
								pageName: 'practice-info',
							},
							required: false,
						}
					]
				},
				{
					model: User,
					as: 'patient',
					attributes: ['id', 'firstname', 'lastname', 'email', 'timeZone', 'phone'],
				},
				{
					model: MinorPatient,
					as: 'minorPatient',
				}
			],
		});

		if (appointments.length === 0) {
			logger.info(`Confirm Payment Notify Job - No appointments found to process`);
			return;
		}

		logger.info(`Starting Confirm Payment Notify Job - Found ${appointments.length} appointments to process`);

		for (const appointment of appointments) {
			logger.info(`Confirm Payment Notify Email Job - Processing appointment ID: ${appointment.id}`);
			const therapistFullName = capitalizeFirstLetter(`${appointment.therapist?.firstname} ${appointment.therapist?.lastname}`);
			let patientFullName = capitalizeFirstLetter(`${appointment.patient?.firstname} ${appointment.patient?.lastname}`);

			if (appointment.isMinor && appointment.minorId && appointment.minorPatient) {
				const minorPatient = appointment.minorPatient;
				if (minorPatient.firstName) {
					patientFullName = capitalizeFirstLetter(`${minorPatient.firstName} ${minorPatient?.lastName}`);
				}
			}
			let appointmentLocation: string | null = null;
			if (appointment.type === 'in-person') {
				appointmentLocation = (appointment.therapist.address as any)?.full_address || null;

				if (!appointmentLocation) {
					const therapistPracticeInfo = appointment.therapist?.registrationInfo?.find(
						(info) => info.pageName === 'practice-info'
					)?.payloadInfo as unknown as PracticeInfo;

					if (therapistPracticeInfo && therapistPracticeInfo.business_address) {
						appointmentLocation = therapistPracticeInfo.business_address?.full_address || null;
					}	
				}
			}
			// Notify therapist
			try {
				const therapistTimeZone = appointment.therapist.timeZone;

				if (therapistTimeZone && isValidTimeZone(therapistTimeZone)) {
					logger.info(`Confirm Payment Notify Email Job - Preparing Notification data for appointment ID: ${appointment.id} - Therapist ID: ${appointment.therapist.id}`);
					const therapistAppDate = dayjs.tz(appointment.appointmentDate, therapistTimeZone).format('MMMM D, YYYY');
					const therapistAppTime = dayjs.tz(appointment.appointmentDate, therapistTimeZone).format('h:mm A');
					const therapistTimeZoneAbbr = getTimezoneAbbr(therapistTimeZone);

					// Email Notification
					try {
						logger.info(`Preparing Confirm Payment Notification email data for appointment ID: ${appointment.id} - Therapist ID: ${appointment.therapist.id}`);
						const therapistEmailData = TherapistConfirmPaymentNotifyEmail.compile({
							email: appointment.therapist.email,
							subject: `Appointment Reminder: ${patientFullName} on ${therapistAppDate} at ${therapistAppTime}`,
							therapistName: therapistFullName,
							patientName: patientFullName,
							appointmentDate: therapistAppDate,
							appointmentTime: `${therapistAppTime} (${therapistTimeZoneAbbr})`,
							duration: formatDuration(appointment.duration),
							location: appointmentLocation || '',
							title: `This is a reminder of your upcoming appointment with ${patientFullName}:`,
							appointmentType: capitalizeFirstLetter(appointment.type),
							confirmPaymentLink: `${process.env.FRONTEND_URL}/reporting?appointmentId=${appointment.id}`
						});

						logger.info(`Sending Confirm Payment Notification Email to ${maskEmail(appointment.therapist.email)}`)
						await mail.sendMail({
							to: therapistEmailData.To,
							subject: therapistEmailData.Subject,
							html: therapistEmailData.HtmlBody,
						}, true);
						logger.info(`Confirm Payment Notification Email sent to ${maskEmail(appointment.therapist.email)}`)
					} catch (error) {
						logger.error(`Confirm Payment Notify Job - Therapist Email error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
					}
				}
			} catch (error) {
				logger.error(`Confirm Payment Notify Job - Therapist Notify error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
			}
		}

		logger.info(`Confirm Payment Notify Job - Notifications Sent successfully`)
	} catch (error) {
		logger.error(`Confirm Payment Notify Job - error: ${error}.`);
	}
};

export default ConfirmPaymentNotifyJob;
