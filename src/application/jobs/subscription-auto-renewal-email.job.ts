import cron from 'node-cron';
import dayjs from 'dayjs';
import timezone from "dayjs/plugin/timezone";
import { StripeInfo, SubscriptionPlan, TherapistSubscription, User } from '@/src/models';
import { Op } from 'sequelize';
import sequelize from '@/src/configs/database.config';
import { capitalizeFirstLetter, isValidTimeZone, maskEmail } from '../helpers/general.helper';
import relativeTime from 'dayjs/plugin/relativeTime';
import { SubscriptionRenewalEmail } from '../emails/subscription.email';
import { mail } from '@/src/configs/sendgrid.config';
import logger from '@/src/configs/logger.config';

dayjs.extend(relativeTime);
dayjs.extend(timezone);

const SubscriptionAutoRenewalEmailJob = () => {
	cron.schedule("*/1 * * * *", runSubsAutoRenewalEmailJob);
}

export const runSubsAutoRenewalEmailJob = async () => {
	try {
		if (process.env.BYPASS_JOBS === "true") return;
		
		logger.info(`Subscription Auto Renewal Email Job - Checking for subscription renewals`);
		const now = dayjs().millisecond(0);
		const twentyFourHours = now.add(24, 'hour').toISOString();
		const tenDays = now.add(10, 'day').toISOString();
		
		const renewals = await StripeInfo.findAll({
			where: {
				stripeCancelAtPeriodEnd: false,
				status: 'active',
				[Op.or]: [
					sequelize.where(
						sequelize.fn('date_trunc', 'minute', sequelize.col('stripeCurrentPeriodEnd')),
						twentyFourHours
					),
					sequelize.where(
						sequelize.fn('date_trunc', 'minute', sequelize.col('stripeCurrentPeriodEnd')),
						tenDays
					)
				],
			},
			include: [
				{
					model: User,
					as: 'therapist',
					attributes: ['id', 'firstname', 'lastname', 'email', 'address', 'timeZone', 'phone'],
				},
				{
					model: TherapistSubscription,
					as: 'therapistSubscription',
					include: [
						{
							model: SubscriptionPlan,
							as: 'subscriptionPlan',
						}
					]
				}
			]
		});

		if (renewals.length === 0) {
			logger.info('Subscription Auto Renewal Email Job - No renewals found to process.');
			return;
		}

		logger.info(`Starting Subscription Auto Renewal Email Job - Found ${renewals.length} renewals to process`);

		for (const renewal of renewals) {
			try {
				const therapistTimeZone = renewal.therapist.timeZone;

				if (therapistTimeZone && isValidTimeZone(therapistTimeZone)) {
					logger.info(`Preparing Subscription Auto Renewal email data for therapist ID: ${renewal.therapist.id} - Stripe Info ID: ${renewal.id}`);
					const autoRenewalDate = dayjs(renewal.stripeCurrentPeriodEnd).tz(therapistTimeZone).format('MMMM D, YYYY h:mm A');
					const relativeTime = dayjs(renewal.stripeCurrentPeriodEnd).tz(therapistTimeZone).fromNow();
					const renewalDateDisplay = `${autoRenewalDate} (${relativeTime})`;

					const activeSubscriptionPlan = renewal.therapistSubscription.subscriptionPlan;
					if (!activeSubscriptionPlan.isActive) throw new Error('Subscription plan is not active. Please try again.');

					const amount = renewal.therapistSubscription.subscriptionType === 'monthly'
						? activeSubscriptionPlan.monthlyPrice :
							renewal.therapistSubscription.subscriptionType === 'yearly'
								? activeSubscriptionPlan.annualPrice :
									null;
					if (!amount) throw new Error('Invalid subscription plan. Please try again.');

					const renewalEmailData = SubscriptionRenewalEmail.compile({
						email: renewal.therapist.email,
						subject: `NextTherapist Subscription Renewal`,
						therapistName: capitalizeFirstLetter(`${renewal.therapist?.firstname} ${renewal.therapist?.lastname}`),
						autoRenewalDate: renewalDateDisplay,
						amount: `$${amount}`,
						subscriptionPlan: capitalizeFirstLetter(renewal.therapistSubscription.subscriptionType),
					});
					logger.info(`Sending Subscription Auto Renewal Email to ${maskEmail(renewal.therapist.email)}`)
					await mail.sendMail({
						to: renewalEmailData.To,
						subject: renewalEmailData.Subject,
						html: renewalEmailData.HtmlBody,
					});
					logger.info(`Subscription Auto Renewal Email sent to ${maskEmail(renewal.therapist.email)}`)
				}
			} catch (error) {
				logger.error(`Subscription Auto Renewal Email - Therapist Notify error - Therapist ID: (${renewal.therapist.id}) - Stripe Info ID: (${renewal.id}): ${error}.`);
			}
		}
	} catch (error) {
		logger.error(`Subscription Auto Renewal Email Job - error: ${error}.`);
	}
};

export default SubscriptionAutoRenewalEmailJob;
