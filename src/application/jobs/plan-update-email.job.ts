import { mail } from '@/src/configs/sendgrid.config';
import { NotifySubscriptionUpdate, StripeInfo, SubscriptionPlan, TherapistSubscription, User } from '@/src/models';
import cron from 'node-cron';
import { PlanChangeEmail } from '../emails/subscription.email';
import { capitalizeFirstLetter, maskEmail } from '../helpers/general.helper';
import logger from '@/src/configs/logger.config';
import { Op } from 'sequelize';
import stripe from '@/src/configs/stripe-new.config';
import sequelize from '@/src/configs/database.config';
import { ForbiddenError } from '../handlers/errors';

const PlanUpdateEmailJob = () => {
	cron.schedule("*/5 * * * *", runPlanUpdateEmailJob);
};

export const runPlanUpdateEmailJob = async () => {
	try {
		if (process.env.BYPASS_JOBS === "true") return;

		logger.info(`Subscription Plan Update/Notify Job - Checking for pending subscription updates`);
		const subUpdateList = await NotifySubscriptionUpdate.findAll({
			where: {
				[Op.or]: [
					{ isNotified: false },
					{ isUpdated: false },
				],
				isFailed: false,
			},
			include: [
				{
					model: User,
					as: 'user',
					attributes: ['id', 'email', 'firstname', 'lastname'],
				},
				{
					model: SubscriptionPlan,
					as: 'oldSubscriptionPlan',
					attributes: ['id', 'annualPrice', 'monthlyPrice'],
				},
				{
					model: SubscriptionPlan,
					as: 'newSubscriptionPlan',
					attributes: ['id', 'annualPrice', 'monthlyPrice'],
				},
				{
					model: TherapistSubscription,
					as: 'therapistSubscription',
					attributes: ['id', 'subscriptionType'],
					include: [
						{
							model: StripeInfo,
							as: 'stripeInfo',
							attributes: ['id', 'stripeSubscriptionId', 'stripeSubscriptionScheduleId', 'nextScheduledSubscriptionType'],
							// where: {
							// 	status: 'active',
							// },
							// required: false,
						},
					]
				}
			],
			limit: 20,
		});

		if (!subUpdateList || subUpdateList.length === 0) {
			logger.info('Subscription Plan Update/Notify Job - No pending subscription updates found');
			return;
		}

		logger.info(`Subscription Plan Update/Notify Job - Found ${subUpdateList.length} pending subscription updates`);

		let newSubPlanId;
		let newMonthlyStripePrice, newAnnualStripePrice;

		for (const thSub of subUpdateList) {
			try {
				if (!thSub.therapistSubscription) {
					logger.info(`Subscription Plan Update/Notify Job - therapist subscription not found with ID: ${thSub.therapistSubscriptionId} for user ID: ${thSub.user.id}`);
					throw new ForbiddenError(`Therapist subscription not found with ID: ${thSub.therapistSubscriptionId}`);
				}
				if (!thSub.newSubscriptionPlan) {
					logger.info(`Subscription Plan Update/Notify Job - new subscription plan not found with ID: ${thSub.newSubscriptionPlanId} for user ID: ${thSub.user.id}`);
					throw new ForbiddenError(`New subscription plan not found with ID: ${thSub.newSubscriptionPlanId}`);
				}
				if (!thSub.oldSubscriptionPlan) {
					logger.info(`Subscription Plan Update/Notify Job - old subscription plan not found with ID: ${thSub.oldSubscriptionPlanId} for user ID: ${thSub.user.id}`);
					throw new ForbiddenError(`Old subscription plan not found with ID: ${thSub.oldSubscriptionPlanId}`);
				}

				if (!newSubPlanId || (newSubPlanId !== thSub.newSubscriptionPlanId)) {
					newSubPlanId = thSub.newSubscriptionPlan.id;

					logger.info(`Subscription Plan Update/Notify Job - Creating new Stripe prices for the new active plan`);
					[newMonthlyStripePrice, newAnnualStripePrice] = await Promise.all([
						stripe.prices.create({
							currency: 'usd',
							unit_amount: Math.round(thSub.newSubscriptionPlan.monthlyPrice * 100),
							recurring: { interval: 'month' },
							product_data: {
								name: 'NextTherapist - Monthly',
							},
						}),
						stripe.prices.create({
							currency: 'usd',
							unit_amount: Math.round(thSub.newSubscriptionPlan.annualPrice * 100),
							recurring: { interval: 'year' },
							product_data: {
								name: 'NextTherapist - Yearly',
							},
						})
					]);
					
					logger.info(`Subscription Plan Update/Notify Job - New Stripe prices created for plan ID: ${thSub.newSubscriptionPlan.id}`);
				}
				if (!newMonthlyStripePrice || !newAnnualStripePrice) throw new ForbiddenError(`Subscription Plan Update/Notify Job - New Stripe prices is not available`);

				// update the subscription
				if (!thSub.isUpdated) {
					if (!thSub.therapistSubscription.stripeInfo) {
						logger.info(`Subscription Plan Update/Notify Job - Stripe info not found for therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}`);
						throw new ForbiddenError(`Stripe info not found for therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}`);
					}
					const transaction = await sequelize.transaction();
					logger.info(`Subscription Plan Update/Notify Job - Updating subscription of therapist ID: ${thSub.user.id}`);
					try {
						// deactivate the old subscription plan
						await TherapistSubscription.update({
							isActive: false,
							expiredAt: new Date(),
						}, {
							where: {
								id: thSub.therapistSubscription.id,
							},
							transaction,
						});
						logger.info(`Subscription Plan Update/Notify Job - Old subscription of therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id} deactivated successfully`);

						// create a new subscription entry with the new plan ID
						const newTherapistSubscription = await TherapistSubscription.create(
							{
								therapistId: thSub.user.id,
								subscriptionPlanId: thSub.newSubscriptionPlan.id,
								subscriptionType: thSub.therapistSubscription.subscriptionType,
								isActive: true,
								subscribedAt: new Date(),
							},
							{ transaction }
						);
						logger.info(`Subscription Plan Update/Notify Job - New subscription of therapist ID: ${thSub.user.id} - Subscription ID: ${newTherapistSubscription.id} created successfully`);

						const newStripePriceId = 
							newTherapistSubscription.subscriptionType === 'monthly'
								? newMonthlyStripePrice.id
								: newTherapistSubscription.subscriptionType === 'yearly'
									? newAnnualStripePrice.id
									: '';

						if (!newStripePriceId) throw new ForbiddenError(`New Stripe Price ID could not be defined for therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}`);

						// update the stripe info
						await StripeInfo.update({
							therapistSubscriptionId: newTherapistSubscription.id,
						}, {
							where: {
								id: thSub.therapistSubscription.stripeInfo.id,
							},
							transaction
						});
						logger.info(`Subscription Plan Update/Notify Job - Stripe info of therapist ID: ${thSub.user.id} - Stripe info ID: ${thSub.therapistSubscription.stripeInfo.id} updated successfully`);

						const thStripeSubscription = await stripe.subscriptions.retrieve(thSub.therapistSubscription.stripeInfo.stripeSubscriptionId);
						if (!thStripeSubscription) throw new ForbiddenError(`Stripe Subscription not found for therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}`);

						await stripe.subscriptions.update(thStripeSubscription.id, {
							proration_behavior: 'none',
							items: [
								{
									id: thStripeSubscription.items.data[0].id,
									price: newStripePriceId,
								},
							],
							metadata: {
								...thStripeSubscription.metadata,
								email: thSub.user.email,
								subscriptionPlanId: thSub.newSubscriptionPlan.id,
								priceUpdatedByAdminAt: new Date().toISOString(),
							},
						});

						// update the next scheduled subscription if needed
						if (thSub.therapistSubscription.stripeInfo.stripeSubscriptionScheduleId && thSub.therapistSubscription.stripeInfo.nextScheduledSubscriptionType) {
							const newSchedulePriceId = thSub.therapistSubscription.stripeInfo.nextScheduledSubscriptionType === 'monthly'
								? newMonthlyStripePrice.id
								: thSub.therapistSubscription.stripeInfo.nextScheduledSubscriptionType === 'yearly'
									? newAnnualStripePrice.id
									: '';

							if (!newSchedulePriceId) throw new ForbiddenError(`New Stripe Price ID could not be defined for scheduled subscription of therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}`);

							const scheduledSubscription = await stripe.subscriptionSchedules.retrieve(thSub.therapistSubscription.stripeInfo.stripeSubscriptionScheduleId);

							await stripe.subscriptionSchedules.update(scheduledSubscription.id, {
								phases: [
									{
										start_date: scheduledSubscription.phases[0].start_date,
										items: [
											{
												price: newSchedulePriceId,
												quantity: 1,
											},
										],
										metadata: {
											...scheduledSubscription.phases[0].metadata,
											email: thSub.user.email,
											subscriptionPlanId: thSub.newSubscriptionPlan.id,
											priceUpdatedByAdminAt: new Date().toISOString(),
										},
										default_payment_method: scheduledSubscription.phases[0].default_payment_method as string,
									},
								],
							});
						}

						await thSub.update({
							isUpdated: true,
							updatedAt: new Date(),
						}, {
							transaction
						});

						await transaction.commit();
					} catch (error) {
						logger.error(`Subscription Plan Update/Notify Job - Error updating subscription of therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}: ${error}`);
						await transaction.rollback();
						await thSub.update({
							isFailed: true,
						});
					}
				}

				// notify the user about the subscription plan update
				if (!thSub.isNotified) {
					try {
						logger.info(`Subscription Plan Update/Notify Job - Preparing plan change mail data for therapist ID: ${thSub.user.id}`);
						const emailData = PlanChangeEmail.compile({
							email: thSub.user.email,
							fullName: capitalizeFirstLetter(`${thSub.user?.firstname} ${thSub.user?.lastname}`),
							oldAnnualPrice: Number(thSub.oldSubscriptionPlan.annualPrice),
							oldMonthlyPrice: Number(thSub.oldSubscriptionPlan.monthlyPrice),
							newAnnualPrice: Number(thSub.newSubscriptionPlan.annualPrice),
							newMonthlyPrice: Number(thSub.newSubscriptionPlan.monthlyPrice),
						});
						logger.info(`Subscription Plan Update/Notify Job - Sending email notification to ${maskEmail(thSub.user.email)}`);
						await mail.sendMail({
							to: emailData.To,
							subject: emailData.Subject,
							html: emailData.HtmlBody,
						});
						logger.info(`Subscription Plan Update/Notify Job - Email notification sent to ${maskEmail(thSub.user.email)}`);
						await thSub.update({
							isNotified: true,
							notifiedAt: new Date(),
						});
					} catch (error) {
						await thSub.update({
							isFailed: true,
						});
						logger.error(`Subscription Plan Update/Notify Job - Error sending email notification to ${maskEmail(thSub.user.email)}: ${error}`);
					}
				}
			} catch (error) {
				await thSub.update({
					isFailed: true,
				});
				logger.error(`Subscription Plan Update/Notify Job - Error updating subscription of therapist ID: ${thSub.user.id} - Subscription ID: ${thSub.therapistSubscription.id}: ${error}`);
			}
		}
	} catch (error) {
		logger.error(`Subscription Plan Update/Notify Job - error: ${error}.`);
	}
}

export default PlanUpdateEmailJob;
