import { expect } from 'chai';
import sinon from 'sinon';
import cron, { ScheduledTask } from 'node-cron';
import dayjs from 'dayjs';
import { Op } from 'sequelize';
import sequelize from '@/src/configs/database.config';
import { mail } from '@/src/configs/sendgrid.config';
import { SubscriptionRenewalEmail } from '../emails/subscription.email';
import logger from '@/src/configs/logger.config';
import { StripeInfo, User, TherapistSubscription, SubscriptionPlan } from '@/src/models';
import { runSubsAutoRenewalEmailJob } from './subscription-auto-renewal-email.job';

describe('Subscription Auto Renewal Email Job', function() {
  this.timeout(10000);

  let sandbox: sinon.SinonSandbox;
  let cronScheduleStub: sinon.SinonStub;
  let mailSendStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let findAllStub: sinon.SinonStub;
  let renewalEmailCompileStub: sinon.SinonStub;

  const now = dayjs().millisecond(0);
  const twentyFourHours = now.add(24, 'hour').toISOString();
  const tenDays = now.add(10, 'day').toISOString();

  const mockPlan = { id: 1, isActive: true, monthlyPrice: 10, annualPrice: 100 };
  const mockTherapist = {
    id: 1,
    firstname: 'John',
    lastname: 'Doe',
    email: '<EMAIL>',
    address: {},
    timeZone: 'America/New_York',
    phone: '+1234567890',
  };
  const mockTherapistSubscription = {
    id: 1,
    subscriptionType: 'monthly',
    subscriptionPlan: mockPlan,
  };
  const mockRenewal = {
    id: 1,
    stripeCancelAtPeriodEnd: false,
    status: 'active',
    stripeCurrentPeriodEnd: now.toISOString(),
    therapist: mockTherapist,
    therapistSubscription: mockTherapistSubscription,
  };

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    cronScheduleStub = sandbox.stub(cron, 'schedule').callsFake((cronTime, callback) => {
      const task = {
        start: () => task,
        stop: () => task,
        now: () => new Date(),
        addListener: () => task,
        on: () => task,
        once: () => task,
        removeListener: () => task,
        off: () => task,
        removeAllListeners: () => task,
        setMaxListeners: () => task,
        getMaxListeners: () => 10,
        listeners: () => [],
        rawListeners: () => [],
        emit: () => true,
        listenerCount: () => 0,
        prependListener: () => task,
        prependOnceListener: () => task,
        eventNames: () => [],
        next: () => new Promise<void>(resolve => resolve())
      } as unknown as ScheduledTask;
      if (typeof callback === 'function') {
        callback('init');
      }
      return task;
    });
    mailSendStub = sandbox.stub(mail, 'sendMail').resolves({ statusCode: 202 } as any);
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    findAllStub = sandbox.stub(StripeInfo, 'findAll').resolves([mockRenewal] as any);
    renewalEmailCompileStub = sandbox.stub(SubscriptionRenewalEmail, 'compile').callsFake((data: any) => ({
      From: '<EMAIL>',
      To: data.email,
      Subject: data.subject,
      HtmlBody: `<div>${data.therapistName || ''}</div>`
    }));
    sandbox.stub(process, 'env').value({ ...process.env, BYPASS_JOBS: 'false', INFO_EMAIL_ADDRESS: '<EMAIL>' });
    sandbox.stub(dayjs, 'tz').callsFake((date: any, tz?: any) => {
      const realDayjs = dayjs(date);
      const origFormat = realDayjs.format.bind(realDayjs);
      (realDayjs as any).format = (fmt: string) => {
        if (fmt === 'MMMM D, YYYY h:mm A') return 'June 24, 2023 2:00 PM';
        return origFormat(fmt);
      };
      (realDayjs as any).fromNow = () => 'in 1 day';
      return realDayjs;
    });
  });

  afterEach(function() {
    sandbox.restore();
    renewalEmailCompileStub?.restore();
  });

  it('should not notify if no renewals found', async function() {
    findAllStub.resolves([]);
    await runSubsAutoRenewalEmailJob();
    expect(mailSendStub.called).to.be.false;
  });

  it('should send renewal email for valid renewal', async function() {
    await runSubsAutoRenewalEmailJob();
    expect(mailSendStub.calledOnce).to.be.true;
    const emailOptions = mailSendStub.firstCall.args[0];
    expect(emailOptions.to).to.equal(mockTherapist.email);
    expect(emailOptions.subject).to.include('Subscription Renewal');
  });

  it('should handle error in mail.sendMail gracefully', async function() {
    mailSendStub.rejects(new Error('Mail error'));
    await runSubsAutoRenewalEmailJob();
    expect(loggerErrorStub.called).to.be.true;
    const errorMsg = loggerErrorStub.getCalls().map(c => c.args[0]).join(' ');
    expect(errorMsg).to.include('Therapist Notify error');
  });

  it('should respect BYPASS_JOBS environment variable', async function() {
    sandbox.stub(process, 'env').value({ ...process.env, BYPASS_JOBS: 'true' });
    await runSubsAutoRenewalEmailJob();
    expect(findAllStub.called).to.be.false;
  });

  it('should handle inactive plan gracefully', async function() {
    const renewal = { ...mockRenewal, therapistSubscription: { ...mockTherapistSubscription, subscriptionPlan: { ...mockPlan, isActive: false } } };
    findAllStub.resolves([renewal] as any);
    await runSubsAutoRenewalEmailJob();
    expect(loggerErrorStub.called).to.be.true;
    const errorMsg = loggerErrorStub.getCalls().map(c => c.args[0]).join(' ');
    expect(errorMsg).to.include('not active');
  });

  it('should handle invalid plan gracefully', async function() {
    const renewal = { ...mockRenewal, therapistSubscription: { ...mockTherapistSubscription, subscriptionPlan: { ...mockPlan }, subscriptionType: 'other' } };
    findAllStub.resolves([renewal] as any);
    await runSubsAutoRenewalEmailJob();
    expect(loggerErrorStub.called).to.be.true;
    const errorMsg = loggerErrorStub.getCalls().map(c => c.args[0]).join(' ');
    expect(errorMsg).to.include('Invalid subscription plan');
  });
}); 