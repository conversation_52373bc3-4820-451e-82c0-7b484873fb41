import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import { NotifySubscriptionUpdate, StripeInfo, TherapistSubscription } from '@/src/models';
import { expect } from 'chai';
import { mail } from '@/src/configs/sendgrid.config';
import { runPlanUpdateEmailJob } from './plan-update-email.job';
import stripe from '@/src/configs/stripe-new.config';
import sequelize from '@/src/configs/database.config';

describe('Plan Update Job Test', function () {
	let sandbox: sinon.SinonSandbox;

	let notifySubsUpdateFindAllStub: sinon.SinonStub;
	let therapistSubsCreateStub: sinon.SinonStub;
	let therapistSubsUpdateStub: sinon.SinonStub;
	let stripeInfoUpdateStub: sinon.SinonStub;

	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	let stripePricesCreateStub: sinon.SinonStub;
	let stripeSubscriptionsRetrieveStub: sinon.SinonStub;
	let stripeSubscriptionsUpdateStub: sinon.SinonStub;
	let stripeSubsScheduleRetrieveStub: sinon.SinonStub;
	let stripeSubsScheduleUpdateStub: sinon.SinonStub;

	let sendEmailStub: sinon.SinonStub;

	let dummyNotifySubsUpdate = {
		id: 1,
		userId: 1,
		newSubscriptionPlanId: 2,
		oldSubscriptionPlanId: 1,
		therapistSubscriptionId: 1,
		isUpdated: false,
		isNotified: false,
		user: {
			id: 1,
			email: '<EMAIL>',
			firstname: 'John',
			lastname: 'Doe',
		},
		oldSubscriptionPlan: {
			id: 1,
			annualPrice: 120,
			monthlyPrice: 12,
		},
		newSubscriptionPlan: {
			id: 2,
			annualPrice: 240,
			monthlyPrice: 24,
		},
		therapistSubscription: {
			id: 1,
			subscriptionType: 'monthly',
			stripeInfo: {
				id: 1,
				stripeSubscriptionId: 'sub_12345',
				stripeSubscriptionScheduleId: 'sub_sched_12345',
				nextScheduledSubscriptionType: 'yearly',
			},
		},
		update: () => Promise.resolve(),
	}

	let dummyStripePrice = {
		id: 'price_123456',
		unit_amount: 1000,
		currency: 'usd',
	}

	let dummyTherapistSubscription = {
		id: 1,
		subscriptionPlanId: 1,
		therapistId: 2,
		subscriptionType: 'monthly',
	}

	let dummyStripeSubscription = {
		id: 'sub_123456',
		items: {
			data: [
				{
					id: 'si_123456',
				}
			]
		},
		metadata: {
			subscriptionType: 'monthly',
		}
	}

	let dummyStripeSubscriptionSchedule = {
		id: 'sub_schedule_123456',
		phases: [
			{
				start_date: 1324654,
				metadata: {
					subscriptionType: 'yearly',
				},
				default_payment_method: 'pm_123456',
			}
		]
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		notifySubsUpdateFindAllStub = sandbox.stub(NotifySubscriptionUpdate, 'findAll');
		therapistSubsCreateStub = sandbox.stub(TherapistSubscription, 'create');
		therapistSubsUpdateStub = sandbox.stub(TherapistSubscription, 'update');
		stripeInfoUpdateStub = sandbox.stub(StripeInfo, 'update');

		commitStub = sandbox.stub().resolves();
		rollbackStub = sandbox.stub().resolves();
		sandbox.stub(sequelize, 'transaction').resolves({ 
			commit: commitStub,
			rollback: rollbackStub
		} as any);

		stripePricesCreateStub = sandbox.stub(stripe.prices, 'create');
		stripeSubscriptionsRetrieveStub = sandbox.stub(stripe.subscriptions, 'retrieve');
		stripeSubscriptionsUpdateStub = sandbox.stub(stripe.subscriptions, 'update');
		stripeSubsScheduleRetrieveStub = sandbox.stub(stripe.subscriptionSchedules, 'retrieve');
		stripeSubsScheduleUpdateStub = sandbox.stub(stripe.subscriptionSchedules, 'update');

		sendEmailStub = sandbox.stub(mail, 'sendMail').resolves();
	});

	afterEach(function() {
		process.env.BYPASS_JOBS = "false";
		sandbox.restore();
	});

	it('should not run if BYPASS_JOBS is true', async () => {
		process.env.BYPASS_JOBS = "true";

		await runPlanUpdateEmailJob();

		expect(notifySubsUpdateFindAllStub.notCalled).to.be.true;
	});

	it('should not run if no subscription updates are pending', async () => {
		notifySubsUpdateFindAllStub.resolves([]);

		await runPlanUpdateEmailJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	// ! for below test cases, we are only assuming for one subscription update,
	// ! in real scenario, if there are multiple updates and one fails, others should still be processed

	it('should not run if therapist subscription is not found', async () => {
		notifySubsUpdateFindAllStub.resolves([{
			...dummyNotifySubsUpdate,
			therapistSubscription: null,
		}]);

		await runPlanUpdateEmailJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should not run if new subscription plan is not found', async () => {
		notifySubsUpdateFindAllStub.resolves([{
			...dummyNotifySubsUpdate,
			newSubscriptionPlan: null,
		}]);

		await runPlanUpdateEmailJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should not run if old subscription plan is not found', async () => {
		notifySubsUpdateFindAllStub.resolves([{
			...dummyNotifySubsUpdate,
			oldSubscriptionPlan: null,
		}]);

		await runPlanUpdateEmailJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should not run if stripe prices are not available', async () => {
		notifySubsUpdateFindAllStub.resolves([dummyNotifySubsUpdate]);
		stripePricesCreateStub.resolves(null);

		await runPlanUpdateEmailJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should not run if stripe info is not available', async () => {
		notifySubsUpdateFindAllStub.resolves([{
			...dummyNotifySubsUpdate,
			therapistSubscription: {
				...dummyNotifySubsUpdate.therapistSubscription,
				stripeInfo: null,
			},
		}]);
		stripePricesCreateStub.resolves(dummyStripePrice);

		await runPlanUpdateEmailJob();

		expect(sendEmailStub.notCalled).to.be.true;
	});

	it('should not update if new stripe price id is not available', async () => {
		notifySubsUpdateFindAllStub.resolves([dummyNotifySubsUpdate]);
		stripePricesCreateStub.resolves(dummyStripePrice);
		therapistSubsUpdateStub.resolves();
		therapistSubsCreateStub.resolves({
			...dummyTherapistSubscription,
			subscriptionType: 'random',
		});

		await runPlanUpdateEmailJob();

		expect(stripeInfoUpdateStub.notCalled).to.be.true;
		expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
		expect(stripeSubsScheduleUpdateStub.notCalled).to.be.true;
		expect(rollbackStub.calledOnce).to.be.true;
	});

	it('should not update if stripe subscription is not found', async () => {
		notifySubsUpdateFindAllStub.resolves([dummyNotifySubsUpdate]);
		stripePricesCreateStub.resolves(dummyStripePrice);
		therapistSubsUpdateStub.resolves();
		therapistSubsCreateStub.resolves(dummyTherapistSubscription);
		stripeInfoUpdateStub.resolves();
		stripeSubscriptionsRetrieveStub.resolves(null);

		await runPlanUpdateEmailJob();

		expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
		expect(stripeSubsScheduleUpdateStub.notCalled).to.be.true;
		expect(rollbackStub.calledOnce).to.be.true;
	});

	it('should not update if new scheduled price id is not found', async () => {
		notifySubsUpdateFindAllStub.resolves([{
			...dummyNotifySubsUpdate,
			therapistSubscription: {
				...dummyNotifySubsUpdate.therapistSubscription,
				stripeInfo: {
					...dummyNotifySubsUpdate.therapistSubscription.stripeInfo,
					nextScheduledSubscriptionType: 'random',
				},
			},
		}]);
		stripePricesCreateStub.resolves(dummyStripePrice);
		therapistSubsUpdateStub.resolves();
		therapistSubsCreateStub.resolves(dummyTherapistSubscription);
		stripeInfoUpdateStub.resolves();
		stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);

		await runPlanUpdateEmailJob();

		expect(stripeSubsScheduleRetrieveStub.notCalled).to.be.true;
		expect(stripeSubsScheduleUpdateStub.notCalled).to.be.true;
		expect(rollbackStub.calledOnce).to.be.true;
	});

	it('should update subscription and send email successfully', async () => {
		notifySubsUpdateFindAllStub.resolves([dummyNotifySubsUpdate]);
		stripePricesCreateStub.resolves(dummyStripePrice);
		therapistSubsUpdateStub.resolves();
		therapistSubsCreateStub.resolves(dummyTherapistSubscription);
		stripeInfoUpdateStub.resolves();
		stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
		stripeSubscriptionsUpdateStub.resolves();
		stripeSubsScheduleRetrieveStub.resolves(dummyStripeSubscriptionSchedule);
		stripeSubsScheduleUpdateStub.resolves();

		await runPlanUpdateEmailJob();

		expect(commitStub.calledOnce).to.be.true;
		expect(rollbackStub.notCalled).to.be.true;
		expect(sendEmailStub.calledOnce).to.be.true;
	});
});