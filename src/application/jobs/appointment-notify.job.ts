import { mail } from '@/src/configs/sendgrid.config';
import { Appointments, MinorPatient, User, UserDevice, UserRegistrationInfo } from '@/src/models';
import cron from 'node-cron';
import dayjs from 'dayjs';
import { Op } from 'sequelize';
import { PatientAppointmentNotifyEmail, TherapistAppointmentNotifyEmail } from '../emails/appointment-notify.email';
import timezone from "dayjs/plugin/timezone";
import { capitalizeFirstLetter, formatDuration, getTimezoneAbbr, isValidTimeZone, maskEmail, maskPhoneNumber } from '../helpers/general.helper';
import { sendSms } from '@/src/configs/twilio.config';
import { PracticeInfo, WaitlistNotifications } from '@/src/types/registration.interface';
import logger from '@/src/configs/logger.config';
import admin from '@/src/configs/firebase.config';

dayjs.extend(timezone);

const AppointmentNotifyJob = () => {
	cron.schedule("*/5 * * * *", runAppointmentNotifyJob);
};

export const runAppointmentNotifyJob = async () => {
	try {
		if (process.env.BYPASS_JOBS === "true") return;
		logger.info(`Appointment Notify Job - Checking for appointments to notify`);
		const now = dayjs().millisecond(0);
		const twoHours = now.add(2, 'hour').toISOString();
		const twentyFourHours = now.add(24, 'hour').toISOString();

		const appointments = await Appointments.findAll({
			where: {
				[Op.or]: [
					{ appointmentDate: twoHours },
					{ appointmentDate: twentyFourHours },
				],
				isBilled: true,
				cancelledAt: null,
			},
			include: [
				{
					model: User,
					as: 'therapist',
					attributes: ['id', 'firstname', 'lastname', 'email', 'address', 'timeZone', 'phone'],
					include: [
						{
							model: UserRegistrationInfo,
							where: {
								[Op.or]: [
									{ pageName: 'waitlist-notifications' },
									{ pageName: 'practice-info' }
								]
							},
							required: false,
						}
					]
				},
				{
					model: User,
					as: 'patient',
					attributes: ['id', 'firstname', 'lastname', 'email', 'timeZone', 'phone'],
					include: [
						{
							model: UserDevice,
							attributes: ['deviceId', 'deviceType']
						}
					]
				},
				{
					model: MinorPatient,
					as: 'minorPatient',
				}
			],
		});

		if (appointments.length === 0) {
			logger.info(`Appointment Notify Job - No appointments found to process`);
			return;
		}

		logger.info(`Starting Appointment Notify Job - Found ${appointments.length} appointments to process`);

		for (const appointment of appointments) {
			logger.info(`Appointment Notify Job - Processing appointment ID: ${appointment.id}`);
			const therapistFullName = capitalizeFirstLetter(`${appointment.therapist?.firstname} ${appointment.therapist?.lastname}`);
			let patientFullName = capitalizeFirstLetter(`${appointment.patient?.firstname} ${appointment.patient?.lastname}`);

			if (appointment.isMinor && appointment.minorId && appointment.minorPatient) {
				const minorPatient = appointment.minorPatient;
				if (minorPatient.firstName) {
					patientFullName = capitalizeFirstLetter(`${minorPatient.firstName} ${minorPatient?.lastName}`);
				}
			}
			let appointmentLocation: string | null = null;
			if (appointment.type === 'in-person') {
				appointmentLocation = (appointment.therapist.address as any)?.full_address || null;

				if (!appointmentLocation) {
					const therapistPracticeInfo = appointment.therapist?.registrationInfo?.find(
						(info) => info.pageName === 'practice-info'
					)?.payloadInfo as unknown as PracticeInfo;

					if (therapistPracticeInfo && therapistPracticeInfo.business_address) {
						appointmentLocation = therapistPracticeInfo.business_address?.full_address || null;
					}	
				}
			}
			// Notify therapist
			try {
				const therapistTimeZone = appointment.therapist.timeZone;

				if (therapistTimeZone && isValidTimeZone(therapistTimeZone)) {
					logger.info(`Appointment Notify Job - Preparing Appointment Notification data for appointment ID: ${appointment.id} - Therapist ID: ${appointment.therapist.id}`);
					const therapistAppDate = dayjs.tz(appointment.appointmentDate, therapistTimeZone).format('MMMM D, YYYY');
					const therapistAppTime = dayjs.tz(appointment.appointmentDate, therapistTimeZone).format('h:mm A');
					const therapistTimeZoneAbbr = getTimezoneAbbr(therapistTimeZone);

					const therapistNotifInfo = appointment.therapist?.registrationInfo?.find(
						(info) => info.pageName === 'waitlist-notifications'
					)?.payloadInfo as unknown as WaitlistNotifications;

					let therapistPhone = appointment.therapist?.phone;
					if (therapistNotifInfo && therapistNotifInfo?.notification_preferences?.includes('text') && therapistNotifInfo.phone_number) {
						therapistPhone = therapistNotifInfo.phone_number;
					}

					// Email Notification
					const shouldSendEmail = therapistNotifInfo ? therapistNotifInfo?.notification_preferences?.includes('email') : false;
					if (shouldSendEmail) {
						try {
							logger.info(`Preparing Appointment Notification email data for appointment ID: ${appointment.id} - Therapist ID: ${appointment.therapist.id}`);
							const therapistEmailData = TherapistAppointmentNotifyEmail.compile({
								email: appointment.therapist.email,
								subject: `Appointment Reminder: ${patientFullName} on ${therapistAppDate} at ${therapistAppTime}`,
								therapistName: therapistFullName,
								patientName: patientFullName,
								appointmentDate: therapistAppDate,
								appointmentTime: `${therapistAppTime} (${therapistTimeZoneAbbr})`,
								duration: formatDuration(appointment.duration),
								location: appointmentLocation || '',
								title: `This is a reminder of your upcoming appointment with ${patientFullName}:`,
								appointmentType: capitalizeFirstLetter(appointment.type),
								header: `Appointment Reminder With ${patientFullName}`
							});
							logger.info(`Sending Appointment Notification Email to ${maskEmail(appointment.therapist.email)}`)
							await mail.sendMail({
								to: therapistEmailData.To,
								subject: therapistEmailData.Subject,
								html: therapistEmailData.HtmlBody,
							});
							logger.info(`Appointment Notification Email sent to ${maskEmail(appointment.therapist.email)}`)
						} catch (error) {
							logger.error(`Appointment Notify Job - Therapist Email error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
						}
					}
					
					const shouldSendSms = therapistNotifInfo ? therapistNotifInfo?.notification_preferences?.includes('text') : false;
					// SMS Notification
					if (shouldSendSms && therapistPhone) {
						try {
							logger.info(`Sending Appointment Notification SMS to ${maskPhoneNumber(therapistPhone)}`)
							await sendSms(
								therapistPhone,
								`NextTherapist - Appointment Reminder: Your have an upcoming appointment with ${patientFullName} on ${therapistAppDate} at ${therapistAppTime}`
							)
							logger.info(`Appointment Notification SMS sent to ${maskPhoneNumber(therapistPhone)}`)
						} catch (error) {
							logger.error(`Appointment Notify Job - Therapist SMS error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
						}
					}
				}
			} catch (error) {
				logger.error(`Appointment Notify Job - Therapist Notify error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
			}

			// Notify patient
			try {
				const patientTimeZone = appointment.patient.timeZone;

				if (patientTimeZone && isValidTimeZone(patientTimeZone)) {
					logger.info(`Appointment Notify Job - Preparing Appointment Notification data for appointment ID: ${appointment.id} - Patient ID: ${appointment.patient.id}`);
					const patientAppDate = dayjs.tz(appointment.appointmentDate, patientTimeZone).format('MMMM D, YYYY');
					const patientAppTime = dayjs.tz(appointment.appointmentDate, patientTimeZone).format('h:mm A');
					const patientTimeZoneAbbr = getTimezoneAbbr(patientTimeZone);
					const patientDevices = appointment.patient?.userDevices || []
					const patientPhone = appointment.patient?.phone;

					// FCM Notification
					if (Array.isArray(patientDevices) && patientDevices.length > 0) {
						for (const device of patientDevices) {
							if (!device.deviceId) continue;

							const payload = {
								token: device.deviceId,
								notification: {
									title: "NextTherapist - Appointment Reminder",
									body: `You have an upcoming appointment with ${therapistFullName} on ${patientAppDate} at ${patientAppTime}`
								},
								android: {
									priority: 'high' as 'high' | 'normal' | undefined
								}
							};

							try {
								logger.info(`Sending FCM notification to patient ID: ${appointment.patient.id}, device ID: [MASKED-${device.deviceId.slice(-4)}]`);
								await admin.messaging().send(payload);
								logger.info(`FCM notification sent to patient ID: ${appointment.patient.id}, device ID: [MASKED-${device.deviceId.slice(-4)}]`);
							} catch (error) {
								logger.error(`FCM notification error - Patient ID: (${appointment.patient.id}) - Device ID: (${device.deviceId}) - Appointment ID: (${appointment.id}): ${error}`);
							}
						}
					}
					// Email Notification
					try {
						logger.info(`Preparing Appointment Notification email data for appointment ID: ${appointment.id} - Patient ID: ${appointment.patient.id}`);
						const patientEmailData = PatientAppointmentNotifyEmail.compile({
							email: appointment.patient.email,
							subject: `Appointment Reminder: ${therapistFullName} on ${patientAppDate} at ${patientAppTime}`,
							therapistName: therapistFullName,
							patientName: patientFullName,
							appointmentDate: patientAppDate,
							appointmentTime: `${patientAppTime} (${patientTimeZoneAbbr})`,
							duration: formatDuration(appointment.duration),
							location: appointmentLocation || '',
							title: `This is a reminder of your upcoming appointment with ${therapistFullName}:`,
							appointmentType: capitalizeFirstLetter(appointment.type),
							header: `Appointment Reminder With ${therapistFullName}`
						});

						logger.info(`Sending Appointment Notification Email to ${maskEmail(appointment.patient.email)}`);
						await mail.sendMail({
							to: patientEmailData.To,
							subject: patientEmailData.Subject,
							html: patientEmailData.HtmlBody,
						});
						logger.info(`Appointment Notification Email sent to ${maskEmail(appointment.patient.email)}`);
					} catch (error) {
						logger.error(`Appointment Notify Job - Patient Email error - Patient ID: (${appointment.patient.id}) - Appointment ID: (${appointment.id}): ${error}.`);
					}

					// SMS Notification
					if (patientPhone) {
						try {
							logger.info(`Sending Appointment Notification SMS to ${maskPhoneNumber(patientPhone)}`)
							await sendSms(
								patientPhone,
								`NextTherapist - Appointment Reminder: You have an upcoming appointment with ${therapistFullName} on ${patientAppDate} at ${patientAppTime}`
							)
							logger.info(`Appointment Notification SMS sent to ${maskPhoneNumber(patientPhone)}`)
						} catch (error) {
							logger.error(`Appointment Notify Job - Patient SMS error - Patient ID: (${appointment.patient.id}) - Appointment ID: (${appointment.id}): ${error}.`);
						}
					}
				}
			} catch (error) {
				logger.error(`Appointment Notify Job - Patient Notify error - Patient ID: (${appointment.patient.id}) - Appointment ID: (${appointment.id}): ${error}.`);
			}
		}

		logger.info(`Appointment Notify Job - Appointment Notifications Sent`)
	} catch (error) {
		logger.error(`Appointment Notify Job - error: ${error}.`);
	}
}

export default AppointmentNotifyJob;
