import { expect } from 'chai';
import sinon from 'sinon';
import * as mailModule from '@/src/configs/sendgrid.config';
import * as smsModule from '@/src/configs/twilio.config';
import * as job from '../../../src/application/jobs/license-expiration-notify.job';
import { User } from '@/src/models';
import moment from 'moment';
import logger from '@/src/configs/logger.config';

const makeTherapist = (daysLeft: number, prefs: { email?: boolean; sms?: boolean }) => {
  const expiryDate = moment().add(daysLeft, 'days').format('MM-DD-YYYY');
  return {
    id: 1,
    email: '<EMAIL>',
    firstname: '<PERSON>',
    lastname: 'Doe',
    registrationInfo: [
      {
        pageName: 'license',
        payloadInfo: {
          licenses: [
            {
              license_state: { label: 'CA' },
              expiry_date: expiryDate
            }
          ]
        }
      },
      {
        pageName: 'waitlist-notifications',
        payloadInfo: {
          notification_preferences: [
            ...(prefs.email ? ['email'] : []),
            ...(prefs.sms ? ['text'] : [])
          ],
          phone_number: prefs.sms ? '+**********' : undefined
        }
      }
    ]
  };
};

describe('License Expiration Notify Job', () => {
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('sends reminder email and SMS for NOTIFY_DAYS', async () => {
    const sendMailStub = sandbox.stub(mailModule.mail, 'sendMail').resolves();
    const sendSmsStub = sandbox.stub(smsModule, 'sendSms').resolves();
    sandbox.stub(User, 'findAll').resolves([
      makeTherapist(10, { email: true, sms: true })
    ] as any);

    await job['checkExpiringLicenses']();

    expect(sendMailStub.calledOnce).to.be.true;
    expect(sendSmsStub.calledOnce).to.be.true;
  });

  it('sends only email if SMS not enabled', async () => {
    const sendMailStub = sandbox.stub(mailModule.mail, 'sendMail').resolves();
    const sendSmsStub = sandbox.stub(smsModule, 'sendSms').resolves();
    sandbox.stub(User, 'findAll').resolves([
      makeTherapist(3, { email: true, sms: false })
    ] as any);

    await job['checkExpiringLicenses']();

    expect(sendMailStub.calledOnce).to.be.true;
    expect(sendSmsStub.notCalled).to.be.true;
  });

  it('sends only SMS if email not enabled', async () => {
    const sendMailStub = sandbox.stub(mailModule.mail, 'sendMail').resolves();
    const sendSmsStub = sandbox.stub(smsModule, 'sendSms').resolves();
    sandbox.stub(User, 'findAll').resolves([
      makeTherapist(2, { email: false, sms: true })
    ] as any);

    await job['checkExpiringLicenses']();

    expect(sendMailStub.notCalled).to.be.true;
    expect(sendSmsStub.calledOnce).to.be.true;
  });

  it('sends separate expired email and SMS on 0 days left', async () => {
    const sendMailStub = sandbox.stub(mailModule.mail, 'sendMail').resolves();
    const sendSmsStub = sandbox.stub(smsModule, 'sendSms').resolves();
    sandbox.stub(User, 'findAll').resolves([
      makeTherapist(0, { email: true, sms: true })
    ] as any);

    await job['checkExpiringLicenses']();

    expect(sendMailStub.calledOnce).to.be.true;
    expect(sendSmsStub.calledOnce).to.be.true;
  });

  it('handles no licenses gracefully', async () => {
    sandbox.stub(mailModule.mail, 'sendMail').resolves();
    sandbox.stub(smsModule, 'sendSms').resolves();
    sandbox.stub(User, 'findAll').resolves([
      {
        id: 2,
        registrationInfo: [
          {
            pageName: 'license',
            payloadInfo: { licenses: [] }
          }
        ]
      }
    ] as any);

    await job['checkExpiringLicenses']();
  });
});