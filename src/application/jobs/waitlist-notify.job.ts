import logger from '@/src/configs/logger.config'
import { Appointments, Calendar, NormalOfficeHours, TherapistWaitlist, User, UserDevice, UserRegistrationInfo } from '@/src/models'
import cron from 'node-cron'
import { UserType } from '../helpers/constant.helper'
import { Op } from 'sequelize'
import dayjs from 'dayjs'
import { capitalizeFirstLetter, getTimezoneAbbr, maskEmail, maskPhoneNumber } from '../helpers/general.helper'
import timezone from 'dayjs/plugin/timezone'
import { WaitlistNotifyEmail } from '../emails/waitlist-notify.email'
import { mail } from '@/src/configs/sendgrid.config'
import { sendSms } from '@/src/configs/twilio.config'
import admin from '@/src/configs/firebase.config'
import { getAvailableOfficeHours } from '../helpers/availability.helper'
import { WaitlistNotifications } from '@/src/types/registration.interface'
import { FormattedOfficeHours } from '../helpers/types.helper'

dayjs.extend(timezone)

const WaitlistNotifyJob = () => {
	cron.schedule('*/5 * * * *', runWaitlistNotifyJob);
}

export const runWaitlistNotifyJob = async () => {
	try {
		if (process.env.BYPASS_JOBS === 'true') return

		logger.info(`WaitlistNotifyJob: Running job to notify patients on waitlist`)

		const therapistList = await User.findAll({
			where: {
				role: UserType.THERAPIST,
			},
			attributes: ['id', 'email', 'firstname', 'lastname', 'timeZone'],
			include: [
				{
					model: NormalOfficeHours,
					attributes: ['id', 'workingDay', 'availabilityHours', 'appointmentMethod'],
					where: {
						isDisabled: false,
						deletedAt: null,
					},
					required: false,
				},
				{
					model: Appointments,
					as: 'therapistAppointments',
					attributes: ['id', 'workingHourId', 'appointmentDate', 'duration'],
					where: {
						cancelledAt: null,
						appointmentDate: {
							[Op.gte]: dayjs().toDate(),
						},
					},
					required: false,
				},
				{
					model: Calendar,
					attributes: ['type', 'credentials'],
				},
				{
					model: UserRegistrationInfo,
					attributes: ['pageName', 'payloadInfo'],
					where: {
						pageName: 'waitlist-notifications',
					},
					required: false,
				},
				{
					model: TherapistWaitlist,
					where: {
						status: {
							[Op.in]: ['waiting', 'notified'],
						},
					},
					required: true,
					include: [
						{
							model: User,
							as: 'patient',
							attributes: ['id', 'email', 'firstname', 'lastname', 'timeZone'],
							include: [
								{
									model: UserDevice,
									as: 'userDevices',
									attributes: ['deviceId', 'deviceType'],
								},
							],
						},
					],
				},
			],
		});

		if (!therapistList || therapistList.length === 0) {
			logger.info(`WaitlistNotifyJob: No patients on waitlist`)
			return
		}
		logger.info(`WaitlistNotifyJob: Found ${therapistList.length} therapists with patients on waitlist`)
		for (const therapist of therapistList) {
			try {
				const waitlistNotifInfo = (therapist.registrationInfo.find((info) => info.pageName === 'waitlist-notifications')?.payloadInfo as unknown as WaitlistNotifications) || null

				const availableHoursList = await getAvailableOfficeHours({
					therapistId: therapist.id,
					officeHours: therapist.normalOfficeHours,
					appointments: therapist.therapistAppointments,
					therapistTimeZone: therapist.timeZone,
					calendars: therapist.calendars,
					waitlistNotifInfo,
					returnAvailableHours: true,
				})

				if (Array.isArray(availableHoursList) && availableHoursList.length > 0) {
					for (const waitlist of therapist.therapistWaitlist) {
						try {
							// Notify the patient
							const patientTimeZone = waitlist.patient?.timeZone || 'UTC'
							const therapistFullName = capitalizeFirstLetter(`${therapist?.firstname} ${therapist?.lastname}`)
							const patientFullName = capitalizeFirstLetter(`${waitlist?.patient?.firstname} ${waitlist.patient?.lastname}`)
							const patientTimeZoneAbbr = getTimezoneAbbr(patientTimeZone)
							const patientDevices = waitlist.patient?.userDevices || []
							const patientPhone = waitlist.phoneNumber

							const nowInPatientTZ = dayjs().tz(patientTimeZone)

							const notifiedAt = waitlist.notifiedAt ? dayjs(waitlist.notifiedAt).tz(patientTimeZone) : null

							const deepLinkUrl = `${process.env.FRONTEND_URL}/therapist/profile-schedule/${therapist.id}`

							// Skip if patient was already notified today
							if (notifiedAt && notifiedAt.isSame(nowInPatientTZ, 'day')) {
								logger.info(`WaitlistNotifyJob: Skipping patient ID - ${waitlist.patient.id} already notified today`)
								continue
							}

							// Skip if current time in patient's timezone is before 6 AM
							if (nowInPatientTZ.hour() < 6 || nowInPatientTZ.hour() > 8) {
								logger.info(`WaitlistNotifyJob: Skipping patient ID - ${waitlist.patient.id} due to time < 6AM or > 8AM in their timezone`)
								continue
							}

							const firstThreeHours = availableHoursList.slice(0, 3)
							const remainingCount = availableHoursList.length > 3 ? availableHoursList.length - 3 : null

							const appointmentSlots = firstThreeHours.map((availableHour: FormattedOfficeHours) => ({
								date: dayjs(availableHour.dateTimeUTC).tz(patientTimeZone).format('MMMM D, YYYY'),
								time: `${dayjs(availableHour.dateTimeUTC).tz(patientTimeZone).format('h:mm A')} (${patientTimeZoneAbbr})`,
							}))

							// Email notification
							if (waitlist.notificationPreferences.includes('email')) {
								try {
									const emailData = WaitlistNotifyEmail.compile({
										email: waitlist.patient.email,
										subject: `Appointment Available with ${therapistFullName}`,
										therapistName: therapistFullName,
										patientName: patientFullName,
										appointmentSlots,
										remainingSlotsCount: remainingCount,
										deepLinkUrl,
									})

									logger.info(`WaitlistNotifyJob: Sending email to ${maskEmail(waitlist.patient.email)}`)
									await mail.sendMail({
										to: emailData.To,
										subject: emailData.Subject,
										html: emailData.HtmlBody,
									})
									logger.info(`WaitlistNotifyJob: Email sent to ${maskEmail(waitlist.patient.email)}`)

									await TherapistWaitlist.update(
										{
											status: 'notified',
											notifiedAt: new Date(),
										},
										{
											where: {
												id: waitlist.id,
											},
										}
									)
								} catch (error) {
									logger.error(`WaitlistNotifyJob: Error while sending email to patient ID - ${waitlist.patient.id} - waitlist ID - ${waitlist.id}: ${error}`)
								}
							}

							// SMS notification
							if (waitlist.notificationPreferences.includes('sms') && patientPhone) {
								try {
									logger.info(`WaitlistNotifyJob: Sending SMS to ${maskPhoneNumber(patientPhone)}`)
									await sendSms(patientPhone, `Hi ${patientFullName}, a session with ${therapistFullName} is now available. You joined the waitlist, so you’re first to know. Book now: ${deepLinkUrl}`)
									logger.info(`WaitlistNotifyJob: SMS sent to ${maskPhoneNumber(patientPhone)}`)

									await TherapistWaitlist.update(
										{
											status: 'notified',
											notifiedAt: new Date(),
										},
										{
											where: {
												id: waitlist.id,
											},
										}
									)
								} catch (error) {
									logger.error(`WaitlistNotifyJob: Error while sending SMS to patient ID - ${waitlist.patient.id} - waitlist ID - ${waitlist.id}: ${error}`)
								}
							}

							// FCM notification
							if (waitlist.notificationPreferences.includes('fcm') && Array.isArray(patientDevices) && patientDevices.length > 0) {
								const fcmPayload = {
									notification: {
										title: 'NextTherapist - Appointment Available',
										body: `A session with ${therapistFullName} has become available. You joined the waitlist, so you’re first to know. Book now via your NextTherapist account.`,
									},
									android: {
										priority: 'high' as const,
									},
									data: {
										therapistId: therapist.id.toString(),
										navigateTo: 'profile-schedule',
									},
								}

								for (const device of patientDevices) {
									const deviceId = device.deviceId
									if (!deviceId) continue

									try {
										logger.info(`WaitlistNotifyJob: Sending FCM notification to device ID - [MASKED-${deviceId.slice(-4)}] for patient ID - ${waitlist.patient.id}`)

										await admin.messaging().send({
											...fcmPayload,
											token: deviceId,
										})

										logger.info(`WaitlistNotifyJob: FCM notification sent to device ID - [MASKED-${deviceId.slice(-4)}] for patient ID - ${waitlist.patient.id}`)
									} catch (error) {
										logger.error(`WaitlistNotifyJob: Error sending FCM to device ID - [MASKED-${deviceId.slice(-4)}], patient ID - ${waitlist.patient.id}, waitlist ID - ${waitlist.id}: ${error}`)
									}
								}

								await TherapistWaitlist.update(
									{
										status: 'notified',
										notifiedAt: new Date(),
									},
									{
										where: { id: waitlist.id },
									}
								)
							}
						} catch (error) {
							logger.error(`WaitlistNotifyJob: Error while processing waitlist ID - ${waitlist.id}: ${error}`)
						}
					}
				}
			} catch (error) {
				logger.error(`WaitlistNotifyJob: Error while processing waitlist patients for therapist ID - ${therapist.id}: ${error}`)
			}
		}
	} catch (error) {
		logger.error(`WaitlistNotifyJob: Error while running job: ${error}`)
	}
}

export default WaitlistNotifyJob
