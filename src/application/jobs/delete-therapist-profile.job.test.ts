import sinon from 'sinon';
import { expect, use } from 'chai';
import { Appointments, User } from '@/src/models';
import { runUserCleanupJob } from '@/src/application/jobs/delete-therapist-profile.job';
import * as calendarRepository from '@/src/application/repositories/calendar.repository';

describe('Delete Therapist Profile Job Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindAllStub: sinon.SinonStub;
	let userDestroyStub: sinon.SinonStub;
	let appointmentsFindAllStub: sinon.SinonStub;
	let appointmentsDestroyStub: sinon.SinonStub;

	let deleteGoogleEventStub: sinon.SinonStub;
	let deleteOutlookEventStub: sinon.SinonStub;

	let transactionStub: sinon.SinonStub;
	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindAllStub = sandbox.stub(User, 'findAll');
		userDestroyStub = sandbox.stub(User, 'destroy');
		appointmentsFindAllStub = sandbox.stub(Appointments, 'findAll');
		appointmentsDestroyStub = sandbox.stub(Appointments, 'destroy');

		deleteGoogleEventStub = sandbox.stub(calendarRepository, 'deleteGoogleEvent').resolves();
		deleteOutlookEventStub = sandbox.stub(calendarRepository, 'deleteOutlookEvent').resolves();

		commitStub = sinon.stub().resolves();
		rollbackStub = sinon.stub().resolves();

		const fakeTransaction = {
			commit: commitStub,
			rollback: rollbackStub,
		};
		transactionStub = sandbox.stub(User.sequelize as any, 'transaction').resolves(fakeTransaction);
	});

	afterEach(function() {
		sandbox.restore();
	});

	it('should not run if transaction is not available', async () => {
		transactionStub.resolves(null);

		await runUserCleanupJob();

		expect(userFindAllStub.notCalled).to.be.true;
	});

	it('should rollback if any error occurs', async () => {
		userFindAllStub.rejects(new Error('Database error'));

		await runUserCleanupJob();

		expect(commitStub.notCalled).to.be.true;
		expect(rollbackStub.calledOnce).to.be.true;
	});

	it('should run delete therapist profile stub successfully', async () => {
		userFindAllStub.resolves([
			{
				id: 1,
				calendars: [
					{
						type: 'google',
						credentials: {
							tokens : 'fake-google-token',
						}
					},
					{
						type: 'outlook',
						credentials: {
							tokens : 'fake-outlook-token',
						}
					},
				]
			}
		]);

		appointmentsFindAllStub.resolves([
			{
				id: 1,
				therapistId: 1,
				patientId: 2,
				googleEventId: 'google-event-123',
				outlookEventId: 'outlook-event-456'
			}
		]);

		appointmentsDestroyStub.resolves(1);

		userDestroyStub.resolves(1);

		await runUserCleanupJob();

		expect(userFindAllStub.calledOnce).to.be.true;
		expect(appointmentsFindAllStub.calledOnce).to.be.true;
		expect(appointmentsDestroyStub.calledOnce).to.be.true;
		expect(userDestroyStub.calledOnce).to.be.true;
		expect(deleteGoogleEventStub.calledOnce).to.be.true;
		expect(deleteOutlookEventStub.calledOnce).to.be.true;
		expect(commitStub.calledOnce).to.be.true;
		expect(rollbackStub.notCalled).to.be.true;
	});
});