import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import { TherapistWaitlist, User } from '@/src/models';
import { expect } from 'chai';
import { mail } from '@/src/configs/sendgrid.config';
import { runWaitlistNotifyJob } from './waitlist-notify.job';
import dayjs from 'dayjs';
import * as availabilityHelper from '@/src/application/helpers/availability.helper';
import * as twilioConfig from '@/src/configs/twilio.config';
import admin from '@/src/configs/firebase.config';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(timezone)

describe('Waitlist Notify Job Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindAllStub: sinon.SinonStub;
	let therapistWaitlistUpdateStub: sinon.SinonStub;

	let sendEmailStub: sinon.SinonStub;
	let sendSmsStub: sinon.SinonStub;
	let sendFCMStub: sinon.SinonStub;

	let getAvailableOfficeHoursStub: sinon.SinonStub;
	let dayjsStub: sinon.SinonStub;

	let dummyTherapist = {
		id: 1,
		email: '<EMAIL>',
		firstname: 'John',
		lastname: 'Doe',
		timeZone: 'America/New_York',
		normalOfficeHours: [
			{
				id: 1,
				workingDay: 'Monday',
				availabilityHours: '09:00:00',
				appointmentMethod: 'in-person'
			}
		],
		therapistAppointments: [],
		calendars: [],
		registrationInfo: [
			{
				pageName: 'waitlist-notifications',
				payloadInfo: {
					week_visibility: 3,
				}
			},
		],
		therapistWaitlist: [
			{
				id: 1,
				patientId: 1,
				notificationPreferences: ['email', 'sms', 'fcm'],
				phoneNumber: '+**********',
				notifiedAt: null,
				patient: {
					id: 1,
					email: '<EMAIL>',
					timeZone: 'America/New_York',
					firstname: 'Jane',
					lastname: 'Doe',
					userDevices: [
						{
							deviceId: 'device123',
							deviceType: 'android',
						}
					]
				}
			}
		],
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindAllStub = sandbox.stub(User, 'findAll');
		therapistWaitlistUpdateStub = sandbox.stub(TherapistWaitlist, 'update').resolves();

		sendEmailStub = sandbox.stub(mail, 'sendMail').resolves();
		sendSmsStub = sandbox.stub(twilioConfig, 'sendSms').resolves();
		sendFCMStub = sandbox.stub(admin.messaging(), 'send').resolves();

		getAvailableOfficeHoursStub = sandbox.stub(availabilityHelper, 'getAvailableOfficeHours');

		dayjsStub = sandbox.stub(dayjs.prototype, 'tz').returns(
			dayjs().set('hour', 7).set('minute', 0).set('second', 0).set('millisecond', 0)
		);
	});

	afterEach(function() {
		process.env.BYPASS_JOBS = "false";
		sandbox.restore();
	});

	it('should not run if BYPASS_JOBS is true', async () => {
		process.env.BYPASS_JOBS = "true";

		await runWaitlistNotifyJob();

		expect(userFindAllStub.notCalled).to.be.true;
	});

	it('should not run if no therapists have any waitlist', async () => {
		userFindAllStub.resolves([]);

		await runWaitlistNotifyJob();

		expect(getAvailableOfficeHoursStub.notCalled).to.be.true;
	});

	it('should send waitlist notifications successfully', async () => {
		userFindAllStub.resolves([dummyTherapist]);
		getAvailableOfficeHoursStub.resolves([
			{
				id: 3,
				day: 'Monday',
			},
			{
				id: 4,
				day: 'Monday',
			},
			{
				id: 5,
				day: 'Monday',
			},
			{
				id: 6,
				day: 'Monday',
			},
		]);

		await runWaitlistNotifyJob();

		expect(getAvailableOfficeHoursStub.calledOnce).to.be.true;
		expect(sendEmailStub.calledOnce).to.be.true;
		expect(sendSmsStub.calledOnce).to.be.true;
		expect(sendFCMStub.calledOnce).to.be.true;
		expect(therapistWaitlistUpdateStub.calledThrice).to.be.true;
	});

	it('should not send waitlist notifications if already notified for that day', async () => {
		userFindAllStub.resolves([{
			...dummyTherapist,
			therapistWaitlist: [{
				...dummyTherapist.therapistWaitlist[0],
				notifiedAt: dayjs().subtract(5, 'hours').toISOString(),
			}],
		}]);
		getAvailableOfficeHoursStub.resolves([
			{
				id: 3,
				day: 'Monday',
			},
		]);

		await runWaitlistNotifyJob();

		expect(getAvailableOfficeHoursStub.calledOnce).to.be.true;
		expect(sendEmailStub.notCalled).to.be.true;
		expect(sendSmsStub.notCalled).to.be.true;
		expect(sendFCMStub.notCalled).to.be.true;
		expect(therapistWaitlistUpdateStub.notCalled).to.be.true;
	});

	it('should not send waitlist notifications if patient current time is not between 6 and 8 AM', async () => {
		userFindAllStub.resolves([dummyTherapist]);
		getAvailableOfficeHoursStub.resolves([
			{
				id: 3,
				day: 'Monday',
			},
		]);
		dayjsStub.returns(
			dayjs().set('hour', 10).set('minute', 0).set('second', 0).set('millisecond', 0)
		);

		await runWaitlistNotifyJob();

		expect(getAvailableOfficeHoursStub.calledOnce).to.be.true;
		expect(sendEmailStub.notCalled).to.be.true;
		expect(sendSmsStub.notCalled).to.be.true;
		expect(sendFCMStub.notCalled).to.be.true;
		expect(therapistWaitlistUpdateStub.notCalled).to.be.true;
	});
});