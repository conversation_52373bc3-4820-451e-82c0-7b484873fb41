import cron from 'node-cron';
import { User, Appointments, Calendar } from '@/src/models';
import { Op, Transaction } from 'sequelize';
import logger from '@/src/configs/logger.config';
import { deleteGoogleEvent, deleteOutlookEvent } from '@/src/application/repositories/calendar.repository';

const DeleteTherapistProfileJob = () => {
  cron.schedule('0 0 * * *', runUserCleanupJob, {
    timezone: 'America/Denver',
  });
}

export const runUserCleanupJob = async () => {
  logger.info('Running user cleanup at 12 AM US Mountain Time');
  const transaction = await User.sequelize?.transaction();
  
  try {
    if (!transaction) {
      logger.error('Transaction could not be started');
      return;
    }
    
    const now = new Date(new Date().toLocaleString('en-US', { timeZone: 'America/Denver' }));
    const thirtyDaysAgo = new Date(now);
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30); // Subtract 30 days
    
    logger.info(`Current Denver time: ${now}`);
    logger.info(`30 days ago (Denver time): ${thirtyDaysAgo}`);
    
    // Find all users that need to be deleted
    const usersToDelete = await User.findAll({
      where: {
        deletedAt: {
          [Op.lte]: thirtyDaysAgo,
        },
        role: 'therapist',
      },
      include: [
        {
          model: Calendar,
          as: 'calendars',
          attributes: ['type', 'credentials'],
          required: false,
        }
      ],
      transaction,
    });
    
    logger.info(`Found ${usersToDelete.length} user(s) to delete`);
    
    // Process each user for deletion
    for (const user of usersToDelete) {
      const userId = user.id;
      logger.info(`Processing user ID: ${userId} for deletion`);
      
      // Find all appointments related to this user
      const appointments = await Appointments.findAll({
        where: { 
          [Op.or]: [
            { therapistId: userId }, 
            { patientId: userId }
          ] 
        },
        transaction,
      });
      
      logger.info(`Found ${appointments.length} appointment(s) to delete for user ${userId}`);
      
      // Delete each appointment's calendar events first
      await Promise.all(appointments.map(async (appointment) => {
        try {
          // Handle Google calendar events
          if (appointment.googleEventId) {
            const therapistGoogleCal = user.calendars?.find((c) => c.type === 'google');
            if (therapistGoogleCal) {
              const { tokens } = therapistGoogleCal.credentials as any;
              if (tokens) {
                logger.info(`Deleting Google event ID: ${appointment.googleEventId} for appointment ID: ${appointment.id}`);
                await deleteGoogleEvent({ tokens, eventId: appointment.googleEventId });
              }
            }
          }
          
          // Handle Outlook calendar events
          if (appointment.outlookEventId) {
            const therapistOutlookCal = user.calendars?.find((c) => c.type === 'outlook');
            if (therapistOutlookCal) {
              const { tokens } = therapistOutlookCal.credentials as any;
              if (tokens) {
                logger.info(`Deleting Outlook event ID: ${appointment.outlookEventId} for appointment ID: ${appointment.id}`);
                await deleteOutlookEvent({ tokens, userId, eventId: appointment.outlookEventId });
              }
            }
          }
        } catch (error) {
          logger.error(`Error deleting calendar event for appointment ID ${appointment.id}:`, error);
          // Continue with the next appointment even if there's an error with one
        }
      }));
      
      // Delete all appointments for this user
      const deletedAppointmentsCount = await Appointments.destroy({ 
        where: { 
          [Op.or]: [
            { therapistId: userId }, 
            { patientId: userId }
          ] 
        }, 
        transaction 
      });
      
      logger.info(`Deleted ${deletedAppointmentsCount} appointment(s) for user ${userId}`);
    }
    
    // Now that all appointments and calendar events are cleaned up, delete the users
    const deletedUsersCount = await User.destroy({
      where: {
        id: usersToDelete.map(user => user.id)
      },
      transaction,
    });
    
    logger.info(`Successfully deleted ${deletedUsersCount} user(s) whose accounts were inactive for 30 days.`);
    
    await transaction.commit();
  } catch (error) {
    logger.error('Error while deleting users:', error);
    await transaction?.rollback();
  }
}

export default DeleteTherapistProfileJob;
