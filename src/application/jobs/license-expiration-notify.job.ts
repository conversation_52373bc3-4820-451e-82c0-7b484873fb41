import cron from 'node-cron';
import moment from 'moment';
import { mail } from '@/src/configs/sendgrid.config';
import { Op } from 'sequelize';
import { User, UserRegistrationInfo } from '@/src/models';
import { LicensePayload, WaitlistNotifications } from '@/src/types/registration.interface';
import { sendSms } from '@/src/configs/twilio.config';
import logger from '@/src/configs/logger.config';
import { LicenseExpirationEmail } from '../emails/license-expiration.email';
import { maskEmail } from '../helpers/general.helper';
import { LicenseExpiredEmail } from '../emails/license-expired.email';

const NOTIFY_DAYS = [45, 30, 10, 3, 2, 1];

const getAllTherapistsWithLicenses = async () => {
  return await User.findAll({
    where: {
      role: 'therapist',
      deletedAt: null,
      deactivatedAt: null,
    },
    include: [     
      {
        model: UserRegistrationInfo,
        where: {
          [Op.or]: [
                { pageName: 'license' },
                { pageName: 'waitlist-notifications' },
            ]
        },
        required: false,
      }
    ],
    attributes: ['id', 'email', 'firstname', 'lastname'],
  });
};

export const checkExpiringLicenses = async () => {
const today = moment().startOf('day');
  const therapists = await getAllTherapistsWithLicenses();

  for (const therapist of therapists) {
    try {
      const registrationInfo = therapist.registrationInfo?.find(
        (info: any) => info.pageName === 'license'
      );

      const payload = registrationInfo?.payloadInfo as LicensePayload;
      const licenses = payload?.licenses || [];

      const therapistNotifInfo = therapist.registrationInfo?.find(
        (info) => info.pageName === 'waitlist-notifications'
      )?.payloadInfo as unknown as WaitlistNotifications;

      for (const license of licenses) {
        const expiry = license.expiry_date;
        if (!expiry) continue;

        const expiryDate = moment(expiry, 'MM-DD-YYYY').startOf('day');
        const daysLeft = expiryDate.diff(today, 'days'); 

        if (daysLeft === 0) {
        logger.info(`License Expiration Notify Job - License expired today. Therapist ID: ${therapist.id}, Expiry: ${expiry}`);

        const shouldSendEmail = therapistNotifInfo?.notification_preferences?.includes('email');
        const shouldSendSms = therapistNotifInfo?.notification_preferences?.includes('text');
        const therapistPhone = therapistNotifInfo?.phone_number;

      if (shouldSendEmail) {
        try {
          const expiredEmailData = LicenseExpiredEmail.compile({
            email: therapist.email,
            subject: "Your License Has Expired – Action Required",
            therapist_name: therapist.firstname +" "+therapist.lastname,
            license_state: license.license_state?.label,
            expiry: expiry,
            daysLeft: 0,
          });

          

          await mail.sendMail({
            to: expiredEmailData.To,
            subject: expiredEmailData.Subject,
            html: expiredEmailData.HtmlBody,
          });

          logger.info(`License Expiration Notify Job - Expired email sent to therapist ID: ${therapist.id}`);
        } catch (error) {
          logger.error(`License Expiration Notify Job - Expired email error - Therapist ID: (${therapist.id}): ${error}`);
        }
      }

      if (shouldSendSms && therapistPhone) {
        try {
          await sendSms(
            therapistPhone,
            `Your license on file with NextTherapist has expired as of today (${expiry}). Please update it in your account to avoid interruption:${process.env.FRONTEND_URL}`
          );
          logger.info(`License Expiration Notify Job - Expired SMS sent to ${therapistPhone}`);
        } catch (error) {
          logger.error(`License Expiration Notify Job - Expired SMS error - Therapist ID: (${therapist.id}): ${error}`);
        }
      }

    } else if (NOTIFY_DAYS.includes(daysLeft)) {
          logger.info(`License Expiration Notify Job - Preparing to notify therapist ID: ${therapist.id} for license expiring on ${expiry}`);

          const shouldSendEmail = therapistNotifInfo?.notification_preferences?.includes('email');

          if (shouldSendEmail) {
            try {
                const therapistEmailData = LicenseExpirationEmail.compile({
                    email: therapist.email,
                    subject: "Action Required – Your License is Expiring Soon",
                    therapist_name:  therapist.firstname +" "+therapist.lastname,
                    license_state:license.license_state?.label,
                    expiry: expiry,
                    daysLeft: daysLeft,     
                });

                await mail.sendMail({
                    to: therapistEmailData.To,
                    subject: therapistEmailData.Subject,
                    html: therapistEmailData.HtmlBody,
                });              
            
              logger.info(`License Expiration Notify Job - Email sent to therapist ID: ${therapist.id}`);
            } catch (error) {
              logger.error(`License Expiration Notify Job - Email error - Therapist ID: (${therapist.id}): ${error}`);
            }
          }

          const shouldSendSms = therapistNotifInfo?.notification_preferences?.includes('text');
          const therapistPhone = therapistNotifInfo?.phone_number;

          if (shouldSendSms && therapistPhone) {
            try {
              await sendSms(
                therapistPhone,
                `Your license on file with NextTherapist expires in ${daysLeft} days. Please update it in your account to avoid interruption:${process.env.FRONTEND_URL}`
              );
              logger.info(`License Expiration Notify Job - SMS sent to ${therapistPhone}`);
            } catch (error) {
              logger.error(`License Expiration Notify Job - SMS error - Therapist ID: (${therapist.id}): ${error}`);
            }
          }
        }
      }
    } catch (error) {
      logger.error(`License Expiration Notify Job - Therapist Notify error - Therapist ID: (${therapist.id}): ${error}`);
    }
  }

  logger.info(`[${new Date().toISOString()}] License expiry check complete.`);
};


cron.schedule('0 14 * * *', async () => {
  // Runs every day at 14:00 UTC (2:00 PM UTC)
  // Equivalent local times:
  //   - 10:00 AM Eastern (New York, Ohio - EDT)
  //   - 9:00 AM Central (Chicago - CDT)
  //   - 8:00 AM Mountain (Denver - MDT)
  //   - 7:00 AM Pacific (Los Angeles - PDT)
  //
  // India: 7:30 PM IST
  // This time hits morning hours in all major U.S. time zones
  try {
    if (process.env.BYPASS_JOBS === "true") return;
    logger.info(`License expiry check started.`);
    await checkExpiringLicenses();
  } catch (error) {
    logger.error('License reminder cron error:', error);
  }
});

