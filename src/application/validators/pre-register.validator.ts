import { body } from 'express-validator'

const PreRegisterValidator = [
	body('phone')
		.custom((value, { req }) => {
			if (req.body.email == null) {
				return value ?? value.length >= 8
			}
			return true
		})
		.withMessage('Either phone or email is required'),
	body('email')
		.custom((value, { req }) => {
			if (req.body.phone == null) {
				return value ?? value.length >= 8
			}
			return true
		})
		.withMessage('Either phone or email is required'),
]

export default PreRegisterValidator
