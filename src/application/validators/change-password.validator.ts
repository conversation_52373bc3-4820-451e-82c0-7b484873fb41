import { body } from 'express-validator'

const ChangePasswordValidator = [
	body('email').notEmpty().withMessage('Email is required'),
	body('old_password').notEmpty().withMessage('Old password is required'),
	body('new_password').notEmpty().withMessage('New Password is required'),
	body('confirm_password')
		.notEmpty()
		.withMessage('Confirm password is required'),
]

export default ChangePasswordValidator
