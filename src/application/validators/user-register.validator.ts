import { body } from 'express-validator'

const UserRegisterValidator = [
	body('user').optional(),
	// body('firstname')
	// 	.notEmpty()
	// 	.withMessage(
	// 		'Firstname is required and must be at least 2 characters long'
	// 	),
	// body('lastname').notEmpty().withMessage('Lastname is required'),
	// body('phone').notEmpty().withMessage('Phone is required'),
	// body('email').notEmpty().withMessage('Email is required'),
	// body('address.city').notEmpty().withMessage('City is required'),
	// body('address.state').notEmpty().withMessage('State is required'),
	// body('address.street').notEmpty().withMessage('Street is required'),
	// body('address.country').notEmpty().withMessage('Country is required'),
	// body('address.zip').notEmpty().withMessage('Zip is required'),
]

export default UserRegisterValidator
