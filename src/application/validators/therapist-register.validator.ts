import { body } from 'express-validator'

const TherapistRegisterValidator = [
	body('firstname')
		.notEmpty()
		.withMessage(
			'Firstname is required and must be at least 2 characters long'
		),
	body('lastname').notEmpty().withMessage('Lastname is required'),
	body('phone').notEmpty().withMessage('Phone is required'),
	body('email').notEmpty().withMessage('Email is required'),
	body('address')
		.custom((value) => {
			return (
				value.city &&
				value.state &&
				value.country &&
				value.street &&
				value.zip
			)
		})
		.withMessage('Address is required'),
]

export default TherapistRegisterValidator
