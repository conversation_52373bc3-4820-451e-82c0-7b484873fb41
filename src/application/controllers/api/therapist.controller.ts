import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import {
	FindAttributeOptions,
	GroupOption,
	Includeable,
	Op,
	Order,
	WhereOptions,
	Sequelize,
	QueryTypes,
} from 'sequelize'
import { Appointments, Calendar, NormalOfficeHours, StripeInfo, TherapistProfile, TherapistSubscription, TherapistWaitlist, User, UserRegistrationInfo } from '@/src/models'
import { UserType } from '@/src/application/helpers/constant.helper'
import {
	paginated,
	paginatedData,
} from '@/src/application/helpers/pagination.helper'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { getTherapistDetails } from '@/src/application/repositories/therapist.repository'
import sequelize from '@/src/configs/database.config'
import dayjs from 'dayjs'
import TherapistDurationAndCost from '@/src/models/therepist-duration-and-cost.model'
import PortalMiddleware from '../../middlewares/portal.middleware'
import { OfficeHoursInfo, WaitlistNotifications } from '@/src/types/registration.interface'
import { conditions, modalityMappings, religionMapping, raceMappings, focusAreaMapping, languageMapping, validGenders,insuranceMappings,TELEHEALTH, IN_PERSON,IN_PERSON_TELEHEALTH,paymentMappings } from '../../controllers/api/constants';
import logger from '@/src/configs/logger.config'
import client from '@/src/configs/redis.config'
import { getAvailableOfficeHours } from '../../helpers/availability.helper'

const NodeGeocoder = require('node-geocoder');

const router = express.Router()

/********************************
 * * Get therapists lists
 ********************************/
router.get(
    '/',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        logger.info(`User: ${req.user?.id} accessed therapist list`);
        logger.info(`Query parameters: ${JSON.stringify(req.query)}`);
        const patientId = req.user?.id;
        let patientTimeZone: string | null = null;
        let recentInteractions: any[] = [];
 
        // Get patient's timezone and recent interactions if this is a patient
        if (patientId) {
            try {
                const patient = await User.findOne({
                    where: { id: patientId },
                    attributes: ['id', 'timeZone']
                });
 
                if (patient?.timeZone) {
                    patientTimeZone = patient.timeZone;
                    
                    
                    // Fetch appointments and waitlist entries
                    const [appointments, waitlistEntries] = await Promise.all([
                        Appointments.findAll({
                            where: { patientId },
                            attributes: ['id', 'appointmentDate', 'createdAt', 'therapistId'],
                            include: [{
                                model: User,
                                as: 'therapist',
                                attributes: ['firstname', 'lastname']
                            }],
                            raw: true,
                            nest: true
                        }),
                        TherapistWaitlist.findAll({
                            where: { patientId },
                            attributes: ['id', 'createdAt', 'therapistId', 'status'],
                            include: [{
                                model: User,
                                as: 'therapist',
                                attributes: ['firstname', 'lastname']
                            }],
                            raw: true,
                            nest: true
                        })
                    ]);
 
                    // Combine interactions
                    recentInteractions = [
                        ...appointments.map(a => ({
                            id: a.id,
                            type: 'appointment' as const,
                            date: a.appointmentDate,
                            createdAt: a.createdAt,
                            therapist: a.therapist,
                            therapistId: a.therapistId,
                        })),
                        ...waitlistEntries.map(w => ({
                            id: w.id,
                            type: 'waitlist' as const,
                            date: w.createdAt,
                            createdAt: w.createdAt,
                            therapist: w.therapist,
                            therapistId: w.therapistId,
                            status: w.status
                        }))
                    ];
                }
            } catch (error) {
                logger.error('Error fetching patient interactions:', error);
                // Continue without recent interactions if there's an error
            }
        }
 
        // Get therapist IDs from previous interactions using Sequelize
        let previousInteractions: any[] = [];
        
        if (patientId) {
            try {
                // Get current time in patient's timezone
                const currentDateTime = patientTimeZone ? 
                    dayjs().tz(patientTimeZone) : 
                    dayjs();
                
                // Fetch appointments with timezone conversion
                const appointments = await Appointments.findAll({
                    where: { patientId },
                    attributes: [
                        'therapistId',
                        'appointmentDate',
                        [sequelize.literal(`'appointment'`), 'interaction_type']
                    ],
                    raw: true
                });
                
                // Fetch waitlist entries
                const waitlistEntries = await TherapistWaitlist.findAll({
                    where: { patientId },
                    attributes: [
                        'therapistId',
                        'createdAt',
                        [sequelize.literal(`'waitlist'`), 'interaction_type']
                    ],
                    raw: true
                });
                
                // Combine and process interactions
                const allInteractions = [
                    ...appointments.map(a => ({
                        therapistId: a.therapistId,
                        interaction_date: a.appointmentDate,
                        interaction_type: 'appointment'
                    })),
                    ...waitlistEntries.map(w => ({
                        therapistId: w.therapistId,
                        interaction_date: w.createdAt,
                        interaction_type: 'waitlist'
                    }))
                ];
                
                // Process interactions in JavaScript with timezone awareness
                const therapistInteractionMap = new Map();
                
                allInteractions.forEach(interaction => {
                    const interactionDate = patientTimeZone ? 
                        dayjs(interaction.interaction_date).tz(patientTimeZone) : 
                        dayjs(interaction.interaction_date);
                    
                    const isFuture = interactionDate.isAfter(currentDateTime);
                    const dateOnly = interactionDate.format('YYYY-MM-DD');
                    const timeInSeconds = interactionDate.hour() * 3600 + interactionDate.minute() * 60 + interactionDate.second();
                    
                    const existingInteraction = therapistInteractionMap.get(interaction.therapistId);
                    
                    if (!existingInteraction) {
                        therapistInteractionMap.set(interaction.therapistId, {
                            therapistId: interaction.therapistId,
                            interaction_date: interaction.interaction_date,
                            interaction_type: interaction.interaction_type,
                            is_future: isFuture,
                            date_only: dateOnly,
                            time_seconds: timeInSeconds,
                            interaction_date_obj: interactionDate
                        });
                    } else {
                        // Apply the same priority logic as the SQL query
                        const existingIsFuture = existingInteraction.is_future;
                        const existingDate = existingInteraction.interaction_date_obj;
                        
                        let shouldReplace = false;
                        
                        // Priority 1: Future over past
                        if (isFuture && !existingIsFuture) {
                            shouldReplace = true;
                        } else if (!isFuture && existingIsFuture) {
                            shouldReplace = false;
                        } else if (isFuture && existingIsFuture) {
                            // Both future: prefer earlier date, then earlier time for same date
                            if (interactionDate.isBefore(existingDate, 'day')) {
                                shouldReplace = true;
                            } else if (interactionDate.isSame(existingDate, 'day') && interactionDate.isBefore(existingDate)) {
                                shouldReplace = true;
                            }
                        } else {
                            // Both past: prefer later date, then later time for same date
                            if (interactionDate.isAfter(existingDate, 'day')) {
                                shouldReplace = true;
                            } else if (interactionDate.isSame(existingDate, 'day') && interactionDate.isAfter(existingDate)) {
                                shouldReplace = true;
                            }
                        }
                        
                        if (shouldReplace) {
                            therapistInteractionMap.set(interaction.therapistId, {
                                therapistId: interaction.therapistId,
                                interaction_date: interaction.interaction_date,
                                interaction_type: interaction.interaction_type,
                                is_future: isFuture,
                                date_only: dateOnly,
                                time_seconds: timeInSeconds,
                                interaction_date_obj: interactionDate
                            });
                        }
                    }
                });
                
                // Convert map to array and sort
                previousInteractions = Array.from(therapistInteractionMap.values()).sort((a, b) => {
                    // Primary: future first, then past
                    if (a.is_future && !b.is_future) return -1;
                    if (!a.is_future && b.is_future) return 1;
                    
                    // Secondary: date comparison
                    if (a.is_future && b.is_future) {
                        // Future: earlier dates first
                        const dateCompare = a.interaction_date_obj.diff(b.interaction_date_obj, 'day');
                        if (dateCompare !== 0) return dateCompare;
                        
                        // Same date: earlier time first
                        return a.time_seconds - b.time_seconds;
                    } else {
                        // Past: later dates first
                        const dateCompare = b.interaction_date_obj.diff(a.interaction_date_obj, 'day');
                        if (dateCompare !== 0) return dateCompare;
                        
                        // Same date: later time first
                        return b.time_seconds - a.time_seconds;
                    }
                });
                
            } catch (error) {
                logger.error('Error processing interactions with Sequelize:', error);
                previousInteractions = [];
            }
        }
 
        // Extract therapist IDs in the proper order
        const therapistIds = previousInteractions.map((t: any) => t.therapistId);
 
        // If no previous interactions, return empty response
        if (therapistIds.length === 0) {
            logger.info(`No previous interactions found for patient ${patientId}`);
            return res.send(paginatedData({ rows: [], count: 0 }, req));
        }
 
        let where: WhereOptions = {
            id: {
                [Op.in]: therapistIds
            },
            role: UserType.THERAPIST,
            emailVerifiedAt: {
                [Op.not]: null,
            },
            acceptedAt: {
                [Op.not]: null,
            },
            deletedAt: null,
            deactivatedAt: null,
            rejectedAt: null,
            active: true,
        };
 
        if (req.query.patLat && req.query.patLng) {
            let lat = parseFloat(req.query.patLat as string);
            let lng = parseFloat(req.query.patLng as string);
 
            
            async function getStateFromCoordinates(lat: number, lng: number): Promise<string | null> {
                if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
                    logger.info(`Invalid coordinates: lat=${lat}, lng=${lng}`);
                    return null;
                }
                
                const formattedLat = parseFloat(lat.toFixed(6));
                const formattedLng = parseFloat(lng.toFixed(6));
                
                const cacheKey = `state:${formattedLat}:${formattedLng}`;
                
                try {
                    // Try to get state from cache
                    const cachedState = await client.get(cacheKey);
                    if (cachedState) {
                        logger.info(`Found state in cache: ${cachedState} for coordinates: lat=${formattedLat}, lng=${formattedLng}`);
                        return cachedState;
                    }
                    
                    logger.info(`State not found in cache for coordinates: lat=${formattedLat}, lng=${formattedLng}, proceeding with geocoding`);
                    
                    // Try multiple geocoding providers in sequence
                    const providers = [
                        {
                            name: 'openstreetmap',
                            options: { provider: 'openstreetmap' }
                        },
                        {
                            name: 'google',
                            options: {
                                provider: 'google',
                                apiKey: process.env.GOOGLE_MAPS_API_KEY || 'YOUR_API_KEY'
                            }
                        }
                    ];
                    
                    for (const provider of providers) {
                        try {                       
                            const geocoder = NodeGeocoder(provider.options);
                            const result = await geocoder.reverse({ lat: formattedLat, lng: formattedLng });
                            
                            if (result && result.length > 0) {
                                const state = result[0].state || result[0].administrativeLevels?.level1long || null;
                                
                                if (state) {
                                    // Cache the state in Redis for future use (expire after 30 days)
                                    // Fixed Redis syntax - using separate ex parameter
                                    await client.set(cacheKey, state);
                                    await client.expire(cacheKey, 60 * 60 * 24 * 30); // 30 days expiration
                                    logger.info(`Cached state: ${state} for coordinates: lat=${formattedLat}, lng=${formattedLng}`);
                                    return state;
                                }
                            }
                        } catch (error: any) {
                            logger.info(error, "error");
                        }
                    }
                    
                    try {
                        const response = await fetch(
                            `https://nominatim.openstreetmap.org/reverse?format=json&lat=${formattedLat}&lon=${formattedLng}&zoom=10`,
                            {
                                headers: {
                                    'User-Agent': 'TherapistApp/1.0'
                                }
                            }
                        );
                        
                        if (response.ok) {
                            const data = await response.json();
                            logger.info("Nominatim direct API result:", JSON.stringify(data, null, 2));
                            
                            if (data.address) {
                                const state = data.address.state ||
                                            data.address.region ||
                                            data.address.province ||
                                            data.address.county;
                                
                                if (state) {
                                    // Cache the state in Redis for future use (expire after 30 days)
                                    // Fixed Redis syntax - using separate ex parameter
                                    await client.set(cacheKey, state);
                                    await client.expire(cacheKey, 60 * 60 * 24 * 30); // 30 days expiration
                                    logger.info(`Found and cached state: ${state} using direct Nominatim API`);
                                    return state;
                                }
                            }
                        }
                    } catch (error: any) {
                        logger.info("Direct Nominatim API error:", error.message);
                    }
                    
                    return null;
                } catch (cacheError: any) {
                    logger.info(`Redis cache error: ${cacheError.message}`);
                    // If Redis fails, continue with normal geocoding
                    return null;
                }
            }
            
            try {
                const stateName = await getStateFromCoordinates(lat, lng);
                if (stateName) {
                    (where as any)[Op.and] = [
                        ...(where as any)[Op.and] || [],
                     sequelize.where(
						sequelize.literal(`(
							SELECT EXISTS (
							SELECT 1
							FROM jsonb_array_elements(("payloadInfo"->'licenses')::jsonb) AS license
							WHERE license->'license_state'->>'label' = '${stateName}'
								AND (license->'license_status'->>'label' = 'Active' OR license->'license_status'->>'label' = 'Provisional')
								AND to_date(license->>'expiry_date', 'MM-DD-YYYY') >= CURRENT_DATE
							)
							OR EXISTS (
							SELECT 1
							FROM jsonb_array_elements(("payloadInfo"->'compact_licenses')::jsonb) AS compact_license
							WHERE compact_license->'compact_privilege'->>'label' = 'Yes'
								AND EXISTS (
								SELECT 1
								FROM jsonb_array_elements(compact_license->'compact_states') AS compact_state
								WHERE compact_state->>'label' = '${stateName}'
								)
							)
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'license'
							LIMIT 1
						)`),
						true
						)
                    ];
                    
                    
                }
            } catch (error: any) {
                logger.info("Error getting state from coordinates:", error.message);
            }
        }
 
        if (req.query.lat && req.query.lng) {
            let lat = parseFloat(req.query.lat as string);
            let lng = parseFloat(req.query.lng as string);
        
            const milesRadius = 30;
            const latDiff = milesRadius / 69;
            const lngDiff = milesRadius / (69 * Math.cos(lat * (Math.PI / 180)));
        
            (where as any)[Op.and] = [
                ...((where as any)[Op.and] || []),
                sequelize.where(
                    sequelize.literal(`(
                        SELECT ("payloadInfo"->'business_address'->>'lat')::numeric
                        FROM "user_registration_informations"
                        WHERE "user_registration_informations"."userId" = "User"."id"
                        AND "user_registration_informations"."pageName" = 'practice-info'
                        LIMIT 1
                    )`),
                    { [Op.between]: [lat - latDiff, lat + latDiff] }
                ),
                sequelize.where(
                    sequelize.literal(`(
                        SELECT ("payloadInfo"->'business_address'->>'lng')::numeric
                        FROM "user_registration_informations"
                        WHERE "user_registration_informations"."userId" = "User"."id"
                        AND "user_registration_informations"."pageName" = 'practice-info'
                        LIMIT 1
                    )`),
                    { [Op.between]: [lng - lngDiff, lng + lngDiff] }
                ),
            ];
        }
 
        // Create a custom order based on the therapist interaction sequence
        const therapistOrderMap = new Map();
        therapistIds.forEach((id, index) => {
            therapistOrderMap.set(id, index);
        });
 
        const include: Includeable[] = [
            {
                model: NormalOfficeHours,
                attributes: [],
                required: true,
            },
            {
                model: UserRegistrationInfo,
                as: 'registrationInfo',
                attributes: ['pageName', 'payloadInfo'],
                required: false,
            },
            {
                model: TherapistSubscription,
                where: {
                    isActive: true,
                },
                attributes: [],
                required: true,
            },
            {
                model: StripeInfo,
                where: {
                    status: 'active'
                },
                attributes: [],
                required: true,
            }
        ];
 
        const attributes: FindAttributeOptions = []
        const { search } = req.query

        if (search) {
                const searchParts = (search as string).split(' ');
        
                where = {
                        ...where,
                        [Op.or]: [
                                { firstname: { [Op.iLike]: `%${search}%` } },
                                { lastname: { [Op.iLike]: `%${search}%` } },
                                {
                                        [Op.and]: [
                                                { firstname: { [Op.iLike]: `%${searchParts[0] || ''}%` } },
                                                { lastname: { [Op.iLike]: `%${searchParts[1] || ''}%` } }
                                        ]
                                }
                        ]
                };
        }
 
        // Execute the query
        const therapists = await User.findAndCountAll({
            attributes: {
                include: attributes,
            },
            include,
            where,
            distinct: true,
            ...paginated(req),
        });
 
        if (req.query.count) {
            const totalCount = Array.isArray(therapists.count)
                ? (therapists.count.length > 0 ? therapists.count[0]?.count : 0)
                : therapists.count;
            return res.json({
                count: totalCount,
            });
        }
 
        // Process therapist data (keep existing processing logic)
        therapists.rows.forEach((therapist: any) => {
            const registrationInfo = therapist.registrationInfo || [];
 
            const officeHoursEntry = registrationInfo.find((info: any) =>
                info.pageName === 'normal-office-hours') || {};
            const officeHoursPayload = officeHoursEntry.payloadInfo || {};
            const appointmentMethod = officeHoursPayload?.appointmentMethod || null;
            therapist.dataValues.inPersonFlag = appointmentMethod === IN_PERSON;
            therapist.dataValues.teleHealthFlag = appointmentMethod === TELEHEALTH;
            if (appointmentMethod === IN_PERSON_TELEHEALTH) {
                therapist.dataValues.inPersonFlag = true;
                therapist.dataValues.teleHealthFlag = true;
            }
 
            // Extract paymentForms
            const paymentFormsEntry = registrationInfo.find((info: any) =>
                info.pageName === 'payment-forms') || {};
            const paymentForm = paymentFormsEntry.payloadInfo || {};
            if (paymentForm.insurance_list && paymentForm.insurance_list.checked_list) {
                paymentForm.insurance_list = paymentForm.insurance_list.checked_list;
            }
            therapist.dataValues.paymentForms = paymentForm;
 
            // Extract practiceInfo
            const practiceInfoEntry = registrationInfo.find((info: any) =>
                info.pageName === 'practice-info') || {};
            therapist.dataValues.practiceInfo = practiceInfoEntry.payloadInfo || {};
 
            // Map firstname and lastname to firstName and lastName
            therapist.dataValues.firstName = therapist.dataValues.firstname;
            therapist.dataValues.lastName = therapist.dataValues.lastname;
            delete therapist.dataValues.firstname;
            delete therapist.dataValues.lastname;
 
            // Remove registrationInfo from the response
            delete therapist.dataValues.registrationInfo;
        });
 
        // Sort therapists based on the timezone-aware interaction order
        therapists.rows.sort((a, b) => {
            const orderA = therapistOrderMap.get(a.id) ?? Number.MAX_SAFE_INTEGER;
            const orderB = therapistOrderMap.get(b.id) ?? Number.MAX_SAFE_INTEGER;
            return orderA - orderB; // Ascending order (proper time-based sorting)
        });
 
        // Generate and send the paginated response
        const paginatedResponse = paginatedData(therapists, req);
        res.send(paginatedResponse);
    })
);
 

/**
 * @route GET /map-view
 * @description Retrieves a list of therapists based on query parameters in map view
 * @returns {Object[]} List of therapist users that match the filtering criteria.
 */
router.get(
	'/map-view',
	wrap(async (req: Request, res: Response) => {
		logger.info(`User: ${req.user?.id} accessed therapist list`);
		logger.info(`Query parameters: ${JSON.stringify(req.query)}`);

		const group: GroupOption = []
		const options = {
			provider: 'openstreetmap',
		  };
		  const geocoder = NodeGeocoder(options);
		let where: WhereOptions = {
			role: UserType.THERAPIST,
			emailVerifiedAt: {
				[Op.not]: null,
			},
			acceptedAt: {
				[Op.not]: null,
			},
			deletedAt: null,
			deactivatedAt: null,
			rejectedAt: null,
			active:true,
		};
		if (req.query.searchId) {
    where = {
        ...where,
        id: parseInt(req.query.searchId as string)
    };
    logger.info(`Filtering therapist by specific ID: ${req.query.searchId}`);
}
		    let searchName =  req.query.searchName;
 
if (searchName) {
    const searchParts = (searchName as string).split(' ');
    
    const nameSearchConditions = [
        { firstname: { [Op.iLike]: `%${searchName}%` } },
        { lastname: { [Op.iLike]: `%${searchName}%` } }
    ];
 
    if (searchParts.length > 1) {
        nameSearchConditions.push({
            [Op.and]: [
                { firstname: { [Op.iLike]: `%${searchParts[0] || ''}%` } },
                { lastname: { [Op.iLike]: `%${searchParts.slice(1).join(' ') || ''}%` } }
            ]
        } as any);
    }
 
    (where as any)[Op.and] = [
        ...((where as any)[Op.and] || []),
        { [Op.or]: nameSearchConditions }
    ];
}
		if (req.query.patLat && req.query.patLng) {
            let lat = parseFloat(req.query.patLat as string);
            let lng = parseFloat(req.query.patLng as string);

			
			async function getStateFromCoordinates(lat: number, lng: number): Promise<string | null> {
				if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
					logger.info(`Invalid coordinates: lat=${lat}, lng=${lng}`);
					return null;
				}
				
				const formattedLat = parseFloat(lat.toFixed(6));
				const formattedLng = parseFloat(lng.toFixed(6));
				
				const cacheKey = `state:${formattedLat}:${formattedLng}`;
				
				try {
					// Try to get state from cache
					const cachedState = await client.get(cacheKey);
					if (cachedState) {
						logger.info(`Found state in cache: ${cachedState} for coordinates: lat=${formattedLat}, lng=${formattedLng}`);
						return cachedState;
					}
					
					logger.info(`State not found in cache for coordinates: lat=${formattedLat}, lng=${formattedLng}, proceeding with geocoding`);
					
					// Try multiple geocoding providers in sequence
					const providers = [
						{
							name: 'openstreetmap',
							options: { provider: 'openstreetmap' }
						},
						{
							name: 'google',
							options: { 
								provider: 'google',
								apiKey: process.env.GOOGLE_MAPS_API_KEY || 'YOUR_API_KEY'
							}
						}
					];
					
					for (const provider of providers) {
						try {						
							const geocoder = NodeGeocoder(provider.options);
							const result = await geocoder.reverse({ lat: formattedLat, lng: formattedLng });
							
							if (result && result.length > 0) {
								const state = result[0].state || result[0].administrativeLevels?.level1long || null;
								
								if (state) {
									// Cache the state in Redis for future use (expire after 30 days)
									// Fixed Redis syntax - using separate ex parameter
									await client.set(cacheKey, state);
									await client.expire(cacheKey, 60 * 60 * 24 * 30); // 30 days expiration
									logger.info(`Cached state: ${state} for coordinates: lat=${formattedLat}, lng=${formattedLng}`);
									return state;
								}
							}
						} catch (error: any) {
							logger.info(error, "error");
						}
					}
					
					try {
						const response = await fetch(
							`https://nominatim.openstreetmap.org/reverse?format=json&lat=${formattedLat}&lon=${formattedLng}&zoom=10`,
							{
								headers: {
									'User-Agent': 'TherapistApp/1.0'
								}
							}
						);
						
						if (response.ok) {
							const data = await response.json();
							logger.info("Nominatim direct API result:", JSON.stringify(data, null, 2));
							
							if (data.address) {
								const state = data.address.state || 
											data.address.region || 
											data.address.province || 
											data.address.county;
								
								if (state) {
									// Cache the state in Redis for future use (expire after 30 days)
									// Fixed Redis syntax - using separate ex parameter
									await client.set(cacheKey, state);
									await client.expire(cacheKey, 60 * 60 * 24 * 30); // 30 days expiration
									logger.info(`Found and cached state: ${state} using direct Nominatim API`);
									return state;
								}
							}
						}
					} catch (error: any) {
						logger.info("Direct Nominatim API error:", error.message);
					}
					
					return null;
				} catch (cacheError: any) {
					logger.info(`Redis cache error: ${cacheError.message}`);
					// If Redis fails, continue with normal geocoding
					return null;
				}
			}
			
			try {
				const stateName = await getStateFromCoordinates(lat, lng);
				
				if (stateName) {
					(where as any)[Op.and] = [
						...(where as any)[Op.and] || [],
						sequelize.where(
							sequelize.literal(`(
								SELECT EXISTS (
									SELECT 1
									FROM jsonb_array_elements(("payloadInfo"->'licenses')::jsonb) AS license
									WHERE license->'license_state'->>'label' = '${stateName}'
									AND (license->'license_status'->>'label' = 'Active' OR license->'license_status'->>'label' = 'Provisional')
									AND to_date(license->>'expiry_date', 'MM-DD-YYYY') >= CURRENT_DATE
								)
								OR EXISTS (
									SELECT 1
									FROM jsonb_array_elements(("payloadInfo"->'compact_licenses')::jsonb) AS compact_license
									WHERE compact_license->'compact_privilege'->>'label' = 'Yes'
									AND EXISTS (
										SELECT 1
										FROM jsonb_array_elements(compact_license->'compact_states') AS compact_state
										WHERE compact_state->>'label' = '${stateName}'
									)
								)
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'license'
								LIMIT 1
							)`),
							true
						)
					];
					
					
				}
			} catch (error: any) {
				logger.info("Error getting state from coordinates:", error.message);
			}
        }
		if (req.query.lat && req.query.lng) {
    let lat = parseFloat(req.query.lat as string);
    let lng = parseFloat(req.query.lng as string);

    const milesRadius = 30;
    const latDiff = milesRadius / 69;
    const lngDiff = milesRadius / (69 * Math.cos(lat * (Math.PI / 180)));

    (where as any)[Op.and] = [
        ...((where as any)[Op.and] || []),
        sequelize.where(
            sequelize.literal(`(
                SELECT ("payloadInfo"->'business_address'->>'lat')::numeric
                FROM "user_registration_informations"
                WHERE "user_registration_informations"."userId" = "User"."id"
                AND "user_registration_informations"."pageName" = 'practice-info'
                LIMIT 1
            )`),
            { [Op.between]: [lat - latDiff, lat + latDiff] }
        ),
        sequelize.where(
            sequelize.literal(`(
                SELECT ("payloadInfo"->'business_address'->>'lng')::numeric
                FROM "user_registration_informations"
                WHERE "user_registration_informations"."userId" = "User"."id"
                AND "user_registration_informations"."pageName" = 'practice-info'
                LIMIT 1
            )`),
            { [Op.between]: [lng - lngDiff, lng + lngDiff] }
        ),
    ];
}
if (req.query.therapy_for) {
    const therapyForValues = (req.query.therapy_for as string)
        .split(',')
        .map(val => val.trim())
        .filter(Boolean);

    if (therapyForValues.length > 0) {
        const orConditions = therapyForValues
            .map(val => `client = '${val}'`)
            .join(' OR ');

        (where as any)[Op.and] = [
            ...((where as any)[Op.and] || []),
            sequelize.where(
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(
                        (SELECT "payloadInfo"->'client_served'
                         FROM "user_registration_informations"
                         WHERE "user_registration_informations"."userId" = "User"."id"
                         AND "user_registration_informations"."pageName" = 'practice-focus'
                         LIMIT 1)::jsonb
                    ) AS client
                    WHERE ${orConditions}
                )`),
                true
            )
        ];

        logger.info(`Filtering therapists by therapy_for values: ${therapyForValues.join(', ')}`);
    }
}

if (req.query.therapy_for) {
    const therapyForValues = (req.query.therapy_for as string)
        .split(',')
        .map(val => val.trim())
        .filter(Boolean);

    if (therapyForValues.length > 0) {
        const orConditions = therapyForValues
            .map(val => `client = '${val}'`)
            .join(' OR ');

        (where as any)[Op.and] = [
            ...((where as any)[Op.and] || []),
            sequelize.where(
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(
                        (SELECT "payloadInfo"->'client_served'
                         FROM "user_registration_informations"
                         WHERE "user_registration_informations"."userId" = "User"."id"
                         AND "user_registration_informations"."pageName" = 'practice-focus'
                         LIMIT 1)::jsonb
                    ) AS client
                    WHERE ${orConditions}
                )`),
                true
            )
        ];

        logger.info(`Filtering therapists by therapy_for values: ${therapyForValues.join(', ')}`);
    }
}
if (req.query.gender_exploration) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // specialization.lgbtq includes 'lgbtqAffirmingCare'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'lgbtq')::jsonb ? 'lgbtqAffirmingCare'
            )`),
            true
        ),
        // specialization.relationship includes 'lgbtq'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'relationship')::jsonb ? 'lgbtq'
            )`),
            true
        )
    ];
}
if (req.query.trauma_ptsd) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'ptsd'
            )`),
            true
        )
    ];
}
if (req.query.need_talk) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // anxiety includes 'generalized-anxiety-disorder'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'anxiety')::jsonb ? 'generalized-anxiety-disorder'
            )`),
            true
        ),
        // depression includes 'major-depression'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'depression')::jsonb ? 'major-depression'
            )`),
            true
        ),
        // depression includes 'parental-depression'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'depression')::jsonb ? 'parental-depression'
            )`),
            true
        ),
        // adjustment includes 'stress/trauma'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'adjustment')::jsonb ? 'stress/trauma'
            )`),
            true
        )
    ];
}
if (req.query.military_veterans) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // 1. practice-focus.additional_focus.checked_list includes 'veterans issues'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["veterans issues"]'::jsonb
            )`),
            true
        ),
        // 2. specialization.trauma includes 'ptsd'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'ptsd'
            )`),
            true
        )
    ];
}
if (req.query.acc_injury) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // 1. practice-focus.additional_focus.checked_list includes 'accident,injury,illness'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["accident,injury,illness"]'::jsonb
            )`),
            true
        ),
        // 2. specialization.adjustment includes 'stress/trauma'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'adjustment')::jsonb ? 'stress/trauma'
            )`),
            true
        )
    ];
}
if (req.query.military) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // 1. specialization.trauma includes 'ptsd'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'ptsd'
            )`),
            true
        ),
        // 2. practice-focus.additional_focus.checked_list includes 'veterans issues'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["veterans issues"]'::jsonb
            )`),
            true
        )
    ];
}
if (req.query.multiculturalism) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // 1. specialization.trauma includes 'multiculturalism'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'multiculturalism'
            )`),
            true
        ),
        // 2. practice-focus.additional_focus.checked_list includes 'racial-identity'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["racial-identity"]'::jsonb
            )`),
            true
        )
    ];
}
if (req.query.cashPay) {
    const cashPayValues = (req.query.cashPay as string).split(',');

    const mappedCashPayValues = cashPayValues
        .map(method => paymentMappings[method.trim() as keyof typeof paymentMappings])
        .filter(Boolean);

    if (mappedCashPayValues.length > 0) {
        (where as any)[Op.or] = [
            ...((where as any)[Op.or] || []),
            sequelize.where(
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(
                        (SELECT "payloadInfo"->'payment_methods'
                        FROM "user_registration_informations"
                        WHERE "user_registration_informations"."userId" = "User"."id"
                        AND "user_registration_informations"."pageName" = 'payment-forms'
                        LIMIT 1)::jsonb
                    ) AS payment_method
                    WHERE payment_method IN (${mappedCashPayValues.map(value => `'${value}'`).join(',')})
                )`),
                true
            ),
        ];
    }
}

if (req.query.insurance) {
    const insuranceValues = (req.query.insurance as string).split(',');

    const mappedInsuranceValues = insuranceValues
        .map(item => insuranceMappings[item.trim() as keyof typeof insuranceMappings])
        .filter(Boolean);

    if (mappedInsuranceValues.length > 0) {
        (where as any)[Op.or] = [
            ...((where as any)[Op.or] || []),
            sequelize.where(
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(
                        (SELECT "payloadInfo"->'insurance_list'->'checked_list'
                        FROM "user_registration_informations"
                        WHERE "user_registration_informations"."userId" = "User"."id"
                        AND "user_registration_informations"."pageName" = 'payment-forms'
                        LIMIT 1)::jsonb
                    ) AS insurance_item
                    WHERE insurance_item IN (${mappedInsuranceValues.map(value => `'${value}'`).join(',')})
                )`),
                true
            ),
        ];
    }
}
		
		if (req.query.religion) {
			const religionValues = (req.query.religion as string).split(',');

			const mappedReligionValues = religionValues
				.map(religion => religionMapping[religion.trim() as keyof typeof religionMapping])
				.filter(Boolean);	
			if (mappedReligionValues.length > 0) {
				(where as any)[Op.or] = [
					...((where as any)[Op.or] || []),
					sequelize.where(
						sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_array_elements_text(
								(SELECT "payloadInfo"->'religious_specialization'->'checked_list'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'practice-focus'
								LIMIT 1)::jsonb
							) AS checked_religion
							WHERE checked_religion IN (${mappedReligionValues.map(value => `'${value}'`).join(',')})
						)`),
						true
					),
				];
			}
		}

		if (req.query.race) {
            const raceValue = raceMappings[req.query.race as keyof typeof raceMappings];
            if (raceValue) {
                (where as any)[Op.and] = [
                    ...((where as any)[Op.and] || []),
                    sequelize.where(
                        sequelize.literal(`(
                            SELECT "payloadInfo"->'race'->>'checked'
                            FROM "user_registration_informations"
                            WHERE "user_registration_informations"."userId" = "User"."id"
                            AND "user_registration_informations"."pageName" = 'profile'
                            LIMIT 1
                        )`),
                        raceValue
                    ),
                ];
            }
        }

		if (req.query.language) {
			const languageValues = (req.query.language as string).split(',');
		
			const mappedLanguageValues = languageValues
				.map(language => languageMapping[language.trim() as keyof typeof languageMapping])
				.filter(Boolean);
		
			if (mappedLanguageValues.length > 0) {
				(where as any)[Op.and] = [
					...((where as any)[Op.and] || []),
					sequelize.where(
						sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_array_elements_text(
								(SELECT "payloadInfo"->'language'->'checked_list'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'profile'
								LIMIT 1)::jsonb
							) AS checked_language
							WHERE checked_language IN (${mappedLanguageValues.map(value => `'${value}'`).join(',')})
						)`),
						true
					),
				];
			}
		}

		if (req.query.focusArea) {
			const focusAreaValues = (req.query.focusArea as string).split(',');
		
			const mappedFocusAreaValues = focusAreaValues
				.map(focusArea => focusAreaMapping[focusArea.trim() as keyof typeof focusAreaMapping])
				.filter(Boolean);
		
			if (mappedFocusAreaValues.length > 0) {
				(where as any)[Op.and] = [
					...((where as any)[Op.and] || []),
					sequelize.where(
						sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_array_elements_text(
								(SELECT "payloadInfo"->'additional_focus'->'checked_list'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'practice-focus'
								LIMIT 1)::jsonb
							) AS checked_focus
							WHERE checked_focus IN (${mappedFocusAreaValues.map(value => `'${value}'`).join(',')})
						)`),
						true
					),
				];
			}
		}

		if (req.query.modality) {
			const modalities = (req.query.modality as string).split(',').map(m => m.trim());
			const modalityKeys = modalities
				.map(modality => modalityMappings[modality as keyof typeof modalityMappings])
				.filter(Boolean);

			if (modalityKeys.length) {
				const conditions = modalityKeys.map(modalityKey => `
					EXISTS (
						SELECT 1
						FROM jsonb_each_text(
							(SELECT "payloadInfo"
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'modalities'
							LIMIT 1)::jsonb
						) AS kv
						WHERE kv.key = '${modalityKey}'
					)
				`).join(' or ');

				(where as any)[Op.and] = [
					...((where as any)[Op.and] || []),
					sequelize.where(
						sequelize.literal(`(${conditions})`),
						true
					),
				];
			}
		}




		conditions.forEach((key) => {
			const valueToCheck = req.query[key];
		
			if (valueToCheck) {
				const valuesToCheckArray = typeof valueToCheck === 'string' ? valueToCheck.split(',').map((v: string) => v.trim()) : [];
		
				(where as any)[Op.or] = [
					...((where as any)[Op.or] || []),
					Sequelize.where(
						Sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_each_text(
								(SELECT "payloadInfo"
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'specialization'
								LIMIT 1)::jsonb
							) AS kv
							WHERE kv.key = '${key}'
							AND kv.value::jsonb ?| array[${valuesToCheckArray.map(v => `'${v}'`).join(',')}]
						)`),
						true
					),
				];
			}
		});

		
		if (req.query.specialization) {
			const specializationValues = (req.query.specialization as string).split(',').map(value => value.trim());
			if (specializationValues.length > 0) {
				(where as any)[Op.or] = [
					...((where as any)[Op.or] || []),
					Sequelize.where(
						Sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_each_text(
								(SELECT "payloadInfo"
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'specialization'
								LIMIT 1)::jsonb
							) AS kv
							WHERE kv.key IN (${specializationValues.map(value => `'${value}'`).join(',')})
						)`),
						true
					),
				];
			}
		}
		if (req.query.genderUser) {
			const genders = Array.isArray(req.query.genderUser)
				? req.query.genderUser.map((g) => String(g).toLowerCase())
				: String(req.query.genderUser)
					.split(',')
					.map((g) => g.trim().toLowerCase());
		
			const filteredGenders = genders.filter((gender) => validGenders.includes(gender));
		
			if (filteredGenders.length > 0) {
				(where as any)[Op.and] = [
					...((where as any)[Op.and] || []),
					sequelize.where(
						sequelize.literal(`(
							SELECT "payloadInfo"->'identify'->>'checked'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'profile'
							LIMIT 1
						)`),
						{ [Op.in]: filteredGenders }
					),
				];
			} else {
				throw new Error('Invalid gender value(s)');
			}
		}
		if (req.query.culture) {
    (where as any)[Op.or] = [
        ...((where as any)[Op.or] || []),
        // 1. Check for specialization with trauma including 'multiculturalism'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'multiculturalism'
            )`),
            true
        ),
        // 2. Check for practice-focus with additional_focus.checked_list including 'racial-identity'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["racial-identity"]'::jsonb
            )`),
            true
        )
    ];
}
	

		const order: Order = []

		if (req.user?.role === UserType.PATIENT) {
			const patientId = req.user.id;
		
			order.push([
				sequelize.literal(`(
					SELECT MAX(latest_interaction) FROM (
						SELECT MAX("createdAt") AS latest_interaction
						FROM "appointments"
						WHERE "appointments"."therapistId" = "User"."id"
						AND "appointments"."patientId" = ${patientId}
						
						UNION
						
						SELECT MAX("createdAt") AS latest_interaction
						FROM "therapist_waitlist"
						WHERE "therapist_waitlist"."therapistId" = "User"."id"
						AND "therapist_waitlist"."patientId" = ${patientId}
					) AS interactions
				)`),
				'DESC NULLS LAST'
			]);
		}

		order.push(['id', 'DESC'])

		const include: Includeable[] = [
			{
				model: NormalOfficeHours,
				attributes: [],
				required: true,
			},
			{
				model: UserRegistrationInfo,
				as: 'registrationInfo',
				attributes: ['pageName', 'payloadInfo'],
				required: false,
			},
			{
				model: TherapistSubscription,
				where: {
					isActive: true,
				},
				attributes: [],
				required: true,
			},
			{
				model: StripeInfo,
				where: {
					status: 'active'
				},
				attributes: [],
				required: true,
			}
		];

		const attributes: FindAttributeOptions = []

		const { search } = req.query

		if (search) {
				const searchParts = (search as string).split(' ');
		
				where = {
						...where,
						[Op.or]: [
								{ firstname: { [Op.iLike]: `%${search}%` } },
								{ lastname: { [Op.iLike]: `%${search}%` } },
								{
										[Op.and]: [
												{ firstname: { [Op.iLike]: `%${searchParts[0] || ''}%` } },
												{ lastname: { [Op.iLike]: `%${searchParts[1] || ''}%` } }
										]
								}
						]
				};
		}
		if (req.query.sessionType) {
			const sessionTypes = Array.isArray(req.query.sessionType)
				? req.query.sessionType.map((type) => String(type).toLowerCase())
				: String(req.query.sessionType).toLowerCase().split(',').map((type) => type.trim());
		
			const includeConditions: any[] = [];
		
			if (sessionTypes.includes(TELEHEALTH) && sessionTypes.includes(IN_PERSON)) {
				includeConditions.push(
					sequelize.where(
						sequelize.literal(`(
							SELECT "payloadInfo"->>'appointmentMethod'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'normal-office-hours'
							LIMIT 1
						)`),
						'in-person-and-telehealth'
					)
				);
			} else {
				if (sessionTypes.includes(TELEHEALTH)) {
					includeConditions.push(
						sequelize.where(
							sequelize.literal(`(
								SELECT "payloadInfo"->>'appointmentMethod'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'normal-office-hours'
								LIMIT 1
							)`),
							'telehealth'
						)
					);
				}
		
				if (sessionTypes.includes(IN_PERSON)) {
					includeConditions.push(
						sequelize.where(
							sequelize.literal(`(
								SELECT "payloadInfo"->>'appointmentMethod'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'normal-office-hours'
								LIMIT 1
							)`),
							'in-person'
						)
					);
				}
			}
		
			if (includeConditions.length > 0) {
				(where as any)[Op.and] = [
					...((where as any)[Op.and] || []),
					{ [Op.or]: includeConditions },
				];
			}
		}
		
		// Get the pagination parameters
		const paginationParams = paginated(req);
		logger.info(`Pagination parameters: ${JSON.stringify(paginationParams)}`);

		// Execute the query
		const therapists = await User.findAndCountAll({
			attributes: {
				include: attributes,
			},
			include,
			where,
			group,
			order,
			distinct: true,
			// ...paginationParams,
		});
		  

		if (req.query.count) {
			const totalCount = therapists.count.length > 0 ? (Array.isArray(therapists.count) ? therapists.count[0]?.count : therapists.count) : 0;
			return res.json({
				count: totalCount,
			});
		}

	therapists.rows.sort((a: any, b: any) => {
    const firstNameComparison = a.firstname.localeCompare(b.firstname);
    
    if (firstNameComparison === 0) {
        return a.lastname.localeCompare(b.lastname);
    }
    
    return firstNameComparison;
	});

        
    therapists.rows.forEach((therapist: any) => {
		const registrationInfo = therapist.registrationInfo || [];
	
		const officeHoursEntry = registrationInfo.find((info: any) => info.pageName === 'normal-office-hours') || {};
		const officeHoursPayload = officeHoursEntry.payloadInfo || {};
		const appointmentMethod = officeHoursPayload?.appointmentMethod || null;
		therapist.dataValues.inPersonFlag = appointmentMethod === IN_PERSON;
		therapist.dataValues.teleHealthFlag = appointmentMethod === TELEHEALTH;
		if (appointmentMethod === IN_PERSON_TELEHEALTH) {
			therapist.dataValues.inPersonFlag = true;
			therapist.dataValues.teleHealthFlag = true;
		}
	
		// Extract paymentForms
		const paymentFormsEntry = registrationInfo.find((info: any) => info.pageName === 'payment-forms') || {};
		const paymentForm = paymentFormsEntry.payloadInfo || {};
		if (paymentForm.insurance_list && paymentForm.insurance_list.checked_list) {
			paymentForm.insurance_list = paymentForm.insurance_list.checked_list;
		}
		therapist.dataValues.paymentForms = paymentForm;
	
		// Extract practiceInfo
		const practiceInfoEntry = registrationInfo.find((info: any) => info.pageName === 'practice-info') || {};
		therapist.dataValues.practiceInfo = practiceInfoEntry.payloadInfo || {};
	
		// Map firstname and lastname to firstName and lastName
		therapist.dataValues.firstName = therapist.dataValues.firstname;
		therapist.dataValues.lastName = therapist.dataValues.lastname;
		delete therapist.dataValues.firstname;
		delete therapist.dataValues.lastname;
	
		// Remove registrationInfo from the response
		delete therapist.dataValues.registrationInfo;
	});
	res.send(therapists.rows)

	// Generate the paginated response
	const paginatedResponse = paginatedData(therapists, req);
	
	// res.send(paginatedResponse);
}))

/********************************
 * * Get therapists with their first three available hours
 ********************************/
router.get(
	'/available-hours',
	wrap(async (req: Request, res: Response) => {
		const { name, search,culture } = req.query;

		logger.info(`User ${req.user?.id} requested available hours.`);
		logger.info(`Query parameters: ${JSON.stringify(req.query)}`);

		// First query: Check if firstname or lastname matches
		let whereClause: any = {
    role: UserType.THERAPIST,
    emailVerifiedAt: { [Op.not]: null },
    acceptedAt: { [Op.not]: null },
    deletedAt: null,
    deactivatedAt: null,
    rejectedAt: null,
    active: true,
};
// Add the culture filter to the existing whereClause
if (culture === 'true') {
    whereClause[Op.and] = [
        ...(whereClause[Op.and] || []),
        {
            [Op.or]: [
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM "user_registration_informations" uri
                    WHERE uri."userId" = "User"."id"
                    AND uri."pageName" = 'specialization'
                    AND (uri."payloadInfo"->'trauma')::jsonb ? 'multiculturalism'
                )`),
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM "user_registration_informations" uri
                    WHERE uri."userId" = "User"."id"
                    AND uri."pageName" = 'practice-focus'
                    AND (uri."payloadInfo"->'additional_focus'->'checked_list')::jsonb @> '["racial-identity"]'::jsonb
                )`)
            ]
        }
    ];
}

// Add this block right after the whereClause initialization
if (req.query.searchId) {
    whereClause = {
        ...whereClause,
        id: parseInt(req.query.searchId as string)
    };
    logger.info(`Filtering therapist by specific ID: ${req.query.searchId}`);
}

		// Add all filters from the GET '/' API
		// 1. License state filtering if patLat and patLng are provided
		if (req.query.patLat && req.query.patLng) {
			let lat = parseFloat(req.query.patLat as string);
			let lng = parseFloat(req.query.patLng as string);
			
			async function getStateFromCoordinates(lat: number, lng: number): Promise<string | null> {
				if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
					logger.info(`Invalid coordinates: lat=${lat}, lng=${lng}`);
					return null;
				}
				
				const formattedLat = parseFloat(lat.toFixed(6));
				const formattedLng = parseFloat(lng.toFixed(6));
				
				// Check if state name is cached in Redis
				const cacheKey = `state:${formattedLat}:${formattedLng}`;
				
				try {
					// Try to get state from cache
					const cachedState = await client.get(cacheKey);
					if (cachedState) {
						logger.info(`Found state in cache: ${cachedState} for coordinates: lat=${formattedLat}, lng=${formattedLng}`);
						return cachedState;
					}
					
					// If not in cache, proceed with geocoding
					logger.info(`State not found in cache for coordinates: lat=${formattedLat}, lng=${formattedLng}, proceeding with geocoding`);
					
					// Try multiple geocoding providers in sequence
					const providers = [
						{
							name: 'openstreetmap',
							options: { provider: 'openstreetmap' }
						},
						{
							name: 'google',
							options: { 
								provider: 'google',
								apiKey: process.env.GOOGLE_MAPS_API_KEY || 'YOUR_API_KEY'
							}
						}
					];
					
					for (const provider of providers) {
						try {						
							const geocoder = NodeGeocoder(provider.options);
							const result = await geocoder.reverse({ lat: formattedLat, lng: formattedLng });
							
							if (result && result.length > 0) {
								const state = result[0].state || result[0].administrativeLevels?.level1long || null;
								
								if (state) {
									// Cache the state in Redis for future use (expire after 30 days)
									await client.set(cacheKey, state);
									await client.expire(cacheKey, 60 * 60 * 24 * 30); // 30 days expiration
									logger.info(`Cached state: ${state} for coordinates: lat=${formattedLat}, lng=${formattedLng}`);
									return state;
								}
							}
						} catch (error: any) {
							logger.info(error, "error");
						}
					}
					
					try {
						const response = await fetch(
							`https://nominatim.openstreetmap.org/reverse?format=json&lat=${formattedLat}&lon=${formattedLng}&zoom=10`,
							{
								headers: {
									'User-Agent': 'TherapistApp/1.0'
								}
							}
						);
						
						if (response.ok) {
							const data = await response.json();
							logger.info("Nominatim direct API result:", JSON.stringify(data, null, 2));
							
							if (data.address) {
								const state = data.address.state || 
											data.address.region || 
											data.address.province || 
											data.address.county;
								
								if (state) {
									// Cache the state in Redis for future use (expire after 30 days)
									// Fixed Redis syntax - using separate ex parameter
									await client.set(cacheKey, state);
									await client.expire(cacheKey, 60 * 60 * 24 * 30); // 30 days expiration
									logger.info(`Found and cached state: ${state} using direct Nominatim API`);
									return state;
								}
							}
						}
					} catch (error: any) {
						logger.info("Direct Nominatim API error:", error.message);
					}
					
					return null;
				} catch (cacheError: any) {
					logger.info(`Redis cache error: ${cacheError.message}`);
					return null;
				}
			}
			
			// Get the state name
			try {
				const stateName = await getStateFromCoordinates(lat, lng);
				
				// Use the state name to filter therapists by license state
				if (stateName) {
					whereClause[Op.and] = [
						...(whereClause[Op.and] || []),
						sequelize.where(
							sequelize.literal(`(
								SELECT EXISTS (
									SELECT 1
									FROM jsonb_array_elements(("payloadInfo"->'licenses')::jsonb) AS license
									WHERE license->'license_state'->>'label' = '${stateName}'
									AND (license->'license_status'->>'label' = 'Active' OR license->'license_status'->>'label' = 'Provisional')
									AND to_date(license->>'expiry_date', 'MM-DD-YYYY') >= CURRENT_DATE
								)
								OR EXISTS (
									SELECT 1
									FROM jsonb_array_elements(("payloadInfo"->'compact_licenses')::jsonb) AS compact_license
									WHERE compact_license->'compact_privilege'->>'label' = 'Yes'
									AND EXISTS (
										SELECT 1
										FROM jsonb_array_elements(compact_license->'compact_states') AS compact_state
										WHERE compact_state->>'label' = '${stateName}'
									)
								)
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'license'
								LIMIT 1
							)`),
							true
						)
					];
										
				}
			} catch (error: any) {
				logger.info("Error getting state from coordinates:", error.message);
			}
		}

		// 2. Lat/Lng for distance-based filtering
		if (req.query.lat && req.query.lng) {
            let lat = parseFloat(req.query.lat as string);
            let lng = parseFloat(req.query.lng as string);
        
            const milesRadius = 30;
            const latDiff = milesRadius / 69;
            const lngDiff = milesRadius / (69 * Math.cos(lat * (Math.PI / 180)));
        
            whereClause[Op.and] = [
                ...(whereClause[Op.and] || []),
                sequelize.where(
                    sequelize.literal(`(
                        SELECT ("payloadInfo"->>'business_address'->>'lat')::numeric
                        FROM "user_registration_informations"
                        WHERE "user_registration_informations"."userId" = "User"."id"
                        AND "user_registration_informations"."pageName" = 'practice-info'
                        LIMIT 1
                    )`),
                    { [Op.between]: [lat - latDiff, lat + latDiff] }
                ),
                sequelize.where(
                    sequelize.literal(`(
                        SELECT ("payloadInfo"->>'business_address'->>'lng')::numeric
                        FROM "user_registration_informations"
                        WHERE "user_registration_informations"."userId" = "User"."id"
                        AND "user_registration_informations"."pageName" = 'practice-info'
                        LIMIT 1
                    )`),
                    { [Op.between]: [lng - lngDiff, lng + lngDiff] }
                ),
            ];
        }
	if (req.query.gender_exploration) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        // specialization.lgbtqAff includes 'lgbtqAffirmingCare'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'lgbtq')::jsonb ? 'lgbtqAffirmingCare'
            )`),
            true
        ),
        // specialization.relationship includes 'lgbtq'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'relationship')::jsonb ? 'lgbtq'
            )`),
            true
        )
    ];
}
if (req.query.trauma_ptsd) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'ptsd'
            )`),
            true
        )
    ];
}
if (req.query.need_talk) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        // anxiety includes 'generalized-anxiety-disorder'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'anxiety')::jsonb ? 'generalized-anxiety-disorder'
            )`),
            true
        ),
        // depression includes 'major-depression'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'depression')::jsonb ? 'major-depression'
            )`),
            true
        ),
        // depression includes 'parental-depression'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'depression')::jsonb ? 'parental-depression'
            )`),
            true
        ),
        // adjustment includes 'stress/trauma'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'adjustment')::jsonb ? 'stress/trauma'
            )`),
            true
        )
    ];
}
if (req.query.military_veterans) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        // 1. practice-focus.additional_focus.checked_list includes 'veterans issues'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["veterans issues"]'::jsonb
            )`),
            true
        ),
        // 2. specialization.trauma includes 'ptsd'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'ptsd'
            )`),
            true
        )
    ];
}
		if (req.query.acc_injury) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        // 1. practice-focus.additional_focus.checked_list includes 'accident,injury,illness'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["accident,injury,illness"]'::jsonb
            )`),
            true
        ),
        // 2. specialization.adjustment includes 'stress/trauma'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'adjustment')::jsonb ? 'stress/trauma'
            )`),
            true
        )
    ];
}

if (req.query.military) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        // 1. specialization.trauma includes 'ptsd'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'ptsd'
            )`),
            true
        ),
        // 2. practice-focus.additional_focus.checked_list includes 'veterans issues'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["veterans issues"]'::jsonb
            )`),
            true
        )
    ];
}

		if (req.query.multiculturalism) {
    whereClause[Op.or] = [
        ...(whereClause[Op.or] || []),
        // 1. specialization.trauma includes 'multiculturalism'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'specialization'
                AND (uri."payloadInfo"->'trauma')::jsonb ? 'multiculturalism'
            )`),
            true
        ),
        // 2. practice-focus.additional_focus.checked_list includes 'racial-identity'
        sequelize.where(
            sequelize.literal(`EXISTS (
                SELECT 1
                FROM "user_registration_informations" uri
                WHERE uri."userId" = "User"."id"
                AND uri."pageName" = 'practice-focus'
                AND (uri."payloadInfo"::jsonb->'additional_focus'->'checked_list')::jsonb @> '["racial-identity"]'::jsonb
            )`),
            true
        )
    ];
}
		// 3. Religion filtering
		if (req.query.religion) {
			const religionValues = (req.query.religion as string).split(',');

			const mappedReligionValues = religionValues
				.map(religion => religionMapping[religion.trim() as keyof typeof religionMapping])
				.filter(Boolean);	
			if (mappedReligionValues.length > 0) {
				whereClause[Op.or] = [
					...(whereClause[Op.or] || []),
					sequelize.where(
						sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_array_elements_text(
								(SELECT "payloadInfo"->'religious_specialization'->>'checked_list'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'practice-focus'
								LIMIT 1)::jsonb
							) AS checked_religion
							WHERE checked_religion IN (${mappedReligionValues.map(value => `'${value}'`).join(',')})
						)`),
						true
					),
				];
			}
		}

		// 4. Race filtering
		if (req.query.race) {
            const raceValue = raceMappings[req.query.race as keyof typeof raceMappings];
            if (raceValue) {
                whereClause[Op.and] = [
                    ...(whereClause[Op.and] || []),
                    sequelize.where(
                        sequelize.literal(`(
                            SELECT "payloadInfo"->'race'->>'checked'
                            FROM "user_registration_informations"
                            WHERE "user_registration_informations"."userId" = "User"."id"
                            AND "user_registration_informations"."pageName" = 'profile'
                            LIMIT 1
                        )`),
                        raceValue
                    ),
                ];
            }
        }

		// 5. Language filtering
		if (req.query.language) {
			const languageValues = (req.query.language as string).split(',');
		
			const mappedLanguageValues = languageValues
				.map(language => languageMapping[language.trim() as keyof typeof languageMapping])
				.filter(Boolean);
		
			if (mappedLanguageValues.length > 0) {
				whereClause[Op.and] = [
					...(whereClause[Op.and] || []),
					sequelize.where(
				sequelize.literal(`EXISTS (
					SELECT 1
					FROM jsonb_array_elements_text(
					(
						SELECT "payloadInfo"->'language'->'checked_list'
						FROM "user_registration_informations"
						WHERE "user_registration_informations"."userId" = "User"."id"
						AND "user_registration_informations"."pageName" = 'profile'
						LIMIT 1
					)::jsonb
					) AS checked_language
					WHERE checked_language IN (${mappedLanguageValues.map(value => `'${value}'`).join(',')})
				)`),
				true
				),
				];
			}
		}

		// 6. Focus Area filtering
		if (req.query.focusArea) {
			const focusAreaValues = (req.query.focusArea as string).split(',');
		
			const mappedFocusAreaValues = focusAreaValues
				.map(focusArea => focusAreaMapping[focusArea.trim() as keyof typeof focusAreaMapping])
				.filter(Boolean);
		
			if (mappedFocusAreaValues.length > 0) {
				whereClause[Op.and] = [
					...(whereClause[Op.and] || []),
					sequelize.where(
  sequelize.literal(`EXISTS (
    SELECT 1
    FROM jsonb_array_elements_text(
      (
        SELECT "payloadInfo"->'additional_focus'->'checked_list'
        FROM "user_registration_informations"
        WHERE "user_registration_informations"."userId" = "User"."id"
        AND "user_registration_informations"."pageName" = 'practice-focus'
        LIMIT 1
      )::jsonb
    ) AS checked_focus
    WHERE checked_focus IN (${mappedFocusAreaValues.map(value => `'${value}'`).join(',')})
  )`),
  true
)

				];
			}
		}

		// 7. Modality filtering
		if (req.query.modality) {
			const modalities = (req.query.modality as string).split(',').map(m => m.trim());
			const modalityKeys = modalities
				.map(modality => modalityMappings[modality as keyof typeof modalityMappings])
				.filter(Boolean);

			if (modalityKeys.length) {
				const conditions = modalityKeys.map(modalityKey => `
					EXISTS (
						SELECT 1
						FROM jsonb_each_text(
							(SELECT "payloadInfo"
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'modalities'
							LIMIT 1)::jsonb
						) AS kv
						WHERE kv.key = '${modalityKey}'
					)
				`).join(' or ');

				whereClause[Op.and] = [
					...(whereClause[Op.and] || []),
					sequelize.where(
						sequelize.literal(`(${conditions})`),
						true
					),
				];
			}
		}

		// 8. Conditions filtering
		conditions.forEach((key) => {
			const valueToCheck = req.query[key];
		
			if (valueToCheck) {
				const valuesToCheckArray = typeof valueToCheck === 'string' ? valueToCheck.split(',').map((v: string) => v.trim()) : [];
		
				whereClause[Op.or] = [
					...(whereClause[Op.or] || []),
					Sequelize.where(
						Sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_each_text(
								(SELECT "payloadInfo"
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'specialization'
								LIMIT 1)::jsonb
							) AS kv
							WHERE kv.key = '${key}'
							AND kv.value::jsonb ?| array[${valuesToCheckArray.map(v => `'${v}'`).join(',')}]
						)`),
						true
					),
				];
			}
		});

		// 9. Specialization filtering
		if (req.query.specialization) {
			const specializationValues = (req.query.specialization as string).split(',').map(value => value.trim());
			if (specializationValues.length > 0) {
				whereClause[Op.or] = [
					...(whereClause[Op.or] || []),
					Sequelize.where(
						Sequelize.literal(`EXISTS (
							SELECT 1
							FROM jsonb_each_text(
								(SELECT "payloadInfo"
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'specialization'
								LIMIT 1)::jsonb
							) AS kv
							WHERE kv.key IN (${specializationValues.map(value => `'${value}'`).join(',')})
						)`),
						true
					),
				];
			}
		}

		// 10. Gender filtering
		if (req.query.genderUser) {
			const genders = Array.isArray(req.query.genderUser)
				? req.query.genderUser.map((g) => String(g).toLowerCase())
				: String(req.query.genderUser)
					.split(',')
					.map((g) => g.trim().toLowerCase());
		
			const filteredGenders = genders.filter((gender) => validGenders.includes(gender));
		
			if (filteredGenders.length > 0) {
				whereClause[Op.and] = [
					...(whereClause[Op.and] || []),
					sequelize.where(
						sequelize.literal(`(
							SELECT "payloadInfo"->'identify'->>'checked'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'profile'
							LIMIT 1
						)`),
						{ [Op.in]: filteredGenders }
					),
				];
			} else {
				throw new Error('Invalid gender value(s)');
			}
		}
			if (req.query.cashPay) {
	const cashPayValues = (req.query.cashPay as string).split(',');

	const mappedCashPayValues = cashPayValues
		.map(method => paymentMappings[method.trim() as keyof typeof paymentMappings])
		.filter(Boolean);	

	if (mappedCashPayValues.length > 0) {
		whereClause[Op.or] = [
			...(whereClause[Op.or] || []),
			sequelize.where(
				sequelize.literal(`EXISTS (
					SELECT 1
					FROM jsonb_array_elements_text(
						(SELECT "payloadInfo"->'payment_methods'
						FROM "user_registration_informations"
						WHERE "user_registration_informations"."userId" = "User"."id"
						AND "user_registration_informations"."pageName" = 'payment-forms'
						LIMIT 1)::jsonb
					) AS payment_method
					WHERE payment_method IN (${mappedCashPayValues.map(value => `'${value}'`).join(',')})
				)`),
				true
			),
		];
	}
}
if (req.query.insurance) {
	const insuranceValues = (req.query.insurance as string).split(',');

	const mappedInsuranceValues = insuranceValues
		.map(item => insuranceMappings[item.trim() as keyof typeof insuranceMappings])
		.filter(Boolean);

	if (mappedInsuranceValues.length > 0) {
		whereClause[Op.or] = [
			...(whereClause[Op.or] || []),
			sequelize.where(
				sequelize.literal(`EXISTS (
					SELECT 1
					FROM jsonb_array_elements_text(
						(SELECT "payloadInfo"->'insurance_list'->'checked_list'
						FROM "user_registration_informations"
						WHERE "user_registration_informations"."userId" = "User"."id"
						AND "user_registration_informations"."pageName" = 'payment-forms'
						LIMIT 1)::jsonb
					) AS insurance_item
					WHERE insurance_item IN (${mappedInsuranceValues.map(value => `'${value}'`).join(',')})
				)`),
				true
			),
		];
	}
}


		// 12. Session Type filtering
		if (req.query.sessionType) {
			const sessionTypes = Array.isArray(req.query.sessionType)
				? req.query.sessionType.map((type) => String(type).toLowerCase())
				: String(req.query.sessionType).toLowerCase().split(',').map((type) => type.trim());
		
			const includeConditions: any[] = [];
		
			if (sessionTypes.includes(TELEHEALTH) && sessionTypes.includes(IN_PERSON)) {
				includeConditions.push(
					sequelize.where(
						sequelize.literal(`(
							SELECT "payloadInfo"->>'appointmentMethod'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id"
							AND "user_registration_informations"."pageName" = 'normal-office-hours'
							LIMIT 1
						)`),
						'in-person-and-telehealth'
					)
				);
			} else {
				if (sessionTypes.includes(TELEHEALTH)) {
					includeConditions.push(
						sequelize.where(
							sequelize.literal(`(
								SELECT "payloadInfo"->>'appointmentMethod'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'normal-office-hours'
								LIMIT 1
							)`),
							'telehealth'
						)
					);
				}
		
				if (sessionTypes.includes(IN_PERSON)) {
					includeConditions.push(
						sequelize.where(
							sequelize.literal(`(
								SELECT "payloadInfo"->>'appointmentMethod'
								FROM "user_registration_informations"
								WHERE "user_registration_informations"."userId" = "User"."id"
								AND "user_registration_informations"."pageName" = 'normal-office-hours'
								LIMIT 1
							)`),
							'in-person'
						)
					);
				}
			}
		
			if (includeConditions.length > 0) {
				whereClause[Op.and] = [
					...(whereClause[Op.and] || []),
					{ [Op.or]: includeConditions },
				];
			}
		}

		// 13. Name/Search filtering - FIXED: Now properly applying the search condition
		if (search) {
			const searchTerm = search || name;
			const searchParts = (searchTerm as string).split(' ');
			
			const nameSearchConditions = [
				{ firstname: { [Op.iLike]: `%${searchTerm}%` } },
				{ lastname: { [Op.iLike]: `%${searchTerm}%` } }
			];

			// If multiple words, also search for first name + last name combination
			if (searchParts.length > 1) {
				nameSearchConditions.push({
					[Op.and]: [
						{ firstname: { [Op.iLike]: `%${searchParts[0] || ''}%` } },
						{ lastname: { [Op.iLike]: `%${searchParts.slice(1).join(' ') || ''}%` } }
					]
				} as any);
			}

			// Add name search to the where clause
			whereClause[Op.and] = [
				...(whereClause[Op.and] || []),
				{ [Op.or]: nameSearchConditions }
			];
		}

if (req.query.therapy_for) {
    const therapyForValues = (req.query.therapy_for as string)
        .split(',')
        .map(val => val.trim())
        .filter(Boolean);

    if (therapyForValues.length > 0) {
        const orConditions = therapyForValues
            .map(val => `client = '${val}'`)
            .join(' OR ');

        whereClause[Op.and] = [
            ...(whereClause[Op.and] || []),
            sequelize.where(
                sequelize.literal(`EXISTS (
                    SELECT 1
                    FROM jsonb_array_elements_text(
                        (SELECT "payloadInfo"->'client_served'
                         FROM "user_registration_informations"
                         WHERE "user_registration_informations"."userId" = "User"."id"
                         AND "user_registration_informations"."pageName" = 'practice-focus'
                         LIMIT 1)::jsonb
                    ) AS client
                    WHERE ${orConditions}
                )`),
                true
            )
        ];

        logger.info(`Filtering therapists by therapy_for values: ${therapyForValues.join(', ')}`);
    }
}

		const order: Order = []

		order.push(
			['firstname', 'ASC'],
			['lastname', 'ASC'],
			['createdAt', 'DESC']
		);

		// Perform the query
		let therapistData = await User.findAndCountAll({
			where: whereClause,
			order,
			attributes: {
				include: [
					[
						sequelize.literal(`(
							SELECT "payloadInfo"->>'user_profile'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id" AND "user_registration_informations"."pageName" = 'profile'
							LIMIT 1
						)`),
						'profilePicture',
					],
					[
						sequelize.literal(`(
							SELECT "payloadInfo"->>'session_fee'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id" AND "user_registration_informations"."pageName" = 'payment-forms'
							LIMIT 1
						)`),
						'sessionFee',
					],
					[
						sequelize.literal(`(
							SELECT "payloadInfo"->>'business_address'
							FROM "user_registration_informations"
							WHERE "user_registration_informations"."userId" = "User"."id" AND "user_registration_informations"."pageName" = 'practice-info'
							LIMIT 1
						)`),
						'practiceInfo',
					],
				],
			},
			include: [
				{
					model: NormalOfficeHours,
					required: true,
					attributes: ['id', 'workingDay', 'availabilityHours', 'appointmentMethod'],
					where: {
						isDisabled: false,
						deletedAt: null,
					}
				},
				{
					model: Appointments,
					as: 'therapistAppointments',
					attributes: ['id', 'workingHourId', 'appointmentDate', 'duration'],
					where: {
						cancelledAt: null,
						appointmentDate: {
							[Op.gte]: dayjs().toDate(),
						},
					},
					required: false,
				},
				{ model: Calendar, attributes: ['type', 'credentials'] },
				{
					model: UserRegistrationInfo,
					attributes: ['pageName', 'payloadInfo'],
					where: {
						pageName: {
							[Op.in]: ['waitlist-notifications', 'normal-office-hours'],
						}
					},
					required: false,
				},
				{
					model: TherapistSubscription,
					where: {
						isActive: true,
					},
					attributes: [],
					required: true,
				},
				{
					model: StripeInfo,
					where: {
						status: 'active'
					},
					attributes: [],
					required: true,
				},
			],
			distinct: true,
			...paginated(req)
		});

		logger.info(` Found ${therapistData.count} therapists for User ${req.user?.id}.`);


		// If no results were found, attempt full name search (first + last)
		if (name && therapistData.count === 0) {
			const nameParts = (name as string).split(' ');
			if (nameParts.length > 1) {
				whereClause[Op.or] = [
					{
						[Op.and]: [
							{ firstname: { [Op.iLike]: `%${nameParts[0]}%` } },
							{ lastname: { [Op.iLike]: `%${nameParts.slice(1).join(' ')}%` } }
						]
					}
				];

				therapistData = await User.findAndCountAll({
					where: whereClause,
					order,
					attributes: {
						include: [
							[
								sequelize.literal(`(
									SELECT "payloadInfo"->>'user_profile'
									FROM "user_registration_informations"
									WHERE "user_registration_informations"."userId" = "User"."id" AND "user_registration_informations"."pageName" = 'profile'
									LIMIT 1
								)`),
								'profilePicture',
							],
							[
								sequelize.literal(`(
									SELECT "payloadInfo"->>'session_fee'
									FROM "user_registration_informations"
									WHERE "user_registration_informations"."userId" = "User"."id" AND "user_registration_informations"."pageName" = 'payment-forms'
									LIMIT 1
								)`),
								'sessionFee',
							],
							[
								sequelize.literal(`(
									SELECT "payloadInfo"->>'business_address'
									FROM "user_registration_informations"
									WHERE "user_registration_informations"."userId" = "User"."id" AND "user_registration_informations"."pageName" = 'practice-info'
									LIMIT 1
								)`),
								'practiceInfo',
							],
						],
					},
					include: [
						{
							model: NormalOfficeHours,
							required: true,
							attributes: ['id', 'workingDay', 'availabilityHours', 'appointmentMethod'],
							where: {
								isDisabled: false,
								deletedAt: null,
							}
						},
						{
							model: Appointments,
							as: 'therapistAppointments',
							attributes: ['id', 'workingHourId', 'appointmentDate', 'duration'],
							where: {
								cancelledAt: null,
								appointmentDate: {
									[Op.gte]: dayjs().toDate(),
								},
							},
							required: false,
						},
						{ model: Calendar, attributes: ['type', 'credentials'] },
						// { model: TherapistProfile, as: 'profile', attributes: ['bio', 'acceptingPatients', 'acceptsInsurance', 'telehealth', 'InPerson', 'license', 'education', 'experience', 'therapyStyle'] },
						// { model: TherapistDurationAndCost, as: 'durationAndCost', attributes: ['duration', 'amount', 'category'] },
						{
							model: UserRegistrationInfo,
							attributes: ['pageName', 'payloadInfo'],
							where: {
								pageName: {
									[Op.in]: ['waitlist-notifications', 'normal-office-hours'],
								}
							},
							required: false,
						},
						{
							model: TherapistSubscription,
							where: {
								isActive: true,
							},
							attributes: [],
							required: true,
						},
						{
							model: StripeInfo,
							where: {
								status: 'active'
							},
							attributes: [],
							required: true,
						},
						{
							model: TherapistWaitlist,
							where: {
								patientId: req?.user?.id,
								status: {
									[Op.in]: ['waiting', 'notified'],
								},
							},
							required: false,
							attributes: ['id', 'patientId', 'status'],
						}
					],
					distinct: true,
					...paginated(req)
				});
			}
		}

		const therapists = therapistData.rows;
		if (therapists.length === 0) return res.send(paginatedData({ rows: [], count: therapistData.count }, req));

		const response = [];

		for (const therapist of therapists) {
			const waitlistNotifInfo = therapist.registrationInfo.find(
				(info) => info.pageName === 'waitlist-notifications'
			)?.payloadInfo as unknown as WaitlistNotifications || null;

			const therapistWaitlist = therapist?.therapistWaitlist?.[0] || null;
			const availableOfficeHours = await getAvailableOfficeHours({
				therapistId: therapist.id,
				officeHours: therapist.normalOfficeHours,
				appointments: therapist.therapistAppointments,
				therapistTimeZone: therapist.timeZone,
				calendars: therapist.calendars,
				waitlistNotifInfo,
				returnAvailableHours: true,
				returnHoursCount: 3,
				therapistWaitlist,
			});

			let practiceInfo = therapist.dataValues.practiceInfo;
			if (practiceInfo) {
				try {
					const parsedPracticeInfo = JSON.parse(practiceInfo);
					practiceInfo = {
						practice_name: parsedPracticeInfo.practice_name || '',
						business_phone: parsedPracticeInfo.business_phone || '',
						business_email: parsedPracticeInfo.business_email || '',
						business_address: {
							street: parsedPracticeInfo.street || '',
							city: parsedPracticeInfo.city || '',
							country: parsedPracticeInfo.country || '',
							full_address: parsedPracticeInfo.full_address || '',
							description: parsedPracticeInfo.description || '',
							zipCode: parsedPracticeInfo.zipCode || '',
							lat: parsedPracticeInfo.lat || 0,
							lng: parsedPracticeInfo.lng || 0,
							place_id: parsedPracticeInfo.place_id || '',
							zone: parsedPracticeInfo.zone || '',
							state:parsedPracticeInfo.state || '',
						}
					};
				} catch (error) {
					console.error('Error parsing practiceInfo:', error);
				}
			}
			const officeHoursInfo = therapist.registrationInfo.find(
				(info) => info.pageName === 'normal-office-hours')?.payloadInfo as unknown as OfficeHoursInfo;
			const appointmentMethod = officeHoursInfo?.appointmentMethod || null;
			const teleHealthFlag = [TELEHEALTH, IN_PERSON_TELEHEALTH].includes(appointmentMethod ?? '');
			const inPersonFlag = [IN_PERSON, IN_PERSON_TELEHEALTH].includes(appointmentMethod ?? '');
			

			response.push({
				id: therapist.id,
				firstName: therapist.firstname,
				lastName: therapist.lastname,
				email: therapist.email,
				profilePicture: therapist.dataValues.profilePicture,
				sessionFee : therapist.dataValues.sessionFee,
				address: therapist.address,
				emailVerifiedAt: therapist.emailVerifiedAt,
				acceptedAt: therapist.acceptedAt,
				appointmentMethod,
				practiceInfo,
				teleHealthFlag,
				inPersonFlag,
				availableOfficeHours,
			});
		}

		return res.send(paginatedData({ rows: response, count: therapistData.count }, req));
	})
);


/********************************
 * * Get therapist detail
 ********************************/
router.get(
	'/:id',
	wrap(async (req: Request, res: Response) => {
		const therapistDetails = await getTherapistDetails(Number(req.params.id), Number(req.user?.id))
  	res.send(therapistDetails)
	})
)


router.get("/byRadius/detail", APIMiddleware, async (req: Request, res: Response) => {
	const { lat, lng, radius } = req.query;
	logger.info(`Accessed by User ID: ${req.user?.id}`);

	if (!lat || !lng || !radius) {
		return res.status(400).json({ error: "lat, lng, and radius are required." });
	}

	try {
		const latitude = parseFloat(lat as string);
		const longitude = parseFloat(lng as string);
		const searchRadius = parseFloat(radius as string);
		const therapists = await User.findAll({
			attributes: {
				include: [
					[
						Sequelize.literal(`
				ST_Distance(
				  ST_SetSRID(ST_Point(
					("address"::json->>'lng')::double precision, 
					("address"::json->>'lat')::double precision
				  ), 4326)::geography, 
				  ST_SetSRID(ST_Point(${longitude}, ${latitude}), 4326)::geography
				)`),
						"distance",
					],
				],
			},
			where: Sequelize.literal(`
		  "role" = 'therapist' AND
		  ST_DWithin(
			ST_SetSRID(ST_Point(
			  ("address"::json->>'lng')::double precision, 
			  ("address"::json->>'lat')::double precision
			), 4326)::geography, 
			ST_SetSRID(ST_Point(${longitude}, ${latitude}), 4326)::geography, 
			${searchRadius}
		  )
		`),
			order: Sequelize.literal("distance ASC"),
			include: [
				{
					model: TherapistProfile,
					as: 'profile',
					attributes: {
						include: [
							[
								Sequelize.literal(`(
					SELECT "payloadInfo"->>'user_profile'
					FROM "user_registration_informations"
					WHERE "user_registration_informations"."userId" = "profile"."userId" 
					AND "user_registration_informations"."pageName" = 'profile-info'
					LIMIT 1
				  )`),
								'profile_picture',
							],
						],
					},
				},
			],
		});

		logger.info(`User ${req.user?.id} Found ${therapists.length} therapists for the given radius.`);
		return res.json({ therapists });
	} catch (error) {
		logger.error("Error fetching therapists:", error);
		return res.status(500).json({ error: "Internal Server Error." });
	}
});

router.post('/duration-cost', PortalMiddleware, async (req: Request, res: Response) => {
	try {
		// Extract therapistId from the user object (assuming it's in req.user)
		const therapistId = req.user?.id;

		if (!therapistId) {
			// If therapistId does not exist, return unauthorized
			return res.status(401).json({
				status: 401,
				message: 'Unauthorized: Therapist ID not found',
			});
		}

		// Extract the data array from the request body
		const { data } = req.body;

		// Fetch existing records for this therapist
		const existingRecords = await TherapistDurationAndCost.findAll({
			where: { therapistId },
		});

		// Create a lookup map for existing records, using category as the key
		const existingMap = new Map(
			existingRecords.map((record) => [record.category, record])
		);

		// Process the new data entries
		await Promise.all(
			data.map(async (entry: { category: string; duration: number; cost?: number }) => {
				const existingEntry = existingMap.get(entry.category);

				// Set the amount: if 'cost' is undefined, default to 0.00 for 'waitlist'
				const amountValue: number = entry.category.toLowerCase() === "waitlist" ? 0.00 : (entry.cost ?? 0.00);

				if (existingEntry) {
					// If entry exists, update the existing record
					await existingEntry.update({
						duration: entry.duration,
						amount: amountValue,  // Ensure `amount` is always a number
					});
				} else {
					// If entry does not exist, create a new record
					await TherapistDurationAndCost.create({
						therapistId,
						category: entry.category,
						duration: entry.duration,
						amount: amountValue,  // Ensure `amount` is always a number
					} as any);
				}
			})
		);

		logger.info(`Successfully updated duration & cost for therapist ${therapistId} by User ${req.user?.id}`);
		
		// Return success response after all operations
		return res.status(200).json({
			status: 200,
			message: 'Duration and costs updated successfully',
		});
	} catch (error: any) {
		// Log and return error if something goes wrong
		console.error('Error updating duration and costs:', error);
		return res.status(500).json({
			status: 500,
			message: 'Internal server error',
			error: error.message,
		});
	}
});

router.get('/duration-cost/detail', PortalMiddleware, async (req: Request, res: Response) => {
	try {
		// Extract therapistId from the user object (assuming it's in req.user)
		logger.info(`User ${req.user?.id} requested duration cost details`);
		const therapistId = req.user?.id;

		if (!therapistId) {
			return res.status(401).json({
				status: 401,
				message: 'Unauthorized: Therapist ID not found',
			});
		}

		// Fetch existing records for this therapist
		const existingRecords = await TherapistDurationAndCost.findAll({
			where: { therapistId },
		});

		// Define category name mappings
		const categoryMappings: Record<string, string> = {
			"Intake": "intake",
			"Individual": "individual_patient",
			"couple/family": "couple_family",
			"teen": "teen",
			"child": "child",
		};

		// Define the response structure
		const formattedResponse: Record<string, Record<string, string> | string> = {};

		// Check and store 'waitlist' value from the database if exists
		const waitlistRecord = existingRecords.find(record => record.category === 'waitlist');
		if (waitlistRecord) {
			formattedResponse["waitlist"] = waitlistRecord.duration.toString(); // Set the waitlist duration from the database
		}

		// Process each record and populate the response structure
		existingRecords.forEach(record => {
			// Normalize category name
			const normalizedCategory = categoryMappings[record.category] ||
				record.category.toLowerCase().replace(/\s+/g, "_").replace(/\//g, "_");

			// Initialize the category in the formattedResponse if it doesn't already exist
			if (!formattedResponse[normalizedCategory]) {
				formattedResponse[normalizedCategory] = {};
			}

			// If category already has an object, make sure we add the duration to it
			if (typeof formattedResponse[normalizedCategory] === 'object') {
				// Convert amount to a float before calling toFixed(2)
				const amountValue = parseFloat(record.amount as unknown as string);
				// Assign the amount to the corresponding duration
				(formattedResponse[normalizedCategory] as Record<string, string>)[record.duration.toString()] = amountValue.toFixed(2);
			}
		});

		// Return success response
		return res.status(200).json({
			status: 200,
			data: formattedResponse,
		});
	} catch (error: any) {
		console.error('Error fetching duration and costs:', error);
		return res.status(500).json({
			status: 500,
			message: 'Internal server error',
			error: error.message,
		});
	}
});



/**
 * @route GET /availablity/days
 * @description Retrieves the availability of a therapist by working days.
 * @queryparam {string} therapistId - The ID of the therapist whose availability is being requested.
 * @returns {Object} 200 - An object representing the available dates
 */

router.get(
    '/availablity/days',
	// APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const { therapistId } = req.query;
		// const id = req.user?.id; 
        const patTimeZone = req.query.patientTimeZone || 'America/New_York'; // Default timezone if not provided
        if (!therapistId) {
            return res.status(400).json({ error: 'therapistId is required' });
        }
		if (!patTimeZone) {
            return res.status(400).json({ error: 'patientTimeZone is required' });
        }
 
        try {
            // Get both the therapist's and patient's timezones
            const [therapist] = await Promise.all([
                User.findOne({
                    where: { id: therapistId },
                    attributes: ['timeZone']
                }),
                // User.findOne({
                //     where: { id: id }, // patient id from req.user
                //     attributes: ['timeZone']
                // })
            ]);

            if (!therapist || !therapist.timeZone) {
                return res.status(404).json({ error: 'Therapist timezone not found' });
            }
            
            // if (!patient || !patient.timeZone) {
            //     return res.status(404).json({ error: 'Patient timezone not found' });
            // }
            
            // Get booking time hours from user_registration_informations table
            const registrationInfo = await UserRegistrationInfo.findOne({
                where: {
                    userId: therapistId,
                    pageName: 'waitlist-notifications'
                },
                attributes: ['payloadInfo']
            });
            
            // Default to 24 hours if no booking time preference is set
            let bookingTimeHours = 24;
            
            if (registrationInfo && registrationInfo.payloadInfo) {
                try {
                    const payloadInfo = typeof registrationInfo.payloadInfo === 'string' 
                        ? JSON.parse(registrationInfo.payloadInfo) 
                        : registrationInfo.payloadInfo;
                    
                    if (payloadInfo.booking_time_hour) {
                        bookingTimeHours = parseInt(payloadInfo.booking_time_hour);
                        logger.info(`Therapist ${therapistId} has booking time preference of ${bookingTimeHours} hours`);
                    }
                } catch (e) {
                    logger.error(`Error parsing payloadInfo for therapist ${therapistId}:`, e);
                }
            }

            const therapistTimeZone = therapist.timeZone;
            const patientTimeZone = patTimeZone;
            
            // Current date/time in therapist's timezone
            let therapistDate = dayjs().tz(therapistTimeZone);
            

            // Get all office hours for the therapist
            const officeHours = await NormalOfficeHours.findAll({
                where: {
                    userId: therapistId,
                    isDisabled: false,
                    deletedAt: null,
                },
                attributes: ['workingDay', 'availabilityHours']
            });

            // Get all appointments for the therapist for the next 4 weeks
            const fourWeeksLater = therapistDate.add(4, 'week').toDate();
            const appointments = await Appointments.findAll({
                where: {
                    therapistId,
                    cancelledAt: null,
                    appointmentDate: {
                        [Op.gte]: therapistDate.toDate(),
                        [Op.lt]: fourWeeksLater
                    }
                },
                attributes: ['appointmentDate']
            });
            
            // Log for debugging
            logger.info(`Found ${appointments.length} appointments for therapist ${therapistId}`);
            if (appointments.length > 0) {
                logger.info(`Sample appointment date: ${appointments[0].appointmentDate}`);
            }
 
            // Create an array to hold all dates (both available and unavailable)
            const allDates: string[] = [];
            
            // Create an array to hold only available dates
            const availableDates: string[] = [];
            
            // Create an array of the next 28 days (4 weeks)
            const dates: dayjs.Dayjs[] = [];
            
            for (let i = 0; i < 28; i++) {
                const currentDate = therapistDate.clone().add(i, 'day');
                const dateString = currentDate.format('YYYY-MM-DD');
                
                dates.push(currentDate);
                allDates.push(dateString);
            }
 
            // Group office hours by day
            const officeHoursByDay = new Map<string, string[]>();
            officeHours.forEach((officeHour) => {
                const day = officeHour.workingDay.toLowerCase();
                if (!officeHoursByDay.has(day)) {
                    officeHoursByDay.set(day, []);
                }
                officeHoursByDay.get(day)?.push(officeHour.availabilityHours);
            });

            // Reset therapistDate to ensure consistent calculations
            therapistDate = dayjs().tz(therapistTimeZone);
            
            // Check each date's availability
            for (const date of dates) {
                // Get the day name (e.g., 'monday', 'tuesday')
                const dayName = date.format('dddd').toLowerCase();
                
                // Skip if there are no office hours for this day
                if (!officeHoursByDay.has(dayName)) {
                    continue;
                }
                
                const slots = officeHoursByDay.get(dayName) || [];
                let hasAvailableSlot = false;
                
                // Check each slot for this day
                for (const slot of slots) {
                    // Parse the slot time (e.g., "09:00" -> 9:00)
                    const [hours, minutes] = slot.split(':');
                    
                    // Create a date object in the therapist's timezone for this slot
                    const slotTimeTherapist = date.clone()
                        .hour(parseInt(hours))
                        .minute(parseInt(minutes))
                        .second(0)
                        .millisecond(0);

                    // Check if this slot is in the past in therapist's timezone
                    if (slotTimeTherapist.isBefore(therapistDate)) {
                        continue; // Skip past slots
                    }
                    
                    // Convert the slot time to patient's timezone
                    // First get the UTC time of the slot
                    const slotTimeUTC = slotTimeTherapist.clone().tz('UTC');
                    // Then convert to patient's timezone
					const slotTimePatient = slotTimeUTC.clone().tz(String(patientTimeZone));
                    
                    // Get the date in patient's timezone (for display purposes)
                    const patientDateString = slotTimePatient.format('YYYY-MM-DD');
                    
                    // Check if this slot is within the booking time hours (exclude slots within the booking window)
                    // Use therapist's timezone for booking time calculation
                    const hoursDifference = slotTimeTherapist.diff(therapistDate, 'hour');
                    if (hoursDifference <= bookingTimeHours) {
                        continue; // Skip slots that are within the booking time hours
                    }

                    // Check if this slot is booked (using therapist's timezone for comparison with appointments)
                    const slotEndTimeTherapist = slotTimeTherapist.clone().add(1, 'hour'); // Assuming 1-hour slots
                    
                    const isBooked = appointments.some(appointment => {
                        // Parse the appointment date from UTC format and convert to therapist's timezone
                        const appointmentStart = dayjs(appointment.appointmentDate).tz(therapistTimeZone);
                        // Assume each appointment is 1 hour long
                        const appointmentEnd = appointmentStart.clone().add(1, 'hour');
                        
                        // Check for overlap between slot and appointment (using therapist's timezone)
                        return (
                            // Slot starts during appointment
                            (slotTimeTherapist.isAfter(appointmentStart) && slotTimeTherapist.isBefore(appointmentEnd)) ||
                            // Slot ends during appointment
                            (slotEndTimeTherapist.isAfter(appointmentStart) && slotEndTimeTherapist.isBefore(appointmentEnd)) ||
                            // Appointment is within slot
                            ((appointmentStart.isSame(slotTimeTherapist) || appointmentStart.isAfter(slotTimeTherapist)) && 
                             (appointmentEnd.isSame(slotEndTimeTherapist) || appointmentEnd.isBefore(slotEndTimeTherapist))) ||
                            // Slot is within appointment
                            ((slotTimeTherapist.isSame(appointmentStart) || slotTimeTherapist.isAfter(appointmentStart)) && 
                             (slotEndTimeTherapist.isSame(appointmentEnd) || slotEndTimeTherapist.isBefore(appointmentEnd)))
                        );
                    });

                    if (!isBooked) {
                        hasAvailableSlot = true;
                        break;
                    }
                }

                // If there's an available slot, add the date in patient's timezone to the available dates
                if (hasAvailableSlot) {
                    const slotsForDay = officeHoursByDay.get(dayName) || [];
                    
                    // Check each slot in this day to see when it appears in patient's timezone
                    for (const slot of slotsForDay) {
                        const [hours, minutes] = slot.split(':');
                        
                        // Create slot time in therapist timezone
                        const slotTime = date.clone()
                            .hour(parseInt(hours))
                            .minute(parseInt(minutes))
                            .second(0)
                            .millisecond(0);
                        
                        // Skip if this slot is in the past or within booking time hours
                        if (slotTime.isBefore(therapistDate) || 
                            slotTime.diff(therapistDate, 'hour') <= bookingTimeHours) {
                            continue;
                        }
                        
                        // Check if this specific slot is booked
                        const slotEndTime = slotTime.clone().add(1, 'hour');
                        const isSlotBooked = appointments.some(appointment => {
                            const appointmentStart = dayjs(appointment.appointmentDate).tz(therapistTimeZone);
                            const appointmentEnd = appointmentStart.clone().add(1, 'hour');
                            
                            return (
                                (slotTime.isAfter(appointmentStart) && slotTime.isBefore(appointmentEnd)) ||
                                (slotEndTime.isAfter(appointmentStart) && slotEndTime.isBefore(appointmentEnd)) ||
                                ((appointmentStart.isSame(slotTime) || appointmentStart.isAfter(slotTime)) && 
                                 (appointmentEnd.isSame(slotEndTime) || appointmentEnd.isBefore(slotEndTime))) ||
                                ((slotTime.isSame(appointmentStart) || slotTime.isAfter(appointmentStart)) && 
                                 (slotEndTime.isSame(appointmentEnd) || slotEndTime.isBefore(appointmentEnd)))
                            );
                        });
                        
                        if (!isSlotBooked) {
                            // Convert to patient timezone - this is the key part
                            const slotTimeUTC = slotTime.clone().tz('UTC');
							const slotTimePatient = slotTimeUTC.clone().tz(String(patientTimeZone));
                            
                            // Only add the date in patient's timezone
                            const patientDateString = slotTimePatient.format('YYYY-MM-DD');
                            
                            if (!availableDates.includes(patientDateString)) {
                                availableDates.push(patientDateString);
                            }
                        }
                    }
                }
            }
 
            // Sort the available dates chronologically
            availableDates.sort();
            
            return res.json(availableDates);
        } catch (error) {
            logger.error('Error checking therapist availability:', error);
            return res.status(500).json({ error: 'Internal server error' });
        }
    })
);
// Helper function to get day number (0-6, where 0 is Sunday)


// Helper function to get the next occurrence of a specific day of the week
// function getNextDayOfWeek(day: string): number {
//     const dayOfWeek = getDayNumber(day);
//     const today = dayjs().day();
    
//     // If today is the same day, return today
//     if (today === dayOfWeek) {
//         return today;
//     }
    
//     // Calculate days until next occurrence
//     let daysUntilNext = (dayOfWeek + 7 - today) % 7;
//     if (daysUntilNext === 0) daysUntilNext = 7; // If it's the same day next week
    
//     return today + daysUntilNext;
// }


export default router
