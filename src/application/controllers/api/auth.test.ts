import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import * as MailModule from '@/src/configs/sendgrid.config';
import authController from '@/src/application/controllers/api/auth.controller';
import { User as UserModel } from '@/src/models';
import sequelize from '@/src/configs/database.config';
import * as RegisterEmail from '../../emails/register.email';
import * as PasswordResetEmailModule from '../../emails/password.email';
import * as Helper from '@/src/application/helpers/encryption.helper';
import * as CryptoUtil from '@/src/cryptoUtil';
import dayjs from 'dayjs';
import logger from '@/src/configs/logger.config';

const app = express();
app.use(express.json());
app.use('/api/auth', authController);

describe('Auth Controller', function () {
    this.timeout(5000);

    let sandbox: sinon.SinonSandbox;
    let userFindOneStub: sinon.SinonStub;
    let userUpdateStub: sinon.SinonStub;
    let userCreateStub: sinon.SinonStub;
    let sequelizeStub: sinon.SinonStub;
    let encryptedB64Stub: sinon.SinonStub;
    let decryptedB64Stub: sinon.SinonStub;
    let hashDataStub: sinon.SinonStub;
    let registerPatientStub: sinon.SinonStub;
    let passwordResetEmailCompileStub: sinon.SinonStub;
    let sendMailStub: sinon.SinonStub;
    let loggerInfoStub: sinon.SinonStub;
    let loggerErrorStub: sinon.SinonStub;

    const TEST_USER_ID = '213';
    const TEST_TOKEN = 'valid-token';
    const TEST_PASSWORD = 'Password123!';
    const TEST_EMAIL = '<EMAIL>';
    const TEST_HASHED_EMAIL = 'hashed_email_value';
    const FUTURE_TIME = dayjs().add(1, 'hour').unix();
    const PAST_TIME = dayjs().subtract(1, 'hour').unix();
    const FRONTEND_URL = 'http://example.com';

    beforeEach(function() {
        sandbox = sinon.createSandbox();
        
        userFindOneStub = sandbox.stub(UserModel, 'findOne');
        userUpdateStub = sandbox.stub(UserModel.prototype, 'update');
        decryptedB64Stub = sandbox.stub(Helper, 'decryptedB64');
        encryptedB64Stub = sandbox.stub(Helper, 'encryptedB64');
        hashDataStub = sandbox.stub(CryptoUtil, 'hashData').returns(TEST_HASHED_EMAIL);
        
        loggerInfoStub = sandbox.stub(logger, 'info');
        loggerErrorStub = sandbox.stub(logger, 'error');
        
        process.env.FRONTEND_URL = FRONTEND_URL;
    });

    afterEach(function() {
        sandbox.restore();
        delete process.env.FRONTEND_URL;
    });

    describe('POST /register-patient', function() {
        beforeEach(function() {
            sequelizeStub = sandbox.stub(sequelize, 'transaction').resolves({ 
                commit: async () => Promise.resolve(),
                rollback: async () => Promise.resolve()
            } as any);
            
            userCreateStub = sandbox.stub(UserModel, 'create').resolves({
                id: TEST_USER_ID,
                update: userUpdateStub,
                toJSON: () => ({
                    id: TEST_USER_ID,
                    firstname: 'Janee',
                    lastname: 'Doe',
                    email: '<EMAIL>'
                }),
                generateToken: () => 'mocked-access-token'
            } as any);
            
            registerPatientStub = sandbox.stub(RegisterEmail.RegisterPatientEmail, 'compile').resolves({
                To: 'to',
                Subject: 'subject',
                HtmlBody: 'htmlbody'
            });
            
            sendMailStub = sandbox.stub(MailModule.mail, 'sendMail').resolves();
            sandbox.stub(Math, 'random').returns(0.123456);
            sandbox.stub(dayjs.prototype, 'diff').returns(20);
            sandbox.stub(dayjs.prototype, 'add').returns({
                unix: () => **********
            } as any);
        });

        it('should register a patient and return a success message', async function() {
            userFindOneStub.resolves(null);

            const reqBody = {
                firstname: 'Janee',
                lastname: 'Doe',
                dob: '2005-01-01',
                email: '<EMAIL>',
                password: TEST_PASSWORD,
                confirmPassword: TEST_PASSWORD,
                is_minor: true,
                address: {
                    lat: 34.0522,
                    lng: -118.2437
                }
            };

            const res = await request(app)
                .post('/api/auth/register-patient')
                .send(reqBody)
                .expect(HttpStatusCode.Ok);
        });
    });

    describe('POST /reset-password', function() {

        it('should return 400 when token is expired', async function() {
            decryptedB64Stub.returns(`${TEST_USER_ID}:${PAST_TIME}`);

            const response = await request(app)
                .post('/api/auth/reset-password')
                .send({
                    token: TEST_TOKEN,
                    password: TEST_PASSWORD,
                    confirmPassword: TEST_PASSWORD
                })
                .expect(HttpStatusCode.BadRequest);

            expect(response.body).to.have.property('message', 'Token expired');
        });

        it('should return 404 when user is not found with token', async function() {
            decryptedB64Stub.returns(`${TEST_USER_ID}:${FUTURE_TIME}`);
            userFindOneStub.resolves(null);

            const response = await request(app)
                .post('/api/auth/reset-password')
                .send({
                    token: TEST_TOKEN,
                    password: TEST_PASSWORD,
                    confirmPassword: TEST_PASSWORD
                })
                .expect(HttpStatusCode.NotFound);

            expect(response.body).to.have.property('message', 'This link has already been used, please generate new link');
        });

        it('should return 400 when passwords do not match', async function() {
            decryptedB64Stub.returns(`${TEST_USER_ID}:${FUTURE_TIME}`);
            
            const mockUser = {
                id: TEST_USER_ID,
                role: 'patient',
                update: userUpdateStub,
                passwordResetToken: TEST_TOKEN
            };
            
            userFindOneStub.resolves(mockUser);

            const response = await request(app)
                .post('/api/auth/reset-password')
                .send({
                    token: TEST_TOKEN,
                    password: TEST_PASSWORD,
                    confirmPassword: 'differentPassword123!'
                })
                .expect(HttpStatusCode.BadRequest);

            expect(response.body).to.have.property('message', 'Password does not match');
        });
    });

    describe('POST /forgot-password', function() {
        beforeEach(function() {
            passwordResetEmailCompileStub = sandbox.stub(PasswordResetEmailModule.PasswordResetEmail, 'compile').returns({
                From: '<EMAIL>',
                To: TEST_EMAIL,
                Subject: 'Password Reset Request',
                HtmlBody: '<p>Click <a href="http://reset-link">here</a> to reset your password.</p>'
            });
            
            sendMailStub = sandbox.stub(MailModule.mail, 'sendMail').resolves();
            
            encryptedB64Stub.returns(TEST_TOKEN);
        });
 
        it('should return 400 when token is expired', async function() {
            decryptedB64Stub.returns(`${TEST_USER_ID}:${PAST_TIME}`);
 
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({
                    token: TEST_TOKEN,
                    password: TEST_PASSWORD,
                    confirmPassword: TEST_PASSWORD
                })
                .expect(HttpStatusCode.BadRequest);
        });
 
        it('should return 201 and success message when email is valid', async function() {
            userFindOneStub.resolves({
                id: TEST_USER_ID,
                update: userUpdateStub,
                toJSON: () => ({
                    id: TEST_USER_ID,
                    email: TEST_EMAIL
                })
            });
 
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({ email: TEST_EMAIL })
                .expect(HttpStatusCode.Created);
 
            expect(response.body).to.have.property('message', 'Magic link sent to your email.');
            expect(hashDataStub.calledOnceWith(TEST_EMAIL)).to.be.true;
            expect(userFindOneStub.calledOnceWith({
                where: { email_hash: TEST_HASHED_EMAIL }
            })).to.be.true;
            expect(encryptedB64Stub.calledOnce).to.be.true;
            expect(encryptedB64Stub.firstCall.args[0]).to.include(TEST_USER_ID);
            expect(userUpdateStub.calledOnceWith({
                passwordResetToken: TEST_TOKEN
            })).to.be.true;
            expect(passwordResetEmailCompileStub.calledOnce).to.be.true;
            expect(passwordResetEmailCompileStub.firstCall.args[0]).to.deep.include({
                email: TEST_EMAIL,
                url: `${FRONTEND_URL}/auth/reset-password?token=${TEST_TOKEN}`
            });
            expect(sendMailStub.calledOnce).to.be.true;
            expect(loggerInfoStub.called).to.be.true;
        });
 
        it('should return 404 when user is not found', async function() {
            userFindOneStub.resolves(null);
 
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({ email: TEST_EMAIL })
                .expect(HttpStatusCode.NotFound);
 
            expect(response.body).to.have.property('message', 'User not found');
            expect(hashDataStub.calledOnceWith(TEST_EMAIL)).to.be.true;
            expect(userFindOneStub.calledOnceWith({
                where: { email_hash: TEST_HASHED_EMAIL }
            })).to.be.true;
            expect(encryptedB64Stub.called).to.be.false;
            expect(userUpdateStub.called).to.be.false;
            expect(passwordResetEmailCompileStub.called).to.be.false;
            expect(sendMailStub.called).to.be.false;
        });
 
        it('should handle email sending failure', async function() {
            userFindOneStub.resolves({
                id: TEST_USER_ID,
                update: userUpdateStub,
                toJSON: () => ({
                    id: TEST_USER_ID,
                    email: TEST_EMAIL
                })
            });
            
            const emailError = new Error('Email sending failed');
            sendMailStub.rejects(emailError);
 
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({ email: TEST_EMAIL })
                .expect(HttpStatusCode.InternalServerError);
 
            expect(response.body).to.have.property('message', 'Failed to send email');
            expect(hashDataStub.calledOnceWith(TEST_EMAIL)).to.be.true;
            expect(userFindOneStub.calledOnce).to.be.true;
            expect(encryptedB64Stub.calledOnce).to.be.true;
            expect(userUpdateStub.calledOnce).to.be.true;
            expect(passwordResetEmailCompileStub.calledOnce).to.be.true;
            expect(sendMailStub.calledOnce).to.be.true;
            // expect(loggerErrorStub.calledOnce).to.be.false;
            expect(loggerErrorStub.firstCall.args[0]).to.include('Failed to send password reset email');
        });
 
        it('should handle missing email field in request', async function() {
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({})
                .expect(HttpStatusCode.BadRequest);
 
            expect(response.body).to.have.property('message');
            expect(userFindOneStub.called).to.be.false;
        });
 
        it('should handle invalid email format', async function() {
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({ email: 'invalid-email' })
                .expect(HttpStatusCode.BadRequest);
 
            expect(response.body).to.have.property('message');
            expect(userFindOneStub.called).to.be.false;
        });
 
        it('should handle database errors when updating user', async function() {
            userFindOneStub.resolves({
                id: TEST_USER_ID,
                update: sandbox.stub().rejects(new Error('Database error')),
                toJSON: () => ({
                    id: TEST_USER_ID,
                    email: TEST_EMAIL
                })
            });
 
            const response = await request(app)
                .post('/api/auth/forgot-password')
                .send({ email: TEST_EMAIL })
                .expect(HttpStatusCode.InternalServerError);
 
            expect(hashDataStub.calledOnceWith(TEST_EMAIL)).to.be.true;
            expect(userFindOneStub.calledOnce).to.be.true;
            expect(encryptedB64Stub.calledOnce).to.be.true;
            expect(passwordResetEmailCompileStub.called).to.be.false;
            expect(sendMailStub.called).to.be.false;
        });
    });
 

    
});