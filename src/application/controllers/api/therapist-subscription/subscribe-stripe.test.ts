import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { StripeInfo, SubscriptionPlan, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistSubscriptionRoutes from '@/src/application/controllers/api/therapist-subscription';
import stripe from '@/src/configs/stripe-new.config';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistSubscriptionRoutes);

describe('Stripe Subscription API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindOneStub: sinon.SinonStub;
	let subPlanFindOneStub: sinon.SinonStub;
	let stripeInfoFindOneStub: sinon.SinonStub;

	let stripeCustomersRetrieveStub: sinon.SinonStub;
	let stripeCheckoutSessionsCreateStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyStripeInfo: any;
	let dummySubscriptionPlan: any;
	let dummyStripeCustomer: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindOneStub = sandbox.stub(User, 'findOne');
		subPlanFindOneStub = sandbox.stub(SubscriptionPlan, 'findOne');
		stripeInfoFindOneStub = sandbox.stub(StripeInfo, 'findOne');

		stripeCustomersRetrieveStub = sandbox.stub(stripe.customers, 'retrieve');
		stripeCheckoutSessionsCreateStub = sandbox.stub(stripe.checkout.sessions, 'create');

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}

		dummySubscriptionPlan = {
			id: 1,
			annualPrice: 100,
			monthlyPrice: 10,
			isActive: true,
			createdAt: new Date(),
		}

		dummyStripeInfo = {
			id: 1,
			therapistId: 1,
			stripeCustomerId: 'cus_123456',
		}

		dummyStripeCustomer = {
			id: 'cus_123456',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/stripe/subscribe', function() {
		it('should fail if user is not found', async function () {
			userFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should fail if redirectPath is not provided', async function () {
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Please provide redirectPath');
		});

		it('should fail if subscriptionType is not provided', async function () {
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Please provide subscriptionType and subscriptionPlanId');
		});

		it('should fail if subscriptionPlanId is not provided', async function () {
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
					subscriptionType: 'monthly',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Please provide subscriptionType and subscriptionPlanId');
		});

		it('should fail if subscription plan is not found', async function () {
			userFindOneStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
					subscriptionType: 'monthly',
					subscriptionPlanId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Subscription plan does not exist');
		});

		it('should fail if price is not found', async function () {
			userFindOneStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
					subscriptionType: 'error',
					subscriptionPlanId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Price could not be found. Please try again');
		});

		it('should fail if error occurs during stripe customer retrieval', async function () {
			userFindOneStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			stripeInfoFindOneStub.resolves(dummyStripeInfo);
			stripeCustomersRetrieveStub.rejects(new Error('Error'));

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
					subscriptionType: 'monthly',
					subscriptionPlanId: '123',
				});
			
			expect(stripeCustomersRetrieveStub.calledOnce).to.be.true;
			expect(stripeCheckoutSessionsCreateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Error retrieving Stripe customer. Please try again.');
		});

		it('should create stripe checkout session successfully with monthly plan', async function () {
			userFindOneStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			stripeInfoFindOneStub.resolves(dummyStripeInfo);
			stripeCustomersRetrieveStub.resolves({
				...dummyStripeCustomer,
				deleted: true,
			})
			stripeCheckoutSessionsCreateStub.resolves({
				id: '123',
				url: 'https://stripe.com',
			})

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
					subscriptionType: 'monthly',
					subscriptionPlanId: '123',
				});
			
			expect(stripeCustomersRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(stripeCheckoutSessionsCreateStub.calledWithMatch(sinon.match.has('customer_email', dummyUser.email))).to.be.true;
			expect(response.body).to.have.property('stripeSessionUrl', 'https://stripe.com');
		});

		it('should create stripe checkout session successfully with yearly plan', async function () {
			userFindOneStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			stripeInfoFindOneStub.resolves(dummyStripeInfo);
			stripeCustomersRetrieveStub.resolves(dummyStripeCustomer)
			stripeCheckoutSessionsCreateStub.resolves({
				id: '123',
				url: 'https://stripe.com',
			})

			const response = await request(app)
				.post('/api/stripe/subscribe')
				.send({
					userToken: '123',
					redirectPath: '/test',
					subscriptionType: 'yearly',
					subscriptionPlanId: '123',
				});
			
			expect(stripeCustomersRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(stripeCheckoutSessionsCreateStub.calledWithMatch(sinon.match.has('customer', dummyStripeCustomer.id))).to.be.true;
			expect(response.body).to.have.property('stripeSessionUrl', 'https://stripe.com');
		});
	});

});