import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { ForbiddenError, NotFoundError } from '@/src/application/handlers/errors'
import stripe from '@/src/configs/stripe-new.config'
import { HttpStatusCode } from 'axios'
import { StripeInfo, SubscriptionPlan, User } from '@/src/models'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Redirect to Checkout Page for Subscription
 ********************************/
router.post(
	'/stripe/subscribe',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { subscriptionType, subscriptionPlanId, redirectPath, userToken } = req.body
		const user = await User.findOne({
			where: {
				userToken,
			},
		})

		if (!user) throw new ForbiddenError('User not found');

		logger.info(`Subscription request received by ${user.id}`);
		if (!redirectPath) throw new ForbiddenError('Invalid request. Please provide redirectPath')
		if (!subscriptionType || !subscriptionPlanId) throw new ForbiddenError('Invalid request. Please provide subscriptionType and subscriptionPlanId')

		const plan = await SubscriptionPlan.findOne({
			where: {
				id: subscriptionPlanId,
			},
		})

		if (!plan) throw new NotFoundError('Subscription plan does not exist')

		let price;
		if (subscriptionType === 'monthly') {
			price = plan.monthlyPrice;
		} else if (subscriptionType === 'yearly') {
			price = plan.annualPrice;
		}
		if (!price) throw new ForbiddenError('Price could not be found. Please try again');

		const stripeInfo = await StripeInfo.findOne({
			where: {
				therapistId: user.id,
			}
		});

		let stripeCustomer;
		if (stripeInfo && stripeInfo.stripeCustomerId) {
			try {
				const existingStripeCustomer = await stripe.customers.retrieve(stripeInfo.stripeCustomerId);
				if (!existingStripeCustomer.deleted) stripeCustomer = existingStripeCustomer;
			} catch (error: any) {
				logger.error(`Error occurred while retrieving Stripe customer: ${error}`);
				return res.forbidden('Error retrieving Stripe customer. Please try again.');
			}
		}

		const session = await stripe.checkout.sessions.create({
			mode: 'subscription',
			payment_method_types: ['card'],
			success_url: `${process.env.FRONTEND_URL}${redirectPath}?session_id={CHECKOUT_SESSION_ID}&success=true`,
			cancel_url: `${process.env.FRONTEND_URL}${redirectPath}`,
			subscription_data: {
				metadata: {
					email: user.email,
					userId: user.id,
					subscriptionPlanId,
					subscriptionType,
				},
			},
			...(stripeCustomer ? { customer: stripeCustomer.id } : { customer_email: user.email }),
			billing_address_collection: 'auto',
			line_items: [
				{
					price_data: {
						currency: 'usd',
						product_data: {
							name: `Next Therapist - ${subscriptionType === 'monthly' ? 'Monthly' : subscriptionType === 'yearly' ? 'Yearly' : ''}`,
							description: `${subscriptionType === 'monthly' ? 'Monthly' : subscriptionType === 'yearly' ? 'Yearly' : ''} Next Therapist Subscription`,
						},
						unit_amount: Math.round(price * 100), // Convert $ to cents
						recurring: {
							interval: subscriptionType === 'monthly' ? 'month' : 'year',
						},
					},
					quantity: 1,
				},
			],
		});
		logger.info(`Stripe checkout session created - Session ID: ${session.id}`);

		return res.status(HttpStatusCode.Ok).send({ stripeSessionUrl: session.url })
	})
)

export default router
