import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { HttpStatusCode } from 'axios'
import { StripeInfo, SubscriptionPlan, TherapistSubscription, User } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import stripe from '@/src/configs/stripe-new.config'
import <PERSON><PERSON> from 'stripe'
import sequelize from '@/src/configs/database.config'
import logger from '@/src/configs/logger.config'
import { NewSubscriptionEmail } from '@/src/application/emails/subscription.email'
import { capitalizeFirstLetter, getTimezoneAbbr, maskEmail } from '@/src/application/helpers/general.helper'
import dayjs from 'dayjs'
import timezone from "dayjs/plugin/timezone";
import { mail } from '@/src/configs/sendgrid.config'

dayjs.extend(timezone);

const router = express.Router()

/**
 * Store therapist subscription and stripe info in db
 */
router.post(
	'/therapist-subscription',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { sessionId, userToken } = req.body
		logger.info(`Subscription creation request received`);
		const user = await User.findOne({
			where: {
				userToken,
			},
		})
		if (!user) throw new ForbiddenError('User not found');
		logger.info(`Subscription creation request received. User ID: ${user.id}`);
		if (!sessionId) throw new ForbiddenError('Invalid request. Session Id is missing.')
		logger.info(`Retrieving Stripe session for User ID: ${user.id}, Session ID: ${sessionId}`);
		const session = await stripe.checkout.sessions.retrieve(sessionId, { expand: ['subscription'] })
		if (!session) throw new ForbiddenError('Invalid request. Session not found.')
		
		const subscription = session.subscription as Stripe.Subscription
		if (!subscription) throw new ForbiddenError('Invalid Request. Subscription is not found in session.')
		if (subscription.status !== 'active') throw new ForbiddenError('Subscription is not active. Please try again.')

		if (Number(subscription.metadata.userId) !== Number(user.id)) throw new ForbiddenError('Invalid Request. User Id does not match.')
		
		logger.info(`Valid subscription found for User ID: ${user.id}, Subscription ID: ${subscription.id}`);

		// Check if subscription plan is active
		const subscriptionPlan = await SubscriptionPlan.findOne({
			where: {
				id: subscription.metadata.subscriptionPlanId,
			}
		})
		if (!subscriptionPlan?.isActive) throw new ForbiddenError('Subscription plan is not active. Please try again.')

		const transaction = await sequelize.transaction()

		try {
			logger.info(`Processing subscription transaction for User ID: ${user.id}`);

			// Create or update in therapist subscription table
			let therapistSubscription;
			therapistSubscription = await TherapistSubscription.findOne({
				where: {
					therapistId: subscription.metadata.userId,
					subscriptionPlanId: subscription.metadata.subscriptionPlanId,
					isActive: true,
				},
				transaction
			})

			if (therapistSubscription) {
				await therapistSubscription.update({
					subscriptionType: subscription.metadata.subscriptionType,
					subscribedAt: new Date(subscription.current_period_start * 1000),
				}, {
					transaction
				})
				logger.info(`Updated existing subscription for User ID: ${user.id}, Subscription ID: ${therapistSubscription.id}`);
			} else {
				therapistSubscription = await TherapistSubscription.create({
					subscriptionType: subscription.metadata.subscriptionType,
					subscriptionPlanId: subscription.metadata.subscriptionPlanId,
					therapistId: subscription.metadata.userId,
					isActive: true,
					subscribedAt: new Date(subscription.current_period_start * 1000),
				}, {
					transaction
				})
				logger.info(`Created new subscription for User ID: ${user.id}, Subscription ID: ${therapistSubscription.id}`);
			}

			// Create or update in stripe info table
			let stripeInfo = await StripeInfo.findOne({
				where: {
					therapistId: subscription.metadata.userId,
				},
				transaction
			})

			if (stripeInfo) {
				await stripeInfo.update({
					therapistSubscriptionId: therapistSubscription.id,
					stripeSubscriptionId: subscription.id,
					stripeCustomerId: subscription.customer,
					stripeCurrentPeriodStart: new Date(subscription.current_period_start * 1000),
					stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
					stripeCancelAtPeriodEnd: subscription.cancel_at_period_end,
					status: subscription.status,
					billedPrice: subscription.items.data[0].price.unit_amount,
					currency: subscription.items.data[0].price.currency,
					canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
				}, {
					transaction
				})
				logger.info(`Updated Stripe info for User ID: ${user.id}`);
			} else {
				stripeInfo = await StripeInfo.create({
					therapistId: subscription.metadata.userId,
					therapistSubscriptionId: therapistSubscription.id,
					stripeSubscriptionId: subscription.id,
					stripeCustomerId: subscription.customer,
					stripeCurrentPeriodStart: new Date(subscription.created * 1000),
					stripeCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
					stripeCancelAtPeriodEnd: subscription.cancel_at_period_end,
					status: subscription.status,
					billedPrice: subscription.items.data[0].price.unit_amount,
					currency: subscription.items.data[0].price.currency,
					canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
				}, {
					transaction
				})
				logger.info(`Created new Stripe info for User ID: ${user.id}`)
			}

			const plan = await TherapistSubscription.findOne({
				where: {
					therapistId: user.id,
					subscriptionPlanId: subscription.metadata.subscriptionPlanId,
					isActive: true,
				},
				include: [
					{
						model: SubscriptionPlan,
						as: 'subscriptionPlan',
					},
					{
						model: StripeInfo,
						as: 'stripeInfo',
					}
				],
				transaction
			})

			await transaction.commit();
			logger.info(`Subscription successfully created for User ID: ${user.id}`);

			try {
				logger.info(`Creating subscription confirmation email data for User ID: ${user.id}`);
				const nextRenewalDate = user.timeZone
					? dayjs(stripeInfo.stripeCurrentPeriodEnd).tz(user.timeZone).format('MMMM D, YYYY h:mm A')
						: dayjs(stripeInfo.stripeCurrentPeriodEnd).format('MMMM D, YYYY h:mm A');
				
				const timeZoneAbbreviation = user.timeZone
					? getTimezoneAbbr(user.timeZone)
						: getTimezoneAbbr(dayjs.tz.guess());

				const emailData = NewSubscriptionEmail.compile({
					email: user.email,
					therapistName: capitalizeFirstLetter(`${user?.firstname} ${user?.lastname}`),
					nextRenewalDate: `${nextRenewalDate} (${timeZoneAbbreviation})`,
					amount: `$${stripeInfo.billedPrice / 100}`,
					subscriptionPlan: capitalizeFirstLetter(therapistSubscription.subscriptionType),
				})
				logger.info(`Sending subscription confirmation email for User ID: ${user.id} - email: ${maskEmail(user.email)}`);
				await mail.sendMail({
					to: emailData.To,
					subject: emailData.Subject,
					html: emailData.HtmlBody,
				});
				logger.info(`Subscription confirmation email sent to User ID: ${user.id} - email: ${maskEmail(user.email)}`);
			} catch (error) {
				logger.error(`Failed to send subscription confirmation email for User ID: ${user.id} - email: ${maskEmail(user.email)}, Error: ${error}`);
			}

			return res.status(HttpStatusCode.Created).send({ message: 'Your subscription has been created successfully', data: plan })
		} catch (error) {
			await transaction.rollback();
			logger.error(`Subscription creation failed for User ID: ${user.id}, Error: ${error}`)
			return res.forbidden('Subscription creation failed. Please try again.');
		}
	})
)

export default router
