import express from 'express';
import getTherapistSubscription from './therapist-subscription.get';
import createTherapistSubscription from './therapist-subscription.post';
import updateTherapistSubscription from './therapist-subscription.put';
import subscribeStripe from './subscribe-stripe';
import billingStripe from './billing-stripe';
import stripeWebhook from './stripe-webhook';

const app = express();

app.use(getTherapistSubscription);
app.use(createTherapistSubscription);
app.use(updateTherapistSubscription);
app.use(stripeWebhook);
app.use(subscribeStripe);
app.use(billingStripe);

export default app;
