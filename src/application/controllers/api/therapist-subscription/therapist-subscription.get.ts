import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { StripeInfo, SubscriptionPlan, TherapistSubscription } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Get therapist subscription plan
 ********************************/
router.get(
	'/therapist-subscription/:therapistId?',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {

		const { therapistId } = req.params;
    	const { user } = req;
		if (!therapistId && !user?.id) {
			throw new ForbiddenError('Invalid request.');
		}

		const userId = therapistId || user?.id;
		
		logger.info(`Fetching active subscription for User ID: ${userId}`);
		const plan = await TherapistSubscription.findOne({
			where: {
				therapistId: userId,
				isActive: true,
			},
			include: [
				{
					model: SubscriptionPlan,
					as: 'subscriptionPlan',
				},
				{
					model: StripeInfo,
					as: 'stripeInfo',
					where: {
						status: 'active',
					},
					required: true,
				}
			],
		})
		if (!plan) return res.forbidden('Subscription not found');
		logger.info(`Subscription found for User ID: ${userId}, Subscription ID: ${plan?.id}`);

		return res.status(200).send({ plan })
	})
)

export default router
