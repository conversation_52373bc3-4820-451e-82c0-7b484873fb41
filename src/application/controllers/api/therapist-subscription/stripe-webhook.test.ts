import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { StripeInfo, TherapistSubscription } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import therapistSubscriptionRoutes from '@/src/application/controllers/api/therapist-subscription';
import stripe from '@/src/configs/stripe-new.config';
import sequelize from '@/src/configs/database.config';
import * as thWaitlistRepo from '@/src/application/repositories/therapist-waitlist.repository';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistSubscriptionRoutes);

describe('Stripe Webhook API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let therapistSubFindOneStub: sinon.SinonStub;
	let therapistSubCreateStub: sinon.SinonStub;
	let stripeInfoFindOneStub: sinon.SinonStub;
	let stripeInfoCreateStub: sinon.SinonStub;

	let therapistSubSingleUpdateStub: sinon.SinonStub;
	let stripeInfoSingleUpdateStub: sinon.SinonStub;

	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	let removeFromWaitlistStub: sinon.SinonStub;

	let stripeSubscriptionsRetrieveStub: sinon.SinonStub;
	let stripeWebhookConstructEventStub: sinon.SinonStub;

	let dummyStripeInfo: any;
	let dummyTherapistSubscription: any;
	let dummyStripeSubscription: any;
	let dummyWebhookEvent: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		therapistSubFindOneStub = sandbox.stub(TherapistSubscription, 'findOne');
		therapistSubCreateStub = sandbox.stub(TherapistSubscription, 'create');
		stripeInfoFindOneStub = sandbox.stub(StripeInfo, 'findOne');
		stripeInfoCreateStub = sandbox.stub(StripeInfo, 'create');

		therapistSubSingleUpdateStub = sandbox.stub().resolves();
		stripeInfoSingleUpdateStub = sandbox.stub().resolves();

		commitStub = sandbox.stub().resolves();
		rollbackStub = sandbox.stub().resolves();

		removeFromWaitlistStub = sandbox.stub(thWaitlistRepo, 'removePatientsFromTherapistWaitlist').resolves(2);

		sandbox.stub(sequelize, 'transaction').resolves({ 
			commit: commitStub.resolves(),
			rollback: rollbackStub.resolves(),
		} as any);

		stripeSubscriptionsRetrieveStub = sandbox.stub(stripe.subscriptions, 'retrieve');
		stripeWebhookConstructEventStub = sandbox.stub(stripe.webhooks, 'constructEvent');

		dummyStripeInfo = {
			id: 1,
			therapistId: 1,
			stripeCustomerId: 'cus_123456',
			stripeCurrentPeriodEnd: new Date(),
			stripeSubscriptionId: 'sub_123456',
			billedPrice: 1000,
			stripeSubscriptionScheduleId: null,
			nextScheduledSubscriptionType: 'null',
		}

		dummyTherapistSubscription = {
			id: 1,
			subscriptionPlanId: 1,
			therapistId: 2,
			subscriptionType: 'monthly',
			createdAt: new Date(),
			stripeInfo: dummyStripeInfo,
		}

		dummyStripeSubscription = {
			id: 'sub_123456',
			status: 'active',
			current_period_start: 1324654,
			current_period_end: 1324654,
			customer: 'cus_123456',
			cancel_at_period_end: false,
			canceled_at: null,
			default_payment_method: 'pm_123456',
			metadata: {
				userId: '1',
				subscriptionPlanId: '1',
				subscriptionType: 'monthly',
			},
			items: {
				data: [
					{
						price: {
							unit_amount: 1000,
							currency: 'usd',
						},
					}
				]
			}
		}

		dummyWebhookEvent = {
			type: 'checkout.session.completed',
			data: {
				object: {
					id: 'sub_123456',
					status: 'active',
					current_period_start: 1324654,
					current_period_end: 1324654,
					customer: 'cus_123456',
					cancel_at_period_end: false,
					canceled_at: null,
					default_payment_method: 'pm_123456',
				},
				previous_attributes: {
					status: 'complete',
					cancel_at: null,
				},
			},
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/stripe/webhook', function() {
		it('should fail errors occurs during construct event', async function () {
			stripeWebhookConstructEventStub.throws(new Error('Construct Event Error'));

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(response.status).to.equal(400);
			expect(response.text).to.equal('Webhook error: Construct Event Error');
		});

		it('case: checkout.session.completed - should complete successfully', async function () {
			stripeWebhookConstructEventStub.returns(dummyWebhookEvent);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: checkout.session.completed');
		});

		it('case: customer.subscription.created - should fail if any error occurs during db operations', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.created',
			});
			stripeSubscriptionsRetrieveStub.resolves({
				...dummyStripeSubscription,
				metadata: {
					...dummyStripeSubscription.metadata,
					planUpdatedByUserAt: new Date().toISOString(),
				},
			});
			therapistSubCreateStub.rejects(new Error('Error TherapistSubscription'));

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubCreateStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.notCalled).to.be.true;
			expect(rollbackStub.calledOnce).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Error TherapistSubscription');
		});

		it('case: customer.subscription.created - should complete successfully and create new stripe info', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.created',
			});
			stripeSubscriptionsRetrieveStub.resolves({
				...dummyStripeSubscription,
				metadata: {
					...dummyStripeSubscription.metadata,
					planUpdatedByUserAt: new Date().toISOString(),
				},
			});
			therapistSubCreateStub.resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubCreateStub.calledOnce).to.be.true;
			expect(stripeInfoCreateStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: customer.subscription.created');
		});

		it('case: customer.subscription.created - should complete successfully and update old stripe info', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.created',
			});
			stripeSubscriptionsRetrieveStub.resolves({
				...dummyStripeSubscription,
				metadata: {
					...dummyStripeSubscription.metadata,
					planUpdatedByUserAt: new Date().toISOString(),
				},
			});
			therapistSubCreateStub.resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves({
				...dummyStripeInfo,
				update: stripeInfoSingleUpdateStub,
			});

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubCreateStub.calledOnce).to.be.true;
			expect(stripeInfoCreateStub.notCalled).to.be.true;
			expect(stripeInfoSingleUpdateStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: customer.subscription.created');
		});

		it('case: customer.subscription.updated - should fail if therapist subscription is not found', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.updated',
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.notCalled).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Therapist subscription not found');
		});

		it('case: customer.subscription.updated - should fail if stripe info is not found', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.updated',
				data: {
					...dummyWebhookEvent.data,
					previous_attributes: {
						status: 'complete',
						cancel_at: 12314654,
					},
				}
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoSingleUpdateStub.notCalled).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Stripe info not found');
		});

		it('case: customer.subscription.updated - should complete successfully and update old stripe info', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.updated',
				data: {
					...dummyWebhookEvent.data,
					previous_attributes: {
						status: 'complete',
						cancel_at: 12314654,
						current_period_end: 12314654,
					},
				}
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves({
				...dummyStripeInfo,
				update: stripeInfoSingleUpdateStub,
			});

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoSingleUpdateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: customer.subscription.updated');
		});

		it('case: customer.subscription.deleted - should fail if therapist subscription is not found', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.deleted',
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(therapistSubSingleUpdateStub.notCalled).to.be.true;
			expect(stripeInfoFindOneStub.notCalled).to.be.true;
			expect(rollbackStub.calledOnce).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Therapist subscription not found');
		});

		it('case: customer.subscription.deleted - should fail if any error occurs during db operations', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.deleted',
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				update: therapistSubSingleUpdateStub,
			});
			therapistSubSingleUpdateStub.rejects(new Error('Error TherapistSubscription'));

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(therapistSubSingleUpdateStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.notCalled).to.be.true;
			expect(rollbackStub.calledOnce).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Error TherapistSubscription');
		});

		it('case: customer.subscription.deleted - should complete successfully and update old stripe info', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.deleted',
			});
			stripeSubscriptionsRetrieveStub.resolves({
				...dummyStripeSubscription,
				status: 'canceled',
				cancel_at_period_end: true,
			});
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				update: therapistSubSingleUpdateStub,
			});
			stripeInfoFindOneStub.resolves({
				...dummyStripeInfo,
				update: stripeInfoSingleUpdateStub,
			});

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoSingleUpdateStub.calledOnce).to.be.true;
			expect(removeFromWaitlistStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: customer.subscription.deleted');
		});

		it('case: customer.subscription.deleted - should complete successfully and create new stripe info', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'customer.subscription.deleted',
			});
			stripeSubscriptionsRetrieveStub.resolves({
				...dummyStripeSubscription,
				status: 'canceled',
				cancel_at_period_end: true,
			});
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				update: therapistSubSingleUpdateStub,
			});
			stripeInfoFindOneStub.resolves(null);
			stripeInfoCreateStub.resolves();

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoSingleUpdateStub.notCalled).to.be.true;
			expect(stripeInfoCreateStub.calledOnce).to.be.true;
			expect(removeFromWaitlistStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: customer.subscription.deleted');
		});

		it('case: invoice.payment_succeeded - should fail if therapist subscription is not found', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'invoice.payment_succeeded',
				data: {
					...dummyWebhookEvent.data,
					object: {
						...dummyWebhookEvent.data.object,
						billing_reason: 'subscription_cycle',
					},
				}
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.notCalled).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Therapist subscription not found');
		});

		it('case: invoice.payment_succeeded - should fail if stripe info is not found', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'invoice.payment_succeeded',
				data: {
					...dummyWebhookEvent.data,
					object: {
						...dummyWebhookEvent.data.object,
						billing_reason: 'subscription_cycle',
					},
				}
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoSingleUpdateStub.notCalled).to.be.true;
			expect(response.status).to.equal(500);
			expect(response.text).to.equal('Webhook error: Stripe info not found');
		});

		it('case: invoice.payment_succeeded - should complete successfully and update old stripe info', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'invoice.payment_succeeded',
				data: {
					...dummyWebhookEvent.data,
					object: {
						...dummyWebhookEvent.data.object,
						billing_reason: 'subscription_cycle',
					},
				}
			});
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves({
				...dummyStripeInfo,
				update: stripeInfoSingleUpdateStub,
			});

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(stripeSubscriptionsRetrieveStub.calledOnce).to.be.true;
			expect(therapistSubFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoFindOneStub.calledOnce).to.be.true;
			expect(stripeInfoSingleUpdateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: invoice.payment_succeeded');
		});

		it('case: invoice.payment_failed - should complete successfully', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'invoice.payment_failed',
			});

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: invoice.payment_failed');
		});

		it('case: default(unhandled events) - should complete successfully', async function () {
			stripeWebhookConstructEventStub.returns({
				...dummyWebhookEvent,
				type: 'invoice.upcoming',
			});

			const response = await request(app)
				.post('/api/stripe/webhook')
				.set('stripe-signature', 'abc')
				.send({});
		
			expect(response.status).to.equal(200);
			expect(response.text).to.equal('Webhook received: invoice.upcoming');
		});
	});
});