import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { TherapistSubscription, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistSubscriptionRoutes from '@/src/application/controllers/api/therapist-subscription';
import jwt from 'jsonwebtoken';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistSubscriptionRoutes);

describe('Therapist Subscription Get API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let therapistSubFindOneStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyTherapistSubscription: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		therapistSubFindOneStub = sandbox.stub(TherapistSubscription, 'findOne');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}

		dummyTherapistSubscription = {
			id: 1,
			subscriptionPlanId: 1,
			therapistId: 2,
			subscriptionType: 'monthly',
			createdAt: new Date(),
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/therapist-subscription/:therapistId', function() {
		// it('should fail if therapistId is not provided', async function () {
		// 	userFindByPkStub.resolves(dummyUser);

		// 	const response = await request(app)
		// 		.get('/api/therapist-subscription/1')
		// 		.set('Authorization', 'Bearer accessToken');

		// 	expect(response.status).to.equal(HttpStatusCode.Forbidden);
		// 	expect(response.body).to.have.property('message', 'Invalid Request.');
		// });

		it('should fail if therapist subscription is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(null);

			const response = await request(app)
				.get('/api/therapist-subscription/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription not found');
		});

		it('should return therapist subscription successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);

			const response = await request(app)
				.get('/api/therapist-subscription/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('plan');
		});
	});
});