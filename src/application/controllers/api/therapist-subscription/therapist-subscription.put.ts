import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { StripeInfo, SubscriptionPlan, TherapistSubscription, User } from '@/src/models'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import stripe from '@/src/configs/stripe-new.config'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/**
 * Update therapist subscription
 */
router.put(
	'/therapist-subscription',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { subscriptionType: newSubType, subscriptionPlanId, therapistSubscriptionId } = req.body
		const { user: therapist } = req
		logger.info(`Subscription update request received for user ID : ${therapist?.id}.`);

		if (!therapist) return res.forbidden('Invalid request');

		if (!newSubType || !subscriptionPlanId || !therapistSubscriptionId) return res.forbidden('Invalid request. Missing required data')

		// const therapist = await User.findOne({
		// 	where: {
		// 		id: user.id,
		// 	},
		// })

		// if (!therapist) return res.forbidden('Therapist does not exist');

		const therapistSubscription = await TherapistSubscription.findOne({
			where: {
				id: therapistSubscriptionId,
				therapistId: therapist.id,
				subscriptionPlanId,
				isActive: true,
			},
			include: [
				{
					model: StripeInfo,
					as: 'stripeInfo',
					where: {
						status: 'active',
					},
					required: true,
				}
			]
		});
		if (!therapistSubscription) {
			logger.info(`Therapist subscription now found for user ID: ${therapist.id}`);
			return res.forbidden('Therapist subscription does not exist');
		}

		if (!therapistSubscription.stripeInfo) {
			logger.info(`Stripe Info now found for user ID: ${therapist.id}`);
			return res.forbidden('Stripe Info does not exist');
		}
		if (!therapistSubscription.stripeInfo.stripeSubscriptionId) {
			logger.info(`Stripe Subscription Id now found for user ID: ${therapist.id}`);
			return res.forbidden('Stripe Subscription Id does not exist');
		}

		if (therapistSubscription.stripeInfo.stripeSubscriptionScheduleId && therapistSubscription.stripeInfo.nextScheduledSubscriptionType) {
			logger.info(`Reverting subscription change for user ID: ${therapist.id}`);
			
			await stripe.subscriptions.update(therapistSubscription.stripeInfo.stripeSubscriptionId, {
				cancel_at_period_end: false,
			});

			try {
				await stripe.subscriptionSchedules.cancel(therapistSubscription.stripeInfo.stripeSubscriptionScheduleId);
			} catch (error: any) {
				logger.error(`Error occurred while cancelling Stripe subscription schedule: ${error}`);

				await stripe.subscriptions.update(therapistSubscription.stripeInfo.stripeSubscriptionId, {
					cancel_at_period_end: true,
				});
				// throw new Error(error);
				return res.forbidden('Something went wrong. Please try again');
			}

			await StripeInfo.update({
				stripeCancelAtPeriodEnd: false,
				stripeSubscriptionScheduleId: null,
				nextScheduledSubscriptionType: null,
			}, {
				where: {
					id: therapistSubscription.stripeInfo.id,
				},
			});

			logger.info(`Subscription change reverted successfully for user ID: ${therapist.id}`);
			return res.success('Your subscription change has been reverted successfully');
		}

		const plan = await SubscriptionPlan.findOne({
			where: {
				id: therapistSubscription.subscriptionPlanId,
			},
		})

		if (!plan) {
			logger.info(`Subscription plan with id ${therapistSubscription.subscriptionPlanId} now found`);
			return res.forbidden('Subscription plan does not exist');
		}

		logger.info(`Creating new Stripe price for subscription type: ${newSubType}`);

		const newPrice = newSubType === 'monthly'
			? await stripe.prices.create({
					currency: 'usd',
					unit_amount: Math.round(plan.monthlyPrice * 100),
					recurring: { interval: 'month' },
					product_data: {
						name: 'Next Therapist - Monthly',
					},
				})
			: newSubType === 'yearly' ? await stripe.prices.create({
					currency: 'usd',
					unit_amount: Math.round(plan.annualPrice * 100),
					recurring: { interval: 'year' },
					product_data: {
						name: 'Next Therapist - Yearly',
					},
				}) : null;

		if (!newPrice) {
			logger.info(`New Price not found for user ID: ${therapist.id}`);
			return res.forbidden('New Price could not be found. Please try again');
		}
		
		const stripeSubscription = await stripe.subscriptions.retrieve(therapistSubscription.stripeInfo.stripeSubscriptionId);
		if (!stripeSubscription) {
			logger.info(`Stripe Subscription with id ${therapistSubscription.stripeInfo.stripeSubscriptionId} not found`);
			return res.forbidden('Stripe Subscription does not exist');
		}
		logger.info(`Updating Stripe subscription.`);

		const schedule = await stripe.subscriptionSchedules.create({
			customer: stripeSubscription.customer as string,
			start_date: stripeSubscription.current_period_end,
			end_behavior: 'release',
			phases: [
				{
					items: [
						{
							price: newPrice.id,
							quantity: 1,
						},
					],
					metadata: {
						email: therapist.email,
						userId: therapist.id,
						subscriptionPlanId: plan.id,
						subscriptionType: newSubType,
						planUpdatedByUserAt: new Date().toISOString(),
					},
					default_payment_method: stripeSubscription.default_payment_method as string,
				},
			],
    });

		try {
			await stripe.subscriptions.update(stripeSubscription.id, {
				cancel_at_period_end: true,
			});
		} catch (error: any) {
			logger.error(`Error occurred while updating Stripe subscription: ${error}`);
			await stripe.subscriptionSchedules.cancel(schedule.id);
			// throw new Error(error);
			return res.forbidden('Something went wrong. Please try again');
		}

		await StripeInfo.update({
			stripeCancelAtPeriodEnd: true,
			stripeSubscriptionScheduleId: schedule.id,
			nextScheduledSubscriptionType: newSubType,
		}, {
			where: {
				id: therapistSubscription.stripeInfo.id,
			},
		});

		logger.info(`Therapist subscription updated successfully. Therapist ID: ${therapist.id}`);
		return res.success('Your subscription has been updated successfully');
	})
)

export default router
