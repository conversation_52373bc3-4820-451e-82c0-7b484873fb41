import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { StripeInfo, SubscriptionPlan, TherapistSubscription, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistSubscriptionRoutes from '@/src/application/controllers/api/therapist-subscription';
import stripe from '@/src/configs/stripe-new.config';
import jwt from 'jsonwebtoken';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistSubscriptionRoutes);

describe('Therapist Subscription Update API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let subPlanFindOneStub: sinon.SinonStub;
	let therapistSubFindOneStub: sinon.SinonStub;
	let stripeInfoFindOneStub: sinon.SinonStub;
	let stripeInfoUpdateStub: sinon.SinonStub;

	let stripeSubscriptionsUpdateStub: sinon.SinonStub;
	let stripeSubscriptionSchedulesCancelStub: sinon.SinonStub;
	let stripePricesCreateStub: sinon.SinonStub;
	let stripeSubscriptionsRetrieveStub: sinon.SinonStub;
	let stripeSubscriptionSchedulesCreateStub: sinon.SinonStub;

	let dummyUser: any;
	let dummySubscriptionPlan: any;
	let dummyStripeInfo: any;
	let dummyTherapistSubscription: any;
	let dummyStripeSubscription: any;
	let dummyStripePrice: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		subPlanFindOneStub = sandbox.stub(SubscriptionPlan, 'findOne');
		therapistSubFindOneStub = sandbox.stub(TherapistSubscription, 'findOne');
		stripeInfoFindOneStub = sandbox.stub(StripeInfo, 'findOne');
		stripeInfoUpdateStub = sandbox.stub(StripeInfo, 'update');

		stripeSubscriptionsUpdateStub = sandbox.stub(stripe.subscriptions, 'update');
		stripeSubscriptionSchedulesCancelStub = sandbox.stub(stripe.subscriptionSchedules, 'cancel');
		stripePricesCreateStub = sandbox.stub(stripe.prices, 'create');
		stripeSubscriptionsRetrieveStub = sandbox.stub(stripe.subscriptions, 'retrieve');
		stripeSubscriptionSchedulesCreateStub = sandbox.stub(stripe.subscriptionSchedules, 'create');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			firstname: 'John',
			lastname: 'Doe',
			role: 'therapist',
			timeZone: 'America/New_York',
		}

		dummySubscriptionPlan = {
			id: 1,
			annualPrice: 100,
			monthlyPrice: 10,
			isActive: true,
			createdAt: new Date(),
		}

		dummyStripeInfo = {
			id: 1,
			therapistId: 1,
			stripeCustomerId: 'cus_123456',
			stripeCurrentPeriodEnd: new Date(),
			stripeSubscriptionId: 'sub_123456',
			billedPrice: 1000,
			stripeSubscriptionScheduleId: null,
			nextScheduledSubscriptionType: 'null',
		}

		dummyTherapistSubscription = {
			id: 1,
			subscriptionPlanId: 1,
			therapistId: 2,
			subscriptionType: 'monthly',
			createdAt: new Date(),
			stripeInfo: dummyStripeInfo,
		}

		dummyStripeSubscription = {
			id: 'sub_123456',
			status: 'active',
			current_period_start: 1324654,
			current_period_end: 1324654,
			customer: 'cus_123456',
			cancel_at_period_end: false,
			canceled_at: null,
			default_payment_method: 'pm_123456',
		}

		dummyStripePrice = {
			id: 'price_123456',
			unit_amount: 1000,
			currency: 'usd',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/therapist-subscription', function() {
		it('should fail if subscription type is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Missing required data');
		});

		it('should fail if subscription plan id is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					therapistSubscriptionId: '1',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Missing required data');
		});

		it('should fail if therapist subscription id is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Missing required data');
		});

		it('should fail if therapist subscription is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(null);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Therapist subscription does not exist');
		});

		it('should complete successfully by cancelling subscription schedule when it already exists', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				stripeInfo: {
					...dummyStripeInfo,
					stripeSubscriptionScheduleId: 'sub_schedule_123456',
					nextScheduledSubscriptionType: 'monthly',
				},
			});
			stripeSubscriptionsUpdateStub.resolves();
			stripeSubscriptionSchedulesCancelStub.resolves();
			stripeInfoUpdateStub.resolves();

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.calledOnce).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.calledOnce).to.be.true;
			expect(stripeInfoUpdateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Your subscription change has been reverted successfully');
		});

		it('should fail if error occurs while cancelling subscription schedule', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				stripeInfo: {
					...dummyStripeInfo,
					stripeSubscriptionScheduleId: 'sub_schedule_123456',
					nextScheduledSubscriptionType: 'monthly',
				},
			});
			stripeSubscriptionsUpdateStub.resolves();
			stripeSubscriptionSchedulesCancelStub.rejects(new Error('Error'));

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.calledTwice).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.calledOnce).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Something went wrong. Please try again');
		});

		it('should fail if stripe info is not available', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				stripeInfo: null,
			});

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Stripe Info does not exist');
		});

		it('should fail if stripe subscription id is not available', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				stripeInfo: {
					...dummyStripeInfo,
					stripeSubscriptionId: null,
				},
			});

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Stripe Subscription Id does not exist');
		});

		it('should fail if subscription plan is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			subPlanFindOneStub.resolves(null);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription plan does not exist');
		});

		it('should fail if price could not be created', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'error',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(stripePricesCreateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'New Price could not be found. Please try again');
		});

		it('should fail if stripe subscription is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			stripePricesCreateStub.resolves(dummyStripePrice);
			stripeSubscriptionsRetrieveStub.resolves(null);

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'monthly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.notCalled).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(stripePricesCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Stripe Subscription does not exist');
		});

		it('should fail if stripe subscription update gives error and should cancel the created schedule', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			stripePricesCreateStub.resolves(dummyStripePrice);
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			stripeSubscriptionSchedulesCreateStub.resolves({
				id: 'sub_schedule_123456',
			});
			stripeSubscriptionsUpdateStub.rejects(new Error('Error'));
			stripeSubscriptionSchedulesCancelStub.resolves();

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'yearly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.calledOnce).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.calledOnce).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(stripePricesCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Something went wrong. Please try again');
		});

		it('should update plan of user successfully by creating new subscription schedule', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistSubFindOneStub.resolves(dummyTherapistSubscription);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			stripePricesCreateStub.resolves(dummyStripePrice);
			stripeSubscriptionsRetrieveStub.resolves(dummyStripeSubscription);
			stripeSubscriptionSchedulesCreateStub.resolves({
				id: 'sub_schedule_123456',
			});
			stripeSubscriptionsUpdateStub.resolves();
			stripeInfoUpdateStub.resolves();

			const response = await request(app)
				.put('/api/therapist-subscription')
				.set('Authorization', 'Bearer userToken')
				.send({
					subscriptionType: 'yearly',
					subscriptionPlanId: '1',
					therapistSubscriptionId: '1',
				});
			
			expect(stripeSubscriptionsUpdateStub.calledOnce).to.be.true;
			expect(stripeSubscriptionSchedulesCancelStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.calledOnce).to.be.true;
			expect(stripePricesCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Your subscription has been updated successfully');
		});
	});
});