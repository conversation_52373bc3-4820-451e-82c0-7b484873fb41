import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { StripeInfo } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import stripe from '@/src/configs/stripe-new.config'
import { HttpStatusCode } from 'axios'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Redirect to Stripe Billing Page
 ********************************/
router.post(
	'/stripe/billing',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) throw new ForbiddenError('Invalid request.')

		const { stripeInfoId } = req.body;
		if (!stripeInfoId) return res.forbidden('Invalid Request. Missing required data')

		logger.info(`User ${user?.id} initiated a Stripe billing session.`);

		const stripeInfo = await StripeInfo.findOne({
			where: {
				therapistId: user.id,
				id: stripeInfoId,
			},
		})

		if (!stripeInfo) return res.forbidden('Invalid request. Stripe Info not found')
		
		const billingSession = await stripe.billingPortal.sessions.create({
			customer: stripeInfo.stripeCustomerId,
			return_url: `${process.env.FRONTEND_URL}/subscriptions-payments`
		})
		logger.info(`User ${user.id} successfully created a Stripe billing session.`);

		return res.status(HttpStatusCode.Ok).send({ billingSessionUrl: billingSession.url })
	})
)

export default router
