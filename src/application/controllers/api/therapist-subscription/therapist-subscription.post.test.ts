import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { StripeInfo, SubscriptionPlan, TherapistSubscription, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistSubscriptionRoutes from '@/src/application/controllers/api/therapist-subscription';
import stripe from '@/src/configs/stripe-new.config';
import sequelize from '@/src/configs/database.config';
import { mail } from '@/src/configs/sendgrid.config';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistSubscriptionRoutes);

describe('Therapist Subscription Create API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindOneStub: sinon.SinonStub;
	let subPlanFindOneStub: sinon.SinonStub;
	let therapistSubFindOneStub: sinon.SinonStub;
	let therapistSubCreateStub: sinon.SinonStub;
	let stripeInfoFindOneStub: sinon.SinonStub;
	let stripeInfoCreateStub: sinon.SinonStub;

	let therapistSubUpdateStub: sinon.SinonStub;
	let stripeInfoUpdateStub: sinon.SinonStub;

	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	let stripeCheckoutSessionsRetrieveStub: sinon.SinonStub;

	let dummyUser: any;
	let dummySubscriptionPlan: any;
	let dummyStripeCheckoutSession: any;
	let dummyStripeInfo: any;
	let dummyTherapistSubscription: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindOneStub = sandbox.stub(User, 'findOne');
		subPlanFindOneStub = sandbox.stub(SubscriptionPlan, 'findOne');
		therapistSubFindOneStub = sandbox.stub(TherapistSubscription, 'findOne');
		therapistSubCreateStub = sandbox.stub(TherapistSubscription, 'create');
		stripeInfoFindOneStub = sandbox.stub(StripeInfo, 'findOne');
		stripeInfoCreateStub = sandbox.stub(StripeInfo, 'create');

		therapistSubUpdateStub = sandbox.stub().resolves();
		stripeInfoUpdateStub = sandbox.stub().resolves();

		commitStub = sandbox.stub().resolves();
		rollbackStub = sandbox.stub().resolves();

		sandbox.stub(sequelize, 'transaction').resolves({ 
			commit: commitStub.resolves(),
			rollback: rollbackStub.resolves(),
		} as any);

		sandbox.stub(mail, 'sendMail').resolves();

		stripeCheckoutSessionsRetrieveStub = sandbox.stub(stripe.checkout.sessions, 'retrieve');

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			firstname: 'John',
			lastname: 'Doe',
			role: 'therapist',
			timeZone: 'America/New_York',
		}

		dummySubscriptionPlan = {
			id: 1,
			annualPrice: 100,
			monthlyPrice: 10,
			isActive: true,
			createdAt: new Date(),
		}

		dummyStripeCheckoutSession = {
			id: '123',
			subscription: {
				id: '456',
				status: 'active',
				current_period_start: 1324654,
				current_period_end: 1324654,
				customer: 'cus_123456',
				cancel_at_period_end: false,
				canceled_at: null,
				metadata: {
					userId: '1',
					subscriptionPlanId: '1',
					subscriptionType: 'monthly',
				},
				items: {
					data: [
						{
							price: {
								unit_amount: 1000,
								currency: 'usd',
							},
						}
					]
				}
			},
		}

		dummyStripeInfo = {
			id: 1,
			therapistId: 1,
			stripeCustomerId: 'cus_123456',
			stripeCurrentPeriodEnd: new Date(),
			billedPrice: 1000,
		}

		dummyTherapistSubscription = {
			id: 1,
			subscriptionPlanId: 1,
			therapistId: 2,
			subscriptionType: 'monthly',
			createdAt: new Date(),
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/therapist-subscription', function() {
		it('should fail if user is not found', async function () {
			userFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should fail if sessionId is not provided', async function () {
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Session Id is missing.');
		});

		it('should fail if session is not found', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves(null)

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Session not found.');
		});

		it('should fail if subscription does not exist in stripe session', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves({
				...dummyStripeCheckoutSession,
				subscription: null,
			});

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Request. Subscription is not found in session.');
		});

		it('should fail if subscription is not active', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves({
				...dummyStripeCheckoutSession,
				subscription: {
					...dummyStripeCheckoutSession.subscription,
					status: 'not_active',
				},
			});

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription is not active. Please try again.');
		});

		it('should fail if user ids do not match', async function () {
			userFindOneStub.resolves({
				...dummyUser,
				id: 23
			});
			stripeCheckoutSessionsRetrieveStub.resolves(dummyStripeCheckoutSession);

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Request. User Id does not match.');
		});

		it('should fail if subscription plan is not active', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves(dummyStripeCheckoutSession);
			subPlanFindOneStub.resolves({
				...dummySubscriptionPlan,
				isActive: false,
			});

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription plan is not active. Please try again.');
		});

		it('should complete successfully and update therapist subscription and stripe info', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves(dummyStripeCheckoutSession);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			therapistSubFindOneStub.resolves({
				...dummyTherapistSubscription,
				update: therapistSubUpdateStub,
			});
			stripeInfoFindOneStub.resolves({
				...dummyStripeInfo,
				update: stripeInfoUpdateStub,
			});

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(therapistSubUpdateStub.calledOnce).to.be.true;
			expect(stripeInfoUpdateStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Your subscription has been created successfully');
			expect(response.body).to.have.property('data');
		});

		it('should complete successfully and create therapist subscription and stripe info', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves(dummyStripeCheckoutSession);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			therapistSubFindOneStub.onFirstCall().resolves(null);
			therapistSubFindOneStub.onSecondCall().resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves(null);
			therapistSubCreateStub.resolves(dummyTherapistSubscription);
			stripeInfoCreateStub.resolves(dummyStripeInfo);

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(therapistSubUpdateStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(therapistSubCreateStub.calledOnce).to.be.true;
			expect(stripeInfoCreateStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Your subscription has been created successfully');
			expect(response.body).to.have.property('data');
		});

		it('should fail and rollback', async function () {
			userFindOneStub.resolves(dummyUser);
			stripeCheckoutSessionsRetrieveStub.resolves(dummyStripeCheckoutSession);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			therapistSubFindOneStub.onFirstCall().resolves(null);
			therapistSubFindOneStub.onSecondCall().resolves(dummyTherapistSubscription);
			stripeInfoFindOneStub.resolves(null);
			therapistSubCreateStub.resolves(dummyTherapistSubscription);
			stripeInfoCreateStub.rejects(new Error('Error'));

			const response = await request(app)
				.post('/api/therapist-subscription')
				.send({
					userToken: '123',
					sessionId: '123',
				});

			expect(therapistSubUpdateStub.notCalled).to.be.true;
			expect(stripeInfoUpdateStub.notCalled).to.be.true;
			expect(therapistSubCreateStub.calledOnce).to.be.true;
			expect(stripeInfoCreateStub.calledOnce).to.be.true;
			expect(commitStub.notCalled).to.be.true;
			expect(rollbackStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription creation failed. Please try again.');
			expect(response.body).to.not.have.property('data');
		});
	});
});