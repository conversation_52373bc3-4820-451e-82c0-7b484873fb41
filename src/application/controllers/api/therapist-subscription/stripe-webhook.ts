import { NotFoundError } from '@/src/application/handlers/errors';
import { removePatientsFromTherapistWaitlist } from '@/src/application/repositories/therapist-waitlist.repository';
import sequelize from '@/src/configs/database.config';
import logger from '@/src/configs/logger.config';
import stripe from '@/src/configs/stripe-new.config';
import { StripeInfo, TherapistSubscription } from '@/src/models';
import express, { Request, Response } from 'express'

const router = express.Router()

router.post('/stripe/webhook', async (req: Request, res: Response) => {
	try {
    let event;

		try {
			const sig = req.headers["stripe-signature"];
			event = stripe.webhooks.constructEvent(
				req.body,
				sig as string,
				process.env.STRIPE_WEBHOOK_SECRET as string
			);
		} catch (error: any) {
			logger.error(error);
			return res.status(400).send(`Webhook error: ${error.message}`);
		}

    logger.info(`Received Stripe webhook: ${event.type}`);

    switch (event.type) {
      case "checkout.session.completed":
        logger.info(`Received event: ${event.type}`);
        logger.info(`Checkout session completed.`);
        break;
      case "customer.subscription.created":
        logger.info(`Received event: ${event.type}`);
        logger.info(`A new subscription was created.`);
        const newSubscription = await stripe.subscriptions.retrieve(event.data.object.id);

        // this is only executed when a scheduled subscription starts and it creates a new subscription
        if (newSubscription.metadata.subscriptionType && newSubscription.metadata.planUpdatedByUserAt) {
          logger.info(`Creating New Subscription created for User ${newSubscription.metadata.userId}.`);

          logger.info(`Creating New Therapist Subscription for User ${newSubscription.metadata.userId}.`);
          const transaction = await sequelize.transaction();
          try {
            const newTherapistSubscription = await TherapistSubscription.create({
              therapistId: newSubscription.metadata.userId,
              subscriptionPlanId: newSubscription.metadata.subscriptionPlanId,
              subscriptionType: newSubscription.metadata.subscriptionType,
              isActive: true,
              subscribedAt: new Date(newSubscription.current_period_start * 1000),
            }, {
              transaction,
            });
            logger.info(`New Therapist Subscription created for User ${newSubscription.metadata.userId}.`);

            logger.info(`Updating stripe info details for User ${newSubscription.metadata.userId}.`);
            const stripeInfo = await StripeInfo.findOne({
              where: {
                therapistId: newSubscription.metadata.userId,
                stripeCustomerId: newSubscription.customer,
              },
              transaction,
            });

            if (stripeInfo) {
              await stripeInfo.update({
                therapistSubscriptionId: newTherapistSubscription.id,
                stripeSubscriptionId: newSubscription.id,
                stripeCustomerId: newSubscription.customer,
                stripeCurrentPeriodStart: new Date(newSubscription.current_period_start * 1000),
                stripeCurrentPeriodEnd: new Date(newSubscription.current_period_end * 1000),
                stripeCancelAtPeriodEnd: newSubscription.cancel_at_period_end,
                status: newSubscription.status,
                billedPrice: newSubscription.items.data[0].price.unit_amount,
                currency: newSubscription.items.data[0].price.currency,
                canceledAt: null,
                stripeSubscriptionScheduleId: null,
                nextScheduledSubscriptionType: null,
              }, {
                transaction,
              });
            } else {
              await StripeInfo.create({
                therapistId: newSubscription.metadata.userId,
                therapistSubscriptionId: newTherapistSubscription.id,
                stripeSubscriptionId: newSubscription.id,
                stripeCustomerId: newSubscription.customer,
                stripeCurrentPeriodStart: new Date(newSubscription.current_period_start * 1000),
                stripeCurrentPeriodEnd: new Date(newSubscription.current_period_end * 1000),
                stripeCancelAtPeriodEnd: newSubscription.cancel_at_period_end,
                status: newSubscription.status,
                billedPrice: newSubscription.items.data[0].price.unit_amount,
                currency: newSubscription.items.data[0].price.currency,
              }, {
                transaction,
              });
            }
            await transaction.commit();
            logger.info(`Stripe info details updated successfully for User ${newSubscription.metadata.userId}.`);
          } catch (error: any) {
            await transaction.rollback();
            logger.error(`Error occurred while creating subscription: ${error}`);
            throw error;
          }
        }
        break;
      case "customer.subscription.updated":
				logger.info(`Received event: ${event.type}`);
        logger.info(`A subscription was updated..`);
        const subscription = await stripe.subscriptions.retrieve(event.data.object.id);
        const previousAttributes = event.data.previous_attributes;

        // this is only executed when the subscription is being cancelled or renewed by the user during their active subscription period
        if (
          previousAttributes &&
          previousAttributes.status !== 'incomplete' &&
          'cancel_at' in previousAttributes
          // ('cancel_at' in previousAttributes || 'current_period_end' in previousAttributes)
        ) {
          logger.info(`Subscription update detected for User ${subscription.metadata?.userId}.`);
          if ('cancel_at' in previousAttributes && previousAttributes.cancel_at === null) {
            logger.info('Subscription is being cancelled by the user');
          } else if ('cancel_at' in previousAttributes && previousAttributes.cancel_at !== null) {
            logger.info('Subscription is being renewed by the user');
          }
          // else if ('current_period_end' in previousAttributes && previousAttributes.current_period_end !== null) {
          //   logger.info('Subscription is being auto-renewed by stripe');
          // }

          const therapistSubscription = await TherapistSubscription.findOne({
            where: {
              therapistId: subscription.metadata.userId,
              subscriptionPlanId: subscription.metadata.subscriptionPlanId,
              subscriptionType: subscription.metadata.subscriptionType,
              isActive: true,
            }
          });
          if (!therapistSubscription) throw new NotFoundError('Therapist subscription not found');

          const stripeInfo = await StripeInfo.findOne({
            where: {
              therapistId: subscription.metadata.userId,
              therapistSubscriptionId: therapistSubscription.id,
              stripeCustomerId: subscription.customer,
            },
          })
          if (!stripeInfo) throw new NotFoundError('Stripe info not found');

          logger.info(`Updating stripe info details for User ${subscription.metadata.userId}.`);
          await stripeInfo.update({
            stripeCancelAtPeriodEnd: subscription.cancel_at_period_end,
            status: subscription.status,
            canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
          });
          logger.info(`Stripe info details updated successfully for User ${subscription.metadata.userId}.`);

          logger.info(`Subscription update completed for User ${subscription.metadata.userId}.`);
        }

        break;
      case "customer.subscription.deleted":
        logger.info(`Received event: ${event.type}`);
        logger.info(`Subscription deleted.`);
        const deletedSubscription = await stripe.subscriptions.retrieve(event.data.object.id);

        // this is only executed when a subscription reaches the end of its active period and no auto-renewal is set
        logger.info(`Handling actual subscription cancellation (deletion) for User ${deletedSubscription.metadata?.userId}.`);
        const transaction = await sequelize.transaction();
        try {
          const therapistSubscription = await TherapistSubscription.findOne({
            where: {
              therapistId: deletedSubscription.metadata.userId,
              subscriptionPlanId: deletedSubscription.metadata.subscriptionPlanId,
              isActive: true,
              subscriptionType: deletedSubscription.metadata.subscriptionType,
            },
            transaction,
          });
          if (!therapistSubscription) throw new NotFoundError('Therapist subscription not found');

          await therapistSubscription.update({
            isActive: false,
            expiredAt: new Date(),
          }, {
            transaction,
          });

          const stripeInfo = await StripeInfo.findOne({
            where: {
              therapistId: deletedSubscription.metadata.userId,
              therapistSubscriptionId: therapistSubscription.id,
              stripeCustomerId: deletedSubscription.customer,
            },
            transaction,
          })
          logger.info(`Updating stripe info details for User ${deletedSubscription.metadata.userId}.`);
          if (stripeInfo) {
            await stripeInfo.update({
              stripeCurrentPeriodStart: new Date(deletedSubscription.current_period_start * 1000),
              stripeCurrentPeriodEnd: new Date(deletedSubscription.current_period_end * 1000),
              stripeCancelAtPeriodEnd: deletedSubscription.cancel_at_period_end,
              status: deletedSubscription.status,
              canceledAt: deletedSubscription.canceled_at ? new Date(deletedSubscription.canceled_at * 1000) : null,
            }, {
              transaction,
            });
          } else {
            await StripeInfo.create({
              therapistId: deletedSubscription.metadata.userId,
              therapistSubscriptionId: therapistSubscription.id,
              stripeSubscriptionId: deletedSubscription.id,
              stripeCustomerId: deletedSubscription.customer,
              stripeCurrentPeriodStart: new Date(deletedSubscription.current_period_start * 1000),
              stripeCurrentPeriodEnd: new Date(deletedSubscription.current_period_end * 1000),
              stripeCancelAtPeriodEnd: deletedSubscription.cancel_at_period_end,
              status: deletedSubscription.status,
              canceledAt: deletedSubscription.canceled_at ? new Date(deletedSubscription.canceled_at * 1000) : null,
              billedPrice: deletedSubscription.items.data[0].price.unit_amount,
              currency: deletedSubscription.items.data[0].price.currency,
            }, {
              transaction,
            });
          }
          
          // Remove patients from waitlist only if subscription is canceled and cancel_at_period_end is true
          if (
            deletedSubscription.status === 'canceled' &&
            deletedSubscription.cancel_at_period_end
          ) {
            const removedCount = await removePatientsFromTherapistWaitlist(deletedSubscription.metadata.userId);
            logger.info(`Successfully removed ${removedCount} patient(s) from the waitlist of therapist ID: ${deletedSubscription.metadata.userId} on subscription expiration.`);
          }

          await transaction.commit();
          logger.info('Finished handling actual subscription cancellation (deletion)');
        } catch (error: any) {
          await transaction.rollback();
          logger.error(`Error occurred while cancelling subscription: ${error}`);
          throw error;
        }
        break;
      case "invoice.payment_succeeded":
        logger.info(`Received event: ${event.type}`);
        
        // this is only executed when a subscription is auto-renewed successfully
        if (event.data.object.billing_reason === 'subscription_cycle') {
          const renewedSubscription = await stripe.subscriptions.retrieve(event.data.object.subscription as string);
          logger.info(`Subscription renewed successfully for User ${renewedSubscription.metadata?.userId}.`);

          const therapistSubscription = await TherapistSubscription.findOne({
            where: {
              therapistId: renewedSubscription.metadata.userId,
              subscriptionPlanId: renewedSubscription.metadata.subscriptionPlanId,
              subscriptionType: renewedSubscription.metadata.subscriptionType,
              isActive: true,
            }
          });
          if (!therapistSubscription) throw new NotFoundError('Therapist subscription not found');

          const stripeInfo = await StripeInfo.findOne({
            where: {
              therapistId: renewedSubscription.metadata.userId,
              therapistSubscriptionId: therapistSubscription.id,
              stripeCustomerId: renewedSubscription.customer,
            },
          })
          if (!stripeInfo) throw new NotFoundError('Stripe info not found');

          logger.info(`Updating stripe info details for User ${renewedSubscription.metadata.userId}.`);
            await stripeInfo.update({
              stripeCurrentPeriodStart: new Date(renewedSubscription.current_period_start * 1000),
              stripeCurrentPeriodEnd: new Date(renewedSubscription.current_period_end * 1000),
              stripeCancelAtPeriodEnd: renewedSubscription.cancel_at_period_end,
              status: renewedSubscription.status,
              billedPrice: renewedSubscription.items.data[0].price.unit_amount,
              currency: renewedSubscription.items.data[0].price.currency,
              canceledAt: renewedSubscription.canceled_at ? new Date(renewedSubscription.canceled_at * 1000) : null,
            })
          logger.info(`Stripe info details updated successfully for User ${renewedSubscription.metadata.userId}.`);
        }
        break;
      case "invoice.payment_failed":
        logger.info(`Received event: ${event.type}`);
        break;
      default:
        logger.info(`Unhandled event type: ${event.type}`);
        break;
    }

    return res.status(200).send(`Webhook received: ${event.type}`);
  } catch (error: any) {
    console.error(error);
    return res.status(500).send(`Webhook error: ${error.message}`);
  }
})

export default router