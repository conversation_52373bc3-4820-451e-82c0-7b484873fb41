import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { StripeInfo, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistSubscriptionRoutes from '@/src/application/controllers/api/therapist-subscription';
import stripe from '@/src/configs/stripe-new.config';
import jwt from 'jsonwebtoken';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistSubscriptionRoutes);

describe('Stripe Billing API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let stripeInfoFindOneStub: sinon.SinonStub;

	let stripeBillingSessionCreateStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyStripeInfo: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		stripeInfoFindOneStub = sandbox.stub(StripeInfo, 'findOne');

		stripeBillingSessionCreateStub = sandbox.stub(stripe.billingPortal.sessions, 'create');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}

		dummyStripeInfo = {
			id: 1,
			therapistId: 1,
			stripeCustomerId: 'cus_123456',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/stripe/billing', function() {
		it('should fail if stripeInfoId is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/stripe/billing')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Request. Missing required data');
		});

		it('should fail if stripe info of user is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeInfoFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/stripe/billing')
				.set('Authorization', 'Bearer accessToken')
				.send({
					stripeInfoId: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Stripe Info not found');
		});

		it('should create billing session successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeInfoFindOneStub.resolves(dummyStripeInfo);
			stripeBillingSessionCreateStub.resolves({
				id: '123',
				url: 'https://stripe.com',
			})

			const response = await request(app)
				.post('/api/stripe/billing')
				.set('Authorization', 'Bearer accessToken')
				.send({
					stripeInfoId: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('billingSessionUrl', 'https://stripe.com');
		});
	});
});