import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import multer from 'multer';
import AWS from 'aws-sdk';
import { generateString } from '../../helpers/general.helper';
import { FileMimeType } from '../../helpers/constant.helper';
import logger from '@/src/configs/logger.config';
AWS.config.update({
	accessKeyId: process.env.AWS_ACCESS_KEY,
	secretAccessKey: process.env.AWS_SECRET_KEY
});
const s3 = new AWS.S3();
const storage = multer.memoryStorage(); 

const upload = multer({ storage: storage });


const router = express.Router()

/**
 * Upload multiple files to S3
 */
router.post(
  '/upload-file',
	upload.array('file'),
  wrap(async (req: Request, res: Response) => {
    const files = req.files as Express.Multer.File[];
    if (!files || files.length === 0) {
      return res.forbidden('No files uploaded');
    }

    const uploaded = [];

    for (const file of files) {
      try {
        const ext = FileMimeType[file.mimetype];
        if (!ext) {
          logger.error(`Unsupported file type: ${file.mimetype} for ${file.originalname}`);
          continue; // Skip unsupported file types
        }

        const key = `temp/${generateString(10)}.${ext}`;
        const params = {
          Bucket: 'next-therapist',
          Key: key,
          Body: file.buffer,
          ContentType: file.mimetype,
          ACL: 'public-read'
        };

        const response = await s3.upload(params).promise();
        // await TempUpload.create({ uploaded_file_key: response.Key });

        uploaded.push({ ...response, originalName: file.originalname});
      } catch (error) {
        logger.error(`Failed to upload ${file.originalname}:`, error);
      } 
    }

    return res.send({ uploaded });
  })
);

export default router
