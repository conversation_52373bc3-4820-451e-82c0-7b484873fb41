import sinon from 'sinon';
import express from 'express';
import { MinorPatient, PatientProfile, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import patientsPutRoute from '@/src/application/controllers/api/patients/patients.put';
import { ERROR_MESSAGES,REASON_FOR_THERAPY_UPDATED,REASON_FOR_THERAPY_REQUIRED,ERROR_UPDATING_REASON_FOR_THERAPY,ERROR_REASON_LIMIT } from './constants';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', patientsPutRoute);

describe('Patient Update API Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let userUpdateStub: sinon.SinonStub;
	let patientProfileUpdateStub: sinon.SinonStub;
	let minorPatientFindAllStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyPatient: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		userUpdateStub = sandbox.stub(User, 'update');
		patientProfileUpdateStub = sandbox.stub(PatientProfile, 'update');
		minorPatientFindAllStub = sandbox.stub(MinorPatient, 'findAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}

		dummyPatient = {
			id: 1,
			firstname: 'John',
			lastname: 'Doe',
			role: 'patient',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/v1/patient/update/profile', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.put('/api/v1/patient/update/profile')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.AUTHENTICATION_REQUIRED);
		});

		it('should fail if patient is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.put('/api/v1/patient/update/profile')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.USER_NOT_FOUND);
		});

		it('should fail if bio length is more than 150 characters', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyPatient);

			const response = await request(app)
				.put('/api/v1/patient/update/profile')
				.set('Authorization', 'Bearer accessToken')
				.send({
					bio: 'Discover the unexpected in every moment—where creativity meets purpose, and ideas turn into action. Embrace the journey, explore more, and stay inspired every day.',
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.BIO_TOO_SHORT);
		});

		it('should update patient data successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyPatient);
			minorPatientFindAllStub.resolves([dummyPatient]);

			const response = await request(app)
				.put('/api/v1/patient/update/profile')
				.set('Authorization', 'Bearer accessToken')
				.send({
					firstname: 'Jake',
					lastname: 'Doe',
					gender: 'male',
					dob: '1990-01-01',
					timeZone: 'Asia/Kolkata',
					bio: 'Discover the unexpected in every moment—where creativity meets purpose.',
					seekingTherapyFor: 'self',
					address: '123 Main St, New York, NY 10001',
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('patient');
			expect(response.body).to.have.property('minorPatients');
		});
	});

	describe('PUT /api/v1/patient/reason-for-therapy', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.put('/api/v1/patient/reason-for-therapy')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Unauthorized);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.AUTHENTICATION_REQUIRED);
		});

		it('should fail if reason is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/reason-for-therapy')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', REASON_FOR_THERAPY_REQUIRED);
		});

		it('should fail if reason is more than 150 characters', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/reason-for-therapy')
				.set('Authorization', 'Bearer accessToken')
				.send({
					reason: 'Discover the unexpected in every moment—where creativity meets purpose, and ideas turn into action. Embrace the journey, explore more, and stay inspired every day.'
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', ERROR_REASON_LIMIT);
		});

		it('should fail if user to update is not found', async function () {
			userFindByPkStub.onFirstCall().resolves(dummyUser);
			userFindByPkStub.onSecondCall().resolves(null);

			const response = await request(app)
				.put('/api/v1/patient/reason-for-therapy')
				.set('Authorization', 'Bearer accessToken')
				.send({
					reason: 'Discover the unexpected in every moment—where creativity meets purpose, and ideas turn.'
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.USER_NOT_FOUND);
		});

		it('should fail if error occurs while updating reason for therapy', async function () {
			userFindByPkStub.resolves(dummyUser);
			userUpdateStub.rejects(new Error('Error updating reason for therapy'));

			const response = await request(app)
				.put('/api/v1/patient/reason-for-therapy')
				.set('Authorization', 'Bearer accessToken')
				.send({
					reason: 'Discover the unexpected in every moment—where creativity meets purpose, and ideas turn.'
				});

			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', ERROR_UPDATING_REASON_FOR_THERAPY);
		});

		it('should update reason for therapy successfully', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/reason-for-therapy')
				.set('Authorization', 'Bearer accessToken')
				.send({
					reason: 'Discover the unexpected in every moment—where creativity meets purpose, and ideas turn.'
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', REASON_FOR_THERAPY_UPDATED);
			expect(response.body).to.have.property('reason');
		});
	});
});