import express from 'express';
import get from './patients.get';
import getProfile from './profile.get';
import getQuestinnaire from './questionnaire.get';
import updateProfile from './profile.put';
import updatePatientProfile from './patients.put';
import updateProfilePicture from './userProfile.put';
import bookAppointment from './bookAppointment.get';
import removePatient from './removePatient';
import updateTimezone from './patient-timezone.put';
import minorPatientUpdate from './minorPatient.put';
import calendarInvite from './calendarInvite.post';

const app = express();

app.use(get);
app.use(getProfile);
app.use(getQuestinnaire);
app.use(updateProfile);
app.use(updatePatientProfile);
app.use(updateProfilePicture);
app.use(bookAppointment);
app.use(removePatient);
app.use(updateTimezone);
app.use(minorPatientUpdate);
app.use(calendarInvite);


export default app;
