import express, { Request, Response } from 'express';
import { wrap } from '@/src/application/helpers';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import { MinorPatient } from '@/src/models';
import { ERROR_MESSAGES } from './constants';
import logger from '@/src/configs/logger.config';
import AWS from 'aws-sdk';
import { generateString } from '../../../helpers/general.helper';
import multer from 'multer';
import { FileMimeType } from '../../../helpers/constant.helper';
import {UPDATED_PROFILE} from './constants'
import dayjs from 'dayjs';
import { BadRequestError } from '@/src/application/handlers/errors'
import {DOB_VALIDATION} from './constants'

AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY,
});

const s3 = new AWS.S3();
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });


const router = express.Router();

/********************************
 * * Update Minor Patient's Profile
 ********************************/
router.put(
    '/update/minorPatient',
    APIMiddleware,
    upload.single('file'),
    wrap(async (req: Request, res: Response) => {
        const userId = req.body.id;
        logger.info(`[Minor Patient Profile Update] User ID: ${userId} initiated a profile update.`);
        if (!userId) {
            logger.info(`[Minor Patient Profile Update] Unauthorized access attempt.`);
            return res.status(400).send({ message: ERROR_MESSAGES.AUTHENTICATION_REQUIRED });
        }
        let minorPatient = await MinorPatient.findOne({
            where: { id: userId },
        });
        const updates: Partial<MinorPatient> = {};
        if (req.body.firstName) updates.firstName = req.body.firstName;
        if (req.body.lastName) updates.lastName = req.body.lastName;
        if (req.body.dob) {
            const minorDob = req.body.dob;
            const minorAge = dayjs().diff(dayjs(minorDob), 'year');
            if (minorAge >= 18) {
                throw new BadRequestError(DOB_VALIDATION);
            }
            updates.dob = minorDob;
        }
        
        if (!minorPatient) {
            return res.status(404).send({ message: ERROR_MESSAGES.USER_NOT_FOUND });
        }
        
        if (req.file) {
            const currentProfile = minorPatient?.userProfile;
        
            const params = {
                Bucket: 'next-therapist',
                Key: `temp/${generateString(10)}.${FileMimeType[req.file.mimetype]}`,
                Body: req.file.buffer,
                ContentType: req.file.mimetype,
                ACL: 'public-read',
            };
        
            const response = await s3.upload(params).promise();
        
            updates.userProfile = response.Key;
            logger.info(`User ${userId} successfully uploaded a new profile picture: ${response.Key}`);
        
            if (currentProfile) {
                const deleteParams = {
                    Bucket: 'next-therapist',
                    Key: currentProfile,
                };
        
                try {
                    await s3.deleteObject(deleteParams).promise();
                    logger.info(`Deleted old profile picture from S3: ${currentProfile}`);
                } catch (error) {
                    logger.error(`Failed to delete old profile picture from S3: ${currentProfile}`, error);
                }
            }
        } else {
            const currentProfile = minorPatient?.userProfile;
        
            if (currentProfile) {
                const deleteParams = {
                    Bucket: 'next-therapist',
                    Key: currentProfile,
                };
        
                try {
                    await s3.deleteObject(deleteParams).promise();
                    logger.info(`Deleted profile picture from S3 as no new file was provided: ${currentProfile}`);
                } catch (error) {
                    logger.error(`Failed to delete profile picture from S3: ${currentProfile}`, error);
                }
            }
        
            updates.userProfile = null;
        }
        
        await MinorPatient.update(updates, { where: { id: userId } });
        
        const updatedMinorPatient = await MinorPatient.findOne({
            where: { id: userId },
        });
        
        if (!updatedMinorPatient) {
            return res.status(404).send({ message: ERROR_MESSAGES.USER_NOT_FOUND });
        }
        
        return res.send({
            message: UPDATED_PROFILE,
            minorPatient: updatedMinorPatient,
        });    
    })
);

export default router;
