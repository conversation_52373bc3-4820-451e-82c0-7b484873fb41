import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import PatientProfile from '@/src/models/patient-profile.model'
import { Includeable } from 'sequelize'
import { UserType } from '@/src/application/helpers/constant.helper'
import { Answer, Page, Questionnaire, User, UserQuestionnaire, UserSettings, MinorPatient } from '@/src/models'
import { NotFoundError } from '@/src/application/handlers/errors'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Get patient profile details
 ********************************/
router.get(
    '/patient/profile',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const userId = req.user?.id;
        logger.info(`[Patient Profile] User ${userId} requested their profile.`);
        if (!userId) throw new NotFoundError('User not found');

        const patient = await User.findOne({
            where: {
                id: userId,
                role: UserType.PATIENT,
            },
            include: [
                {
                    model: PatientProfile,
                    as: 'patient_profile',
                    attributes: ['paymentPreference', 'preferredDistance', 'telehealth', 'seekingTherapyFor'],
                },
                {
                    model: UserSettings,
                    as: 'settings',
                    attributes: ['id', 'notificationSettings']
                }
            ],
        });

        if (!patient) throw new NotFoundError('Patient not found');

        const minorPatients = await MinorPatient.findAll({
            where: {
                userId: userId,
            },
        });

        logger.info(`[Patient Profile] Successfully retrieved profile for User ${userId}.`);
        const response = {
            ...patient.toJSON(),
            minorPatients: minorPatients,
        };

        return res.send(response);
    })
)

export default router
