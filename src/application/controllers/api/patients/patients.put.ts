import express, { Request, Response } from 'express';
import { wrap } from '@/src/application/helpers';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import { User, PatientProfile,MinorPatient } from '@/src/models';
import { ERROR_MESSAGES,REASON_FOR_THERAPY_UPDATED,REASON_FOR_THERAPY_REQUIRED,ERROR_UPDATING_REASON_FOR_THERAPY,ERROR_REASON_LIMIT } from './constants';
import logger from '@/src/configs/logger.config';

const router = express.Router();

/********************************
 * * Update Patient's Profile
 ********************************/
router.put(
    '/patient/update/profile',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const userId = req.user?.id;
        logger.info(`[Patient Profile Update] User ID: ${userId} initiated a profile update.`);

        if (!userId) {
            logger.info(`[Patient Profile Update] Unauthorized access attempt.`);
            return res.status(400).send({ message: ERROR_MESSAGES.AUTHENTICATION_REQUIRED });
        }

        let patient = await User.findOne({
            where: { id: userId },
        });

        if (!patient) {
            return res.status(404).send({ message: ERROR_MESSAGES.USER_NOT_FOUND });
        }

        const updates: Partial<User> = {};
        if (req.body.firstname) updates.firstname = req.body.firstname;
        if (req.body.lastname) updates.lastname = req.body.lastname;
        if (req.body.gender) updates.gender = req.body.gender;
        if (req.body.dob) updates.dob = req.body.dob;
        if (req.body.timeZone) updates.dob = req.body.timeZone
        if(req.body.gender) updates.gender = req.body.gender

        if (req.body.bio !== undefined) {
            if (req.body.bio.length > 150) {
                return res.status(400).send({ message: ERROR_MESSAGES.BIO_TOO_SHORT });
            }
            updates.bio = req.body.bio;
        }
        

        if (Object.keys(updates).length > 0) {
            await User.update(updates, { where: { id: userId } });
            logger.info(`[Patient Profile Update] User ID: ${userId} successfully updated profile details.`);
        }

        if (req.body.seekingTherapyFor) {
            await PatientProfile.update(
                { seekingTherapyFor: req.body.seekingTherapyFor },
                { where: { userId: userId } }
            );
            logger.info(`[Patient Profile Update] User ID: ${userId} updated therapy preferences.`);
        }

        if (req.body.address) {
            await User.update(
                { address: req.body.address },
                { where: { id: userId } }
            );
            logger.info(`[Patient Profile Update] User ID: ${userId} updated address information.`);
        }
        logger.info(`[Patient Profile Update] User ID: ${userId} profile update completed.`);

        patient = await User.findOne({ where: { id: userId } });
        const minorPatients = await MinorPatient.findAll({
            where: { userId: userId },
          });
        return res.send({patient,minorPatients});
    })
);

/**
 * @route PUT /patient/reason-for-therapy
 * @middleware APIMiddleware
 * @description Updates the reason for therapy for the authenticated user.
 * @param {Request} req - Express request object containing the user's ID from the middleware and the reason in the body.
 * @param {Response} res - Express response object used to send back HTTP responses.
 * @returns {Response} 200 - If the reason is successfully updated.
 */

router.put(
    '/patient/reason-for-therapy',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const userId = req.user?.id;
        logger.info(`[Reason Update] User ID: ${userId} initiated reason update.`);

        if (!userId) {
            logger.error(`[Reason Update] Unauthorized access attempt.`);
            return res.status(401).send({ message: ERROR_MESSAGES.AUTHENTICATION_REQUIRED });
        }

        if (!req.body.reason) {
            logger.error(`[Reason Update] No reason provided in request body.`);
            return res.status(400).send({ message: REASON_FOR_THERAPY_REQUIRED });
        }

        if (req.body.reason.length > 150) {
            logger.error(`[Reason Update] Reason exceeds 150 characters.`);
            return res.status(400).send({ message: ERROR_REASON_LIMIT});
        }

        try {
            const user = await User.findByPk(userId);
            
            if (!user) {
                logger.error(`[Reason Update] User not found with ID: ${userId}`);
                return res.status(404).send({ message: ERROR_MESSAGES.USER_NOT_FOUND });
            }

            await User.update(
                { reason_for_help: req.body.reason },
                { where: { id: userId } }
            );

            logger.info(`[Reason Update] User ID: ${userId} successfully updated reason for therapy.`);
            
            return res.status(200).send({
                message: REASON_FOR_THERAPY_UPDATED,
                reason: req.body.reason
            });
        } catch (error) {
            logger.error(`[Reason Update] Error updating reason for User ID: ${userId}`, error);
            return res.status(500).send({ message: ERROR_UPDATING_REASON_FOR_THERAPY });
        }
    })
);

/**
 * @route PUT /patient/seeking-therapy-for
 * @middleware APIMiddleware
 * @description Updates the `therapy_for` field for the authenticated user.
 * @param {Request} req - Express request object containing the authenticated user's ID and the `therapy_for` value in the body.
 * @param {Response} res - Express response object used to send back the status and message.
 * @returns {Object} 200 - Success response with updated `therapy_for` field.
 */


router.put(
    '/patient/seeking-therapy-for',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const userId = req.user?.id;
        logger.info(`[Therapy For Update] User ID: ${userId} initiated therapy_for update.`);

        if (!userId) {
            logger.error(`[Therapy For Update] Unauthorized access attempt.`);
            return res.status(401).send({ message: ERROR_MESSAGES.AUTHENTICATION_REQUIRED });
        }

        if (!req.body.therapy_for) {
            logger.error(`[Therapy For Update] No therapy_for value provided in request body.`);
            return res.status(400).send({ message: 'Therapy for field is required' });
        }

        try {
            const user = await User.findByPk(userId);
            
            if (!user) {
                logger.error(`[Therapy For Update] User not found with ID: ${userId}`);
                return res.status(404).send({ message: ERROR_MESSAGES.USER_NOT_FOUND });
            }

            await User.update(
                { therapy_for: req.body.therapy_for },
                { where: { id: userId } }
            );

            logger.info(`[Therapy For Update] User ID: ${userId} successfully updated therapy_for field.`);
            
            return res.status(200).send({
                message: 'Therapy for field updated successfully',
                therapy_for: req.body.therapy_for
            });
        } catch (error) {
            logger.error(`[Therapy For Update] Error updating therapy_for for User ID: ${userId}`, error);
            return res.status(500).send({ message: 'Error updating therapy for field' });
        }
    })
);

export default router;
