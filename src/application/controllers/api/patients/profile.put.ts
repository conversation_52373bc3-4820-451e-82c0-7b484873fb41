import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { UserQuestionnaire } from '@/src/models'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Update patient profile
 ********************************/
router.put(
	'/patient/profile',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		// update looged in user's profile

		const { questionnaireId, pageId, answers } = req.body
		const userId = req.user?.id
		if (!questionnaireId || !pageId || !answers) {
			return res.forbidden('Invalid request. questionnaireId, pageId, and answers are required.')
		}

		logger.info(`[INFO] [Patient Profile] User ${userId} is updating questionnaire ${questionnaireId} on page ${pageId}.`);

		const existingUserQuestionnaires = await UserQuestionnaire.findAll({
			where: {
				userId,
				questionnaireId,
				pageId
			}
		})

		// Get existing answer IDs
		const existingAnswerIds = existingUserQuestionnaires.map(uq => uq.answerId)

		// Convert new answers to Set for easier comparison
		const newAnswerIdsSet = new Set(answers)
		// Convert existing answers to Set for easier comparison
		const existingAnswerIdsSet = new Set(existingAnswerIds)

		// Find answers to create (in new but not in existing)
		const answersToCreate = answers.filter((answerId: number) => !existingAnswerIdsSet.has(answerId))

		// Find answers to delete (in existing but not in new)
		const answersToDelete = existingAnswerIds.filter(answerId => !newAnswerIdsSet.has(answerId))

		// Perform delete operation if needed
		if (answersToDelete.length > 0) {
			await UserQuestionnaire.destroy({
				where: {
					userId,
					questionnaireId,
					pageId,
					answerId: answersToDelete
				}
			})
		}

		// Perform create operation if needed
		if (answersToCreate.length > 0) {
			logger.info(`[Patient Profile] User ${userId} added answers: ${answersToCreate.join(', ')} to questionnaire ${questionnaireId}, page ${pageId}.`);
			await UserQuestionnaire.bulkCreate(
				answersToCreate.map((answerId: number) => ({
					pageId,
					answerId,
					questionnaireId,
					userId,
				}))
			)
		}

		logger.info(`[Patient Profile] User ${userId} successfully updated profile.`);

		return res.json({
			message: 'Successfully updated profile',
			stats: {
				created: answersToCreate.length,
				deleted: answersToDelete.length,
				unchanged: existingAnswerIds.length - answersToDelete.length
			}
		})
	})
)

export default router
