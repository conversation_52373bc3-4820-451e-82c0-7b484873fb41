import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { isValidTimeZone } from '@/src/application/helpers/general.helper';
import { User } from '@/src/models'
import logger from '@/src/configs/logger.config';

const router = express.Router()

/********************************
 * * Update patient timezone
 ********************************/
router.put(
	'/patient-timezone',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		logger.info(`[Patient Timezone] User ID: ${user?.id} requested to update timezone.`);
		if (!user) return res.forbidden('Invalid Request.')
		
		const { timezone } = req.body
		if (!timezone) return res.forbidden('Timezone is required.');

		const isValid = isValidTimeZone(timezone);

		if (!isValid)
			return res.forbidden('Invalid timezone. Please provide a valid IANA timezone format, e.g., "America/New_York".');

		await User.update({
			timeZone: timezone
		}, {
			where: {
				id: user.id
			}
		});
		logger.info(`[Patient Timezone] User ${user.id} successfully updated timezone to ${timezone}.`);
		return res.success('Timezone updated successfully.');
	})
)

export default router
