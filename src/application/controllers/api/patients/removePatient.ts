import express, { Request, Response } from 'express';
import { wrap } from '@/src/application/helpers';
import { User, Appointments, PatientTherapist, UserSettings, TherapistProfile, PatientProfile, UserDevice, UserRegistrationInfo } from '@/src/models';
import { Op, Sequelize } from 'sequelize';
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware';
import { USER_DELETE } from './constants';
import logger from '@/src/configs/logger.config';

const router = express.Router();

/********************************
 * * Delete patient (Set inactive & delete appointment data)
 ********************************/
router.delete(
  '/users/:userId',
    APIMiddleware,
  wrap(async (req: Request, res: Response) => {
    const { userId } = req.params;
    logger.info(`[Patient Deletion] User ${req.user?.id} requested to delete user ${userId}.`);


    const transaction = await User.sequelize?.transaction();

    try {
      if (!transaction) {
        return res.status(500).json({ message: 'Transaction could not be started' });
      }

      const user = await User.findOne({
        where: { id: userId },
        include: [
          {
            model: UserRegistrationInfo,
            as: 'registrationInfo',
            where: { pageName: 'profile-info' },
            attributes: {
              exclude: ['payloadInfo'],
              include: [
                [
                  Sequelize.literal(`(
                    SELECT jsonb_strip_nulls(
                      jsonb_build_object(
                        'user_profile', "payloadInfo"->>'user_profile'
                      )
                    )
                    FROM "user_registration_informations"
                    WHERE "user_registration_informations"."userId" = "registrationInfo"."userId"
                    AND "user_registration_informations"."pageName" = 'profile-info'
                    LIMIT 1
                  )`),
                  'payload_info',
                ],
              ],
            },
            required: false,
          },
        ],
        transaction,
      });
      

      if (!user) {
        throw new Error('User not found');
      }

      logger.info(`[Patient Deletion] User ${userId} found. Proceeding with deactivation.`);
      await User.update({ active: false }, { where: { id: userId }, transaction });
      await user.update({ version: 0 }, { transaction });

      logger.info(`[Patient Deletion] User ${userId} marked as inactive.`);


      await Promise.all([
        Appointments.destroy({ where: { [Op.or]: [{ therapistId: userId }, { patientId: userId }] }, transaction }),
      ]);

      logger.info(`[Patient Deletion] Deleted related appointments for user ${userId}.`);


      await transaction.commit();

      logger.info(`[Patient Deletion] Successfully deleted user ${userId}.`);

      res.status(200).json({
        message: USER_DELETE,
        user: {
          id: user.id,
          email: user.email,
          firstname: user.firstname,
          lastname: user.lastname,
          active: false,
          profile: user.profile,
        },
      });
    } catch (error) {
      logger.error(`[Patient Deletion] Failed to delete user ${userId}:`, (error as Error)?.message || 'Unknown error');
      
      await transaction?.rollback();
      res.status(500).json({
        message: 'Failed to delete user',
      });
    }
  })
);

export default router;
