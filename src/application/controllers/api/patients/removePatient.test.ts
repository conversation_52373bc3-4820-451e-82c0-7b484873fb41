import sinon from 'sinon';
import express from 'express';
import { Appointments, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import removePatientRoute from '@/src/application/controllers/api/patients/removePatient';
import { USER_DELETE } from './constants';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', removePatientRoute);

describe('Remove Patient API Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let userUpdateStub: sinon.SinonStub;
	let appointmentsDestroyStub: sinon.SinonStub;

	let transactionStub: sinon.SinonStub;
	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyPatient: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		userUpdateStub = sandbox.stub(User, 'update');
		appointmentsDestroyStub = sandbox.stub(Appointments, 'destroy');

		commitStub = sinon.stub().resolves();
		rollbackStub = sinon.stub().resolves();

		const fakeTransaction = {
			commit: commitStub,
			rollback: rollbackStub,
		};
		transactionStub = sandbox.stub(User.sequelize as any, 'transaction').resolves(fakeTransaction);

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}

		dummyPatient = {
			id: 1,
			firstname: 'John',
			lastname: 'Doe',
			role: 'patient',
			update: () => Promise.resolve(),
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('DELETE /api/v1/users/:userId', function() {
		it('should return error if transaction is not available', async () => {
			userFindByPkStub.resolves(dummyUser);
			transactionStub.resolves(null);
	
			const response = await request(app)
				.delete('/api/v1/users/1')
				.set('Authorization', 'Bearer accessToken');
	
			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'Transaction could not be started');
		});

		it('should rollback transaction if user is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);
	
			const response = await request(app)
				.delete('/api/v1/users/1')
				.set('Authorization', 'Bearer accessToken');
	
			expect(rollbackStub.calledOnce).to.be.true;
			expect(commitStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'Failed to delete user');
		});

		it('should delete user successfully', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyPatient);
			userUpdateStub.resolves();
			appointmentsDestroyStub.resolves();
	
			const response = await request(app)
				.delete('/api/v1/users/1')
				.set('Authorization', 'Bearer accessToken');

			expect(rollbackStub.notCalled).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', USER_DELETE);
			expect(response.body).to.have.property('user').that.is.an('object');
		});
	});
});