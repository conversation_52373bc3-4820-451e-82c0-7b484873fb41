import sinon from 'sinon';
import express from 'express';
import { MinorPatient, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import patientProfileGetRoute from '@/src/application/controllers/api/patients/profile.get';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', patientProfileGetRoute);

describe('Patient Profile Fetch Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let minorPatientFindAllStub: sinon.SinonStub;

	let dummyUser: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		minorPatientFindAllStub = sandbox.stub(MinorPatient, 'findAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/v1/patient/profile', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.get('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should fail if patient is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.get('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Patient not found');
		});

		it('should return patient profile successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				toJSON: () => dummyUser,
			});
			minorPatientFindAllStub.resolves([dummyUser]);

			const response = await request(app)
				.get('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object').to.have.property('minorPatients');
		});
	});
});