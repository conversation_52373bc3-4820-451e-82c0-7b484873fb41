import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginated, paginatedData } from '@/src/application/helpers/pagination.helper'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { UserType } from '@/src/application/helpers/constant.helper'
import { User } from '@/src/models'
import { GroupOption, WhereOptions, Order, FindAttributeOptions, Op } from 'sequelize'
import logger from '@/src/configs/logger.config'
import sequelize from '@/src/configs/database.config'

const router = express.Router()

/********************************
 * * Get all patients
 ********************************/
router.get(
	'/patients',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const group: GroupOption = []
		let where: WhereOptions = {}
		const order: Order = [['firstname', 'ASC']]
		const relations: any = []

		if (req.user?.role === UserType.PATIENT) {
			logger.info(`Patient ID: ${req.user?.id} is trying to access patients list. Access denied.`);
			return res.forbidden('You are not allowed to access this resource.')
		}
		
		where = {
			role: UserType.PATIENT,
			id: {
				[Op.ne]: req.user?.id,
			},
			deletedAt: null,
			deactivatedAt: null,
		}

		if (req.user?.role === UserType.THERAPIST) {
			relations.push({
				model: User,
				as: 'therapists',
				required: true,
				attributes: [],
				where: {
					id: req.user?.id,
				},
			})
		}
		
		if (req.query.search) {
			logger.info(`[Patients] User ID: ${req.user?.id} is searching for patients with query: "${req.query.search}".`);
			where = {
				...where,
				[Op.or]: [
					{
						firstname: {
							[Op.iLike]: `%${req.query.search}%`,
						},
					},
					{
						lastname: {
							[Op.iLike]: `%${req.query.search}%`,
						},
					},
				],
			}
		}

		order.push(['createdAt', 'DESC'])

		const patients = await User.findAndCountAll({
			where,
			attributes: {
				include: [
					[
						sequelize.literal(`EXISTS (
							SELECT 1 FROM "patient_cards" AS pc
							WHERE pc."patientId" = "User"."id"
						)`),
						'hasPaymentSetup'
					]
				],
			},
			include: relations,
			group,
			order,
			...paginated(req),
		})
		
		logger.info(`[Patients] User ID: ${req.user?.id} successfully retrieved ${patients.rows.length} patients.`);
		res.send(paginatedData(patients, req))
	})
)

export default router
