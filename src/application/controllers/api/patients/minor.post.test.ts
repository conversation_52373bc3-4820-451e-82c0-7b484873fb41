import sinon from 'sinon';
import express from 'express';
import { MinorPatient, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import { DOB_VALIDATION, USER_ID_REQUIRED, SUCCESS_ADD_MINORS, MINOR_DETAILS_REQUIRED, MINOR_ALREADY_EXISTS } from './constants'
import minorPatientPostRoute from '@/src/application/controllers/api/patients/minor.post';
import dayjs from 'dayjs';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', minorPatientPostRoute);

describe('Create Minor Patient Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let minorPatientFindOneStub: sinon.SinonStub;
	let minorPatientCreateStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyMinorPatient: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		minorPatientFindOneStub = sandbox.stub(MinorPatient, 'findOne');
		minorPatientCreateStub = sandbox.stub(MinorPatient, 'create');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}

		dummyMinorPatient = {
			id: 1,
			userId: 1,
			firstName: 'John',
			lastName: 'Doe',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/v1/minor-patient', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.post('/api/v1/minor-patient')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', USER_ID_REQUIRED);
		});

		it('should fail if minor details is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/minor-patient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					minor_details: [],
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', MINOR_DETAILS_REQUIRED);
		});

		it('should fail if minor is above 18 years old', async function () {
			userFindByPkStub.resolves(dummyUser);
			const dob = dayjs().subtract(19, 'year').format('YYYY-MM-DD');

			const response = await request(app)
				.post('/api/v1/minor-patient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					minor_details: [
						{
							firstName: 'Jake',
							lastName: 'Doe',
							dob,
						}
					],
				});

			expect(minorPatientCreateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', DOB_VALIDATION);
		});

		it('should fail if minor patient already exist', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.resolves(dummyMinorPatient);

			const dob = dayjs().subtract(12, 'year').format('YYYY-MM-DD');

			const response = await request(app)
				.post('/api/v1/minor-patient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					minor_details: [
						{
							firstName: 'Jake',
							lastName: 'Doe',
							dob,
						}
					],
				});

			expect(minorPatientCreateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', MINOR_ALREADY_EXISTS);
		});

		it('should create new minor patient successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.resolves(null);

			const dob = dayjs().subtract(12, 'year').format('YYYY-MM-DD');

			const response = await request(app)
				.post('/api/v1/minor-patient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					minor_details: [
						{
							firstName: 'Jake',
							lastName: 'Doe',
							dob,
						}
					],
				});
			
			expect(minorPatientCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', SUCCESS_ADD_MINORS);
		});
	});
});