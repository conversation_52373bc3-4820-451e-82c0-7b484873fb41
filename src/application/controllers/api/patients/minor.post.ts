import { Router, Request, Response } from 'express'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { wrap } from '@/src/application/helpers'
import MinorPatient from '@/src/models/minor-patient.model'
import { DOB_VALIDATION, USER_ID_REQUIRED, SUCCESS_ADD_MINORS, MINOR_DETAILS_REQUIRED, MINOR_ALREADY_EXISTS } from './constants'
import logger from '@/src/configs/logger.config'
import dayjs from 'dayjs'

const router = Router()

router.post(
    '/minor-patient',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const userId = req.user?.id
        const { minor_details } = req.body

        if (!userId) {
            return res.status(400).send({ message: USER_ID_REQUIRED})
        }

        if (!Array.isArray(minor_details) || minor_details.length === 0) {
            return res.status(400).send({ message: MINOR_DETAILS_REQUIRED })
        }

        logger.info(`[Minor Patient] User ${userId} is adding ${minor_details.length} minors.`);

        const currentDate = dayjs();

        for (const detail of minor_details) {
            const { firstName, lastName, dob } = detail
            const dobDate = dayjs(dob)

            if (currentDate.diff(dobDate, 'year') > 18) {
                return res.status(400).send({ message: DOB_VALIDATION })
            }

            const existingMinor = await MinorPatient.findOne({
                where: {
                    userId,
                    firstName,
                    lastName,
                    dob: dobDate.toDate(),
                },
            })

            if (existingMinor) {
                logger.info(`[Minor Patient] User ${userId} attempted to add a duplicate minor (Record ID: ${existingMinor.id}).`);
                return res.status(400).send({ message: MINOR_ALREADY_EXISTS })
            }

            await MinorPatient.create({
                userId,
                firstName,
                lastName,
                dob: dobDate.toDate(),
            })
        }
        logger.info(`[Minor Patient] User ${userId} successfully added minors.`);
        return res.status(201).send({ message: SUCCESS_ADD_MINORS })
    })
)

export default router