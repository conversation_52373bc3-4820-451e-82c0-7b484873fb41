import sinon from 'sinon';
import express from 'express';
import { User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import patientTimezonePutRoute from '@/src/application/controllers/api/patients/patient-timezone.put';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', patientTimezonePutRoute);

describe('Patient Timezone Update Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userUpdateStub: sinon.SinonStub;

	let dummyUser: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userUpdateStub = sandbox.stub(User, 'update');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/v1/patient-timezone', function() {
		it('should fail if timezone is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient-timezone')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Timezone is required.');
		});

		it('should fail if timezone is invalid', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient-timezone')
				.set('Authorization', 'Bearer accessToken')
				.send({
					timezone: 'invalid-timezone',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid timezone. Please provide a valid IANA timezone format, e.g., "America/New_York".');
		});

		it('should update timezone successfully', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient-timezone')
				.set('Authorization', 'Bearer accessToken')
				.send({
					timezone: 'America/New_York',
				});
			
			expect(userUpdateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Timezone updated successfully.');
		});
	});
});