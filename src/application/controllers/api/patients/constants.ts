export const ERROR_MESSAGES = {
    AUTHENTICATION_REQUIRED: 'User not authenticated',
    USER_NOT_FOUND: 'User not found',
    BIO_TOO_SHORT: 'Bio must be within 150 characters',
};
export const USER_DELETE = 'User set as inactive and related data deleted successfully'
export const DOB_VALIDATION = 'Minor DOB must be under 18 years';
export const USER_ID_REQUIRED = 'User ID is required';
export const MINOR_DETAILS_REQUIRED = 'Minor details are required';
export const SUCCESS_ADD_MINORS = 'Successfully added minor patient(s)';
export const MINOR_ALREADY_EXISTS = 'Minor patient already exists';
export const INVALID_CREDENTIALS_MESSAGE = 'Invalid or missing flags: isGoogleCalendar, isMicrosoftCalendar, isAppleCalendar';
export const NO_CALENDAR_DATA = 'No calendar data found for the user';
export const UPDATED_PROFILE = "User profile updated successfully";
export const REASON_FOR_THERAPY_UPDATED = "Reason for therapy updated successfully";
export const REASON_FOR_THERAPY_REQUIRED = "Reason for therapy is required";
export const ERROR_UPDATING_REASON_FOR_THERAPY = "Error updating reason for therapy";
export const ERROR_REASON_LIMIT = "Reason must be 150 characters or fewer";