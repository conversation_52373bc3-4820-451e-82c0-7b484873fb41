import sinon from 'sinon';
import express from 'express';
import { Page, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import questionnaireGetRoute from '@/src/application/controllers/api/patients/questionnaire.get';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', questionnaireGetRoute);

describe('Fetch Patient Questionnaire and answers API Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let pageFindAllStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyPage: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		pageFindAllStub = sandbox.stub(Page, 'findAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
			patient_profile: {
				seekingTherapyFor: 'self',
			}
		}

		dummyPage = {
			id: 1,
			title: 'Dummy Page',
			category: 'self',
			sortOrder: 1,
			type: 'questionnaire',
			questionnaireType: 'choose',
			questionnaire: {
				id: 1,
				question: 'What is your main concern?',
				info: 'Please describe your main concern in detail.',
				user_answer: [{
					id: 1,
					pageId: 1,
					questionnaireId: 1,
					userId: 1,
					answerId: 1,
					points: 5,
					createdAt: new Date().toISOString(),
					updatedAt: new Date().toISOString(),
				}],
			},
			answers: [{
				id: 1,
				answer: 'Anxiety',
				info: 'Feeling anxious frequently',
				slug: 'anxiety',	
			}],
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/v1/patient/profile/questionnaire', function() {
		it('should return error if patient is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Patient not found');
		});

		it('should return error if patient profile is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				patient_profile: null,
			});
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Patient profile not found');
		});

		it('should return error if seekingTherapyFor is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				patient_profile: {
					seekingTherapyFor: null,
				},
			});
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Seeking therapy for not found');
		});

		it('should return patient questionnaire and answers successfully', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			pageFindAllStub.resolves([dummyPage]);
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('array');
		});
	});

	describe('GET /api/v1/patient/profile/questionnaire/:pagesId', function() {
		it('should return error if patient is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Patient not found');
		});

		it('should return error if patient profile is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				patient_profile: null,
			});
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Patient profile not found');
		});

		it('should return error if seekingTherapyFor is not found', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				patient_profile: {
					seekingTherapyFor: null,
				},
			});
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Seeking therapy for not found');
		});

		it('should return patient questionnaire and answers successfully', async () => {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			pageFindAllStub.resolves([dummyPage]);
	
			const response = await request(app)
				.get('/api/v1/patient/profile/questionnaire/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('array');
		});
	});
});