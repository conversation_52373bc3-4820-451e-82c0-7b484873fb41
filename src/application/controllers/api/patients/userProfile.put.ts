import express, { Request, Response } from 'express';
import { wrap } from '@/src/application/helpers';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import { User} from '@/src/models';
import multer from 'multer';
import AWS from 'aws-sdk';
import { generateString } from '../../../helpers/general.helper';
import { FileMimeType } from '../../../helpers/constant.helper';
import logger from '@/src/configs/logger.config';

AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY,
});

const s3 = new AWS.S3();
const storage = multer.memoryStorage();
const upload = multer({ storage: storage });

const router = express.Router();

/********************************
 * * Update Patient's Profile Picture
 ********************************/
router.put(
    '/patient/profile/upload',
    APIMiddleware,
    upload.single('file'),
    wrap(async (req: Request, res: Response) => {
        const userId = req.user?.id;

        if (!userId) {
            return res.status(400).send({ message: 'User not authenticated' });
        }
        logger.info(`User ${userId} requested to upload a profile picture`);

        let patient = await User.findOne({
            where: { id: userId },
        });

        if (!patient) {
            return res.status(404).send({ message: 'User not found' });
        }

        const currentProfile = patient.userProfile; // Fetch the current profile picture

        if (req.file) {
            const params = {
                Bucket: 'next-therapist',
                Key: `temp/${generateString(10)}.${FileMimeType[req.file.mimetype]}`,
                Body: req.file.buffer,
                ContentType: req.file.mimetype,
                ACL: 'public-read',
            };

            const response = await s3.upload(params).promise();

            if (currentProfile) {
                const deleteParams = {
                    Bucket: 'next-therapist',
                    Key: currentProfile,
                };

                try {
                    await s3.deleteObject(deleteParams).promise();
                    logger.info(`Deleted old profile picture from S3: ${currentProfile}`);
                } catch (error) {
                    logger.error(`Failed to delete old profile picture from S3: ${currentProfile}`, error);
                }
            }

            await User.update(
                { userProfile: response.Key },
                { where: { id: userId } }
            );
            logger.info(`User ${userId} successfully uploaded a new profile picture: ${response.Key}`);

            return res.send({ message: 'User profile updated successfully', userProfile: response.Key });
        } else {
            if (currentProfile) {
                const deleteParams = {
                    Bucket: 'next-therapist',
                    Key: currentProfile,
                };

                try {
                    await s3.deleteObject(deleteParams).promise();
                    logger.info(`Deleted profile picture from S3 as no new file was provided: ${currentProfile}`);
                } catch (error) {
                    logger.error(`Failed to delete profile picture from S3: ${currentProfile}`, error);
                }
            }

            // Clear the userProfile field in the database
            await User.update(
                { userProfile: null },
                { where: { id: userId } }
            );
            logger.info(`User ${userId} profile picture removed`);

            return res.send({ message: 'User profile picture removed successfully' });
        }
    })
);

export default router;
