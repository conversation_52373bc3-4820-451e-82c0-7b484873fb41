import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { UserType } from '@/src/application/helpers/constant.helper'
import { Answer, Page, Questionnaire, User, UserQuestionnaire, UserSettings } from '@/src/models'
import { NotFoundError } from '@/src/application/handlers/errors'

 
const router = express.Router()
 
/********************************
* * Get patient questionnaire and answers
********************************/
router.get(
    '/patient/profile/questionnaire',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const patient = await User.findOne({
            where: {
                id: req.user?.id,
                role: UserType.PATIENT,
            },
            include: ['patient_profile'],
        });
 
        if (!patient) throw new NotFoundError('Patient not found')
        if (!patient.patient_profile) throw new NotFoundError('Patient profile not found')
        else if (!patient.patient_profile.seekingTherapyFor) throw new NotFoundError('Seeking therapy for not found')
 
        const pages = await Page.findAll({
            where: {
                category: patient.patient_profile.seekingTherapyFor
            },
            include: [
                {
                    model: Questionnaire,
                    include: [
                        {
                            model: UserQuestionnaire,
                            as: 'user_answer',
                            where: {
                                "userId": patient.id
                            },
                            required: false,
                        }
                    ]
                },
                {
                    model: Answer,
                    order: [['ansOrder', 'ASC']]
                },
            ],
            order: [
                [ 'sortOrder' , "ASC" ]
            ]
        });
 
        // Process the pages and handle circular references
        const processedPages = pages
            .filter(page => !page.isFollowedThrough)
            .map(page => {
                // Extract only the needed properties from the page
                const processedPage: {
                    id: any,
                    title: string,
                    category: string,
                    sortOrder: number,
                    type: string,
                    questionnaireType: string,
                    questionnaire: {
                        id: any,
                        question: string,
                        info: string,
                        user_answer: any
                    } | null
                } = {
                    id: page.id,
                    title: page.title,
                    category: page.category,
                    sortOrder: page.sortOrder,
                    type: page.type,
                    questionnaireType: page.questionnaireType,
                    questionnaire: null,
                };
 
                if (page.questionnaire) {
                    // Get the answers map for quick lookup
                    const answersMap: { [key: number]: string } = page.answers.reduce((acc: { [key: number]: string }, ans: Answer) => {
                        acc[ans.id] = ans.answer;
                        return acc;
                    }, {});
 
                    // Process questionnaire and include answer text in user_answer
                    const questionnaireData = {
                        id: page.questionnaire.id,
                        question: page.questionnaire.question,
                        info: page.questionnaire.info,
                        user_answer: page.questionnaire.user_answer?.map(ua => ({
                            id: ua.id,
                            questionnaireId: ua.questionnaireId,
                            userId: ua.userId,
                            answerId: ua.answerId,
                            answer: answersMap[ua.answerId],  // Include the answer text here
                            points: ua.points,
                            createdAt: ua.createdAt,
                            updatedAt: ua.updatedAt
                        })) || []
                    };
 
                    processedPage.questionnaire = questionnaireData;
                }
 
                return processedPage;
            });
 
        return res.send(processedPages);
    })
)


/********************************
* * get patient questionnaire and answers for a specific page
********************************/
router.get(
    '/patient/profile/questionnaire/:pagesId',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const patient = await User.findOne({
            where: {
                id: req.user?.id,
                role: UserType.PATIENT,
            },
            include: ['patient_profile'],
        });
 
 
        if (!patient) throw new NotFoundError('Patient not found')
        if (!patient.patient_profile) throw new NotFoundError('Patient profile not found')
        else if (!patient.patient_profile.seekingTherapyFor) throw new NotFoundError('Seeking therapy for not found')
 
        const pages = await Page.findAll({
            where: {
                category: patient.patient_profile.seekingTherapyFor,
                id: req.params.pagesId
            },
            include: [
                {
                    model: Questionnaire,
                    include: [
                        {
                            model: UserQuestionnaire,
                            as: 'user_answer',
                            where: {
                                "userId": patient.id
                            },
                            required: false,
                        }
                    ]
                },
                {
                    model: Answer,
                    order: [['ansOrder', 'ASC']]
                },
            ],
            order: [
                [ 'sortOrder' , "ASC" ]
            ]
    })
 
        return res.send(pages);
    })
) 
export default router