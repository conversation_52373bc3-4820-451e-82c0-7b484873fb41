import sinon from 'sinon';
import express from 'express';
import { CalendarInvitePatient, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import { INVALID_CREDENTIALS_MESSAGE } from './constants'
import calendarInvitePostRoute from '@/src/application/controllers/api/patients/calendarInvite.post';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', calendarInvitePostRoute);

describe('Add Calendar Invite Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let calendarInvitePatientCreateStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyCalendarInvite: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		calendarInvitePatientCreateStub = sandbox.stub(CalendarInvitePatient, 'create');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}

		dummyCalendarInvite = {
			id: 1,
			userId: 1,
			dateTime: new Date(),
			appointmentId: 1,
			calendarType: 'google',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/v1/add-calendar-invite', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.post('/api/v1/add-calendar-invite')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Unauthorized);
			expect(response.body).to.have.property('message', INVALID_CREDENTIALS_MESSAGE);
		});

		it('should fail if id is not provided in request body', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/add-calendar-invite')
				.set('Authorization', 'Bearer accessToken')
				.send({
					dateTime: new Date(),
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', 'Missing required fields: id or dateTime');
		});

		it('should fail if dateTime is not provided in request body', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/add-calendar-invite')
				.set('Authorization', 'Bearer accessToken')
				.send({
					id: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', 'Missing required fields: id or dateTime');
		});

		it('should create calendar invite successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			calendarInvitePatientCreateStub.resolves(dummyCalendarInvite);

			const response = await request(app)
				.post('/api/v1/add-calendar-invite')
				.set('Authorization', 'Bearer accessToken')
				.send({
					id: 1,
					dateTime: new Date(),
					calendarType: 'google',
					appointmentId: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Calendar invite created successfully');
			expect(response.body).to.have.property('data');
		});
	});
});