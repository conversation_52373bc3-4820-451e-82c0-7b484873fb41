import sinon from 'sinon';
import express from 'express';
import { User, UserQuestionnaire } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import profilePutRoute from '@/src/application/controllers/api/patients/profile.put';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', profilePutRoute);

describe('Update Profile Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userQuestionnaireFindAllStub: sinon.SinonStub;
	let userQuestionnaireDestroyStub: sinon.SinonStub;
	let userQuestionnaireBulkCreateStub: sinon.SinonStub;

	let dummyUser: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userQuestionnaireFindAllStub = sandbox.stub(UserQuestionnaire, 'findAll');
		userQuestionnaireDestroyStub = sandbox.stub(UserQuestionnaire, 'destroy');
		userQuestionnaireBulkCreateStub = sandbox.stub(UserQuestionnaire, 'bulkCreate');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/v1/patient/profile', function() {
		it('should fail if questionnaireId is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. questionnaireId, pageId, and answers are required.');
		});

		it('should fail if pageId is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken')
				.send({
					questionnaireId: 1,
					answers: [1, 2, 3]
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. questionnaireId, pageId, and answers are required.');
		});

		it('should fail if answers are not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken')
				.send({
					questionnaireId: 1,
					pageId: 1
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. questionnaireId, pageId, and answers are required.');
		});

		it('should update questionnaire answers by adding and deleting correctly', async function () {
			userFindByPkStub.resolves(dummyUser);
			userQuestionnaireFindAllStub.resolves([
				{ answerId: 101 },
				{ answerId: 102 },
			]);
	
			const response = await request(app)
				.put('/api/v1/patient/profile')
				.set('Authorization', 'Bearer accessToken')
				.send({
					questionnaireId: 10,
					pageId: 5,
					answers: [102, 103] // 101 will be deleted, 103 added
				});

			expect(userQuestionnaireFindAllStub.calledOnce).to.be.true;
			expect(userQuestionnaireDestroyStub.calledOnce).to.be.true;
			expect(userQuestionnaireBulkCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body.message).to.equal('Successfully updated profile');
			expect(response.body.stats).to.deep.equal({
				created: 1,
				deleted: 1,
				unchanged: 1
			});
		});
	});
});