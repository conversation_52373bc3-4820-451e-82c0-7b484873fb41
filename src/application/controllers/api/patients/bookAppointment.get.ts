import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginated, paginatedData } from '@/src/application/helpers/pagination.helper'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { UserType } from '@/src/application/helpers/constant.helper'
import { User,Appointments, UserRegistrationInfo , TherapistProfile,MinorPatient, TherapistSubscription } from '@/src/models'
import { Op, Sequelize } from 'sequelize'
import logger from '@/src/configs/logger.config'

const router = express.Router();

/********************************
 * * Get patients appointments
 ********************************/
router.get(
    '/events/scheduled/:id',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
      const appointmentId = req.params.id;
      const filter = req.query.filter as string;
      const now = new Date();
      const todayDate = now.toISOString().split('T')[0];
      const currentTime = now.toTimeString().split(' ')[0];

      const appointment = await Appointments.findOne({
        where: { id: appointmentId },
      });

      if (!appointment) {
        return res.status(404).send({ message: 'Appointment not found' });
      }

      const patientId = appointment.patientId;

      const whereCondition: any = {
        id: appointmentId,
        patientId,
      };

      logger.info(`Fetching related appointments, Appointment ID : ${ appointmentId} and Patient ID: ${patientId} by User ID:${req.user?.id}`);

        const appointments = await Appointments.findAll({
            where: whereCondition,
            include: [
                {
                    model: UserRegistrationInfo,
                    as: 'profile',
                    where: { pageName: 'profile' },
                    attributes: {
                        exclude: ['payloadInfo'],
                        include: [
                            [
                                Sequelize.literal(`(
                                    SELECT json_build_object(
                                        'lat', ("payloadInfo"->'business_address'->>'lat')::float,
                                        'lng', ("payloadInfo"->'business_address'->>'lng')::float,
                                        'city', "payloadInfo"->'business_address'->>'city',
                                        'state', "payloadInfo"->'business_address'->>'state',
                                        'street', "payloadInfo"->'business_address'->>'street',
                                        'country', "payloadInfo"->'business_address'->>'country',
                                        'zipCode', "payloadInfo"->'business_address'->>'zipCode',
                                        'place_id', "payloadInfo"->'business_address'->>'place_id',
                                        'description', "payloadInfo"->'business_address'->>'description',
                                        'full_address', "payloadInfo"->'business_address'->>'full_address'
                                    )
                                    FROM "user_registration_informations"
                                    WHERE "user_registration_informations"."userId" = "therapist"."id"
                                    AND "user_registration_informations"."pageName" = 'practice-info'
                                    LIMIT 1
                                )`),
                                'address',
                            ],
                            [
                                Sequelize.literal(`(
                                    SELECT "payloadInfo"->>'session_fee'
                                    FROM "user_registration_informations"
                                    WHERE "user_registration_informations"."userId" = "therapist"."id"
                                    AND "user_registration_informations"."pageName" = 'payment-forms'
                                    LIMIT 1
                                )`),
                                'session_fee',
                            ],
                            [
                                Sequelize.literal(`(
                                    SELECT "payloadInfo"->>'appointmentMethod'
                                    FROM "user_registration_informations"
                                    WHERE "user_registration_informations"."userId" = "therapist"."id"
                                    AND "user_registration_informations"."pageName" = 'normal-office-hours'
                                    LIMIT 1
                                )`),
                                'telehealth'
                        
                            ],

                        ],
                    },
                },
                {
                    model: User,
                    as: 'therapist',
                    attributes: ['firstname', 'lastname', 'dob', 'email', 'userProfile'],
                    required: true,
                    include: [
                        {
                            model: TherapistSubscription,
                            where: {
                                isActive: true,
                            },
                            attributes: ['isActive'],
                            required: false,
                        },
                    ],
                },
                {
                    model: MinorPatient,
                    as: 'minorPatient',
                    attributes: ['firstName', 'lastName'],
                    required: false,
                },
            ],
            order: [
                ['appointmentDate', 'ASC'],
            ],
        });

      logger.info('Transforming appointments', { count: appointments.length });
        const transformedAppointments = appointments.map(appointment => {
            const { profile, therapist, minorPatient, ...rest } = appointment.get({ plain: true });
           
           let isSubscriptionActive = Array.isArray(therapist.subscription)
            ? therapist.subscription[0]?.isActive ?? false
            : therapist.subscription?.isActive ?? false;

            return {
                ...rest,
                isMinor: appointment.isMinor,
                therapist: {
                    ...profile,
                    firstName: therapist.firstname,
                    lastName: therapist.lastname,
                    dob: therapist.dob,
                    email: therapist.email,
                    user_profile: therapist.userProfile,
                    isSubscriptionActive
                },
                minorPatient: appointment.isMinor ? {
                    firstName: minorPatient?.firstName,
                    lastName: minorPatient?.lastName,
                } : null,
            };
        });

      res.json(transformedAppointments);
    })
);

export default router;