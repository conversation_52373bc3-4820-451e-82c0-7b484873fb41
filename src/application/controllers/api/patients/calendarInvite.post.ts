import { Router, Request, Response } from 'express';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import { wrap } from '@/src/application/helpers';
import CalendarInvitePatient from '../../../../models/calendar_invite_patient.model';
import { INVALID_CREDENTIALS_MESSAGE } from './constants';

const router = Router();

router.post(
  '/add-calendar-invite',
  APIMiddleware,
  wrap(async (req: Request, res: Response) => {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ message: INVALID_CREDENTIALS_MESSAGE });
    }

    const { id, dateTime, calendarType,appointmentId } = req.body;

    if (!id || !dateTime) {
      return res.status(400).json({ message: 'Missing required fields: id or dateTime' });
    }

    const invite = await CalendarInvitePatient.create({
      userId,
      calendarId: id,
      dateTime,
      appointmentId,
      calendarType: calendarType || null,
    });

    return res.status(201).json({
      message: 'Calendar invite created successfully',
      data: invite,
    });
  })
);

export default router;
