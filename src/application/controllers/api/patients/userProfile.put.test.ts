import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import userProfilePutRoute from '@/src/application/controllers/api/patients/userProfile.put';
import AWS from 'aws-sdk';
import * as helperUtils from '@/src/application/helpers/general.helper';
import { FileMimeType } from '@/src/application/helpers/constant.helper';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', userProfilePutRoute);

describe('Update User Profile Picture Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let userUpdateStub: sinon.SinonStub;

	let s3UploadStub: sinon.SinonStub;
	let s3DeleteStub: sinon.SinonStub;

	let dummyUser: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		userUpdateStub = sandbox.stub(User, 'update').resolves();

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		s3UploadStub = sandbox.stub(AWS.S3.prototype, 'upload').returns({
			promise: () =>
				Promise.resolve({
					Location: 'https://bucket.s3.amazonaws.com/temp/testfile.png',
					Key: 'temp/testfile.png',
					Bucket: 'next-therapist',
				}),
		} as any);

		// s3DeleteStub = sandbox.stub(AWS.S3.prototype, 'deleteObject').returns({
		// 	promise: () => Promise.resolve(),
		// } as any);

		sandbox.stub(helperUtils, 'generateString').returns('testfile');

		FileMimeType['image/png'] = 'png';

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
			userProfile: null,
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/v1/patient/profile/upload', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.put('/api/v1/patient/profile/upload')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', 'User not authenticated');
		});

		it('should fail if patient is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.put('/api/v1/patient/profile/upload')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should update user profile picture successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/profile/upload	')
				.set('Authorization', 'Bearer accessToken')
				.attach('file', Buffer.from('test file content'), {
					filename: 'example.png',
					contentType: 'image/png',
				});
			
			expect(s3UploadStub.calledOnce).to.be.true;
			// expect(s3DeleteStub.calledOnce).to.be.true;
			expect(userUpdateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'User profile updated successfully');
			expect(response.body).to.have.property('userProfile', 'temp/testfile.png');
		});

		it('should remove user profile picture successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.put('/api/v1/patient/profile/upload	')
				.set('Authorization', 'Bearer accessToken');
			
			expect(s3UploadStub.notCalled).to.be.true;
			// expect(s3DeleteStub.calledOnce).to.be.true;
			expect(userUpdateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'User profile picture removed successfully');
		});
	});
});