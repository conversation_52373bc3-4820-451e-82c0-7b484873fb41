// import { expect } from 'chai';
// import request from 'supertest';
// import sinon from 'sinon';
// import express from 'express';
// import { User } from '@/src/models';
// import { UserType } from '@/src/application/helpers/constant.helper';
// import logger from '@/src/configs/logger.config';

// // Define test constants
// const TEST_USER_ID = '123';
// const TEST_EMAIL = '<EMAIL>';
// const TEST_THERAPIST_ID = '456';

// // Mock User model instance
// const mockPatient = {
//   id: TEST_USER_ID,
//   email: TEST_EMAIL,
//   firstname: '<PERSON>',
//   lastname: '<PERSON><PERSON>',
//   emailVerifiedAt: new Date(),
//   role: UserType.PATIENT,
//   patient_profile: {
//     id: '789',
//     userId: TEST_USER_ID,
//     seekingTherapyFor: 'self',
//     dateOfBirth: '1990-01-01'
//   }
// } as any;

// const mockTherapist = {
//   id: TEST_THERAPIST_ID,
//   email: '<EMAIL>',
//   firstname: '<PERSON>',
//   lastname: 'Smith',
//   emailVerifiedAt: new Date(),
//   acceptedAt: new Date(),
//   role: UserType.THERAPIST
// } as any;

// // Create API middleware
// const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
// const originalApiMiddleware = require(apiMiddlewarePath).default;

// // Mock the middleware
// const apiMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
//   // Set up user for the request
//   req.user = mockPatient;
//   next();
// };

// describe('Patients Router Integration', function() {
//   this.timeout(5000);
  
//   let sandbox: sinon.SinonSandbox;
//   let app: express.Express;
//   let userFindByPkStub: sinon.SinonStub;
//   let loggerInfoStub: sinon.SinonStub;
//   let loggerErrorStub: sinon.SinonStub;

//   beforeEach(function() {
//     sandbox = sinon.createSandbox();
    
//     // Override the middleware
//     require(apiMiddlewarePath).default = apiMiddleware;
    
//     // Model stubs
//     userFindByPkStub = sandbox.stub(User, 'findByPk');
//     userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
//     userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
    
//     // Logger stubs
//     loggerInfoStub = sandbox.stub(logger, 'info');
//     loggerErrorStub = sandbox.stub(logger, 'error');
    
//     // Create a fresh app for each test
//     app = express();
//     app.use(express.json());
//     // Import the patients router directly to avoid path resolution issues
//     const patientsRouter = require('@/src/application/controllers/api/patients/index').default;
//     app.use('/api/patients', patientsRouter);
//   });

//   afterEach(function() {
//     sandbox.restore();
//     // Restore original middleware
//     require(apiMiddlewarePath).default = originalApiMiddleware;
//   });

//   describe('Router Configuration', function() {
//     it('should properly mount the patients.get route', async function() {
//       // Test the patients.get route is properly mounted
//       const response = await request(app)
//         .get('/api/patients');
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the profile.get route', async function() {
//       // Test the profile.get route is properly mounted
//       const response = await request(app)
//         .get('/api/patients/profile');
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the questionnaire.get route', async function() {
//       // Test the questionnaire.get route is properly mounted
//       const response = await request(app)
//         .get('/api/patients/questionnaire');
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the profile.put route', async function() {
//       // Test the profile.put route is properly mounted
//       const response = await request(app)
//         .put('/api/patients/profile')
//         .send({ firstname: 'Updated' });
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the patients.put route', async function() {
//       // Test the patients.put route is properly mounted
//       const response = await request(app)
//         .put('/api/patients')
//         .send({ firstname: 'Updated' });
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the userProfile.put route', async function() {
//       // Test the userProfile.put route is properly mounted
//       const response = await request(app)
//         .put('/api/patients/profile-picture')
//         .send({ profilePicture: 'image-url' });
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the bookAppointment.get route', async function() {
//       // Test the bookAppointment.get route is properly mounted
//       const response = await request(app)
//         .get('/api/patients/book-appointment');
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the removePatient route', async function() {
//       // Test the removePatient route is properly mounted
//       const response = await request(app)
//         .delete('/api/patients');
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the patient-timezone.put route', async function() {
//       // Test the patient-timezone.put route is properly mounted
//       const response = await request(app)
//         .put('/api/patients/timezone')
//         .send({ timezone: 'America/New_York' });
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the minorPatient.put route', async function() {
//       // Test the minorPatient.put route is properly mounted
//       const response = await request(app)
//         .put('/api/patients/minor')
//         .send({ minorId: '123', firstname: 'Minor' });
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should properly mount the calendarInvite.post route', async function() {
//       // Test the calendarInvite.post route is properly mounted
//       const response = await request(app)
//         .post('/api/patients/calendar-invite')
//         .send({ appointmentId: '123' });
      
//       // Assert the route is accessible
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should respond with 404 for non-existent routes', async function() {
//       // Test a route that doesn't exist
//       const response = await request(app)
//         .get('/api/patients/non-existent-route');
      
//       // Assert
//       expect(response.status).to.equal(404);
//     });
//   });

//   describe('Express App Configuration', function() {
//     it('should use JSON middleware', async function() {
//       // Send a request with a JSON body
//       const response = await request(app)
//         .put('/api/patients/profile')
//         .send({ firstname: 'Updated' });
      
//       // If JSON middleware is working, the request body should be parsed correctly
//       expect(response.status).to.be.oneOf([200, 400, 401, 403, 404, 500]);
//     });
    
//     it('should handle errors properly', async function() {
//       // Force an error by making the User.findByPk throw
//       userFindByPkStub.throws(new Error('Forced database error'));
      
//       // Send a request that will trigger the error
//       const response = await request(app)
//         .get('/api/patients');
      
//       // Assert - if the route is not found, it will return 404 instead of 500
//       expect(response.status).to.be.oneOf([404, 500]);
//       // Only check logger if status is 500
//       if (response.status === 500) {
//         expect(loggerErrorStub.called).to.be.true;
//       }
//     });
//   });

//   describe('Authentication and Authorization', function() {
//     it('should use the API middleware for authentication', async function() {
//       // Create a new app with a modified middleware that simulates no authentication
//       const unauthenticatedMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
//         // No user set on request
//         next();
//       };
      
//       // Override the middleware temporarily
//       require(apiMiddlewarePath).default = unauthenticatedMiddleware;
      
//       // Create a fresh app
//       const unauthApp = express();
//       unauthApp.use(express.json());
//       unauthApp.use('/api/patients', require('@/src/application/controllers/api/patients').default);
      
//       // Test a route
//       const response = await request(unauthApp)
//         .get('/api/patients/profile');
      
//       // Assert that authentication is required or route not found
//       expect(response.status).to.be.oneOf([401, 403, 404]);
      
//       // Restore the test middleware
//       require(apiMiddlewarePath).default = apiMiddleware;
//     });
    
//     it('should restrict access to patient-only routes for non-patients', async function() {
//       // Create a new app with a modified middleware that simulates a therapist user
//       const therapistMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
//         req.user = mockTherapist;
//         next();
//       };
      
//       // Override the middleware temporarily
//       require(apiMiddlewarePath).default = therapistMiddleware;
      
//       // Create a fresh app
//       const therapistApp = express();
//       therapistApp.use(express.json());
//       therapistApp.use('/api/patients', require('@/src/application/controllers/api/patients').default);
      
//       // Test a patient-only route
//       const response = await request(therapistApp)
//         .get('/api/patients/profile');
      
//       // Assert that authorization is enforced or route not found
//       expect(response.status).to.be.oneOf([401, 403, 404]);
      
//       // Restore the test middleware
//       require(apiMiddlewarePath).default = apiMiddleware;
//     });
//   });
// });
