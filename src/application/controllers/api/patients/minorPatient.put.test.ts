import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { MinorPatient, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import { DOB_VALIDATION, ERROR_MESSAGES, UPDATED_PROFILE } from './constants';
import minorPatientPutRoute from '@/src/application/controllers/api/patients/minorPatient.put';
import dayjs from 'dayjs';
import AWS from 'aws-sdk';
import * as helperUtils from '@/src/application/helpers/general.helper';
import { FileMimeType } from '@/src/application/helpers/constant.helper';

// AWS.config.update({
// 	accessKeyId: process.env.AWS_ACCESS_KEY,
// 	secretAccessKey: process.env.AWS_SECRET_KEY
// });
// const s3 = new AWS.S3();

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', minorPatientPutRoute);

describe('Update Minor Patient Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let minorPatientFindOneStub: sinon.SinonStub;
	let minorPatientUpdateStub: sinon.SinonStub;

	let s3UploadStub: sinon.SinonStub;
	let s3DeleteStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyMinorPatient: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		minorPatientFindOneStub = sandbox.stub(MinorPatient, 'findOne');
		minorPatientUpdateStub = sandbox.stub(MinorPatient, 'update');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		// s3UploadStub = sandbox.stub(s3, 'upload').returns({
		// 	promise: () =>
		// 		Promise.resolve({
		// 			Location: 'https://bucket.s3.amazonaws.com/temp/testfile.png',
		// 			Key: 'temp/testfile.png',
		// 			Bucket: 'next-therapist',
		// 		}),
		// } as any);
		s3UploadStub = sandbox.stub(AWS.S3.prototype, 'upload').returns({
			promise: () =>
				Promise.resolve({
					Location: 'https://bucket.s3.amazonaws.com/temp/testfile.png',
					Key: 'temp/testfile.png',
					Bucket: 'next-therapist',
				}),
		} as any);

		// s3DeleteStub = sandbox.stub(AWS.S3.prototype, 'deleteObject').returns({
		// 	promise: () => Promise.resolve(),
		// } as any);

		sandbox.stub(helperUtils, 'generateString').returns('testfile');

		FileMimeType['image/png'] = 'png';

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}

		dummyMinorPatient = {
			id: 1,
			userId: 1,
			firstName: 'John',
			lastName: 'Doe',
			// userProfile: 'temp/profile.png',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/v1/update/minorPatient', function() {
		it('should fail if request user id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: null,
			});

			const response = await request(app)
				.put('/api/v1/update/minorPatient')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.AUTHENTICATION_REQUIRED);
		});

		it('should fail minor patient is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.resolves(null);

			const response = await request(app)
				.put('/api/v1/update/minorPatient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					id: 1
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.USER_NOT_FOUND);
		});

		it('should update minor patient successfully and delete old profile picture', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.resolves(dummyMinorPatient);

			const response = await request(app)
				.put('/api/v1/update/minorPatient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					id: 1,
					firstName: 'Jane',
					lastName: 'Doe',
					dob: dayjs().subtract(10, 'year').format('YYYY-MM-DD'),
				});
			
			expect(s3UploadStub.notCalled).to.be.true;
			// expect(s3DeleteStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', UPDATED_PROFILE);
			expect(response.body).to.have.property('minorPatient');
		});

		it('should fail if updated minor patient is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.onFirstCall().resolves(dummyMinorPatient);
			minorPatientFindOneStub.onSecondCall().resolves(null);

			const response = await request(app)
				.put('/api/v1/update/minorPatient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					id: 1,
					firstName: 'Jane',
					lastName: 'Doe',
					dob: dayjs().subtract(10, 'year').format('YYYY-MM-DD'),
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', ERROR_MESSAGES.USER_NOT_FOUND);
		});

		it('should fail if minor patient is more than 18 years old', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.resolves(dummyMinorPatient);

			const response = await request(app)
				.put('/api/v1/update/minorPatient')
				.set('Authorization', 'Bearer accessToken')
				.send({
					id: 1,
					firstName: 'Jane',
					lastName: 'Doe',
					dob: dayjs().subtract(20, 'year').format('YYYY-MM-DD'),
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', DOB_VALIDATION);
		});

		it('should update minor patient successfully and upload new profile picture', async function () {
			userFindByPkStub.resolves(dummyUser);
			minorPatientFindOneStub.resolves(dummyMinorPatient);

			const response = await request(app)
				.put('/api/v1/update/minorPatient')
				.set('Authorization', 'Bearer accessToken')
				.attach('file', Buffer.from('test file content'), {
					filename: 'example.png',
					contentType: 'image/png',
				})
				.field('id', 1)
				.field('firstName', 'Jane')
				.field('lastName', 'Doe')
				.field('dob', dayjs().subtract(10, 'year').format('YYYY-MM-DD'));
			
			expect(s3UploadStub.calledOnce).to.be.true;
			// expect(s3DeleteStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', UPDATED_PROFILE);
			expect(response.body).to.have.property('minorPatient');
		});
	});
});