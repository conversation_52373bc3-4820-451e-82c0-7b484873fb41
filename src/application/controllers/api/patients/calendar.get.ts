import { Router, Request, Response } from 'express';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import { wrap } from '@/src/application/helpers';
import CalendarPatient from '@/src/models/calendar-patient.model';
import { USER_ID_REQUIRED,NO_CALENDAR_DATA } from './constants';

const router = Router();

router.get(
    '/calendar-patient',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const id = req.user?.id;
        const { appointmentId } = req.query;

        if (!id) {
            return res.status(400).send({ USER_ID_REQUIRED });
        }

        const parsedAppointmentId = typeof appointmentId === 'string' ? parseInt(appointmentId, 10) : undefined;

        if (!parsedAppointmentId || isNaN(parsedAppointmentId)) {
            return res.status(400).send({ error: 'Invalid appointmentId' });
        }

        const calendarData = await CalendarPatient.findOne({
            where: { appointmentId: parsedAppointmentId },
        });

        if (!calendarData) {
            return res.status(404).send({ NO_CALENDAR_DATA });
        }

        return res.status(200).send(calendarData);
    })
);

export default router;