import sinon from 'sinon';
import express from 'express';
import { User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import patientsGetRoute from '@/src/application/controllers/api/patients/patients.get';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', patientsGetRoute);

describe('Patients List Fetch API Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindAndCountAllStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyPatient: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindAndCountAllStub = sandbox.stub(User, 'findAndCountAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}

		dummyPatient = {
			id: 1,
			firstname: 'John',
			lastname: 'Doe',
			role: 'patient',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/v1/patients', function() {
		it('should fail if request user role is patient', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'patient',
			});

			const response = await request(app)
				.get('/api/v1/patients')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'You are not allowed to access this resource.');
		});

		it('should return patients list successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindAndCountAllStub.resolves({
				count: 2,
				rows: [dummyPatient, dummyPatient],
			});

			const response = await request(app)
				.get('/api/v1/patients')
				.set('Authorization', 'Bearer accessToken')
				.query({
					search: 'john',
					page: 1,
					perPage: 10,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').to.be.an('array').of.length(2);
			expect(response.body).to.have.property('meta').to.be.an('object').to.have.property('total', 2);
		});
	});
});