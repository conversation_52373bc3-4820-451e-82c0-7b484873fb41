import { Router, Request, Response } from 'express';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import { wrap } from '@/src/application/helpers';
import CalendarPatient from '@/src/models/calendar-patient.model';
import {INVALID_CREDENTIALS_MESSAGE} from './constants'

const router = Router();

router.put(
    '/calendar-patient',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const id = req.user?.id;

        const { isGoogleCalendar, isMicrosoftCalendar, isAppleCalendar,appointmentId } = req.body;

        if (
            typeof isGoogleCalendar !== 'boolean' ||
            typeof isMicrosoftCalendar !== 'boolean' ||
            typeof isAppleCalendar !== 'boolean'
        ) {
            return res.status(400).json({INVALID_CREDENTIALS_MESSAGE});
        }

        const existingRecord = await CalendarPatient.findOne({
            where: { appointmentId: appointmentId },
        });

        if (existingRecord) {
            await existingRecord.update({
                isGoogleCalendar,
                isMicrosoftCalendar,
                isAppleCalendar,
            });

            return res.status(200).json({
                data: existingRecord,
            });
        } else {
            const newEntry = await CalendarPatient.create({
                userId: id,
                isGoogleCalendar,
                isMicrosoftCalendar,
                isAppleCalendar,
                appointmentId
            });

            return res.status(201).json({
                data: newEntry,
            });
        }
    })
);

export default router;