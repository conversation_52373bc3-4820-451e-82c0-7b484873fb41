import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import stripe from '@/src/configs/stripe-new.config'
import { HttpStatusCode } from 'axios'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { User } from '@/src/models'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Create Stripe Connect Account and Account Link and return Redirect URL
 ********************************/
router.get(
	'/therapist-payment/stripe/connect',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const user = req.user as User;

		if (user.role !== 'therapist') {
			return res.forbidden('Invalid request.');
		}

		let stripeConnectAccount;
		if (user.stripeConnectAccountId) {
			try {
				stripeConnectAccount = await stripe.accounts.retrieve(user.stripeConnectAccountId);
			} catch (error) {
				logger.error(`User ${user.id} - Error retrieving Stripe Connect Account: ${error}`);
			}
		}

		if (!stripeConnectAccount) {
			logger.info(`User ${user.id} - Creating new Stripe Connect Account`);
			stripeConnectAccount = await stripe.accounts.create({
				type: 'express',
				email: user.email,
				capabilities: {
					transfers: {
						requested: true,
					},
				},
				metadata: {
					userId: user.id,
				},
			});

			logger.info(`User ${user.id} - Stripe Connect Account created successfully`);
			await user.update({
				stripeConnectAccountId: stripeConnectAccount.id,
			});
		}

		const accountLink = await stripe.accountLinks.create({
			account: stripeConnectAccount.id,
			type: 'account_onboarding',
			refresh_url: `${process.env.FRONTEND_URL}/reporting`,
			return_url: `${process.env.FRONTEND_URL}/reporting`,
		});

		return res.status(HttpStatusCode.Ok).send({
			redirectUrl: accountLink.url,
		});
	})
)

export default router
