import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistPaymentRoutes from '@/src/application/controllers/api/therapist-payment';
import jwt from 'jsonwebtoken';
import stripe from '@/src/configs/stripe-new.config';
import { User } from '@/src/models';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', therapistPaymentRoutes);

describe('Therapist Payment API Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;

	let stripeAccountsRetrieveStub: sinon.SinonStub;
	let stripeAccountsCreateStub: sinon.SinonStub;
	let stripeAccountLinksCreateStub: sinon.SinonStub;

	let dummyUser = {
		id: 1,
		email: '<EMAIL>',
		role: 'therapist',
		stripeConnectAccountId: 'acct_123456',
		update: sinon.stub().resolves(),
	}

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');

		stripeAccountsRetrieveStub = sandbox.stub(stripe.accounts, 'retrieve');
		stripeAccountsCreateStub = sandbox.stub(stripe.accounts, 'create');
		stripeAccountLinksCreateStub = sandbox.stub(stripe.accountLinks, 'create');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/therapist-payment/stripe/connect', function() {
		it('should return error if user is not a therapist', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'patient',
			});

			const response = await request(app)
				.get('/api/therapist-payment/stripe/connect')
				.set('Authorization', 'Bearer accessToken');
			
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request.');
		});

		it('should return redirect url to stripe connect without creating new account', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeAccountsRetrieveStub.resolves({
				id: 'acct_123456',
			});
			stripeAccountLinksCreateStub.resolves({
				url: 'https://stripe.com',
			});

			const response = await request(app)
				.get('/api/therapist-payment/stripe/connect')
				.set('Authorization', 'Bearer accessToken');
			
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('redirectUrl');
		});

		it('should return redirect url to stripe connect by creating new account', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				stripeConnectAccountId: null,
			});
			stripeAccountsCreateStub.resolves({
				id: 'acct_123456',
			});
			stripeAccountLinksCreateStub.resolves({
				url: 'https://stripe.com',
			});

			const response = await request(app)
				.get('/api/therapist-payment/stripe/connect')
				.set('Authorization', 'Bearer accessToken');
			
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('redirectUrl');
		});
	});
});