import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import UserSettings from '@/src/models/user-settings.model'
import { HttpStatusCode } from 'axios'
import { ForbiddenError } from '../../../handlers/errors'
import { User } from '@/src/models'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import logger from '@/src/configs/logger.config'
import { getUserWithCalendarInfo } from '@/src/application/repositories/user.repository'

const router = express.Router()

/**
 * Create user settings
 */
router.put(
	'/user-settings',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { notificationSettings } = req.body;
		const user_id = req.user?.id;

		logger.info(`User ${user_id} requested to upsert notification settings`);

		const existingSettings = await UserSettings.findOne({
			where: { userId: user_id },
		});

		if (existingSettings) {
			await UserSettings.update(
				{ notificationSettings },
				{ where: { userId: user_id } }
			);
			logger.info(`User ${user_id} updated existing notification settings`);
		} else {
			await UserSettings.create({
				userId: user_id,
				notificationSettings,
			});
			logger.info(`User ${user_id} created new notification settings`);
		}

		return res.status(HttpStatusCode.Ok).send('Settings saved successfully');
	})
);


/********************************
 * * Enable / Disable MFA
 ********************************/
router.put(
	'/user-settings/mfa',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
    logger.info(`User ID ${req.user?.id} initiated MFA enable/disable request.`);
		const { enable } = req.body;
    if (typeof enable !== 'boolean') {
      logger.info(`User ID ${req.user?.id} attempted to enable/disable MFA with invalid request body. The 'enable' value is not a boolean.`);
      return res.forbidden("Invalid request. Please provide a valid value for 'enable'.");
    }

    logger.info(`${enable ? 'Enabling' : 'Disabling'} MFA for user ID ${req.user?.id}`);

    await User.update(
      { mfaEnabled: enable },
      { where: { id: req.user?.id } }
    );
    logger.info(`MFA ${enable ? 'enabled' : 'disabled'} for user ID ${req.user?.id}`);

    const userData = await getUserWithCalendarInfo(req.user?.id)
    return res.success(`${enable ? 'Enabled' : 'Disabled'} MFA successfully.`, { user: userData });
	})
)

export default router
