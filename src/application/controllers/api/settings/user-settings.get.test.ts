import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import { User, UserSettings } from '@/src/models';
import logger from '@/src/configs/logger.config';
import { UserType } from '@/src/application/helpers/constant.helper';
import jwt from 'jsonwebtoken';

// Mock user data
const mockUser = {
  id: '123',
  email: '<EMAIL>',
  role: UserType.PATIENT,
  version: 1
} as any;

// Mock user settings
const mockUserSettings = {
  id: '456',
  userId: mockUser.id,
  notificationSettings: {
    emailNotifications: true,
    smsNotifications: false
  }
};

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Controller path
const controllerPath = '@/src/application/controllers/api/settings/user-settings.get';

describe('API User Settings Get Controller', function() {
  let sandbox: sinon.SinonSandbox;
  let userSettingsFindOneStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let userFindByPkStub: sinon.SinonStub;

  before(function() {
    // Create custom middleware that will be used instead of the real API middleware
    const customMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
      req.user = mockUser;
      req.role = mockUser.role;
      next();
    };

    // Replace the real middleware with our custom one
    const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
    const originalApiMiddleware = require(apiMiddlewarePath).default;
    require(apiMiddlewarePath).default = customMiddleware;

    // Set up the routes with our middleware
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userSettingsFindOneStub = sandbox.stub(UserSettings, 'findOne');
    
    // JWT and User stubs
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'api',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockUser);
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
    require.cache[apiMiddlewarePath] = undefined;
  });

  describe('GET /user-settings', function() {
    it('should return user settings successfully', async function() {
      // Setup
      userSettingsFindOneStub.resolves(mockUserSettings);
      
      // Execute
      const response = await request(app)
        .get('/api/user-settings')
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.deep.equal(mockUserSettings);
      expect(userSettingsFindOneStub.calledOnce).to.be.true;
      expect(userSettingsFindOneStub.firstCall.args[0]).to.deep.include({
        where: {
          userId: mockUser.id
        }
      });
    });

    it('should return 404 when user settings not found', async function() {
      // Setup
      userSettingsFindOneStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .get('/api/user-settings')
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.NotFound);
      expect(response.body).to.have.property('message').that.equals('User Settings not found');
    });

    it('should handle errors properly', async function() {
      // Setup
      userSettingsFindOneStub.rejects(new Error('Database error'));
      
      // Execute
      const response = await request(app)
        .get('/api/user-settings')
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.InternalServerError);
    });

    it('should return 401 when no authorization header is provided', async function() {
      const response = await request(app)
        .get('/api/user-settings');

      expect(response.status).to.equal(HttpStatusCode.Unauthorized);
    });
  });
});
