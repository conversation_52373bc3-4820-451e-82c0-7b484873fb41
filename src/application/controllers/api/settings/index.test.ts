import { expect } from 'chai';
import indexApp from './index';

describe('Settings API Router', function() {
  it('should export an express app', function() {
    expect(indexApp).to.exist;
    expect(typeof indexApp).to.equal('function');
    expect(indexApp).to.have.property('_router');
  });
  
  it('should have registered the correct routes', function() {
    const router = (indexApp as any)._router;
    expect(router).to.exist;
    expect(router).to.have.property('stack').that.is.an('array');
    
    const routerLayers = router.stack.filter((layer: any) => layer.name === 'router');
    expect(routerLayers.length).to.be.at.least(2);
    expect(indexApp).to.exist;
  });
});
