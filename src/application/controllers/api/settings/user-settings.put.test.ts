import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { User, UserSettings } from '@/src/models';
import { HttpStatusCode } from 'axios';
import { UserType } from '@/src/application/helpers/constant.helper';
import logger from '@/src/configs/logger.config';
import { ForbiddenError } from '@/src/application/handlers/errors';
import * as UserRepository from '@/src/application/repositories/user.repository';

// Define test constants
const TEST_USER_ID = '123';
const TEST_EMAIL = '<EMAIL>';

// Mock User model instance
const mockUser = {
  id: TEST_USER_ID,
  email: TEST_EMAIL,
  firstname: '<PERSON>',
  lastname: 'Doe',
  emailVerifiedAt: new Date(),
  role: UserType.PATIENT,
  mfaEnabled: false
} as any;

// Mock UserSettings model instance
const mockUserSettings = {
  id: 1,
  userId: TEST_USER_ID,
  notificationSettings: {
    email: true,
    sms: false
  },
  createdAt: new Date(),
  updatedAt: new Date()
} as any;

// Mock user data with calendar info
const mockUserWithCalendarInfo = {
  id: TEST_USER_ID,
  email: TEST_EMAIL,
  firstname: 'John',
  lastname: 'Doe',
  mfaEnabled: true
};

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Import jwt for authentication mocking
import jwt from 'jsonwebtoken';

// Path to the API middleware
const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';

// Create the app instance and set up middleware
const app = express();
app.use(express.json());

// Controller path - will be required in before() hook
const controllerPath = '@/src/application/controllers/api/settings/user-settings.put';

describe('API User Settings Put Controller', function() {
  this.timeout(5000);

  let sandbox: sinon.SinonSandbox;
  let userSettingsFindOneStub: sinon.SinonStub;
  let userSettingsUpdateStub: sinon.SinonStub;
  let userSettingsCreateStub: sinon.SinonStub;
  let userUpdateStub: sinon.SinonStub;
  let getUserWithCalendarInfoStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;

  before(function() {
    // Create custom middleware that will be used instead of the real API middleware
    const customMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
      req.user = mockUser;
      req.role = mockUser.role;
      next();
    };

    // Replace the real middleware with our custom one
    const originalApiMiddleware = require(apiMiddlewarePath).default;
    require(apiMiddlewarePath).default = customMiddleware;

    // Set up the routes with our middleware
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userSettingsFindOneStub = sandbox.stub(UserSettings, 'findOne');
    userSettingsUpdateStub = sandbox.stub(UserSettings, 'update');
    userSettingsCreateStub = sandbox.stub(UserSettings, 'create');
    userUpdateStub = sandbox.stub(User, 'update');
    
    // JWT and User stubs
    sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'api',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    sandbox.stub(User, 'findByPk').resolves(mockUser);
    
    // Repository stubs
    getUserWithCalendarInfoStub = sandbox.stub(UserRepository, 'getUserWithCalendarInfo');
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    delete require.cache[require.resolve(controllerPath)];
    delete require.cache[require.resolve(apiMiddlewarePath)];
  });

  describe('PUT /user-settings', function() {
    it('should update existing user settings successfully', async function() {
      // Setup
      const notificationSettings = {
        email: true,
        sms: true
      };
      
      userSettingsFindOneStub.resolves(mockUserSettings);
      userSettingsUpdateStub.resolves([1]);
      
      // Execute
      const response = await request(app)
        .put('/api/user-settings')
        .set('Authorization', 'Bearer test_token')
        .send({ notificationSettings });

      // Assert - Fix: Check response.body.message instead of response.body
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.text).to.equal('Settings saved successfully');
      expect(userSettingsFindOneStub.calledOnce).to.be.true;
      expect(userSettingsUpdateStub.calledOnce).to.be.true;
      expect(userSettingsUpdateStub.firstCall.args[0]).to.deep.equal({ notificationSettings });
      expect(userSettingsUpdateStub.firstCall.args[1]).to.deep.include({
        where: { userId: TEST_USER_ID }
      });
      expect(loggerInfoStub.calledTwice).to.be.true;
    });

    it('should create new user settings when none exist', async function() {
      // Setup
      const notificationSettings = {
        email: true,
        sms: true
      };
      
      userSettingsFindOneStub.resolves(null);
      userSettingsCreateStub.resolves(mockUserSettings);
      
      // Execute
      const response = await request(app)
        .put('/api/user-settings')
        .set('Authorization', 'Bearer test_token')
        .send({ notificationSettings });

      // Assert - Fix: Check response.body.message instead of response.body
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.text).to.equal('Settings saved successfully');
      expect(userSettingsFindOneStub.calledOnce).to.be.true;
      expect(userSettingsCreateStub.calledOnce).to.be.true;
      expect(userSettingsCreateStub.firstCall.args[0]).to.deep.equal({
        userId: TEST_USER_ID,
        notificationSettings
      });
      expect(loggerInfoStub.calledTwice).to.be.true;
    });
  });

  describe('PUT /user-settings/mfa', function() {
    it('should enable MFA successfully', async function() {
      // Setup
      getUserWithCalendarInfoStub.resolves(mockUserWithCalendarInfo);
      userUpdateStub.resolves([1]);
      
      // Execute
      const response = await request(app)
        .put('/api/user-settings/mfa')
        .set('Authorization', 'Bearer test_token')
        .send({ enable: true });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.equals('Enabled MFA successfully.');
      expect(response.body).to.have.property('user').that.deep.equals(mockUserWithCalendarInfo);
      expect(userUpdateStub.calledOnce).to.be.true;
      expect(userUpdateStub.firstCall.args[0]).to.deep.equal({ mfaEnabled: true });
      expect(userUpdateStub.firstCall.args[1]).to.deep.include({
        where: { id: TEST_USER_ID }
      });
      expect(loggerInfoStub.calledThrice).to.be.true;
    });

    it('should disable MFA successfully', async function() {
      // Setup
      getUserWithCalendarInfoStub.resolves({
        ...mockUserWithCalendarInfo,
        mfaEnabled: false
      });
      userUpdateStub.resolves([1]);
      
      // Execute
      const response = await request(app)
        .put('/api/user-settings/mfa')
        .set('Authorization', 'Bearer test_token')
        .send({ enable: false });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.equals('Disabled MFA successfully.');
      expect(userUpdateStub.calledOnce).to.be.true;
      expect(userUpdateStub.firstCall.args[0]).to.deep.equal({ mfaEnabled: false });
      expect(loggerInfoStub.calledThrice).to.be.true;
    });

    it('should return forbidden for invalid enable value', async function() {
      // Execute
      const response = await request(app)
        .put('/api/user-settings/mfa')
        .set('Authorization', 'Bearer test_token')
        .send({ enable: 'not-a-boolean' });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Invalid request');
      expect(userUpdateStub.called).to.be.false;
      expect(loggerInfoStub.calledTwice).to.be.true;
    });
  });
});