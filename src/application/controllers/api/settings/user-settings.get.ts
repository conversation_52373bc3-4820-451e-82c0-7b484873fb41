import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { NotFoundError } from '../../../handlers/errors'
import UserSettings from '@/src/models/user-settings.model'
import { User } from '@/src/models'
import APIMiddleware from '@/src/application/middlewares/api.middleware'

const router = express.Router()

/**
 * Get user settings
 */
router.get(
	'/user-settings',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { id } = req.user as User;

		const settings = await UserSettings.findOne({
			where: {
				userId: id,
			},
		})

		if (!settings) throw new NotFoundError('User Settings not found')

		res.send(settings)
	})
)

export default router
