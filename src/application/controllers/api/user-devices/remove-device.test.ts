import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import { UserDevice } from '@/src/models';
import logger from '@/src/configs/logger.config';

const mockUser = {
  id: '123',
  email: '<EMAIL>'
} as any;

express.response.forbidden = function (message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function (message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

const app = express();
app.use(express.json());

// Controller path
const controllerPath = '@/src/application/controllers/api/user-devices/remove-device.post';

describe('POST /remove-device', () => {
  let sandbox: sinon.SinonSandbox;
  let userDeviceFindOneStub: sinon.SinonStub;
  let userDeviceDestroyStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let originalApiMiddleware: any;
  const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';

  before(() => {
    originalApiMiddleware = require(apiMiddlewarePath).default;

    const mockMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
      req.user = { ...mockUser };
      req.role = mockUser.role;
      next();
    };

    originalApiMiddleware = require(apiMiddlewarePath).default;
    require(apiMiddlewarePath).default = mockMiddleware;
    
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    userDeviceFindOneStub = sandbox.stub(UserDevice, 'findOne');
    userDeviceDestroyStub = sandbox.stub();
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(() => {
    sandbox.restore();
  });

  after(() => {
    require(apiMiddlewarePath).default = originalApiMiddleware;
    require.cache[require.resolve(controllerPath)] = undefined;
    require.cache[require.resolve(apiMiddlewarePath)] = undefined;
  });

  it('should remove device successfully', async () => {
    const fakeDevice = {
      destroy: userDeviceDestroyStub.resolves()
    };

    userDeviceFindOneStub.resolves(fakeDevice);

    const response = await request(app)
      .post('/api/remove-device')
      .set('Authorization', 'Bearer test_token')
      .send({ deviceId: 'abc123' });

    expect(response.status).to.equal(HttpStatusCode.Ok);
    expect(response.body.message).to.equal('Device removed successfully');
    expect(userDeviceFindOneStub.calledOnce).to.be.true;
    expect(userDeviceDestroyStub.calledOnce).to.be.true;
    expect(loggerInfoStub.calledWithMatch(/Device removal request received/)).to.be.true;
    expect(loggerInfoStub.calledWithMatch(/successfully removed/)).to.be.true;
  });

  it('should return 403 if user ID is missing', async () => {
    const tempMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
      req.user = { ...mockUser, id: null  };
      next();
    };

    require(apiMiddlewarePath).default = tempMiddleware;
    require.cache[require.resolve(controllerPath)] = undefined;

    const tempApp = express();
    tempApp.use(express.json());
    tempApp.use('/api', require(controllerPath).default);

    const response = await request(tempApp)
      .post('/api/remove-device')
      .send({ deviceId: 'abc123' });

    expect(response.status).to.equal(HttpStatusCode.Forbidden);
    expect(response.body.message).to.equal('User ID is required');
  });

  it('should return 403 if device ID is missing', async () => {
    const response = await request(app)
      .post('/api/remove-device')
      .send({});

    expect(response.status).to.equal(HttpStatusCode.Forbidden);
    expect(response.body.message).to.equal('Device ID is required.');
  });

  it('should return forbidden if device is not found', async () => {
    userDeviceFindOneStub.resolves(null);

    const response = await request(app)
      .post('/api/remove-device')
      .send({ deviceId: 'notfound' });

    expect(response.status).to.equal(HttpStatusCode.Forbidden);
    expect(response.body.message).to.equal('Device with the ID not found');
    expect(loggerErrorStub.calledWithMatch(/No device found/)).to.be.true;
  });

});
