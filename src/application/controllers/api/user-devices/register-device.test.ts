import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import { User, UserDevice } from '@/src/models';
import logger from '@/src/configs/logger.config';
import { UserType } from '@/src/application/helpers/constant.helper';
import jwt from 'jsonwebtoken';

// Mock user data
const mockUser = {
  id: '123',
  email: '<EMAIL>',
  role: UserType.PATIENT,
  version: 1
} as any;

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Controller path
const controllerPath = '@/src/application/controllers/api/user-devices/register-device';

describe('API Register Device Controller', function() {
  let sandbox: sinon.SinonSandbox;
  let userDeviceFindOneStub: sinon.SinonStub;
  let userDeviceCreateStub: sinon.SinonStub;
  let userDeviceUpdateStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let userFindByPkStub: sinon.SinonStub;

  let originalApiMiddleware: any;
  let apiMiddlewarePath: string;

  before(function() {
    // Create custom middleware that will be used instead of the real API middleware
    const customMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
      req.user = { ...mockUser }; // Use a copy to avoid shared reference issues
      req.role = mockUser.role;
      next();
    };

    // Replace the real middleware with our custom one
    apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
    originalApiMiddleware = require(apiMiddlewarePath).default;
    require(apiMiddlewarePath).default = customMiddleware;

    // Set up the routes with our middleware
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userDeviceFindOneStub = sandbox.stub(UserDevice, 'findOne');
    userDeviceCreateStub = sandbox.stub(UserDevice, 'create');
    
    // Mock the update method on the UserDevice instance
    userDeviceUpdateStub = sandbox.stub().resolves({});
    
    // JWT and User stubs
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'api',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockUser);
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
    require.cache[require.resolve(apiMiddlewarePath)] = undefined;
  });

  describe('POST /register-device', function() {

    it('should return 403 when no user is provided', async function() {
      // Setup
      const deviceData = {
        deviceId: 'device123',
        deviceType: 'android'
      };
      
      // Create a new middleware that simulates missing user ID
      const noUserMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = { ...mockUser, id: null };
        req.role = mockUser.role;
        next();
      };
      
      // Replace the middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = noUserMiddleware;
      
      try {
        // Clear controller cache to use the new middleware
        const cachedController = require.cache[require.resolve(controllerPath)];
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create a temporary app with the new middleware
        const tempApp = express();
        tempApp.use(express.json());
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .set('Authorization', 'Bearer test_token')
          .send(deviceData);
        
        // Assert
        expect(response.status).to.equal(HttpStatusCode.Forbidden);
        expect(response.body).to.have.property('message').that.equals('User ID is required');
      } finally {
        // Restore the original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });

    it('should return 403 when deviceId is missing', async function() {
      // Setup
      const deviceData = {
        deviceType: 'android'
      };
      
      // Create a temporary app with proper middleware for this specific test
      const tempMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = { ...mockUser }; // Ensure user ID is present
        req.role = mockUser.role;
        next();
      };
      
      // Replace middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = tempMiddleware;
      
      try {
        // Clear controller cache
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create temp app
        const tempApp = express();
        tempApp.use(express.json());
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .set('Authorization', 'Bearer test_token')
          .send(deviceData);

        // Assert
        expect(response.status).to.equal(HttpStatusCode.Forbidden);
        expect(response.body).to.have.property('message').that.equals('Device ID or device type is required.');
      } finally {
        // Restore original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });

    it('should return 403 when deviceType is missing', async function() {
      // Setup
      const deviceData = {
        deviceId: 'device123'
      };
      
      // Create a temporary app with proper middleware for this specific test
      const tempMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = { ...mockUser }; // Ensure user ID is present
        req.role = mockUser.role;
        next();
      };
      
      // Replace middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = tempMiddleware;
      
      try {
        // Clear controller cache
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create temp app
        const tempApp = express();
        tempApp.use(express.json());
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .set('Authorization', 'Bearer test_token')
          .send(deviceData);

        // Assert
        expect(response.status).to.equal(HttpStatusCode.Forbidden);
        expect(response.body).to.have.property('message').that.equals('Device ID or device type is required.');
      } finally {
        // Restore original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });

    it('should return 403 when deviceType is invalid', async function() {
      // Setup
      const deviceData = {
        deviceId: 'device123',
        deviceType: 'windows'  // Invalid device type
      };
      
      // Create a temporary app with proper middleware for this specific test
      const tempMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = { ...mockUser }; // Ensure user ID is present
        req.role = mockUser.role;
        next();
      };
      
      // Replace middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = tempMiddleware;
      
      try {
        // Clear controller cache
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create temp app
        const tempApp = express();
        tempApp.use(express.json());
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .set('Authorization', 'Bearer test_token')
          .send(deviceData);

        // Assert
        expect(response.status).to.equal(HttpStatusCode.Forbidden);
        expect(response.body).to.have.property('message').that.equals('Invalid device type. Please provide a valid device type.');
      } finally {
        // Restore original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });

    it('should return 403 when additionalPayload is invalid', async function() {
      // Setup
      const deviceData = {
        deviceId: 'device123',
        deviceType: 'android',
        additionalPayload: {}  // Empty object is invalid
      };
      
      // Create a temporary app with proper middleware for this specific test
      const tempMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = { ...mockUser }; // Ensure user ID is present
        req.role = mockUser.role;
        next();
      };
      
      // Replace middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = tempMiddleware;
      
      try {
        // Clear controller cache
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create temp app
        const tempApp = express();
        tempApp.use(express.json());
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .set('Authorization', 'Bearer test_token')
          .send(deviceData);

        // Assert
        expect(response.status).to.equal(HttpStatusCode.Forbidden);
        expect(response.body).to.have.property('message').that.equals('Invalid additionalPayload. Must be a non-empty JSON object.');
      } finally {
        // Restore original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });

    it('should handle database errors properly', async function() {
      // Setup
      const deviceData = {
        deviceId: 'device123',
        deviceType: 'android'
      };
      
      // Create a temporary app with proper middleware for this specific test
      const tempMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = { ...mockUser }; // Ensure user ID is present
        req.role = mockUser.role;
        next();
      };
      
      // Replace middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = tempMiddleware;
      
      try {
        // Clear controller cache
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create temp app with error handling
        const tempApp = express();
        tempApp.use(express.json());
        
        // Add error handler middleware
        tempApp.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
          res.status(HttpStatusCode.InternalServerError).send({ message: 'Internal Server Error' });
        });
        
        // Setup database error
        userDeviceFindOneStub.rejects(new Error('Database error'));
        
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .set('Authorization', 'Bearer test_token')
          .send(deviceData);

        // Assert
        expect(response.status).to.equal(HttpStatusCode.InternalServerError);
      } finally {
        // Restore original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });

    it('should return 401 when no authorization header is provided', async function() {
      const deviceData = {
        deviceId: 'device123',
        deviceType: 'android'
      };
      
      // Create unauthorized middleware
      const unauthorizedMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
      };
      
      // Replace middleware temporarily
      const originalMiddleware = require(apiMiddlewarePath).default;
      require(apiMiddlewarePath).default = unauthorizedMiddleware;
      
      try {
        // Clear controller cache
        require.cache[require.resolve(controllerPath)] = undefined;
        
        // Create temp app
        const tempApp = express();
        tempApp.use(express.json());
        tempApp.use('/api', require(controllerPath).default);
        
        // Execute
        const response = await request(tempApp)
          .post('/api/register-device')
          .send(deviceData);

        // Assert
        expect(response.status).to.equal(HttpStatusCode.Unauthorized);
      } finally {
        // Restore original middleware
        require(apiMiddlewarePath).default = originalMiddleware;
      }
    });
  });
});