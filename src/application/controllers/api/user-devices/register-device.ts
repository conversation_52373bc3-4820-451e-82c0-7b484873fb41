import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { ForbiddenError } from '../../../handlers/errors'
import { User, UserDevice } from '@/src/models'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import logger from '@/src/configs/logger.config'
import admin from '@/src/configs/firebase.config'
import { Op } from 'sequelize'

const router = express.Router()

/**
 * Register User Device
 */
router.post(
	'/register-device',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user?.id) throw new ForbiddenError('User ID is required');

		const { deviceId, deviceType, additionalPayload } = req.body;
		
		if (!deviceId || !deviceType) throw new ForbiddenError('Device ID or device type is required.');

		if (deviceType !== 'ios' && deviceType !== 'android')
			throw new ForbiddenError('Invalid device type. Please provide a valid device type.');

		if (additionalPayload && (typeof additionalPayload !== 'object' || Object.keys(additionalPayload).length === 0))
			throw new ForbiddenError('Invalid additionalPayload. Must be a non-empty JSON object.');

		logger.info(`Device registration request received for User ID: ${user.id}.`);
		logger.info(`Device Type: ${deviceType}, Device ID: [MASKED-${deviceId.slice(-4)}].`);

		await UserDevice.destroy({
			where: {
				deviceId,
				userId: { [Op.ne]: user.id },
			},
		});
		
		const userDevice = await UserDevice.findOne({ where: { userId: user.id,deviceId } })

		if (userDevice) {
			logger.info(`Updating existing device record for User ID: ${user.id}.`);
			await userDevice.update({
				deviceType,
				additionalPayload: additionalPayload || null,
			})
		} else {
			logger.info(`Creating new device record for User ID: ${user.id}.`);
			await UserDevice.create({
				userId: user ? user.id : null,
				deviceId,
				deviceType,
				additionalPayload: additionalPayload || null,
			})
		}

		return res.success('Device registered successfully')
	})
)

export default router
