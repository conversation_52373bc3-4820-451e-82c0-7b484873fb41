import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { ForbiddenError } from '../../../handlers/errors'
import { UserDevice } from '@/src/models'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/**
 * Remove User Device
 */
router.post(
	'/remove-device',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user?.id) throw new ForbiddenError('User ID is required');

		const { deviceId } = req.body;

		if (!deviceId) throw new ForbiddenError('Device ID is required.');

		logger.info(`Device removal request received for User ID: ${user.id}.`);
		logger.info(`Device ID: [MASKED-${deviceId.slice(-4)}].`);

		const userDevice = await UserDevice.findOne({
			where: {
				userId: user.id,
				deviceId,
			}
		});

		if (!userDevice) {
			logger.error(`No device found for User ID: ${user.id}`);
			return res.forbidden('Device with the ID not found');
		}

		await userDevice.destroy();
		logger.info(`Device with ID: [MASKED-${deviceId.slice(-4)}] successfully removed for User ID: ${user.id}.`);

		return res.success('Device removed successfully');
	})
)

export default router
