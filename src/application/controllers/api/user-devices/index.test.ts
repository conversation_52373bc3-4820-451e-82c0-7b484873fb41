import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import { User } from '@/src/models';
import { UserType } from '@/src/application/helpers/constant.helper';
import jwt from 'jsonwebtoken';

// Mock user data
const mockUser = {
  id: '123',
  email: '<EMAIL>',
  role: UserType.PATIENT,
  version: 1
} as any;

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Controller path
const controllerPath = '@/src/application/controllers/api/user-devices/index';
const registerDevicePath = '@/src/application/controllers/api/user-devices/register-device';

describe('API User Devices Index Controller', function() {
  let sandbox: sinon.SinonSandbox;
  let jwtVerifyStub: sinon.SinonStub;
  let userFindByPkStub: sinon.SinonStub;
  let registerDeviceRouterStub: sinon.SinonStub;

  before(function() {
    // Create custom middleware that will be used instead of the real API middleware
    const customMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
      req.user = mockUser;
      req.role = mockUser.role;
      next();
    };

    // Replace the real middleware with our custom one
    const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
    const originalApiMiddleware = require(apiMiddlewarePath).default;
    require(apiMiddlewarePath).default = customMiddleware;

    // Mock the register-device router
    const mockRegisterDeviceRouter = express.Router();
    mockRegisterDeviceRouter.post('/register-device', (req, res) => {
      res.success('Mock register device endpoint');
    });
    
    // Stub the register-device module
    require.cache[require.resolve(registerDevicePath)] = {
      exports: mockRegisterDeviceRouter
    } as NodeModule;

    // Set up the routes with our middleware
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // JWT and User stubs
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'api',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockUser);
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    require.cache[require.resolve(registerDevicePath)] = undefined;
    const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
    require.cache[require.resolve(apiMiddlewarePath)] = undefined;
  });

  describe('Router Configuration', function() {
    it('should properly mount the register-device router', async function() {
      // Execute
      const response = await request(app)
        .post('/api/register-device')
        .set('Authorization', 'Bearer test_token')
        .send({});

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.equals('Mock register device endpoint');
    });
  });
});
