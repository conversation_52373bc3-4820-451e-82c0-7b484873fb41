import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { User, NormalOfficeHours, Appointments, Calendar, UserRegistrationInfo } from '@/src/models'
import dayjs from 'dayjs'
import { Op } from 'sequelize'
import { WaitlistNotifications } from '@/src/types/registration.interface'
import timezone from "dayjs/plugin/timezone";
import { getAvailableOfficeHours } from '@/src/application/helpers/availability.helper'

dayjs.extend(timezone);

const router = express.Router()

/**
 * Check if the therapist has available appointment slots in the next 14 days.
 * @returns {boolean} - true if there are available slots, false otherwise.
 */
router.get(
	'/check-availability/:id',
	wrap(async (req: Request, res: Response) => {

		const { id } = req.params;
		if (!id) {
			return res.forbidden('Invalid Request. Missing therapist ID');
		}

		const therapist = await User.findOne({
      where: {
        id,
      },
      include: [
        {
          model: NormalOfficeHours,
          attributes: ['id', 'workingDay', 'availabilityHours','appointmentMethod'],
          where: {
            isDisabled: false,
						deletedAt: null,
          },
          required: false,
        },
        {
          model: Appointments,
          as: 'therapistAppointments',
          attributes: ['id', 'workingHourId', 'appointmentDate', 'duration'],
          where: {
            cancelledAt: null,
            appointmentDate: {
              [Op.gte]: dayjs().toDate(),
            },
          },
          required: false,
        },
        {
          model: Calendar,
          attributes: ['type', 'credentials'],
        },
        {
          model: UserRegistrationInfo,
          attributes: ['pageName', 'payloadInfo'],
					where: {
            pageName: 'waitlist-notifications',
          },
					required: false,
        }
      ],
      attributes: ['id', 'timeZone']
    });
		if (!therapist) {
			return res.forbidden('Invalid Request. Therapist not found');
		}

		if (!therapist.normalOfficeHours || therapist.normalOfficeHours.length === 0) {
			return res.send({ available: false });
		}

		const waitlistNotifInfo = therapist.registrationInfo.find(
				(info) => info.pageName === 'waitlist-notifications'
			)?.payloadInfo as unknown as WaitlistNotifications || null;

		const isAvailable = await getAvailableOfficeHours({
			therapistId: therapist.id,
			officeHours: therapist.normalOfficeHours,
			appointments: therapist.therapistAppointments,
			therapistTimeZone: therapist.timeZone,
			calendars: therapist.calendars,
			waitlistNotifInfo,
			returnAvailableHours: false,
		});

		return res.send({ available: isAvailable });
	})
);

export default router
