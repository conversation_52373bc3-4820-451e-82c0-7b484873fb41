import dotenv from 'dotenv';
dotenv.config();

import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { User } from '../../../../models';
import { HttpStatusCode } from 'axios';
import { UserType } from '../../../helpers/constant.helper';
import * as CalendarRepository from '../../../repositories/calendar.repository';
import dayjs from 'dayjs';
import logger from '../../../../configs/logger.config';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import isBetween from 'dayjs/plugin/isBetween';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import availableHoursGetRoute from './available-office-hours.get';
import jwt from 'jsonwebtoken';
import { response } from '@/src/application/helpers/response.helper';

// Extend dayjs with all required plugins
dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.extend(isBetween);
dayjs.extend(customParseFormat);

// Define test constants
const TEST_USER_ID = '123';
const TEST_THERAPIST_ID = '456';
const TEST_EMAIL = '<EMAIL>';
const TEST_THERAPIST_EMAIL = '<EMAIL>';
const TEST_DATE = dayjs().add(1, 'day').format('YYYY-MM-DD');
const TEST_TIMEZONE = 'America/New_York';

// Mock User model instances
const mockPatient = {
  id: TEST_USER_ID,
  email: TEST_EMAIL,
  firstname: 'John',
  lastname: 'Doe',
  emailVerifiedAt: new Date(),
  role: UserType.PATIENT,
  timeZone: TEST_TIMEZONE
} as any;

const mockTherapist = {
  id: TEST_THERAPIST_ID,
  email: TEST_THERAPIST_EMAIL,
  firstname: 'Jane',
  lastname: 'Smith',
  emailVerifiedAt: new Date(),
  acceptedAt: new Date(),
  role: UserType.THERAPIST,
  timeZone: TEST_TIMEZONE,
  normalOfficeHours: [
    {
      id: '1',
      workingDay: 'sunday',
      availabilityHours: '10:00:00',
      appointmentMethod: 'in-person-and-telehealth',
      isDisabled: false,
      deletedAt: null
    },
  ],
  therapistAppointments: [
    {
      id: 1,
      therapistId: 1,
      patientId: 2,
      isBilled: false,
      cancelledAt: null,
      amount: 100,
      appointmentDate: dayjs().add(1, 'day').toISOString(),
    }
  ],
  calendars: [
    {
      type: 'google',
      email: TEST_THERAPIST_EMAIL,
      credentials: {
        tokens: {
          access_token: 'google_access_token',
          refresh_token: 'google_refresh_token'
        }
      }
    },
    {
      type: 'outlook',
      email: TEST_THERAPIST_EMAIL,
      credentials: {
        tokens: {
          access_token: 'outlook_access_token',
          refresh_token: 'outlook_refresh_token'
        }
      }
    }
  ],
  registrationInfo: [
    {
      pageName: 'waitlist-notifications',
      payloadInfo: {
        booking_time_hour: '24',
        notification_preferences: ['email', 'text'],
        phone_number: '+**********',
        week_visibility: '4'
      }
    }
  ],
  therapistWaitlist: [
    {
      id: '1',
      patientId: TEST_USER_ID,
      therapistId: TEST_THERAPIST_ID,
      status: 'waiting',
    }
  ]
} as any;

const app = express();

app.use(express.json());
app.use(response);
app.use('/api', availableHoursGetRoute);

describe('API Available Office Hours Controller', function() {
  let sandbox: sinon.SinonSandbox;

  let userFindByPkStub: sinon.SinonStub;
  let userFindOneStub: sinon.SinonStub;

  let getGoogleEventsStub: sinon.SinonStub;
  let getOutlookEventsStub: sinon.SinonStub;

  let loggerErrorStub: sinon.SinonStub;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockPatient);
    userFindOneStub = sandbox.stub(User, 'findOne').resolves(mockTherapist);
    
    // Calendar repository stubs
    getGoogleEventsStub = sandbox.stub(CalendarRepository, 'getGoogleEvents');
    getOutlookEventsStub = sandbox.stub(CalendarRepository, 'getOutlookEvents');
    
    loggerErrorStub = sandbox.stub(logger, 'error');

    getGoogleEventsStub.resolves([]);
    getOutlookEventsStub.resolves([]);

    sandbox.stub(jwt, 'verify').returns({
      id: '1',
      type: 'api',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    } as any);
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('GET /available-hours', function() {

    it('should return forbidden if therapist ID is missing', async function() {
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          date: TEST_DATE,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Therapist ID is required');
    });

    it('should return forbidden if date is missing', async function() {
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Date is required');
    });

    it('should return forbidden if timeZone is missing', async function() {
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: TEST_DATE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Time zone is required');
    });

    it('should return forbidden if date is invalid', async function() {
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: 'invalid-date',
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid date. Please ensure the date is valid and is in format YYYY-MM-DD');
    });

    it('should return forbidden if date is in the past', async function() {
      // Execute
      const pastDate = dayjs().subtract(2, 'day').format('YYYY-MM-DD');
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: pastDate,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid date. Past dates are not allowed.');
    });

    it('should return forbidden if therapist is not found', async function() {
      // Setup
      userFindOneStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: TEST_DATE,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid Request. Therapist not found.');
    });

    it('should return forbidden if therapist timezone is not set', async function() {
      // Setup
      userFindOneStub.resolves({
        ...mockTherapist,
        timeZone: null,
      });
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: TEST_DATE,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid Request. Therapist time zone is not set.');
    });

    it('should return empty array if therapist has no office hours', async function() {
      // Setup
      userFindOneStub.resolves({
        ...mockTherapist,
        normalOfficeHours: [],
      });

      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: TEST_DATE,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.be.an('array').that.is.empty;
    });

    it('should return forbidden if date is beyond week visibility', async function() {
      // Setup
      const futureDate = dayjs().add(5, 'weeks').format('YYYY-MM-DD'); // Beyond 4 weeks
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: futureDate,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid date. Please ensure the date is within 4 weeks from today.');
    });

    it('should return empty array if all slots are booked', async function() {
      // Setup
      const date = dayjs().tz(TEST_TIMEZONE).add(2, 'day');
      const formattedDate = date.format('YYYY-MM-DD');
      const day = date.tz(mockTherapist.timeZone).format('dddd').toLowerCase();

      userFindOneStub.resolves({
        ...mockTherapist,
        normalOfficeHours: [
          {
            id: '2',
            workingDay: day,
            availabilityHours: '09:00:00',
            appointmentMethod: 'telehealth',
          },
          {
            id: '3',
            workingDay: day,
            availabilityHours: '10:00:00',
            appointmentMethod: 'in-person',
          },
        ],
        therapistAppointments: [
          {
            duration: 60,
            appointmentDate: date.tz(mockTherapist.timeZone).hour(9).minute(0).second(0).millisecond(0).toISOString(),
          },
          {
            duration: 60,
            appointmentDate: date.tz(mockTherapist.timeZone).hour(10).minute(0).second(0).millisecond(0).toISOString(),
          }
        ]
      })
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: formattedDate,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.be.an('array').that.is.empty;
    });

    it('should return empty array if available slots are before the advance notice time', async function() {
      // Setup
      const date = dayjs().tz(TEST_TIMEZONE).add(1, 'day').startOf('day');
      const formattedDate = date.format('YYYY-MM-DD');
      const day = date.tz(mockTherapist.timeZone).format('dddd').toLowerCase();

      userFindOneStub.resolves({
        ...mockTherapist,
        normalOfficeHours: [
          {
            id: '2',
            workingDay: day,
            availabilityHours: '09:00:00',
            appointmentMethod: 'telehealth',
          },
          {
            id: '3',
            workingDay: day,
            availabilityHours: '10:00:00',
            appointmentMethod: 'in-person',
          },
        ],
        registrationInfo: [
          {
            pageName: 'waitlist-notifications',
            payloadInfo: {
              booking_time_hour: '48',
              notification_preferences: ['email', 'text'],
              phone_number: '+**********',
              week_visibility: '4'
            }
          }
        ],
      })
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: formattedDate,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.be.an('array').that.is.empty;
    });

    it('should return empty array if available slots are blocked by google events', async function() {
      // Setup
      const patientTimeZone = 'America/Los_Angeles';
      const date = dayjs().tz(patientTimeZone).add(2, 'day');
      const formattedDate = date.format('YYYY-MM-DD');
      const day1 = date.startOf('day').tz(mockTherapist.timeZone);
      const day2 = date.endOf('day').tz(mockTherapist.timeZone);

      userFindOneStub.resolves({
        ...mockTherapist,
        normalOfficeHours: [
          {
            id: '2',
            workingDay: day1.format('dddd').toLowerCase(),
            availabilityHours: day1.add(1, 'hour').format('HH:mm:ss'),
            appointmentMethod: 'telehealth',
          },
          {
            id: '3',
            workingDay: day1.format('dddd').toLowerCase(),
            availabilityHours: day1.add(2, 'hour').format('HH:mm:ss'),
            appointmentMethod: 'telehealth',
          },
          {
            id: '4',
            workingDay: day2.format('dddd').toLowerCase(),
            availabilityHours: day2.subtract(1, 'hour').format('HH:mm:ss'),
            appointmentMethod: 'in-person',
          },
          {
            id: '5',
            workingDay: day2.format('dddd').toLowerCase(),
            availabilityHours: day2.subtract(2, 'hour').format('HH:mm:ss'),
            appointmentMethod: 'telehealth',
          },
        ],
      });
      getGoogleEventsStub.resolves([
        {
          start: {
            dateTime: day1.add(1, 'hour').toISOString(),
            timeZone: 'UTC'
          },
          end: {
            dateTime: day1.add(2, 'hour').toISOString(),
            timeZone: 'UTC'
          },
        },
        {
          start: {
            dateTime: day1.add(2, 'hour').toISOString(),
            timeZone: 'UTC'
          },
          end: {
            dateTime: day1.add(3, 'hour').toISOString(),
            timeZone: 'UTC'
          },
        },
        {
          start: {
            dateTime: day2.subtract(1, 'hour').toISOString(),
            timeZone: 'UTC'
          },
          end: {
            dateTime: day2.toISOString(),
            timeZone: 'UTC'
          },
        },
        {
          start: {
            dateTime: day2.subtract(2, 'hour').toISOString(),
            timeZone: 'UTC'
          },
          end: {
            dateTime: day2.subtract(1, 'hour').toISOString(),
            timeZone: 'UTC'
          },
        }
      ]);
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: formattedDate,
          timeZone: patientTimeZone
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.be.an('array').to.be.empty;
    });

    it('should return empty array if available slots are blocked by outlook events', async function() {
      // Setup
      const date = dayjs().tz(TEST_TIMEZONE).add(1, 'day').startOf('day');
      const formattedDate = date.format('YYYY-MM-DD');
      const day = date.tz(mockTherapist.timeZone).format('dddd').toLowerCase();

      userFindOneStub.resolves({
        ...mockTherapist,
        normalOfficeHours: [
          {
            id: '3',
            workingDay: day,
            availabilityHours: '10:00:00',
            appointmentMethod: 'in-person',
          },
        ],
      });
      getOutlookEventsStub.resolves([
        {
          start: {
            dateTime: date.tz(mockTherapist.timeZone).hour(10).minute(0).second(0).millisecond(0).toISOString(),
            timeZone: 'UTC',
          },
          end: {
            dateTime: date.tz(mockTherapist.timeZone).hour(11).minute(0).second(0).millisecond(0).toISOString(),
            timeZone: 'UTC',
          },
        }
      ]);
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: formattedDate,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.be.an('array').that.is.empty;
    });

    it('should return available hours when valid request is made', async function() {
      // Setup
      const date = dayjs().tz(TEST_TIMEZONE).add(2, 'day');
      const formattedDate = date.format('YYYY-MM-DD');
      const day = date.tz(mockTherapist.timeZone).format('dddd').toLowerCase();

      userFindOneStub.resolves({
        ...mockTherapist,
        normalOfficeHours: [
          {
            id: '2',
            workingDay: day,
            availabilityHours: '09:00:00',
            appointmentMethod: 'telehealth',
          },
          {
            id: '3',
            workingDay: day,
            availabilityHours: '14:00:00',
            appointmentMethod: 'in-person',
          },
        ],
        therapistAppointments: [],
        registrationInfo: [],
        therapistWaitlist: []
      });
      
      // Execute
      const response = await request(app)
        .get('/api/available-hours')
        .set('Authorization', 'Bearer accessToken')
        .query({
          therapistId: TEST_THERAPIST_ID,
          date: formattedDate,
          timeZone: TEST_TIMEZONE
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(2);
      expect(response.body[0]).to.have.property('id', '2');
      expect(response.body[0]).to.have.property('appointmentMethod', 'telehealth');
      expect(response.body[1]).to.have.property('id', '3');
      expect(response.body[1]).to.have.property('appointmentMethod', 'in-person');
    });
  });
});