import { expect } from 'chai';
import sinon from 'sinon';

// Import the modules we want to test
import officeHoursController from '.';
import getAvailableOfficeHours from './available-office-hours.get';
import checkAvailability from './check-availability.get';

describe('Office Hours Controller Index', function () {
  let sandbox: sinon.SinonSandbox;
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
  });
  
  afterEach(function() {
    sandbox.restore();
  });
  
  it('should import all required routes', function () {
    expect(getAvailableOfficeHours).to.exist;
    expect(checkAvailability).to.exist;
  });
  
  it('should export a router', function () {
    expect(officeHoursController).to.exist;
  });
});
