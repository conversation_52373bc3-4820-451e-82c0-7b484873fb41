import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import sinon from 'sinon';
import { HttpStatusCode } from 'axios';
import { expect } from 'chai';
import checkAvailabilityController from './check-availability.get';
import { User} from '../../../../models';
import logger from '../../../../configs/logger.config';
import jwt from 'jsonwebtoken';
import * as availabilityHelper from '../../../helpers/availability.helper';

const app = express();
app.use(express.json());

// Mock user data
const mockUser = {
  id: '123',
  role: 'patient',
  version: 1,
  firstname: '<PERSON>',
  lastname: '<PERSON><PERSON>',
  email: '<EMAIL>'
} as any;

app.use('/api/office-hours', checkAvailabilityController);

describe('Check Availability Controller', function () {
	let sandbox: sinon.SinonSandbox;

  let findOneStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let getAvailableOfficeHoursStub: sinon.SinonStub;
	let userFindByPkStub: sinon.SinonStub;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    findOneStub = sandbox.stub(User, 'findOne');
		userFindByPkStub = sandbox.stub(User, 'findByPk');
    
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

    getAvailableOfficeHoursStub = sandbox.stub(availabilityHelper, 'getAvailableOfficeHours');
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('GET /check-availability/:id', function () {
    it('should return available: true when therapist has available slots', async function () {
      const mockTherapist = {
        id: '789',
        timeZone: 'America/New_York',
        normalOfficeHours: [
          {
            id: '1',
            workingDay: 'Monday',
            availabilityHours: ['09:00-10:00', '11:00-12:00'],
            appointmentMethod: 'video'
          }
        ],
        therapistAppointments: [],
        calendars: [],
        registrationInfo: [
          {
            pageName: 'waitlist-notifications',
            payloadInfo: {
              isWaitlistEnabled: true
            }
          }
        ]
      };

			userFindByPkStub.resolves(mockUser);
      findOneStub.resolves(mockTherapist);
      getAvailableOfficeHoursStub.resolves(true);
      
      const therapistId = '789';
      
      const response = await request(app)
        .get(`/api/office-hours/check-availability/${therapistId}`)
        .set('Authorization', 'Bearer valid-token')
        .expect(HttpStatusCode.Ok);
      
      expect(response.body).to.have.property('available', true);
      expect(findOneStub.calledOnce).to.be.true;
      expect(getAvailableOfficeHoursStub.calledOnce).to.be.true;
    });

    it('should return available: false when therapist has no available slots', async function () {
      const mockTherapist = {
        id: '789',
        timeZone: 'America/New_York',
        normalOfficeHours: [
          {
            id: '1',
            workingDay: 'Monday',
            availabilityHours: ['09:00-10:00', '11:00-12:00'],
            appointmentMethod: 'video'
          }
        ],
        therapistAppointments: [],
        calendars: [],
        registrationInfo: []
      };

			userFindByPkStub.resolves(mockUser);
      findOneStub.resolves(mockTherapist);
      getAvailableOfficeHoursStub.resolves(false);
      
      const therapistId = '789';
      
      const response = await request(app)
        .get(`/api/office-hours/check-availability/${therapistId}`)
        .set('Authorization', 'Bearer valid-token')
        .expect(HttpStatusCode.Ok);
      
      expect(response.body).to.have.property('available', false);
    });

    it('should return available: false when therapist has no office hours', async function () {
      const mockTherapist = {
        id: '789',
        timeZone: 'America/New_York',
        normalOfficeHours: [],
        therapistAppointments: [],
        calendars: [],
        registrationInfo: []
      };

			userFindByPkStub.resolves(mockUser);
      findOneStub.resolves(mockTherapist);
      
      const therapistId = '789';
      
      const response = await request(app)
        .get(`/api/office-hours/check-availability/${therapistId}`)
        .set('Authorization', 'Bearer valid-token')
        .expect(HttpStatusCode.Ok);
      
      expect(response.body).to.have.property('available', false);
      expect(getAvailableOfficeHoursStub.called).to.be.false;
    });

    it('should return forbidden when therapist is not found', async function () {
      findOneStub.resolves(null);
      userFindByPkStub.resolves(mockUser);

      const therapistId = '789';
      
      const response = await request(app)
        .get(`/api/office-hours/check-availability/${therapistId}`)
        .set('Authorization', 'Bearer valid-token');
      
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body.message).to.include('Therapist not found');
    });

    it('should handle database errors gracefully', async function () {
			userFindByPkStub.resolves(mockUser);
      findOneStub.throws(new Error('Database connection error'));
      
      const therapistId = '789';
      
      const response = await request(app)
        .get(`/api/office-hours/check-availability/${therapistId}`)
        .set('Authorization', 'Bearer valid-token');
      
      expect(response.status).to.be.greaterThan(399);
    });

    it('should handle availability helper errors gracefully', async function () {
      const mockTherapist = {
        id: '789',
        timeZone: 'America/New_York',
        normalOfficeHours: [
          {
            id: '1',
            workingDay: 'Monday',
            availabilityHours: ['09:00-10:00', '11:00-12:00'],
            appointmentMethod: 'video'
          }
        ],
        therapistAppointments: [],
        calendars: [],
        registrationInfo: []
      };

			userFindByPkStub.resolves(mockUser);
      findOneStub.resolves(mockTherapist);
      getAvailableOfficeHoursStub.throws(new Error('Availability calculation error'));
      
      const therapistId = '789';
      
      const response = await request(app)
        .get(`/api/office-hours/check-availability/${therapistId}`)
        .set('Authorization', 'Bearer valid-token');
      
      expect(response.status).to.be.greaterThan(399);
    });
  });
});
