import { Appointments, Calendar, NormalOfficeHours, TherapistWaitlist, User, UserRegistrationInfo } from "@/src/models";
import express, { Request, Response } from "express";
import { wrap } from "@/src/application/helpers";
import dayjs from "dayjs";
import APIMiddleware from "@/src/application/middlewares/api.middleware";
import { getGoogleEvents, getOutlookEvents } from "@/src/application/repositories/calendar.repository";
import timezone from "dayjs/plugin/timezone"; 
import { WaitlistNotifications } from "@/src/types/registration.interface";
import logger from "@/src/configs/logger.config";
import { Op } from "sequelize";
import { FormattedOfficeHours } from "@/src/application/helpers/types.helper";

// dayjs.extend(isBetween);
// dayjs.extend(customParseFormat);
// dayjs.extend(utc);
dayjs.extend(timezone);

const router = express.Router()

router.get(
  '/available-hours',
  wrap(async (req: Request, res: Response) => {

    const { therapistId, date, timeZone } = req.query;
    if (!therapistId) return res.forbidden('Therapist ID is required');
    if (!date) return res.forbidden('Date is required');
    if (!timeZone) return res.forbidden('Time zone is required');
    
    const patientTimeZone = timeZone.toString();
    
    // check if the date sent by the patient is valid
    const validDate = dayjs(date.toString(), 'YYYY-MM-DD', true);
    if (!validDate.isValid()) return res.forbidden('Invalid date. Please ensure the date is valid and is in format YYYY-MM-DD');

    // patient date dayjs object
    const patientDate = dayjs.tz(date.toString(), patientTimeZone);
    const patientToday = dayjs().tz(patientTimeZone).startOf('day');

    if (patientDate.isBefore(patientToday)) {
      return res.forbidden('Invalid date. Past dates are not allowed.');
    }

    const therapist = await User.findOne({
      where: {
        id: therapistId
      },
      include: [
        {
          model: NormalOfficeHours,
          attributes: ['id', 'workingDay', 'availabilityHours','appointmentMethod'],
          where: {
            isDisabled: false,
            deletedAt: null,
          },
          required: false,
        },
        {
          model: Appointments,
          as: 'therapistAppointments',
          attributes: ['id', 'workingHourId', 'appointmentDate', 'duration'],
          where: {
            cancelledAt: null,
            appointmentDate: {
              [Op.gte]: dayjs().toDate(),
            },
          },
          required: false,
        },
        {
          model: Calendar,
          attributes: ['type', 'credentials'],
        },
        {
          model: UserRegistrationInfo,
          attributes: ['pageName', 'payloadInfo'],
          where: {
            pageName: 'waitlist-notifications',
          },
          required: false,
        },
      ],
      attributes: ['id', 'timeZone']
    })
    
    if (!therapist) return res.forbidden('Invalid Request. Therapist not found.')
    if (!therapist.timeZone) return res.forbidden('Invalid Request. Therapist time zone is not set.')
    if (therapist.normalOfficeHours.length === 0) return res.single([]);

    const therapistTimeZone = therapist.timeZone;

    let timeAfterAdvanceNoticeAdjust: dayjs.Dayjs | undefined;

    if (therapist.registrationInfo.length > 0) {
      const waitlistInfo = therapist.registrationInfo[0]?.payloadInfo as unknown as WaitlistNotifications;
      // check week visibility of the therapist
      if (waitlistInfo?.week_visibility) {
        const weekVisibility = Number(waitlistInfo.week_visibility.trim());

        if (!isNaN(weekVisibility) && weekVisibility > 0) {
          const dateAfterWeekVisibility = patientToday.add(weekVisibility, 'week');
          if (patientDate.isAfter(dateAfterWeekVisibility) || patientDate.isSame(dateAfterWeekVisibility)) {
            return res.forbidden(`Invalid date. Please ensure the date is within ${weekVisibility} weeks from today.`);
          }
        }
      }

      // check advance notice time of the therapist
			if (waitlistInfo?.booking_time_hour) {
				const advanceNoticeHour = Number(waitlistInfo.booking_time_hour.trim());

				if (!isNaN(advanceNoticeHour) && advanceNoticeHour > 0) {
					const currentThTime = dayjs().tz(therapistTimeZone).millisecond(0);
					timeAfterAdvanceNoticeAdjust = currentThTime.add(advanceNoticeHour, "hour");
				}
			}
    }

    // check if the date sent by the patient is the same day in the patient's time zone
    const isToday = patientDate.isSame(dayjs().tz(patientTimeZone), 'day');

    // get the start and end date of the therapist according to the patient date
    const therapistStartDate = isToday
      ? dayjs().tz(therapistTimeZone).second(0).millisecond(0)
      : patientDate.startOf('day').tz(therapistTimeZone).second(0).millisecond(0);
    const therapistEndDate = patientDate.endOf('day').tz(therapistTimeZone).second(0).millisecond(0);

    // get the time only of the start and end date of the therapist - this will be used for filtering the office hours for a day
    const therapistStartTime = therapistStartDate.format('HH:mm:00');
    const therapistEndTime = therapistEndDate.format('HH:mm:00');

    // get the day of the start and end date of the therapist - this will be used for filtering the office hours for a day
    // when patient time is converted it could overlap with two different days of the therapist
    // so we need to check that and get the necessary office hours
    const therapistStartDay = therapistStartDate.format('dddd').toLowerCase();
    const therapistEndDay = therapistEndDate.format('dddd').toLowerCase();

    // for calendar - this will be used for fetching the calendar events
    const startOfDay = therapistStartDate.millisecond(0).toISOString()
    const endOfDay = therapistEndDate.millisecond(0).toISOString()

    let availableOfficeHours = therapist.normalOfficeHours;

    // First filter by the day of the week - get the office hours for relevant day
    // if there is no overlap with two days, then we get only the office hours for the specific day
    if (therapistStartDay === therapistEndDay) {
      availableOfficeHours = availableOfficeHours
        .filter((officeHour) => officeHour.workingDay === therapistStartDay)
        .sort((a, b) => a.availabilityHours.localeCompare(b.availabilityHours));
    } else {
      // Separate office hours by start day and end day
      const startDayHours = availableOfficeHours
        .filter((officeHour) => officeHour.workingDay === therapistStartDay)
        .sort((a, b) => a.availabilityHours.localeCompare(b.availabilityHours));

      const endDayHours = availableOfficeHours
        .filter((officeHour) => officeHour.workingDay === therapistEndDay)
        .sort((a, b) => a.availabilityHours.localeCompare(b.availabilityHours));

      // Merge sorted lists: Start day first, then end day
      availableOfficeHours = [...startDayHours, ...endDayHours];
    }

    if (availableOfficeHours.length > 0) {
      availableOfficeHours = availableOfficeHours.filter((officeHour) => {
        if (therapistStartDay === therapistEndDay) {
          return officeHour.availabilityHours >= therapistStartTime && officeHour.availabilityHours <= therapistEndTime;
        } else {
          if (officeHour.workingDay === therapistStartDay) {
            return officeHour.availabilityHours >= therapistStartTime;
          } else {
            return officeHour.availabilityHours <= therapistEndTime;
          }
        }
      });
    }

    // filter by the appointments
    if (availableOfficeHours.length > 0 && therapist.therapistAppointments.length > 0) {
      let appointments = therapist.therapistAppointments;
      // const remainingOfficeHourIds = availableOfficeHours.map((officeHour) => officeHour.id);

      // // filter appointments by the remaining office hours
      // appointments = appointments.filter((appointment) => remainingOfficeHourIds.includes(appointment.workingHourId));

      // Filter appointments that fall within the start and end date
      appointments = appointments.filter((appointment) => {
        const appointmentTime = dayjs(appointment.appointmentDate).tz(therapistTimeZone);

        return (
          appointmentTime.isSame(therapistStartDate) ||
          (appointmentTime.isAfter(therapistStartDate) && appointmentTime.isBefore(therapistEndDate)) ||
          appointmentTime.isSame(therapistEndDate)
        );
      });

      if (appointments.length > 0) {
        availableOfficeHours = availableOfficeHours.filter((officeHour) => {
          const [hours, minutes] = officeHour.availabilityHours.split(':');

          // here we are essentially creating an appointment date in utc format by using the therapist date and their office hour
          // we can use this to check whether there is already an appointment for the date and time
          let officeHourDate: dayjs.Dayjs;
          if (therapistStartDay === therapistEndDay) {
            officeHourDate = therapistStartDate.hour(parseInt(hours)).minute(parseInt(minutes)).second(0).millisecond(0);
          } else {
            officeHourDate = officeHour.workingDay === therapistStartDay ?
              therapistStartDate.hour(parseInt(hours)).minute(parseInt(minutes)).second(0).millisecond(0) :
                therapistEndDate.hour(parseInt(hours)).minute(parseInt(minutes)).second(0).millisecond(0);
          }
          
          const isBooked = appointments.some((appointment) => {
            const appointmentStart = dayjs(appointment.appointmentDate).tz(therapistTimeZone).millisecond(0);
            const appointmentEnd = appointmentStart.add(appointment.duration, 'minute');

            // return (
            //   (appointment.workingHourId === officeHour.id &&
            //     appointmentStart.isSame(officeHourDate)) || // Exact match
            //   (officeHourDate.isAfter(appointmentStart) && officeHourDate.isBefore(appointmentEnd)) // Overlap within 1-hour window
            // );
            const officeHourStart = officeHourDate;
            const officeHourEnd = officeHourStart.add(60, 'minute');

            // Check for overlap
            return (
              officeHourStart.isSame(appointmentStart) ||
              (officeHourStart.isBefore(appointmentEnd) &&
              officeHourEnd.isAfter(appointmentStart))
            );
          });
          return !isBooked;
        });
      }
    }

    // if (availableOfficeHours.length > 0) {
    //   availableOfficeHours = availableOfficeHours.filter(
    //     (officeHour) => !officeHour.deletedAt
    //   );
    // }
    
    if (availableOfficeHours.length === 0) return res.send(availableOfficeHours);

    // format the available office hours - this will also help in comparing with the calendar events
    const formattedAvailableOfficeHours: FormattedOfficeHours[] = availableOfficeHours.map((officeHour) => {
      const [hour, minute] = officeHour.availabilityHours.split(':').map(Number);
      const isStartDay = officeHour.workingDay === therapistStartDay;

      const date = isStartDay ? therapistStartDate : therapistEndDate;
      const dateWithTime = date.hour(hour).minute(minute).second(0).millisecond(0);

      const dateTimeUTC = dateWithTime.toISOString();

      let isOnWaitlist = false;
      if (therapist?.therapistWaitlist?.length > 0) {
        isOnWaitlist = true;
      }

      return {
        id: officeHour.id,
        day: officeHour.workingDay,
        patientDateTime: dateWithTime.tz(patientTimeZone).format('ddd, MMM D, YYYY h:mm A'),
        dateTime: dateWithTime.format(), // therapist time zone
        dateTimeUTC,
        therapistTimeZone,
        appointmentMethod: officeHour.appointmentMethod,
        isOnWaitlist,
      };
    });

    let filteredAvailableOfficeHours = formattedAvailableOfficeHours;

    // filter by advance notice time
    if (timeAfterAdvanceNoticeAdjust) {
      filteredAvailableOfficeHours = filteredAvailableOfficeHours.filter((officeHour) =>
        dayjs(officeHour.dateTime).isAfter(timeAfterAdvanceNoticeAdjust)
      );
    }

    if (filteredAvailableOfficeHours.length === 0) return res.send(filteredAvailableOfficeHours);

    // now filtering with calendar events
    // Check if Google calendar is configured then only fetch Google events
    const googleCalendar = therapist.calendars.find((calendar) => calendar.type === 'google')
		const outlookCalendar = therapist.calendars.find((calendar) => calendar.type === 'outlook')

    if (googleCalendar) {
      const { tokens: googleTokens } = googleCalendar?.credentials as any;

      if (googleTokens) {
        const googleEvents = await getGoogleEvents({
          tokens: googleTokens,
          forDate: true,
          startDate: startOfDay,
          endDate: endOfDay,
        })

        if (googleEvents && googleEvents.length > 0) {
          const googleEventsList = googleEvents;

          const googleEventTimes = 
            googleEventsList?.map((event: any) => ({
              start: event?.start?.timeZone === 'UTC'
                ? dayjs.utc(event.start.dateTime).toISOString()
                  : dayjs(event.start.dateTime).toISOString(),
              end: event?.end?.timeZone === 'UTC'
                ? dayjs.utc(event.end.dateTime).toISOString()
                  : dayjs(event.end.dateTime).toISOString(),
            })) || [];

          // Filter out conflicting office hours
          filteredAvailableOfficeHours = filteredAvailableOfficeHours.filter((officeHour) => {
            const officeHourStart = dayjs(officeHour.dateTimeUTC);
            const officeHourEnd = officeHourStart.add(1, 'hour');

            const isOverlapping = googleEventTimes.some((event) => {
              // return dayjs(event.start).isSame(officeHourStart);
              return (
                dayjs(event.start).isBefore(officeHourEnd) &&
                dayjs(event.end).isAfter(officeHourStart)
              );
              // return (
              //   (officeHourStart.isAfter(dayjs(event.start)) || officeHourStart.isSame(dayjs(event.start))) &&
              //   officeHourStart.isBefore(dayjs(event.end))
              // );
            });

            return !isOverlapping;
          });
        }
      }
    }

    // check if filteredAvailableOfficeHours is empty, return as no further filtering is required
    // this means the therapist has events in the google calendar that overlap with the all remaining office hours
    if (filteredAvailableOfficeHours.length === 0) return res.send(filteredAvailableOfficeHours);

    // Check if Outlook calendar is configured then only fetch Outlook events
    if (outlookCalendar) {
      const { tokens: outlookTokens } = outlookCalendar?.credentials as any;

      if (outlookTokens) {
        const outlookEvents = await getOutlookEvents({
          tokens: outlookTokens,
          userId: Number(therapistId),
          forDate: true,
          startDate: startOfDay,
          endDate: endOfDay
        })

        if (outlookEvents && outlookEvents.length > 0) {
          const outlookEventList = outlookEvents;

          const outlookEventTimes = 
            outlookEventList?.map((event: any) => ({
              start: event?.start?.timeZone === 'UTC'
                ? dayjs.utc(event.start.dateTime).toISOString()
                  : dayjs(event.start.dateTime).toISOString(),
              end: event?.end?.timeZone === 'UTC'
                ? dayjs.utc(event.end.dateTime).toISOString()
                  : dayjs(event.end.dateTime).toISOString(),
            })) || [];

          // Filter out conflicting office hours
          filteredAvailableOfficeHours = filteredAvailableOfficeHours.filter((officeHour) => {
            const officeHourStart = dayjs(officeHour.dateTimeUTC);
            const officeHourEnd = officeHourStart.add(1, 'hour');
            
            const isOverlapping = outlookEventTimes.some((event: any) => {
              return (
                dayjs(event.start).isBefore(officeHourEnd) &&
                dayjs(event.end).isAfter(officeHourStart)
              );
              // return (
              //   (officeHourStart.isAfter(dayjs(event.start)) || officeHourStart.isSame(dayjs(event.start))) &&
              //   officeHourStart.isBefore(dayjs(event.end))
              // );
            });

            return !isOverlapping;
          });
        }
      }
    }
    logger.info('Returning available hours', JSON.stringify({ therapistId, count: therapist.normalOfficeHours.length }));

    return res.send(filteredAvailableOfficeHours);
  })
);

export default router;