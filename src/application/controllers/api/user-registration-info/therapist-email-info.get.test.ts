import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import { User } from '@/src/models';
import logger from '@/src/configs/logger.config';
import * as EncryptionHelper from '@/src/application/helpers/encryption.helper';
import dayjs from 'dayjs';
import {
  INVALID_REQUEST,
  USER_NOT_FOUND,
  CODE_EXPIRED,
  CODE_MISMATCH,
  ID_MISMATCH,
  NO_VERIFICATION_CODE
} from '@/src/application/repositories/constants';

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Controller path
const controllerPath = '@/src/application/controllers/api/user-registration-info/therapist-email-info.get';

describe('API Therapist Email Info Get Controller', function() {
  let sandbox: sinon.SinonSandbox;
  let userFindOneStub: sinon.SinonStub;
  let userUpdateStub: sinon.SinonStub;
  let decryptedB64Stub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;

  // Test data
  const userId = '123';
  const validCode = 'valid-code';
  const invalidCode = 'invalid-code';
  const expiredTimestamp = dayjs().subtract(1, 'hour').unix().toString();
  const futureTimestamp = dayjs().add(1, 'hour').unix().toString();
  
  // Mock user data
  const mockUser = {
    id: userId,
    firstname: 'John',
    lastname: 'Doe',
    email: '<EMAIL>',
    password: 'hashedPassword',
    userToken: 'user-token',
    passwordResetToken: 'encrypted-token',
    update: sinon.stub().resolves(true)
  };

  before(function() {
    // Set up the routes
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userFindOneStub = sandbox.stub(User, 'findOne');
    userUpdateStub = sandbox.stub().resolves(true);
    mockUser.update = userUpdateStub;
    
    // Helper stubs
    decryptedB64Stub = sandbox.stub(EncryptionHelper, 'decryptedB64');
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('GET /login-info/:userId', function() {
    it('should return user data when code is valid', async function() {
      // Setup
      userFindOneStub.resolves(mockUser);
      decryptedB64Stub.returns(`${userId}:${futureTimestamp}:${validCode}`);
      
      // Execute
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${validCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('id', userId);
      expect(response.body).to.have.property('firstname', 'John');
      expect(response.body).to.have.property('lastname', 'Doe');
      expect(userUpdateStub.calledOnce).to.be.true;
      expect(userUpdateStub.firstCall.args[0]).to.deep.include({
        passwordResetToken: null,
        deletedAt: null
      });
      expect(loggerInfoStub.calledWith(`User ${userId} is trying to get their registration info.`)).to.be.true;
    });

    it('should return 403 when userId or code is missing', async function() {
      // Execute without code
      const responseNoCode = await request(app)
        .get(`/api/login-info/${userId}`);

      // Assert
      expect(responseNoCode.status).to.equal(HttpStatusCode.Forbidden);
      expect(responseNoCode.body).to.have.property('message', INVALID_REQUEST);

      // Execute without userId
      const responseNoUserId = await request(app)
        .get(`/api/login-info/?code=${validCode}`);

      // Assert
      expect(responseNoUserId.status).to.equal(HttpStatusCode.NotFound);
    });

    it('should return 404 when user is not found', async function() {
      // Setup
      userFindOneStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${validCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.NotFound);
      expect(response.body).to.have.property('message', USER_NOT_FOUND);
    });

    it('should return 403 when passwordResetToken is missing', async function() {
      // Setup
      const userWithoutToken = { ...mockUser, passwordResetToken: null };
      userFindOneStub.resolves(userWithoutToken);
      
      // Execute
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${validCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', NO_VERIFICATION_CODE);
    });

    it('should return 403 when user ID does not match', async function() {
      // Setup
      userFindOneStub.resolves(mockUser);
      decryptedB64Stub.returns(`999:${futureTimestamp}:${validCode}`); // Different user ID
      
      // Execute
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${validCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', ID_MISMATCH);
    });

    it('should return 403 when code has expired', async function() {
      // Setup
      userFindOneStub.resolves(mockUser);
      decryptedB64Stub.returns(`${userId}:${expiredTimestamp}:${validCode}`); // Expired timestamp
      
      // Execute
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${validCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', CODE_EXPIRED);
    });

    it('should return 403 when code does not match', async function() {
      // Setup
      userFindOneStub.resolves(mockUser);
      decryptedB64Stub.returns(`${userId}:${futureTimestamp}:${validCode}`);
      
      // Execute with invalid code
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${invalidCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', CODE_MISMATCH);
    });

    it('should handle errors properly', async function() {
      // Setup
      userFindOneStub.rejects(new Error('Database error'));
      
      // Execute
      const response = await request(app)
        .get(`/api/login-info/${userId}?code=${validCode}`);

      // Assert
      expect(response.status).to.equal(HttpStatusCode.InternalServerError);
    });
  });
});
