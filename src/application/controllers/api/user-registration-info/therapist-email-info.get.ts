import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import logger from '@/src/configs/logger.config'
import { Calendar, User, UserRegistrationInfo } from '@/src/models'
import { decryptedB64 } from '@/src/application/helpers/encryption.helper'
import dayjs from 'dayjs'
import {
    INVALID_REQUEST,
    USER_NOT_FOUND,
    CODE_EXPIRED,
    CODE_MISMATCH,
    ID_MISMATCH,
    NO_VERIFICATION_CODE
} from '@/src/application/repositories/constants'

const router = express.Router()

/**
 * Get the user with login info
 */
router.get(
    '/login-info/:userId',
    wrap(async (req: Request, res: Response) => {
        const { userId } = req.params;
        const { code: receivedCode } = req.query;
        if (!userId || !receivedCode) return res.forbidden(INVALID_REQUEST);

        logger.info(`User ${userId} is trying to get their registration info.`);

        const user = await User.findOne({
            where: {
                id: userId,
            },
            attributes: ['id', 'firstname', 'lastname', 'email', 'password', 'userToken', 'passwordResetToken'],
        });

        if (!user) return res.notFound(USER_NOT_FOUND);
        if (!user.passwordResetToken) return res.forbidden(NO_VERIFICATION_CODE);

        const [id, expiration, code] = decryptedB64(user.passwordResetToken).split(':');

        if (Number(user.id) !== Number(id)) return res.forbidden(ID_MISMATCH);
        if (dayjs().unix() > Number(expiration)) return res.forbidden(CODE_EXPIRED);
        if (receivedCode !== code) return res.forbidden(CODE_MISMATCH);

        await user.update({
            passwordResetToken: null,
            emailVerifiedAt: dayjs().toDate(),
            deletedAt:null,
        });

        return res.send(user);
    })
);

export default router