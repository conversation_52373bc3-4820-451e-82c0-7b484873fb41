import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import logger from '@/src/configs/logger.config'
import { Calendar, User, UserRegistrationInfo } from '@/src/models'
import { decryptedB64 } from '@/src/application/helpers/encryption.helper'
import dayjs from 'dayjs'

const router = express.Router()

/**
 * Get the user with registration info and calendars
 */
router.get(
	'/registration-info/:userId',
	wrap(async (req: Request, res: Response) => {
		const { userId } = req.params;
		const { code: receivedCode } = req.query;
		if (!userId || !receivedCode) return res.forbidden('Invalid Request. User ID and code are required');

		logger.info(`User ${userId} is trying to get their registration info.`);

		const user = await User.findOne({
			where: {
				id: userId,
			},
			attributes: ['id', 'firstname', 'lastname', 'email', 'password', 'userToken', 'passwordResetToken'],
			include: [
				{
					model: UserRegistrationInfo,
					as: 'registrationInfo',
					attributes: ['pageName', 'payloadInfo'],
				},
				{
					model: Calendar,
					as: 'calendars',
					attributes: ['type', 'email'],
				}
			]
		});

		if (!user) return res.notFound('User not found');
		if (!user.passwordResetToken) return res.forbidden('Invalid Request. Please request a new verification code.');

		const [id, expiration, code] = decryptedB64(user.passwordResetToken).split(':')

		if (Number(user.id) !== Number(id)) return res.forbidden('Invalid Request. User ID does not match.');

		if (dayjs().unix() > Number(expiration)) return res.forbidden('Invalid Request. Code has expired.');

		if (receivedCode !== code) return res.forbidden('Invalid Request. Code does not match.');

		await user.update({
			passwordResetToken: null,
			emailVerifiedAt: dayjs().toDate(),
		});

		return res.send(user);
	})
);

export default router