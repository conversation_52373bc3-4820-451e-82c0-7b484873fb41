import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';

// Create the app instance
const app = express();
app.use(express.json());

// Controller paths
const indexPath = '@/src/application/controllers/api/user-registration-info/index';
const registrationInfoPath = '@/src/application/controllers/api/user-registration-info/registration-info.get';
const therapistEmailInfoPath = '@/src/application/controllers/api/user-registration-info/therapist-email-info.get';

describe('API User Registration Info Index Controller', function() {
  let sandbox: sinon.SinonSandbox;
  let registrationInfoRouterStub: express.Router;
  let therapistEmailInfoRouterStub: express.Router;

  before(function() {
    // Create mock routers for the sub-controllers
    registrationInfoRouterStub = express.Router();
    registrationInfoRouterStub.get('/registration-info/:userId', (req, res) => {
      res.status(HttpStatusCode.Ok).send({ message: 'Mock registration info endpoint' });
    });
    
    therapistEmailInfoRouterStub = express.Router();
    therapistEmailInfoRouterStub.get('/login-info/:userId', (req, res) => {
      res.status(HttpStatusCode.Ok).send({ message: 'Mock login info endpoint' });
    });
    
    // Stub the sub-controller modules
    require.cache[require.resolve(registrationInfoPath)] = {
      exports: registrationInfoRouterStub
    } as NodeModule;
    
    require.cache[require.resolve(therapistEmailInfoPath)] = {
      exports: therapistEmailInfoRouterStub
    } as NodeModule;

    // Set up the routes with our middleware
    app.use('/api', require(indexPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(indexPath)] = undefined;
    require.cache[require.resolve(registrationInfoPath)] = undefined;
    require.cache[require.resolve(therapistEmailInfoPath)] = undefined;
  });

  describe('Router Configuration', function() {
    it('should properly mount the registration-info router', async function() {
      // Execute
      const response = await request(app)
        .get('/api/registration-info/123?code=test-code');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.equals('Mock registration info endpoint');
    });

    it('should properly mount the login-info router', async function() {
      // Execute
      const response = await request(app)
        .get('/api/login-info/123?code=test-code');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.equals('Mock login info endpoint');
    });
  });
});
