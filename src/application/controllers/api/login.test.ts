import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import authController from '@/src/application/controllers/api/auth.controller';
import { User as UserModel } from '@/src/models';
import * as CryptoUtil from '@/src/cryptoUtil';
import logger from '@/src/configs/logger.config';
import jwt from 'jsonwebtoken';


const app = express();
app.use(express.json());
app.use('/api/auth', authController);


describe('Auth Controller - Login', function () {
 this.timeout(10000);
  let sandbox: sinon.SinonSandbox;
 let userFindOneStub: sinon.SinonStub;
 let hashDataStub: sinon.SinonStub;
 let loggerInfoStub: sinon.SinonStub;
 let loggerErrorStub: sinon.SinonStub;
 let jwtSignStub: sinon.SinonStub;


 const TEST_USER_ID = '213';
 const TEST_EMAIL = '<EMAIL>';
 const TEST_PASSWORD = 'Password123!';
 const TEST_HASHED_EMAIL = 'hashed_email_value';
 const TEST_ACCESS_TOKEN = 'mocked-access-token';
 const TEST_REFRESH_TOKEN = 'mocked-refresh-token';


 beforeEach(function() {
   sandbox = sinon.createSandbox();
  
   userFindOneStub = sandbox.stub(UserModel, 'findOne');
   hashDataStub = sandbox.stub(CryptoUtil, 'hashData').returns(TEST_HASHED_EMAIL);
   loggerInfoStub = sandbox.stub(logger, 'info');
   loggerErrorStub = sandbox.stub(logger, 'error');
  
   jwtSignStub = sandbox.stub(jwt, 'sign');
   jwtSignStub.callsFake(() => TEST_REFRESH_TOKEN);
 });


 afterEach(function() {
   sandbox.restore();
 });


 describe('POST /login', function() {
   it('should return 404 when user is not found', async function() {
     userFindOneStub.resolves(null);


     const response = await request(app)
       .post('/api/auth/login')
       .send({
         email: TEST_EMAIL,
         password: TEST_PASSWORD
       });


     expect(response.status).to.equal(HttpStatusCode.NotFound);
     expect(response.body).to.have.property('message', 'User not found');
   });



   it('should handle missing email field in request', async function() {
     const response = await request(app)
       .post('/api/auth/login')
       .send({
         password: TEST_PASSWORD
       });


     expect(response.status).to.equal(HttpStatusCode.NotFound);
     expect(response.body).to.have.property('message');
   });


   it('should handle missing password field in request', async function() {
     const response = await request(app)
       .post('/api/auth/login')
       .send({
         email: TEST_EMAIL
       });


     expect(response.status).to.equal(HttpStatusCode.NotFound);
     expect(response.body).to.have.property('message');
   });


   it('should handle invalid email format', async function() {
     const response = await request(app)
       .post('/api/auth/login')
       .send({
         email: 'invalid-email',
         password: TEST_PASSWORD
       });


     expect(response.status).to.equal(HttpStatusCode.NotFound);
     expect(response.body).to.have.property('message');
   });



   it('should handle database errors when finding user', async function() {
     const dbError = new Error('Database error');
     userFindOneStub.rejects(dbError);


     const response = await request(app)
       .post('/api/auth/login')
       .send({
         email: TEST_EMAIL,
         password: TEST_PASSWORD
       });


     expect(response.status).to.equal(HttpStatusCode.InternalServerError);
     expect(response.body).to.have.property('message');
   });
 });
});