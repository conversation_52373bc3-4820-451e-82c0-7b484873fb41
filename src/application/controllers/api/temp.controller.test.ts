import express from 'express';
import request from 'supertest';
import sinon from 'sinon';
import { expect } from 'chai';
import * as AWS from 'aws-sdk';
import { response } from '@/src/application/helpers/response.helper';

import uploadFileRoutes from '@/src/application/controllers/api/temp.controller';
import * as helperUtils from '@/src/application/helpers/general.helper';
import { FileMimeType } from '@/src/application/helpers/constant.helper';

describe('POST /upload-file', () => {
  let sandbox: sinon.SinonSandbox;
  let s3UploadStub: sinon.SinonStub;

  const app = express();
  app.use(response);
  app.use(express.json());
  app.use('/api/v1', uploadFileRoutes);

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Stub S3 upload
    s3UploadStub = sandbox.stub(AWS.S3.prototype, 'upload').returns({
      promise: () =>
        Promise.resolve({
          Location: 'https://bucket.s3.amazonaws.com/temp/testfile.png',
          Key: 'temp/testfile.png',
          Bucket: 'next-therapist',
        }),
    } as any);

    sandbox.stub(helperUtils, 'generateString').returns('testfile');

    FileMimeType['image/png'] = 'png';
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should upload file and return response', async () => {
    const res = await request(app)
      .post('/api/v1/upload-file')
      .attach('file', Buffer.from('test file content'), {
        filename: 'example.png',
        contentType: 'image/png',
      });

    expect(res.status).to.equal(200);
    expect(res.body.uploaded).to.be.an('array').that.has.lengthOf(1);
    expect(res.body.uploaded[0]).to.include({
      Key: 'temp/testfile.png',
      Bucket: 'next-therapist',
      originalName: 'example.png',
    });
    expect(s3UploadStub.calledOnce).to.be.true;
  });

  it('should return 403 if no file uploaded', async () => {
    const res = await request(app)
      .post('/api/v1/upload-file');

    expect(res.status).to.equal(403);
    expect(res.body.message).to.equal('No files uploaded');
  });
});
