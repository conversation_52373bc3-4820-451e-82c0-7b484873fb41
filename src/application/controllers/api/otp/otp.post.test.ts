import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { User } from '@/src/models';
import { HttpStatusCode } from 'axios';
import logger from '@/src/configs/logger.config';
import * as TwilioConfig from '@/src/configs/twilio.config';
import { 
  MOBILE_NUMBER_REQUIRED,
  USER_NOT_FOUND,
  OTP_SENT_SUCCESSFULLY,
  FAILED_TO_SEND_OTP,
  MOBILE_NUMBER_REQUIRED_MESSAGE,
  OTP_NOT_FOUND,
  INVALID_OTP,
  OTP_VERIFIED_MESSAGE,
  FAILED_VERIFY_OTP 
} from '../constants';

// Define test constants
const TEST_USER_ID = '123';
const TEST_MOBILE_NUMBER = '+1234567890';
const TEST_COUNTRY_CODE = '+1';
const TEST_OTP = 123456;

// Mock User model instance
const mockUser = {
  id: TEST_USER_ID,
  email: '<EMAIL>',
  firstname: '<PERSON>',
  lastname: 'Doe',
  otp: TEST_OTP,
  otpExpiresAt: new Date(Date.now() + 60 * 1000),
  update: sinon.stub().resolves(true)
} as any;

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create API middleware
const apiMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Set up user for the request
  req.user = { id: TEST_USER_ID } as any;
  // Add authorization header
  req.headers.authorization = 'Bearer test_token';
  next();
};

// Mock the middleware
const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
const originalApiMiddleware = require(apiMiddlewarePath).default;

// Create the app instance and set up middleware
const app = express();
app.use(express.json());

// Controller path - will be required in before() hook
const controllerPath = '@/src/application/controllers/api/otp/otp.post';

describe('API OTP Controller', function() {
  this.timeout(5000);

  let sandbox: sinon.SinonSandbox;
  let userFindOneStub: sinon.SinonStub;
  let sendSmsStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let mathRandomStub: sinon.SinonStub;

  before(function() {
    require(apiMiddlewarePath).default = apiMiddleware;
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userFindOneStub = sandbox.stub(User, 'findOne');
    
    // Twilio stub
    sendSmsStub = sandbox.stub(TwilioConfig, 'sendSms').resolves();
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    // Math.random stub to ensure consistent OTP generation
    mathRandomStub = sandbox.stub(Math, 'random').returns(0.123456);
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    require.cache[apiMiddlewarePath] = undefined;
    require(apiMiddlewarePath).default = originalApiMiddleware;
  });

  describe('POST /send-otp', function() {
    it('should return 400 if mobile number is missing', async function() {
      // Execute - missing mobile number
      const response = await request(app)
        .post('/api/send-otp')
        .send({});

      // Assert
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('message').that.equals(MOBILE_NUMBER_REQUIRED);
    });

    it('should return 404 if user is not found', async function() {
      // Setup
      userFindOneStub.resolves(null);

      // Execute
      const response = await request(app)
        .post('/api/send-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER });

      // Assert
      expect(response.status).to.equal(404);
      expect(response.body).to.have.property('message').that.equals(USER_NOT_FOUND);
      expect(userFindOneStub.calledOnce).to.be.true;
    });

    it('should send OTP successfully', async function() {
      // Setup
      userFindOneStub.resolves(mockUser);

      // Execute
      const response = await request(app)
        .post('/api/send-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER });

      // Assert
      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('message').that.equals(OTP_SENT_SUCCESSFULLY);
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(mockUser.update.calledOnce).to.be.true;
      expect(sendSmsStub.calledOnce).to.be.true;
      expect(loggerInfoStub.calledOnce).to.be.true;
    });

    it('should handle errors during OTP sending', async function() {
      // Setup
      userFindOneStub.resolves(mockUser);
      sendSmsStub.rejects(new Error('SMS sending failed'));

      // Execute
      const response = await request(app)
        .post('/api/send-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER });

      // Assert
      expect(response.status).to.equal(500);
      expect(response.body).to.have.property('message').that.equals(FAILED_TO_SEND_OTP);
      expect(loggerErrorStub.calledOnce).to.be.true;
    });
  });

  describe('POST /verify-otp', function() {
    it('should return 400 if mobile number or OTP is missing', async function() {
      // Execute - missing mobile number and OTP
      const response = await request(app)
        .post('/api/verify-otp')
        .send({});

      // Assert
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('message').that.equals(MOBILE_NUMBER_REQUIRED_MESSAGE);
    });

    it('should return 404 if user is not found', async function() {
      // Setup
      userFindOneStub.resolves(null);

      // Execute
      const response = await request(app)
        .post('/api/verify-otp')
        .send({ 
          mobileNumber: TEST_MOBILE_NUMBER,
          otp: TEST_OTP,
          countryCode: TEST_COUNTRY_CODE
        });

      // Assert
      expect(response.status).to.equal(404);
      expect(response.body).to.have.property('message').that.equals(USER_NOT_FOUND);
      expect(userFindOneStub.calledOnce).to.be.true;
    });

    it('should return 400 if OTP is expired or not found', async function() {
      // Setup - user with expired OTP
      const userWithExpiredOtp = {
        ...mockUser,
        otpExpiresAt: new Date(Date.now() - 60 * 1000) // Expired 1 minute ago
      };
      userFindOneStub.resolves(userWithExpiredOtp);

      // Execute
      const response = await request(app)
        .post('/api/verify-otp')
        .send({ 
          mobileNumber: TEST_MOBILE_NUMBER,
          otp: TEST_OTP,
          countryCode: TEST_COUNTRY_CODE
        });

      // Assert
      expect(response.status).to.equal(400);
      expect(response.body).to.have.property('message').that.equals(OTP_NOT_FOUND);
      expect(userFindOneStub.calledOnce).to.be.true;
    });

    // it('should return 400 if OTP is invalid', async function() {
    //   // Setup
    //   userFindOneStub.resolves(mockUser);

    //   // Execute - wrong OTP
    //   const response = await request(app)
    //     .post('/api/verify-otp')
    //     .send({ 
    //       mobileNumber: TEST_MOBILE_NUMBER,
    //       otp: 999999, // Different from TEST_OTP
    //       countryCode: TEST_COUNTRY_CODE
    //     });

    //   // Assert
    //   expect(response.status).to.equal(400);
    //   expect(response.body).to.have.property('message').that.equals(INVALID_OTP);
    //   expect(userFindOneStub.calledOnce).to.be.true;
    // });

    // it('should handle errors during OTP verification', async function() {
    //   // Setup
    //   userFindOneStub.resolves(mockUser);
    //   mockUser.update.rejects(new Error('Database update failed'));

    //   // Execute
    //   const response = await request(app)
    //     .post('/api/verify-otp')
    //     .send({ 
    //       mobileNumber: TEST_MOBILE_NUMBER,
    //       otp: TEST_OTP,
    //       countryCode: TEST_COUNTRY_CODE
    //     });

    //   // Assert
    //   expect(response.status).to.equal(500);
    //   expect(response.body).to.have.property('message').that.equals(FAILED_VERIFY_OTP);
    //   expect(loggerErrorStub.calledOnce).to.be.true;
    // });
  });
});
