import express, { Request, Response } from 'express';
import { wrap } from '@/src/application/helpers';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import logger from '@/src/configs/logger.config';
import { sendSms } from '@/src/configs/twilio.config';
import User from '@/src/models/user.model';
import { MOBILE_NUMBER_REQUIRED,USER_NOT_FOUND,OTP_SENT_MESSAGE,OTP_SENT_SUCCESSFULLY,FAILED_TO_SEND_OTP,MOBILE_NUMBER_REQUIRED_MESSAGE,OTP_NOT_FOUND,INVALID_OTP,OTP_VERIFIED_MESSAGE,FAILED_VERIFY_OTP } from '../constants'

const router = express.Router();

/********************************
 * * Send OTP to a mobile number
 ********************************/
router.post(
    '/send-otp',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        try {
            const { mobileNumber } = req.body;
            const userId = req.user?.id;

            if (!mobileNumber) {
                return res.status(400).send({ message: MOBILE_NUMBER_REQUIRED });
            }

            const otp = Math.floor(100000 + Math.random() * 900000);

            const otpExpiresAt = new Date(Date.now() + 60 * 1000);

            const user = await User.findOne({ where: { id:userId } });
            if (!user) {
                return res.status(404).send({ message: USER_NOT_FOUND });
            }

            await user.update({ otp, otpExpiresAt });

            const messageBody = `${otp} ${OTP_SENT_MESSAGE}`;

            await sendSms(mobileNumber, messageBody);

            logger.info(`OTP sent successfully to ${mobileNumber}`);

            return res.status(200).send({
                message: OTP_SENT_SUCCESSFULLY,
            });
        } catch (error) {
            logger.error(`Failed to send OTP: ${error}`);
            return res.status(500).send({ message: FAILED_TO_SEND_OTP, error });
        }
    })
);

router.post(
    '/verify-otp',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        try {
            const { mobileNumber, otp ,countryCode } = req.body;
            const userId = req.user?.id;

            if (!mobileNumber || !otp) {
                return res.status(400).send({ message: MOBILE_NUMBER_REQUIRED_MESSAGE });
            }

            const user = await User.findOne({ where: { id: userId } });
            if (!user) {
                return res.status(404).send({ message: USER_NOT_FOUND });
            }

            if (!user.otp || !user.otpExpiresAt || new Date() > user.otpExpiresAt) {
                return res.status(400).send({ message: OTP_NOT_FOUND });
            }

            if (user.otp !== otp) {
                return res.status(400).send({ message: INVALID_OTP });
            }

            await user.update({
                phone: mobileNumber,
                country_code: countryCode,
                otp: null,
                otpExpiresAt: null,
                phoneVerifiedAt: new Date(),
            });

            logger.info(`Phone number updated successfully for user ID: ${userId}`);

            return res.status(200).send({ message: OTP_VERIFIED_MESSAGE });
        } catch (error) {
            logger.error(`Failed to verify OTP: ${error}`);
            return res.status(500).send({ message: FAILED_VERIFY_OTP, error});
        }
    })
);
export default router;