import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { User } from '@/src/models';
import { UserType } from '@/src/application/helpers/constant.helper';
import * as TwilioConfig from '@/src/configs/twilio.config';
import logger from '@/src/configs/logger.config';

// Define test constants
const TEST_USER_ID = '123';
const TEST_EMAIL = '<EMAIL>';
const TEST_MOBILE_NUMBER = '+**********';
const TEST_OTP = 123456;

// Mock User model instance
const mockUser = {
  id: TEST_USER_ID,
  email: TEST_EMAIL,
  firstname: '<PERSON>',
  lastname: '<PERSON><PERSON>',
  emailVerifiedAt: new Date(),
  role: UserType.PATIENT,
  otp: TEST_OTP,
  otpExpiresAt: new Date(Date.now() + 60 * 1000), // OTP expires in 1 minute
  update: sinon.stub().resolves(true)
} as any;

// Create API middleware
const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
const originalApiMiddleware = require(apiMiddlewarePath).default;

// Mock the middleware
const apiMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Set up user for the request
  req.user = mockUser;
  next();
};

describe('OTP Router Integration', function() {
  this.timeout(5000);
  
  let sandbox: sinon.SinonSandbox;
  let app: express.Express;
  let userFindOneStub: sinon.SinonStub;
  let sendSmsStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Override the middleware
    require(apiMiddlewarePath).default = apiMiddleware;
    
    // Model stubs
    userFindOneStub = sandbox.stub(User, 'findOne').resolves(mockUser);
    
    // SMS stub
    sendSmsStub = sandbox.stub(TwilioConfig, 'sendSms').resolves();
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    // Create a fresh app for each test
    app = express();
    app.use(express.json());
    app.use('/api/otp', require('@/src/application/controllers/api/otp').default);
  });

  afterEach(function() {
    sandbox.restore();
    // Restore original middleware
    require(apiMiddlewarePath).default = originalApiMiddleware;
  });

  describe('Router Configuration', function() {
    it('should properly mount the OTP routes', async function() {
      // Test the send-otp route is properly mounted
      const sendOtpResponse = await request(app)
        .post('/api/otp/send-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER });
      
      // Assert the route is accessible (not checking full functionality as that's in the otp.post.test.ts)
      expect(sendOtpResponse.status).to.be.oneOf([200, 400, 404, 500]);
      
      // Test the verify-otp route is properly mounted
      const verifyOtpResponse = await request(app)
        .post('/api/otp/verify-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER, otp: TEST_OTP });
      
      // Assert the route is accessible
      expect(verifyOtpResponse.status).to.be.oneOf([200, 400, 404, 500]);
    });
    
    it('should respond with 404 for non-existent routes', async function() {
      // Test a route that doesn't exist
      const response = await request(app)
        .post('/api/otp/non-existent-route')
        .send({});
      
      // Assert
      expect(response.status).to.equal(404);
    });
  });

  describe('Express App Configuration', function() {
    it('should use JSON middleware', async function() {
      // Send a request with a JSON body
      const response = await request(app)
        .post('/api/otp/send-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER });
      
      // If JSON middleware is working, the request body should be parsed correctly
      expect(userFindOneStub.called).to.be.true;
    });
    
    it('should handle errors properly', async function() {
      // Force an error by making the User.findOne throw
      userFindOneStub.throws(new Error('Forced database error'));
      
      // Send a request that will trigger the error
      const response = await request(app)
        .post('/api/otp/send-otp')
        .send({ mobileNumber: TEST_MOBILE_NUMBER });
      
      // Assert
      expect(response.status).to.equal(500);
      expect(loggerErrorStub.called).to.be.true;
    });
  });
});
