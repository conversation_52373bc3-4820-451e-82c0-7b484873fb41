import { Router, Request, Response } from 'express'
import {
	decryptedB64,
	encryptedB64,
} from '@/src/application/helpers/encryption.helper'
import {
	LoginValidator,
	UserRegisterValidator,
	ForgotPasswordValidator,
	VerifyEmailValidator,
	ResetPasswordValidator,
	MfaValidator
} from '@/src/application/validators'
import { wrap } from '@/src/application/helpers'
import { User } from '@/src/models'
import {
	BadRequestError,
	NotFoundError,
} from '@/src/application/handlers/errors'
import { HttpStatusCode } from 'axios'
import { 
	forgotPassword,
	resetPassword,
	emailVerification,
	verifyEmail,
	registerPatient,
	registerPatientMinor,
	verifyPatientEmail,
	resendMFA,
	verifyMFA,
	sendMfaCode,
	loginWithoutMfa
} from '@/src/application/repositories/auth.repository'
import logger from '@/src/configs/logger.config'
import { USER_NOT_FOUND } from '../../repositories/constants'
import { hashData } from '@/src/cryptoUtil'


const router = Router()

/********************************
 * * Login user
 * @param email
 * @param password
 * @returns message
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/login',
	wrap(async (req: Request, res: Response) => {
		logger.info(`LOGIN ATTEMPT: Device: ${req.headers['device-type'] || 'Unknown'}`);
		const hash_email =  hashData(req.body.email);
		const checkUser = await User.findOne({
			where: {
				email_hash:hash_email,
			}
		});
		if (!checkUser) throw new NotFoundError(USER_NOT_FOUND);
		if (checkUser.mfaEnabled) {
			const { message, user } = await sendMfaCode(req);
			if (user?.id) {
				await User.update(
					{ version: 1 },
					{ where: { id: user.id } } 
				);
			}		
			logger.info(`MFA code sent successfully to user ${user.id}`)
			res.status(201).send({
				message
			});
		} else {
			const { accessToken, refreshToken, user } = await loginWithoutMfa(req, (req.headers['device-type'] as string) || 'Unknown');		
			if (user?.id) {
				await User.update(
					{ version: 1 },
					{ where: { id: user.id } }
				);
			}

			logger.info(`User ${user?.id} logged in successfully`);

			res.send({
				accessToken,
				refreshToken,
				user,
			});
		}
	})
);

/**
 * @desc Resends the MFA code via email
 * @param email
 * @returns success message
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 */
router.post(
	"/resend-mfa",
	wrap(async (req: Request, res: Response) => {
	  logger.info(`MFA Resend initiated for email ${req.body.email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
	  const { message } = await resendMFA(req);
	  res.send({ message });
	})
  );
  

/**
 * @desc Verifies MFA code and generates authentication tokens
 * @param email
 * @param code // mfa code
 * @returns user
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 */
router.post(
	'/verify-mfa',
	MfaValidator,
	wrap(async (req: Request, res: Response) => {
		logger.info(`MFA Verification initiated`);
		const { accessToken, refreshToken, user } = await verifyMFA(req, (req.headers['device-type'] as string) || 'Unknown');		
		if (user?.id) {
			await User.update(
				{ version: 1 },
				{ where: { id: user.id } }
			);
		}

		logger.info(`User ${user?.id} logged in successfully`);

		res.send({
			accessToken,
			refreshToken,
			user,
		});
	})
  );


/********************************
 * * Register User
 * @param refreshToken
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/register',
	UserRegisterValidator,
	wrap(async (req: Request, res: Response) => {
		await registerPatient(req);
		
		return res.status(HttpStatusCode.Created).send({
			message: 'Registration Successful!',
		})
	})
)
/********************************
 * * Register Patient with minor
 * @param {Request} req - Express request object containing user details in the body.
 * @param {Response} res - Express response object.
 * @param {string | null} device - The device type (either 'api' or 'web').
 * @throws {BadRequestError} If any required field is missing or invalid.
 * @throws {UnprocessableEntityError} If a user with the same email already exists.
 * @returns {Promise<Object>} Registration success response containing access token, refresh token, user details, and minor patients (if applicable).
 */
router.post(
	'/register-patient',
	UserRegisterValidator,
	wrap(async (req: Request, res: Response) => {
		const {  user,minorPatients } = await registerPatientMinor(req,res,req.headers['device-type'] as string)
		logger.info(`Registering patient: User ID: ${user?.id}, Has Minor Patients: ${minorPatients?.length > 0 ? 'Yes' : 'No'}`);
		res.send({
			user,
			minorPatients
		});
	})
)

/********************************
 * * Login user after registration
 * @param email
 * @param password
 * @returns user
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/patient-direct-login',
	LoginValidator,
    wrap(async (req: Request, res: Response) => {
        const { accessToken, refreshToken, user } = await verifyPatientEmail (req, req.headers['device-type'] as string);
        
        if (user?.id) {
            await User.update(
                { version: 1 },
                { where: { id: user.id } }
            );
        }       
        res.send({
            accessToken,
            refreshToken,
            user,
        });
    })
);

/********************************
 * * Email Verification
 * @param email
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/email-verification',
	ForgotPasswordValidator,
	wrap(async (req: Request, res: Response) => {
		const { message } = await emailVerification(req);
		return res.send({
			message,
		})
	})
)

/********************************
 * * Verify email
 * @param token
 * @returns message
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/verify-email',
	VerifyEmailValidator,
	wrap(async (req: Request, res: Response) => {
		const { message , role} = await verifyEmail(req);
		return res.status(HttpStatusCode.Created).send({
			message,
			role
		})
	})
)

/********************************
 * * Forgot password
 * @param email
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/forgot-password',
	ForgotPasswordValidator,
	wrap(async (req: Request, res: Response) => {
		const { message } = await forgotPassword(req);
		res.status(HttpStatusCode.Created).send({ message });
	})
)

/********************************
 * * Reset password
 * @param password
 * @param token
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/reset-password',
	ResetPasswordValidator,
	wrap(async (req: Request, res: Response) => {
		const { message } = await resetPassword(req);
		return res.status(HttpStatusCode.Created).send({
			message,
		})
	})
)

/********************************
 * * Refresh token
 * @param refreshToken
 * @returns accessToken
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/refresh',
	wrap(async (req: Request, res: Response) => {
		const { refreshToken } = req.body
		const token = req.headers.authorization?.split(' ')[1] ?? ''

		if (!token) throw new BadRequestError('Access token is required')

		try {
			const [data, id] = decryptedB64(refreshToken).split(':')
			if (token === data) {
				const user = await User.findByPk(id)
				if (!user) throw new NotFoundError('User not found')
				const accessToken = user.generateToken('api', 168)
				const refreshToken = encryptedB64(`${accessToken}:${user.id}`)

				res.send({
					accessToken,
					refreshToken,
					user
				})
			} else {
				throw new BadRequestError('Invalid token')
			}
		} catch (error) {
			logger.error(`Failed to refresh token: ${error}`);
			throw new BadRequestError('Invalid token')
		}
	})
)

/********************************
 * * Get Profile
 * @returns therapist
 * @throws NotFoundError
 ********************************/
// router.get(
// 	'/profile',
// 	APIMiddleware,
// 	wrap(async (req: Request, res: Response) => {
		// const user = await User.findByPk(req.user?.id, {
		// 	attributes: {
		// 		include: [
		// 			[
		// 				sequelize.literal(
		// 					`case when "calendars".id is not null and "calendars"."type" = 'google' then true else false end`
		// 				),
		// 				'hasGoogleCalendar',
		// 			],
		// 		],
		// 	},
		// 	include: [
		// 		{
		// 			model: Calendar,
		// 			attributes: [],
		// 		},
		// 	],
		// })

		// if (!user) throw new NotFoundError('User not found')

		// return res.send({ data: user })

// 		const profileDetails = await getTherapistDetails(req.user?.id)
// 		res.send(profileDetails)
// 	})
// )

export default router
