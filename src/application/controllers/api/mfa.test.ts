import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import logger from '@/src/configs/logger.config';
import * as authRepository from '@/src/application/repositories/auth.repository';
import authController from '@/src/application/controllers/api/auth.controller';
import { BadRequestError, NotFoundError } from '@/src/application/handlers/errors';

const app = express();
app.use(express.json());
app.use('/api/auth', authController);

describe('Auth Controller - Resend MFA', function () {
  this.timeout(10000);
  
  let sandbox: sinon.SinonSandbox;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let resendMFAStub: sinon.SinonStub;

  const TEST_EMAIL = '<EMAIL>';

  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    resendMFAStub = sandbox.stub(authRepository, 'resendMFA');
  });

  afterEach(function () {
    sandbox.restore();
  });

  describe('POST /resend-mfa', function () {
    it('should successfully resend MFA code', function (done) {
      const resendResponse = { message: 'MFA code resent successfully' };
      resendMFAStub.resolves(resendResponse);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .expect(200)
        .end((err, response) => {
          if (err) return done(err);
          expect(response.body).to.have.property('message', 'MFA code resent successfully');
          expect(loggerInfoStub.called).to.be.true;
          expect(resendMFAStub.calledWith(sinon.match.object)).to.be.true;
          done();
        });
    });

    it('should handle general errors during MFA code resending', function (done) {
      resendMFAStub.rejects(new Error('Failed to resend MFA code'));

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          expect(loggerInfoStub.called).to.be.true;
          // expect(loggerErrorStub.called).to.be.true;
          done();
        });
    });

    it('should handle BadRequestError during MFA code resending', function (done) {
      const badRequestError = new BadRequestError('Invalid MFA code');
      badRequestError.statusCode = HttpStatusCode.BadRequest;
      resendMFAStub.rejects(badRequestError);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.equal(HttpStatusCode.BadRequest);
          expect(response.body).to.have.property('message', 'Invalid MFA code');
          done();
        });
    });

    it('should handle NotFoundError during MFA code resending', function (done) {
      const notFoundError = new NotFoundError('User not found');
      notFoundError.statusCode = HttpStatusCode.NotFound;
      resendMFAStub.rejects(notFoundError);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.equal(HttpStatusCode.NotFound);
          expect(response.body).to.have.property('message', 'User not found');
          done();
        });
    });

    it('should handle missing email in request body', function (done) {
      const errorHandler = (err: any, req: any, res: any, next: any) => {
        res.status(400).json({ message: 'Email is required' });
      };
      app.use(errorHandler);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({})
        .end((err, response) => {
          if (err) return done(err);
          done();
        });
    });

    it('should handle null email in request body', function (done) {
      resendMFAStub.callsFake((req) => {
        if (!req.body || req.body.email === null) {
          const error = new BadRequestError('Email is required');
          error.statusCode = 400;
          throw error;
        }
        return Promise.resolve({ message: 'MFA code resent successfully' });
      });

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: null })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          done();
        });
    });

    it('should handle undefined email in request body', function (done) {
      const reqBody: any = {};
      reqBody.email = undefined;

      resendMFAStub.callsFake((req) => {
        if (!req.body || req.body.email === undefined) {
          const error = new BadRequestError('Email is required');
          error.statusCode = 400;
          throw error;
        }
        return Promise.resolve({ message: 'MFA code resent successfully' });
      });

      request(app)
        .post('/api/auth/resend-mfa')
        .send(reqBody)
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          done();
        });
    });

    it('should handle TypeError when email is undefined', function (done) {
      resendMFAStub.callsFake((req) => {
        if (!req.body || !req.body.email) {
          const error = new BadRequestError('Email is required');
          error.statusCode = 400;
          throw error;
        }
        return Promise.resolve({ message: 'MFA code resent successfully' });
      });

      request(app)
        .post('/api/auth/resend-mfa')
        .send({})
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          done();
        });
    });

    it('should handle TypeError when destructuring undefined response', function (done) {
      resendMFAStub.resolves(undefined);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          done();
        });
    });

    it('should handle token expired error', function (done) {
      const error = new BadRequestError('Token expired');
      error.statusCode = 400;
      resendMFAStub.rejects(error);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.equal(400);
          expect(response.body).to.have.property('message', 'Token expired');
          done();
        });
    });

    it('should handle link already used error', function (done) {
      const error = new NotFoundError('This link has already been used, please generate new link');
      error.statusCode = 404;
      resendMFAStub.rejects(error);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.equal(404);
          expect(response.body).to.have.property('message', 'This link has already been used, please generate new link');
          done();
        });
    });

    it('should handle password mismatch error', function (done) {
      const error = new BadRequestError('Password does not match');
      error.statusCode = 400;
      resendMFAStub.rejects(error);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.equal(400);
          expect(response.body).to.have.property('message', 'Password does not match');
          done();
        });
    });

    it('should handle user not found error', function (done) {
      const error = new NotFoundError('User not found');
      error.statusCode = 404;
      resendMFAStub.rejects(error);

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.equal(404);
          expect(response.body).to.have.property('message', 'User not found');
          done();
        });
    });

    it('should handle email sending failure', function (done) {
      resendMFAStub.rejects(new Error('Failed to send email'));

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          done();
        });
    });

    it('should handle database error', function (done) {
      resendMFAStub.rejects(new Error('Database error'));

      request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL })
        .end((err, response) => {
          if (err) return done(err);
          expect(response.status).to.be.greaterThan(399);
          done();
        });
    });
  });

  describe('Email masking', function () {
    it('should correctly mask standard email format', function () {
      const email = '<EMAIL>';
      const masked = email.replace(/(.{2}).+(@.+)/, "$1***$2");
      expect(masked).to.equal('te***@example.com');
    });

    it('should handle very short email addresses', function () {
      const email = 'a@b.c';
      let masked;
      if (email.indexOf('@') <= 2) {
        masked = email.replace(/@/, "***@");
      } else {
        masked = email.replace(/(.{2}).+(@.+)/, "$1***$2");
      }
      expect(masked).to.equal('a***@b.c');
    });

    it('should handle emails without domain parts', function () {
      const email = 'janeedoe';
      let masked;
      if (email.indexOf('@') === -1) {
        const len = email.length;
        if (len <= 4) {
          masked = email;
        } else {
          masked = email.substring(0, 2) + '***' + email.substring(len - 2);
        }
      } else {
        masked = email.replace(/(.{2}).+(@.+)/, "$1***$2");
      }
      expect(masked).to.equal('ja***oe');
    });
  });
});
