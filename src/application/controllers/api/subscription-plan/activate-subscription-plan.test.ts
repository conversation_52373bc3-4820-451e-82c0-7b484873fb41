import sinon from 'sinon';
import express from 'express';
import { NotifySubscriptionUpdate, SubscriptionPlan, TherapistSubscription, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import subscriptionPlanRoutes from '@/src/application/controllers/api/subscription-plan';
import sequelize from '@/src/configs/database.config';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', subscriptionPlanRoutes);

describe('Subscription Plan Create API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let subPlanFindOneStub: sinon.SinonStub;
	let notifySubUpdateFindAllStub: sinon.SinonStub;
	let notifySubUpdateBulkCreateStub: sinon.SinonStub;
	let therapistSubFindAllStub: sinon.SinonStub;
	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	let dummyUser: any;
	let dummySubscriptionPlan: any;
	let dummyNotifySubUpdate: any;
	let dummyTherapistSub: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		subPlanFindOneStub = sandbox.stub(SubscriptionPlan, 'findOne');
		notifySubUpdateFindAllStub = sandbox.stub(NotifySubscriptionUpdate, 'findAll');
		notifySubUpdateBulkCreateStub = sandbox.stub(NotifySubscriptionUpdate, 'bulkCreate');
		therapistSubFindAllStub = sandbox.stub(TherapistSubscription, 'findAll');

		commitStub = sandbox.stub().resolves();
		rollbackStub = sandbox.stub().resolves();

		sandbox.stub(sequelize, 'transaction').resolves({ 
			commit: commitStub,
			rollback: rollbackStub,
		} as any);
		// commitStub = sandbox.stub(sequelize.transaction.prototype, 'commit').resolves();

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'system',
		}

		dummySubscriptionPlan = {
			id: 1,
			annualPrice: 100,
			monthlyPrice: 10,
			isActive: true,
			createdAt: new Date(),
		}

		dummyNotifySubUpdate = {
			id: 1,
			userId: 1,
			isNotified: false,
		}

		dummyTherapistSub = {
			id: 1,
			subscriptionPlanId: 1,
			therapistId: 2,
			subscriptionType: 'monthly',
			createdAt: new Date(),
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('PUT /api/v1/subscription-plan/activate/:id', function() {
		it('should fail if pending subscription updates found', async function () {
			userFindByPkStub.resolves(dummyUser);
			notifySubUpdateFindAllStub.resolves([dummyNotifySubUpdate]);

			const response = await request(app)
				.put('/api/v1/subscription-plan/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Pending subscription updates found. Please wait for them to be processed before activating a new plan.');
		});

		it('should fail if new subscription plan does not exist', async function () {
			userFindByPkStub.resolves(dummyUser);
			notifySubUpdateFindAllStub.resolves(null);
			subPlanFindOneStub.resolves(null);

			const response = await request(app)
				.put('/api/v1/subscription-plan/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription plan to activate does not exist');
		});

		it('should fail if old subscription plan does not exist', async function () {
			userFindByPkStub.resolves(dummyUser);
			notifySubUpdateFindAllStub.resolves(null);
			subPlanFindOneStub.onFirstCall().resolves(dummySubscriptionPlan);
			subPlanFindOneStub.onSecondCall().resolves(null);

			const response = await request(app)
				.put('/api/v1/subscription-plan/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Previous Active Subscription plan does not exist');
		});

		it('should activate successfully without creating notifications if there are no active subscriptions for the old plan', async function () {
			userFindByPkStub.resolves(dummyUser);
			notifySubUpdateFindAllStub.resolves(null);
			subPlanFindOneStub.resolves({
				...dummySubscriptionPlan,
				update: async () => Promise.resolve(),
			});
			therapistSubFindAllStub.resolves(null);

			const response = await request(app)
				.put('/api/v1/subscription-plan/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(notifySubUpdateBulkCreateStub.notCalled).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'New Subscription plan has been activated successfully.');
		});

		it('should activate successfully and create notifications if there are active subscriptions for the old plan', async function () {
			userFindByPkStub.resolves(dummyUser);
			notifySubUpdateFindAllStub.resolves(null);
			subPlanFindOneStub.resolves({
				...dummySubscriptionPlan,
				update: async () => Promise.resolve(),
			});
			therapistSubFindAllStub.resolves([dummyTherapistSub]);
			notifySubUpdateBulkCreateStub.resolves();

			const response = await request(app)
				.put('/api/v1/subscription-plan/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(notifySubUpdateBulkCreateStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'New Subscription plan has been activated successfully. It will take some time to update the subscriptions for the users and send email notifications.');
		});

		it('should fail and rollback if any error occurs during the transaction of activating new plan', async function () {
			userFindByPkStub.resolves(dummyUser);
			notifySubUpdateFindAllStub.resolves(null);
			subPlanFindOneStub.resolves({
				...dummySubscriptionPlan,
				update: async () => Promise.resolve(),
			});
			therapistSubFindAllStub.resolves([dummyTherapistSub]);
			notifySubUpdateBulkCreateStub.rejects(new Error('Error'));

			const response = await request(app)
				.put('/api/v1/subscription-plan/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(notifySubUpdateBulkCreateStub.calledOnce).to.be.true;
			expect(commitStub.notCalled).to.be.true;
			expect(rollbackStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('message', 'Failed to activate new subscription plan. Please try again later.');
		});

	});
});