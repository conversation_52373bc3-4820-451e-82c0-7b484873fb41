import sinon from 'sinon';
import express from 'express';
import { SubscriptionPlan } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import subscriptionPlanRoutes from '@/src/application/controllers/api/subscription-plan';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', subscriptionPlanRoutes);

describe('Subscription Plan List API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let subPlanFindAndCountAllStub: sinon.SinonStub;

	let dummySubscriptionPlans: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		subPlanFindAndCountAllStub = sandbox.stub(SubscriptionPlan, 'findAndCountAll');

		dummySubscriptionPlans = [
			{
				id: 1,
				annualPrice: 100,
				monthlyPrice: 10,
				isActive: true,
				createdAt: new Date(),
			}
		]
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/v1/subscription-plan', function() {
		it('should return paginated subscription plans list', async function () {
			subPlanFindAndCountAllStub.resolves({
				rows: dummySubscriptionPlans,
				count: dummySubscriptionPlans.length,
			});

			const response = await request(app)
				.get('/api/v1/subscription-plan?type=active')
				.send();
			
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data');
			expect(response.body).to.have.property('meta');
		});

	});
});