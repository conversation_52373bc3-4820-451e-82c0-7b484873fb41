import sinon from 'sinon';
import express from 'express';
import { SubscriptionPlan, TherapistSubscription, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import subscriptionPlanRoutes from '@/src/application/controllers/api/subscription-plan';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', subscriptionPlanRoutes);

describe('Subscription Plan Delete API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let subPlanFindOneStub: sinon.SinonStub;
	let therapistSubFindOneStub: sinon.SinonStub;
	let subPlanDestroyStub: sinon.SinonStub;

	let dummyUser: any;
	let dummySubscriptionPlan: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		subPlanFindOneStub = sandbox.stub(SubscriptionPlan, 'findOne');
		therapistSubFindOneStub = sandbox.stub(TherapistSubscription, 'findOne');
		subPlanDestroyStub = sandbox.stub(SubscriptionPlan.prototype, 'destroy');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'system',
		}

		dummySubscriptionPlan = {
			id: 1,
			annualPrice: 100,
			monthlyPrice: 10,
			isActive: true,
			createdAt: new Date(),
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('DELETE /api/v1/subscription-plan/:id', function() {
		it('should fail if subscription plan not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(null);

			const response = await request(app)
				.delete('/api/v1/subscription-plan/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription plan does not exist');
		});

		it('should fail if subscription plan is being used', async function () {
			userFindByPkStub.resolves(dummyUser);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);
			therapistSubFindOneStub.resolves({
				id: 1,
				subscriptionPlanId: 1,
				subscriptionType: 'monthly',
				createdAt: new Date(),
			});

			const response = await request(app)
				.delete('/api/v1/subscription-plan/1')
				.set('Authorization', 'Bearer accessToken');
			
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Billing has already been done for this subscription plan. You cannot delete it now');
		});

		it('should delete subscription plan successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			subPlanFindOneStub.resolves({
				...dummySubscriptionPlan,
				destroy: subPlanDestroyStub.resolves(),
			});
			therapistSubFindOneStub.resolves(null);

			const response = await request(app)
				.delete('/api/v1/subscription-plan/1')
				.set('Authorization', 'Bearer accessToken');
			
			expect(subPlanDestroyStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Subscription plan deleted successfully');
		});
	});
});