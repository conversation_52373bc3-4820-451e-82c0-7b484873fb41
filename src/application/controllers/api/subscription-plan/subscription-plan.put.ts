import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { HttpStatusCode } from 'axios'
import { SubscriptionPlan, TherapistSubscription } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/**
 * Update subscription plan
 */
router.put(
	'/subscription-plan/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { annualPrice, monthlyPrice } = req.body
		const { id } = req.params

		logger.info(`User: ${req.user?.id} initiated Subscription Plan update for Plan ID: ${id}.`);


		if (!id) throw new ForbiddenError('Invalid request. Please provide id')
		if (!annualPrice || !monthlyPrice) throw new ForbiddenError('Invalid request. Please provide annualPrice and monthlyPrice')

		const plan = await SubscriptionPlan.findOne({
			where: {
				id,
			},
		})

		if (!plan) throw new ForbiddenError('Subscription plan does not exist')

		const therapistSubscription = await TherapistSubscription.findOne({
			where: {
				subscriptionPlanId: id,
			},
		})

		if (therapistSubscription) throw new ForbiddenError('Billing has already been done for this subscription plan. You cannot update it now')

		await plan.update({
			annualPrice,
			monthlyPrice,
		})
		logger.info(`User: ${req.user?.id} successfully updated Subscription Plan (ID: ${id}).`);
		return res.status(HttpStatusCode.Ok).send({ message: 'Subscription plan updated successfully' })
	})
)

export default router
