import sinon from 'sinon';
import express from 'express';
import { SubscriptionPlan, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import subscriptionPlanRoutes from '@/src/application/controllers/api/subscription-plan';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', subscriptionPlanRoutes);

describe('Subscription Plan Create API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let subPlanFindOneStub: sinon.SinonStub;
	let subPlanCreateStub: sinon.SinonStub;
	let subPlanFindAllStub: sinon.SinonStub;

	let dummyUser: any;
	let dummySubscriptionPlan: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		subPlanFindOneStub = sandbox.stub(SubscriptionPlan, 'findOne');
		subPlanCreateStub = sandbox.stub(SubscriptionPlan, 'create');
		subPlanFindAllStub = sandbox.stub(SubscriptionPlan, 'findAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'system',
		}

		dummySubscriptionPlan = {
			id: 1,
			annualPrice: 100,
			monthlyPrice: 10,
			isActive: true,
			createdAt: new Date(),
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/v1/subscription-plan/:id', function() {
		it('should fail if annualPrice is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/subscription-plan')
				.set('Authorization', 'Bearer accessToken')
				.send({
					monthlyPrice: 12,
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Please provide annualPrice and monthlyPrice');
		});

		it('should fail if monthlyPrice is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/subscription-plan')
				.set('Authorization', 'Bearer accessToken')
				.send({
					annualPrice: 120,
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Please provide annualPrice and monthlyPrice');
		});

		it('should fail if subscription plan already exists', async function () {
			userFindByPkStub.resolves(dummyUser);
			subPlanFindAllStub.resolves([dummySubscriptionPlan]);
			subPlanFindOneStub.resolves(dummySubscriptionPlan);

			const response = await request(app)
				.post('/api/v1/subscription-plan')
				.set('Authorization', 'Bearer accessToken')
				.send({
					annualPrice: 120,
					monthlyPrice: 12,
				});
			
			expect(subPlanFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Subscription plan already exists for the given prices');
		});

		it('should create plan successfully with isActive set to true', async function () {
			userFindByPkStub.resolves(dummyUser);
			subPlanFindAllStub.resolves(null);
			subPlanCreateStub.resolves();

			const response = await request(app)
				.post('/api/v1/subscription-plan')
				.set('Authorization', 'Bearer accessToken')
				.send({
					annualPrice: 120,
					monthlyPrice: 12,
				});
			
			expect(subPlanFindOneStub.notCalled).to.be.true;
			expect(subPlanCreateStub.calledOnceWithExactly({
				annualPrice: 120,
				monthlyPrice: 12,
				isActive: true,
			})).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Subscription plan created successfully');
		});

		it('should create plan successfully with isActive set to false', async function () {
			userFindByPkStub.resolves(dummyUser);
			subPlanFindAllStub.resolves([dummySubscriptionPlan]);
			subPlanFindOneStub.resolves(null);
			subPlanCreateStub.resolves();

			const response = await request(app)
				.post('/api/v1/subscription-plan')
				.set('Authorization', 'Bearer accessToken')
				.send({
					annualPrice: 120,
					monthlyPrice: 12,
				});
			
			expect(subPlanFindOneStub.calledOnce).to.be.true;
			expect(subPlanCreateStub.calledOnceWithExactly({
				annualPrice: 120,
				monthlyPrice: 12,
				isActive: false,
			})).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Subscription plan created successfully');
		});
	});
});