import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { NotifySubscriptionUpdate, SubscriptionPlan, TherapistSubscription } from '@/src/models'
import sequelize from '@/src/configs/database.config'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import logger from '@/src/configs/logger.config'
import { Op } from 'sequelize'

const router = express.Router()

/**
 * Activate new subscription plan, deactivate the old one, update the subscription in Stripe and send email notifications
 */
router.put(
	'/subscription-plan/activate/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { id } = req.params
		logger.info(`User requested to activate subscription plan with ID: ${id}`);

		if (!id) return res.forbidden('Invalid request. Please provide subscription plan ID.');

		const pendingSubscriptionUpdates = await NotifySubscriptionUpdate.findAll({
			where: {
				[Op.or]: [
					{ isNotified: false },
					{ isUpdated: false },
				],
				isFailed: false,
			}
		});
		if (pendingSubscriptionUpdates && pendingSubscriptionUpdates.length > 0) {
			logger.info(`Pending subscription updates found. New Plan cannot be updated right now.`);
			return res.forbidden('Pending subscription updates found. Please wait for them to be processed before activating a new plan.');
		}

		const newActivePlan = await SubscriptionPlan.findOne({
			where: {
				id,
			},
		})

		if (!newActivePlan) {
			logger.error(`Subscription plan ID ${id} does not exist.`);
			return res.forbidden('Subscription plan to activate does not exist');
		}

		const oldActivePlan = await SubscriptionPlan.findOne({
			where: {
				isActive: true,
			},
		})

		if (!oldActivePlan) {
			logger.error(`Previous active subscription plan does not exist.`);
			return res.forbidden('Previous Active Subscription plan does not exist');
		}
		
		// Check if there are any active subscriptions for the old plan, if so update them and notify users
		const existingTherapistSubscriptions = await TherapistSubscription.findAll({
			where: {
				subscriptionPlanId: oldActivePlan.id,
				isActive: true,
			},
		});

		logger.info(`Found ${existingTherapistSubscriptions?.length || 0} active therapist subscriptions for the old plan. Proceeding with updates.`);
		
		const transaction = await sequelize.transaction();
		try {
			if (existingTherapistSubscriptions?.length > 0) {
				const bulkData = existingTherapistSubscriptions.map(thSubscription => ({
					userId: thSubscription.therapistId,
					newSubscriptionPlanId: newActivePlan.id,
					oldSubscriptionPlanId: oldActivePlan.id,
					isNotified: false,
					isUpdated: false,
					therapistSubscriptionId: thSubscription.id,
				}));

				await NotifySubscriptionUpdate.bulkCreate(bulkData, { transaction });
			}

			await newActivePlan.update({ isActive: true }, { transaction });
			await oldActivePlan.update({ isActive: false }, { transaction });

			await transaction.commit();
			logger.info(`Subscription Plan Update/Notifications JOB scheduled successfully.`);

			return res.success(
				`New Subscription plan has been activated successfully.${
					existingTherapistSubscriptions?.length > 0
						? ' It will take some time to update the subscriptions for the users and send email notifications.'
						: ''
				}`
			);
		} catch (error) {
			await transaction.rollback();
			logger.error(`Error inserting data into notify_subscription_update table: ${error}`);
			return res.failure('Failed to activate new subscription plan. Please try again later.');
		}
	})
)

export default router
