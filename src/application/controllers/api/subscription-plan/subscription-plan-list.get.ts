import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginated, paginatedData } from '@/src/application/helpers/pagination.helper'
import { SubscriptionPlan } from '@/src/models'
import { WhereOptions } from 'sequelize'

const router = express.Router()

/********************************
 * * Get all subscription plans
 ********************************/
router.get(
	'/subscription-plan',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { type } = req.query

		const where: WhereOptions = {}

		if (type === 'active') where.isActive = true

		const plans = await SubscriptionPlan.findAndCountAll({
			where,
			...paginated(req),
			order: [['createdAt', 'DESC']],
		})
		res.send(paginatedData(plans, req))
	})
)

export default router
