import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { HttpStatusCode } from 'axios'
import { SubscriptionPlan, TherapistSubscription } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/**
 * Delete subscription plan
 */
router.delete(
	'/subscription-plan/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { id } = req.params
		logger.info(`User: ${req.user?.id} requested to delete Subscription Plan ${id}`);

		if (!id) throw new ForbiddenError('Invalid request. Please provide id')

		const plan = await SubscriptionPlan.findOne({
			where: {
				id,
				isActive: false,
			},
		})

		if (!plan) throw new ForbiddenError('Subscription plan does not exist')

		const therapistSubscription = await TherapistSubscription.findOne({
			where: {
				subscriptionPlanId: id,
			},
		})

		if (therapistSubscription) throw new ForbiddenError('Billing has already been done for this subscription plan. You cannot delete it now')

		await plan.destroy();
		logger.info(`User ${req.user?.id} successfully deleted Subscription Plan ${id}.`);


		return res.status(HttpStatusCode.Ok).send({ message: 'Subscription plan deleted successfully' })
	})
)

export default router
