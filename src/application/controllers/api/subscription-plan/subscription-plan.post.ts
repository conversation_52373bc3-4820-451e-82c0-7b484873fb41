import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { HttpStatusCode } from 'axios'
import { SubscriptionPlan } from '@/src/models'
import { ForbiddenError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/**
 * Create subscription plan
 */
router.post(
	'/subscription-plan',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { annualPrice, monthlyPrice } = req.body
		logger.info(`User: ${req.user?.id} initiated Subscription Plan creation.`);

		if (!annualPrice || !monthlyPrice) throw new ForbiddenError('Invalid request. Please provide annualPrice and monthlyPrice')

		const existingPlans = await SubscriptionPlan.findAll();

		let isActive = false;
		if (!existingPlans || existingPlans.length === 0) isActive = true;
		else {
			const samePlan = await SubscriptionPlan.findOne({
				where: {
					annualPrice,
					monthlyPrice,
				}
			});
			if (samePlan) throw new ForbiddenError('Subscription plan already exists for the given prices');
		}

		await SubscriptionPlan.create({
			annualPrice,
			monthlyPrice,
			isActive,
		})
		logger.info(`User: ${req.user?.id} successfully created a new Subscription Plan.`);

		return res.status(HttpStatusCode.Created).send({ message: 'Subscription plan created successfully' })
	})
)

export default router
