import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import * as MailModule from '@/src/configs/sendgrid.config';
import authController from '@/src/application/controllers/api/auth.controller';
import { User as UserModel } from '@/src/models';
import sequelize from '@/src/configs/database.config';
import * as RegisterEmail from '../../emails/register.email';
import * as PasswordResetEmailModule from '../../emails/password.email';
import * as Helper from '@/src/application/helpers/encryption.helper';
import * as CryptoUtil from '@/src/cryptoUtil';
import * as authRepository from '@/src/application/repositories/auth.repository';
import dayjs from 'dayjs';
import logger from '@/src/configs/logger.config';

const app = express();
app.use(express.json());
app.use('/api/auth', authController);

describe('Auth Controller', function () {
  this.timeout(5000);

  let sandbox: sinon.SinonSandbox;
  let userFindOneStub: sinon.SinonStub;
  let userUpdateStub: sinon.SinonStub;
  let encryptedB64Stub: sinon.SinonStub;
  let decryptedB64Stub: sinon.SinonStub;
  let hashDataStub: sinon.SinonStub;
  let passwordResetEmailCompileStub: sinon.SinonStub;
  let sendMailStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;

  const TEST_USER_ID = '213';
  const TEST_TOKEN = 'valid-token';
  const TEST_PASSWORD = 'Password123!';
  const TEST_EMAIL = '<EMAIL>';
  const TEST_HASHED_EMAIL = 'hashed_email_value';
  const FUTURE_TIME = dayjs().add(1, 'hour').unix();
  const PAST_TIME = dayjs().subtract(1, 'hour').unix();
  const FRONTEND_URL = 'http://example.com';

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    userFindOneStub = sandbox.stub(UserModel, 'findOne');
    userUpdateStub = sandbox.stub(UserModel, 'update');
    
    // Helper stubs
    decryptedB64Stub = sandbox.stub(Helper, 'decryptedB64');
    encryptedB64Stub = sandbox.stub(Helper, 'encryptedB64');
    hashDataStub = sandbox.stub(CryptoUtil, 'hashData').returns(TEST_HASHED_EMAIL);
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    // Repository stubs - only stub the specific methods we're testing
    sandbox.stub(authRepository, 'sendMfaCode');
    sandbox.stub(authRepository, 'loginWithoutMfa');
    sandbox.stub(authRepository, 'resendMFA');
    sandbox.stub(authRepository, 'verifyMFA');
    sandbox.stub(authRepository, 'registerPatientMinor');
    sandbox.stub(authRepository, 'verifyPatientEmail');
    sandbox.stub(authRepository, 'emailVerification');
    sandbox.stub(authRepository, 'verifyEmail');
    sandbox.stub(authRepository, 'forgotPassword');
    sandbox.stub(authRepository, 'resetPassword');
    
    process.env.FRONTEND_URL = FRONTEND_URL;
  });

  afterEach(function() {
    sandbox.restore();
    delete process.env.FRONTEND_URL;
  });

  describe('POST /login', function() {
    it('should login user with MFA enabled', async function() {
      // Setup
      const mockUser = { id: TEST_USER_ID, mfaEnabled: true };
      userFindOneStub.resolves(mockUser);
      (authRepository.sendMfaCode as sinon.SinonStub).resolves({
        message: 'MFA code sent',
        user: mockUser
      });
      userUpdateStub.resolves([1]);

      // Execute
      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: TEST_EMAIL, password: TEST_PASSWORD });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Created);
      expect(response.body).to.have.property('message', 'MFA code sent');
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(userFindOneStub.firstCall.args[0]).to.deep.include({
        where: { email_hash: TEST_HASHED_EMAIL }
      });
      expect((authRepository.sendMfaCode as sinon.SinonStub).calledOnce).to.be.true;
      expect(userUpdateStub.calledOnce).to.be.true;
      expect(userUpdateStub.firstCall.args).to.deep.equal([
        { version: 1 },
        { where: { id: TEST_USER_ID } }
      ]);
    });

    it('should login user without MFA', async function() {
      // Setup
      const mockUser = { id: TEST_USER_ID, mfaEnabled: false };
      const mockTokens = {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        user: mockUser
      };
      
      userFindOneStub.resolves(mockUser);
      (authRepository.loginWithoutMfa as sinon.SinonStub).resolves(mockTokens);
      userUpdateStub.resolves([1]);

      // Execute
      const response = await request(app)
        .post('/api/auth/login')
        .set('device-type', 'web')
        .send({ email: TEST_EMAIL, password: TEST_PASSWORD });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.deep.equal(mockTokens);
      expect(userFindOneStub.calledOnce).to.be.true;
      expect((authRepository.loginWithoutMfa as sinon.SinonStub).calledOnce).to.be.true;
      expect(userUpdateStub.calledOnce).to.be.true;
    });

    it('should return 404 if user not found', async function() {
      // Setup
      userFindOneStub.resolves(null);

      // Execute
      const response = await request(app)
        .post('/api/auth/login')
        .send({ email: TEST_EMAIL, password: TEST_PASSWORD });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.NotFound);
      expect(response.body).to.have.property('message');
      expect(userFindOneStub.calledOnce).to.be.true;
      expect((authRepository.sendMfaCode as sinon.SinonStub).called).to.be.false;
      expect((authRepository.loginWithoutMfa as sinon.SinonStub).called).to.be.false;
    });
  });

  describe('POST /resend-mfa', function() {
    it('should resend MFA code successfully', async function() {
      // Setup
      (authRepository.resendMFA as sinon.SinonStub).resolves({
        message: 'MFA code resent successfully'
      });

      // Execute
      const response = await request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message', 'MFA code resent successfully');
      expect((authRepository.resendMFA as sinon.SinonStub).calledOnce).to.be.true;
    });

    it('should handle errors when resending MFA code', async function() {
      // Setup
      (authRepository.resendMFA as sinon.SinonStub).rejects(new Error('Failed to resend MFA code'));

      // Execute
      const response = await request(app)
        .post('/api/auth/resend-mfa')
        .send({ email: TEST_EMAIL });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.InternalServerError);
      expect(response.body).to.have.property('message');
      expect((authRepository.resendMFA as sinon.SinonStub).calledOnce).to.be.true;
    });
  });

  describe('POST /verify-mfa', function() {
    it('should verify MFA code and return tokens', async function() {
      // Setup
      const mockUser = { id: TEST_USER_ID };
      const mockTokens = {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        user: mockUser
      };
      
      (authRepository.verifyMFA as sinon.SinonStub).resolves(mockTokens);
      userUpdateStub.resolves([1]);

      // Execute
      const response = await request(app)
        .post('/api/auth/verify-mfa')
        .set('device-type', 'mobile')
        .send({ email: TEST_EMAIL, code: '123456' });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.deep.equal(mockTokens);
      expect((authRepository.verifyMFA as sinon.SinonStub).calledOnce).to.be.true;
      expect(userUpdateStub.calledOnce).to.be.true;
    });
  });

  describe('POST /register-patient', function() {
    beforeEach(function() {
      sandbox.stub(sequelize, 'transaction').resolves({
        commit: async () => Promise.resolve(),
        rollback: async () => Promise.resolve()
      } as any);
      
      (authRepository.registerPatientMinor as sinon.SinonStub).resolves({
        user: {
          id: TEST_USER_ID,
          firstname: 'Janee',
          lastname: 'Doe',
          email: TEST_EMAIL
        },
        minorPatients: [
          {
            id: '456',
            firstname: 'Junior',
            lastname: 'Doe'
          }
        ]
      });
    });

    it('should register patient with minor patients', async function() {
      // Execute
      const response = await request(app)
        .post('/api/auth/register-patient')
        .set('device-type', 'web')
        .send({
          firstname: 'Janee',
          lastname: 'Doe',
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
          confirmPassword: TEST_PASSWORD,
          dob: '1990-01-01',
          minorPatients: [
            {
              firstname: 'Junior',
              lastname: 'Doe',
              dob: '2015-01-01'
            }
          ]
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('user');
      expect(response.body).to.have.property('minorPatients');
      expect(response.body.user).to.have.property('id', TEST_USER_ID);
      expect((authRepository.registerPatientMinor as sinon.SinonStub).calledOnce).to.be.true;
    });
  });

  describe('POST /patient-direct-login', function() {
    it('should login patient after registration', async function() {
      // Setup
      const mockUser = { id: TEST_USER_ID };
      const mockTokens = {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
        user: mockUser
      };
      
      (authRepository.verifyPatientEmail as sinon.SinonStub).resolves(mockTokens);
      userUpdateStub.resolves([1]);

      // Execute
      const response = await request(app)
        .post('/api/auth/patient-direct-login')
        .set('device-type', 'web')
        .send({ email: TEST_EMAIL, password: TEST_PASSWORD });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.deep.equal(mockTokens);
      expect((authRepository.verifyPatientEmail as sinon.SinonStub).calledOnce).to.be.true;
      expect(userUpdateStub.calledOnce).to.be.true;
    });
  });

  describe('POST /email-verification', function() {
    it('should send email verification', async function() {
      // Setup
      (authRepository.emailVerification as sinon.SinonStub).resolves({
        message: 'Verification email sent'
      });

      // Execute
      const response = await request(app)
        .post('/api/auth/email-verification')
        .send({ email: TEST_EMAIL });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message', 'Verification email sent');
      expect((authRepository.emailVerification as sinon.SinonStub).calledOnce).to.be.true;
    });
  });

  describe('POST /forgot-password', function() {
    beforeEach(function() {
      passwordResetEmailCompileStub = sandbox.stub(PasswordResetEmailModule.PasswordResetEmail, 'compile').returns({
        From: '<EMAIL>',
        To: TEST_EMAIL,
        Subject: 'Password Reset Request',
        HtmlBody: '<p>Click <a href="http://reset-link">here</a> to reset your password.</p>'
      });
      
      sendMailStub = sandbox.stub(MailModule.mail, 'sendMail').resolves();
    });
  });
  describe('POST /verify-email', function() {
    it('should verify email with token', async function() {
      // Setup
      const mockCode = '123456';
      const mockDecryptedToken = `${TEST_USER_ID}:${FUTURE_TIME}:${mockCode}`;
      
      // Setup the decryption to return the expected format
      decryptedB64Stub.returns(mockDecryptedToken);
      
      // Setup the repository response
      (authRepository.verifyEmail as sinon.SinonStub).resolves({
        message: 'Email verified successfully',
        role: 'patient'
      });
  
      // Execute with all required fields
      const response = await request(app)
        .post('/api/auth/verify-email')
        .send({ 
          token: TEST_TOKEN,
          email: TEST_EMAIL,
          code: mockCode  // This needs to match the code in the decrypted token
        });
  
      // Assert
      expect(response.status).to.equal(HttpStatusCode.Created);
      expect(response.body).to.deep.equal({
        message: 'Email verified successfully',
        role: 'patient'
      });
      expect((authRepository.verifyEmail as sinon.SinonStub).calledOnce).to.be.true;
    });
  });
  
  
});