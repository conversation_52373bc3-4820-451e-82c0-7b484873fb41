import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { NormalOfficeHours, User, UserRegistrationInfo } from '@/src/models';
import { HttpStatusCode } from 'axios';
import authController from '@/src/application/controllers/web/auth.controller';
import { response } from '@/src/application/helpers/response.helper'
import { hashData } from '@/src/cryptoUtil';
import sequelize from '@/src/configs/database.config';
import { mail } from '@/src/configs/sendgrid.config';
import * as encryptionHelper from '@/src/application/helpers/encryption.helper';
import dayjs from 'dayjs';

const app = express();
app.use(express.json());
app.use(response);
app.use('/auth', authController);

describe('Therapist Registration Unit Test', function () {
    let sandbox: sinon.SinonSandbox;
    let userFindOneStub: sinon.SinonStub;
    let userCreateStub: sinon.SinonStub;
    let userRegistrationInfoCreateStub: sinon.SinonStub;
    let userRegistrationInfoFindOneStub: sinon.SinonStub;
    let normalOfficeHoursCreateStub: sinon.SinonStub;
    let normalOfficeHoursFindAllStub: sinon.SinonStub;
    let normalOfficeHoursBulkCreateStub: sinon.SinonStub;
    let decryptedB64Stub: sinon.SinonStub;
    let rollbackStub: sinon.SinonStub;
    let userUpdateStub: sinon.SinonStub;
    let userRegistrationInfoUpdateStub: sinon.SinonStub;

    let dummyUser:Partial<User>;
    let dummyUserRegInfo:Partial<UserRegistrationInfo>;
    let dummyNormalOfficeHours:Partial<NormalOfficeHours>[];

    beforeEach(() => {
        sandbox = sinon.createSandbox();
        rollbackStub = sandbox.stub().resolves();
        sandbox.stub(sequelize, 'transaction').resolves({ 
                commit: async () => Promise.resolve(),
                rollback: rollbackStub
            } as any);

        sandbox.stub(mail, 'sendMail').resolves();
        userFindOneStub = sandbox.stub(User, 'findOne');
        userCreateStub = sandbox.stub(User, 'create');
        userRegistrationInfoCreateStub = sandbox.stub(UserRegistrationInfo, 'create');
        userRegistrationInfoFindOneStub = sandbox.stub(UserRegistrationInfo, 'findOne');
        normalOfficeHoursCreateStub = sandbox.stub(NormalOfficeHours, 'create');
        normalOfficeHoursFindAllStub = sandbox.stub(NormalOfficeHours, 'findAll');
        normalOfficeHoursBulkCreateStub = sandbox.stub(NormalOfficeHours, 'bulkCreate');
        decryptedB64Stub = sandbox.stub(encryptionHelper, 'decryptedB64');
        userUpdateStub = sandbox.stub().resolves();
        userRegistrationInfoUpdateStub = sandbox.stub().resolves();

        const hash_email =  hashData('<EMAIL>');
        
        dummyUser = {
            id: 1,
            email: '<EMAIL>',
            userToken: '123',
            email_hash: hash_email,
            role: 'therapist',
            isUnderReview: false,
            firstname: 'Janee',
            lastname: 'doe',
            acceptedAt: new Date(),
            rejectedAt: new Date(),
            emailVerifiedAt: new Date(),
        }

        dummyUserRegInfo = {
            id: 1,
            pageName: 'random-page',
            userId: 1,
            payloadInfo: JSON.parse('{}'),
        }

        dummyNormalOfficeHours = [
            {
                id: 1,
                userId: 1,
                workingDay: 'monday',
                availabilityHours: '11:00:00',
                availabilityHoursPlain: '11:00 AM',
                appointmentMethod: 'telehealth',
                isDisabled: false,
            },
            {
                id: 2,
                userId: 1,
                workingDay: 'monday',
                availabilityHours: '11:30:00',
                availabilityHoursPlain: '11:30 AM',
                appointmentMethod: 'telehealth',
                isDisabled: true,
            },
        ]
    })
    
    afterEach(function() {
        sandbox.restore();
    });

    describe('POST /auth/register', function() {
        it('should fail when no page number is sent', async function() {
            const response = await request(app).post('/auth/register');
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Page Name is required');
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should fail when no token is sent', async function() {
            const response = await request(app).post('/auth/register?page=practice-info');
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('User Token is required');
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should fail when user exists and is not a therapist ', async function() {
            dummyUser.role = 'patient';
            userFindOneStub.resolves(dummyUser);
            const response = await request(app).post('/auth/register?page=create-account&token=123').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
                business_address: {
                    lat: 123,
                    lng: 123
                }
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Unauthorized Request. Only Therapists are allowed');
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should fail when page is create-account but user already exists', async function() {
            dummyUser.role = 'patient';
            userFindOneStub.resolves(dummyUser);
            const response = await request(app).post('/auth/register?page=create-account').send({
                email: dummyUser.email,
                password: 'password@002',
                first_name: 'Janee',
                last_name: 'doe',
            });
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('An account with this email already exists');
            expect(response.body).to.have.property('data');
            expect(response.body.data).to.have.property('accountExist', true);
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should register user successfully', async function() {
            userFindOneStub.resolves(null);
            userCreateStub.resolves({
                ...dummyUser,
                id: 100,
                emailVerifiedAt: null,
                update: async () => Promise.resolve(),
                toJSON: () => ({}),
            });

            const response = await request(app).post('/auth/register?page=create-account').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Account has been successfully created.');
            expect(response.status).to.equal(HttpStatusCode.Created);
        })

        it('should fail non existing user but with token', async function() {
            userFindOneStub.resolves(null);
            const response = await request(app).post('/auth/register?page=practice-info&token=123').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Please Create an account first');
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should fail existing user check with token but email not verified', async function() {
            dummyUser.emailVerifiedAt = null;
            userFindOneStub.resolves(dummyUser);
            const response = await request(app).post('/auth/register?page=practice-info&token=123').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Please verify your email first');
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should update user info if token is provided and send email if not verified in create-account page', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                emailVerifiedAt: null,
                update: async () => Promise.resolve(),
                toJSON: () => ({}),
            });

            const response = await request(app).post('/auth/register?page=create-account&token=123').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been updated successfully');
            expect(response.body).has.property('data');
            expect(response.body.data).to.have.property('verificationEmailSent', true);
            expect(response.status).to.equal(HttpStatusCode.Ok);
        })

        it('should fail and rollback when error during create or update in create-account page', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.reject('error'),
            });

            const response = await request(app).post('/auth/register?page=create-account&token=123').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
            });

            expect(rollbackStub.calledOnce).to.be.true;
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Something went wrong. Please try again.');
            expect(response.status).to.equal(HttpStatusCode.Forbidden);
        })

        it('should insert practice info in user registration info successfully', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            userRegistrationInfoFindOneStub.resolves(null);
            const response = await request(app).post('/auth/register?page=practice-info&token=123').send({
                email: '<EMAIL>',
                password: 'password@002',
                first_name: dummyUser.firstname,
                last_name: dummyUser.lastname,
                business_address: {
                    lat: 123,
                    lng: 123
                }
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been saved successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should update practice info in user registration info successfully', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            userRegistrationInfoFindOneStub.resolves({
                ...dummyUserRegInfo,
                update: async () => Promise.resolve(),
            });
            const response = await request(app).post('/auth/register?page=practice-info&token=123').send({
                business_email: '<EMAIL>',
                business_address: {
                    lat: 123,
                    lng: 123
                }
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been updated successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should create normal office hours and user reg info successfully', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            userRegistrationInfoFindOneStub.resolves(null);
            normalOfficeHoursFindAllStub.resolves([]);

            const response = await request(app).post('/auth/register?page=normal-office-hours&token=123').send({
                timezone: 'America/New_York',
                activeTimes: {
                    monday: [
                        { time: '09:00 AM', appointmentMethod: 'telehealth', disabled: false },
                        { time: '09:30 AM', appointmentMethod: 'telehealth', disabled: true }
                    ],
                    tuesday: [
                        { time: '02:00 PM', appointmentMethod: 'telehealth', disabled: false }
                    ]
                },
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been saved successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should update normal office hours and user reg info successfully', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            userRegistrationInfoFindOneStub.resolves({
                ...dummyUserRegInfo,
                update: async () => Promise.resolve(),
            });
            normalOfficeHoursFindAllStub.resolves(
                dummyNormalOfficeHours.map((item) => ({
                    ...item,
                    deletedAt: null,
                    update: async () => Promise.resolve(),
                }))
            );

            const response = await request(app).post('/auth/register?page=normal-office-hours&token=123').send({
                timezone: 'America/New_York',
                activeTimes: {
                    monday: [
                        { time: '09:00 AM', appointmentMethod: 'telehealth', disabled: false },
                        { time: '09:30 AM', appointmentMethod: 'telehealth', disabled: true },
                        { time: '11:00 AM', appointmentMethod: 'in-person', disabled: true },
                    ],
                    tuesday: [
                        { time: '02:00 PM', appointmentMethod: 'telehealth', disabled: false }
                    ]
                },
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been updated successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should create user registration info with waitlist notifications successfully', async function() {
            userFindOneStub.resolves(dummyUser);
            userRegistrationInfoFindOneStub.resolves(null);

            const response = await request(app).post('/auth/register?page=waitlist-notifications&token=123').send({
                booking_time_hour: "6",
                notification_preferences: ["email"],
                phone_number: "",
                week_visibility: "1",
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been saved successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });
        
        it('should update user registration info with waitlist notifications successfully', async function() {
            userFindOneStub.resolves(dummyUser);
            userRegistrationInfoFindOneStub.resolves({
                ...dummyUserRegInfo,
                update: async () => Promise.resolve(),
            });

            const response = await request(app).post('/auth/register?page=waitlist-notifications&token=123').send({
                booking_time_hour: "6",
                notification_preferences: ["email"],
                phone_number: "",
                week_visibility: "1",
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been updated successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should fail to update user registration info with waitlist notifications when code is invalid', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            decryptedB64Stub.returns(`${dummyUser.id}:${dayjs().add(2, 'minute').unix()}:123456`);

            const response = await request(app).post('/auth/register?page=waitlist-notifications&token=123').send({
                booking_time_hour: "6",
                notification_preferences: ["email"],
                phone_number: "",
                week_visibility: "1",
                code: '123457',
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Code does not match');
            expect(response.status).to.equal(HttpStatusCode.Conflict);
        });

        it('should fail to update user registration info with waitlist notifications when user id does not match', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            decryptedB64Stub.returns(`2:${dayjs().add(2, 'minute').unix()}:123456`);

            const response = await request(app).post('/auth/register?page=waitlist-notifications&token=123').send({
                booking_time_hour: "6",
                notification_preferences: ["email"],
                phone_number: "",
                week_visibility: "1",
                code: '123456',
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Code does not match');
            expect(response.status).to.equal(HttpStatusCode.Conflict);
        });

        it('should fail to update user registration info with waitlist notifications when code is expired', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            decryptedB64Stub.returns(`2:${dayjs().subtract(2, 'minute').unix()}:123456`);

            const response = await request(app).post('/auth/register?page=waitlist-notifications&token=123').send({
                booking_time_hour: "6",
                notification_preferences: ["email"],
                phone_number: "",
                week_visibility: "1",
                code: '123456',
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Verification Token expired');
            expect(response.status).to.equal(HttpStatusCode.BadRequest);
        });

        it('should update user when code is not expired, valid and user id matches in waitlist-notifications case', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: async () => Promise.resolve(),
            });
            userRegistrationInfoFindOneStub.resolves({
                ...dummyUserRegInfo,
                update: async () => Promise.resolve(),
            });
            decryptedB64Stub.returns(`${dummyUser.id}:${dayjs().add(2, 'minute').unix()}:123456`);

            const response = await request(app).post('/auth/register?page=waitlist-notifications&token=123').send({
                booking_time_hour: "6",
                notification_preferences: ["email"],
                phone_number: "",
                week_visibility: "1",
                code: '123456',
            });

            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been updated successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should update user and user registration info in profile case', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: userUpdateStub,
            });
            userRegistrationInfoFindOneStub.resolves({
                ...dummyUserRegInfo,
                update: userRegistrationInfoUpdateStub,
            });

            const response = await request(app).post('/auth/register?page=profile&token=123').send({
                isRegComplete: true,
                values: {
                    name: 'Janee Doe',
                    email: '<EMAIL>',
                },
            });

            expect(userUpdateStub.calledTwice).to.be.true;
            expect(userRegistrationInfoUpdateStub.calledOnce).to.be.true;
            expect(response.body).has.property('message');
        });

        it('should update user and create user registration info in profile case', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: userUpdateStub,
            });
            userRegistrationInfoFindOneStub.resolves(null);

            const response = await request(app).post('/auth/register?page=profile&token=123').send({
                isRegComplete: false,
                values: {
                    name: 'Janee Doe',
                    email: '<EMAIL>',
                },
            });

            expect(userUpdateStub.calledOnce).to.be.true;
            expect(userRegistrationInfoCreateStub.calledOnce).to.be.true;
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been saved successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should create user registration info in default page case', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: userUpdateStub,
            });
            userRegistrationInfoFindOneStub.resolves(null);

            const response = await request(app).post('/auth/register?page=license&token=123').send({
                name: 'Janee Doe',
            });

            expect(userUpdateStub.notCalled).to.be.true;
            expect(userRegistrationInfoCreateStub.calledOnce).to.be.true;
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been saved successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });

        it('should update user registration info in default page case', async function() {
            userFindOneStub.resolves({
                ...dummyUser,
                update: userUpdateStub,
            });
            userRegistrationInfoFindOneStub.resolves({
                ...dummyUserRegInfo,
                update: userRegistrationInfoUpdateStub,
            });

            const response = await request(app).post('/auth/register?page=license&token=123').send({
                name: 'Janee Doe',
            });

            expect(userUpdateStub.notCalled).to.be.true;
            expect(userRegistrationInfoCreateStub.notCalled).to.be.true;
            expect(userRegistrationInfoUpdateStub.calledOnce).to.be.true;
            expect(response.body).has.property('message');
            expect(response.body['message']).to.equal('Your Info has been updated successfully');
            expect(response.status).to.equal(HttpStatusCode.Created);
        });
    })

})
