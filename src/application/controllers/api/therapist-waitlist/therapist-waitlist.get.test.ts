import sinon from 'sinon';
import express from 'express';
import { TherapistWaitlist, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import therapistWaitlistRoutes from '@/src/application/controllers/api/therapist-waitlist';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';

const app = express();

app.use(express.json());
app.use(response);

// Add middleware to mock authentication
app.use((req, res, next) => {
	// Mock user authentication
	req.user = {
		id: 1,
		email: '<EMAIL>',
		role: 'patient',
	};
	next();
});

app.use('/api/v1', therapistWaitlistRoutes);

describe('Therapist Waitlist API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let therapistWaitlistFindOneStub: sinon.SinonStub;
	let therapistWaitlistCreateStub: sinon.SinonStub;
	let therapistWaitlistFindAndCountAllStub: sinon.SinonStub;

	let dummyUser: Partial<User>;
	let dummyTherapistWaitlist: Partial<TherapistWaitlist>;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		therapistWaitlistFindOneStub = sandbox.stub(TherapistWaitlist, 'findOne');
		therapistWaitlistCreateStub = sandbox.stub(TherapistWaitlist, 'create');
		therapistWaitlistFindAndCountAllStub = sandbox.stub(TherapistWaitlist, 'findAndCountAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}

		dummyTherapistWaitlist = {
			id: 1,
			therapistId: 1,
			patientId: 1,
			status: 'waiting',
		}
	})

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/v1/therapist-waitlist', function() {
		it('should fail if therapistId not found in request body', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					notificationPreferences: ["email"],
				});

			expect(response.body).to.have.property('message', 'Invalid Request. Missing required fields');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if notificationPreferences not found in request body', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
				});

			expect(response.body).to.have.property('message', 'Invalid Request. Missing required fields');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if notificationPreferences is not an array', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: "email",
				});

			expect(response.body).to.have.property('message', 'Invalid Request. Missing required fields');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if notificationPreferences is an empty array', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: [],
				});

			expect(response.body).to.have.property('message', 'Invalid Request. Missing required fields');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if invalid notificationPreferences is sent', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: ['hello'],
				});

			expect(response.body).to.have.property('message', 'Invalid Request. Unsupported notification preferences: hello');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if notificationPreferences has sms but no phone number is sent', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: ['sms'],
				});

			expect(response.body).to.have.property('message', 'Phone number is required if SMS notification is selected');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if therapist with the given id not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: ['email'],
				});
			
			expect(response.body).to.have.property('message', 'Invalid Request. Therapist not found');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should fail if patient is already on the waitlist', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			therapistWaitlistFindOneStub.resolves(dummyTherapistWaitlist);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: ['email'],
				});
			
			expect(therapistWaitlistFindOneStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'You are already on the waitlist for this therapist');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should update waitlist if waitlist is already present and status is booked', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			therapistWaitlistFindOneStub.resolves({
				...dummyTherapistWaitlist,
				status: 'booked',
				update: async () => Promise.resolve(),
			});

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: ['email'],
				});
			
			expect(therapistWaitlistFindOneStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Successfully joined the waitlist for the therapist');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});

		it('should create new waitlist if waitlist is not present', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken')
				.send({
					therapistId: 1,
					notificationPreferences: ['email'],
				});
			
			expect(therapistWaitlistFindOneStub.calledOnce).to.be.true;
			expect(therapistWaitlistCreateStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Successfully joined the waitlist for the therapist');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('GET /api/v1/therapist-waitlist', function() {
		it('should fail if user is not on any waitlist', async function () {
			userFindByPkStub.resolves(dummyUser);
			// Fix: Return an object with empty rows array instead of null
			therapistWaitlistFindAndCountAllStub.resolves({
				count: 0,
				rows: []
			});

			const response = await request(app)
				.get('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken');

			expect(therapistWaitlistFindAndCountAllStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'No waitlist found. You are not on any waitlist');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});

		it('should return paginated waitlist data successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistWaitlistFindAndCountAllStub.resolves({
				rows: [dummyTherapistWaitlist],
				count: 1,
			});

			const response = await request(app)
				.get('/api/v1/therapist-waitlist')
				.set('Authorization', 'Bearer accessToken');

			expect(therapistWaitlistFindAndCountAllStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data');
			expect(response.body).to.have.property('meta');
		});
	});

	describe('DELETE /api/v1/therapist-waitlist/:id', function() {
		it('should fail if waitlist for given id not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistWaitlistFindOneStub.resolves(null);

			const response = await request(app)
				.delete('/api/v1/therapist-waitlist/123')
				.set('Authorization', 'Bearer accessToken');

			expect(therapistWaitlistFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Request. Waitlist not found');
		});

		it('should delete waitlist successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			therapistWaitlistFindOneStub.resolves({
				...dummyTherapistWaitlist,
				therapist: {
					firstname: 'New',
					lastname: 'Therapist',
				},
				destroy: async () => Promise.resolve(),
			});

			const response = await request(app)
				.delete('/api/v1/therapist-waitlist/123')
				.set('Authorization', 'Bearer accessToken');

			expect(therapistWaitlistFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'You have successfully exited the waitlist for therapist New Therapist');
		});
	});
});