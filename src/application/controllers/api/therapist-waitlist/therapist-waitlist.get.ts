import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import logger from '@/src/configs/logger.config'
import { User, TherapistWaitlist } from '@/src/models'
import { Op } from 'sequelize'
import { paginated, paginatedData } from '@/src/application/helpers/pagination.helper'
 
const router = express.Router()
 
/**
* Get the waitlist that a user is on along with the therapist info.
*/
router.get(
    '/therapist-waitlist',   
    wrap(async (req: Request, res: Response) => {   
        const { userId } = req.query;   
        const emptyArray =req.query.emptyArray ? req.query.emptyArray === 'true' : false;
        if (emptyArray) return res.send({ count: 0, data: [] , meta: {
                    currentPage: 1,
                    perPage: 10,
                    total: 0,
                    lastPage: 1,
                    nextPage: null,
                    prevPage: null
                }});
 
        const perPage = req.query.perPage ? parseInt(req.query.perPage as string, 10) : undefined;
 
        const waitlists = await TherapistWaitlist.findAndCountAll({   
            where: {   
                patientId: userId,   
                status: {   
                    [Op.in]: ['waiting', 'notified'],   
                },
            },   
            include: [   
                {   
                    model: User,   
                    as: 'therapist',   
                    attributes: ['id', 'firstname', 'lastname', 'email', 'address', 'userProfile'],   
                    required: true,   
                }   
            ],
            order: [['createdAt', 'DESC']],
            ...paginated(req, perPage),
            });
 
        if (!waitlists || waitlists.rows.length === 0) {   
            logger.error(`User ${userId} failed to get the waitlist that they are on. No waitlist found.`);   
            return res.forbidden('No waitlist found. You are not on any waitlist');   
        }
 
        res.send(paginatedData({ count: waitlists.count, rows: waitlists.rows }, req));
    })
);
 
export default router
 