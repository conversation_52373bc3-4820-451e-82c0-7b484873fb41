import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import logger from '@/src/configs/logger.config'
import { User, TherapistWaitlist } from '@/src/models'

const router = express.Router()

/**
 * Join the waitlist for a therapist to get notified when they are available.
 */
router.post(
	'/therapist-waitlist',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) return res.forbidden('Invalid Request. User not found');

		logger.info(`User ${user.id} is trying to join the waitlist for therapist ${req.body.therapistId}`);

		const { therapistId, notificationPreferences, phoneNumber } = req.body;
		if (!therapistId || !notificationPreferences || !Array.isArray(notificationPreferences) || notificationPreferences.length === 0) {
			logger.error(`User ${user.id} failed to join the waitlist. Missing required fields.`);
			return res.forbidden('Invalid Request. Missing required fields');
		}

		const validPreferences = ['email', 'sms', 'fcm'];
		const invalidPrefs = notificationPreferences.filter(
			(pref: string) => !validPreferences.includes(pref)
		);

		if (invalidPrefs.length > 0) {
			logger.error(`User ${user.id} failed to join the waitlist. Invalid notification preferences: ${invalidPrefs.join(', ')}`);
			return res.forbidden(`Invalid Request. Unsupported notification preferences: ${invalidPrefs.join(', ')}`);
		}

		if (notificationPreferences.includes('sms') && !phoneNumber) {
			logger.error(`User ${user.id} failed to join the waitlist. Phone number required for SMS preference.`);
			return res.forbidden('Phone number is required if SMS notification is selected');
		}

		const therapist = await User.findOne({
			where: {
				id: therapistId,
			},
		});

		if (!therapist) {
			logger.error(`User ${user.id} failed to join the waitlist. Therapist not found.`);
			return res.forbidden('Invalid Request. Therapist not found');
		}

		const waitlist = await TherapistWaitlist.findOne({
			where: {
				therapistId,
				patientId: user.id,
			},
		});
		if (waitlist && (waitlist.status === 'notified' || waitlist.status === 'waiting')) {
			logger.error(`User ${user.id} failed to join the waitlist. Already on the waitlist.`);
			return res.forbidden('You are already on the waitlist for this therapist');
		}

		if (waitlist) {
			await waitlist.update({
				status: 'waiting',
				notificationPreferences,
				phoneNumber: notificationPreferences.includes('sms') ? phoneNumber : null,
			});
			logger.info(`User ${user.id} successfully updated the waitlist for therapist ${therapistId} - Waitlist ID: ${waitlist.id}`);
		} else {
			await TherapistWaitlist.create({
				therapistId,
				patientId: user.id,
				notificationPreferences,
				phoneNumber: notificationPreferences.includes('sms') ? phoneNumber : null,
				status: 'waiting',
			});
			logger.info(`User ${user.id} successfully joined the waitlist for therapist ${therapistId}`);
		}
		
		return res.success('Successfully joined the waitlist for the therapist');
	})
);

export default router
