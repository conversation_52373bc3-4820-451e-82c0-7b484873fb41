import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import logger from '@/src/configs/logger.config'
import { User, TherapistWaitlist } from '@/src/models'
import { capitalizeFirstLetter } from '@/src/application/helpers/general.helper'

const router = express.Router()

/**
 * Delete the waitlist by ID that a user is on
 */
router.delete(
	'/therapist-waitlist/:id',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) return res.forbidden('Invalid Request. User not found');

		logger.info(`User ${user.id} is trying to delete the waitlist ${req.params.id} that they are on.`);

		const { id } = req.params;
		if (!id) return res.forbidden('Invalid Request. Waitlist ID is required');

		const waitlist = await TherapistWaitlist.findOne({
			where: {
				id,
				patientId: user.id,
			},
			include: [
				{
					model: User,
					as: 'therapist',
					attributes: ['firstname', 'lastname'],
					required: true,
				}
			]
		});

		if (!waitlist) {
			logger.error(`User ${user.id} failed to delete the waitlist ${req.params.id}. Waitlist not found.`);
			return res.forbidden('Invalid Request. Waitlist not found');
		}
		await waitlist.destroy();
		logger.info(`User ${user.id} successfully deleted the waitlist ${waitlist.id}`);

		return res.success(`You have successfully exited the waitlist for therapist ${capitalizeFirstLetter(`${waitlist.therapist?.firstname} ${waitlist.therapist?.lastname}`)}`);	
	})
);

export default router
