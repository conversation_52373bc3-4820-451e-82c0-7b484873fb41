import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { Appointments, PatientPayment, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import confirmAppointmentPayment from './confirm-payment.post';
import stripe from '@/src/configs/stripe-new.config';
import dayjs from 'dayjs';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', confirmAppointmentPayment);

describe('Confirm Appointment Payment API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let appointmentFindOneStub: sinon.SinonStub;
	let patientPaymentCreateStub: sinon.SinonStub;

	let stripePaymentIntentsCreateStub: sinon.SinonStub;
	let stripePaymentMethodsRetrieveStub: sinon.SinonStub;
	let stripeAccountsRetrieveStub: sinon.SinonStub;

	let dummyAppointment: any;
	let dummyUser: any;
	let dummyCusPaymentMethod: any;
	let dummyPatientPaymentIntent: any;
	let dummyStripeConnectAccount: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		appointmentFindOneStub = sandbox.stub(Appointments, 'findOne');
		patientPaymentCreateStub = sandbox.stub(PatientPayment, 'create');

		stripePaymentIntentsCreateStub = sandbox.stub(stripe.paymentIntents, 'create');
		stripePaymentMethodsRetrieveStub = sandbox.stub(stripe.paymentMethods, 'retrieve');
		stripeAccountsRetrieveStub = sandbox.stub(stripe.accounts, 'retrieve');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
			stripeConnectAccountId: 'acct_123456',
		}

		dummyAppointment = {
			id: 1,
			therapistId: 1,
			patientId: 2,
			isBilled: false,
			cancelledAt: null,
			amount: 100,
			appointmentDate: dayjs().subtract(1, 'day').toISOString(),
			patient: {
				id: 2,
				stripeCustomerId: 'cus_123456',
			},
			patientCard: {
				stripePaymentMethodId: 'pm_123456',
			}
		}

		dummyPatientPaymentIntent = {
			id: 'pi_123456',
			customer: 'cus_123456',
			amount: 100,
			currency: 'usd',
		}

		dummyCusPaymentMethod = {
			id: 'pm_123456',
			customer: 'cus_123456',
			metadata: {
				userId: 2,
			},
		}

		dummyStripeConnectAccount = {
			id: 'acct_123456',
			capabilities: {
				transfers: 'active',
			},
			charges_enabled: true,
			payouts_enabled: true,
			metadata: {
				userId: 1,
			},
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/v1/confirm-payment', function() {
		it('should fail if appointmentId is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Request. Appointment ID is required.');
		});

		it('should fail if appointment with given id not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(null);

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Appointment ID. Appointment not found.');
		});

		it('should fail if appointment does not have patientCard', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				patientCard: null,
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'No payment method found for this appointment. It cannot be billed.');
		});

		it('should fail if therapistId of appointment is not same as requesting user', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				therapistId: 2,
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});

			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid Appointment ID. You are not associated with this appointment.');
		});

		it('should fail if appointment is already billed', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				isBilled: true,
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Sorry, this Appointment is already billed.');
		});

		it('should fail if appointment is cancelled', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				cancelledAt: new Date(),
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Sorry, this Appointment is cancelled. It cannot be billed.');
		});

		it('should fail if appointment date is in future', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				appointmentDate: dayjs().add(1, 'day').toISOString(),
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Cannot bill for future appointments.');
		});

		it('should fail if therapist does not have stripe connect account', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				stripeConnectAccountId: null,
			});
			appointmentFindOneStub.resolves(dummyAppointment);

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripeAccountsRetrieveStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Stripe Connect Account has not been setup.');
			expect(response.body).to.have.property('data').to.have.property('noStripeConnect', true);
		});

		it('should fail if error occurs during stripe connect account fetch', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.rejects(new Error('Error'));

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripeAccountsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Error retrieving Stripe Connect Account. Please try again.');
		});

		it('should fail if stripe connect account metadata userId does not match therapist id', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves({
				...dummyStripeConnectAccount,
				metadata: {
					userId: 3,
				}
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripeAccountsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'This Stripe Connect Account is not associated with you.');
		});

		it('should fail if stripe connect account is not active', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves({
				...dummyStripeConnectAccount,
				payouts_enabled: false,
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripeAccountsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Stripe Connect Account is not active.');
			expect(response.body).to.have.property('data').to.have.property('noStripeConnect', true);
		});

		it('should fail if error occurs during stripe payment method fetch', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.rejects(new Error('Error'));

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Error retrieving payment method. Please try again.');
		});

		it('should fail if stripe payment method of user is not available', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.resolves(null);

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'No payment method found. This appointment cannot be billed.');
		});

		it('should fail if stripe payment method metadata userId does not match patient id', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.resolves({
				...dummyCusPaymentMethod,
				metadata: {
					userId: 3,
				}
			});

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid payment method details. This appointment cannot be billed.');
		});

		it('should fail if appointment does not have amount', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				amount: null,
			});
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid appointment amount. This appointment cannot be billed.');
		});

		it('should fail if error occurs during stripe paymentIntents create', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
			stripePaymentIntentsCreateStub.rejects(new Error('Error'));

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(stripePaymentIntentsCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Error billing the patient. Please try again later.');
		});

		it('should fail if paymentIntent is not available', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves(dummyAppointment);
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
			stripePaymentIntentsCreateStub.resolves(null);

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(stripePaymentIntentsCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Error billing the patient. Please try again later.');
		});

		it('should confirm payment successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindOneStub.resolves({
				...dummyAppointment,
				update: async () => Promise.resolve(),
			});
			stripeAccountsRetrieveStub.resolves(dummyStripeConnectAccount);
			stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
			stripePaymentIntentsCreateStub.resolves(dummyPatientPaymentIntent);
			patientPaymentCreateStub.resolves();

			const response = await request(app)
				.post('/api/v1/confirm-payment')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentId: 1,
				});
			
			expect(appointmentFindOneStub.calledOnce).to.be.true;
			expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(stripePaymentIntentsCreateStub.calledOnce).to.be.true;
			expect(patientPaymentCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Payment confirmed successfully. Appointment is now billed.');
		});

	});
});