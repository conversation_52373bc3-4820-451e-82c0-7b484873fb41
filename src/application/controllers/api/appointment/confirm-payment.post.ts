import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { Appointments, PatientCard, PatientPayment, User } from '@/src/models'
import dayjs from 'dayjs'
import timezone from "dayjs/plugin/timezone";
import stripe from '@/src/configs/stripe-new.config'
import logger from '@/src/configs/logger.config'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'

dayjs.extend(timezone);

const router = express.Router()

/********************************
 * * Payment Confirmation for the Appointment
 ********************************/
router.post(
	'/confirm-payment',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) return res.forbidden('Invalid Request.');
		logger.info(`User ID: ${user.id} attempting to confirm payment.`);

		const { appointmentId } = req.body;
		if (!appointmentId) return res.forbidden('Invalid Request. Appointment ID is required.');
		
		const appointment = await Appointments.findOne({
			where: {
				id: appointmentId,
			},
			include: [
				{
					model: User,
					as: 'patient',
					attributes: ['id', 'stripeCustomerId'],
					required: true,
				},
				{
					model: PatientCard,
				}
			]
		});
		if (!appointment) return res.forbidden('Invalid Appointment ID. Appointment not found.');
		if (!appointment.patientCard) return res.forbidden('No payment method found for this appointment. It cannot be billed.');
		if (appointment.therapistId !== user.id) return res.forbidden('Invalid Appointment ID. You are not associated with this appointment.');

		if (appointment.isBilled) {
			logger.info(`Appointment ${appointment.id} is already billed.`);
			return res.forbidden('Sorry, this Appointment is already billed.');
		}

		if (appointment.cancelledAt !== null) {
			logger.info(`Appointment ${appointment.id} is cancelled.`);
			return res.forbidden('Sorry, this Appointment is cancelled. It cannot be billed.');
		}

		if (dayjs(appointment.appointmentDate).isAfter(dayjs())) {
			logger.info(`User ID: ${user.id} - Appointment date is in the future.`);
			return res.forbidden('Cannot bill for future appointments.');
		}

		if (!user.stripeConnectAccountId) {
			logger.info(`Confirm Payment - Appointment ${appointment.id} - Stripe Connect Account not found.`);
			return res.forbidden('Stripe Connect Account has not been setup.', {
				noStripeConnect: true,
			});
		}
		let stripeConnectAccount;

		try {
			stripeConnectAccount = await stripe.accounts.retrieve(user.stripeConnectAccountId);
		} catch (error) {
			logger.error(`User ID: ${user.id} - Error retrieving Stripe Connect Account: ${error}`);
			return res.forbidden('Error retrieving Stripe Connect Account. Please try again.');
		}

		if (stripeConnectAccount.metadata?.userId && (Number(stripeConnectAccount.metadata.userId) !== Number(user.id))) {
			logger.info(`User ${user?.id} - Stripe Connect Account user ID does not match`);
			return res.forbidden('This Stripe Connect Account is not associated with you.');
		}

		if (
			stripeConnectAccount.capabilities?.transfers !== 'active' ||
			!stripeConnectAccount.charges_enabled ||
			!stripeConnectAccount.payouts_enabled
		) {
			logger.info(`Confirm Payment - Appointment ${appointment.id} - Stripe Connect Account is not active.`);
			return res.forbidden('Stripe Connect Account is not active.', {
				noStripeConnect: true,
			});
		}

		let stripeCusPaymentMethod;

		try {
			stripeCusPaymentMethod = await stripe.paymentMethods.retrieve(appointment.patientCard.stripePaymentMethodId);
		} catch (error) {
			logger.error(`User ID: ${user.id} - Error retrieving payment method: ${error}`);
			return res.forbidden('Error retrieving payment method. Please try again.');
		}

		if (!stripeCusPaymentMethod) {
			logger.info(`User ID: ${user.id} - Payment method not found for patient.`);
			return res.forbidden('No payment method found. This appointment cannot be billed.');
		}

		if (stripeCusPaymentMethod.metadata?.userId
			&& (Number(stripeCusPaymentMethod.metadata.userId) !== Number(appointment.patient.id))) {
			logger.info(`User ID: ${user.id} - User ID in payment method metadata mismatch.`);
			return res.forbidden('Invalid payment method details. This appointment cannot be billed.');
		}

		const amount = appointment.amount;
		if (!amount) {
			logger.info(`User ID: ${user.id} - Amount not found for appointment.`);
			return res.forbidden('Invalid appointment amount. This appointment cannot be billed.');
		}

		// confirm the appointment using the setup intent of the customer
		let paymentIntent;
		try {
			paymentIntent = await stripe.paymentIntents.create({
				amount: Math.round(amount * 100),
				currency: 'usd',
				customer: appointment.patient.stripeCustomerId,
				payment_method: stripeCusPaymentMethod.id,
				off_session: true,
				confirm: true,
				description: 'Appointment Payment Confirmation',
				metadata: {
					patientId: appointment.patientId,
					therapistId: appointment.therapistId,
					appointmentId: appointment.id,
				},
				transfer_data: {
					destination: user.stripeConnectAccountId,
				},

				// ? OPTIONAL — make receipts & emails show therapist's info
				// on_behalf_of: 'acct_1RdUkMRtZo1NscQw', - might need card_payments: { requested: true }, in account creation
			});
		} catch (error) {
			logger.error(`User ID: ${user.id} - Error confirming appointment payment - appointment ID: ${appointment.id}: ${error}`);
			return res.forbidden('Error billing the patient. Please try again later.');
		}

		if (!paymentIntent) {
			logger.info(`User ID: ${user.id} - Payment intent not found for appointment.`);
			return res.forbidden('Error billing the patient. Please try again later.');
		}

		logger.info(`Patient ID: ${appointment.patientId} - charged successfully. Payment Intent ID: ${paymentIntent.id}`);

		await appointment.update({
			isBilled: true,
			cancelledAt: null,
		});

		await PatientPayment.create({
			appointmentId: appointment.id,
			patientId: appointment.patientId,
			therapistId: appointment.therapistId,
			stripePaymentId: paymentIntent.id,
			stripeCustomerId: paymentIntent.customer as string,
			billedPrice: paymentIntent.amount,
			currency: paymentIntent.currency,
		});

		return res.success('Payment confirmed successfully. Appointment is now billed.');
	})
)

export default router
