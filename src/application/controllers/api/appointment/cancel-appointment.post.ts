import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { Appointments, Calendar, MinorPatient, PatientCard, PatientPayment, User, UserRegistrationInfo } from '@/src/models'
import dayjs from 'dayjs'
import { deleteGoogleEvent, deleteOutlookEvent } from '@/src/application/repositories/calendar.repository'
import stripe from '@/src/configs/stripe-new.config'
import { capitalizeFirstLetter, formatDuration, getTimezoneAbbr, isValidTimeZone, maskEmail, maskPhoneNumber } from '@/src/application/helpers/general.helper'
import { PracticeInfo, WaitlistNotifications } from '@/src/types/registration.interface'
import { TherapistAppointmentCancelEmail } from '@/src/application/emails/appointment-notify.email'
import { sendSms } from '@/src/configs/twilio.config'
import { mail } from '@/src/configs/sendgrid.config'
import logger from '@/src/configs/logger.config'
import { Op } from 'sequelize'

const router = express.Router()

/********************************
 * * Cancel appointment
 ********************************/
router.post(
	'/cancel-appointment/:id',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		logger.info(`Request received to cancel appointment.`, { userId: req.user?.id });

		if (!user) {
			logger.error(`Unauthorized request to cancel appointment.`);
			return res.forbidden('Invalid Request.');
		}

		const { id: appointmentId } = req.params;
		if (!appointmentId) return res.forbidden('Appointment id is required.')
		logger.info(`Fetching appointment with ID: ${appointmentId}`);
		const appointment = await Appointments.findOne({
			where: {
				id: appointmentId,
				patientId: user.id,
			},
			include: [
				{
					model: User,
					as: 'therapist',
					attributes: ['id', 'firstname', 'lastname', 'email', 'address', 'timeZone', 'phone'],
					include: [
						{ model: Calendar, attributes: ['type', 'credentials'] },
						{
							model: UserRegistrationInfo,
							where: {
								[Op.or]: [
									{ pageName: 'waitlist-notifications' },
									{ pageName: 'practice-info' }
								]
							},
							required: false,
						}
					]
				},
				{
					model: PatientPayment,
					as: 'patientPayment',
				},
				{
					model: User,
					as: 'patient',
					attributes: ['id', 'firstname', 'lastname', 'email', 'timeZone', 'stripeCustomerId'],
				},
				{
					model: MinorPatient,
					as: 'minorPatient',
				},
				{
					model: PatientCard,
					as: 'patientCard',
				}
			]
		});

		if (!appointment) return res.notFound('Appointment not found');
		if (appointment.patient.id !== user.id) return res.forbidden('You are not authorized to cancel this appointment.');
		if (appointment.isBilled) return res.forbidden('Appointment is already billed. It cannot be canceled.');
		if (appointment.cancelledAt) return res.forbidden('Appointment is already canceled.');

		const now = dayjs().second(0).millisecond(0);
		const appointmentDate = dayjs(appointment.appointmentDate).second(0).millisecond(0);
		if (appointmentDate.isBefore(now) || appointmentDate.isSame(now)) {
			logger.error(`User (${user.id}) attempted to cancel an appointment that has already passed. Appointment ID: ${appointmentId}`);
			return res.forbidden('Appointment date has already passed. It cannot be canceled.');
		}

		// Check if the appointment is within or exactly 24 hours from now
		const isWithin24Hours = appointmentDate.isBefore(now.add(24, 'hours')) || appointmentDate.isSame(now.add(24, 'hours'));

		// if withing 24 hours, charge the user the appointment fee
		if (isWithin24Hours) {
			logger.info(`User (${user.id}) attempted to cancel an appointment within 24 hours to the appointment date. Appointment ID: ${appointmentId}`);

			if (!appointment.patientCard) return res.forbidden('No payment method found for this appointment. It cannot be canceled.');
			
			let stripeCusPaymentMethod;

			try {
				stripeCusPaymentMethod = await stripe.paymentMethods.retrieve(appointment.patientCard.stripePaymentMethodId);
			} catch (error) {
				logger.error(`User ID: ${user.id} - Error retrieving payment method: ${error}`);
				return res.forbidden('Error retrieving payment method. Please try again.');
			}

			if (!stripeCusPaymentMethod) {
				logger.info(`User ID: ${user.id} - Payment method not found for patient.`);
				return res.forbidden('No payment method found. This appointment cannot be canceled.');
			}

			if (stripeCusPaymentMethod.metadata?.userId
				&& (Number(stripeCusPaymentMethod.metadata.userId) !== Number(appointment.patient.id))) {
				logger.info(`User ID: ${user.id} - User ID in payment method metadata mismatch.`);
				return res.forbidden('Invalid payment method details. This appointment cannot be canceled.');
			}

			const amount = appointment.amount;
			if (!amount) {
				logger.info(`User ID: ${user.id} - Amount not found for appointment.`);
				return res.forbidden('Invalid appointment amount. This appointment cannot be canceled.');
			}
			logger.info(`Charging the user the appointment fee as per the cancellation policy. Appointment ID: ${appointmentId}`);

			let paymentIntent;
			try {
				paymentIntent = await stripe.paymentIntents.create({
					amount: Math.round(amount * 100),
					currency: 'usd',
					customer: appointment.patient.stripeCustomerId,
					payment_method: stripeCusPaymentMethod.id,
					off_session: true,
					confirm: true,
					description: 'Appointment Payment Confirmation',
					metadata: {
						patientId: appointment.patientId,
						therapistId: appointment.therapistId,
						appointmentId: appointment.id,
					},
				});
			} catch (error) {
				logger.error(`User ID: ${user.id} - Error confirming appointment payment - appointment ID: ${appointment.id}: ${error}`);
				return res.forbidden('Error billing the appointment. Please try again.');
			}

			if (!paymentIntent) {
				logger.info(`User ID: ${user.id} - Payment intent not found for appointment.`);
				return res.forbidden('Error billing the appointment. Please try again.');
			}

			logger.info(`Marking appointment as billed. Appointment ID: ${appointmentId}`);
			await appointment.update({
				isBilled: true,
			});

			await PatientPayment.create({
				appointmentId: appointment.id,
				patientId: appointment.patientId,
				therapistId: appointment.therapistId,
				stripePaymentId: paymentIntent.id,
				stripeCustomerId: paymentIntent.customer as string,
				billedPrice: paymentIntent.amount,
				currency: paymentIntent.currency,
			});
		}

		const cancelDate = new Date();

		logger.info(`Marking appointment as canceled. Appointment ID: ${appointmentId}`);
		await appointment.update({
			cancelledAt: cancelDate,
		});

		if (appointment.googleEventId) {
			logger.info(`Attempting to delete Google event. AppointmentID: ${appointmentId}`);
			
			const therapistGoogleCal = appointment.therapist?.calendars?.find((c) => c.type === 'google');

			if (therapistGoogleCal) {
				const { tokens } = therapistGoogleCal?.credentials as any;

				if (tokens) {
					const isDeleted = await deleteGoogleEvent({
						tokens,
						eventId: appointment.googleEventId,
					})

					if (isDeleted) {
						await appointment.update({
							googleEventId: null,
						});
					}
				}
			}
		}

		if (appointment.outlookEventId) {
			logger.info(`Attempting to delete Outlook event. AppointmentID: ${appointmentId}`);
			const therapistOutlookCal = appointment.therapist?.calendars?.find((c) => c.type === 'outlook');

			if (therapistOutlookCal) {
				const { tokens } = therapistOutlookCal?.credentials as any;

				if (tokens) {
					const isDeleted = await deleteOutlookEvent({
						tokens,
						userId: appointment.therapist.id,
						eventId: appointment.outlookEventId,
					});

					if (isDeleted) {
						await appointment.update({
							outlookEventId: null,
						});
					}
				}
			}
		}

		// Notifying therapist
		const therapistTimeZone = appointment.therapist.timeZone;
		if (therapistTimeZone && isValidTimeZone(therapistTimeZone)) {
			try {
				logger.info(`Preparing Appointment Cancellation notification data for appointment ID: ${appointment.id} - Therapist ID: ${appointment.therapist.id}`);
				const therapistAppDate = dayjs(appointment.appointmentDate).tz(therapistTimeZone).format('MMMM D, YYYY');
				const therapistAppTime = dayjs(appointment.appointmentDate).tz(therapistTimeZone).format('h:mm A');
				const therapistTimeZoneAbbr = getTimezoneAbbr(therapistTimeZone);
				const canceledAt = dayjs(cancelDate).tz(therapistTimeZone).format('MMMM D, YYYY h:mm A');

				const therapistNotifInfo = appointment.therapist?.registrationInfo?.find(
					(info) => info.pageName === 'waitlist-notifications'
				)?.payloadInfo as unknown as WaitlistNotifications;
				
				let therapistPhone = appointment.therapist?.phone;
				if (therapistNotifInfo && therapistNotifInfo?.notification_preferences?.includes('text') && therapistNotifInfo.phone_number) {
					therapistPhone = therapistNotifInfo.phone_number;
				}
				const therapistFullName = capitalizeFirstLetter(`${appointment.therapist?.firstname} ${appointment.therapist?.lastname}`);
				let patientFullName = capitalizeFirstLetter(`${appointment.patient?.firstname} ${appointment.patient?.lastname}`);

				if (appointment.isMinor && appointment.minorId && appointment.minorPatient) {
					const minorPatient = appointment.minorPatient;
					if (minorPatient.firstName) {
						patientFullName = capitalizeFirstLetter(`${minorPatient.firstName} ${minorPatient?.lastName}`);
					}
				}

				let appointmentLocation: string | null = null;
				if (appointment.type === 'in-person') {
					appointmentLocation = (appointment.therapist.address as any)?.full_address || null;

					if (!appointmentLocation) {
						const therapistPracticeInfo = appointment.therapist?.registrationInfo?.find(
							(info) => info.pageName === 'practice-info'
						)?.payloadInfo as unknown as PracticeInfo;

						if (therapistPracticeInfo && therapistPracticeInfo.business_address) {
							appointmentLocation = therapistPracticeInfo.business_address?.full_address || null;
						}	
					}
				}
				// Email Notification
				const shouldSendEmail = therapistNotifInfo ? therapistNotifInfo?.notification_preferences?.includes('email') : false;
				if (shouldSendEmail) {
					try {
						logger.info(`Creating Appointment Cancel Email for appointment ID: ${appointment.id} - Therapist ID: ${appointment.therapist.id}`);
						const therapistEmailData = TherapistAppointmentCancelEmail.compile({
							email: appointment.therapist.email,
							subject: `Appointment Cancelled: ${patientFullName} on ${therapistAppDate} at ${therapistAppTime} cancelled`,
							therapistName: therapistFullName,
							patientName: patientFullName,
							appointmentDate: therapistAppDate,
							appointmentTime: `${therapistAppTime} (${therapistTimeZoneAbbr})`,
							duration: formatDuration(appointment.duration),
							location: appointmentLocation || '',
							canceledAt,
							appointmentType: capitalizeFirstLetter(appointment.type),
						});
						logger.info(`Sending Appointment Cancel Email notification to ${maskEmail(appointment.therapist.email)}`);
						await mail.sendMail({
							to: therapistEmailData.To,
							subject: therapistEmailData.Subject,
							html: therapistEmailData.HtmlBody,
						});
						logger.info(`Email notification sent to ${maskEmail(appointment.therapist.email)}`);
					} catch (error) {
						logger.error(`Appointment cancel Email - Therapist Notify Email error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`)
					}
				}

				// SMS Notification
				const shouldSendSms = therapistNotifInfo ? therapistNotifInfo?.notification_preferences?.includes('text') : false;
				if (shouldSendSms && therapistPhone) {
					try {
						logger.info(`Sending Appointment Cancel SMS notification to ${maskPhoneNumber(therapistPhone)}`);
						await sendSms(
							therapistPhone,
							`NextTherapist - Appointment Cancelled: Your appointment with ${patientFullName} on ${therapistAppDate} at ${therapistAppTime} has been cancelled by the patient.`
						);
						logger.info(`Appointment Cancel SMS notification sent to ${maskPhoneNumber(therapistPhone)}`);
					} catch (error) {
						logger.error(`Appointment cancel SMS - Therapist Notify SMS error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
					}
				}
			} catch (error) {
				logger.error(`Appointment Cancel - Therapist Notify error - Therapist ID: (${appointment.therapist.id}) - Appointment ID: (${appointment.id}): ${error}.`);
			}
		}

		logger.info(`Appointment cancellation completed. ID: ${appointmentId}, UserId: ${user.id}`);
		return res.success(
			`Your appointment with ${capitalizeFirstLetter(`${appointment.therapist?.firstname} ${appointment.therapist?.lastname}`)} has been canceled successfully.`
		);
	})
)

export default router
