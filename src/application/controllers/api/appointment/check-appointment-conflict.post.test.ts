import sinon from 'sinon';
import express from 'express';
import { Appointments, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import appointmentRoutes from '@/src/application/controllers/api/appointment';
import dayjs from 'dayjs';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', appointmentRoutes);

describe('Check Appointment Conflict API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let appointmentFindAllStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyAppointment: any;

	let TEST_DATE = dayjs().add(2, 'days').hour(10).minute(0).second(0).millisecond(0).toISOString();

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		appointmentFindAllStub = sandbox.stub(Appointments, 'findAll');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}

		dummyAppointment = {
			id: 1,
			duration: 60,
			appointmentDate: TEST_DATE,
			therapist: {
				id: 1,
				firstname: 'Henry',
				lastname: 'Ford',
			}
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /api/v1/appointment/check-conflict', function() {
		it('should fail if appointment date is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/appointment/check-conflict')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Appointment Date is required');
		});

		it('should fail if appointment date is not valid ISO string', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/appointment/check-conflict')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentDate: 'invalid-date',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid date format. Please provide a valid ISO string in UTC.');
		});

		it('should return response successfully with no conflicting appointment', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindAllStub.resolves([dummyAppointment]);

			const response = await request(app)
				.post('/api/v1/appointment/check-conflict')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentDate: dayjs(TEST_DATE).add(5, 'hour').toISOString(),
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object');
			expect(response.body).to.have.property('isConflicting', false);
			expect(response.body).to.have.property('isWithin24Hours', false);
			expect(response.body).to.have.property('conflictingAppointment', null);
		});

		it('should return response successfully with conflicting appointment', async function () {
			userFindByPkStub.resolves(dummyUser);
			appointmentFindAllStub.resolves([dummyAppointment]);

			const response = await request(app)
				.post('/api/v1/appointment/check-conflict')
				.set('Authorization', 'Bearer accessToken')
				.send({
					appointmentDate: TEST_DATE,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object');
			expect(response.body).to.have.property('isConflicting', true);
			expect(response.body).to.have.property('isWithin24Hours', false);
			expect(response.body).to.have.property('conflictingAppointment');
		});
	});
});