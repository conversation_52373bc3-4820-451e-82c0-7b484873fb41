import dotenv from 'dotenv';
dotenv.config();

import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { Appointments, PatientPayment } from '@/src/models';
import { mail } from '@/src/configs/sendgrid.config';
import { HttpStatusCode } from 'axios';
import { UserType } from '@/src/application/helpers/constant.helper';
import * as GeneralHelper from '@/src/application/helpers/general.helper';
import * as CalendarRepository from '@/src/application/repositories/calendar.repository';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import logger from '@/src/configs/logger.config';
import { TherapistAppointmentCancelEmail } from '@/src/application/emails/appointment-notify.email';
import * as TwilioConfig from '@/src/configs/twilio.config';
import stripe from '@/src/configs/stripe-new.config';

// Configure dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);

// Define test constants
const TEST_USER_ID = '123';
const TEST_THERAPIST_ID = '456';
const TEST_APPOINTMENT_ID = '789';
const TEST_EMAIL = '<EMAIL>';
const TEST_THERAPIST_EMAIL = '<EMAIL>';
const TEST_APPOINTMENT_DATE = dayjs().add(3, 'days').toISOString(); // 3 days in future to pass 24-hour check

// Mock User model instances
const mockPatient = {
  id: TEST_USER_ID,
  email: TEST_EMAIL,
  firstname: 'John',
  lastname: 'Doe',
  emailVerifiedAt: new Date(),
  role: UserType.PATIENT,
  stripeCustomerId: 'cus_123456',
} as any;

const mockTherapist = {
  id: TEST_THERAPIST_ID,
  email: TEST_THERAPIST_EMAIL,
  firstname: 'Jane',
  lastname: 'Smith',
  emailVerifiedAt: new Date(),
  acceptedAt: new Date(),
  role: UserType.THERAPIST,
  timeZone: 'America/New_York',
  phone: '+**********',
  address: {
    full_address: '123 Main St, New York, NY 10001'
  },
  registrationInfo: [
    {
      dataValues: {
        pageName: 'waitlist-notifications',
        payloadInfo: {
          notification_preferences: ['email', 'text'],
          phone_number: '+**********'
        }
      },
      pageName: 'waitlist-notifications',
      payloadInfo: {
        notification_preferences: ['email', 'text'],
        phone_number: '+**********'
      }
    },
    {
      dataValues: {
        pageName: 'practice-info',
        payloadInfo: {
          business_address: {
            full_address: '123 Main St, New York, NY 10001'
          }
        }
      },
      pageName: 'practice-info',
      payloadInfo: {
        business_address: {
          full_address: '123 Main St, New York, NY 10001'
        }
      }
    }
  ],
  calendars: [
    {
      type: 'google',
      email: TEST_THERAPIST_EMAIL,
      credentials: {
        tokens: {
          access_token: 'google_access_token',
          refresh_token: 'google_refresh_token'
        }
      }
    },
    {
      type: 'outlook',
      email: TEST_THERAPIST_EMAIL,
      credentials: {
        tokens: {
          access_token: 'outlook_access_token',
          refresh_token: 'outlook_refresh_token'
        }
      }
    }
  ]
} as any;

const mockAppointment = {
  id: TEST_APPOINTMENT_ID,
  name: `Therapy session between Jane Smith and John Doe`,
  description: 'Initial session',
  appointmentDate: TEST_APPOINTMENT_DATE,
  therapistId: TEST_THERAPIST_ID,
  patientId: TEST_USER_ID,
  duration: 60,
  amount: 100,
  isBilled: false,
  cancelledAt: null,
  type: 'telehealth',
  isMinor: false,
  minorId: null,
  googleEventId: 'google_event_id_123',
  outlookEventId: 'outlook_event_id_456',
  update: sinon.stub().resolves(true),
  therapist: mockTherapist,
  patient: mockPatient,
  patientPayment: {
    id: '999',
    amount: 100
  },
  minorPatient: null,
  patientCard: {
    stripePaymentMethodId: 'pm_123456',
  },
} as any;

const dummyPatientPaymentIntent = {
  id: 'pi_123456',
  customer: 'cus_123456',
  amount: 100,
  currency: 'usd',
} as any;

const	dummyCusPaymentMethod = {
  id: 'pm_123456',
  customer: 'cus_123456',
  metadata: {
    userId: 123,
  },
} as any;

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create API middleware
const apiMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Set up user for the request
  req.user = mockPatient;
  // Add authorization header
  req.headers.authorization = 'Bearer test_token';
  next();
};

// Mock the middleware
const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
const originalApiMiddleware = require(apiMiddlewarePath).default;

// Create the app instance and set up middleware
const app = express();
app.use(express.json());

// Controller path - will be required in before() hook
const controllerPath = '@/src/application/controllers/api/appointment/cancel-appointment.post';

describe('API Cancel Appointment Controller', function() {
  this.timeout(5000);

  let sandbox: sinon.SinonSandbox;
  let appointmentsFindOneStub: sinon.SinonStub;
  let deleteGoogleEventStub: sinon.SinonStub;
  let deleteOutlookEventStub: sinon.SinonStub;
  let sendMailStub: sinon.SinonStub;
  let sendSmsStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let isValidTimeZoneStub: sinon.SinonStub;
  let getTimezoneAbbrStub: sinon.SinonStub;
  let capitalizeFirstLetterStub: sinon.SinonStub;
  let formatDurationStub: sinon.SinonStub;
  let maskEmailStub: sinon.SinonStub;
  let maskPhoneNumberStub: sinon.SinonStub;
  let compileStub: sinon.SinonStub;
  let patientPaymentCreateStub: sinon.SinonStub;  
  let stripePaymentIntentsCreateStub: sinon.SinonStub;
  let stripePaymentMethodsRetrieveStub: sinon.SinonStub;

  before(function() {
    require(apiMiddlewarePath).default = apiMiddleware;
    app.use('/api', require(controllerPath).default);
  });

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Model stubs
    appointmentsFindOneStub = sandbox.stub(Appointments, 'findOne');
    patientPaymentCreateStub = sandbox.stub(PatientPayment, 'create');
    
    // Helper stubs
    isValidTimeZoneStub = sandbox.stub(GeneralHelper, 'isValidTimeZone').returns(true);
    getTimezoneAbbrStub = sandbox.stub(GeneralHelper, 'getTimezoneAbbr').returns('EST');
    capitalizeFirstLetterStub = sandbox.stub(GeneralHelper, 'capitalizeFirstLetter').callsFake((str) => str);
    formatDurationStub = sandbox.stub(GeneralHelper, 'formatDuration').returns('60 minutes');
    maskEmailStub = sandbox.stub(GeneralHelper, 'maskEmail').callsFake((email) => email);
    maskPhoneNumberStub = sandbox.stub(GeneralHelper, 'maskPhoneNumber').callsFake((phone) => phone);
    
    // Repository stubs
    deleteGoogleEventStub = sandbox.stub(CalendarRepository, 'deleteGoogleEvent').resolves(true);
    deleteOutlookEventStub = sandbox.stub(CalendarRepository, 'deleteOutlookEvent').resolves(true);
    
    // Mail and SMS stubs
    sendMailStub = sandbox.stub(mail, 'sendMail').resolves();
    sendSmsStub = sandbox.stub(TwilioConfig, 'sendSms').resolves();

    // Stripe stubs
    stripePaymentIntentsCreateStub = sandbox.stub(stripe.paymentIntents, 'create');
		stripePaymentMethodsRetrieveStub = sandbox.stub(stripe.paymentMethods, 'retrieve');
    
    // Email template stub
    compileStub = sandbox.stub(TherapistAppointmentCancelEmail, 'compile').returns({
      From: '<EMAIL>',
      To: TEST_THERAPIST_EMAIL,
      Subject: 'Appointment Cancelled',
      HtmlBody: '<html><body>Appointment cancelled</body></html>'
    });
    
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(function() {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    require.cache[apiMiddlewarePath] = undefined;
    require(apiMiddlewarePath).default = originalApiMiddleware;
  });

  describe('POST /cancel-appointment/:id', function() {
    it('should cancel an appointment successfully', async function() {
      // Setup
      appointmentsFindOneStub.resolves(mockAppointment);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      expect(appointmentsFindOneStub.calledOnce).to.be.true;
      expect(deleteGoogleEventStub.calledOnce).to.be.true;
      expect(deleteOutlookEventStub.calledOnce).to.be.true;
      expect(sendMailStub.calledOnce).to.be.false;
      expect(sendSmsStub.calledOnce).to.be.true;
    });


    it('should return forbidden if appointment id is not provided', async function() {
      // Execute - missing appointment id
      const response = await request(app)
        .post('/api/cancel-appointment/')
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.NotFound); // Express router will return 404 for this route
    });

    it('should return not found if appointment does not exist', async function() {
      // Setup
      appointmentsFindOneStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.NotFound);
      expect(response.body).to.have.property('message', 'Appointment not found');
    });

    it('should return forbidden if req user id does not match appointment patient id', async function() {
      // Setup
      appointmentsFindOneStub.resolves({
        ...mockAppointment,
        patient: {
          ...mockPatient,
          id: 666,
        }
      });
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'You are not authorized to cancel this appointment.');
    });

    it('should return forbidden if appointment is already billed', async function() {
      // Setup
      const billedAppointment = {
        ...mockAppointment,
        isBilled: true
      };
      appointmentsFindOneStub.resolves(billedAppointment);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('already billed');
    });

    it('should return forbidden if appointment is already canceled', async function() {
      // Setup
      const canceledAppointment = {
        ...mockAppointment,
        cancelledAt: new Date()
      };
      appointmentsFindOneStub.resolves(canceledAppointment);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('already canceled');
    });

    it('should return forbidden if appointment date has already passed', async function() {
      // Setup
      const pastAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().subtract(1, 'day').toISOString()
      };
      appointmentsFindOneStub.resolves(pastAppointment);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('already passed');
    });

    it('should bill the patient and cancel successfully if appointment is within 24 hours', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString()
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
			stripePaymentIntentsCreateStub.resolves(dummyPatientPaymentIntent);
      patientPaymentCreateStub.resolves();
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');
      console.log(response.body)
      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      expect(appointmentsFindOneStub.calledOnce).to.be.true;
      expect(deleteGoogleEventStub.calledOnce).to.be.true;
      expect(deleteOutlookEventStub.calledOnce).to.be.true;
      expect(sendMailStub.calledOnce).to.be.false;
      expect(sendSmsStub.calledOnce).to.be.true;
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
			expect(stripePaymentIntentsCreateStub.calledOnce).to.be.true;
			expect(patientPaymentCreateStub.calledOnce).to.be.true;
    });

    it('should fail if appointment is within 24 hours and does not have patientCard', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
        patientCard: null,
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'No payment method found for this appointment. It cannot be canceled.');
      expect(stripePaymentMethodsRetrieveStub.notCalled).to.be.true;
      expect(stripePaymentIntentsCreateStub.notCalled).to.be.true;
    });

    it('should fail if appointment is within 24 hours and error occurs during stripe payment method fetch', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.rejects(new Error('Error'));
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Error retrieving payment method. Please try again.');
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
      expect(stripePaymentIntentsCreateStub.notCalled).to.be.true;
    });

    it('should fail if appointment is within 24 hours and stripe payment method of user is not available', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'No payment method found. This appointment cannot be canceled.');
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
      expect(stripePaymentIntentsCreateStub.notCalled).to.be.true;
    });

    it('should fail if appointment is within 24 hours and stripe payment method metadata userId does not match patient id', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.resolves({
				...dummyCusPaymentMethod,
				metadata: {
					userId: 399,
				}
			});
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid payment method details. This appointment cannot be canceled.');
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
      expect(stripePaymentIntentsCreateStub.notCalled).to.be.true;
    });

    it('should fail if appointment is within 24 hours and does not have amount', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
        amount: null,
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid appointment amount. This appointment cannot be canceled.');
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
      expect(stripePaymentIntentsCreateStub.notCalled).to.be.true;
    });

    it('should fail if appointment is within 24 hours and error occurs during stripe paymentIntents create', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
      stripePaymentIntentsCreateStub.rejects(new Error('Error'));
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Error billing the appointment. Please try again.');
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
      expect(stripePaymentIntentsCreateStub.calledOnce).to.be.true;
    });

    it('should fail if appointment is within 24 hours and paymentIntent is not available', async function() {
      // Setup
      const soonAppointment = {
        ...mockAppointment,
        appointmentDate: dayjs().add(23, 'hours').toISOString(),
      };
      appointmentsFindOneStub.resolves(soonAppointment);
      stripePaymentMethodsRetrieveStub.resolves(dummyCusPaymentMethod);
      stripePaymentIntentsCreateStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Error billing the appointment. Please try again.');
      expect(stripePaymentMethodsRetrieveStub.calledOnce).to.be.true;
      expect(stripePaymentIntentsCreateStub.calledOnce).to.be.true;
    });

    it('should handle Google calendar event deletion failure gracefully', async function() {
      // Setup
      const mockAppointmentCopy = {
        ...mockAppointment,
        update: sinon.stub().resolves(true)
      };
      appointmentsFindOneStub.resolves(mockAppointmentCopy);
      deleteGoogleEventStub.resolves(false); // Google event deletion fails
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      // The appointment should still be marked as canceled even if event deletion fails
      expect(mockAppointmentCopy.update.calledWith({ cancelledAt: sinon.match.any })).to.be.true;
      // Since the deletion failed, there should be no call to update googleEventId to null
      expect(mockAppointmentCopy.update.calledWith({ googleEventId: null })).to.be.false;
    });

    it('should handle Outlook calendar event deletion failure gracefully', async function() {
      // Setup
      const mockAppointmentCopy = {
        ...mockAppointment,
        update: sinon.stub().resolves(true)
      };
      appointmentsFindOneStub.resolves(mockAppointmentCopy);
      deleteOutlookEventStub.resolves(false); // Outlook event deletion fails
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      // The appointment should still be marked as canceled even if event deletion fails
      expect(mockAppointmentCopy.update.calledWith({ cancelledAt: sinon.match.any })).to.be.true;
      // Since the deletion failed, there should be no call to update outlookEventId to null
      expect(mockAppointmentCopy.update.calledWith({ outlookEventId: null })).to.be.false;
    });

    it('should handle email notification errors gracefully', async function() {
      // Setup
      appointmentsFindOneStub.resolves(mockAppointment);
      sendMailStub.rejects(new Error('Email sending failed'));
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      expect(loggerErrorStub.calledWith(sinon.match(/Email error/))).to.be.true;
    });

    it('should handle SMS notification errors gracefully', async function() {
      // Setup
      appointmentsFindOneStub.resolves(mockAppointment);
      sendSmsStub.rejects(new Error('SMS sending failed'));
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      expect(loggerErrorStub.calledWith(sinon.match(/SMS error/))).to.be.true;
    });

    it('should handle appointment with minor patient correctly', async function() {
      // Setup
      const appointmentWithMinor = {
        ...mockAppointment,
        isMinor: true,
        minorId: '999',
        minorPatient: {
          firstName: 'Minor',
          lastName: 'Patient'
        }
      };
      appointmentsFindOneStub.resolves(appointmentWithMinor);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      // Verify that the minor patient name was used in notifications
      expect(compileStub.calledWith(sinon.match({ patientName: 'Minor Patient' }))).to.be.true;
    });

    it('should handle in-person appointment location correctly', async function() {
      // Setup
      const inPersonAppointment = {
        ...mockAppointment,
        type: 'in-person'
      };
      appointmentsFindOneStub.resolves(inPersonAppointment);
      
      // Execute
      const response = await request(app)
        .post(`/api/cancel-appointment/${TEST_APPOINTMENT_ID}`)
        .set('Authorization', 'Bearer test_token');

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('has been canceled successfully');
      // Verify that the location was included in notifications
      expect(compileStub.calledWith(sinon.match({ 
        location: '123 Main St, New York, NY 10001',
        appointmentType: 'in-person'
      }))).to.be.true;
    });
  });
});
