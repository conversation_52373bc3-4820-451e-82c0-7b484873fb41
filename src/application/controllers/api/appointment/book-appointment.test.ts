import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, {  } from 'express';
import { Appointments, MinorPatient, NormalOfficeHours, PatientCard, TherapistWaitlist, User } from '@/src/models';
import { mail } from '@/src/configs/sendgrid.config';
import { HttpStatusCode } from 'axios';
import { UserType } from '@/src/application/helpers/constant.helper';
import * as GeneralHelper from '@/src/application/helpers/general.helper';
import * as CalendarRepository from '@/src/application/repositories/calendar.repository';
import dayjs from 'dayjs';
import logger from '@/src/configs/logger.config';
import * as TwilioConfig from '@/src/configs/twilio.config';

// Define test constants
const TEST_USER_ID = '123';
const TEST_THERAPIST_ID = '456';
const TEST_EMAIL = '<EMAIL>';
const TEST_THERAPIST_EMAIL = '<EMAIL>';
const TEST_APPOINTMENT_DATE = dayjs().add(2, 'days').toISOString();
const TEST_OFFICE_HOUR_ID = '789';

// Mock User model instances
const mockPatient = {
  id: TEST_USER_ID,
  email: TEST_EMAIL,
  firstname: 'John',
  lastname: 'Doe',
  emailVerifiedAt: new Date(),
  role: UserType.PATIENT,
  stripeCustomerId: 'cus_123456',
  stripeSetupIntentId: 'seti_123456',
  patient_profile: {
    seekingTherapyFor: 'self'
  }
} as any;

const mockTherapist = {
  id: TEST_THERAPIST_ID,
  email: TEST_THERAPIST_EMAIL,
  firstname: 'Jane',
  lastname: 'Smith',
  emailVerifiedAt: new Date(),
  acceptedAt: new Date(),
  role: UserType.THERAPIST,
  timeZone: 'America/New_York',
  address: {
    full_address: '123 Main St, New York, NY 10001'
  },
  registrationInfo: [
    {
      dataValues: {
        pageName: 'payment-forms',
        payloadInfo: {
          session_fee: '100'
        }
      },
      pageName: 'payment-forms',
      payloadInfo: {
        session_fee: '100'
      }
    },
    {
      dataValues: {
        pageName: 'waitlist-notifications',
        payloadInfo: {
          booking_time_hour: '24',
          notification_preferences: ['email', 'text'],
          phone_number: '+**********'
        }
      },
      pageName: 'waitlist-notifications',
      payloadInfo: {
        booking_time_hour: '24',
        notification_preferences: ['email', 'text'],
        phone_number: '+**********'
      }
    }
  ],
  calendars: [
    {
      type: 'google',
      email: TEST_THERAPIST_EMAIL,
      credentials: {
        tokens: {
          access_token: 'google_access_token',
          refresh_token: 'google_refresh_token'
        }
      }
    }
  ],
  therapistWaitlist: []
} as any;

const mockWorkingHour = {
  id: TEST_OFFICE_HOUR_ID,
  userId: TEST_THERAPIST_ID,
  workingDay: 'monday',
  startTime: '09:00:00',
  endTime: '17:00:00',
  appointmentMethod: 'telehealth'
} as any;

const mockAppointment = {
  id: '999',
  name: `Therapy session between Jane Smith and John Doe`,
  description: 'Initial session',
  appointmentDate: TEST_APPOINTMENT_DATE,
  therapistId: TEST_THERAPIST_ID,
  patientId: TEST_USER_ID,
  workingHourId: TEST_OFFICE_HOUR_ID,
  duration: 60,
  isBilled: false,
  type: 'telehealth',
  isFromWaitlist: false,
  lockedByUserId: TEST_USER_ID,
  lockedAt: new Date(),
  amount: 100,
  update: sinon.stub().resolves(true)
} as any;

const mockPatientCard = {
  id: 1,
  patientId: TEST_USER_ID,
  stripeSetupIntentId: 'seti_123456',
  stripePaymentMethodId: 'pm_123456',
  cardLastFourDigits: '4242',
  cardBrand: 'Visa',
  cardExpMonth: 12,
  cardExpYear: 2100,
} as any;

// Set up Express response methods that are used in the controller
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create API middleware
const apiMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Set up user for the request
  req.user = mockPatient;
  // Add authorization header
  req.headers.authorization = 'Bearer test_token';
  next();
};

// Mock the middleware
const apiMiddlewarePath = '@/src/application/middlewares/api.middleware';
const originalApiMiddleware = require(apiMiddlewarePath).default;

// Create the app instance and set up middleware
const app = express();
app.use(express.json());

// Controller path - will be required in before() hook
const controllerPath = '@/src/application/controllers/api/appointment/book-appointment';

describe('API Book Appointment Controller', function() {
  this.timeout(5000);

  let sandbox: sinon.SinonSandbox;
  let userFindByPkStub: sinon.SinonStub;
  let normalOfficeHoursFindOneStub: sinon.SinonStub;
  let appointmentsFindOneStub: sinon.SinonStub;
  let appointmentsCreateStub: sinon.SinonStub;
  let therapistWaitlistUpdateStub: sinon.SinonStub;
  let createGoogleEventStub: sinon.SinonStub;
  let createOutlookEventStub: sinon.SinonStub;
  let sendMailStub: sinon.SinonStub;
  let sendSmsStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let isValidTimeZoneStub: sinon.SinonStub;
  let getTimezoneAbbrStub: sinon.SinonStub;
  let capitalizeFirstLetterStub: sinon.SinonStub;
  let formatDurationStub: sinon.SinonStub;
  let maskEmailStub: sinon.SinonStub;
  let maskPhoneNumberStub: sinon.SinonStub;
  let patientCardFindOneStub: sinon.SinonStub;
  let minorPatientFindOneStub: sinon.SinonStub;

  before(function() {
    require(apiMiddlewarePath).default = apiMiddleware;
    app.use('/api', require(controllerPath).default);
  })

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    // Model stubs
    userFindByPkStub = sandbox.stub(User, 'findByPk');
    normalOfficeHoursFindOneStub = sandbox.stub(NormalOfficeHours, 'findOne');
    appointmentsFindOneStub = sandbox.stub(Appointments, 'findOne');
    appointmentsCreateStub = sandbox.stub(Appointments, 'create');
    therapistWaitlistUpdateStub = sandbox.stub(TherapistWaitlist, 'update');
    patientCardFindOneStub = sandbox.stub(PatientCard, 'findOne').resolves(mockPatientCard);
    minorPatientFindOneStub = sandbox.stub(MinorPatient, 'findOne');
    
    // Helper stubs
    isValidTimeZoneStub = sandbox.stub(GeneralHelper, 'isValidTimeZone').returns(true);
    getTimezoneAbbrStub = sandbox.stub(GeneralHelper, 'getTimezoneAbbr').returns('EST');
    capitalizeFirstLetterStub = sandbox.stub(GeneralHelper, 'capitalizeFirstLetter').callsFake((str) => str);
    formatDurationStub = sandbox.stub(GeneralHelper, 'formatDuration').returns('60 minutes');
    maskEmailStub = sandbox.stub(GeneralHelper, 'maskEmail').callsFake((email) => email);
    maskPhoneNumberStub = sandbox.stub(GeneralHelper, 'maskPhoneNumber').callsFake((phone) => phone);
    
    // Repository stubs
    createGoogleEventStub = sandbox.stub(CalendarRepository, 'createGoogleEvent').resolves({ id: 'google_event_id' });
    createOutlookEventStub = sandbox.stub(CalendarRepository, 'createOutlookEvent').resolves({ id: 'outlook_event_id' });
    
    // Mail and SMS stubs
    sendMailStub = sandbox.stub(mail, 'sendMail').resolves();
    sendSmsStub = sandbox.stub(TwilioConfig, 'sendSms').resolves();
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
  });

  afterEach(function() {
    sandbox.restore();
    // Restore original middleware
    // require(apiMiddlewarePath).default = originalApiMiddleware;
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    require.cache[apiMiddlewarePath] = undefined;
    require(apiMiddlewarePath).default = originalApiMiddleware;
  })

  describe('POST /book-appointment', function() {
    // Test for missing required fields
    it('should return forbidden if required fields are missing', async function() {
      // Execute - missing therapistOfficeHourId
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Missing required information');
    });

    it('should return forbidden if booking for a minor without minor id', async function() {
      // Execute - missing therapistOfficeHourId
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
          isMinor: true,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Minor ID is required when booking an appointment for a minor.');
    });

    it('should return forbidden if minor patient is not found', async function() {
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      minorPatientFindOneStub.resolves(null);

      // Execute - missing therapistOfficeHourId
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          description: 'Initial session',
          type: 'telehealth',
          patientCardId: 1,
          isMinor: true,
          minorId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message', 'Invalid Minor ID. Please provide a valid Minor ID.');
    });

    it('should book an appointment successfully', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      normalOfficeHoursFindOneStub.resolves(mockWorkingHour);
      appointmentsFindOneStub.resolves(null); // No existing appointment
      appointmentsCreateStub.resolves(mockAppointment);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(response.body).to.have.property('message').that.includes('Appointment scheduled successfully');
      expect(response.body).to.have.property('appointment');
      expect(response.body.appointment).to.have.property('id');
      expect(userFindByPkStub.calledTwice).to.be.true;
      expect(normalOfficeHoursFindOneStub.calledOnce).to.be.true;
      expect(appointmentsFindOneStub.calledOnce).to.be.true;
      expect(appointmentsCreateStub.calledOnce).to.be.true;
      expect(createGoogleEventStub.calledOnce).to.be.true;
    });

    it('should handle minor patient information correctly', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      normalOfficeHoursFindOneStub.resolves(mockWorkingHour);
      appointmentsFindOneStub.resolves(null); // No existing appointment
      minorPatientFindOneStub.resolves({
        firstName: 'Minor',
        lastName: 'Patient'
      });
      
      // Create a mock appointment with minor patient info
      const appointmentWithMinor = {
        ...mockAppointment,
        isMinor: true,
        minorId: '789',
        update: sinon.stub().resolves(true)
      };
      appointmentsCreateStub.resolves(appointmentWithMinor);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          isMinor: true,
          minorId: '789',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(appointmentsCreateStub.calledOnce).to.be.true;
      expect(appointmentsCreateStub.firstCall.args[0]).to.include({
        isMinor: true,
        minorId: '789'
      });
    });

    it('should handle in-person appointment location correctly', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      normalOfficeHoursFindOneStub.resolves({ ...mockWorkingHour, appointmentMethod: 'in-person-and-telehealth' });
      appointmentsFindOneStub.resolves(null); // No existing appointment
      
      // Create a mock appointment with in-person type
      const inPersonAppointment = {
        ...mockAppointment,
        type: 'in-person',
        update: sinon.stub().resolves(true)
      };
      appointmentsCreateStub.resolves(inPersonAppointment);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'in-person',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(appointmentsCreateStub.calledOnce).to.be.true;
      expect(appointmentsCreateStub.firstCall.args[0]).to.include({
        type: 'in-person'
      });
    });

    it('should handle therapist with practice info for in-person appointment', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      
      // Create therapist with practice info but no address
      const therapistWithPracticeInfo = {
        ...mockTherapist,
        address: null,
        registrationInfo: [
          ...mockTherapist.registrationInfo,
          {
            dataValues: {
              pageName: 'practice-info',
              payloadInfo: {
                business_address: {
                  full_address: '456 Practice St, New York, NY 10001'
                }
              }
            },
            pageName: 'practice-info',
            payloadInfo: {
              business_address: {
                full_address: '456 Practice St, New York, NY 10001'
              }
            }
          }
        ]
      };
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(therapistWithPracticeInfo);
      normalOfficeHoursFindOneStub.resolves({ ...mockWorkingHour, appointmentMethod: 'in-person-and-telehealth' });
      appointmentsFindOneStub.resolves(null); // No existing appointment
      
      // Create a mock appointment with in-person type
      const inPersonAppointment = {
        ...mockAppointment,
        type: 'in-person',
        update: sinon.stub().resolves(true)
      };
      appointmentsCreateStub.resolves(inPersonAppointment);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'in-person',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(appointmentsCreateStub.calledOnce).to.be.true;
      expect(createGoogleEventStub.calledOnce).to.be.true;
      // The event should include the practice address
      expect(createGoogleEventStub.firstCall.args[0].eventData.location).to.include('456 Practice St');
    });

    it('should return forbidden if user is not authenticated', async function() {
      // Setup - simulate no user in request
      const noAuthMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
        req.user = undefined; // Using undefined instead of null to match the expected type
        next();
      };
      app.use('/api/no-auth', noAuthMiddleware, require('@/src/application/controllers/api/appointment/book-appointment').default);

      // Execute
      const response = await request(app)
        .post('/api/no-auth/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      // The exact error message is 'Invalid Request.' from the throw, but the response.forbidden returns 'Invalid Request. User Info not found.'
      expect(response.body).to.have.property('message').that.includes('Invalid Request');
    });

    it('should return forbidden if user is not a patient', async function() {
      // Setup
      const therapistUser = { ...mockPatient, role: UserType.THERAPIST };
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(therapistUser);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Only patients can book appointments');
    });

    it('should return forbidden if patient email is not verified', async function() {
      // Setup
      const unverifiedPatient = { ...mockPatient, emailVerifiedAt: null };
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(unverifiedPatient);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('verify your email address');
    });

    it('should return forbidden if patientCardId is invalid', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);

      patientCardFindOneStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Invalid Card ID. Please provide a valid Patient Card ID.');
    });

    it('should return forbidden if patient card is expired', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);

      patientCardFindOneStub.resolves({
        ...mockPatientCard,
        cardExpMonth: 12,
        cardExpYear: 2024,
      });
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Your card has expired. Please update your payment method.');
    });

    it('should return forbidden if patient card expires before the appointment date', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);

      patientCardFindOneStub.resolves({
        ...mockPatientCard,
        cardExpMonth: 5,
        cardExpYear: 2100,
      });
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: '2100-06-04T17:00:00.000Z',
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Your card expires before the appointment date. Please update your payment method.');
    });

    it('should return not found if therapist does not exist', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(null);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.NotFound);
      expect(response.body).to.have.property('message', 'Therapist not found');
    });

    it('should return forbidden if therapist is not verified', async function() {
      // Setup
      const unverifiedTherapist = { ...mockTherapist, emailVerifiedAt: null };
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(unverifiedTherapist);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('therapist is not valid');
    });

    it('should return forbidden if appointment date is invalid', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: 'invalid-date',
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Invalid date format');
    });

    it('should return forbidden if booking time is less than required notice', async function() {
      // Setup
      const therapistWithNotice = { 
        ...mockTherapist,
        registrationInfo: [
          {
            dataValues: {
              pageName: 'payment-forms',
              payloadInfo: {
                session_fee: '100'
              }
            },
            pageName: 'payment-forms',
            payloadInfo: {
              session_fee: '100'
            }
          },
          {
            dataValues: {
              pageName: 'waitlist-notifications',
              payloadInfo: {
                booking_time_hour: '48',
                notification_preferences: ['email', 'text'],
                phone_number: '+**********'
              }
            },
            pageName: 'waitlist-notifications',
            payloadInfo: {
              booking_time_hour: '48',
              notification_preferences: ['email', 'text'],
              phone_number: '+**********'
            }
          }
        ]
      };
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(therapistWithNotice);
      normalOfficeHoursFindOneStub.resolves(mockWorkingHour);
      
      // Execute - using a date that's only 24 hours in the future
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: dayjs().add(1, 'day').toISOString(),
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('This therapist requires a 48 hour advance notice');
    });

    it('should return forbidden if working hour does not exist', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      normalOfficeHoursFindOneStub.resolves(null);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Invalid office hour');
    });

    it('should return forbidden if appointment type does not match office hour method', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      normalOfficeHoursFindOneStub.resolves(mockWorkingHour);
      
      // Execute - sending in-person for a telehealth-only office hour
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'in-person',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('appointment type does not match');
    });

    it('should return forbidden if no appointment method is set for office hour', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      const workingHourWithoutMethod = { ...mockWorkingHour, appointmentMethod: null };
      normalOfficeHoursFindOneStub.resolves(workingHourWithoutMethod);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('No appointment method is set for this office hour');
    });

    it('should return forbidden if invalid appointment type for in-person-and-telehealth method', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      const workingHourWithBothMethods = { ...mockWorkingHour, appointmentMethod: 'in-person-and-telehealth' };
      normalOfficeHoursFindOneStub.resolves(workingHourWithBothMethods);
      
      // Execute - sending an invalid type
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'invalid-type',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('Invalid appointment type');
    });

    it('should return forbidden if appointment slot is already booked', async function() {
      // Setup
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(mockTherapist);
      normalOfficeHoursFindOneStub.resolves(mockWorkingHour);
      appointmentsFindOneStub.resolves(mockAppointment); // Existing appointment
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: TEST_APPOINTMENT_DATE,
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Forbidden);
      expect(response.body).to.have.property('message').that.includes('already booked');
    });

    it('should update waitlist status if booking from waitlist', async function() {
      // Setup
      const futureDate = dayjs().add(7, 'days');
      const therapistWithWaitlist = {
        ...mockTherapist,
        therapistWaitlist: [
          {
            patientId: TEST_USER_ID,
            therapistId: TEST_THERAPIST_ID,
            status: 'active'
          }
        ]
      };
      
      userFindByPkStub.withArgs(TEST_USER_ID).resolves(mockPatient);
      userFindByPkStub.withArgs(TEST_THERAPIST_ID).resolves(therapistWithWaitlist);
      normalOfficeHoursFindOneStub.resolves(mockWorkingHour);
      appointmentsFindOneStub.resolves(null);
      
      const waitlistAppointment = {
        ...mockAppointment,
        isFromWaitlist: true,
        appointmentDate: futureDate.toISOString()
      };
      appointmentsCreateStub.resolves(waitlistAppointment);
      
      // Execute
      const response = await request(app)
        .post('/api/book-appointment')
        .send({
          appointmentDate: futureDate.toISOString(),
          therapistId: TEST_THERAPIST_ID,
          description: 'Initial session',
          therapistOfficeHourId: TEST_OFFICE_HOUR_ID,
          type: 'telehealth',
          patientCardId: 1,
        });

      // Assert
      expect(response.status).to.equal(HttpStatusCode.Ok);
      expect(therapistWaitlistUpdateStub.calledOnce).to.be.true;
      expect(therapistWaitlistUpdateStub.firstCall.args[0]).to.deep.equal({
        status: 'booked'
      });
    });
  });
});
