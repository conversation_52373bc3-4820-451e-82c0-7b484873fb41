import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { ForbiddenError, NotFoundError } from '@/src/application/handlers/errors'
import { Appointments, MinorPatient, NormalOfficeHours, PatientCard, TherapistWaitlist, User, UserRegistrationInfo } from '@/src/models'
import { UserType } from '@/src/application/helpers/constant.helper'
import dayjs from 'dayjs'
import timezone from "dayjs/plugin/timezone";
import { PaymentForms, PracticeInfo, WaitlistNotifications } from '@/src/types/registration.interface'
import logger from '@/src/configs/logger.config'
import { capitalizeFirstLetter, formatDuration, getTimezoneAbbr, isValidTimeZone, maskEmail, maskPhoneNumber } from '@/src/application/helpers/general.helper'
import { Op } from 'sequelize';
import { createGoogleEvent, createOutlookEvent } from '@/src/application/repositories/calendar.repository'
import { PatientAppointmentNotifyEmail, TherapistAppointmentNotifyEmail } from '@/src/application/emails/appointment-notify.email'
import { mail } from '@/src/configs/sendgrid.config'
import { sendSms } from '@/src/configs/twilio.config'
import utc from 'dayjs/plugin/utc';

dayjs.extend(timezone);
dayjs.extend(utc);

const router = express.Router()

/********************************
 * * Book an appointment
 ********************************/
router.post(
	'/book-appointment',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user){			
			throw new ForbiddenError('Invalid Request.')
		} 
		logger.info(`User ${user.id} is attempting to book an appointment.`);
	
		if (!user) throw new ForbiddenError('Invalid Request.')
		
		const { appointmentDate, therapistId, description, therapistOfficeHourId, minorId, isMinor, type, patientCardId, conflictingAppointmentId } = req.body;
		if (!appointmentDate || !therapistId || !therapistOfficeHourId || !type || !patientCardId) throw new ForbiddenError('Missing required information.')
		
		if (isMinor && !minorId) return res.forbidden('Minor ID is required when booking an appointment for a minor.');
		
		// Validate Patient
		const patient = await User.findByPk(user.id, {
			include: ['patient_profile']
		})
		if (!patient) {
			return res.forbidden('Invalid Request. User Info not found.')
		}
		if (patient.role !== UserType.PATIENT) {
			logger.info(`User ${user.id} attempted to book an appointment, but is not a patient.`);
			return res.forbidden('You are not authorized to book an appointment. Only patients can book appointments.')
		}
		if (!patient.emailVerifiedAt) {
			logger.info(`User ${user.id} attempted to book an appointment, but has not verified their email address.`);
			return res.forbidden('You have not verified your email address. Please verify your email address before booking an appointment.')
		}

		let minorPatient;
		if (isMinor && minorId) {
			minorPatient = await MinorPatient.findOne({
				where: {
					id: minorId,
					userId: user.id,
				},
			});

			if (!minorPatient) {
				logger.info(`User ${user.id} attempted to book an appointment for a minor, but the minor ID is invalid.`);
				return res.forbidden('Invalid Minor ID. Please provide a valid Minor ID.');
			}
		}
		
		const patientCard = await PatientCard.findOne({
			where: {
				id: patientCardId,
				patientId: patient.id,
			},
		});
		if (!patientCard) {
			logger.info(`Patient ${patient.id} attempted to book an appointment with an invalid patient card ID.`);
			return res.forbidden('Invalid Card ID. Please provide a valid Patient Card ID.');
		}

		const currentMonth = dayjs().month() + 1; // dayjs month is 0-indexed, so we add 1
		const currentYear = dayjs().year();

		const appointmentMonth = dayjs(appointmentDate).month() + 1;
		const appointmentYear = dayjs(appointmentDate).year();

		if (patientCard.cardExpMonth && patientCard.cardExpYear) {
			if (currentYear > patientCard.cardExpYear ||
				(currentYear === patientCard.cardExpYear && currentMonth > patientCard.cardExpMonth)) {
					logger.info(`Patient ${patient.id} attempted to book an appointment with an expired card.`);
					return res.forbidden('Your card has expired. Please update your payment method.');
			}
			else if (appointmentYear > patientCard.cardExpYear ||
				(appointmentYear === patientCard.cardExpYear && appointmentMonth > patientCard.cardExpMonth)) {
					logger.info(`Patient ${patient.id} attempted to book an appointment with a card that expires before the appointment date.`);
					return res.forbidden('Your card expires before the appointment date. Please update your payment method.');
			}
		}

		// Validate Therapist
		const therapist = await User.findByPk(therapistId, {
			include: [
				'calendars',
				{
					model: UserRegistrationInfo,
					attributes: ['pageName', 'payloadInfo'],
          where: {
            pageName: {
							[Op.in]: ['payment-forms', 'waitlist-notifications'],
						}
          },
          required: false,
				},
				{
					model: TherapistWaitlist,
					where: {
						patientId: user.id,
						status: {
							[Op.in]: ['waiting', 'notified'],
						},
					},
					required: false,
				}
			],
		})
		if (!therapist) throw new NotFoundError('Therapist not found')

		if (therapist.role !== UserType.THERAPIST)
			throw new ForbiddenError('The provided therapist id is not a valid Therapist ID. Please provide a valid Therapist ID.')
		if (!therapist.emailVerifiedAt || !therapist.acceptedAt) throw new ForbiddenError('The therapist is not valid.')

		// Validate appointment date
		const validDate = dayjs.utc(appointmentDate, 'YYYY-MM-DDTHH:mm:ss.SSS[Z]', true);
		if (!validDate.isValid()) throw new ForbiddenError('Invalid date format. Please provide a valid ISO string in UTC.');

		const therapistTimeZone = therapist.timeZone;
		const therapistDate = validDate.millisecond(0).tz(therapistTimeZone); // appointment date in therapist's timezone

		const dayOfWeek = therapistDate.format('dddd'); // day of week for the therapist

		const waitlistInfo = therapist.registrationInfo.find(
			(info) => info.dataValues.pageName === 'waitlist-notifications')?.payloadInfo as unknown as WaitlistNotifications;
    if (waitlistInfo?.booking_time_hour) {
			const bookingHours = Number(waitlistInfo.booking_time_hour.trim());

			if (!isNaN(bookingHours) && bookingHours > 0) {
				const now = dayjs().millisecond(0);
				const diffInHours = dayjs(appointmentDate).diff(now, "hour");

				if (diffInHours < bookingHours) {
					return res.forbidden(
						`This therapist requires a ${bookingHours} hour advance notice for scheduling. Please select a later time.`
					);
				}
			}
		}

		const workingHour = await NormalOfficeHours.findOne({
			where: {
				id: therapistOfficeHourId,
				userId: therapist.id,
				workingDay: dayOfWeek.toLowerCase(),
			},
		});

		if (!workingHour) return res.forbidden('Invalid office hour. Please provide a valid office hour associated with the therapist for the provided date.')

		const appointmentType = type.toLowerCase();
		if (!workingHour.appointmentMethod) {
			return res.forbidden('No appointment method is set for this office hour. Please contact your therapist for more information.');
		}

		if (workingHour.appointmentMethod === 'in-person-and-telehealth') {
			if (!['in-person', 'telehealth'].includes(appointmentType)) {
				logger.info(`User ${user.id} attempted to book an appointment with appointment type ${appointmentType}, but the allowed types for this office hour are "in-person" or "telehealth".`);
				return res.forbidden('Invalid appointment type. Allowed types are "in-person" or "telehealth".');
			}
		} else if (workingHour.appointmentMethod !== appointmentType) {
			logger.info(`User ${user.id} attempted to book an appointment with appointment type ${appointmentType}, but the therapist's office hour is set to ${workingHour.appointmentMethod}.`);
			return res.forbidden('The appointment type does not match the appointment type associated with the provided appointment date.');
		}

		const appointment = await Appointments.findOne({
			where: {
				therapistId: therapist.id,
				workingHourId: workingHour.id,
				appointmentDate: validDate.millisecond(0).toISOString(),
				cancelledAt: null,
			},
		})

		if (appointment) {
			return res.forbidden('This appointment date is already booked. Please select a different slot.');
		}

		if (conflictingAppointmentId) {
			const conflictingAppointment = await Appointments.findOne({
				where: {
					id: Number(conflictingAppointmentId),
					patientId: patient.id,
				},
			});

			if (conflictingAppointment) {
				await conflictingAppointment.update({
					cancelledAt: dayjs().toDate(),
				});
			}
		}

		// const nowInTherapistTZ = dayjs().tz(therapistTimeZone).millisecond(0);
		// const isSameWeek = therapistDate.isSame(nowInTherapistTZ, 'week'); // checking if the appointment date week is same as current week

		// let appointmentStart, appointmentEnd;

		// if (isSameWeek) {
		// 	// If the appointment is in the current week, check from now until the end of the week
		// 	appointmentStart = nowInTherapistTZ;
		// 	appointmentEnd = nowInTherapistTZ.endOf('week').millisecond(0);
		// } else {
		// 	// Otherwise, check from the start to the end of the appointment week
		// 	appointmentStart = therapistDate.startOf('week').millisecond(0);
		// 	appointmentEnd = therapistDate.endOf('week').millisecond(0);
		// }

		// // Count active appointments for the patient in the same week (excluding expired ones)
		// const weeklyAppointments = await Appointments.count({
		// 	where: {
		// 		therapistId: therapist.id,
		// 		patientId: patient.id,
		// 		appointmentDate: {
		// 			[Op.between]: [appointmentStart.toISOString(), appointmentEnd.toISOString()]
		// 		}
		// 	}
		// });

		// if (weeklyAppointments >= 1) return res.forbidden('You can only book a maximum of one appointments per week with the same therapist.');

		// const therapistGoogleCalendar = therapist.calendars.find((calendar) => calendar.type === 'google')
		// const therapistOutlookCalendar = therapist.calendars.find((calendar) => calendar.type === 'outlook')

		// let category: string;

		// // checking whether the appointment is an initial appointment or not
		// const existingAppointment = await Appointments.findOne({
		// 	where: { therapistId: therapist.id, patientId: patient.id },
		// });

		// if (!existingAppointment) category = 'intake';
		// else category = patient?.patient_profile?.seekingTherapyFor || 'self';

		// const	duration = therapist?.durationAndCost?.find((duration) => duration.category === category)?.duration || 60
		const duration = 60;

		const paymentInfo = therapist.registrationInfo.find(
			(info) => info.dataValues.pageName === 'payment-forms')?.payloadInfo as unknown as PaymentForms;
		const sessionFee = Number(paymentInfo?.session_fee);

		if (isNaN(sessionFee) || sessionFee <= 0) {
			throw new ForbiddenError(
				"Session fee is not set correctly for this therapist. You can check out other therapists or contact your therapist for more information."
			);
		}

		logger.info(`User ${user.id} passed all validation checks. Proceeding with appointment booking.`);

		let isFromWaitlist = false;
		if (therapist?.therapistWaitlist?.length > 0) {
			isFromWaitlist = true;
		}

		const newAppointment = await Appointments.create({
			name: `Therapy session between ${therapist.firstname} ${therapist.lastname} and ${patient.firstname} ${patient.lastname}`,
			description: description || null,
			appointmentDate: validDate.millisecond(0).toISOString(),
			therapistId: therapist.id,
			patientId: patient.id,
			workingHourId: workingHour.id,
			duration,
			isBilled: false,
			minorId: isMinor ? minorId : null,
			isMinor: isMinor,
			type: appointmentType,
			isFromWaitlist,
			lockedByUserId: patient.id,
			lockedAt: new Date(),
			amount: sessionFee,
			patientCardId: patientCard.id,
		});
		
		if (isFromWaitlist) {
			await TherapistWaitlist.update({
				status: 'booked',
			}, {
				where: {
					patientId: newAppointment.patientId,
					therapistId: newAppointment.therapistId,
				}
			});
		}

		try {
			const therapistTimeZone = therapist?.timeZone;

			const therapistFullName = capitalizeFirstLetter(`${therapist?.firstname} ${therapist?.lastname}`);
			let patientFullName = capitalizeFirstLetter(`${patient?.firstname} ${patient?.lastname}`);

			if (newAppointment.isMinor && newAppointment.minorId && minorPatient) {
				if (minorPatient.firstName) {
					patientFullName = capitalizeFirstLetter(`${minorPatient.firstName} ${minorPatient?.lastName}`);
				}
			}

			let appointmentLocation: string | null = null;
			if (newAppointment.type === 'in-person') {
				appointmentLocation = (therapist.address as any)?.full_address || null;

				if (!appointmentLocation) {
					const therapistPracticeInfo = therapist?.registrationInfo?.find(
						(info) => info.pageName === 'practice-info'
					)?.payloadInfo as unknown as PracticeInfo;

					if (therapistPracticeInfo && therapistPracticeInfo.business_address) {
						appointmentLocation = therapistPracticeInfo.business_address?.full_address || null;
					}	
				}
			} else if (newAppointment.type === 'telehealth') {
				appointmentLocation = 'Telehealth';
			}

			if (therapistTimeZone && isValidTimeZone(therapistTimeZone)) {
				// Create calendar event
				try {
					const therapistDate = dayjs(newAppointment.appointmentDate).millisecond(0).tz(therapistTimeZone);

					const mainDescription = newAppointment.description || `Therapy Session Scheduled via NextTherapist`;
					
					const therapistGoogleCalendar = therapist.calendars.find((calendar) => calendar.type === 'google')
					const therapistOutlookCalendar = therapist.calendars.find((calendar) => calendar.type === 'outlook')

					let googleEvent;
					if (therapistGoogleCalendar) {
						try {
							const { tokens } = therapistGoogleCalendar.credentials as any;
							if (tokens) {
								const description = 
									`<b>${mainDescription}</b><br><br>` +
									`<b>Patient Name:</b> ${patientFullName}<br>` +
									`<b>Reason for Seeking Help:</b> ${patient.reason_for_help || 'N/A'}<br>` +
									(patient.phone ? `<b>Phone Number:</b> (${patient.phone})` : '');

								const eventData = {
									summary: `Therapy session for ${patientFullName} with ${therapistFullName}`,
									description: description,
									start: {
										dateTime: therapistDate.toISOString(),
										timeZone: therapistTimeZone,
									},
									end: {
										dateTime: therapistDate.add(duration, 'minute').toISOString(),
										timeZone: therapistTimeZone,
									},
									...(appointmentLocation ? { location: appointmentLocation } : {}),
									attendees: [
										{
											email: patient.email,
											displayName: patientFullName,
										},
										{
											email: therapistGoogleCalendar.email,
											displayName: therapistFullName,
										},
									],
									reminders: {
										useDefault: false,
										overrides: [
											{ method: 'email', minutes: 24 * 60 },
											{ method: 'email', minutes: 2 * 60 },
										],
									},
								}
								logger.info(`Creating Google Calendar event for therapist ID: ${therapist.id}.`);
								googleEvent = await createGoogleEvent({ tokens, eventData })
							}
						} catch (error) {
							logger.error(`Book Appointment - Error creating Google Calendar event for therapist ID: ${therapist.id}.`, error);
						}
					}

					let outlookEvent;
					if (therapistOutlookCalendar) {
						try {
							const { tokens } = therapistOutlookCalendar.credentials as any;
							if (tokens) {
								const description = 
									`<b>${mainDescription}</b><br><br>` +
									`<b>Patient Name:</b> ${patientFullName}<br>` +
									`<b>Patient Email:</b> ${patient.email}<br>` +
									`<b>Reason for Seeking Help:</b> ${patient.reason_for_help || 'N/A'}<br>` +
									(patient.phone ? `<b>Phone Number:</b> (${patient.phone})` : '');

								const eventData = {
									subject: `Therapy session for ${patientFullName} with ${therapistFullName}`,
									body: {
										content: description,
										contentType: 'html',
									},
									start: {
										dateTime: therapistDate.toISOString(),
										timeZone: therapistTimeZone,
									},
									end: {
										dateTime:  therapistDate.add(duration, 'minute').toISOString(),
										timeZone: therapistTimeZone,
									},
									...(appointmentLocation ? { location: { displayName: appointmentLocation } } : {}),
									attendees: [
										{
											emailAddress: {
												address: patient.email,
												name: patientFullName
											},
											type: 'required'
										},
									]
								}
								logger.info(`Creating Outlook Calendar event for therapist ID: ${therapist.id}.`);
								outlookEvent = await createOutlookEvent({ tokens, eventData, userId: Number(therapist.id) })
							}
						} catch (error) {
							logger.error(`Book Appointment - Error creating Outlook Calendar event for therapist ID: ${therapist.id}.`, error);
						}
					}

					await newAppointment.update({
						googleEventId: googleEvent ? googleEvent?.id : null,
						outlookEventId: outlookEvent ? outlookEvent?.id : null,
					});
				} catch (error) {
					logger.error(`Book Appointment - Error creating calendar event for therapist ID: ${therapist.id}.`, error);
				}

				// Notifying therapist
				try {
					logger.info(`Notifying therapist ID: ${therapist.id} for booked appointment ID: ${newAppointment.id}.`);
					logger.info(`Preparing notification data for therapist ID: ${therapist.id}.`);
					const therapistAppDate = dayjs(newAppointment.appointmentDate).tz(therapistTimeZone).format('MMMM D, YYYY');
					const therapistAppTime = dayjs(newAppointment.appointmentDate).tz(therapistTimeZone).format('h:mm A');
					const therapistTimeZoneAbbr = getTimezoneAbbr(therapistTimeZone);
					const bookedAt = dayjs(newAppointment.createdAt).tz(therapistTimeZone).format('MMMM D, YYYY h:mm A');

					const therapistNotifInfo = therapist?.registrationInfo?.find(
						(info) => info.pageName === 'waitlist-notifications'
					)?.payloadInfo as unknown as WaitlistNotifications;

					let therapistPhone = therapist?.phone;
					if (therapistNotifInfo && therapistNotifInfo?.notification_preferences?.includes('text') && therapistNotifInfo.phone_number) {
						therapistPhone = therapistNotifInfo.phone_number;
					}
					
					// Email Notification
					const shouldSendEmail = therapistNotifInfo ? therapistNotifInfo?.notification_preferences?.includes('email') : false;
					if (shouldSendEmail) {
						try {
							const therapistEmailData = TherapistAppointmentNotifyEmail.compile({
								email: therapist.email,
								subject: `Appointment booked by ${patientFullName}`,
								therapistName: therapistFullName,
								patientName: patientFullName,
								appointmentDate: therapistAppDate,
								appointmentTime: `${therapistAppTime} (${therapistTimeZoneAbbr})`,
								duration: formatDuration(newAppointment.duration),
								location: appointmentLocation || '',
								bookedAt,
								title: `${patientFullName} has booked an appointment with you. See the details below:`,
								appointmentType: capitalizeFirstLetter(newAppointment.type),
								header: `Appointment booked by ${patientFullName}`
							});
							logger.info(`Sending New Appointment Booked Email notification to ${maskEmail(therapist.email)}`);
							await mail.sendMail({
								to: therapistEmailData.To,
								subject: therapistEmailData.Subject,
								html: therapistEmailData.HtmlBody,
							});
							logger.info(`Email notification sent to ${maskEmail(therapist.email)}`);
						} catch (error) {
							logger.error(`New Appointment Email - Therapist Notify Email error - Therapist ID: (${therapist.id}) - Appointment ID: (${newAppointment.id}): ${error}.`);
						}
					}

					// SMS Notification
					const shouldSendSms = therapistNotifInfo ? therapistNotifInfo?.notification_preferences?.includes('text') : false;
					if (shouldSendSms && therapistPhone) {
						try {
							logger.info(`Sending New Appointment Booked SMS notification to ${maskPhoneNumber(therapistPhone)}`);
							await sendSms(
								therapistPhone,
								`NextTherapist - Appointment Booked: ${patientFullName} has booked an appointment scheduled for ${therapistAppDate} at ${therapistAppTime}`
							);
							logger.info(`SMS notification sent to ${maskPhoneNumber(therapistPhone)}`);
						} catch (error) {
							logger.error(`New Appointment SMS - Therapist Notify SMS error - Therapist ID: (${therapist.id}) - Appointment ID: (${newAppointment.id}): ${error}.`);
						}
					}
				} catch (error) {
					logger.error(`New Appointment - Therapist Notify error - Therapist ID: (${therapist.id}) - Appointment ID: (${newAppointment.id}): ${error}.`);
				}
			}

			const patientTimeZone = patient?.timeZone;
			if (patientTimeZone && isValidTimeZone(patientTimeZone)) {
				// Notifying patient
				try {
					logger.info(`Notifying patient ID: ${patient.id} for booked appointment ID: ${newAppointment.id}.`);
					logger.info(`Preparing notification data for patient ID: ${patient.id}.`);
					const patientAppDate = dayjs(newAppointment.appointmentDate).tz(patientTimeZone).format('MMMM D, YYYY');
					const patientAppTime = dayjs(newAppointment.appointmentDate).tz(patientTimeZone).format('h:mm A');
					const patientTimeZoneAbbr = getTimezoneAbbr(patientTimeZone);
					const bookedAt = dayjs(newAppointment.createdAt).tz(patientTimeZone).format('MMMM D, YYYY h:mm A');

					try {
						const patientEmailData = PatientAppointmentNotifyEmail.compile({
							email: patient.email,
							subject: `Appointment booked with ${therapistFullName}`,
							therapistName: therapistFullName,
							patientName: patientFullName,
							appointmentDate: patientAppDate,
							appointmentTime: `${patientAppTime} (${patientTimeZoneAbbr})`,
							duration: formatDuration(newAppointment.duration),
							location: appointmentLocation || '',
							bookedAt,
							title: `This is a confirmation of your appointment booked with ${therapistFullName}:`,
							appointmentType: capitalizeFirstLetter(newAppointment.type),
							header: `Appointment booked with ${therapistFullName}`
						});

						logger.info(`Sending New Appointment Booked Email to ${maskEmail(patient.email)}`);
						await mail.sendMail({
							to: patientEmailData.To,
							subject: patientEmailData.Subject,
							html: patientEmailData.HtmlBody,
						});
						logger.info(`New Appointment Booked Email sent to ${maskEmail(patient.email)}`);
					} catch (error) {
						logger.error(`New Appointment Email - Patient Notify Email error - Patient ID: (${patient.id}) - Appointment ID: (${newAppointment.id}): ${error}.`);
					}
				} catch (error) {
					logger.error(`New Appointment - Patient Notify error - Patient ID: (${patient.id}) - Appointment ID: (${newAppointment.id}): ${error}.`);
				}
			}
		} catch(error) {
			logger.error(`Error creating calendar event or notifying for new booked appointment - therapist ID: ${therapist.id} - Patient ID: ${patient.id} - Appointment ID: ${newAppointment.id}.`, error);
		}
		
		return res.success(
			`Appointment scheduled successfully with therapist ${capitalizeFirstLetter(`${therapist?.firstname} ${therapist?.lastname}`)}.`,
			{
				appointment: {
					id: newAppointment.id,
					name: newAppointment.name,
					description: newAppointment.description,
					appointmentDate: newAppointment.appointmentDate,
				}
			}
		)
	})
)

export default router
