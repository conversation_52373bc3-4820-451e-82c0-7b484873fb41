import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { User, Appointments } from '@/src/models'
import dayjs from 'dayjs'
import { Op } from 'sequelize'
import APIMiddleware from '@/src/application/middlewares/api.middleware'

const router = express.Router()

/**
 * * Check if the appointment date conflicts with any existing appointments of the patient
 * * @param appointmentDate - The date and time of the appointment to check
 * * @returns { isConflicting: boolean, isWithin24Hours: boolean, conflictingAppointment: Record<string, any> }
 */
router.post(
	'/appointment/check-conflict',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const user = req.user as User;

		const { appointmentDate } = req.body;
		if (!appointmentDate) {
			return res.forbidden('Appointment Date is required');
		}

		const checkingAppDate = dayjs.utc(appointmentDate, 'YYYY-MM-DDTHH:mm:ss.SSS[Z]', true);
		if (!checkingAppDate.isValid()) return res.forbidden('Invalid date format. Please provide a valid ISO string in UTC.');

		const appointments = await Appointments.findAll({
			where: {
				patientId: user.id,
				cancelledAt: null,
				appointmentDate: {
					[Op.gte]: dayjs().subtract(2, 'hour').toDate(),
				},
			},
			attributes: ['id', 'appointmentDate', 'duration'],
			include: [
				{
					model: User,
					as: 'therapist',
					attributes: ['id', 'firstname', 'lastname'],
				}
			],
		});

		let isConflicting = false;
    let isWithin24Hours = false;
		let conflictingAppointment = null;

		for (const app of appointments) {
      const appStart = dayjs(app.appointmentDate);
      const durationMinutes = app.duration ?? 60;
      const appEnd = appStart.add(durationMinutes, 'minute');

			const newAppStart = dayjs(appointmentDate).millisecond(0);
			const newAppEnd = newAppStart.add(60, 'minute');

      const isExactMatch = checkingAppDate.isSame(appStart);
      const isInRange = newAppStart.isBefore(appEnd) && newAppEnd.isAfter(appStart);

      if (isExactMatch || isInRange) {
        isConflicting = true;
        isWithin24Hours = appStart.diff(dayjs(), 'hour') <= 24;
				conflictingAppointment = app;
        break;
      }
    }

    return res.json({
			isConflicting,
			isWithin24Hours,
			conflictingAppointment,
		});
	})
);

export default router
