import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { HttpStatusCode } from 'axios';
import authController from '@/src/application/controllers/api/auth.controller';
import { User as UserModel } from '@/src/models';
import * as Helper from '@/src/application/helpers/encryption.helper';
import logger from '@/src/configs/logger.config';

const app = express();
app.use(express.json());
app.use('/api/auth', authController);

describe('Auth Controller - Refresh Token', function () {
  this.timeout(5000);
  
  let sandbox: sinon.SinonSandbox;
  let decryptedB64Stub: sinon.SinonStub;
  let encryptedB64Stub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let originalFindByPk: any;
  
  const TEST_USER_ID = '213';
  const TEST_ACCESS_TOKEN = 'test-access-token';
  const TEST_REFRESH_TOKEN = 'test-refresh-token';
  const TEST_NEW_ACCESS_TOKEN = 'new-access-token';
  const TEST_NEW_REFRESH_TOKEN = 'new-refresh-token';
  
  // Create a mock user object
  const mockUser = {
    id: TEST_USER_ID,
    generateToken: () => TEST_NEW_ACCESS_TOKEN,
    toJSON: () => ({ id: TEST_USER_ID, email: '<EMAIL>' })
  };
  
  before(function() {
    // Save the original findByPk method before any tests run
    originalFindByPk = UserModel.findByPk;
  });
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Helper stubs
    decryptedB64Stub = sandbox.stub(Helper, 'decryptedB64');
    encryptedB64Stub = sandbox.stub(Helper, 'encryptedB64');
    
    // Logger stubs
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    // Configure stubs with default behavior
    decryptedB64Stub.withArgs(TEST_REFRESH_TOKEN).returns(`${TEST_ACCESS_TOKEN}:${TEST_USER_ID}`);
    encryptedB64Stub.withArgs(`${TEST_NEW_ACCESS_TOKEN}:${TEST_USER_ID}`).returns(TEST_NEW_REFRESH_TOKEN);
    
    // Replace the findByPk method directly
    // This avoids using sinon.stub which is causing the "already wrapped" error
    UserModel.findByPk = async (id: any) => {
      if (id === TEST_USER_ID) {
        return mockUser;
      }
      return null;
    };
  });

  afterEach(function() {
    // Clean up all other stubs
    sandbox.restore();
    
    // Restore the original findByPk method after each test
    UserModel.findByPk = originalFindByPk;
  });

  it('should refresh tokens successfully with valid tokens', async function() {
    // Execute
    const response = await request(app)
      .post('/api/auth/refresh')
      .set('Authorization', `Bearer ${TEST_ACCESS_TOKEN}`)
      .send({ refreshToken: TEST_REFRESH_TOKEN });

    // Assert
    expect(response.status).to.equal(HttpStatusCode.Ok);
    expect(response.body).to.have.property('accessToken', TEST_NEW_ACCESS_TOKEN);
    expect(response.body).to.have.property('refreshToken', TEST_NEW_REFRESH_TOKEN);
    expect(response.body).to.have.property('user');
    
    expect(decryptedB64Stub.calledWith(TEST_REFRESH_TOKEN)).to.be.true;
    expect(encryptedB64Stub.calledWith(`${TEST_NEW_ACCESS_TOKEN}:${TEST_USER_ID}`)).to.be.true;
  });

  it('should return 400 when access token is missing', async function() {
    // Execute - not setting Authorization header
    const response = await request(app)
      .post('/api/auth/refresh')
      .send({ refreshToken: TEST_REFRESH_TOKEN });

    // Assert
    expect(response.status).to.equal(HttpStatusCode.BadRequest);
    expect(response.body).to.have.property('message', 'Access token is required');
  });

  it('should return 400 when refresh token is invalid', async function() {
    // Setup - make decryption throw an error
    decryptedB64Stub.withArgs(TEST_REFRESH_TOKEN).throws(new Error('Decryption failed'));
    
    // Execute
    const response = await request(app)
      .post('/api/auth/refresh')
      .set('Authorization', `Bearer ${TEST_ACCESS_TOKEN}`)
      .send({ refreshToken: TEST_REFRESH_TOKEN });

    // Assert
    expect(response.status).to.equal(HttpStatusCode.BadRequest);
    expect(response.body).to.have.property('message', 'Invalid token');
    
    // Verify error was logged
    expect(loggerErrorStub.called).to.be.true;
  });

  it('should return 400 when tokens do not match', async function() {
    // Setup - make decrypted token not match the access token
    decryptedB64Stub.withArgs(TEST_REFRESH_TOKEN).returns(`different-token:${TEST_USER_ID}`);
    
    // Execute
    const response = await request(app)
      .post('/api/auth/refresh')
      .set('Authorization', `Bearer ${TEST_ACCESS_TOKEN}`)
      .send({ refreshToken: TEST_REFRESH_TOKEN });

    // Assert
    expect(response.status).to.equal(HttpStatusCode.BadRequest);
    expect(response.body).to.have.property('message', 'Invalid token');
  });

  it('should return 400 when user is not found', async function() {
    // Setup - temporarily replace the findByPk implementation to return null
    UserModel.findByPk = async () => null;
    
    // Execute
    const response = await request(app)
      .post('/api/auth/refresh')
      .set('Authorization', `Bearer ${TEST_ACCESS_TOKEN}`)
      .send({ refreshToken: TEST_REFRESH_TOKEN });

    // Assert
    expect(response.status).to.equal(HttpStatusCode.BadRequest);
    expect(response.body).to.have.property('message', 'Invalid token');
  });

  it('should return 400 when refresh token is missing', async function() {
    // Execute - not sending refreshToken in body
    const response = await request(app)
      .post('/api/auth/refresh')
      .set('Authorization', `Bearer ${TEST_ACCESS_TOKEN}`)
      .send({});

    // Assert
    expect(response.status).to.equal(HttpStatusCode.BadRequest);
    expect(response.body).to.have.property('message', 'Invalid token');
    
    // Verify error was logged
    expect(loggerErrorStub.called).to.be.true;
  });
});
