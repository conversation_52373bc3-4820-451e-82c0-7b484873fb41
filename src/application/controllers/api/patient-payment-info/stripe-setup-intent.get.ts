import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import stripe from '@/src/configs/stripe-new.config'
import logger from '@/src/configs/logger.config'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { UserType } from '@/src/application/helpers/constant.helper'

const router = express.Router()

/********************************
 * * Create a Stripe Setup Intent and save the Stripe Customer ID
 ********************************/
router.get(
	'/stripe/setup-intent',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) return res.forbidden('Unauthorized request. User not found');

		logger.info(`User ID: ${user.id} - is trying to create stripe setup intent...`)
		if (user.role !== UserType.PATIENT) {
			logger.error(`User ID: ${user.id} - Unauthorized request. User is not a patient`);
			return res.forbidden('Unauthorized request. User is not a patient');	
		}

		let stripeCustomer;
		if (user?.stripeCustomerId) {
			const existingStripeCustomer = await stripe.customers.retrieve(user.stripeCustomerId);
			if (!existingStripeCustomer.deleted) stripeCustomer = existingStripeCustomer;
		}

		if (!stripeCustomer) {
			stripeCustomer = await stripe.customers.create({
				email: user.email,
				metadata: { userId: user.id }
			});
		}

		await user.update({
			stripeCustomerId: stripeCustomer.id,
		});

		logger.info(`User ID: ${user.id} - Creating Stripe setup intent ...`)

		const setupIntent = await stripe.setupIntents.create({
			customer: stripeCustomer.id,
			usage: 'off_session',
			metadata: {
				email: user.email,
				userId: user.id,
			},
		});

		logger.info(`User ID: ${user.id} - Stripe setup intent created successfully`);

		return res.send({
			setupIntentId: setupIntent.id,
			clientSecret: setupIntent.client_secret,
		});
	})
)

export default router
