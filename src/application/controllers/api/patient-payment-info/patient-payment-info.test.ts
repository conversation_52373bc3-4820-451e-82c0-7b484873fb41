import dotenv from 'dotenv';
dotenv.config();

import sinon from 'sinon';
import express from 'express';
import { PatientCard, User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import stripeSetupIntentRoutes from '@/src/application/controllers/api/patient-payment-info';
import cardDeleteRoutes from './card-delete';
import APIMiddleware from '@/src/application/middlewares/api.middleware';
import stripe from '@/src/configs/stripe-new.config';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1', stripeSetupIntentRoutes);
app.use('/api/v1', cardDeleteRoutes);

describe('Stripe Setup Intent API Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let patientCardCreateStub: sinon.SinonStub;
	let patientCardFindAllStub: sinon.SinonStub;
	let patientCardFindOneStub: sinon.SinonStub;
	let patientCardDestroyStub: sinon.SinonStub;
	let appointmentsFindOneStub: sinon.SinonStub;
	let apiMiddlewareStub: sinon.SinonStub;

	let stripeSetupIntentsCreateStub: sinon.SinonStub;
	let stripeCustomersRetrieveStub: sinon.SinonStub;
	let stripeCustomersCreateStub: sinon.SinonStub;
	let stripeSetupIntentsRetrieveStub: sinon.SinonStub;
	let stripePaymentMethodsUpdateStub: sinon.SinonStub;
	let stripePaymentMethodsDetachStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyUserWithTimezone: any;
	let dummyCustomerSetupIntent: any;
	let dummyStripeCustomer: any;
	let dummyPatientCard: any;
	let dummyAppointment: any;
	let mockSequelize: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		patientCardCreateStub = sandbox.stub(PatientCard, 'create');
		patientCardFindAllStub = sandbox.stub(PatientCard, 'findAll');
		patientCardFindOneStub = sandbox.stub(PatientCard, 'findOne');
		patientCardDestroyStub = sandbox.stub(PatientCard, 'destroy');

		stripeSetupIntentsCreateStub = sandbox.stub(stripe.setupIntents, 'create');
		stripeCustomersRetrieveStub = sandbox.stub(stripe.customers, 'retrieve');
		stripeCustomersCreateStub = sandbox.stub(stripe.customers, 'create');
		stripeSetupIntentsRetrieveStub = sandbox.stub(stripe.setupIntents, 'retrieve');
		stripePaymentMethodsUpdateStub = sandbox.stub(stripe.paymentMethods, 'update');
		stripePaymentMethodsDetachStub = sandbox.stub(stripe.paymentMethods, 'detach').resolves();

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		// Mock Sequelize models
		mockSequelize = {
			models: {
				User: {
					findByPk: userFindByPkStub
				},
				appointments: {
					findOne: sandbox.stub()
				}
			}
		};
		appointmentsFindOneStub = mockSequelize.models.appointments.findOne;

		// Stub PatientCard.sequelize
		sandbox.stub(PatientCard, 'sequelize').value(mockSequelize);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
		}

		dummyUserWithTimezone = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
			timeZone: 'America/New_York',
			get: (field: string) => {
				if (field === 'timeZone') return 'America/New_York';
				return undefined;
			}
		}

		dummyCustomerSetupIntent = {
			id: 'seti_123456',
			client_secret: 'secret_12456',
			status: 'succeeded',
			customer: 'cus_123456',
			metadata: {
				userId: '1',
			},
			payment_method: {
				id: 'pm_123456',
				card: {
					last4: '4242',
					exp_month: 12,
					exp_year: 2025,
					brand: 'Visa',
					fingerprint: 'fingerprint_123456',
				}
			}
		}

		dummyStripeCustomer = {
			id: 'cus_123456',
		}

		dummyPatientCard = {
			id: 1,
			patientId: 1,
			stripeSetupIntentId: 'seti_123456',
			stripePaymentMethodId: 'pm_123456',
			cardBrand: 'Visa',
			cardLastFourDigits: '4242',
			cardExpMonth: 12,
			cardExpYear: 2025,
		};

		dummyAppointment = {
			id: 1,
			patientId: 1,
			patientCardId: 1,
			appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
			get: (field: string) => {
				if (field === 'appointmentDate') return new Date(Date.now() + 24 * 60 * 60 * 1000);
				if (field === 'patientCardId') return 1;
				if (field === 'patientId') return 1;
				return undefined;
			}
		};

		// Set up default user authentication for APIMiddleware
		userFindByPkStub.resolves(dummyUserWithTimezone);
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/v1/stripe/setup-intent', function() {

it('should update existing card expiry date when same card with different expiry is added', async function () {
	const existingCardWithDifferentExpiry = {
		...dummyPatientCard,
		cardExpMonth: 10, // Different from payment method (12)
		cardExpYear: 2024, // Different from payment method (2025)
		cardFingerprint: 'fingerprint_123456', // Same fingerprint
		save: sandbox.stub().resolves(), // Mock save method
	};

	userFindByPkStub.resolves({
		...dummyUser,
		update: async () => Promise.resolve(),
	});
	stripeSetupIntentsRetrieveStub.resolves(dummyCustomerSetupIntent);
	stripeCustomersRetrieveStub.resolves(dummyStripeCustomer);
	patientCardFindOneStub.resolves(existingCardWithDifferentExpiry);
	stripePaymentMethodsUpdateStub.resolves();
	
	// Reset the detach stub for this test case
	stripePaymentMethodsDetachStub.reset();
	stripePaymentMethodsDetachStub.resolves();

	const response = await request(app)
		.post('/api/v1/save-payment-info')
		.set('Authorization', 'Bearer accessToken')
		.send({
			setupIntentId: 'seti_123456',
		});

	// Verify the card was updated with new expiry dates
	expect(existingCardWithDifferentExpiry.cardExpMonth).to.equal(12);
	expect(existingCardWithDifferentExpiry.cardExpYear).to.equal(2025);
	expect(existingCardWithDifferentExpiry.stripeSetupIntentId).to.equal('seti_123456');
	expect(existingCardWithDifferentExpiry.stripePaymentMethodId).to.equal('pm_123456');
	
	// Verify save method was called
	expect(existingCardWithDifferentExpiry.save.calledOnce).to.be.true;
	
	// Verify payment method was detached
	expect(stripePaymentMethodsDetachStub.calledOnce).to.be.true;
	expect(stripePaymentMethodsDetachStub.calledWith('pm_123456')).to.be.true;
	
	// Verify no new card was created
	expect(patientCardCreateStub.notCalled).to.be.true;
	
	expect(response.status).to.equal(HttpStatusCode.Ok);
	expect(response.body).to.have.property('message', 'Payment info updated successfully');
});


it('should handle error when detaching payment method after updating expiry', async function () {
	const existingCardWithDifferentExpiry = {
		...dummyPatientCard,
		cardExpMonth: 10, // Different from payment method (12)
		cardExpYear: 2024, // Different from payment method (2025)
		cardFingerprint: 'fingerprint_123456', // Same fingerprint
		save: sandbox.stub().resolves(), // Mock save method to succeed
	};

	userFindByPkStub.resolves({
		...dummyUser,
		update: async () => Promise.resolve(),
	});

	stripeSetupIntentsRetrieveStub.resolves(dummyCustomerSetupIntent);
	stripeCustomersRetrieveStub.resolves(dummyStripeCustomer);
	patientCardFindOneStub.resolves(existingCardWithDifferentExpiry);
	stripePaymentMethodsUpdateStub.resolves();
	
	// Reset the detach stub for this test case and make it fail
	stripePaymentMethodsDetachStub.reset();
	stripePaymentMethodsDetachStub.rejects(new Error('Stripe detach error'));

	const response = await request(app)
		.post('/api/v1/save-payment-info')
		.set('Authorization', 'Bearer accessToken')
		.send({
			setupIntentId: 'seti_123456',
		});

	// Verify the card was updated successfully
	expect(existingCardWithDifferentExpiry.save.calledOnce).to.be.true;
	
	// Verify detach was attempted but failed
	expect(stripePaymentMethodsDetachStub.calledOnce).to.be.true;
	
	// Should still return success even if detach fails
	expect(response.status).to.equal(HttpStatusCode.Ok);
	expect(response.body).to.have.property('message', 'Payment info updated successfully');
});
		it('should fail if user is not patient', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
			});

			const response = await request(app)
				.get('/api/v1/stripe/setup-intent')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Unauthorized request. User is not a patient');
		});

		it('should create setup intent successfully without creating new stripe customer', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				stripeCustomerId: 'cus_123456',
				update: async () => Promise.resolve(),
			});
			stripeCustomersRetrieveStub.resolves(dummyStripeCustomer);
			stripeSetupIntentsCreateStub.resolves(dummyCustomerSetupIntent);

			const response = await request(app)
				.get('/api/v1/stripe/setup-intent')
				.set('Authorization', 'Bearer accessToken')
				.send();
			
			expect(stripeCustomersRetrieveStub.calledOnce).to.be.true;
			expect(stripeSetupIntentsCreateStub.calledOnce).to.be.true;
			expect(stripeCustomersCreateStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('setupIntentId', 'seti_123456');
			expect(response.body).to.have.property('clientSecret', 'secret_12456');
		});

		it('should create setup intent successfully with creating new stripe customer', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				stripeCustomerId: null,
				update: async () => Promise.resolve(),
			});
			stripeSetupIntentsCreateStub.resolves(dummyCustomerSetupIntent);
			stripeCustomersCreateStub.resolves(dummyStripeCustomer);

			const response = await request(app)
				.get('/api/v1/stripe/setup-intent')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(stripeCustomersRetrieveStub.notCalled).to.be.true;
			expect(stripeCustomersCreateStub.calledOnce).to.be.true;
			expect(stripeSetupIntentsCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('setupIntentId', 'seti_123456');
			expect(response.body).to.have.property('clientSecret', 'secret_12456');
		});

		it('should create setup intent successfully with creating new stripe customer when stripe customer id deleted', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				stripeCustomerId: 'cus_123456',
				update: async () => Promise.resolve(),
			});
			stripeSetupIntentsCreateStub.resolves(dummyCustomerSetupIntent);
			stripeCustomersRetrieveStub.resolves({
				...dummyStripeCustomer,
				deleted: true,
			});
			stripeCustomersCreateStub.resolves(dummyStripeCustomer);

			const response = await request(app)
				.get('/api/v1/stripe/setup-intent')
				.set('Authorization', 'Bearer accessToken')
				.send();
			
			expect(stripeCustomersRetrieveStub.calledOnce).to.be.true;
			expect(stripeSetupIntentsCreateStub.calledOnce).to.be.true;
			expect(stripeSetupIntentsCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('setupIntentId', 'seti_123456');
			expect(response.body).to.have.property('clientSecret', 'secret_12456');
		});
	});

	describe('POST /api/v1/save-payment-info', function() {
		it('should fail if user is not patient', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
			});

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Unauthorized request. User is not a patient');
		});

		it('should fail if setupIntentId is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Setup Intent ID is required');
		});

		it('should fail if setupIntent with provided setupIntentId is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves(null);

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Setup intent not found');
		});

		it('should fail if setupIntent is not succeeded', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves({
				...dummyCustomerSetupIntent,
				status: 'requires_action',
			});

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Setup intent is not completed successfully.');
		});

		it('should fail if setupIntent does not have a payment_method', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves({
				...dummyCustomerSetupIntent,
				payment_method: null,
			});

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid setup intent. No payment method associated.');
		});

		it('should fail if setupIntent does not have a customer', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves({
				...dummyCustomerSetupIntent,
				customer: null,
			});

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid setup intent. No customer associated.');
		});

		it('should fail if metadata userId does not match request user id', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				id: 2,
			});
			stripeSetupIntentsRetrieveStub.resolves(dummyCustomerSetupIntent);

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid setup intent. User ID does not match.');
		});

		it('should fail if stripe customer is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves(dummyCustomerSetupIntent);
			stripeCustomersRetrieveStub.resolves(null);

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid setup intent. Customer not found');
		});

		it('should fail if card fingerprint is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves({
				...dummyCustomerSetupIntent,
				payment_method: {
					...dummyCustomerSetupIntent.payment_method,
					card: {
						...dummyCustomerSetupIntent.payment_method.card,
						fingerprint: null,
					}
				}
			});
			stripeCustomersRetrieveStub.resolves(dummyStripeCustomer);

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid payment method. Card fingerprint not found');
		});

		it('should fail if card has already been added', async function () {
			userFindByPkStub.resolves(dummyUser);
			stripeSetupIntentsRetrieveStub.resolves(dummyCustomerSetupIntent);
			stripeCustomersRetrieveStub.resolves(dummyStripeCustomer);
			patientCardFindOneStub.resolves(dummyPatientCard);

			const response = await request(app)
				.post('/api/v1/save-payment-info')
				.set('Authorization', 'Bearer accessToken')
				.send({
					setupIntentId: 'seti_123456',
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Same card already exists. Please use a different card.');
		});

	it('should save payment info successfully', async function () {
	userFindByPkStub.resolves({
		...dummyUser,
		update: async () => Promise.resolve(),
	});
	stripeSetupIntentsRetrieveStub.resolves(dummyCustomerSetupIntent);
	stripeCustomersRetrieveStub.resolves(dummyStripeCustomer);
	patientCardFindOneStub.resolves(null); // No existing card found
	stripePaymentMethodsUpdateStub.resolves();
	
	// Mock the required Stripe methods for the new card creation flow
	const stripePaymentMethodsAttachStub = sandbox.stub(stripe.paymentMethods, 'attach').resolves();
	const stripeCustomersUpdateStub = sandbox.stub(stripe.customers, 'update').resolves();

	const response = await request(app)
		.post('/api/v1/save-payment-info')
		.set('Authorization', 'Bearer accessToken')
		.send({
			setupIntentId: 'seti_123456',
		});
	
	expect(patientCardCreateStub.calledOnce).to.be.true;
	expect(stripePaymentMethodsUpdateStub.calledOnce).to.be.true;
	expect(stripePaymentMethodsAttachStub.calledOnce).to.be.true;
	expect(stripeCustomersUpdateStub.calledOnce).to.be.true;
	expect(response.status).to.equal(HttpStatusCode.Ok);
	expect(response.body).to.have.property('message', 'Payment info saved successfully');
});
	});

	describe('GET /api/v1/card-list', function() {
		it('should return empty array if user does not have saved cards', async function () {
			userFindByPkStub.resolves(dummyUser);
			patientCardFindAllStub.resolves(null);

			const response = await request(app)
				.get('/api/v1/card-list')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('array').that.is.empty;
		});

		it('should return array of available patient cards', async function () {
			userFindByPkStub.resolves(dummyUser);
			patientCardFindAllStub.resolves([dummyPatientCard, dummyPatientCard]);

			const response = await request(app)
				.get('/api/v1/card-list')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('array').that.is.of.length(2);
		});
	});
// Updated test cases to fix the failing tests

describe('DELETE /api/v1/card', function() {
	
	it('should fail if patientCardId is not provided', async function () {
		// Use existing sandbox setup
		userFindByPkStub.resolves(dummyUserWithTimezone);

		const response = await request(app)
			.delete('/api/v1/card')
			.set('Authorization', 'Bearer accessToken')
			.send(); // No patientCardId provided

		expect(response.status).to.equal(400);
		expect(response.body).to.have.property('message', 'patientCardId is required');
	});

	it('should fail if appointments model is not found', async function () {
		// Mock sequelize without appointments model
		const ******************************** = {
			models: {
				User: {
					findByPk: userFindByPkStub
				}
				// No appointments model
			}
		};
		
		// Temporarily stub PatientCard.sequelize
		const originalSequelize = PatientCard.sequelize;
		sandbox.stub(PatientCard, 'sequelize').value(********************************);

		userFindByPkStub.resolves(dummyUserWithTimezone);

		const response = await request(app)
			.delete('/api/v1/card')
			.set('Authorization', 'Bearer accessToken')
			.send({
				patientCardId: 1
			});

		expect(response.status).to.equal(500);
		expect(response.body).to.have.property('message', 'Appointments model not found.');
	});

	it('should fail if card is linked to a future appointment', async function () {
		userFindByPkStub.resolves(dummyUserWithTimezone);
		appointmentsFindOneStub.resolves(dummyAppointment);

		const response = await request(app)
			.delete('/api/v1/card')
			.set('Authorization', 'Bearer accessToken')
			.send({
				patientCardId: 1
			});

		expect(response.status).to.equal(403);
		expect(response.body).to.have.property('message', 'This card is linked to a future appointment and cannot be deleted.');
	});

	it('should fail if patient card is not found or already deleted', async function () {
		userFindByPkStub.resolves(dummyUserWithTimezone);
		appointmentsFindOneStub.resolves(null);
		patientCardDestroyStub.resolves(0); // No rows affected

		const response = await request(app)
			.delete('/api/v1/card')
			.set('Authorization', 'Bearer accessToken')
			.send({
				patientCardId: 1
			});

		expect(response.status).to.equal(404);
		expect(response.body).to.have.property('message', 'Patient card not found or already deleted.');
	});

	it('should successfully delete patient card', async function () {
	userFindByPkStub.resolves(dummyUserWithTimezone);
	appointmentsFindOneStub.resolves(null);
	// Stub for finding the card before deletion
	patientCardFindOneStub.resolves({
		...dummyPatientCard,
		get: (field: string) => dummyPatientCard[field]
	});
	patientCardDestroyStub.resolves(1); // One row affected

	const response = await request(app)
		.delete('/api/v1/card')
		.set('Authorization', 'Bearer accessToken')
		.send({
			patientCardId: 1
		});

	expect(response.status).to.equal(200);
	expect(response.body).to.have.property('message', 'Patient card deleted successfully.');
	expect(patientCardFindOneStub.calledOnce).to.be.true;
	expect(patientCardDestroyStub.calledOnce).to.be.true;
	expect(patientCardDestroyStub.calledWith({
		where: {
			id: 1,
			patientId: 1
		}
	})).to.be.true;
});

it('should verify appointment query uses correct timezone and date comparison', async function () {
	userFindByPkStub.resolves(dummyUserWithTimezone);
	appointmentsFindOneStub.resolves(null);
	// Stub for finding the card before deletion
	patientCardFindOneStub.resolves({
		...dummyPatientCard,
		get: (field: string) => dummyPatientCard[field]
	});
	patientCardDestroyStub.resolves(1);

	const response = await request(app)
		.delete('/api/v1/card')
		.set('Authorization', 'Bearer accessToken')
		.send({
			patientCardId: 1
		});

	expect(response.status).to.equal(200);
	expect(appointmentsFindOneStub.calledOnce).to.be.true;
	
	const appointmentQuery = appointmentsFindOneStub.getCall(0).args[0];
	expect(appointmentQuery.where.patientCardId).to.equal(1);
	expect(appointmentQuery.where.patientId).to.equal(1);
	// Check for the Op.gt structure instead of $gt
	expect(appointmentQuery.where.appointmentDate).to.have.property(Symbol.for('gt'));
});

it('should handle past appointments correctly (should allow deletion)', async function () {
	const pastAppointment = {
		...dummyAppointment,
		appointmentDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
		get: (field: string) => {
			if (field === 'appointmentDate') return new Date(Date.now() - 24 * 60 * 60 * 1000);
			if (field === 'patientCardId') return 1;
			if (field === 'patientId') return 1;
			return undefined;
		}
	};

	userFindByPkStub.resolves(dummyUserWithTimezone);
	appointmentsFindOneStub.resolves(null); // No future appointments found
	// Stub for finding the card before deletion
	patientCardFindOneStub.resolves({
		...dummyPatientCard,
		get: (field: string) => dummyPatientCard[field]
	});
	patientCardDestroyStub.resolves(1);

	const response = await request(app)
		.delete('/api/v1/card')
		.set('Authorization', 'Bearer accessToken')
		.send({
			patientCardId: 1
		});

	expect(response.status).to.equal(200);
	expect(response.body).to.have.property('message', 'Patient card deleted successfully.');
});

it('should handle different timezone scenarios', async function () {
	const userWithDifferentTimezone = {
		...dummyUserWithTimezone,
		timeZone: 'Asia/Tokyo',
		get: (field: string) => {
			if (field === 'timeZone') return 'Asia/Tokyo';
			return dummyUserWithTimezone.get ? dummyUserWithTimezone.get(field) : undefined;
		}
	};

	userFindByPkStub.resolves(userWithDifferentTimezone);
	appointmentsFindOneStub.resolves(null);
	// Stub for finding the card before deletion
	patientCardFindOneStub.resolves({
		...dummyPatientCard,
		get: (field: string) => dummyPatientCard[field]
	});
	patientCardDestroyStub.resolves(1);

	const response = await request(app)
		.delete('/api/v1/card')
		.set('Authorization', 'Bearer accessToken')
		.send({
			patientCardId: 1
		});

	expect(response.status).to.equal(200);
	expect(response.body).to.have.property('message', 'Patient card deleted successfully.');
});

it('should ensure card belongs to the authenticated user', async function () {
	userFindByPkStub.resolves(dummyUserWithTimezone);
	appointmentsFindOneStub.resolves(null);
	// Stub for finding the card before deletion
	patientCardFindOneStub.resolves({
		...dummyPatientCard,
		get: (field: string) => dummyPatientCard[field]
	});
	patientCardDestroyStub.resolves(1);

	const response = await request(app)
		.delete('/api/v1/card')
		.set('Authorization', 'Bearer accessToken')
		.send({
			patientCardId: 1
		});

	expect(response.status).to.equal(200);
	expect(patientCardFindOneStub.calledOnce).to.be.true;
	
	// Verify that the findOne query includes both id and patientId
	const findOneQuery = patientCardFindOneStub.getCall(0).args[0];
	expect(findOneQuery.where.id).to.equal(1);
	expect(findOneQuery.where.patientId).to.equal(1);
});
});
});