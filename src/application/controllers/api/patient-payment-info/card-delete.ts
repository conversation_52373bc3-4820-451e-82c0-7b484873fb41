import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import logger from '@/src/configs/logger.config'
import stripe from '@/src/configs/stripe-new.config'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { PatientCard, Appointments } from '@/src/models'
import { Op } from 'sequelize';

const router = express.Router()

/********************************
 * * Get list of saved cards for the patient
 ********************************/
router.delete(
    '/card',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const { user } = req;
        if (!user) return res.forbidden('Unauthorized request. User not found');
        const { patientCardId } = req.body;
        if (!patientCardId) return res.status(400).json({ message: 'patientCardId is required' });

        // Get user's timezone
const dbUser = await PatientCard.sequelize?.models.User.findByPk(user.id, { attributes: ['timeZone'] });
const timezone = dbUser?.get('timeZone');
if (!timezone) {
    return res.status(404).json({ message: 'User timezone not found' });
}

        // Get current time in user's timezone
        const dayjs = require('dayjs');
        const utc = require('dayjs/plugin/utc');
        const tz = require('dayjs/plugin/timezone');
        dayjs.extend(utc);
        dayjs.extend(tz);
        const now = dayjs().tz(timezone);

        // Check for future appointments with this card
        const Appointments = PatientCard.sequelize?.models.appointments;
        if (!Appointments) {
            return res.status(500).json({ message: 'Appointments model not found.' });
        }
        const futureAppointment = await Appointments.findOne({
            where: {
                patientCardId,
                patientId: user.id,
                appointmentDate: { [Op.gt]: now.toDate() }
            },
        });
        if (futureAppointment) {
            return res.status(403).json({ message: 'This card is linked to a future appointment and cannot be deleted.' });
        }

        // First get the card to be deleted
        const cardToDelete = await PatientCard.findOne({
            where: {
                id: patientCardId,
                patientId: user.id,
            },
        });

        if (!cardToDelete) {
            return res.status(404).json({ message: 'Patient card not found or already deleted.' });
        }

        const stripePaymentMethodId = cardToDelete.get('stripePaymentMethodId');

        // Delete the card from the database
        const deleted = await PatientCard.destroy({
            where: {
                id: patientCardId,
                patientId: user.id,
            },
        });

        if (!deleted) {
            logger.error(`User ID: ${user.id} - Failed to delete patient card ID: ${patientCardId} from database`);
            return res.status(500).json({ message: 'Failed to delete patient card.' });
        }

        if (stripePaymentMethodId) {
            try {
                await stripe.paymentMethods.detach(stripePaymentMethodId);
                logger.info(`User ID: ${user.id} - Successfully deleted payment method ${stripePaymentMethodId} from Stripe`);
            } catch (error) {
                logger.error(`User ID: ${user.id} - Error deleting payment method ${stripePaymentMethodId} from Stripe: ${error}`);
            }
        }

        logger.info(`User ID: ${user.id} - Successfully deleted patient card ID: ${patientCardId}`);
        return res.json({ message: 'Patient card deleted successfully.' });
    })
)

export default router;

