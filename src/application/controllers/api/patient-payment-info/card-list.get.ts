import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import logger from '@/src/configs/logger.config'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { PatientCard } from '@/src/models'

const router = express.Router()

/********************************
 * * Get list of saved cards for the patient
 ********************************/
router.get(
	'/card-list',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) return res.forbidden('Unauthorized request. User not found');

		logger.info(`User ID: ${user.id} - Retrieving saved cards...`);

		const savedCards = await PatientCard.findAll({
			where: {
				patientId: user.id,
			},
		});

		if (!savedCards || savedCards.length === 0) {
			logger.info(`User ID: ${user.id} - No saved cards found`);
			return res.send([]);
		}

		logger.info(`User ID: ${user.id} - Retrieved saved cards successfully`);
		return res.send(savedCards);
	})
)

export default router;

