import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import stripe from '@/src/configs/stripe-new.config'
import logger from '@/src/configs/logger.config'
import APIMiddleware from '@/src/application/middlewares/api.middleware'
import { UserType } from '@/src/application/helpers/constant.helper'
import <PERSON><PERSON> from 'stripe'
import { PatientCard } from '@/src/models'
import { UPDATED_PAYMENT_INFO } from '@/src/application/controllers/api/constants'

const router = express.Router()

/********************************
 * * Validate the stripe setup intent and save the stripe intent id
 ********************************/
router.post(
	'/save-payment-info',
	APIMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { user } = req;
		if (!user) return res.forbidden('Unauthorized request. User not found');

		logger.info(`User ID: ${user.id} - Saving payment info...`)
		if (user.role !== UserType.PATIENT) {
			logger.error(`User ID: ${user.id} - Unauthorized request. User is not a patient`);
			return res.forbidden('Unauthorized request. User is not a patient');	
		}

		const { setupIntentId } = req.body;
		if (!setupIntentId) {
			logger.error(`User ID: ${user.id} - Setup Intent ID is required`);
			return res.forbidden('Setup Intent ID is required');
		}

		const setupIntent = await stripe.setupIntents.retrieve(
			setupIntentId,
			{
				expand: ['payment_method'],
			}
		);

		if (!setupIntent) {
			logger.error(`User ID: ${user.id} - Setup intent not found`);
			return res.forbidden('Setup intent not found');
		}
		
		if (setupIntent.status !== 'succeeded') {
			logger.error(`User ID: ${user.id} - Setup intent is not succeeded`);
			return res.forbidden('Setup intent is not completed successfully.');
		}

		if (!setupIntent.payment_method) {
			logger.error(`User ID: ${user.id} - Setup intent does not have a payment method`);
			return res.forbidden('Invalid setup intent. No payment method associated.');
		}

		if (!setupIntent.customer) {
			logger.error(`User ID: ${user.id} - Setup intent does not have a customer`);
			return res.forbidden('Invalid setup intent. No customer associated.');
		}

		if (!setupIntent.metadata?.userId || (Number(setupIntent.metadata.userId) !== Number(user.id))) {
			logger.error(`User ID: ${user.id} - Setup intent user ID does not match authenticated user`);
			return res.forbidden('Invalid setup intent. User ID does not match.');
		}

		const customer = await stripe.customers.retrieve(setupIntent.customer as string);
		if (!customer) {
			logger.error(`User ID: ${user.id} - Customer not found`);
			return res.forbidden('Invalid setup intent. Customer not found');
		}

		const paymentMethod = setupIntent.payment_method as Stripe.PaymentMethod;

		if (!paymentMethod.card?.fingerprint) {
			logger.error(`User ID: ${user.id} - Payment method does not have a card fingerprint`);
			return res.forbidden('Invalid payment method. Card fingerprint not found');
		}

		const existingCard = await PatientCard.findOne({
			where: {
				patientId: user.id,
				cardFingerprint: paymentMethod.card.fingerprint,
			},
		});
		if (existingCard) {
			// Check if the expiry date is different
			const isExpiryDifferent = existingCard.cardExpMonth !== paymentMethod.card.exp_month || 
								existingCard.cardExpYear !== paymentMethod.card.exp_year;
			if (isExpiryDifferent) {
				// Update the existing card's expiry date in both database and Stripe
				try {
					// First update the card details in Stripe
					await stripe.paymentMethods.update(existingCard.stripePaymentMethodId, {
						card: {
							exp_month: paymentMethod.card.exp_month,
							exp_year: paymentMethod.card.exp_year,
						},
					});

					// Then update our database
					existingCard.cardExpMonth = paymentMethod.card.exp_month;
					existingCard.cardExpYear = paymentMethod.card.exp_year;
					existingCard.stripeSetupIntentId = setupIntent.id;

					await existingCard.save();
					logger.info(`User ID: ${user.id} - Updated existing card's expiry date in both database and Stripe`);

					// Detach the new payment method since we're using the existing one
					try {
						await stripe.paymentMethods.detach(paymentMethod.id);
						logger.info(`User ID: ${user.id} - Temporary payment method detached after updating expiry`);
					} catch (error) {
						logger.error(`User ID: ${user.id} - Error detaching temporary payment method: ${error}`);
					}

					return res.success(UPDATED_PAYMENT_INFO);
				} catch (error) {
					logger.error(`User ID: ${user.id} - Error updating card expiry: ${error}`);
				}
			} else {
				logger.info(`User ID: ${user.id} - Same card already exists for this user`);
				
				try {
					await stripe.paymentMethods.detach(paymentMethod.id);
					logger.info(`User ID: ${user.id} - Payment method detached from user`);
				} catch (error) {
					logger.error(`User ID: ${user.id} - Error detaching payment method: ${error}`);
				}
				return res.forbidden('Same card already exists. Please use a different card.');
			}
		}

		try {
			await stripe.paymentMethods.update(paymentMethod.id, {
				metadata: {
					userId: user.id,
					email: user.email,
				},
			});
			logger.info(`User ID: ${user.id} - Payment method updated with user metadata`);
		} catch (error) {
			logger.error(`User ID: ${user.id} - Error updating payment method metadata: ${error}`);
		}

		try {
			await stripe.paymentMethods.attach(paymentMethod.id, {
				customer: customer.id,
			});

			await stripe.customers.update(customer.id, {
				invoice_settings: {
					default_payment_method: paymentMethod.id,
				},
			});
			logger.info(`User ID: ${user.id} - Payment method attached to customer and set as default`);
		} catch (error) {
			logger.error(`User ID: ${user.id} - Error attaching payment method to customer: ${error}`);
			return res.status(500).json({ success: false, message: 'Failed to save payment method. Please try again.' });
		}

		await user.update({
			stripeCustomerId: customer.id,
		});

		await PatientCard.create({
			patientId: user.id,
			stripeSetupIntentId: setupIntent.id,
			stripePaymentMethodId: paymentMethod.id,
			cardLastFourDigits: paymentMethod.card?.last4 || null,
			cardBrand: paymentMethod.card?.brand || null,
			cardExpMonth: paymentMethod.card?.exp_month || null,
			cardExpYear: paymentMethod.card?.exp_year || null,
			cardFingerprint: paymentMethod.card?.fingerprint || null,
		});
		
		logger.info(`User ID: ${user.id} - Payment info saved successfully`);

		return res.success('Payment info saved successfully');
	})
)

export default router
