export const conditions = ["bipolar", "paranoia", "depression", "anxiety", "adjustment", "domestic", "relationship","substance","eating","lgbtq","gender","trauma","nssid","chronic","obsessive","neurodivergent","schizophrenia","personality"];
export const modalityMappings = {
    acceptance: "acceptance-and-commitment-therapy",
    applied: "applied-behavioral-analysis",
    art: "art-therapy",
    attachment: "attachment-based-therapy",
    animal: "animal-assisted-therapy",
    biofeedback: "biofeedback",
    cognitiveBehavioral: "cognitive-behavioral-therapy",
    cognitiveProcessing: "cognitive-processing-therapy",
    cultural: "culturally-sensitive-therapy",
    dialectical: "dialectical-behavioral-therapy",
    existential: "existential-therapy",
    exposureResponse: "exposure-and-response-prevention",
    feminist: "feminist-therapy",
    gestalt: "gestalt-therapy",
    humanistic: "gottman-method-for-healthy-relationships-humanistic-therapy",
    internalFamily: "internal-family-systems-therapy",
    interpersonal: "interpersonal-psychotherapy",
    jungian: "jungian", /// 
    mindfulness: "mindfulness-based-cognitive-therapy",
    motivationalInterview: "motivational-interviewing",
    multicultural: "multicultural-therapy",
    music: "music-therapy",
    narrative: "narrative-therapy",
    neurofeedback: "neurofeedback",
    personCentered: "person-centered-therapy",
    positivePsycho: "positive-predisposition",
    prolongedExposure: "prolonged-exposure-therapy",
    psychoanalytic: "psychoanalytic-therapy",
    psychodynamic: "psychodynamic-therapy",
    psychologicalTesting: "psychological-testing-and-evaluation",
    rationalEmotive: "rational-emotive-behavior-therapy",
    relational: "relational-therapy",
    schema: "schema-therapy",
    solutionFocused: "solution-focused-brief-therapy",
    somatic: "somatic-therapy",
    strengthBased: "strength-based-therapy",
    traumaFocused: "trauma-focused-cognitive-behavioral-therapy"
};
export const religionMapping = {
    Spiritual: 'spiritual',
    Protestant: 'protestant',
    Evangelical: 'evangelical',
    Hindu: 'hindu',
    Catholic: 'catholic',
    Buddhist: 'buddhist',
    Atheism: 'atheism',
    Christian: 'christian (Non-denominational)',
    ChurchOfJesusChrist: 'Church of Jesus Christ of Latter-day Saints (Mormon)',
    Jewish: 'jewish',
    Muslim: 'muslim',
};
export const raceMappings = {
    Asian: 'asian',
    AmericanIndian: 'american-indian-alaska',
    Black: 'black',
    Caucasian: 'caucasian',
    Latino: 'latino',
    PacificIslander: 'pacific-islander',
    Other: 'other-mixed',
};

export const focusAreaMapping = {
    MenIssues : 'men-issues',
    WomenIssues : 'women-issues',
    FaithCrisis : 'faith-crisis',
    Disability : 'disability',
    Accident  :'accident,injury,illness',
    EatingDisorders: 'eating disorders',
    VeteransIssues:'veterans issues',
    Isolation : 'isolation or loneliness',
    CaregiverIssues : 'care-giver',
    RacialIdentity : 'racial-identity',
    Other : 'other'
};

export const languageMapping = {
    Chinese: 'chinese',
    Arabic: 'arabic',
    ASL : 'asl(American Sign Language)',
    Spanish: 'spanish',
    Tagalog: 'tagalog',
    Vietnamese: 'vietnamese',
    English: 'english',
};

export const validGenders = ['male', 'female', 'other', 'trans-male', 'trans-female', 'non-binary', 'not-say'];
export const INSURANCE = 'insurance';
export const SELF_PAY = 'self_pay';
export const TELEHEALTH = 'telehealth';
export const IN_PERSON = 'in-person';
export const IN_PERSON_TELEHEALTH = 'in-person-and-telehealth';
export const PROFILE = 'profile' ;
export const PAYMENT_FORMS = 'payment-forms';
export const PRACTICE_INFO = 'practice-info';
export const MODALITIES = 'modalities';
export const PRACTICE_FOCUS = 'practice-focus';
export const SPECIALIZATION = 'specialization';
export const NORMAL_OFFICE_HOURS = 'normal-office-hours';
export const LICENSE = 'license';
export const INVALID_OTP_MESSAGE = "Invalid OTP";
export const OTP_VERIFIED_MESSAGE = "OTP verified and phone number updated successfully";
export const MOBILE_NUMBER_REQUIRED_MESSAGE = "Mobile number and OTP are required";
export const MOBILE_NUMBER_REQUIRED = "Mobile number is required";
export const USER_NOT_FOUND = "User not found";
export const OTP_SENT_MESSAGE = "is your verification code to verify your mobile number. Do not share this code with anyone.";
export const OTP_SENT_SUCCESSFULLY = "OTP sent successfully";
export const FAILED_TO_SEND_OTP = "Failed to send OTP";
export const OTP_NOT_FOUND = "OTP not found or expired";
export const INVALID_OTP = "Invalid OTP";
export const FAILED_VERIFY_OTP = "Failed to verify OTP";
export const WAITLIST_NOTIFICATION = "waitlist-notifications";
export const THERAPIST_NOT_AVAILABLE = "This therapist profile is no longer available";
export const DELETION_REASON = "Error fetching deletion reasons";
export const PATIENT_DELETED = "Patient deleted successfully"
export const DELETION_REASON_DELETING = "Error deleting patient"
export const REASON_ID_REQUIRED = "Reason ID is required";
export const TRANSACTION_NOT_STARTED = "Transaction could not be started"
export const PATIENT_NOT_FOUND = "Patient not found";
export const PATIENT_DEACTIVATED = "Patient deactivated successfully";
export const PATIENT_DEACTIVATED_ERROR = "Error deactivating patient";
export const paymentMappings = {
    selfPay: 'self_pay',
    clergyPay : 'clergy_pay',
};
export const insuranceMappings = {
  aetna: 'aetna',
  blueCross: 'blue-cross',
  cigna: 'cigna',
  health: 'health',
  humana: 'humana',
  kaiser: 'kaiser',
  midecaid: 'midecaid',
  medicare: 'medicare',
  tricare: 'tricare',
  selectHealth: 'select-health',
  unitedHealthcare: 'united-healthcare',
  other: 'other',
};
export const UPDATED_PAYMENT_INFO = "Payment info updated successfully";
