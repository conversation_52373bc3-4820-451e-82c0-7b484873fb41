import sinon from 'sinon';
import express from 'express';
import { Appointments, NormalOfficeHours, TherapistDurationAndCost, TherapistWaitlist, User, UserRegistrationInfo } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistControllerRoute from '@/src/application/controllers/api/therapist.controller';
import dayjs from 'dayjs';
import * as therapistRepository from '@/src/application/repositories/therapist.repository';
import * as availabilityHelper from '@/src/application/helpers/availability.helper';

const app = express();

app.use(express.json());
app.use(response);
app.use('/api/v1/therapists', therapistControllerRoute);

describe('Api Therapist Controller Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let userFindAndCountAllStub: sinon.SinonStub;
	let userFindAllStub: sinon.SinonStub;
	let appointmentsFindAllStub: sinon.SinonStub;
	let therapistWaitlistFindAllStub: sinon.SinonStub;
	let therapistDurationCostCreateStub: sinon.SinonStub;
	let therapistDurationCostFindAllStub: sinon.SinonStub;
	let userRegInfoFindOneStub: sinon.SinonStub;
	let normalOfficeHoursFindAllStub: sinon.SinonStub;

	let getTherapistDetailsStub: sinon.SinonStub;
	let getAvailableOfficeHoursStub: sinon.SinonStub;

	let dummyUser: any;
	let dummyAppointment: any;
	let dummyTherapistWaitlist: any;
	let dummyTherapistDurationCost: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		userFindAllStub = sandbox.stub(User, 'findAll');
		appointmentsFindAllStub = sandbox.stub(Appointments, 'findAll');
		therapistWaitlistFindAllStub = sandbox.stub(TherapistWaitlist, 'findAll');
		userFindAndCountAllStub = sandbox.stub(User, 'findAndCountAll');
		therapistDurationCostCreateStub = sandbox.stub(TherapistDurationAndCost, 'create');
		therapistDurationCostFindAllStub = sandbox.stub(TherapistDurationAndCost, 'findAll');
		userRegInfoFindOneStub = sandbox.stub(UserRegistrationInfo, 'findOne');
		normalOfficeHoursFindAllStub = sandbox.stub(NormalOfficeHours, 'findAll');

		getTherapistDetailsStub = sandbox.stub(therapistRepository, 'getTherapistDetails');
		getAvailableOfficeHoursStub = sandbox.stub(availabilityHelper, 'getAvailableOfficeHours');

		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'api',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'patient',
			timeZone: 'America/New_York',
		}

		dummyAppointment = {
			id: 1,
			therapistId: 1,
			appointmentDate: dayjs().subtract(1, 'day').toISOString(),
			createdAt: dayjs().subtract(2, 'day').toISOString(),
			therapist: {
				firstname: 'John',
				lastname: 'Doe',
			}
		}

		dummyTherapistWaitlist = {
			id: 1,
			therapistId: 1,
			createdAt: dayjs().subtract(1, 'day').toISOString(),
			status: 'waiting',
			therapist: {
				firstname: 'John',
				lastname: 'Doe',
			}
		}

		dummyTherapistDurationCost = {
			therapistId: 1,
			duration: 60,
			category: 'individual',
			cost: 100,
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /api/v1/therapists', function() {
		it('should return empty array if no previous interactions available', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			appointmentsFindAllStub.resolves([]);
			therapistWaitlistFindAllStub.resolves([]);

			const response = await request(app)
				.get('/api/v1/therapists')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').that.is.an('array').that.is.empty;
			expect(response.body).to.have.property('meta');
		});

		it('should return count if count is true in req query', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			appointmentsFindAllStub.resolves([dummyAppointment]);
			therapistWaitlistFindAllStub.resolves([dummyTherapistWaitlist]);
			userFindAndCountAllStub.resolves({
				count: 1,
				rows: [dummyUser],
			});

			const response = await request(app)
				.get('/api/v1/therapists')
				.set('Authorization', 'Bearer accessToken')
				.query({
					count: true,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object').to.have.property('count');
		});

		it('should therapist list successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			appointmentsFindAllStub.resolves([dummyAppointment]);
			therapistWaitlistFindAllStub.resolves([dummyTherapistWaitlist]);
			userFindAndCountAllStub.resolves({
				count: 1,
				rows: [{
					...dummyUser,
					dataValues: {
						firstname: 'John',
						lastname: 'Doe',
					},
					registrationInfo: [
						{
							pageName: 'normal-office-hours',
							payloadInfo: {
								appointmentMethod: 'in-person',
							}
						},
						{
							pageName: 'payment-forms',
							payloadInfo: {
								insurance_list: {
									checked_list: ['insurance1', 'insurance2'],
								}
							}
						},
						{
							pageName: 'practice-info',
							payloadInfo: {

							}
						}
					]
				}],
			});

			const response = await request(app)
				.get('/api/v1/therapists')
				.set('Authorization', 'Bearer accessToken')
				.query({
					lat: '40.7128',
					lng: '-74.0060',
					search: 'John Doe',
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').that.is.an('array').that.is.not.empty;
			expect(response.body).to.have.property('meta')
		});
	});

	describe('GET /api/v1/therapists/:id', function() {
		it('should return therapist details successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			getTherapistDetailsStub.resolves(dummyUser);

			const response = await request(app)
				.get('/api/v1/therapists/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object');
		});
	});

	describe('GET /api/v1/therapists/available-hours', function() {
		it('should return empty array if no therapists are found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindAndCountAllStub.resolves({
				count: 0,
				rows: [],
			});

			const response = await request(app)
				.get('/api/v1/therapists/available-hours')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').that.is.an('array').that.is.empty;
			expect(response.body).to.have.property('meta');
		});

		it('should return therapist lists successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindAndCountAllStub.onFirstCall().resolves({
				count: 0,
				rows: [],
			});
			userFindAndCountAllStub.onSecondCall().resolves({
				count: 1,
				rows: [
					{
						id: 1,
						timeZone: 'America/New_York',
						normalOfficeHours: [
							{
								id: 1,
								workingDay: 'Monday',
								availabilityHours: '09:00:00',
								appointmentMethod: 'in-person'
							}
						],
						therapistAppointments: [],
						calendars: [],
						registrationInfo: [
							{
								pageName: 'waitlist-notifications',
								payloadInfo: {
									week_visibility: 3,
								}
							},
							{
								pageName: 'normal-office-hours',
								payloadInfo: {
									appointmentMethod: 'in-person',
								}
							},
						],
						therapistWaitlist: [
							{
								id: 1,
								patientId: 1,
							}
						],
						dataValues: {
							practiceInfo: JSON.stringify({
								practice_name: 'New Practice'
							}),
						}
					}
				],
			});
			getAvailableOfficeHoursStub.resolves([
				{
					id: 3,
					day: 'Monday',
				}
			]);

			const response = await request(app)
				.get('/api/v1/therapists/available-hours')
				.set('Authorization', 'Bearer accessToken')
				.query({
					searchId: 1,
					lat: '40.7128',
					lng: '-74.0060',
					religion: 'Hindu',
					race: 'Black',
					language: 'Chinese',
					focusArea: 'MenIssues',
					modality: 'art',
					specialization: 'anxiety',
					genderUser: ['male', 'female'],
					paymentMethod: ['insurance', 'self-pay'],
					sessionType: ['telehealth'],
					search: 'hello',
					name: 'jake black',
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').that.is.an('array').of.length(1);
			expect(response.body).to.have.property('meta');
		});
	});

	describe('GET /api/v1/therapists/byRadius/detail', function() {
		it('should return error if lat is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/api/v1/therapists/byRadius/detail')
				.set('Authorization', 'Bearer accessToken')
				.query({
					lng: '-74.0060',
					radius: 10,
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('error', 'lat, lng, and radius are required.');
		});

		it('should return error if lng is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/api/v1/therapists/byRadius/detail')
				.set('Authorization', 'Bearer accessToken')
				.query({
					lat: '40.7128',
					radius: 10,
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('error', 'lat, lng, and radius are required.');
		});

		it('should return error if radius is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/api/v1/therapists/byRadius/detail')
				.set('Authorization', 'Bearer accessToken')
				.query({
					lat: '40.7128',
					lng: '-74.0060',
				});

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('error', 'lat, lng, and radius are required.');
		});

		it('should return error if db query gives error', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindAllStub.rejects(new Error('Database error'));

			const response = await request(app)
				.get('/api/v1/therapists/byRadius/detail')
				.set('Authorization', 'Bearer accessToken')
				.query({
					lat: '40.7128',
					lng: '-74.0060',
					radius: 10,
				});

			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('error', 'Internal Server Error.');
		});

		it('should return therapists list successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindAllStub.resolves([dummyUser]);

			const response = await request(app)
				.get('/api/v1/therapists/byRadius/detail')
				.set('Authorization', 'Bearer accessToken')
				.query({
					lat: '40.7128',
					lng: '-74.0060',
					radius: 10,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('therapists').that.is.an('array').that.is.of.length(1);
		});
	});

	describe('POST /api/v1/therapists/duration-cost', function() {
		it('should return error therapist id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
				id: null,
			});

			const response = await request(app)
				.post('/api/v1/therapists/duration-cost')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(response.status).to.equal(HttpStatusCode.Unauthorized);
			expect(response.body).to.have.property('message', 'Unauthorized: Therapist ID not found');
		});

		it('should return error if something fails during duration cost create or update', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
			});
			therapistDurationCostFindAllStub.rejects(new Error('Database error'));

			const response = await request(app)
				.post('/api/v1/therapists/duration-cost')
				.set('Authorization', 'Bearer accessToken')
				.send({
					data: []
				});

			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'Internal server error');
			expect(response.body).to.have.property('error', 'Database error');
		});

		it('should create or update duration cost successfully', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
			});
			therapistDurationCostFindAllStub.resolves([
				{
					...dummyTherapistDurationCost,
					update: () => Promise.resolve(),
				}
			]);

			const response = await request(app)
				.post('/api/v1/therapists/duration-cost')
				.set('Authorization', 'Bearer accessToken')
				.send({
					data: [
						{
							category: 'individual',
							duration: 60,
							cost: 90,
						},
						{
							category: 'waitlist',
							duration: 60,
							cost: 150,
						}
					]
				});

			expect(therapistDurationCostCreateStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Duration and costs updated successfully');
		});
	});

	describe('GET /api/v1/therapists/duration-cost/detail', function() {
		it('should return error therapist id is not available', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
				id: null,
			});

			const response = await request(app)
				.get('/api/v1/therapists/duration-cost/detail')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Unauthorized);
			expect(response.body).to.have.property('message', 'Unauthorized: Therapist ID not found');
		});

		it('should return error if something fails during duration cost fetch', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
			});
			therapistDurationCostFindAllStub.rejects(new Error('Database error'));

			const response = await request(app)
				.get('/api/v1/therapists/duration-cost/detail')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'Internal server error');
			expect(response.body).to.have.property('error', 'Database error');
		});

		it('should return duration cost details successfully', async function () {
			userFindByPkStub.resolves({
				...dummyUser,
				role: 'therapist',
			});
			therapistDurationCostFindAllStub.resolves([dummyTherapistDurationCost]);

			const response = await request(app)
				.get('/api/v1/therapists/duration-cost/detail')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').to.be.an('object');
		});
	});

	describe('GET /api/v1/therapists/availablity/days', function() {
		it('should return error therapistId is not provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/api/v1/therapists/availablity/days')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('error', 'therapistId is required');
		});

		it('should return error if therapist is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.get('/api/v1/therapists/availablity/days')
				.set('Authorization', 'Bearer accessToken')
				.query({
					therapistId: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('error', 'Therapist timezone not found');
		});

		it('should return error if therapist timezone is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				timeZone: null,
			});

			const response = await request(app)
				.get('/api/v1/therapists/availablity/days')
				.set('Authorization', 'Bearer accessToken')
				.query({
					therapistId: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('error', 'Therapist timezone not found');
		});

		// it('should return error if patient is not found', async function () {
		// 	userFindByPkStub.resolves(dummyUser);
		// 	userFindOneStub.onFirstCall().resolves({
		// 		...dummyUser,
		// 		role: 'therapist',
		// 	});
		// 	userFindOneStub.onSecondCall().resolves(null);

		// 	const response = await request(app)
		// 		.get('/api/v1/therapists/availablity/days')
		// 		.set('Authorization', 'Bearer accessToken')
		// 		.query({
		// 			therapistId: 1,
		// 		});

		// 	expect(response.status).to.equal(HttpStatusCode.NotFound);
		// 	expect(response.body).to.have.property('error', 'Patient timezone not found');
		// });

		// it('should return error if patient timezone is not found', async function () {
		// 	userFindByPkStub.resolves(dummyUser);
		// 	userFindOneStub.onFirstCall().resolves({
		// 		...dummyUser,
		// 		role: 'therapist',
		// 	});
		// 	userFindOneStub.onSecondCall().resolves({
		// 		...dummyUser,
		// 		timeZone: null,
		// 	});

		// 	const response = await request(app)
		// 		.get('/api/v1/therapists/availablity/days')
		// 		.set('Authorization', 'Bearer accessToken')
		// 		.query({
		// 			therapistId: 1,
		// 		});

		// 	expect(response.status).to.equal(HttpStatusCode.NotFound);
		// 	expect(response.body).to.have.property('error', 'Patient timezone not found');
		// });

		it('should return error if something fails', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.rejects(new Error('Database error'));

			const response = await request(app)
				.get('/api/v1/therapists/availablity/days')
				.set('Authorization', 'Bearer accessToken')
				.query({
					therapistId: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('error', 'Internal server error');
		});

		it('should return available dates successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(dummyUser);
			userRegInfoFindOneStub.resolves({
				payloadInfo: {
					booking_time_hour: 6,
				}
			});
			normalOfficeHoursFindAllStub.resolves([
				{
					workingDay: 'Sunday',
					availabilityHours: '10:00:00',
				},
				{
					workingDay: 'Monday',
					availabilityHours: '10:00:00',
				},
				{
					workingDay: 'Tuesday',
					availabilityHours: '10:00:00',
				},
				{
					workingDay: 'Wednesday',
					availabilityHours: '10:00:00',
				},
				{
					workingDay: 'Thursday',
					availabilityHours: '10:00:00',
				},
				{
					workingDay: 'Friday',
					availabilityHours: '10:00:00',
				},
				{
					workingDay: 'Saturday',
					availabilityHours: '10:00:00',
				},
			]);
			appointmentsFindAllStub.resolves([
				{
					appointmentDate: dayjs().add(2, 'day').hour(10).minute(0).second(0).toISOString(),
				}
			]);

			const response = await request(app)
				.get('/api/v1/therapists/availablity/days')
				.set('Authorization', 'Bearer accessToken')
				.query({
					therapistId: 1,
				});
				console.log(response.body)
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('array').that.is.not.empty;
		});
	});
});