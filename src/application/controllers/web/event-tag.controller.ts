import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import {
	createTag,
	getTags,
} from '@/src/application/repositories/event-tag.repository'
import { HttpStatusCode } from 'axios'

const router = express.Router()

/********************************
 * * Get all tags
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const tags = await getTags({ req })
		res.send(paginatedData(tags, req))
	})
)

/********************************
 * * Create user
 * * @param firstname
 * * @param lastname
 * * @param email
 * * @param password
 * * @param role_id
 * * @returns User
 * *******************************/
router.post(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const event = await createTag(req)
		res.status(HttpStatusCode.Created).send({
			event,
			message: 'Event created',
		})
	})
)

export default router
