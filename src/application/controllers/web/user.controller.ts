import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import { UserType } from '@/src/application/helpers/constant.helper'
import { User } from '@/src/models'
import { HttpStatusCode } from 'axios'
import { NotFoundError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { RegisterMemberEmail } from '@/src/application/emails/register.email'
import { mail } from '@/src/configs/sendgrid.config'
import { encryptedB64 } from '@/src/application/helpers/encryption.helper'
import { activateUser, deactivateUser, deleteUser, getUsers } from '@/src/application/repositories/user.repository'
import logger from '@/src/configs/logger.config'

const router = express.Router()

/********************************
 * * Get all users
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		logger.info(`User ${req.user?.id} retrieved all users.`);
		const users = await getUsers({ req })
		res.send(paginatedData(users, req))
	})
)

/********************************
 * * Get user by id
 * * @param id
 * * @returns User
 * * @throws UserNotFound
 * *******************************/
router.get(
	'/:id',
	PortalMiddleware,
	// CacheMiddleware('users'),
	wrap(async (req: Request, res: Response) => {
		logger.info(`User ${req.user?.id} accessed details of User ${req.params.id}.`);
		const user = await User.findByPk(req.params.id)
		if (!user) throw new NotFoundError('User not found')
		res.send({ user })
	})
)

/********************************
 * * Create user
 * * @param firstname
 * * @param lastname
 * * @param email
 * * @param password
 * * @param role_id
 * * @returns User
 * *******************************/
router.post(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { firstname, lastname, email, phone, dob } = req.body
		logger.info(`User ${req.user?.id} is creating a new user.`);
		
		const user = await User.create({
			firstname,
			lastname,
			email,
			phone,
			dob,
			role: UserType.SYSTEM,
		})

		logger.info(`New user created with ID: ${user.id} by User ${req.user?.id}.`);

		const token = encryptedB64(user.id.toString())

		const emailData = RegisterMemberEmail.compile({
			email,
			user: user.toJSON(),
			url: `${process.env.FRONTEND_URL}/auth/verify-email?token=${token}`,
		})

		await mail.sendMail({
			to: emailData.To,
			subject: emailData.Subject,
			html: emailData.HtmlBody,
		})

		res.status(HttpStatusCode.Created).send({
			user,
			message: 'User created',
		})
	})
)


/********************************
 * * Update user
 * * @param id
 * * @returns User
 * * @throws UserNotFound
 * *******************************/
router.put(
	'/:id',
	PortalMiddleware,
	// CacheMiddleware('users'),
	wrap(async (req: Request, res: Response) => {
		const { firstname, lastname, email, phone, dob } = req.body
		logger.info(`User ${req.user?.id} is updating User ${req.params.id}.`);

		const user = await User.findByPk(req.params.id)
		if (!user) throw new NotFoundError('User not found')
		user.set({
			firstname,
			lastname,
			email,
			phone,
			dob,
		})
		await user.save()
		await user.reload()
		logger.info(`User ${req.params.id} updated successfully by User ${req.user?.id}.`);
		res.status(HttpStatusCode.Created).send({
			user,
			message: 'User updated',
		})
	})
)

/********************************
 * * Delete user
 * * @param id
 * * @returns User
 * * @throws UserNotFound
 * *******************************/
router.delete(
	'/:id',
	PortalMiddleware,
	// CacheMiddleware('users'),
	wrap(async (req: Request, res: Response) => {
	  const { id } = req.params;
	  const { currentDate } = req.query;  
	  const {reasonId} = req.query;
	  const{reason} = req.query;
	  await deleteUser(id, currentDate as string,reasonId as string,reason as string);
	  logger.info(`User ${id} deleted successfully by User ${req.user?.id}.`);
	  res.status(HttpStatusCode.Ok).send({ message: 'User deleted Successfully' });
	})
  );
/********************************
 * * Activate user
 * * @param id
 * * @returns message
 * * @throws UserNotFound
 * *******************************/
router.patch(
	'/activate/:id',
	PortalMiddleware,
	// CacheMiddleware('users'),
	wrap(async (req: Request, res: Response) => {
		logger.info(`User ${req.user?.id} is activating User ${req.params.id}.`);
		await activateUser(req.params.id)
		logger.info(`User ${req.params.id} activated successfully by User ${req.user?.id}.`);
		res.status(HttpStatusCode.Created).send({ message: 'User activated Successfully' })
	})
)

/********************************
 * * Deactivate user
 * * @param id
 * * @returns message
 * * @throws UserNotFound
 * *******************************/
router.patch(
	'/deactivate/:id',
	PortalMiddleware,
	// CacheMiddleware('users'),
	wrap(async (req: Request, res: Response) => {
		logger.info(`User ${req.user?.id} is deactivating User ${req.params.id}.`);

		
		await deactivateUser(req.params.id,req)
		logger.info(`User ${req.params.id} deactivated successfully by User ${req.user?.id}.`);
		res.status(HttpStatusCode.Created).send({ message: 'User deactivated Successfully' })
	})
)

export default router
