import { wrap } from '@/src/application/helpers';
import { Router, Request, Response } from 'express';
import { ContactUsEmail } from '../../emails/contact-us.email';
import { HttpStatusCode } from 'axios';
import { mail } from '@/src/configs/sendgrid.config';
import logger from '@/src/configs/logger.config';
import User from '@/src/models/user.model';

const router = Router();

/********************************
 * * Send Feedback Email
 * @param subject
 * @param reply_email
 * @param email_content
 * @returns status
 * @throws InternalServerError
 ********************************/
router.post(
  '/',
  wrap(async (req: Request, res: Response) => {
    try {     
      const { subject, reply_email, email_content } = req.body;
       const user = await User.findOne({
        where: {
          email:reply_email,
        },
      })
      const emailData = ContactUsEmail.compile({
        subject,
        reply_email,
        email_content,
      });

      await mail.sendMail({
        to: emailData.To,
        from: emailData.From,
        subject: emailData.Subject,
        html: emailData.HtmlBody,
      });

   

      logger.info(`Contact Us form submitted by user ${user?.id}`);

      return res
        .status(HttpStatusCode.Ok)
        .send({ message: 'Email has been sent successfully.' });
    } catch (error) {
      console.error('Error sending email:', error);

      return res
        .status(HttpStatusCode.InternalServerError)
        .send({ message: 'Failed to send email.' });
    }
  })
);

export default router;
