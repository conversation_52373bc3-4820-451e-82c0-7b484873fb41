// import express, { Request, Response, response } from 'express'
// import { wrap } from '@/src/application/helpers'
// import stripeService from '@/src/configs/stripe.config'
// import { HttpStatusCode } from 'axios'
// import { BadRequestError } from '../../handlers/errors'
// import logger from '@/src/configs/logger.config'

// const router = express.Router()

// /********************************
//  * * Get all pages
//  ********************************/
// router.post(
// 	'/subscribe',
// 	wrap(async (req: Request, res: Response) => {
// 		let customer
// 		const { email, plan: subscriptionPlan, pageUrl } = req.body
// 		const auth0UserId = email
// 		if (!email || !subscriptionPlan || !pageUrl) throw new BadRequestError('Invalid request. Please provide email, plan and pageUrl')
// 		logger.info(`Subscription request initiated for plan: ${subscriptionPlan}`);
// 		try {
// 			// Try to retrieve an existing customer by email
// 			const existingCustomers = await stripeService.listCustomers(email)
// 			if (existingCustomers.data.length > 0) {
// 				//customer already exists
// 				customer = existingCustomers.data[0]
// 				logger.info(`Existing Stripe Customer ID: ${customer.id}`);

// 				//check if the customer already has an active subscription
// 				const subscription = await stripeService.listSubscriptions(customer.id)
				
// 				if (subscription.data.length > 0) {
// 					// Customer already has an active subscription, send them to biiling portal to manage subscription
// 					logger.info(`Redirecting Customer ID: ${customer.id} to Stripe Billing Portal.`);
// 					const url = await stripeService.createBillingSession(customer.id, pageUrl)

// 					return res.status(HttpStatusCode.Created).json({ redirectUrl: url })
// 				} else {
// 					logger.info(`Creating new subscription session for Customer ID: ${customer.id}`);

// 					const sessionId = await stripeService.createCheckoutSession({
// 						customerId: customer.id,
// 						subscriptionPlan,
// 						redirectPage: pageUrl,
// 						metadata: { userId: auth0UserId },
// 					})

// 					logger.info(`Stripe Checkout Session created. Session ID: ${sessionId}`);

// 					return res.status(HttpStatusCode.Created).json({ id: sessionId })
// 				}
// 			} else {
// 				// No customer found, create a new one
// 				logger.info(`Creating new Stripe customer.`);
// 				customer = await stripeService.createCustomer({
// 					email,
// 					metadata: { userId: auth0UserId },
// 				})

// 				logger.info(`New Stripe Customer created. Customer ID: ${customer.id}`);


// 				// Now create the Stripe checkout session with the customer ID
// 				const sessionId = await stripeService.createCheckoutSession({
// 					customerId: customer.id,
// 					subscriptionPlan,
// 					redirectPage: pageUrl,
// 					metadata: { userId: auth0UserId },
// 				})

// 				logger.info(`New Stripe Checkout Session created. Session ID: ${sessionId}`);

// 				return res.status(HttpStatusCode.Created).json({ id: sessionId })
// 			}
// 		} catch (err) {
// 			logger.error(`Error occurred during subscription process : ${err}`)
// 		}
// 	})
// )

// router.get('/success', async (req, res) => {
// 	const session = await stripeService.getSessionById(req.query.session_id as string, { expand: ['subscription'] })
// 	// , { expand: ['subscription', 'subscription.plan.product'] }
// 	logger.info('After Success:', session)
// 	const { redirectPage } = req.query
// 	return res.redirect(`${process.env.FRONTEND_URL}${redirectPage}?success=true`)
// })

// export default router
