import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import { HttpStatusCode } from 'axios'
import { NotFoundError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import CacheMiddleware from '@/src/configs/cache.config'
import { blockPatient, getBlockedPatients, unblockPatient } from '../../repositories/therapist-blocked-list.repository'

const router = express.Router()

/********************************
 * * add patient to blocked list
 * @param patientId
 ********************************/
router.post(
	'/:id',
	PortalMiddleware,
	CacheMiddleware('therapist-blocked-list'),
	wrap(async (req: Request, res: Response) => {
		await blockPatient(req)
		res.status(HttpStatusCode.Created).send({
			message: 'Patient blocked successfully'
		})
	})
)

/********************************
 * * remove patient from blocked list
 * @param patientId
 ********************************/
router.delete(
	'/:id',
	PortalMiddleware,
	CacheMiddleware('therapist-blocked-list'),
	wrap(async (req: Request, res: Response) => {
		await unblockPatient(req)
		res.status(HttpStatusCode.Ok).send({
			message: 'Patient Unblocked successfully'
		})
	})
)

/********************************
 * * get blocked patients of therapist
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	CacheMiddleware('therapist-blocked-list'),
	wrap(async (req: Request, res: Response) => {
		const blockedPatients = await getBlockedPatients(req)
		res.send(paginatedData(blockedPatients, req))
		// res.send(blockedPatients)
	})
)

export default router