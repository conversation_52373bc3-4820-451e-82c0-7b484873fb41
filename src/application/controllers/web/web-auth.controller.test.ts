import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express from 'express';
import { User } from '@/src/models';
import { HttpStatusCode } from 'axios';
import authController from '@/src/application/controllers/web/auth.controller';
import { response } from '@/src/application/helpers/response.helper'
import { hashData } from '@/src/cryptoUtil';
import sequelize from '@/src/configs/database.config';
import { mail } from '@/src/configs/sendgrid.config';
import * as encryptionHelper from '@/src/application/helpers/encryption.helper';
import * as authRepository from '@/src/application/repositories/auth.repository';
import { MFA_CODE_SENT_MESSAGE, USER_NOT_FOUND } from '../../repositories/constants';
import jwt from 'jsonwebtoken';

const app = express();

app.use(express.json());
app.use(response);
app.use('/auth', authController);

describe('Web Auth Controller Unit Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindOneStub: sinon.SinonStub;
	let decryptedB64Stub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;
	let userUpdateStub: sinon.SinonStub;
	let sendMfaCodeStub: sinon.SinonStub;
	let loginWithoutMfaStub: sinon.SinonStub;
	let resendMfaStub: sinon.SinonStub;
	let verifyMfaStub: sinon.SinonStub;
	let userFindByPkStub: sinon.SinonStub;
	let encryptedB64Stub: sinon.SinonStub;
	let userGenerateTokenStub: sinon.SinonStub;
	let forgotPasswordStub: sinon.SinonStub;
	let resetPasswordStub: sinon.SinonStub;
	let changePasswordStub: sinon.SinonStub;
	let resetEmailStub: sinon.SinonStub;
	let emailVerificationStub: sinon.SinonStub;
	let verifyEmailStub: sinon.SinonStub;
	let registerPatientStub: sinon.SinonStub;
	let emailResetTokenStub: sinon.SinonStub;
	let phoneResetTokenStub: sinon.SinonStub;
	let resetPhoneStub: sinon.SinonStub;

	let dummyUser: Partial<User>;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		rollbackStub = sandbox.stub().resolves();
		sandbox.stub(sequelize, 'transaction').resolves({ 
			commit: async () => Promise.resolve(),
			rollback: rollbackStub
		} as any);
		sandbox.stub(mail, 'sendMail').resolves();
		userFindOneStub = sandbox.stub(User, 'findOne');
		userUpdateStub = sandbox.stub(User, 'update');
		decryptedB64Stub = sandbox.stub(encryptionHelper, 'decryptedB64');
		sendMfaCodeStub = sandbox.stub(authRepository, 'sendMfaCode').resolves();
		loginWithoutMfaStub = sandbox.stub(authRepository, 'loginWithoutMfa').resolves();
		resendMfaStub = sandbox.stub(authRepository, 'resendMFA').resolves();
		verifyMfaStub = sandbox.stub(authRepository, 'verifyMFA').resolves();
		userFindByPkStub = sandbox.stub(User, 'findByPk');
		encryptedB64Stub = sandbox.stub(encryptionHelper, 'encryptedB64');
		userGenerateTokenStub = sandbox.stub(User.prototype, 'generateToken');
		forgotPasswordStub = sandbox.stub(authRepository, 'forgotPassword').resolves();
		resetPasswordStub = sandbox.stub(authRepository, 'resetPassword').resolves();
		changePasswordStub = sandbox.stub(authRepository, 'changePassword').resolves();
		resetEmailStub = sandbox.stub(authRepository, 'resetEmail').resolves();
		emailVerificationStub = sandbox.stub(authRepository, 'emailVerification').resolves();
		verifyEmailStub = sandbox.stub(authRepository, 'verifyEmail').resolves();
		registerPatientStub = sandbox.stub(authRepository, 'registerPatient').resolves();
		emailResetTokenStub = sandbox.stub(authRepository, 'emailResetToken').resolves();
		phoneResetTokenStub = sandbox.stub(authRepository, 'phoneResetToken').resolves();
		resetPhoneStub = sandbox.stub(authRepository, 'resetPhone').resolves();
		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		const hash_email =  hashData('<EMAIL>');
		
		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			userToken: '123',
			email_hash: hash_email,
			role: 'therapist',
			isUnderReview: false,
			firstname: 'Janee',
			lastname: 'doe',
			acceptedAt: new Date(),
			rejectedAt: new Date(),
			emailVerifiedAt: new Date(),
		}
	})
	
	afterEach(function() {
		sandbox.restore();
	});

	describe('POST /auth/login', function() {
		it('should fail when no user is found', async function() {
			userFindOneStub.resolves(null);

			const response = await request(app).post('/auth/login').send({
				email: '<EMAIL>',
				password: 'password123'
			});

			expect(userFindOneStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', USER_NOT_FOUND);
			expect(response.status).to.equal(HttpStatusCode.NotFound);
		});

		it('should send mfa code when user has mfaEnabled true', async function() {
			userFindOneStub.resolves({
				...dummyUser,
				mfaEnabled: true,
			});
			sendMfaCodeStub.resolves({
				message: MFA_CODE_SENT_MESSAGE,
				user: dummyUser,
			});

			const response = await request(app).post('/auth/login').send({
				email: '<EMAIL>',
				password: 'password123'
			});

			expect(userFindOneStub.calledOnce).to.be.true;
			expect(userUpdateStub.calledOnce).to.be.true;
			expect(sendMfaCodeStub.calledOnce).to.be.true;
			expect(userUpdateStub.firstCall.args).to.deep.equal([
				{ version: 1 },
				{ where: { id: dummyUser.id } }
			]);
			expect(response.body).to.have.property('message', MFA_CODE_SENT_MESSAGE);
			expect(response.body).to.have.property('mfaCodeSent', true);
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});

		it('should login directly when user has mfaEnabled false', async function() {
			const accessToken = 'dummyAccess';
			const refreshToken = 'dummyRefresh';

			userFindOneStub.resolves({
				...dummyUser,
				mfaEnabled: false,
			});
			loginWithoutMfaStub.resolves({
				accessToken,
				refreshToken,
				user: dummyUser,
			});

			const response = await request(app).post('/auth/login').send({
				email: '<EMAIL>',
				password: 'password123'
			});

			expect(userFindOneStub.calledOnce).to.be.true;
			expect(userUpdateStub.calledOnce).to.be.true;
			expect(loginWithoutMfaStub.calledOnce).to.be.true;
			expect(userUpdateStub.firstCall.args).to.deep.equal([
				{ version: 1 },
				{ where: { id: dummyUser.id } }
			]);
			expect(loginWithoutMfaStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('accessToken', accessToken);
			expect(response.body).to.have.property('refreshToken', refreshToken);
			expect(response.body).to.have.property('user').to.not.be.empty;
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/resend-mfa', function() {
		it('should resend mfa code to the user', async function() {
			resendMfaStub.resolves({
				message: MFA_CODE_SENT_MESSAGE,
			});

			const response = await request(app).post('/auth/resend-mfa').send({
				email: '<EMAIL>',
			});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(resendMfaStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', MFA_CODE_SENT_MESSAGE);
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/verify-mfa', function() {
		it('should verify mfa successfully', async function() {
			const accessToken = 'dummyAccess';
			const refreshToken = 'dummyRefresh';

			verifyMfaStub.resolves({
				accessToken,
				refreshToken,
				user: dummyUser,
			});

			const response = await request(app).post('/auth/verify-mfa').send({
				email: '<EMAIL>',
				code: '123456'
			});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.calledOnce).to.be.true;
			expect(verifyMfaStub.calledOnce).to.be.true;
			expect(userUpdateStub.firstCall.args).to.deep.equal([
				{ version: 1 },
				{ where: { id: dummyUser.id } }
			]);
			expect(response.body).to.have.property('accessToken', accessToken);
			expect(response.body).to.have.property('refreshToken', refreshToken);
			expect(response.body).to.have.property('user').to.not.be.empty;
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/refresh', function() {
		it('should refresh auth tokens successfully', async function() {
			userFindByPkStub.resolves({
				...dummyUser,
				generateToken: userGenerateTokenStub,
			});
			userGenerateTokenStub.returns('newAccessToken');
			decryptedB64Stub.returns('userAccessToken:1');
			encryptedB64Stub.returns('newEncryptedRefreshToken');

			const response = await request(app)
				.post('/auth/refresh')
				.set('Authorization', 'Bearer userAccessToken')
				.send({
					refreshToken: 'oldEncryptedRefreshToken',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledOnce).to.be.true;
			expect(decryptedB64Stub.calledOnce).to.be.true;
			expect(encryptedB64Stub.calledOnce).to.be.true;
			expect(userGenerateTokenStub.calledOnce).to.be.true;
			expect(userFindByPkStub.firstCall.args[0]).to.equal('1');
			expect(decryptedB64Stub.firstCall.args[0]).to.equal('oldEncryptedRefreshToken');
			expect(encryptedB64Stub.firstCall.args[0]).to.equal(`newAccessToken:${dummyUser.id}`);
			expect(response.body).to.have.property('accessToken', 'newAccessToken');
			expect(response.body).to.have.property('resetToken', 'newEncryptedRefreshToken');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});

		it('should fail if refresh token not provided', async function() {
			const response = await request(app)
				.post('/auth/refresh')
				.set('Authorization', 'Bearer userAccessToken')
				.send({});

			expect(userFindByPkStub.notCalled).to.be.true;
			expect(decryptedB64Stub.notCalled).to.be.true;
			expect(encryptedB64Stub.notCalled).to.be.true;
			expect(userGenerateTokenStub.notCalled).to.be.true;
			expect(response.body).to.have.property('message', 'Refresh token is required');
			expect(response.status).to.equal(HttpStatusCode.BadRequest);
		});

		it('should fail if tokens do not match', async function() {
			decryptedB64Stub.returns('differentAccessToken:1');

			const response = await request(app)
				.post('/auth/refresh')
				.set('Authorization', 'Bearer userAccessToken')
				.send({
					refreshToken: 'oldEncryptedRefreshToken',
				});

			expect(userFindByPkStub.notCalled).to.be.true;
			expect(decryptedB64Stub.calledOnceWith('oldEncryptedRefreshToken')).to.be.true;
			expect(encryptedB64Stub.notCalled).to.be.true;
			expect(userGenerateTokenStub.notCalled).to.be.true;
			expect(response.body).to.have.property('message', 'Invalid token');
			expect(response.status).to.equal(HttpStatusCode.BadRequest);
		});

		it('should fail if user not found', async function() {
			decryptedB64Stub.returns('userAccessToken:1');
			userFindByPkStub.resolves(null);

			const response = await request(app)
				.post('/auth/refresh')
				.set('Authorization', 'Bearer userAccessToken')
				.send({
					refreshToken: 'oldEncryptedRefreshToken',
				});

			expect(userFindByPkStub.calledOnceWith('1')).to.be.true;
			expect(decryptedB64Stub.calledOnceWith('oldEncryptedRefreshToken')).to.be.true;
			expect(encryptedB64Stub.notCalled).to.be.true;
			expect(userGenerateTokenStub.notCalled).to.be.true;
			expect(response.body).to.have.property('message', 'User not found');
			expect(response.status).to.equal(HttpStatusCode.NotFound);
		});
	});

	describe('POST /auth/forgot-password', function() {
		it('should send reset password email successfully', async function() {
			forgotPasswordStub.resolves({
				message: 'Magic link sent to your email.',
			});

			const response = await request(app)
				.post('/auth/forgot-password')
				.send({
					email: '<EMAIL>',
				});
			
			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(forgotPasswordStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Magic link sent to your email.');
			expect(response.status).to.equal(HttpStatusCode.Created);
		});
	});

	describe('POST /auth/forgot-password', function() {
		it('should send reset password email successfully', async function() {
			forgotPasswordStub.resolves({
				message: 'Magic link sent to your email.',
			});

			const response = await request(app)
				.post('/auth/forgot-password')
				.send({
					email: '<EMAIL>',
				});
			
			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(forgotPasswordStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Magic link sent to your email.');
			expect(response.status).to.equal(HttpStatusCode.Created);
		});
	});

	describe('POST /auth/reset-password', function() {
		it('should reset password of the user successfully', async function() {
			resetPasswordStub.resolves({
				message: 'Password reset successfully.',
				role: 'therapist',
			});

			const response = await request(app)
				.post('/auth/reset-password')
				.send({
					token: 'resetToken',
					password: 'newPassword123',
					confirmPassword: 'newPassword123',
				});
			
			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(resetPasswordStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Password reset successfully.');
			expect(response.body).to.have.property('role', 'therapist');
			expect(response.status).to.equal(HttpStatusCode.Created);
		});
	});

	describe('POST /auth/change-password', function() {
		it('should change password of the user successfully', async function() {
			changePasswordStub.resolves({
				message: 'Password changed successfully.',
				user: dummyUser,
			});
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/auth/change-password')
				.set('Authorization', 'Bearer accessToken')
				.send({
					old_password: 'oldPassword123',
					new_password: 'newPassword123',
					confirm_password: 'newPassword123',
					email: '<EMAIL>',
				});
			
			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledOnce).to.be.true;
			expect(changePasswordStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Password changed successfully.');
			expect(response.body).to.have.property('user');
			expect(response.status).to.equal(HttpStatusCode.Created);
		});
	});

	describe('POST /auth/reset-email', function() {
		it('should reset email of the user successfully', async function() {
			resetEmailStub.resolves({
				message: 'Email updated successfully.',
				user: dummyUser,
			});
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/auth/reset-email')
				.set('Authorization', 'Bearer accessToken')
				.send({
					old_email: '<EMAIL>',
					new_email: '<EMAIL>',
					code: '123456',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledOnce).to.be.true;
			expect(resetEmailStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Email updated successfully.');
			expect(response.body).to.have.property('user');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/send-token', function() {
		it('should send email with token for email reset successfully', async function() {
			emailResetTokenStub.resolves({
				message: 'Email Reset Token sent to your email.',
			});
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/auth/send-token')
				.set('Authorization', 'Bearer accessToken')
				.send({
					old_email: '<EMAIL>',
					new_email: '<EMAIL>',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledOnce).to.be.true;
			expect(emailResetTokenStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Email Reset Token sent to your email.');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/token-sms', function() {
		it('should send sms with token for phone reset successfully', async function() {
			phoneResetTokenStub.resolves({
				message: 'Verification code sent to your phone.',
			});
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/auth/token-sms')
				.set('Authorization', 'Bearer accessToken')
				.send({
					new_phone: '4568458745',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledOnce).to.be.true;
			expect(phoneResetTokenStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Verification code sent to your phone.');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/reset-phone', function() {
		it('should reset phone number of the user successfully', async function() {
			resetPhoneStub.resolves({
				message: 'Phone number updated successfully.',
				user: dummyUser,
			});
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.post('/auth/reset-phone')
				.set('Authorization', 'Bearer accessToken')
				.send({
					new_phone: '4568458745',
					code: '123456',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledOnce).to.be.true;
			expect(resetPhoneStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Phone number updated successfully.');
			expect(response.body).to.have.property('user');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/email-verification', function() {
		it('should send email verification email successfully', async function() {
			emailVerificationStub.resolves({
				message: 'Magic link sent to your email.',
			});

			const response = await request(app)
				.post('/auth/email-verification')
				.send({
					email: '<EMAIL>',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(emailVerificationStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Magic link sent to your email.');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});
	});

	describe('POST /auth/verify-email', function() {
		it('should verify email of the user successfully', async function() {
			verifyEmailStub.resolves({
				message: 'Email verified successfully.',
				role: 'therapist',
			});

			const response = await request(app)
				.post('/auth/verify-email')
				.send({
					email: '<EMAIL>',
					code: '123456',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(verifyEmailStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Email verified successfully.');
			expect(response.body).to.have.property('role', 'therapist');
			expect(response.status).to.equal(HttpStatusCode.Created);
		});
	});

	describe('POST /auth/pre-register-verification', function() {
		it('should return success response', async function() {
			userFindOneStub.resolves(null);

			const response = await request(app)
				.post('/auth/pre-register-verification')
				.send({
					email: '<EMAIL>',
					phone: '**********',
				});

			expect(userFindOneStub.calledOnce).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(response.body).to.not.have.property('message');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});

		it('should fail if no email and phone is provided', async function() {
			const response = await request(app)
				.post('/auth/pre-register-verification')
				.send({});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(response.body).to.have.property('message', 'Email or phone is required');
			expect(response.status).to.equal(HttpStatusCode.BadRequest);
		});

		it('should fail if user with given email or phone is found', async function() {
			userFindOneStub.resolves(dummyUser);

			const response = await request(app)
				.post('/auth/pre-register-verification')
				.send({
					email: '<EMAIL>',
					phone: '**********',
				});

			expect(userFindOneStub.calledOnce).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(response.body).to.not.have.property('Email or Phone already used');
			expect(response.status).to.equal(HttpStatusCode.Forbidden);
		});
	});

	describe('POST /auth/register/patient', function() {
		it('should register patient successfully', async function() {
			const response = await request(app)
				.post('/auth/register/patient')
				.send({
					email: '<EMAIL>',
				});

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(registerPatientStub.calledOnce).to.be.true;
			expect(response.body).to.have.property('message', 'Registration Successful!');
			expect(response.status).to.equal(HttpStatusCode.Created);
		});
	});

	describe('GET /auth/profile', function() {
		it('should return profile of the user successfully', async function() {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/auth/profile')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledTwice).to.be.true;
			expect(response.body).to.have.property('data');
			expect(response.status).to.equal(HttpStatusCode.Ok);
		});

		it('should fail if user is not found', async function() {
			userFindByPkStub.onFirstCall().resolves(dummyUser);
			userFindByPkStub.onSecondCall().resolves(null);

			const response = await request(app)
				.get('/auth/profile')
				.set('Authorization', 'Bearer accessToken')
				.send();

			expect(userFindOneStub.notCalled).to.be.true;
			expect(userUpdateStub.notCalled).to.be.true;
			expect(userFindByPkStub.calledTwice).to.be.true;
			expect(response.body).to.have.property('message', 'User not found');
			expect(response.status).to.equal(HttpStatusCode.NotFound);
		});
	});

});
