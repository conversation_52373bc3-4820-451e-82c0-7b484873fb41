import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import CheckUserVerificationMiddleware from '../../middlewares/checkUserVerification.middleware'
import { HttpStatusCode } from 'axios'

const router = express.Router()

/********************************
 * * Make appointment
 ********************************/
router.post(
	'/',
	PortalMiddleware,
	CheckUserVerificationMiddleware,
	wrap(async (req: Request, res: Response) => {
		res.status(HttpStatusCode.Created).send({ message: 'Appointment created' })
	})
)

export default router
