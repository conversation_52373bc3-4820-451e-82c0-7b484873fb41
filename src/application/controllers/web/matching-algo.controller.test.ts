import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import { User } from '@/src/models';

// Import repositories to stub
import * as matchingAlgoRepository from '@/src/application/repositories/matching-algo.repository';
import { UserType } from '@/src/application/helpers/constant.helper';

// Mock user data
const mockUser = {
  id: 'test-user-id',
  firstname: 'Test',
  lastname: 'User',
  email: '<EMAIL>',
  emailVerifiedAt: new Date(),
  role: UserType.SYSTEM,
  version: 1
} as any;

// Dummy data for tests
const dummyMatchingAlgoResources = [
  {
    id: 'resource-123',
    therapistPageId: 'therapist-page-123',
    patientPageId: 'patient-page-123',
    category: 'anxiety',
    questionId: 'question-123',
    answerId: 'answer-123',
    createdAt: new Date(),
    updatedAt: new Date(),
    therapistPage: {
      id: 'therapist-page-123',
      title: 'Therapist Page 1',
      slug: 'therapist-page-1'
    },
    patientPage: {
      id: 'patient-page-123',
      title: 'Patient Page 1',
      slug: 'patient-page-1'
    },
    questionnaire: {
      id: 'question-123',
      question: 'Test Question 1'
    },
    answer: {
      id: 'answer-123',
      answer: 'Test Answer 1'
    }
  },
  {
    id: 'resource-456',
    therapistPageId: 'therapist-page-456',
    patientPageId: 'patient-page-456',
    category: 'depression',
    questionId: 'question-456',
    answerId: 'answer-456',
    createdAt: new Date(),
    updatedAt: new Date(),
    therapistPage: {
      id: 'therapist-page-456',
      title: 'Therapist Page 2',
      slug: 'therapist-page-2'
    },
    patientPage: {
      id: 'patient-page-456',
      title: 'Patient Page 2',
      slug: 'patient-page-2'
    },
    questionnaire: {
      id: 'question-456',
      question: 'Test Question 2'
    },
    answer: {
      id: 'answer-456',
      answer: 'Test Answer 2'
    }
  }
];

// Set up Express response methods
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Add auth middleware to set user for all requests
app.use((req: Request, res: Response, next: NextFunction) => {
  req.user = { ...mockUser };
  req.role = mockUser.role;
  next();
});

// Path constants
const portalMiddlewarePath = '../../../application/middlewares/portal.middleware';

// Store original middleware
let originalPortalMiddleware: any;

describe('Web Matching Algo Controller Unit Test', function () {
  let sandbox: sinon.SinonSandbox;
  let createMatchingAlgoResourceStub: sinon.SinonStub;
  let getAllMatchingAlgoResourcesStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let userFindByPkStub: sinon.SinonStub;

  before(function() {
    // Save original middleware if it exists
    if (require.cache[require.resolve(portalMiddlewarePath)]) {
      originalPortalMiddleware = require.cache[require.resolve(portalMiddlewarePath)];
    }

    // Create a custom portal middleware function that can be modified in tests
    const customPortalMiddleware = function(req: Request, res: Response, next: NextFunction) {
      // This will be the default behavior for most tests
      req.user = { ...mockUser };
      req.role = mockUser.role;
      next();
    };
    
    // Override the middleware module in require.cache
    require.cache[require.resolve(portalMiddlewarePath)] = {
      id: require.resolve(portalMiddlewarePath),
      filename: require.resolve(portalMiddlewarePath),
      loaded: true,
      exports: customPortalMiddleware
    } as NodeModule;

    // Mount the controller after mocking the middleware
    const controllerPath = '../../../application/controllers/web/matching-algo.controller';
    app.use('/matching-algo', require(controllerPath).default);
  });
  
  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    // Stub repository methods
    createMatchingAlgoResourceStub = sandbox.stub(matchingAlgoRepository, 'createMatchingAlgoResource');
    getAllMatchingAlgoResourcesStub = sandbox.stub(matchingAlgoRepository, 'getAllMatchingAlgoResources');
    
    // JWT stub
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'web',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    
    // User model stub
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockUser);
  });
  
  afterEach(function () {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    const controllerPath = '../../../application/controllers/web/matching-algo.controller';
    delete require.cache[require.resolve(controllerPath)];
    
    // Restore original middleware
    if (originalPortalMiddleware) {
      require.cache[require.resolve(portalMiddlewarePath)] = originalPortalMiddleware;
    } else {
      delete require.cache[require.resolve(portalMiddlewarePath)];
    }
  });

  describe('GET /matching-algo', function () {
    it('should retrieve all matching algo resources successfully', async function () {
      // Setup stubs
      getAllMatchingAlgoResourcesStub.resolves(dummyMatchingAlgoResources);
      
      const response = await request(app)
        .get('/matching-algo')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getAllMatchingAlgoResourcesStub);
      
      // Check response structure
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(2);
      expect(response.body[0]).to.have.property('id', 'resource-123');
      expect(response.body[0]).to.have.property('category', 'anxiety');
      expect(response.body[1]).to.have.property('id', 'resource-456');
      expect(response.body[1]).to.have.property('category', 'depression');
    });
    
    it('should filter resources by category', async function () {
      // Setup stubs to return filtered results
      const filteredResources = [dummyMatchingAlgoResources[0]];
      getAllMatchingAlgoResourcesStub.resolves(filteredResources);
      
      const response = await request(app)
        .get('/matching-algo?category=anxiety')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called with correct parameters
      sinon.assert.calledOnce(getAllMatchingAlgoResourcesStub);
      const requestArg = getAllMatchingAlgoResourcesStub.firstCall.args[0];
      expect(requestArg.query).to.have.property('category', 'anxiety');
      
      // Check response structure
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.lengthOf(1);
      expect(response.body[0]).to.have.property('id', 'resource-123');
      expect(response.body[0]).to.have.property('category', 'anxiety');
    });
    
    it('should handle errors when retrieving resources', async function () {
      // Setup stubs to throw an error
      getAllMatchingAlgoResourcesStub.rejects(new Error('Database error'));
      
      const response = await request(app)
        .get('/matching-algo')
        .expect(HttpStatusCode.InternalServerError);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getAllMatchingAlgoResourcesStub);
    });
  });

  describe('POST /matching-algo', function () {
    it('should create a matching algo resource successfully', async function () {
      // Setup stubs
      createMatchingAlgoResourceStub.resolves(undefined);
      
      const requestBody = {
        resource1: {
          therapistPage: { value: 'therapist-page-123' },
          patientPage: { value: 'patient-page-123' },
          category: { value: 'anxiety' },
          question: { value: 'question-123' },
          answer: { value: 'answer-123' }
        },
        resource2: {
          therapistPage: { value: 'therapist-page-456' },
          patientPage: { value: 'patient-page-456' },
          category: { value: 'depression' },
          question: { value: 'question-456' },
          answer: { value: 'answer-456' }
        }
      };
      
      const response = await request(app)
        .post('/matching-algo')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(createMatchingAlgoResourceStub);
      
      // Check response structure
      expect(response.body).to.have.property('message', 'Matching Algo Resource created');
    });
    
    it('should return 401 when user is not authenticated', async function () {
      // Create a new app instance with unauthorized middleware
      const testApp = express();
      testApp.use(express.json());
      
      // Create unauthorized middleware
      const unauthorizedMiddleware = (req: Request, res: Response, next: NextFunction) => {
        return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
      };
      
      // Mount the middleware and a simple route
      testApp.post('/matching-algo', unauthorizedMiddleware, (req, res) => {
        // This should never be reached
        res.status(HttpStatusCode.Created).send({ message: 'Matching Algo Resource created' });
      });
      
      const requestBody = {
        resource1: {
          therapistPage: { value: 'therapist-page-123' },
          patientPage: { value: 'patient-page-123' },
          category: { value: 'anxiety' },
          question: { value: 'question-123' },
          answer: { value: 'answer-123' }
        }
      };
      
      const response = await request(testApp)
        .post('/matching-algo')
        .send(requestBody)
        .expect(HttpStatusCode.Unauthorized);
      
      expect(response.body).to.have.property('message', 'Unauthorized');
    });
    
    it('should handle validation errors', async function () {
      // Setup stubs to throw a validation error
      createMatchingAlgoResourceStub.rejects(new Error('Validation error'));
      
      const invalidRequestBody = {
        resource1: {
          // Missing required fields
          category: { value: 'anxiety' }
        }
      };
      
      const response = await request(app)
        .post('/matching-algo')
        .send(invalidRequestBody)
        .expect(HttpStatusCode.InternalServerError);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(createMatchingAlgoResourceStub);
    });
  });
});
