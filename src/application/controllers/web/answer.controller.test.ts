import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import { NotFoundError } from '@/src/application/handlers/errors';
import { Answer } from '@/src/models';
import * as answerRepository from '@/src/application/repositories/answer.repository';
import * as paginationHelper from '@/src/application/helpers/pagination.helper';
import jwt from 'jsonwebtoken';

// Mock user data
const mockUser = {
  id: 'test-user-id',
  firstname: 'Test',
  lastname: 'User',
  email: '<EMAIL>',
  emailVerifiedAt: new Date(),
  role: 'ADMIN',
  version: 1
} as any;

// Set up Express response methods
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Add auth middleware to set user for all requests
app.use((req: Request, res: Response, next: NextFunction) => {
  req.user = { ...mockUser };
  req.role = mockUser.role;
  next();
});

// Path constants
const portalMiddlewarePath = '@/src/application/middlewares/portal.middleware';
const cacheMiddlewarePath = '@/src/configs/cache.config';

// Store original middlewares
let originalPortalMiddleware: any;
let originalCacheMiddleware: any;

describe('Web Answer Controller Unit Test', function () {
  let sandbox: sinon.SinonSandbox;
  let getAnswersStub: sinon.SinonStub;
  let getAnswerByIdStub: sinon.SinonStub;
  let createAnswerStub: sinon.SinonStub;
  let updateAnswerStub: sinon.SinonStub;
  let updateAllSlugStub: sinon.SinonStub;
  let deleteAnswerStub: sinon.SinonStub;
  let paginatedDataStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  
  const dummyAnswer = {
    id: 'answer-123',
    answer: 'Test Answer',
    info: 'Test Info',
    slug: 'test-answer',
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  const dummyAnswers = {
    count: 1,
    rows: [dummyAnswer]
  };

  before(function() {
    // Save original middlewares if they exist
    if (require.cache[require.resolve(portalMiddlewarePath)]) {
      originalPortalMiddleware = require.cache[require.resolve(portalMiddlewarePath)];
    }
    
    if (require.cache[require.resolve(cacheMiddlewarePath)]) {
      originalCacheMiddleware = require.cache[require.resolve(cacheMiddlewarePath)];
    }

    // Create a custom portal middleware function
    const customPortalMiddleware = (req: Request, res: Response, next: NextFunction) => {
      req.user = { ...mockUser };
      req.role = mockUser.role;
      next();
    };

    // Create a custom cache middleware function
    const customCacheMiddleware = (prefix: string) => (req: Request, res: Response, next: NextFunction) => {
      next();
    };

    // Override the middleware modules in require.cache
    require.cache[require.resolve(portalMiddlewarePath)] = {
      id: require.resolve(portalMiddlewarePath),
      filename: require.resolve(portalMiddlewarePath),
      loaded: true,
      exports: customPortalMiddleware
    } as NodeModule;

    require.cache[require.resolve(cacheMiddlewarePath)] = {
      id: require.resolve(cacheMiddlewarePath),
      filename: require.resolve(cacheMiddlewarePath),
      loaded: true,
      exports: customCacheMiddleware
    } as NodeModule;

    // Mount the controller after mocking the middlewares
    const controllerPath = '../../../application/controllers/web/answer.controller';
    app.use('/answers', require(controllerPath).default);
  });
  
  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    // Stubs for answer repository
    getAnswersStub = sandbox.stub(answerRepository, 'getAnswers');
    getAnswerByIdStub = sandbox.stub(answerRepository, 'getAnswerById');
    createAnswerStub = sandbox.stub(answerRepository, 'createAnswer');
    updateAnswerStub = sandbox.stub(answerRepository, 'updateAnswer');
    updateAllSlugStub = sandbox.stub(answerRepository, 'updateAllSlug');
    deleteAnswerStub = sandbox.stub(answerRepository, 'deleteAnswer');
    
    // Stub for pagination helper
    paginatedDataStub = sandbox.stub(paginationHelper, 'paginatedData');

    // JWT stub
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'web',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
  });
  
  afterEach(function () {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    const controllerPath = '../../../application/controllers/web/answer.controller';
    delete require.cache[require.resolve(controllerPath)];
    
    // Restore original middlewares
    if (originalPortalMiddleware) {
      require.cache[require.resolve(portalMiddlewarePath)] = originalPortalMiddleware;
    } else {
      delete require.cache[require.resolve(portalMiddlewarePath)];
    }
    
    if (originalCacheMiddleware) {
      require.cache[require.resolve(cacheMiddlewarePath)] = originalCacheMiddleware;
    } else {
      delete require.cache[require.resolve(cacheMiddlewarePath)];
    }
  });
  
  describe('GET /answers', function () {
    it('should retrieve all answers successfully', async function () {
      // Setup stubs
      getAnswersStub.resolves(dummyAnswers);
      paginatedDataStub.returns({
        data: dummyAnswers.rows,
        meta: {
          total: dummyAnswers.count,
          currentPage: 1,
          perPage: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });
      
      const response = await request(app)
        .get('/answers')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getAnswersStub);
      // Check response structure without asserting paginatedDataStub call count
      // since the controller might be using a different approach
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
      expect(response.body.data).to.be.an('array');
      expect(response.body.meta).to.be.an('object');
    });
    
    it('should filter answers by search term', async function () {
      // Setup stubs
      getAnswersStub.resolves(dummyAnswers);
      paginatedDataStub.returns({
        data: dummyAnswers.rows,
        meta: {
          total: dummyAnswers.count,
          currentPage: 1,
          perPage: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });
      
      const response = await request(app)
        .get('/answers?search=test')
        .expect(HttpStatusCode.Ok);
      
      expect(getAnswersStub.calledOnce).to.be.true;
      expect(getAnswersStub.firstCall.args[0].req.query.search).to.equal('test');
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.be.an('array');
      expect(response.body).to.have.property('meta');
      expect(response.body.meta).to.be.an('object');
    });
    
    it('should filter answers by pageId', async function () {
      // Setup stubs
      getAnswersStub.resolves(dummyAnswers);
      paginatedDataStub.returns({
        data: dummyAnswers.rows,
        meta: {
          total: dummyAnswers.count,
          currentPage: 1,
          perPage: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });
      
      const response = await request(app)
        .get('/answers?pageId=page-123')
        .expect(HttpStatusCode.Ok);
      
      expect(getAnswersStub.calledOnce).to.be.true;
      expect(getAnswersStub.firstCall.args[0].req.query.pageId).to.equal('page-123');
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.be.an('array');
    });
  });
  
  describe('GET /answers/:id', function () {
    it('should retrieve a single answer by ID successfully', async function () {
      // Setup stubs
      getAnswerByIdStub.resolves(dummyAnswer);
      
      const response = await request(app)
        .get('/answers/answer-123')
        .expect(HttpStatusCode.Ok);
      
      sinon.assert.calledOnce(getAnswerByIdStub);
      sinon.assert.calledWith(getAnswerByIdStub, 'answer-123');
      
      expect(response.body).to.have.property('answer');
      expect(response.body).to.have.property('message', 'Answer found');
      
      // Check individual properties instead of deep equal for objects with dates
      expect(response.body.answer.id).to.equal(dummyAnswer.id);
      expect(response.body.answer.answer).to.equal(dummyAnswer.answer);
      expect(response.body.answer.info).to.equal(dummyAnswer.info);
      expect(response.body.answer.slug).to.equal(dummyAnswer.slug);
    });
    
    it('should return 404 when answer is not found', async function () {
      // Setup stubs
      getAnswerByIdStub.throws(new NotFoundError('Answer not found'));
      
      const response = await request(app)
        .get('/answers/non-existent-id')
        .expect(HttpStatusCode.NotFound);
      
      expect(getAnswerByIdStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Answer not found');
    });
  });
  
  describe('POST /answers', function () {
    it('should create a new answer successfully', async function () {
      // Setup stubs
      createAnswerStub.resolves(dummyAnswer);
      
      const requestBody = {
        answer: 'Test Answer',
        info: 'Test Info'
      };
      
      const response = await request(app)
        .post('/answers')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      sinon.assert.calledOnce(createAnswerStub);
      
      expect(response.body).to.have.property('answer');
      expect(response.body).to.have.property('message', 'Answer created');
      
      // Check individual properties instead of deep equal for objects with dates
      expect(response.body.answer.id).to.equal(dummyAnswer.id);
      expect(response.body.answer.answer).to.equal(dummyAnswer.answer);
      expect(response.body.answer.info).to.equal(dummyAnswer.info);
      expect(response.body.answer.slug).to.equal(dummyAnswer.slug);
    });
    
    it('should handle validation errors when creating an answer', async function () {
      // Setup stubs
      createAnswerStub.throws(new Error('Validation error'));
      
      const requestBody = {
        // Missing required fields
      };
      
      const response = await request(app)
        .post('/answers')
        .send(requestBody)
        .expect(HttpStatusCode.InternalServerError);
      
      expect(createAnswerStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message');
    });
  });
  
  describe('PUT /answers/updateAllSlug', function () {
    it('should update all answer slugs successfully', async function () {
      // Setup stubs
      updateAllSlugStub.resolves();
      
      const response = await request(app)
        .put('/answers/updateAllSlug')
        .expect(HttpStatusCode.Created);
      
      expect(updateAllSlugStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'All Answer Slugs updated');
    });
    
    it('should handle errors when updating all slugs', async function () {
      // Setup stubs
      updateAllSlugStub.throws(new Error('Database error'));
      
      const response = await request(app)
        .put('/answers/updateAllSlug')
        .expect(HttpStatusCode.InternalServerError);
      
      expect(updateAllSlugStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message');
    });
  });
  
  describe('PUT /answers/:id', function () {
    it('should update an existing answer successfully', async function () {
      // Setup stubs
      updateAnswerStub.resolves(dummyAnswer);
      
      const requestBody = {
        answer: 'Updated Answer',
        info: 'Updated Info'
      };
      
      const response = await request(app)
        .put('/answers/answer-123')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      sinon.assert.calledOnce(updateAnswerStub);
      
      expect(response.body).to.have.property('answer');
      expect(response.body).to.have.property('message', 'Answer updated');
      
      // Check individual properties instead of deep equal for objects with dates
      expect(response.body.answer.id).to.equal(dummyAnswer.id);
      expect(response.body.answer.answer).to.equal(dummyAnswer.answer);
      expect(response.body.answer.info).to.equal(dummyAnswer.info);
      expect(response.body.answer.slug).to.equal(dummyAnswer.slug);
    });
    
    it('should return 404 when updating a non-existent answer', async function () {
      // Setup stubs
      updateAnswerStub.throws(new NotFoundError('Answer not found'));
      
      const requestBody = {
        answer: 'Updated Answer',
        info: 'Updated Info'
      };
      
      const response = await request(app)
        .put('/answers/non-existent-id')
        .send(requestBody)
        .expect(HttpStatusCode.NotFound);
      
      expect(updateAnswerStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Answer not found');
    });
  });
  
  describe('DELETE /answers/:id', function () {
    it('should delete an answer successfully', async function () {
      // Setup stubs
      deleteAnswerStub.resolves();
      
      const response = await request(app)
        .delete('/answers/answer-123')
        .expect(HttpStatusCode.Ok);
      
      expect(deleteAnswerStub.calledOnce).to.be.true;
      expect(deleteAnswerStub.calledWith('answer-123')).to.be.true;
      expect(response.body).to.have.property('message', 'Answer deleted');
    });
    
    it('should return 404 when deleting a non-existent answer', async function () {
      // Setup stubs
      deleteAnswerStub.throws(new NotFoundError('Answer not found'));
      
      const response = await request(app)
        .delete('/answers/non-existent-id')
        .expect(HttpStatusCode.NotFound);
      
      expect(deleteAnswerStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Answer not found');
    });
  });
});
