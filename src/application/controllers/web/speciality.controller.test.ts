import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: any;
      role?: string;
    }
  }
}

describe('Speciality Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockSpecialityRepository: any;
  let mockPaginationHelper: any;
  let mockWrap: any;
  let mockPortalMiddleware: sinon.SinonStub;
  let mockSpecialityValidator: sinon.SinonStub;
  let mockSpeciality: any;
  let mockNotFoundError: any;
  let specialityController: any;

  // Mock data
  const mockAdminUser = {
    id: 1,
    firstname: 'Admin',
    lastname: 'User',
    email: '<EMAIL>',
    role: 'admin',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockSpecialityData = {
    id: 1,
    name: 'Anxiety Disorders',
    description: 'Treatment of anxiety-related conditions',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  };

  const mockPaginatedResponse = {
    count: 5,
    rows: [mockSpecialityData]
  };

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock the speciality repository functions
    mockSpecialityRepository = {
      getSpecialities: sandbox.stub()
    };

    // Mock pagination helper
    mockPaginationHelper = {
      paginatedData: sandbox.stub()
    };

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(error.statusCode || HttpStatusCode.InternalServerError).send({
            message: error.message,
            data: error.data
          });
        }
      };
    });

    // Mock PortalMiddleware
    mockPortalMiddleware = sandbox.stub().callsFake((req: Request, _res: Response, next: NextFunction) => {
      req.user = mockAdminUser;
      req.role = mockAdminUser.role;
      next();
    });

    // Mock SpecialityValidator
    mockSpecialityValidator = sandbox.stub().callsFake((_req: Request, _res: Response, next: NextFunction) => {
      next();
    });

    // Mock Speciality model
    mockSpeciality = {
      create: sandbox.stub(),
      findByPk: sandbox.stub(),
      update: sandbox.stub(),
      destroy: sandbox.stub()
    };

    // Mock NotFoundError
    mockNotFoundError = class extends Error {
      constructor(message: string) {
        super(message);
        this.name = 'NotFoundError';
      }
    };

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./speciality.controller')];
    delete require.cache[require.resolve('../../repositories/speciality.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../validators')];
    delete require.cache[require.resolve('../../../models')];
    delete require.cache[require.resolve('../../handlers/errors')];

    // Mock the modules
    require.cache[require.resolve('../../repositories/speciality.repository')] = {
      id: require.resolve('../../repositories/speciality.repository'),
      filename: require.resolve('../../repositories/speciality.repository'),
      loaded: true,
      exports: mockSpecialityRepository
    } as NodeModule;

    require.cache[require.resolve('../../helpers/pagination.helper')] = {
      id: require.resolve('../../helpers/pagination.helper'),
      filename: require.resolve('../../helpers/pagination.helper'),
      loaded: true,
      exports: mockPaginationHelper
    } as NodeModule;

    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../middlewares/portal.middleware')] = {
      id: require.resolve('../../middlewares/portal.middleware'),
      filename: require.resolve('../../middlewares/portal.middleware'),
      loaded: true,
      exports: mockPortalMiddleware
    } as NodeModule;

    require.cache[require.resolve('../../validators')] = {
      id: require.resolve('../../validators'),
      filename: require.resolve('../../validators'),
      loaded: true,
      exports: { SpecialityValidator: mockSpecialityValidator }
    } as NodeModule;

    require.cache[require.resolve('../../../models')] = {
      id: require.resolve('../../../models'),
      filename: require.resolve('../../../models'),
      loaded: true,
      exports: { Speciality: mockSpeciality }
    } as NodeModule;

    require.cache[require.resolve('../../handlers/errors')] = {
      id: require.resolve('../../handlers/errors'),
      filename: require.resolve('../../handlers/errors'),
      loaded: true,
      exports: { NotFoundError: mockNotFoundError }
    } as NodeModule;

    // Load the controller after mocking
    specialityController = require('./speciality.controller').default;
    app.use('/specialities', specialityController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./speciality.controller')];
    delete require.cache[require.resolve('../../repositories/speciality.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../validators')];
    delete require.cache[require.resolve('../../../models')];
    delete require.cache[require.resolve('../../handlers/errors')];
  });

  describe('GET /', function() {
    it('should get all specialities with pagination', async function() {
      // Setup mocks
      mockSpecialityRepository.getSpecialities.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 10,
          total: 5,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });

      const response = await request(app)
        .get('/specialities')
        .expect(200);

      expect(mockSpecialityRepository.getSpecialities.calledOnce).to.be.true;
      expect(mockPaginationHelper.paginatedData.calledOnce).to.be.true;
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
    });

    it('should handle errors when getting specialities', async function() {
      // Setup mock to throw error
      mockSpecialityRepository.getSpecialities.rejects(new Error('Database error'));

      const response = await request(app)
        .get('/specialities')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database error');
    });

    it('should not require authentication for GET endpoint', async function() {
      mockSpecialityRepository.getSpecialities.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 1, perPage: 10, total: 5, lastPage: 1, nextPage: null, prevPage: null }
      });

      // Note: GET endpoint doesn't use PortalMiddleware
      await request(app)
        .get('/specialities')
        .expect(200);

      expect(mockPortalMiddleware.called).to.be.false;
    });
  });

  describe('POST /', function() {
    const createSpecialityData = {
      name: 'Depression Treatment',
      description: 'Specialized treatment for depression and mood disorders'
    };

    it('should create speciality successfully', async function() {
      // Setup mock
      mockSpeciality.create.resolves(mockSpecialityData);

      const response = await request(app)
        .post('/specialities')
        .send(createSpecialityData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockSpecialityValidator.calledOnce).to.be.true;
      expect(mockSpeciality.create.calledOnce).to.be.true;
      expect(response.body).to.have.property('speciality');
      expect(response.body).to.have.property('message', 'Speciality created successfully');
    });

    it('should handle validation errors', async function() {
      // Mock validator to call next with error
      mockSpecialityValidator.callsFake((_req: Request, res: Response, _next: NextFunction) => {
        res.status(400).send({ message: 'Name is required' });
      });

      const response = await request(app)
        .post('/specialities')
        .send({ description: 'Missing name field' })
        .expect(400);

      expect(response.body).to.have.property('message', 'Name is required');
    });

    it('should handle database errors during creation', async function() {
      mockSpeciality.create.rejects(new Error('Database creation failed'));

      const response = await request(app)
        .post('/specialities')
        .send(createSpecialityData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Database creation failed');
    });

    it('should create speciality with only name field', async function() {
      const minimalData = {
        name: 'PTSD Treatment'
      };

      const createdSpeciality = {
        ...mockSpecialityData,
        name: 'PTSD Treatment',
        description: null
      };

      mockSpeciality.create.resolves(createdSpeciality);

      const response = await request(app)
        .post('/specialities')
        .send(minimalData)
        .expect(201);

      expect(response.body).to.have.property('speciality');
      expect(response.body.speciality.name).to.equal('PTSD Treatment');
    });
  });

  describe('PUT /:id', function() {
    const updateSpecialityData = {
      name: 'Updated Anxiety Treatment',
      description: 'Updated description for anxiety treatment'
    };

    beforeEach(function() {
      // Mock speciality instance with update method
      const mockSpecialityInstance = {
        id: 1,
        name: 'Anxiety Disorders',
        description: 'Treatment of anxiety-related conditions',
        update: sandbox.stub().resolves({
          ...mockSpecialityData,
          ...updateSpecialityData
        })
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);
    });

    it('should update speciality successfully', async function() {
      const response = await request(app)
        .put('/specialities/1')
        .send(updateSpecialityData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockSpecialityValidator.calledOnce).to.be.true;
      expect(mockSpeciality.findByPk.calledWith('1')).to.be.true;
      expect(response.body).to.have.property('speciality');
      expect(response.body).to.have.property('message', 'Speciality updated successfully');
    });

    it('should handle speciality not found during update', async function() {
      mockSpeciality.findByPk.resolves(null);

      const response = await request(app)
        .put('/specialities/999')
        .send(updateSpecialityData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Speciality not found');
    });

    it('should handle partial updates', async function() {
      const partialUpdateData = {
        name: 'Only updating name'
      };

      const response = await request(app)
        .put('/specialities/1')
        .send(partialUpdateData)
        .expect(201);

      expect(response.body).to.have.property('speciality');
    });

    it('should handle update with empty data', async function() {
      const response = await request(app)
        .put('/specialities/1')
        .send({})
        .expect(201);

      expect(response.body).to.have.property('speciality');
    });

    it('should handle database errors during update', async function() {
      const mockSpecialityInstance = {
        update: sandbox.stub().rejects(new Error('Database update failed'))
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);

      const response = await request(app)
        .put('/specialities/1')
        .send(updateSpecialityData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Database update failed');
    });

    it('should handle invalid ID format', async function() {
      mockSpeciality.findByPk.rejects(new Error('Invalid ID format'));

      const response = await request(app)
        .put('/specialities/invalid-id')
        .send(updateSpecialityData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Invalid ID format');
    });
  });

  describe('DELETE /:id', function() {
    beforeEach(function() {
      // Mock speciality instance with destroy method
      const mockSpecialityInstance = {
        id: 1,
        name: 'Anxiety Disorders',
        description: 'Treatment of anxiety-related conditions',
        destroy: sandbox.stub().resolves()
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);
    });

    it('should delete speciality successfully', async function() {
      const response = await request(app)
        .delete('/specialities/1')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockSpeciality.findByPk.calledWith('1')).to.be.true;
      expect(response.body).to.have.property('message', 'Speciality deleted');
    });

    it('should handle speciality not found during deletion', async function() {
      mockSpeciality.findByPk.resolves(null);

      const response = await request(app)
        .delete('/specialities/999')
        .expect(500);

      expect(response.body).to.have.property('message', 'Speciality not found');
    });

    it('should handle database errors during deletion', async function() {
      const mockSpecialityInstance = {
        destroy: sandbox.stub().rejects(new Error('Database deletion failed'))
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);

      const response = await request(app)
        .delete('/specialities/1')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database deletion failed');
    });

    it('should handle invalid ID format for deletion', async function() {
      mockSpeciality.findByPk.rejects(new Error('Invalid ID format'));

      const response = await request(app)
        .delete('/specialities/invalid-id')
        .expect(500);

      expect(response.body).to.have.property('message', 'Invalid ID format');
    });
  });

  describe('Edge Cases and Error Handling', function() {
    describe('Input Validation', function() {
      it('should handle very long speciality names', async function() {
        const longNameData = {
          name: 'A'.repeat(255),
          description: 'Long name test'
        };

        mockSpeciality.create.resolves({
          ...mockSpecialityData,
          name: longNameData.name
        });

        const response = await request(app)
          .post('/specialities')
          .send(longNameData)
          .expect(201);

        expect(response.body).to.have.property('speciality');
      });

      it('should handle special characters in name and description', async function() {
        const specialCharData = {
          name: 'Anxiety & Depression Treatment',
          description: 'Treatment for anxiety & depression (including PTSD, OCD, etc.)'
        };

        mockSpeciality.create.resolves({
          ...mockSpecialityData,
          ...specialCharData
        });

        const response = await request(app)
          .post('/specialities')
          .send(specialCharData)
          .expect(201);

        expect(response.body).to.have.property('speciality');
      });

      it('should handle null description', async function() {
        const nullDescData = {
          name: 'Valid speciality name',
          description: null
        };

        mockSpeciality.create.resolves({
          ...mockSpecialityData,
          description: null
        });

        const response = await request(app)
          .post('/specialities')
          .send(nullDescData)
          .expect(201);

        expect(response.body).to.have.property('speciality');
      });
    });

    describe('Concurrent Operations', function() {
      it('should handle multiple simultaneous speciality creations', async function() {
        mockSpeciality.create.resolves(mockSpecialityData);

        const promises = Array.from({ length: 5 }, (_, i) =>
          request(app)
            .post('/specialities')
            .send({
              name: `Speciality ${i + 1}`,
              description: `Description ${i + 1}`
            })
        );

        const responses = await Promise.all(promises);

        responses.forEach(response => {
          expect(response.status).to.equal(201);
          expect(response.body).to.have.property('speciality');
        });

        expect(mockSpeciality.create.callCount).to.equal(5);
      });

      it('should handle multiple simultaneous updates', async function() {
        const mockSpecialityInstance = {
          update: sandbox.stub().resolves(mockSpecialityData)
        };
        mockSpeciality.findByPk.resolves(mockSpecialityInstance);

        const promises = Array.from({ length: 3 }, (_, i) =>
          request(app)
            .put('/specialities/1')
            .send({
              name: `Updated Speciality ${i + 1}`,
              description: `Updated Description ${i + 1}`
            })
        );

        const responses = await Promise.all(promises);

        responses.forEach(response => {
          expect(response.status).to.equal(201);
        });
      });
    });

    describe('Large Data Handling', function() {
      it('should handle large number of specialities', async function() {
        const manySpecialities = Array.from({ length: 100 }, (_, i) => ({
          ...mockSpecialityData,
          id: i + 1,
          name: `Speciality ${i + 1}`
        }));

        const largePaginatedResponse = {
          count: 100,
          rows: manySpecialities
        };

        mockSpecialityRepository.getSpecialities.resolves(largePaginatedResponse);
        mockPaginationHelper.paginatedData.returns({
          data: manySpecialities,
          meta: {
            currentPage: 1,
            perPage: 20,
            total: 100,
            lastPage: 5,
            nextPage: 2,
            prevPage: null
          }
        });

        const response = await request(app)
          .get('/specialities')
          .expect(200);

        expect(response.body.data).to.have.length(100);
        expect(response.body.meta.total).to.equal(100);
      });

      it('should handle very long descriptions', async function() {
        const longDescData = {
          name: 'Test Speciality',
          description: 'A'.repeat(2000)
        };

        mockSpeciality.create.resolves({
          ...mockSpecialityData,
          description: longDescData.description
        });

        const response = await request(app)
          .post('/specialities')
          .send(longDescData)
          .expect(201);

        expect(response.body).to.have.property('speciality');
      });
    });
  });

  describe('Middleware Integration', function() {
    it('should require authentication for POST, PUT, DELETE endpoints', async function() {
      // Mock PortalMiddleware to reject unauthorized requests
      const unauthorizedMiddleware = sandbox.stub().callsFake((_req: Request, res: Response, _next: NextFunction) => {
        res.status(401).send({ message: 'Unauthorized' });
      });

      // Clear and re-mock the middleware
      delete require.cache[require.resolve('../../middlewares/portal.middleware')];
      require.cache[require.resolve('../../middlewares/portal.middleware')] = {
        id: require.resolve('../../middlewares/portal.middleware'),
        filename: require.resolve('../../middlewares/portal.middleware'),
        loaded: true,
        exports: unauthorizedMiddleware
      } as NodeModule;

      // Reload controller with new middleware
      delete require.cache[require.resolve('./speciality.controller')];
      const newController = require('./speciality.controller').default;
      const testApp = express();
      testApp.use(express.json());
      testApp.use('/specialities', newController);

      // Test protected routes
      await request(testApp).post('/specialities').send({}).expect(401);
      await request(testApp).put('/specialities/1').send({}).expect(401);
      await request(testApp).delete('/specialities/1').expect(401);

      // GET should still work (no auth required)
      mockSpecialityRepository.getSpecialities.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 1, perPage: 10, total: 5, lastPage: 1, nextPage: null, prevPage: null }
      });
      await request(testApp).get('/specialities').expect(200);
    });

    it('should apply validation middleware to POST and PUT endpoints', async function() {
      // Test that validator is called
      await request(app)
        .post('/specialities')
        .send({ name: 'Test', description: 'Test desc' })
        .expect(201);

      expect(mockSpecialityValidator.calledOnce).to.be.true;

      // Reset and test PUT
      mockSpecialityValidator.resetHistory();
      const mockSpecialityInstance = {
        update: sandbox.stub().resolves(mockSpecialityData)
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);

      await request(app)
        .put('/specialities/1')
        .send({ name: 'Updated Test' })
        .expect(201);

      expect(mockSpecialityValidator.calledOnce).to.be.true;
    });
  });

  describe('Response Format Tests', function() {
    it('should return correct response format for GET /', async function() {
      mockSpecialityRepository.getSpecialities.resolves(mockPaginatedResponse);
      const expectedPaginatedData = {
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 10,
          total: 5,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      };
      mockPaginationHelper.paginatedData.returns(expectedPaginatedData);

      const response = await request(app)
        .get('/specialities')
        .expect(200);

      expect(response.body).to.deep.equal(expectedPaginatedData);
    });

    it('should return correct response format for POST /', async function() {
      mockSpeciality.create.resolves(mockSpecialityData);

      const response = await request(app)
        .post('/specialities')
        .send({
          name: 'Test Speciality',
          description: 'Test description'
        })
        .expect(201);

      expect(response.body).to.have.property('speciality');
      expect(response.body).to.have.property('message', 'Speciality created successfully');
      expect(response.body.speciality).to.deep.equal(mockSpecialityData);
    });

    it('should return correct response format for PUT /:id', async function() {
      const updatedSpeciality = {
        ...mockSpecialityData,
        name: 'Updated Name'
      };

      const mockSpecialityInstance = {
        update: sandbox.stub().resolves(updatedSpeciality)
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);

      const response = await request(app)
        .put('/specialities/1')
        .send({ name: 'Updated Name' })
        .expect(201);

      expect(response.body).to.have.property('speciality');
      expect(response.body).to.have.property('message', 'Speciality updated successfully');
    });

    it('should return correct response format for DELETE /:id', async function() {
      const mockSpecialityInstance = {
        destroy: sandbox.stub().resolves()
      };
      mockSpeciality.findByPk.resolves(mockSpecialityInstance);

      const response = await request(app)
        .delete('/specialities/1')
        .expect(200);

      expect(response.body).to.have.property('message', 'Speciality deleted');
      expect(response.body).to.not.have.property('speciality');
    });
  });
});
