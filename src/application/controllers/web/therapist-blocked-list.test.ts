import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: any;
      role?: string;
    }
  }
}

describe('Therapist Blocked List Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockTherapistBlockedListRepository: any;
  let mockPaginationHelper: any;
  let mockWrap: any;
  let mockPortalMiddleware: sinon.SinonStub;
  let mockCacheMiddleware: sinon.SinonStub;
  let therapistBlockedListController: any;

  // Mock data
  const mockTherapistUser = {
    id: 1,
    firstname: '<PERSON>',
    lastname: 'Therapist',
    email: '<EMAIL>',
    role: 'therapist',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockPatientUser = {
    id: 2,
    firstname: '<PERSON>',
    lastname: 'Patient',
    email: '<EMAIL>',
    role: 'patient',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockBlockedPatientData = {
    id: 1,
    therapistId: 1,
    patientId: 2,
    blockedAt: new Date(),
    patient: mockPatientUser
  };

  const mockBlockedPatientsResponse = {
    count: 2,
    rows: [mockBlockedPatientData]
  };

  const mockPaginatedResponse = {
    data: [mockBlockedPatientData],
    meta: {
      currentPage: 1,
      perPage: 20,
      total: 2,
      lastPage: 1,
      nextPage: null,
      prevPage: null
    }
  };

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock the therapist blocked list repository functions
    mockTherapistBlockedListRepository = {
      blockPatient: sandbox.stub(),
      unblockPatient: sandbox.stub(),
      getBlockedPatients: sandbox.stub()
    };

    // Mock pagination helper
    mockPaginationHelper = {
      paginatedData: sandbox.stub()
    };

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(error.statusCode || HttpStatusCode.InternalServerError).send({
            message: error.message,
            data: error.data
          });
        }
      };
    });

    // Mock PortalMiddleware
    mockPortalMiddleware = sandbox.stub().callsFake((req: Request, _res: Response, next: NextFunction) => {
      req.user = mockTherapistUser;
      req.role = mockTherapistUser.role;
      next();
    });

    // Mock CacheMiddleware
    mockCacheMiddleware = sandbox.stub().callsFake((_cacheKey: string) => {
      return (_req: Request, _res: Response, next: NextFunction) => {
        next();
      };
    });

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./therapist-blocked-list.controller')];
    delete require.cache[require.resolve('../../repositories/therapist-blocked-list.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../configs/cache.config')];

    // Mock the modules
    require.cache[require.resolve('../../repositories/therapist-blocked-list.repository')] = {
      id: require.resolve('../../repositories/therapist-blocked-list.repository'),
      filename: require.resolve('../../repositories/therapist-blocked-list.repository'),
      loaded: true,
      exports: mockTherapistBlockedListRepository
    } as NodeModule;

    require.cache[require.resolve('../../helpers/pagination.helper')] = {
      id: require.resolve('../../helpers/pagination.helper'),
      filename: require.resolve('../../helpers/pagination.helper'),
      loaded: true,
      exports: mockPaginationHelper
    } as NodeModule;

    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../middlewares/portal.middleware')] = {
      id: require.resolve('../../middlewares/portal.middleware'),
      filename: require.resolve('../../middlewares/portal.middleware'),
      loaded: true,
      exports: mockPortalMiddleware
    } as NodeModule;

    require.cache[require.resolve('../../../configs/cache.config')] = {
      id: require.resolve('../../../configs/cache.config'),
      filename: require.resolve('../../../configs/cache.config'),
      loaded: true,
      exports: mockCacheMiddleware
    } as NodeModule;

    // Load the controller after mocking
    therapistBlockedListController = require('./therapist-blocked-list.controller').default;
    app.use('/therapist-blocked-list', therapistBlockedListController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./therapist-blocked-list.controller')];
    delete require.cache[require.resolve('../../repositories/therapist-blocked-list.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../configs/cache.config')];
  });

  describe('POST /:id (Block Patient)', function() {
    it('should block patient successfully', async function() {
      // Setup mocks
      mockTherapistBlockedListRepository.blockPatient.resolves();

      const response = await request(app)
        .post('/therapist-blocked-list/2')
        .expect(HttpStatusCode.Created);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockCacheMiddleware.calledWith('therapist-blocked-list')).to.be.true;
      expect(mockTherapistBlockedListRepository.blockPatient.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Patient blocked successfully');
    });

    it('should handle blocking already blocked patient', async function() {
      // Setup mock to throw error for already blocked patient
      const alreadyBlockedError = new Error('Patient is already blocked');
      alreadyBlockedError.name = 'ConflictError';
      mockTherapistBlockedListRepository.blockPatient.rejects(alreadyBlockedError);

      const response = await request(app)
        .post('/therapist-blocked-list/2')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Patient is already blocked');
    });

    it('should handle blocking non-existent patient', async function() {
      // Setup mock to throw NotFoundError
      const notFoundError = new Error('Patient not found');
      notFoundError.name = 'NotFoundError';
      mockTherapistBlockedListRepository.blockPatient.rejects(notFoundError);

      const response = await request(app)
        .post('/therapist-blocked-list/999')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Patient not found');
    });

    it('should handle database errors during blocking', async function() {
      mockTherapistBlockedListRepository.blockPatient.rejects(new Error('Database connection failed'));

      const response = await request(app)
        .post('/therapist-blocked-list/2')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Database connection failed');
    });

    it('should handle invalid patient ID format', async function() {
      mockTherapistBlockedListRepository.blockPatient.rejects(new Error('Invalid patient ID format'));

      const response = await request(app)
        .post('/therapist-blocked-list/invalid-id')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Invalid patient ID format');
    });

    it('should pass correct request object to repository', async function() {
      mockTherapistBlockedListRepository.blockPatient.resolves();

      await request(app)
        .post('/therapist-blocked-list/2')
        .expect(HttpStatusCode.Created);

      const calledRequest = mockTherapistBlockedListRepository.blockPatient.getCall(0).args[0];
      expect(calledRequest.params.id).to.equal('2');
      expect(calledRequest.user).to.deep.equal(mockTherapistUser);
    });
  });

  describe('DELETE /:id (Unblock Patient)', function() {
    it('should unblock patient successfully', async function() {
      // Setup mocks
      mockTherapistBlockedListRepository.unblockPatient.resolves();

      const response = await request(app)
        .delete('/therapist-blocked-list/2')
        .expect(HttpStatusCode.Ok);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockCacheMiddleware.calledWith('therapist-blocked-list')).to.be.true;
      expect(mockTherapistBlockedListRepository.unblockPatient.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Patient Unblocked successfully');
    });

    it('should handle unblocking non-blocked patient', async function() {
      // Setup mock to throw error for non-blocked patient
      const notBlockedError = new Error('Patient is not blocked');
      notBlockedError.name = 'NotFoundError';
      mockTherapistBlockedListRepository.unblockPatient.rejects(notBlockedError);

      const response = await request(app)
        .delete('/therapist-blocked-list/2')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Patient is not blocked');
    });

    it('should handle unblocking non-existent patient', async function() {
      // Setup mock to throw NotFoundError
      const notFoundError = new Error('Patient not found');
      notFoundError.name = 'NotFoundError';
      mockTherapistBlockedListRepository.unblockPatient.rejects(notFoundError);

      const response = await request(app)
        .delete('/therapist-blocked-list/999')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Patient not found');
    });

    it('should handle database errors during unblocking', async function() {
      mockTherapistBlockedListRepository.unblockPatient.rejects(new Error('Database deletion failed'));

      const response = await request(app)
        .delete('/therapist-blocked-list/2')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Database deletion failed');
    });

    it('should handle invalid patient ID format for unblocking', async function() {
      mockTherapistBlockedListRepository.unblockPatient.rejects(new Error('Invalid patient ID format'));

      const response = await request(app)
        .delete('/therapist-blocked-list/invalid-id')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Invalid patient ID format');
    });

    it('should pass correct request object to repository', async function() {
      mockTherapistBlockedListRepository.unblockPatient.resolves();

      await request(app)
        .delete('/therapist-blocked-list/2')
        .expect(HttpStatusCode.Ok);

      const calledRequest = mockTherapistBlockedListRepository.unblockPatient.getCall(0).args[0];
      expect(calledRequest.params.id).to.equal('2');
      expect(calledRequest.user).to.deep.equal(mockTherapistUser);
    });
  });

  describe('GET / (Get Blocked Patients)', function() {
    it('should get blocked patients with pagination', async function() {
      // Setup mocks
      mockTherapistBlockedListRepository.getBlockedPatients.resolves(mockBlockedPatientsResponse);
      mockPaginationHelper.paginatedData.returns(mockPaginatedResponse);

      const response = await request(app)
        .get('/therapist-blocked-list')
        .expect(HttpStatusCode.Ok);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockCacheMiddleware.calledWith('therapist-blocked-list')).to.be.true;
      expect(mockTherapistBlockedListRepository.getBlockedPatients.calledOnce).to.be.true;
      expect(mockPaginationHelper.paginatedData.calledOnce).to.be.true;
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
      expect(response.body.data).to.be.an('array');
    });

    it('should handle empty blocked patients list', async function() {
      const emptyResponse = {
        count: 0,
        rows: []
      };
      const emptyPaginatedResponse = {
        data: [],
        meta: {
          currentPage: 1,
          perPage: 20,
          total: 0,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      };

      mockTherapistBlockedListRepository.getBlockedPatients.resolves(emptyResponse);
      mockPaginationHelper.paginatedData.returns(emptyPaginatedResponse);

      const response = await request(app)
        .get('/therapist-blocked-list')
        .expect(HttpStatusCode.Ok);

      expect(response.body.data).to.be.an('array').that.is.empty;
      expect(response.body.meta.total).to.equal(0);
    });

  });});
