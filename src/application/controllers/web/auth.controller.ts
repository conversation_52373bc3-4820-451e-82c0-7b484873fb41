import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import {
	ResetPasswordValidator,
	ForgotPasswordValidator,
	VerifyEmailValidator,
	PreRegisterValidator,
	MfaValidator,	
} from '@/src/application/validators'
import { Calendar, NormalOfficeHours, User, UserRegistrationInfo } from '@/src/models'
import {
	BadRequestError,
	NotFoundError,
	ForbiddenError,
	ConflictError,
} from '@/src/application/handlers/errors'
import {
	decryptedB64,
	encryptedB64,
} from '@/src/application/helpers/encryption.helper'
import bcrypt from 'bcryptjs'
import { HttpStatusCode } from 'axios'
import { Op } from 'sequelize'
import { PLATFORM, UserType } from '@/src/application/helpers/constant.helper'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { getTokenFromCode } from '@/src/configs/google.config'
import sequelize from '@/src/configs/database.config'
import {
	forgotPassword,
	resetPassword,
	emailVerification,
	verifyEmail,
	registerPatient,
	changePassword,
	emailResetToken,
	resetEmail,
	phoneResetToken,
	resetPhone,
	verifyMFA,
	resendMFA,
	sendMfaCode,
	loginWithoutMfa,
} from '@/src/application/repositories/auth.repository'
import { generateString, maskEmail, maskPhoneNumber, splitAndGenerateTime } from '../../helpers/general.helper'
import dayjs from 'dayjs'
import { RegisterTherapistEmail ,RegisterNewTherapistEmail} from '../../emails/register.email'
import { RegisterTherapistEmailSendToAdmin } from '../../emails/register-email-send-to-admin'
import { mail } from '@/src/configs/sendgrid.config'
import logger from '@/src/configs/logger.config'
import { USER_NOT_FOUND } from '../../repositories/constants'
import ChangePasswordValidator from '../../validators/change-password.validator'
import { hashData } from '@/src/cryptoUtil'
import { LicenseUpdationEmail } from '../../emails/license-updation.email'

const router = express.Router()

/********************************
 * * Login user
 * @param email
 * @param password
 * @returns message / (accessToken, refreshToken, user)
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/login',
	wrap(async (req: Request, res: Response) => {
		logger.info(`Login attempt initiated by email ${maskEmail(req.body.email)}`);
		const hash_email =  hashData(req.body.email);
		const foundUser = await User.findOne({
			where: {
				email_hash:hash_email,
			}
		});
		if (!foundUser) return res.notFound(USER_NOT_FOUND);
		 
		if (foundUser.mfaEnabled) {
			const { message, user } = await sendMfaCode(req);
			if (user?.id) {
				await User.update(
					{ version: 1 },
					{ where: { id: user.id } }
				);
			}
			logger.info(`MFA code sent successfully to user ${user.id}`)		
			res.send({
				message,
				mfaCodeSent: true,
			});
		} else {
			const { accessToken, refreshToken, user } = await loginWithoutMfa(req);
		
			if (user?.id) {
				await User.update(
					{ version: 1 },
					{ where: { id: user.id } }
				);
			}

			logger.info(`User ${user?.id} logged in successfully`);

			res.send({
				accessToken,
				refreshToken,
				user,
			});
		}
	})
);

/**
 * @desc Resends the MFA code via email
 * @param email
 * @returns success message
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 */
router.post(
	"/resend-mfa",
	wrap(async (req: Request, res: Response) => {
		logger.info(`MFA Resend initiated for email ${req.body.email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		const { message } = await resendMFA(req);
		res.send({ message });
	})
);

/**
 * @desc Verifies MFA code and generates authentication tokens
 * @param email
 * @param code // mfa code
 * @returns user
 * @throws UnauthorizedError
 * @throws BadRequestError
 * @throws NotFoundError
 */
router.post(
	'/verify-mfa',
	MfaValidator,
	wrap(async (req: Request, res: Response) => {
		logger.info(`MFA Verification initiated`);
		const { accessToken, refreshToken, user } = await verifyMFA(req);		
		if (user?.id) {
			await User.update(
				{ version: 1 },
				{ where: { id: user.id } }
			);
		}

		logger.info(`User ${user?.id} logged in successfully`);

		res.send({
			accessToken,
			refreshToken,
			user,
		});
	})
);


/********************************
 * * Refresh token
 * @param refreshToken
 * @returns accessToken
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/refresh',
	wrap(async (req: Request, res: Response) => {
		const { refreshToken } = req.body
		const token = req.headers.authorization?.split(' ')[1] ?? ''

		if (refreshToken) {
			const [data, id] = decryptedB64(refreshToken).split(':')
			if (token === data) {
				const user = await User.findByPk(id)
				if (!user) throw new NotFoundError('User not found')
				const accessToken = user.generateToken()
				const resetToken = encryptedB64(`${accessToken}:${user.id}`)

				res.send({
					accessToken,
					resetToken,
				})
			} else {
				throw new BadRequestError('Invalid token')
			}
		} else {
			throw new BadRequestError('Refresh token is required')
		}
	})
)

/********************************
 * * Forgot password
 * @param email
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/forgot-password',
	ForgotPasswordValidator,
	wrap(async (req: Request, res: Response) => {
		logger.info(`Forgot password request initiated by email ${req.body.email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		const { message } = await forgotPassword(req);
		logger.info(`Forgot password verification link sent to email ${req.body.email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		res.status(HttpStatusCode.Created).send({ message });
	})
)

/********************************
 * * Reset password
 * @param password
 * @param token
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/reset-password',
	ResetPasswordValidator,
	wrap(async (req: Request, res: Response) => {
		const { message, role } = await resetPassword(req);
		return res.status(HttpStatusCode.Created).send({
			message,
			role
		})
	})
)

/********************************
 * * Change password
 * @param password
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/change-password',
	ChangePasswordValidator,
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		logger.info(`Forgot password request initiated by email ${req.body.email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		const { message, user } = await changePassword(req);
		return res.status(HttpStatusCode.Created).send({
			message,
			user,
		})
	})
)

/********************************
 * * Reset Email
 * @param old_email
 * @param new_email
 * @param token
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/reset-email',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		logger.info(`Reset email request initiated by email ${req.body.old_email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		const { message, user } = await resetEmail(req);
		return res.status(HttpStatusCode.Ok).send({
			message,
			user,
		})
	})
)

/********************************
 * * Send Token For Email Reset
 * @param email
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/send-token',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		logger.info(`Email update request initiated by user ${req.body.old_email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		const { message } = await emailResetToken(req);
		logger.info(`Email update request token send successfully to user ${req.body.old_email.replace(/(.{2}).+(@.+)/, "$1***$2")}`);
		return res.status(HttpStatusCode.Ok).send({
			message,
		})
	})
)

/********************************
 * * Send Sms with verification code For Phone Verification
 * @param userId
 * @param new_phone
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/token-sms',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		logger.info(`Phone no. verification code request initiated by user ${req.body.userId}`);
		const { message } = await phoneResetToken(req);
		return res.status(HttpStatusCode.Ok).send({
			message,
		})
	})
)

/********************************
 * * Reset Phone Number
 * @param userId
 * @param new_email
 * @param token
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/reset-phone',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		logger.info(`Reset phone no. request initiated by user ${req.body.userId}`);
		const { message, user } = await resetPhone(req);
		return res.status(HttpStatusCode.Ok).send({
			message,
			user,
		})
	})
)

/********************************
 * * Email Verification
 * @param email
 * @returns message
 * @throws NotFoundError
 ********************************/
router.post(
	'/email-verification',
	ForgotPasswordValidator,
	wrap(async (req: Request, res: Response) => {	
		const { message } = await emailVerification(req);
		return res.send({
			message,
		})
	})
)

/********************************
 * * Verify email
 * @param token
 * @returns message
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/verify-email',
	VerifyEmailValidator,
	wrap(async (req: Request, res: Response) => {
		const { message , role} = await verifyEmail(req);
		return res.status(HttpStatusCode.Created).send({
			message,
			role,
		})
	})
)

/********************************
 * * Pre-register verification
 * @param email
 * @param phone
 * @returns message
 * @throws BadRequestError
 * @throws NotFoundError
 ********************************/
router.post(
	'/pre-register-verification',
	// PreRegisterValidator,
	wrap(async (req: Request, res: Response) => {
		const { email, phone } = req.body

		const conditions = [];
		if (phone) conditions.push({ phone });
		if (email) conditions.push({ email });

		// Ensure there is at least one condition in the where clause
		if (conditions.length === 0) throw new BadRequestError('Email or phone is required');

		const user = await User.findOne({
			where: {
				[Op.or]: conditions
			}
		});

		if (user) throw new ForbiddenError((email && phone ? 'Email or Phone' : email ? 'Email' : 'Phone') + ' already used')

		return res.status(HttpStatusCode.Ok).send()
	})
)

/********************************
 * * Register therapist
 * @returns therapist
 * @throws BadRequestError
 * @throws UnprocessableEntityError
 ********************************/
router.post(
	'/register',
	// TherapistRegisterValidator,
	wrap(async (req: Request, res: Response) => {
		const { page, token } = req.query;
		logger.info(`Therapist Register API called - Page: ${page}, Token: ${token ? 'Present' : 'Missing'}`);
		
		let user: any, userRegInfo: any, newUserToken: string | null = null;
		let wasCreated = false;
		let wasUpdated = false;
		let verificationEmailSent = false;

		if (!page || page === 'undefined' || page === 'null') throw new ForbiddenError('Page Name is required');
		if ((!token || token === 'undefined' || token === 'null') && (page !== 'create-account')) throw new ForbiddenError('User Token is required');

		if (token && token !== 'undefined' && token !== 'null') {
			// Find user
			user = await User.findOne({
				where: {
					userToken: token,
				}
			});

			if (page !== 'create-account') {
				if (!user) throw new ForbiddenError('Please Create an account first');
				else if (!user.emailVerifiedAt) throw new ForbiddenError('Please verify your email first');
			}

			if (user) {				
				if (user.role !== UserType.THERAPIST) return res.forbidden('Unauthorized Request. Only Therapists are allowed')

				// Find user registration info
				userRegInfo = await UserRegistrationInfo.findOne({
					where: {
						userId: user.id,
						pageName: page,
					}
				});

			}
		}

		switch (page) {
			case 'create-account': {
				const { email, password, first_name, last_name } = req.body;
				logger.info(`Attempting user creation`);

				const hash_email =  hashData(req.body.email);
				// Check if user already exists with the same email
				const userExist = await User.findOne({
					where: {
						email_hash: hash_email,
					}
				})
				if (userExist && (userExist.userToken !== token)) {
					return res.forbidden('An account with this email already exists', { accountExist: true });
				}

				let isNewEmail = user?.email !== email;
				const hashedPassword = await bcrypt.hash(password, 10);
				const transaction = await sequelize.transaction();

				try {
					if (user) {
						await user.update({
							firstname: first_name,
							lastname: last_name,
							email,
							email_hash:hash_email,
							password: hashedPassword,
							emailVerifiedAt: isNewEmail ? null : user.emailVerifiedAt,
						}, { transaction });
						wasUpdated = true;
						logger.info(`User updated by ID: ${user.id}`);
					} else {
						newUserToken = generateString(12);
						user = await User.create({
							firstname: first_name,
							lastname: last_name,
							email,
							email_hash: hash_email,
							password: hashedPassword,
							via: PLATFORM.WEB,
							role: UserType.THERAPIST,
							userToken: newUserToken,
						}, { transaction });
						wasCreated = true;
						logger.info(`New user created - ID: ${user.id}`);
					}
	
					if (!user.emailVerifiedAt) {
						const code = Math.floor(100000 + Math.random() * 900000);
						const expiration = dayjs().add(2, 'hours').unix()
						const token = encryptedB64(`${user.id}:${expiration}:${code}`)
	
						await user.update({
							passwordResetToken: token,
						}, { transaction });
	
						const therapistEmailData = RegisterTherapistEmail.compile({
							email: user.email,
							code: code.toString(),
							// url: `${process.env.FRONTEND_URL}/auth/verify-email?token=${token}&email=${user.email}`,
							user: user.toJSON(),
						})
	
						const mailData = {
							to: therapistEmailData.To,
							subject: 'Verify your email',
							html: therapistEmailData.HtmlBody,
						}
	
						await mail.sendMail(mailData, true);
						verificationEmailSent = true;
					}

					await transaction.commit();
				} catch (error) {
					console.log(error);
					logger.error(`Error during registration "create-account": ${error}`);
					await transaction.rollback();
					return res.forbidden('Something went wrong. Please try again.');
				}
				break;
			}

			case 'practice-info': {
				logger.info(`${page} information created/updated by User ID: ${user.id}`);
				const { business_address } = req.body;
				let coordinate = null;
				if (business_address?.lat && business_address?.lng) {
					coordinate= {
						type: 'Point',
						coordinates: [business_address.lat, business_address.lng],
					}
				}

				await user.update({
					address: business_address,
					coordinate,
				});

				if (userRegInfo) {
					await userRegInfo.update({
						payloadInfo: req.body,
					});
					wasUpdated = true;
				} else {
					await UserRegistrationInfo.create({
						userId: user?.id,
						pageName: page,
						payloadInfo: req.body,
					});
					wasCreated = true;
				}
				break;
			}

			case 'normal-office-hours': {
				const { activeTimes, timezone } = req.body;
				logger.info('activeTimes', activeTimes);
				if (timezone && timezone.trim()) {
					await user.update({ timeZone: timezone });
				}

				const existingEntries = await NormalOfficeHours.findAll({
					where: { userId: user?.id },
				});
				
				const newEntries = Object.keys(activeTimes).flatMap((day) =>
					activeTimes[day].map((at: { time: string; appointmentMethod: string; disabled: boolean }) => ({
						day,
						time: at.time,
						appointmentMethod: at.appointmentMethod,
						isDisabled: at.disabled,
					}))
				);
				
				// Step 1: Map existing DB records by `day-time` key
				const existingMap = new Map(
					existingEntries.map((entry) => [`${entry.workingDay}-${entry.availabilityHoursPlain}`, entry])
				);
				
				const entriesToUpdate: Promise<any>[] = [];
				const entriesToCreate: any[] = [];
				
				// Step 2: Loop through incoming client entries
				for (const entry of newEntries) {
					const key = `${entry.day}-${entry.time}`;
					const existing = existingMap.get(key);
				
					if (existing) {
						// Restore/update existing entry
						entriesToUpdate.push(
							existing.update({
								availabilityHours: splitAndGenerateTime(entry.time),
								availabilityHoursPlain: entry.time,
								appointmentMethod: entry.appointmentMethod,
								isDisabled: entry.isDisabled,
								deletedAt: null,
							})
						);
					} else {
						// Entry doesn't exist at all, create it
						entriesToCreate.push({
							userId: user?.id,
							workingDay: entry.day,
							availabilityHoursPlain: entry.time,
							availabilityHours: splitAndGenerateTime(entry.time),
							appointmentMethod: entry.appointmentMethod,
							isDisabled: entry.isDisabled,
						});
					}
				}
				
				// Step 3: Identify and soft-delete obsolete DB entries
				const newKeys = new Set(newEntries.map((e) => `${e.day}-${e.time}`));
				
				const entriesToSoftDelete = existingEntries.filter((entry) => {
					const key = `${entry.workingDay}-${entry.availabilityHoursPlain}`;
					return !newKeys.has(key) && entry.deletedAt === null;
				});
				
				// Step 4: Execute DB operations
				await Promise.all(entriesToUpdate);
				
				if (entriesToCreate.length > 0) {
					await NormalOfficeHours.bulkCreate(entriesToCreate);
				}
				
				if (entriesToSoftDelete.length > 0) {
					await Promise.all(
						entriesToSoftDelete.map((entry) =>
							entry.update({ deletedAt: dayjs().toDate() })
						)
					);
				}							

				// Step 8: Update User Registration Info
				if (userRegInfo) {
					await userRegInfo.update({ payloadInfo: req.body });
					wasUpdated = true;
				} else {
					await UserRegistrationInfo.create({
						userId: user?.id,
						pageName: page,
						payloadInfo: req.body
					});
					wasCreated = true;
				}
				logger.info(`Normal office hrs created/updated by User ID: ${user.id}`);
				break;
			}

			case 'waitlist-notifications': {
				logger.info(`${page} information created/updated by User ID: ${user.id}`);
				const { code: receivedCode, ...userPreferences } = req.body;

				if (receivedCode) {
					logger.info(`New Phone Number ${maskPhoneNumber(userPreferences.phone_number)} for notifications added by User ID: ${user.id}`);
					const [id, expiration, code] = decryptedB64(user.passwordResetToken).split(':');

					logger.info(`Validating Verification code for phone number ${maskPhoneNumber(userPreferences.phone_number)}`);
					if (dayjs().unix() > Number(expiration)) {
						throw new BadRequestError('Verification Token expired');
					}
				
					if (receivedCode !== code || id !== user.id.toString()) {
						throw new ConflictError('Code does not match');
					}
					logger.info(`Verification code matched for phone number ${maskPhoneNumber(userPreferences.phone_number)}`);
					await user.update({
						passwordResetToken: null,
					});
				}

				if (userRegInfo) {
					await userRegInfo.update({
						payloadInfo: userPreferences,
					});
					logger.info(`Updated User Registration Info for ${page} by User ID: ${user.id}`);
					wasUpdated = true;
				} else {
					await UserRegistrationInfo.create({
						userId: user?.id,
						pageName: page,
						payloadInfo: userPreferences,
					});
					logger.info(`Created User Registration Info for ${page} by User ID: ${user.id}`);
					wasCreated = true;
				}

				break;
			}

			case 'profile': {
				const { values, isRegComplete } = req.body;

				const { dob, user_profile } = values;

				await user.update({
					dob: dob || null,
					userProfile: user_profile || null,
				})

				const sanitizedPayload = { ...values };
				delete sanitizedPayload.password;
				delete sanitizedPayload.confirm_password;
				delete sanitizedPayload.email;
				
				if (userRegInfo) {
					await userRegInfo.update({
						payloadInfo: sanitizedPayload
					});
					wasUpdated = true;
				} else {
					await UserRegistrationInfo.create({
						userId: user?.id,
						pageName: page,
						payloadInfo: sanitizedPayload
					});
					wasCreated = true;
				}

				if (isRegComplete) {
					await user.update({
						isUnderReview: true,
						acceptedAt: null,
						rejectedAt: null,
					})
				
				const AdminDeleteEmailData = RegisterTherapistEmailSendToAdmin.compile({
					email: user.email,
					user: user.toJSON(),
				})

				const mailDataAdmin = {
					to: AdminDeleteEmailData.To,
					subject: 'New Therapist Profile Submitted for Review',
					html: AdminDeleteEmailData.HtmlBody,
				}

				await mail.sendMail(mailDataAdmin, true);
				const therapistEmailData = RegisterNewTherapistEmail.compile({
					email: user.email,
					user: user.toJSON(),
				})

				const mailData = {
					to: therapistEmailData.To,
					subject: 'Verify your email',
					html: therapistEmailData.HtmlBody,
				}

				await mail.sendMail(mailData, true);
			}
				logger.info(`Profile created/updated by User ID: ${user.id}`);
				break;
			}

			default: {
				if (userRegInfo) {	
					const existingPayload = userRegInfo?.payloadInfo || {};
					const incomingPayload = req.body;

					const hasChanged = JSON.stringify(existingPayload) !== JSON.stringify(incomingPayload);
					
					await userRegInfo.update({
						payloadInfo: req.body
					});
					wasUpdated = true;
					
					if (page === 'license') {					
					if (hasChanged) {						
						const admin = await User.findOne({ where: { role: UserType.SYSTEM } });
						if (admin) {
							const adminEmail = admin.email || '<EMAIL>';
							try {
								const adminEmailData = LicenseUpdationEmail.compile({
									email: adminEmail,
									subject: 'License Update Notification',
									therapist_name: user.firstname + ' ' + user.lastname,
									therapist_email: user.email,
								});

								await mail.sendMail({
									to: '<EMAIL>',
									subject: adminEmailData.Subject,
									html: adminEmailData.HtmlBody,
								});

								logger.info(`License Update Notify - Email sent to admin ID: ${admin.id}`);
							} catch (error) {
								logger.error(`License Update Notify - Email error - Admin ID: (${admin.id}): ${error}`);
							}
						}
					} else {
						logger.info(`License update skipped — no changes detected for user ID: ${user.id}`);
					}
					}

				} else {
					await UserRegistrationInfo.create({
						userId: user?.id,
						pageName: page,
						payloadInfo: req.body
					});
					wasCreated = true;
				}

				logger.info(`${page} information created/updated by User ID: ${user.id}`);
				break;
			}
		}

		if (newUserToken) return res.status(HttpStatusCode.Created).send({message: 'Your Account has been successfully created.', data: { userToken: newUserToken, verificationEmailSent }});
		else if (verificationEmailSent) return res.updated('Your Info has been updated successfully', { verificationEmailSent });
		else if (wasCreated) return res.status(HttpStatusCode.Created).send({message: 'Your Info has been saved successfully'});
		else if (wasUpdated) return res.status(HttpStatusCode.Created).send({ message: 'Your Info has been updated successfully'});
		else return res.status(HttpStatusCode.Created).created({message: 'Your Info has been saved successfully'});
	})
)

/********************************
 * * Register patient
 ********************************/
router.post(
	'/register/patient',
	wrap(async (req: Request, res: Response) => {
		await registerPatient(req);

		return res.status(HttpStatusCode.Created).send({
			message: 'Registration Successful!',
		})
	})
)

/********************************
 * * Get Profile
 * @returns therapist
 * @throws NotFoundError
 ********************************/
router.get(
	'/profile',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const user = await User.findByPk(req.user?.id, {
			attributes: {
				include: [
					[
						sequelize.literal(
							`case when "calendars".id is not null and "calendars"."type" = 'google' then true else false end`
						),
						'hasGoogleCalendar',
					],
				],
			},
			include: [
				{
					model: Calendar,
					attributes: [],
				},
			],
		})

		if (!user) throw new NotFoundError('User not found')

		return res.send({ data: user })
	})
)

export default router
