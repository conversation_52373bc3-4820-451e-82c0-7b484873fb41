import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import calendarController from '@/src/application/controllers/web/calendar.controller';
import { response } from '@/src/application/helpers/response.helper';
import sequelize from '@/src/configs/database.config';
import { Calendar, User } from '@/src/models';
import * as googleConfig from '@/src/configs/google.config';
import * as outlookConfig from '@/src/configs/outlook.config';
import axios from 'axios';
import { Op } from 'sequelize';
import * as userRepository from '@/src/application/repositories/user.repository';
import logger from '@/src/configs/logger.config';

// Setup express app with middleware
const app = express();
app.use(express.json());
app.use(response);

// Add auth middleware to bypass portal middleware authentication
const mockPortalMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Set up user for the request
  req.user = { 
    id: 'test-user-id',
    firstname: 'Test',
    lastname: 'User',
    email: '<EMAIL>',
    emailVerifiedAt: new Date(),
    role: 'THERAPIST'
  } as any;
  req.role = 'THERAPIST';
  req.headers.authorization = 'Bearer test-token';
  next();
};

// Mock the portal middleware
const portalMiddlewarePath = '@/src/application/middlewares/portal.middleware';
const originalPortalMiddleware = require(portalMiddlewarePath).default;
// require(portalMiddlewarePath).default = (req: Request, res: Response, next: NextFunction) => next();

// Mount the controller
// app.use('/calendar', calendarController);

const controllerPath = '@/src/application/controllers/web/calendar.controller';

describe('Web Calendar Controller Unit Test', function () {
  let sandbox: sinon.SinonSandbox;
  let userFindOneStub: sinon.SinonStub;
  let calendarFindOneStub: sinon.SinonStub;
  let calendarCreateStub: sinon.SinonStub;
  let calendarUpdateStub: sinon.SinonStub;
  let calendarDestroyStub: sinon.SinonStub;
  let getUserWithCalendarInfoStub: sinon.SinonStub;
  let getTokenFromCodeStub: sinon.SinonStub;
  let getUserProfileStub: sinon.SinonStub;
  let revokeAccessStub: sinon.SinonStub;
  let getAuthenticatedClientStub: sinon.SinonStub;
  let getProfileStub: sinon.SinonStub;
  let axiosPostStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  
  const dummyUser = {
    id: 'user-123',
    email: '<EMAIL>',
    userToken: 'test-token'
  };
  
  const dummyCalendarInfo = {
    id: 'calendar-123',
    type: 'google',
    email: '<EMAIL>',
    userId: 'user-123',
    credentials: {
      tokens: {
        access_token: 'access-token',
        refresh_token: 'refresh-token'
      },
      profile: {
        email: '<EMAIL>'
      }
    }
  };

  before(function() {
    require(portalMiddlewarePath).default = mockPortalMiddleware;
    app.use('/calendar', require(controllerPath).default);
  })
  
  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    // Stubs for User model
    userFindOneStub = sandbox.stub(User, 'findOne');
    
    // Stubs for Calendar model
    calendarFindOneStub = sandbox.stub(Calendar, 'findOne');
    calendarCreateStub = sandbox.stub(Calendar, 'create');
    calendarUpdateStub = sandbox.stub(Calendar.prototype, 'update');
    calendarDestroyStub = sandbox.stub(Calendar.prototype, 'destroy');
    
    // Stubs for repositories
    getUserWithCalendarInfoStub = sandbox.stub(userRepository, 'getUserWithCalendarInfo');
    
    // Stubs for Google config
    getTokenFromCodeStub = sandbox.stub(googleConfig, 'getTokenFromCode');
    getUserProfileStub = sandbox.stub(googleConfig, 'getUserProfile');
    revokeAccessStub = sandbox.stub(googleConfig, 'revokeAccess');
    
    // Stubs for Outlook config
    getAuthenticatedClientStub = sandbox.stub(outlookConfig, 'getAuthenticatedClient');
    getProfileStub = sandbox.stub(outlookConfig, 'getProfile');
    
    // Stub for axios
    axiosPostStub = sandbox.stub(axios, 'post');
    
    // Stub for logger
    loggerInfoStub = sandbox.stub(logger, 'info');
  });
  
  afterEach(function () {
    sandbox.restore();
    // Restore original middlewares
    require(portalMiddlewarePath).default = originalPortalMiddleware;
  });

  after(function() {
    // Clear require cache to prevent test pollution
    require.cache[require.resolve(controllerPath)] = undefined;
    require.cache[portalMiddlewarePath] = undefined;
    require(portalMiddlewarePath).default = originalPortalMiddleware;
  })
  
  describe('POST /calendar/google', function () {
    it('should sync Google Calendar successfully', async function () {
      // Setup stubs
      userFindOneStub.resolves(dummyUser);
      getTokenFromCodeStub.resolves({ access_token: 'access-token', refresh_token: 'refresh-token' });
      getUserProfileStub.resolves({ email: '<EMAIL>' });
      calendarFindOneStub.onFirstCall().resolves(null); // No existing calendar with this email
      calendarFindOneStub.onSecondCall().resolves(null); // User doesn't have a calendar yet
      calendarCreateStub.resolves(dummyCalendarInfo);
      getUserWithCalendarInfoStub.resolves({ ...dummyUser, calendars: [dummyCalendarInfo] });
      
      const requestBody = {
        code: 'auth-code',
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .post('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(getTokenFromCodeStub.calledOnce).to.be.true;
      expect(getUserProfileStub.calledOnce).to.be.true;
      expect(calendarFindOneStub.calledTwice).to.be.true;
      expect(calendarCreateStub.calledOnce).to.be.true;
      expect(getUserWithCalendarInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Google Calendar synced successfully');
      expect(response.body).to.have.property('userData');
    });
    
    it('should reject if code is missing', async function () {
      const requestBody = {
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .post('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'Code is required');
    });
    
    it('should reject if userToken is missing', async function () {
      const requestBody = {
        code: 'auth-code'
      };
      
      const response = await request(app)
        .post('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User Token is required');
    });
    
    it('should reject if user is not found', async function () {
      userFindOneStub.resolves(null);
      
      const requestBody = {
        code: 'auth-code',
        userToken: 'invalid-token'
      };
      
      const response = await request(app)
        .post('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User not found');
    });
    
    it('should reject if calendar with email already exists for another user', async function () {
      userFindOneStub.resolves(dummyUser);
      getTokenFromCodeStub.resolves({ access_token: 'access-token', refresh_token: 'refresh-token' });
      getUserProfileStub.resolves({ email: '<EMAIL>' });
      
      // Calendar with this email already exists for another user
      calendarFindOneStub.onFirstCall().resolves({
        ...dummyCalendarInfo,
        userId: 'different-user-id'
      });
      
      const requestBody = {
        code: 'auth-code',
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .post('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'Calendar with this email already exists. Please use a different email.');
    });
  });
  
  describe('DELETE /calendar/google', function () {
    it('should remove Google Calendar successfully', async function () {
      userFindOneStub.resolves(dummyUser);
      calendarFindOneStub.resolves({
        ...dummyCalendarInfo,
        destroy: calendarDestroyStub
      });
      revokeAccessStub.resolves();
      getUserWithCalendarInfoStub.resolves({ ...dummyUser, calendars: [] });
      
      const requestBody = {
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .delete('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Ok);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(calendarFindOneStub.calledOnce).to.be.true;
      expect(revokeAccessStub.calledOnce).to.be.true;
      expect(calendarDestroyStub.calledOnce).to.be.true;
      expect(getUserWithCalendarInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Google Calendar removed successfully.');
      expect(response.body).to.have.property('userData');
    });
    
    it('should reject if userToken is missing', async function () {
      const response = await request(app)
        .delete('/calendar/google')
        .send({})
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User Token is required');
    });
    
    it('should reject if user is not found', async function () {
      userFindOneStub.resolves(null);
      
      const requestBody = {
        userToken: 'invalid-token'
      };
      
      const response = await request(app)
        .delete('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User not found');
    });
    
    it('should reject if Google Calendar is not found', async function () {
      userFindOneStub.resolves(dummyUser);
      calendarFindOneStub.resolves(null);
      
      const requestBody = {
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .delete('/calendar/google')
        .send(requestBody)
        .expect(HttpStatusCode.NotFound);
      
      expect(response.body).to.have.property('message', 'Google Calendar not found');
    });
  });
  
  describe('POST /calendar/outlook', function () {
    it('should sync Outlook Calendar successfully', async function () {
      // Setup stubs
      userFindOneStub.resolves(dummyUser);
      axiosPostStub.resolves({
        data: {
          access_token: 'access-token',
          refresh_token: 'refresh-token',
          expires_in: 3600
        }
      });
      getAuthenticatedClientStub.resolves({});
      getProfileStub.resolves({ mail: '<EMAIL>' });
      calendarFindOneStub.onFirstCall().resolves(null); // No existing calendar with this email
      calendarFindOneStub.onSecondCall().resolves(null); // User doesn't have a calendar yet
      calendarCreateStub.resolves({
        ...dummyCalendarInfo,
        type: 'outlook',
        email: '<EMAIL>'
      });
      getUserWithCalendarInfoStub.resolves({ 
        ...dummyUser, 
        calendars: [{
          ...dummyCalendarInfo,
          type: 'outlook',
          email: '<EMAIL>'
        }] 
      });
      
      const requestBody = {
        authorizationCode: 'auth-code',
        codeVerifier: 'code-verifier',
        userToken: 'test-token',
        redirectPath: '/callback'
      };
      
      const response = await request(app)
        .post('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(axiosPostStub.calledOnce).to.be.true;
      expect(getAuthenticatedClientStub.calledOnce).to.be.true;
      expect(getProfileStub.calledOnce).to.be.true;
      expect(calendarFindOneStub.calledTwice).to.be.true;
      expect(calendarCreateStub.calledOnce).to.be.true;
      expect(getUserWithCalendarInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Outlook Calendar synced successfully');
      expect(response.body).to.have.property('userData');
    });
    
    it('should update existing Outlook Calendar', async function () {
      // Setup stubs
      userFindOneStub.resolves(dummyUser);
      axiosPostStub.resolves({
        data: {
          access_token: 'access-token',
          refresh_token: 'refresh-token',
          expires_in: 3600
        }
      });
      getAuthenticatedClientStub.resolves({});
      getProfileStub.resolves({ mail: '<EMAIL>' });
      calendarFindOneStub.onFirstCall().resolves(null); // No existing calendar with this email
      calendarFindOneStub.onSecondCall().resolves({
        ...dummyCalendarInfo,
        type: 'outlook',
        email: '<EMAIL>',
        update: calendarUpdateStub
      });
      getUserWithCalendarInfoStub.resolves({ 
        ...dummyUser, 
        calendars: [{
          ...dummyCalendarInfo,
          type: 'outlook',
          email: '<EMAIL>'
        }] 
      });
      
      const requestBody = {
        authorizationCode: 'auth-code',
        codeVerifier: 'code-verifier',
        userToken: 'test-token',
        redirectPath: '/callback'
      };
      
      const response = await request(app)
        .post('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(axiosPostStub.calledOnce).to.be.true;
      expect(getAuthenticatedClientStub.calledOnce).to.be.true;
      expect(getProfileStub.calledOnce).to.be.true;
      expect(calendarFindOneStub.calledTwice).to.be.true;
      expect(calendarUpdateStub.calledOnce).to.be.true;
      expect(getUserWithCalendarInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Outlook Calendar synced successfully');
      expect(response.body).to.have.property('userData');
    });
    
    it('should reject if required parameters are missing', async function () {
      const requestBody = {
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .post('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'Required parameters are missing');
    });
    
    it('should reject if user is not found', async function () {
      userFindOneStub.resolves(null);
      
      const requestBody = {
        authorizationCode: 'auth-code',
        codeVerifier: 'code-verifier',
        userToken: 'invalid-token',
        redirectPath: '/callback'
      };
      
      const response = await request(app)
        .post('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User not found');
    });
    
    it('should reject if calendar with email already exists for another user', async function () {
      userFindOneStub.resolves(dummyUser);
      axiosPostStub.resolves({
        data: {
          access_token: 'access-token',
          refresh_token: 'refresh-token',
          expires_in: 3600
        }
      });
      getAuthenticatedClientStub.resolves({});
      getProfileStub.resolves({ mail: '<EMAIL>' });
      
      // Calendar with this email already exists for another user
      calendarFindOneStub.onFirstCall().resolves({
        ...dummyCalendarInfo,
        type: 'outlook',
        email: '<EMAIL>',
        userId: 'different-user-id'
      });
      
      const requestBody = {
        authorizationCode: 'auth-code',
        codeVerifier: 'code-verifier',
        userToken: 'test-token',
        redirectPath: '/callback'
      };
      
      const response = await request(app)
        .post('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'Calendar with this email already exists. Please use a different email.');
    });
  });
  
  describe('DELETE /calendar/outlook', function () {
    it('should remove Outlook Calendar successfully', async function () {
      userFindOneStub.resolves(dummyUser);
      calendarFindOneStub.resolves({
        ...dummyCalendarInfo,
        type: 'outlook',
        email: '<EMAIL>',
        destroy: calendarDestroyStub
      });
      getUserWithCalendarInfoStub.resolves({ ...dummyUser, calendars: [] });
      
      const requestBody = {
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .delete('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Ok);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(calendarFindOneStub.calledOnce).to.be.true;
      expect(calendarDestroyStub.calledOnce).to.be.true;
      expect(getUserWithCalendarInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Outlook Calendar removed successfully.');
      expect(response.body).to.have.property('userData');
    });
    
    it('should reject if userToken is missing', async function () {
      const response = await request(app)
        .delete('/calendar/outlook')
        .send({})
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User Token is required');
    });
    
    it('should reject if user is not found', async function () {
      userFindOneStub.resolves(null);
      
      const requestBody = {
        userToken: 'invalid-token'
      };
      
      const response = await request(app)
        .delete('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.Forbidden);
      
      expect(response.body).to.have.property('message', 'User not found');
    });
    
    it('should reject if Outlook Calendar is not found', async function () {
      userFindOneStub.resolves(dummyUser);
      calendarFindOneStub.resolves(null);
      
      const requestBody = {
        userToken: 'test-token'
      };
      
      const response = await request(app)
        .delete('/calendar/outlook')
        .send(requestBody)
        .expect(HttpStatusCode.NotFound);
      
      expect(response.body).to.have.property('message', 'Outlook Calendar not found');
    });
  });
});
