import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: any;
      role?: string;
    }
  }
}

describe('Page Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockPageRepository: any;
  let mockPaginationHelper: any;
  let mockWrap: any;
  let mockPortalMiddleware: sinon.SinonStub;
  let mockPage: any;
  let pageController: any;

  // Mock data
  const mockUser = {
    id: 1,
    firstname: 'Test',
    lastname: 'User',
    email: '<EMAIL>',
    emailVerifiedAt: new Date(),
    role: 'admin',
    version: 1
  } as any;

  const mockPageData = {
    id: 1,
    code: 'TEST_PAGE',
    title: 'Test Page',
    info: 'Test page info',
    extra: 'Test extra',
    button: 'Next',
    buttonAction: 'next-page',
    nextPageId: 2,
    skipToPageId: null,
    type: 'form',
    category: 'onboarding',
    sortOrder: 1,
    questionnaireId: 1,
    questionnaireType: 'client',
    initialPage: false,
    required: true,
    isFollowedThrough: false,
    matcherInvolvement: false
  };

  const mockPaginatedResponse = {
    count: 10,
    rows: [mockPageData]
  };

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock the page repository functions
    mockPageRepository = {
      getAllPages: sandbox.stub(),
      getPageById: sandbox.stub(),
      createPage: sandbox.stub(),
      updatePage: sandbox.stub(),
      deletePage: sandbox.stub(),
      startPage: sandbox.stub(),
      matcherInvolvement: sandbox.stub()
    };

    // Mock pagination helper
    mockPaginationHelper = {
      paginatedData: sandbox.stub()
    };

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(error.statusCode || HttpStatusCode.InternalServerError).send({
            message: error.message,
            data: error.data
          });
        }
      };
    });

    // Mock PortalMiddleware
    mockPortalMiddleware = sandbox.stub().callsFake((req: Request, _res: Response, next: NextFunction) => {
      req.user = mockUser;
      req.role = mockUser.role;
      next();
    });

    // Mock Page model
    mockPage = {
      update: sandbox.stub().resolves()
    };

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./page.controller')];
    delete require.cache[require.resolve('../../repositories/page.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../models')];

    // Mock the modules
    require.cache[require.resolve('../../repositories/page.repository')] = {
      id: require.resolve('../../repositories/page.repository'),
      filename: require.resolve('../../repositories/page.repository'),
      loaded: true,
      exports: mockPageRepository
    } as NodeModule;

    require.cache[require.resolve('../../helpers/pagination.helper')] = {
      id: require.resolve('../../helpers/pagination.helper'),
      filename: require.resolve('../../helpers/pagination.helper'),
      loaded: true,
      exports: mockPaginationHelper
    } as NodeModule;

    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../middlewares/portal.middleware')] = {
      id: require.resolve('../../middlewares/portal.middleware'),
      filename: require.resolve('../../middlewares/portal.middleware'),
      loaded: true,
      exports: mockPortalMiddleware
    } as NodeModule;

    require.cache[require.resolve('../../../models')] = {
      id: require.resolve('../../../models'),
      filename: require.resolve('../../../models'),
      loaded: true,
      exports: { Page: mockPage }
    } as NodeModule;

    // Load the controller after mocking
    pageController = require('./page.controller').default;
    app.use('/pages', pageController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./page.controller')];
    delete require.cache[require.resolve('../../repositories/page.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../models')];
  });

  describe('GET /', function() {
    it('should get all pages with pagination', async function() {
      // Setup mocks
      mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 10,
          total: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });

      const response = await request(app)
        .get('/pages')
        .expect(200);

      expect(mockPageRepository.getAllPages.calledOnce).to.be.true;
      expect(mockPaginationHelper.paginatedData.calledOnce).to.be.true;
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
    });

    it('should handle errors when getting all pages', async function() {
      // Setup mock to throw error
      mockPageRepository.getAllPages.rejects(new Error('Database error'));

      const response = await request(app)
        .get('/pages')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database error');
    });
  });

  describe('GET /:id', function() {
    it('should get page by id successfully', async function() {
      // Setup mock
      mockPageRepository.getPageById.resolves(mockPageData);

      const response = await request(app)
        .get('/pages/1')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPageRepository.getPageById.calledWith('1')).to.be.true;
      expect(response.body).to.have.property('page');
      expect(response.body.page).to.deep.equal(mockPageData);
    });

    it('should handle errors when getting page by id', async function() {
      // Setup mock to throw error
      mockPageRepository.getPageById.rejects(new Error('Page not found'));

      const response = await request(app)
        .get('/pages/999')
        .expect(500);

      expect(response.body).to.have.property('message', 'Page not found');
    });
  });

  describe('POST /', function() {
    const createPageData = {
      code: 'NEW_PAGE',
      title: 'New Page',
      info: 'New page info',
      extra: 'New extra',
      button: 'Next',
      type: 'form',
      category: 'onboarding',
      questionnaireId: 1,
      questionnaireType: 'client',
      answers: [],
      buttonClickAction: 'next-page',
      required: true
    };

    it('should create page successfully', async function() {
      // Setup mock
      mockPageRepository.createPage.resolves(mockPageData);

      const response = await request(app)
        .post('/pages')
        .send(createPageData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPageRepository.createPage.calledOnce).to.be.true;
      expect(response.body).to.have.property('page');
      expect(response.body).to.have.property('message', 'Page created');
    });

    it('should handle errors when creating page', async function() {
      // Setup mock to throw error
      mockPageRepository.createPage.rejects(new Error('Page creation failed'));

      const response = await request(app)
        .post('/pages')
        .send(createPageData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Page creation failed');
    });
  });

  describe('POST /sort/order', function() {
    const sortOrderData = {
      data: [
        { id: 1, order: 1, initialPage: true },
        { id: 2, order: 2, initialPage: false },
        { id: 3, order: 3, initialPage: false }
      ]
    };

    it('should update page order successfully', async function() {
      // Setup mock
      mockPage.update.resolves();

      const response = await request(app)
        .post('/pages/sort/order')
        .send(sortOrderData)
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Page order updated.');
    });

    it('should set correct buttonAction for last page', async function() {
      const singlePageData = {
        data: [{ id: 1, order: 1, initialPage: true }]
      };

      mockPage.update.resolves();

      await request(app)
        .post('/pages/sort/order')
        .send(singlePageData)
        .expect(200);

      // Verify that the last page gets 'end' buttonAction
      expect(mockPage.update.calledWith(
        sinon.match({
          buttonAction: 'end',
          nextPageId: null
        }),
        sinon.match.any
      )).to.be.true;
    });

    it('should set correct buttonAction for non-last pages', async function() {
      mockPage.update.resolves();

      await request(app)
        .post('/pages/sort/order')
        .send(sortOrderData)
        .expect(200);

      // Verify that non-last pages get 'next-page' buttonAction
      expect(mockPage.update.calledWith(
        sinon.match({
          buttonAction: 'next-page',
          nextPageId: 2
        }),
        sinon.match.any
      )).to.be.true;
    });
  });

  describe('PUT /:id', function() {
    const updatePageData = {
      code: 'UPDATED_PAGE',
      title: 'Updated Page',
      info: 'Updated page info',
      extra: 'Updated extra',
      button: 'Next',
      type: 'form',
      category: 'onboarding',
      questionnaireId: 1,
      questionnaireType: 'client',
      answers: [],
      buttonClickAction: 'next-page',
      required: true
    };

    it('should update page successfully', async function() {
      // Setup mock
      const updatedPageData = { ...mockPageData, ...updatePageData };
      mockPageRepository.updatePage.resolves(updatedPageData);

      const response = await request(app)
        .put('/pages/1')
        .send(updatePageData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPageRepository.updatePage.calledOnce).to.be.true;
      expect(response.body).to.have.property('page');
      expect(response.body).to.have.property('message', 'Page updated');
    });

    it('should handle errors when updating page', async function() {
      // Setup mock to throw error
      mockPageRepository.updatePage.rejects(new Error('Page update failed'));

      const response = await request(app)
        .put('/pages/1')
        .send(updatePageData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Page update failed');
    });
  });

  describe('PUT /:id/start', function() {
    it('should set page as initial page successfully', async function() {
      // Setup mock
      const updatedPageData = { ...mockPageData, initialPage: true };
      mockPageRepository.startPage.resolves(updatedPageData);

      const response = await request(app)
        .put('/pages/1/start')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPageRepository.startPage.calledOnce).to.be.true;
      expect(response.body).to.have.property('page');
      expect(response.body).to.have.property('message', 'Page updated');
    });

    it('should handle errors when setting initial page', async function() {
      // Setup mock to throw error
      mockPageRepository.startPage.rejects(new Error('Start page failed'));

      const response = await request(app)
        .put('/pages/1/start')
        .expect(500);

      expect(response.body).to.have.property('message', 'Start page failed');
    });
  });

  describe('PUT /:id/matcher', function() {
    it('should update matcher involvement successfully', async function() {
      // Setup mock
      const updatedPageData = { ...mockPageData, matcherInvolvement: true };
      mockPageRepository.matcherInvolvement.resolves(updatedPageData);

      const response = await request(app)
        .put('/pages/1/matcher')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPageRepository.matcherInvolvement.calledOnce).to.be.true;
      expect(response.body).to.have.property('page');
      expect(response.body).to.have.property('message', 'Page updated');
    });

    it('should handle errors when updating matcher involvement', async function() {
      // Setup mock to throw error
      mockPageRepository.matcherInvolvement.rejects(new Error('Matcher update failed'));

      const response = await request(app)
        .put('/pages/1/matcher')
        .expect(500);

      expect(response.body).to.have.property('message', 'Matcher update failed');
    });
  });

  describe('DELETE /:id', function() {
    it('should delete page successfully', async function() {
      // Setup mock
      mockPageRepository.deletePage.resolves();

      const response = await request(app)
        .delete('/pages/1')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPageRepository.deletePage.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Page deleted');
    });

    it('should handle errors when deleting page', async function() {
      // Setup mock to throw error
      mockPageRepository.deletePage.rejects(new Error('Page deletion failed'));

      const response = await request(app)
        .delete('/pages/1')
        .expect(500);

      expect(response.body).to.have.property('message', 'Page deletion failed');
    });
  });

  describe('Middleware Integration Tests', function() {
    beforeEach(function() {
      // Reset the controller with different middleware behavior
      delete require.cache[require.resolve('./page.controller')];
    });

    it('should require authentication for protected routes', async function() {
      // Mock PortalMiddleware to reject unauthorized requests
      const unauthorizedMiddleware = sandbox.stub().callsFake((_req: Request, res: Response, _next: NextFunction) => {
        res.status(401).send({ message: 'No token provided' });
      });

      require.cache[require.resolve('../../middlewares/portal.middleware')] = {
        id: require.resolve('../../middlewares/portal.middleware'),
        filename: require.resolve('../../middlewares/portal.middleware'),
        loaded: true,
        exports: unauthorizedMiddleware
      } as NodeModule;

      const controller = require('./page.controller').default;
      const testApp = express();
      testApp.use(express.json());
      testApp.use('/pages', controller);

      // Test protected routes
      await request(testApp).get('/pages/1').expect(401);
      await request(testApp).post('/pages').send({}).expect(401);
      await request(testApp).put('/pages/1').send({}).expect(401);
      await request(testApp).delete('/pages/1').expect(401);
    });

    it('should allow access to unprotected routes without authentication', async function() {
      // Mock repository for unprotected route
      mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 1, perPage: 10, total: 10, lastPage: 1, nextPage: null, prevPage: null }
      });

      const controller = require('./page.controller').default;
      const testApp = express();
      testApp.use(express.json());
      testApp.use('/pages', controller);

      // Test unprotected route (GET /)
      await request(testApp).get('/pages').expect(200);
    });
  });

  describe('Error Handling Tests', function() {
    it('should handle database connection errors', async function() {
      mockPageRepository.getAllPages.rejects(new Error('Database connection failed'));

      const response = await request(app)
        .get('/pages')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database connection failed');
    });

    it('should handle malformed request data', async function() {
      const response = await request(app)
        .post('/pages/sort/order')
        .send({ invalidData: 'test' })
        .expect(500);

      // Should handle the error gracefully
      expect(response.body).to.have.property('message');
    });
  });

  describe('Query Parameter Tests', function() {
    it('should pass query parameters to getAllPages', async function() {
      mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 1, perPage: 10, total: 10, lastPage: 1, nextPage: null, prevPage: null }
      });

      await request(app)
        .get('/pages?category=onboarding&type=form&search=test&exclude=1,2')
        .expect(200);

      // Verify that the request object with query parameters was passed
      expect(mockPageRepository.getAllPages.calledOnce).to.be.true;
      const calledRequest = mockPageRepository.getAllPages.getCall(0).args[0];
      expect(calledRequest.query).to.have.property('category', 'onboarding');
      expect(calledRequest.query).to.have.property('type', 'form');
      expect(calledRequest.query).to.have.property('search', 'test');
      expect(calledRequest.query).to.have.property('exclude', '1,2');
    });

    it('should handle pagination parameters', async function() {
      mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 2, perPage: 5, total: 10, lastPage: 2, nextPage: null, prevPage: 1 }
      });

      await request(app)
        .get('/pages?page=2&perPage=5')
        .expect(200);

      expect(mockPaginationHelper.paginatedData.calledOnce).to.be.true;
      const calledRequest = mockPaginationHelper.paginatedData.getCall(0).args[1];
      expect(calledRequest.query).to.have.property('page', '2');
      expect(calledRequest.query).to.have.property('perPage', '5');
    });
  });

  describe('Business Logic Tests', function() {
    describe('Page Sort Order Logic', function() {
      it('should handle empty data array gracefully', async function() {
        const emptyData = { data: [] };

        const response = await request(app)
          .post('/pages/sort/order')
          .send(emptyData)
          .expect(200);

        expect(response.body).to.have.property('message', 'Page order updated.');
        expect(mockPage.update.called).to.be.false;
      });

      it('should correctly set nextPageId for sequential pages', async function() {
        const sequentialData = {
          data: [
            { id: 1, order: 1, initialPage: true },
            { id: 2, order: 2, initialPage: false },
            { id: 3, order: 3, initialPage: false }
          ]
        };

        mockPage.update.resolves();

        await request(app)
          .post('/pages/sort/order')
          .send(sequentialData)
          .expect(200);

        // Check first page
        expect(mockPage.update.calledWith(
          sinon.match({
            sortOrder: 1,
            initialPage: true,
            buttonAction: 'next-page',
            nextPageId: 2
          }),
          { where: { id: 1 } }
        )).to.be.true;

        // Check middle page
        expect(mockPage.update.calledWith(
          sinon.match({
            sortOrder: 2,
            initialPage: false,
            buttonAction: 'next-page',
            nextPageId: 3
          }),
          { where: { id: 2 } }
        )).to.be.true;

        // Check last page
        expect(mockPage.update.calledWith(
          sinon.match({
            sortOrder: 3,
            initialPage: false,
            buttonAction: 'end',
            nextPageId: null
          }),
          { where: { id: 3 } }
        )).to.be.true;
      });
    });

    describe('Page Creation Logic', function() {
      it('should handle page creation with answers', async function() {
        const pageDataWithAnswers = {
          code: 'PAGE_WITH_ANSWERS',
          title: 'Page with Answers',
          info: 'Page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [
            { id: 1, conditions: {}, answerGroup: 'group1', position: 1 },
            { id: 2, conditions: {}, answerGroup: 'group1', position: 2 }
          ],
          buttonClickAction: 'next-page'
        };

        mockPageRepository.createPage.resolves(mockPageData);

        const response = await request(app)
          .post('/pages')
          .send(pageDataWithAnswers)
          .expect(201);

        expect(mockPageRepository.createPage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
        expect(response.body).to.have.property('message', 'Page created');
      });

      it('should handle page creation with different button actions', async function() {
        const pageDataWithRegisterAction = {
          code: 'REGISTER_PAGE',
          title: 'Register Page',
          info: 'Register page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [],
          buttonClickAction: 'register'
        };

        mockPageRepository.createPage.resolves(mockPageData);

        await request(app)
          .post('/pages')
          .send(pageDataWithRegisterAction)
          .expect(201);

        expect(mockPageRepository.createPage.calledOnce).to.be.true;
      });
    });

    describe('Page Update Logic', function() {
      it('should handle page update with removed answers', async function() {
        const updateDataWithRemovedAnswers = {
          code: 'UPDATED_PAGE',
          title: 'Updated Page',
          info: 'Updated info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [{ id: 1, conditions: {} }],
          removedAnswers: [2, 3],
          buttonClickAction: 'next-page'
        };

        mockPageRepository.updatePage.resolves(mockPageData);

        const response = await request(app)
          .put('/pages/1')
          .send(updateDataWithRemovedAnswers)
          .expect(201);

        expect(mockPageRepository.updatePage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
        expect(response.body).to.have.property('message', 'Page updated');
      });

      it('should handle page update with skip logic', async function() {
        const updateDataWithSkip = {
          code: 'SKIP_PAGE',
          title: 'Skip Page',
          info: 'Skip page info',
          type: 'form',
          category: 'onboarding',
          skipToPageId: 5,
          buttonClickAction: 'next-page'
        };

        mockPageRepository.updatePage.resolves({ ...mockPageData, skipToPageId: 5 });

        const response = await request(app)
          .put('/pages/1')
          .send(updateDataWithSkip)
          .expect(201);

        expect(mockPageRepository.updatePage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });
    });
  });

  describe('Response Format Tests', function() {
    it('should return correct response format for GET /', async function() {
      mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
      const expectedPaginatedData = {
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 10,
          total: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      };
      mockPaginationHelper.paginatedData.returns(expectedPaginatedData);

      const response = await request(app)
        .get('/pages')
        .expect(200);

      expect(response.body).to.deep.equal(expectedPaginatedData);
    });

    it('should return correct response format for GET /:id', async function() {
      mockPageRepository.getPageById.resolves(mockPageData);

      const response = await request(app)
        .get('/pages/1')
        .expect(200);

      expect(response.body).to.have.property('page');
      expect(response.body.page).to.deep.equal(mockPageData);
    });

    it('should return correct response format for POST /', async function() {
      mockPageRepository.createPage.resolves(mockPageData);

      const response = await request(app)
        .post('/pages')
        .send({
          code: 'TEST',
          title: 'Test',
          type: 'form',
          category: 'test',
          answers: []
        })
        .expect(201);

      expect(response.body).to.have.property('page');
      expect(response.body).to.have.property('message', 'Page created');
      expect(response.body.page).to.deep.equal(mockPageData);
    });

    it('should return correct response format for DELETE /:id', async function() {
      mockPageRepository.deletePage.resolves();

      const response = await request(app)
        .delete('/pages/1')
        .expect(200);

      expect(response.body).to.have.property('message', 'Page deleted');
      expect(response.body).to.not.have.property('page');
    });
  });

  describe('Advanced Edge Cases', function() {
    describe('Page Creation Edge Cases', function() {
      it('should handle page creation with duplicate code and category', async function() {
        // Setup mock to throw BadRequestError
        const duplicateError = new Error('Page with same category and code already exists');
        duplicateError.name = 'BadRequestError';
        mockPageRepository.createPage.rejects(duplicateError);

        const duplicatePageData = {
          code: 'EXISTING_PAGE',
          title: 'Duplicate Page',
          info: 'Duplicate page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [],
          buttonClickAction: 'next-page'
        };

        const response = await request(app)
          .post('/pages')
          .send(duplicatePageData)
          .expect(500);

        expect(response.body).to.have.property('message', 'Page with same category and code already exists');
      });

      it('should handle page creation with follow-through answers', async function() {
        const pageDataWithFollowThrough = {
          code: 'FOLLOW_THROUGH_PAGE',
          title: 'Follow Through Page',
          info: 'Follow through page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [
            {
              id: 1,
              conditions: {},
              answerGroup: 'group1',
              position: 1,
              followedBy: 2,
              order: 1
            },
            {
              id: 2,
              conditions: {},
              answerGroup: 'group1',
              position: 2,
              order: 2
            }
          ],
          buttonClickAction: 'next-page'
        };

        mockPageRepository.createPage.resolves({
          ...mockPageData,
          isFollowedThrough: true
        });

        const response = await request(app)
          .post('/pages')
          .send(pageDataWithFollowThrough)
          .expect(201);

        expect(mockPageRepository.createPage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
        expect(response.body).to.have.property('message', 'Page created');
      });

      it('should handle page creation with register button action', async function() {
        const registerPageData = {
          code: 'REGISTER_PAGE',
          title: 'Register Page',
          info: 'Register page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [],
          buttonClickAction: 'register'
        };

        mockPageRepository.createPage.resolves({
          ...mockPageData,
          buttonAction: 'register',
          nextPageId: null
        });

        const response = await request(app)
          .post('/pages')
          .send(registerPageData)
          .expect(201);

        expect(mockPageRepository.createPage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });

      it('should handle page creation with end button action', async function() {
        const endPageData = {
          code: 'END_PAGE',
          title: 'End Page',
          info: 'End page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [],
          buttonClickAction: 'end'
        };

        mockPageRepository.createPage.resolves({
          ...mockPageData,
          buttonAction: 'end',
          nextPageId: null
        });

        const response = await request(app)
          .post('/pages')
          .send(endPageData)
          .expect(201);

        expect(mockPageRepository.createPage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });
    });

    describe('Page Update Edge Cases', function() {
      it('should handle page update with value type and existing pageAnswerId', async function() {
        const updateDataWithValueType = {
          code: 'VALUE_PAGE',
          title: 'Value Page',
          info: 'Value page info',
          type: 'value',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [
            {
              id: 1,
              pageAnswerId: 10,
              conditions: { min: 1, max: 10 },
              answerGroup: 'group1',
              position: 1,
              order: 1
            }
          ],
          removedAnswers: [],
          buttonClickAction: 'next-page'
        };

        mockPageRepository.updatePage.resolves({
          ...mockPageData,
          type: 'value'
        });

        const response = await request(app)
          .put('/pages/1')
          .send(updateDataWithValueType)
          .expect(201);

        expect(mockPageRepository.updatePage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });

      it('should handle page update with selectable type and new answers', async function() {
        const updateDataWithSelectableType = {
          code: 'SELECTABLE_PAGE',
          title: 'Selectable Page',
          info: 'Selectable page info',
          type: 'selectable',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [
            {
              id: 1,
              conditions: {},
              answerGroup: 'group1',
              position: 'left',
              order: 1
            },
            {
              id: 2,
              conditions: {},
              answerGroup: 'group1',
              position: 'right',
              order: 2
            }
          ],
          removedAnswers: [],
          buttonClickAction: 'next-page'
        };

        mockPageRepository.updatePage.resolves({
          ...mockPageData,
          type: 'selectable'
        });

        const response = await request(app)
          .put('/pages/1')
          .send(updateDataWithSelectableType)
          .expect(201);

        expect(mockPageRepository.updatePage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });

      it('should handle page update with removed answers', async function() {
        const updateDataWithRemovedAnswers = {
          code: 'UPDATED_PAGE',
          title: 'Updated Page',
          info: 'Updated page info',
          type: 'form',
          category: 'onboarding',
          questionnaireId: 1,
          answers: [
            {
              id: 1,
              conditions: {},
              answerGroup: 'group1',
              position: 1,
              order: 1
            }
          ],
          removedAnswers: [2, 3, 4],
          buttonClickAction: 'next-page'
        };

        mockPageRepository.updatePage.resolves(mockPageData);

        const response = await request(app)
          .put('/pages/1')
          .send(updateDataWithRemovedAnswers)
          .expect(201);

        expect(mockPageRepository.updatePage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });
    });

    describe('Page Retrieval Edge Cases', function() {
      it('should handle getPageById with follow-through data', async function() {
        const pageWithFollowThrough = {
          ...mockPageData,
          isFollowedThrough: true,
          dataValues: {
            ...mockPageData,
            followThroughAnswers: {
              count: 2,
              rows: [
                {
                  followingBy: 1,
                  followingTo: 2,
                  followingToAnswer: { id: 2, answer: 'Answer 2' },
                  followingByAnswer: { id: 1, answer: 'Answer 1' }
                }
              ]
            }
          }
        };

        mockPageRepository.getPageById.resolves(pageWithFollowThrough);

        const response = await request(app)
          .get('/pages/1')
          .expect(200);

        expect(mockPageRepository.getPageById.calledWith('1')).to.be.true;
        expect(response.body).to.have.property('page');
      });

      it('should handle getPageById with complex answer ordering', async function() {
        const pageWithOrderedAnswers = {
          ...mockPageData,
          answers: [
            {
              id: 1,
              info: 'Answer 1',
              answer: 'First Answer',
              conditions: {},
              answerGroup: 'group1',
              pageAnswerId: 10,
              position: 'left',
              order: 1,
              page: { id: 5, title: 'Source Page' }
            },
            {
              id: 2,
              info: 'Answer 2',
              answer: 'Second Answer',
              conditions: {},
              answerGroup: 'group1',
              pageAnswerId: 11,
              position: 'right',
              order: 2,
              page: null
            }
          ]
        };

        mockPageRepository.getPageById.resolves(pageWithOrderedAnswers);

        const response = await request(app)
          .get('/pages/1')
          .expect(200);

        expect(response.body).to.have.property('page');
        expect(response.body.page.answers).to.be.an('array');
      });
    });

    describe('Sort Order Edge Cases', function() {
      it('should handle sort order with mixed initialPage values', async function() {
        const mixedInitialPageData = {
          data: [
            { id: 1, order: 1, initialPage: false },
            { id: 2, order: 2, initialPage: true },
            { id: 3, order: 3, initialPage: false }
          ]
        };

        mockPage.update.resolves();

        const response = await request(app)
          .post('/pages/sort/order')
          .send(mixedInitialPageData)
          .expect(200);

        expect(response.body).to.have.property('message', 'Page order updated.');

        // Verify middle page (index 1) gets correct settings
        expect(mockPage.update.calledWith(
          sinon.match({
            sortOrder: 2,
            initialPage: true,
            buttonAction: 'next-page',
            nextPageId: 3
          }),
          { where: { id: 2 } }
        )).to.be.true;
      });
    });

    describe('Matcher Involvement Edge Cases', function() {
      it('should toggle matcher involvement from false to true', async function() {
        const updatedPageWithMatcherTrue = {
          ...mockPageData,
          matcherInvolvement: true
        };

        mockPageRepository.matcherInvolvement.resolves(updatedPageWithMatcherTrue);

        const response = await request(app)
          .put('/pages/1/matcher')
          .expect(200);

        expect(mockPageRepository.matcherInvolvement.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
        expect(response.body).to.have.property('message', 'Page updated');
      });

      it('should toggle matcher involvement from true to false', async function() {
        const updatedPageWithMatcherFalse = {
          ...mockPageData,
          matcherInvolvement: false
        };

        mockPageRepository.matcherInvolvement.resolves(updatedPageWithMatcherFalse);

        const response = await request(app)
          .put('/pages/1/matcher')
          .expect(200);

        expect(mockPageRepository.matcherInvolvement.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });
    });

    describe('Start Page Edge Cases', function() {
      it('should handle setting initial page in different category', async function() {
        const pageInDifferentCategory = {
          ...mockPageData,
          category: 'assessment',
          initialPage: false
        };

        const updatedInitialPage = {
          ...pageInDifferentCategory,
          initialPage: true
        };

        mockPageRepository.startPage.resolves(updatedInitialPage);

        const response = await request(app)
          .put('/pages/1/start')
          .expect(200);

        expect(mockPageRepository.startPage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
        expect(response.body).to.have.property('message', 'Page updated');
      });

      it('should handle start page when page is already initial', async function() {
        const alreadyInitialPage = {
          ...mockPageData,
          initialPage: true
        };

        mockPageRepository.startPage.resolves(alreadyInitialPage);

        const response = await request(app)
          .put('/pages/1/start')
          .expect(200);

        expect(mockPageRepository.startPage.calledOnce).to.be.true;
        expect(response.body).to.have.property('page');
      });
    });
  });

  describe('Validation and Security Tests', function() {
    describe('Input Validation', function() {
      it('should handle missing required fields in page creation', async function() {
        const incompletePageData = {
          title: 'Incomplete Page'
          // Missing required fields like code, type, category
        };

        mockPageRepository.createPage.rejects(new Error('Validation error: Missing required fields'));

        const response = await request(app)
          .post('/pages')
          .send(incompletePageData)
          .expect(500);

        expect(response.body).to.have.property('message');
      });

      it('should handle invalid page ID format', async function() {
        mockPageRepository.getPageById.rejects(new Error('Page not found'));

        const response = await request(app)
          .get('/pages/invalid-id')
          .expect(500);

        expect(response.body).to.have.property('message', 'Page not found');
      });

      it('should handle very large sort order data', async function() {
        const largeSortData = {
          data: Array.from({ length: 100 }, (_, i) => ({
            id: i + 1,
            order: i + 1,
            initialPage: i === 0
          }))
        };

        mockPage.update.resolves();

        const response = await request(app)
          .post('/pages/sort/order')
          .send(largeSortData)
          .expect(200);

        expect(response.body).to.have.property('message', 'Page order updated.');
        expect(mockPage.update.callCount).to.equal(100);
      });
    });

    describe('SQL Injection Prevention', function() {
      it('should handle malicious input in search query', async function() {
        mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
        mockPaginationHelper.paginatedData.returns({
          data: mockPaginatedResponse.rows,
          meta: { currentPage: 1, perPage: 10, total: 10, lastPage: 1, nextPage: null, prevPage: null }
        });

        const maliciousSearch = "'; DROP TABLE pages; --";

        await request(app)
          .get(`/pages?search=${encodeURIComponent(maliciousSearch)}`)
          .expect(200);

        expect(mockPageRepository.getAllPages.calledOnce).to.be.true;
        const calledRequest = mockPageRepository.getAllPages.getCall(0).args[0];
        expect(calledRequest.query.search).to.equal(maliciousSearch);
      });

      it('should handle malicious input in exclude parameter', async function() {
        mockPageRepository.getAllPages.resolves(mockPaginatedResponse);
        mockPaginationHelper.paginatedData.returns({
          data: mockPaginatedResponse.rows,
          meta: { currentPage: 1, perPage: 10, total: 10, lastPage: 1, nextPage: null, prevPage: null }
        });

        const maliciousExclude = "1; DROP TABLE pages; --";

        await request(app)
          .get(`/pages?exclude=${encodeURIComponent(maliciousExclude)}`)
          .expect(200);

        expect(mockPageRepository.getAllPages.calledOnce).to.be.true;
      });
    });
  });

  describe('Performance and Load Tests', function() {
    describe('Concurrent Operations', function() {
      it('should handle multiple simultaneous page retrievals', async function() {
        mockPageRepository.getPageById.resolves(mockPageData);

        const promises = Array.from({ length: 10 }, (_, i) =>
          request(app).get(`/pages/${i + 1}`)
        );

        const responses = await Promise.all(promises);

        responses.forEach(response => {
          expect(response.status).to.equal(200);
          expect(response.body).to.have.property('page');
        });

        expect(mockPageRepository.getPageById.callCount).to.equal(10);
      });

      it('should handle multiple simultaneous page updates', async function() {
        mockPageRepository.updatePage.resolves(mockPageData);

        const updateData = {
          code: 'CONCURRENT_UPDATE',
          title: 'Concurrent Update',
          info: 'Concurrent update info',
          type: 'form',
          category: 'test',
          answers: [],
          removedAnswers: [],
          buttonClickAction: 'next-page'
        };

        const promises = Array.from({ length: 5 }, (_, i) =>
          request(app)
            .put(`/pages/${i + 1}`)
            .send({ ...updateData, title: `${updateData.title} ${i + 1}` })
        );

        const responses = await Promise.all(promises);

        responses.forEach(response => {
          expect(response.status).to.equal(201);
          expect(response.body).to.have.property('page');
        });

        expect(mockPageRepository.updatePage.callCount).to.equal(5);
      });
    });

    describe('Large Data Handling', function() {
      it('should handle page with many answers', async function() {
        const pageWithManyAnswers = {
          code: 'MANY_ANSWERS_PAGE',
          title: 'Page with Many Answers',
          info: 'Page with many answers info',
          type: 'form',
          category: 'test',
          questionnaireId: 1,
          answers: Array.from({ length: 50 }, (_, i) => ({
            id: i + 1,
            conditions: { value: i },
            answerGroup: `group${Math.floor(i / 10) + 1}`,
            position: i % 2 === 0 ? 'left' : 'right',
            order: i + 1
          })),
          buttonClickAction: 'next-page'
        };

        mockPageRepository.createPage.resolves({
          ...mockPageData,
          answers: pageWithManyAnswers.answers
        });

        const response = await request(app)
          .post('/pages')
          .send(pageWithManyAnswers)
          .expect(201);

        expect(response.body).to.have.property('page');
        expect(mockPageRepository.createPage.calledOnce).to.be.true;
      });

      it('should handle pagination with large datasets', async function() {
        const largePaginatedResponse = {
          count: 1000,
          rows: Array.from({ length: 100 }, (_, i) => ({
            ...mockPageData,
            id: i + 1,
            code: `PAGE_${i + 1}`,
            title: `Page ${i + 1}`
          }))
        };

        mockPageRepository.getAllPages.resolves(largePaginatedResponse);
        mockPaginationHelper.paginatedData.returns({
          data: largePaginatedResponse.rows,
          meta: {
            currentPage: 1,
            perPage: 100,
            total: 1000,
            lastPage: 10,
            nextPage: 2,
            prevPage: null
          }
        });

        const response = await request(app)
          .get('/pages?perPage=100')
          .expect(200);

        expect(response.body).to.have.property('data');
        expect(response.body.data).to.have.length(100);
        expect(response.body.meta.total).to.equal(1000);
      });
    });
  });

  describe('Integration Tests', function() {
    describe('Workflow Integration', function() {
      it('should handle complete page lifecycle', async function() {
        // Create page
        mockPageRepository.createPage.resolves(mockPageData);

        const createResponse = await request(app)
          .post('/pages')
          .send({
            code: 'LIFECYCLE_PAGE',
            title: 'Lifecycle Page',
            info: 'Lifecycle page info',
            type: 'form',
            category: 'test',
            questionnaireId: 1,
            answers: [],
            buttonClickAction: 'next-page'
          })
          .expect(201);

        expect(createResponse.body).to.have.property('page');

        // Get page
        mockPageRepository.getPageById.resolves(mockPageData);

        const getResponse = await request(app)
          .get('/pages/1')
          .expect(200);

        expect(getResponse.body).to.have.property('page');

        // Update page
        mockPageRepository.updatePage.resolves({
          ...mockPageData,
          title: 'Updated Lifecycle Page'
        });

        const updateResponse = await request(app)
          .put('/pages/1')
          .send({
            code: 'LIFECYCLE_PAGE',
            title: 'Updated Lifecycle Page',
            info: 'Updated lifecycle page info',
            type: 'form',
            category: 'test',
            answers: [],
            removedAnswers: [],
            buttonClickAction: 'next-page'
          })
          .expect(201);

        expect(updateResponse.body).to.have.property('page');

        // Set as initial page
        mockPageRepository.startPage.resolves({
          ...mockPageData,
          initialPage: true
        });

        const startResponse = await request(app)
          .put('/pages/1/start')
          .expect(200);

        expect(startResponse.body).to.have.property('page');

        // Toggle matcher involvement
        mockPageRepository.matcherInvolvement.resolves({
          ...mockPageData,
          matcherInvolvement: true
        });

        const matcherResponse = await request(app)
          .put('/pages/1/matcher')
          .expect(200);

        expect(matcherResponse.body).to.have.property('page');

        // Delete page
        mockPageRepository.deletePage.resolves();

        const deleteResponse = await request(app)
          .delete('/pages/1')
          .expect(200);

        expect(deleteResponse.body).to.have.property('message', 'Page deleted');
      });

      it('should handle page ordering workflow', async function() {
        // Create multiple pages
        const pages = [
          { id: 1, code: 'PAGE_1', title: 'First Page' },
          { id: 2, code: 'PAGE_2', title: 'Second Page' },
          { id: 3, code: 'PAGE_3', title: 'Third Page' }
        ];

        // Get all pages
        mockPageRepository.getAllPages.resolves({
          count: 3,
          rows: pages.map(p => ({ ...mockPageData, ...p }))
        });
        mockPaginationHelper.paginatedData.returns({
          data: pages.map(p => ({ ...mockPageData, ...p })),
          meta: { currentPage: 1, perPage: 10, total: 3, lastPage: 1, nextPage: null, prevPage: null }
        });

        const getAllResponse = await request(app)
          .get('/pages')
          .expect(200);

        expect(getAllResponse.body.data).to.have.length(3);

        // Reorder pages
        mockPage.update.resolves();

        const reorderResponse = await request(app)
          .post('/pages/sort/order')
          .send({
            data: [
              { id: 3, order: 1, initialPage: true },
              { id: 1, order: 2, initialPage: false },
              { id: 2, order: 3, initialPage: false }
            ]
          })
          .expect(200);

        expect(reorderResponse.body).to.have.property('message', 'Page order updated.');
        expect(mockPage.update.callCount).to.equal(3);
      });
    });

    describe('Error Recovery Integration', function() {

      it('should handle repository timeout errors', async function() {
        const timeoutError = new Error('Request timeout');
        timeoutError.name = 'TimeoutError';
        mockPageRepository.getAllPages.rejects(timeoutError);

        const response = await request(app)
          .get('/pages')
          .expect(500);

        expect(response.body).to.have.property('message', 'Request timeout');
      });
    });
  });
});
