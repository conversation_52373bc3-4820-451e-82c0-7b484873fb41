import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import { HttpStatusCode } from 'axios'
import Questionnaire from '@/src/models/questionnaire.model'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import CacheMiddleware from '@/src/configs/cache.config'
import {
	createQuestionnaire,
	deleteQuestionnaire,
	getQuestionnaireById,
	getQuestionnaires,
	updateQuestionnaire,
} from '@/src/application/repositories/questionnaire.repository'

const router = express.Router()

/********************************
 * * Get all questionnaires
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const questionnaires = await getQuestionnaires(req)
		res.send(paginatedData(questionnaires, req))
	})
)

/********************************
 * * Get questionnaire by id
 * * @param id
 * * @returns Questionnaire
 * * @throws QuestionnaireNotFound
 * *******************************/
router.get(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const questionnaire = await getQuestionnaireById(req.params.id)
		res.send({ questionnaire })
	})
)

/********************************
 * * Create questionnaire
 * * @returns Questionnaire
 * *******************************/
router.post(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const questionnaire = await createQuestionnaire(req)
		res.status(HttpStatusCode.Created).send({
			questionnaire,
			message: 'Questionnaire created',
		})
	})
)

/********************************
 * * Sort questionnaire
 * * @returns success message
 * *******************************/
router.post(
	'/sort',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { category, ...rest } = req.body
		const updates = [] as any

		Object.keys(rest).forEach(async (key) => {
			updates.push(
				Questionnaire.update(
					{
						[`${category}Sort`]: rest[key],
					},
					{
						where: {
							id: key,
						},
					}
				)
			)
		})

		await Promise.all(updates)

		res.status(HttpStatusCode.Created).send({
			message: 'Questionnaire created',
		})
	})
)

/********************************
 * * Update questionnaire
 * * @param id
 * * @returns Questionnaire
 * * @throws QuestionnaireNotFound
 * *******************************/
router.put(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const questionnaire = await updateQuestionnaire(req)
		res.status(HttpStatusCode.Created).send({
			questionnaire,
			message: 'Questionnaire updated',
		})
	})
)

/********************************
 * * Delete questionnaire
 * * @param id
 * * @returns Questionnaire
 * * @throws QuestionnaireNotFound
 * *******************************/
router.delete(
	'/:id',
	PortalMiddleware,
	CacheMiddleware('questionnaires'),
	wrap(async (req: Request, res: Response) => {
		await deleteQuestionnaire(req.params.id)
		res.send({ message: 'Questionnaire deleted' })
	})
)

export default router
