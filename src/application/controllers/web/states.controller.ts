import express, { Request, Response } from 'express';
import { wrap } from '@/src/application/helpers';
import { Sequelize } from 'sequelize';
import { State } from '@/src/models'; // Assuming you have a State model defined

const router = express.Router();

/**
 * GET /states
 * Fetch abbrev and name from the states table
 */
router.get(
    '/states',
    wrap(async (req: Request, res: Response) => {
        try {
            const states = await State.findAll({
                attributes: ['name', 'abbrev'], // Fetch only name and abbrev
                order: [['name', 'ASC']], // Optional: Order by name
            });

            res.status(200).json(states);
        } catch (error) {
            console.error('Error fetching states:', error);
            res.status(500).json({ message: 'Internal server error' });
        }
    })
);

export default router;