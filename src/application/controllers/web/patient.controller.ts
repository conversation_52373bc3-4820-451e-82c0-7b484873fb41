import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { getPatients } from '@/src/application/repositories/patient.repository'
import { User,Appointments, PatientPayment,MinorPatient,PatientDeleteDeactivateReason,PatientDeleteDeactivateReasonsHistory } from '@/src/models'
import ExcelJS from 'exceljs';
import { Op } from 'sequelize'
import { UserRegistrationInfo} from '@/src/models' 
import logger from '@/src/configs/logger.config'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import { HttpStatusCode } from 'axios'
import {DELETION_REASON,PATIENT_DELETED,DELETION_REASON_DELETING,REASON_ID_REQUIRED,TRANSACTION_NOT_STARTED,PATIENT_NOT_FOUND,PATIENT_DEACTIVATED,PATIENT_DEACTIVATED_ERROR} from '@/src/application/controllers/api/constants'

dayjs.extend(timezone)

const router = express.Router()

/********************************
 * * Get all patients
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const patients = await getPatients({ req })
		res.send(paginatedData(patients, req))
	})
)


/********************************
* * Get all patients whose appointment is booked with specific therapist as per specified timezone
********************************/
router.get(
    '/appointments',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        const therapistId = req.query.therapistId || req.user?.id ;        

        if (!therapistId) {
            return res.status(400).send({ message: 'Therapist ID is required' });
        }
        logger.info(`Fetching appointments for therapist ID ${therapistId}`);

        // Fetch the timezone from UserRegistrationInfo
        const userRegInfo = await UserRegistrationInfo.findOne({
            where: {
                userId: therapistId,
                pageName: 'normal-office-hours',
            },
        });

        if (!userRegInfo) {
            return res.status(404).send({ message: 'User registration info not found' });
        }

        const payloadInfo = userRegInfo.payloadInfo as any;
        const timezone = payloadInfo?.timezone;
        if (!timezone) {
            return res.status(404).send({ message: 'Timezone not found in user registration info' });
        }

        logger.info(`Retrieved timezone (${timezone}) for therapist ${therapistId}`);


        const referenceDateTime = dayjs().tz(timezone);

        // Fetch all appointments for the therapist
        const appointments = await Appointments.findAll({
            where: {
                therapistId,
            },
            include: [
                {
                    model: User,
                    as: 'patient',
                    attributes: ['id', 'firstname', 'lastname', 'email', 'phone', 'dob', 'gender'],
                },
            ],
            attributes: ['id', 'appointmentDate', 'description', 'patientId', 'isMinor', 'minorId', 'cancelledAt', 'isBilled'],
        });

        logger.info(`Found ${appointments.length} appointments for therapist ${therapistId}`);

        const patientIds = appointments.map(appointment => appointment.patientId);
        const minorIds = appointments.filter(appointment => appointment.isMinor).map(appointment => appointment.minorId);

        const patients = await User.findAll({
            where: {
                id: {
                    [Op.in]: patientIds,
                },
            },
            attributes: ['id', 'firstname', 'lastname', 'email', 'phone', 'dob', 'gender', 'address', 'bio', 'userProfile'],
        });

        logger.info(`Retrieved ${patients.length} patients for therapist ${therapistId}`);
        const minors = await MinorPatient.findAll({
            where: {
                id: {
                    [Op.in]: minorIds,
                },
            },
            attributes: ['id', 'firstName', 'lastName'],
        });

        const appointmentsByPatient: { [key: string]: any[] } = {};
        appointments.forEach(appointment => {
            if (!appointmentsByPatient[appointment.patientId]) {
                appointmentsByPatient[appointment.patientId] = [];
            }
            appointmentsByPatient[appointment.patientId].push(appointment);
        });

        // For each patient, determine which appointments should be marked as first
        const firstAppointmentIds: Set<string> = new Set();
        Object.values(appointmentsByPatient).forEach(patientAppointments => {
            // Sort appointments chronologically temporary for initial appointment response to see which appointment will be first
            patientAppointments.sort((a, b) => 
                new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime()
            );

            // Mark all consecutive canceled appointments from the start as "first"
            let i = 0;
            while (i < patientAppointments.length) {
                firstAppointmentIds.add(patientAppointments[i].id);
                if (!patientAppointments[i].cancelledAt) {
                    break;
                }
                i++;
            }
        });

        const response = appointments.map(appointment => {
            const appointmentDate = dayjs(appointment.appointmentDate).tz(timezone);
            const isPast = appointmentDate.isBefore(referenceDateTime);

            let patientDetails = patients.find(patient => patient.id === appointment.patientId) || appointment.patient;
            if (appointment.isMinor) {
                const minorDetails = minors.find(minor => minor.id === appointment.minorId);
                if (minorDetails) {
                    patientDetails = User.build({
                        ...patientDetails.get(),
                        firstname: minorDetails.firstName,
                        lastname: minorDetails.lastName,
                    });
                }
            }

            return {
                id: appointment.id,
                appointmentDate,
                description: appointment.description,
                patient: patientDetails,
                initial_appointment: firstAppointmentIds.has(appointment.id) ? 'first' : 'return',
                past: isPast,
                isMinor: appointment.isMinor,
                cancelledAppointment: appointment.cancelledAt,
                timezone: timezone,
                isBilled: appointment.isBilled,
            };
        });

        const sortedAppointments = response.sort((a, b) => {
            const dateA = a.appointmentDate;
            const dateB = b.appointmentDate;

            const isPastA = dateA.isBefore(referenceDateTime) || dateA.isSame(referenceDateTime);
            const isPastB = dateB.isBefore(referenceDateTime) || dateB.isSame(referenceDateTime);

            if (isPastA !== isPastB) {
                return isPastA ? 1 : -1;
            }

            if (isPastA && isPastB) {
                return dateB.valueOf() - dateA.valueOf();
            } else {
                return dateA.valueOf() - dateB.valueOf();
            }
        });

        const formattedResponse = sortedAppointments.map(appointment => ({
            ...appointment,
            appointmentDate: appointment.appointmentDate.format('YYYY-MM-DD HH:mm:ss'),
        }));

        res.send(formattedResponse);
    })
);

/********************************
* * Get all patients report whose appointment is booked with specific therapist as per specified timezone
********************************/
router.get(
    '/appointments/export',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        const therapistId = req.user?.id;
        logger.info(`Exporting appointments for Therapist ID: ${therapistId}`);

        if (!therapistId) {
            return res.status(400).send({ message: 'Therapist ID is required' });
        }

        const userRegInfo = await UserRegistrationInfo.findOne({
            where: {
                userId: therapistId,
                pageName: 'normal-office-hours',
            },
        });

        if (!userRegInfo) {
            return res.status(404).send({ message: 'User registration info not found' });
        }

        const payloadInfo = userRegInfo.payloadInfo as any;
        const timezone = payloadInfo?.timezone;
        if (!timezone) {
            return res.status(404).send({ message: 'Timezone not found in user registration info' });
        }

        const referenceDateTime = dayjs().tz(timezone);

        const appointments = await Appointments.findAll({
            where: { therapistId },
            include: [
                {
                    model: User,
                    as: 'patient',
                    attributes: ['id', 'firstname', 'lastname', 'email', 'dob', 'gender', 'phone', 'bio', 'userProfile'],
                },
            ],
            attributes: ['id', 'appointmentDate', 'description', 'patientId'],
        });

        const appointmentsByPatient: { [key: string]: any[] } = {};
        appointments.forEach(appointment => {
            if (!appointmentsByPatient[appointment.patientId]) {
                appointmentsByPatient[appointment.patientId] = [];
            }
            appointmentsByPatient[appointment.patientId].push(appointment);
        });

        const earliestAppointments: { [key: string]: number } = {};
        Object.keys(appointmentsByPatient).forEach(patientId => {
            const patientAppointments = appointmentsByPatient[patientId];
            const earliestAppointment = patientAppointments.reduce((earliest, appointment) => {
                const appointmentDate = dayjs(appointment.appointmentDate).tz(timezone).valueOf();
                return appointmentDate < earliest ? appointmentDate : earliest;
            }, dayjs(patientAppointments[0].appointmentDate).tz(timezone).valueOf());
            earliestAppointments[patientId] = earliestAppointment;
        });

        const mappedAppointments = appointments.map(appointment => {
            const appointmentDate = dayjs(appointment.appointmentDate).tz(timezone);
            const isInitialAppointment = appointmentDate.valueOf() === earliestAppointments[appointment.patientId];

            return {
                id: appointment.id,
                appointmentDate: appointmentDate.toDate(),
                description: appointment.description,
                patient: appointment.patient,
                initial_appointment: isInitialAppointment ? 'First appointment' : 'Return appointment',
            };
        });

        const sortedAppointments = mappedAppointments.sort((a, b) => {            
            const dateA = dayjs(a.appointmentDate).tz(timezone);
            const dateB = dayjs(b.appointmentDate).tz(timezone);

            const isPastA = dateA.isBefore(referenceDateTime) || dateA.isSame(referenceDateTime);
            const isPastB = dateB.isBefore(referenceDateTime) || dateB.isSame(referenceDateTime);

            if (isPastA !== isPastB) {
                return isPastA ? 1 : -1;
            }

            return isPastA && isPastB
                ? dateB.valueOf() - dateA.valueOf()
                : dateA.valueOf() - dateB.valueOf();
        });

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Appointments');

        worksheet.columns = [
            { header: 'Name', key: 'fullName', width: 25 },
            { header: 'Appointment Date & Time', key: 'appointmentDateTime', width: 25 },
            { header: 'Initial Appointment', key: 'initial_appointment', width: 20 }
        ];

        sortedAppointments.forEach(appointment => {
            const appointmentDate = dayjs(appointment.appointmentDate).tz(timezone);

            worksheet.addRow({
                fullName: `${appointment.patient?.firstname || ''} ${appointment.patient?.lastname || ''}`.trim(),
                appointmentDateTime: appointmentDate.format('MMM D, YYYY, h:mm A'),
                initial_appointment: appointment.initial_appointment || ''
            });
        });

        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=appointments.xlsx');

        await workbook.xlsx.write(res);
        res.end();
    })
);


/********************************
* * Get all patients whose done payment for appointment therapist as per specified timezone
********************************/
router.get(
    '/payments-appointments',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        const therapistId = req.user?.id;

        if (!therapistId) {
            return res.status(400).send({ message: 'Therapist ID is required' });
        }
        logger.info(`Therapist ID: ${therapistId} requested payment appointments.`);

        // Fetch the timezone from UserRegistrationInfo
        const userRegInfo = await UserRegistrationInfo.findOne({
            where: {
                userId: therapistId,
                pageName: 'normal-office-hours',
            },
        });

        if (!userRegInfo) {
            return res.status(404).send({ message: 'User registration info not found' });
        }

        const payloadInfo = userRegInfo.payloadInfo as any;
        const timezone = payloadInfo?.timezone;
        if (!timezone) {
            return res.status(404).send({ message: 'Timezone not found in user registration info' });
        }

        const payments = await PatientPayment.findAll({
            where: { therapistId },
            include: [
                {
                    model: User,
                    as: 'patient',
                    attributes: ['id', 'firstname', 'lastname', 'email', 'phone', 'dob', 'gender'],
                },
                {
                    model: Appointments,
                    where: {
                        isBilled: true,
                        cancelledAt: null,
                    },
                    required: true,
                }
            ],
            attributes: ['id', 'appointmentId', 'billedPrice', 'patientId','currency','createdAt'],
            order: [['createdAt', 'DESC']]
        });

        logger.info(`Retrieved ${payments.length} payments for Therapist ID: ${therapistId}`);

        const patientIds = payments.map(payment => payment.patientId);

        const patients = await User.findAll({
            where: {
                id: {
                    [Op.in]: patientIds,
                },
            },
            attributes: ['id', 'firstname', 'lastname', 'email', 'phone', 'dob', 'gender', 'address', 'bio', 'userProfile'],
        });

        logger.info(`Retrieved ${patients.length} patient records.`);

        const response = payments.map(payment => {
            const paymentCreateDate = dayjs(payment.createdAt)
            .tz(timezone)
            .format("YYYY-MM-DD HH:mm:ss");

            return {
                id: payment.id,
                paymentCreateDate,
                patient: patients.find(patient => patient.id === payment.patientId) || payment.patient,
                cost: payment.billedPrice.toFixed(2),
                currency: payment.currency
            };
        });

        res.send(response);
    })
);

/********************************
* * Get all patients whose done payment for appointment therapist as per specified timezone
********************************/
router.get(
    '/payments/export',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        const therapistId = req.user?.id;

        if (!therapistId) {
            return res.status(400).send({ message: 'Therapist ID is required' });
        }

        const userRegInfo = await UserRegistrationInfo.findOne({
            where: {
                userId: therapistId,
                pageName: 'normal-office-hours',
            },
        });

        if (!userRegInfo) {
            return res.status(404).send({ message: 'User registration info not found' });
        }

        const payloadInfo = userRegInfo.payloadInfo as any;
        const timezone = payloadInfo?.timezone;
        if (!timezone) {
            return res.status(404).send({ message: 'Timezone not found in user registration info' });
        }
        const payments = await PatientPayment.findAll({
            where: { therapistId },
            include: [
                {
                    model: User,
                    as: 'patient',
                    attributes: ['id', 'firstname', 'lastname', 'email', 'dob', 'gender', 'phone', 'bio', 'userProfile'],
                },
            ],
            attributes: ['id', 'appointmentId', 'billedPrice', 'patientId','currency','createdAt'],
            order: [['createdAt', 'DESC']]
        });

        const patientIds = payments.map(payment => payment.patientId);

        const patients = await User.findAll({
            where: {
                id: {
                    [Op.in]: patientIds,
                },
            },
            attributes: ['id', 'firstname', 'lastname', 'email', 'phone', 'dob', 'gender', 'address', 'bio', 'userProfile'],
        });

        const response = payments.map(payment => {
            const paymentCreateDate = dayjs(payment.createdAt).tz(timezone)
            .format("YYYY-MM-DD HH:mm:ss");
            const amountPaid: string = ((payment.billedPrice || 0) / 100).toFixed(2)
            return {
                id: payment.id,
                paymentDate: paymentCreateDate,
                patient: patients.find(patient => patient.id === payment.patientId) || payment.patient,
                cost: `$${amountPaid}`,
                currency: payment.currency
            };
        });

        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Payments');

        worksheet.columns = [
            { header: 'Payment Made By', key: 'fullName', width: 25 },
            { header: 'Amount', key: 'payment', width: 20 },
            { header: 'Date & Time', key: 'paymentDate', width: 25 }
        ];

        response.forEach(payment => {
            const paymentDate = dayjs(payment.paymentDate).tz(timezone);
            worksheet.addRow({
                fullName: `${payment.patient?.firstname || ''} ${payment.patient?.lastname || ''}`
                .toLowerCase()
                .replace(/\b\w/g, (char) => char.toUpperCase()).trim(),
                payment: payment.cost,
                paymentDate: paymentDate.format('MMM D, YYYY, h:mm A'),
            });
        });

        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=payments.xlsx');

        await workbook.xlsx.write(res);
        res.end();
    })
);


/********************************
 * * Get all reasons for deleting/deactivating patient account  
 ********************************/
router.get(
    '/delete-deactivate/reasons',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        try {
            logger.info(`User ${req.user?.id} accessed patient deletion/deactivation reasons.`);
            
            const reasons = await PatientDeleteDeactivateReason.findAll({
                attributes: ['id', 'heading'],
                order: [['id', 'ASC']]
            });

            return res.status(200).json({
                success: true,
                reasons: reasons
            });
        } catch (error) {
            logger.error(`Error fetching patient deletion reasons: ${(error as Error).message}`);
            return res.status(500).json({
                success: false,
                message: DELETION_REASON,
                error: (error as Error).message
            });
        }
    })
);


/**
 * @middleware PortalMiddleware
 * @description Soft deletes a patient by updating the `deletedAt` field and creates a corresponding deletion reason history record.
 * @param {string} req.params.id - ID of the user (patient) to be deleted.
 * @param {string} req.query.reasonId - ID of the reason for deleting the patient.
 * @returns {200} Success - Returns success response when patient is deleted successfully.
 * @returns {500} InternalServerError - Returns an error response if deletion fails or transaction cannot be started.
 */

router.delete(
    '/reason/:id',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const { reasonId } = req.query;
            // Start a transaction
            const transaction = await User.sequelize?.transaction();
            
            if (!transaction) {
                throw new Error('Transaction could not be started');
            }

            try {
                // Create history record
                await PatientDeleteDeactivateReasonsHistory.create({
                    userId: id,
                    reasonId: reasonId as string,
                    type: 'deleted'
                }, { transaction });

                // Update user record
                await User.update(
                    {
                        deletedAt: new Date(),
                        active: false, version: 0,
                    },
                    {
                        where: { id },
                        transaction
                    }
                );

                await transaction.commit();                
                return res.status(HttpStatusCode.Ok).json({
                    success: true,
                    message: PATIENT_DELETED
                });

            } catch (error) {
                await transaction.rollback();
                throw error;
            }

        } catch (error) {
            logger.error(`Error deleting patient: ${(error as Error).message}`);
            return res.status(HttpStatusCode.InternalServerError).json({
                success: false,
                message: DELETION_REASON_DELETING,
                error: (error as Error).message
            });
        }
    })
);

/**
 * @middleware PortalMiddleware
 * @description Soft-deactivates a patient by setting the `deactivatedAt` field and logs the action in the deactivation history.
 * @param {string} req.params.id - ID of the patient to deactivate.
 * @param {string} req.query.reasonId - ID of the reason for deactivation (required).
 * @param {string} [req.query.reason] - Optional human-readable reason for logging/debugging purposes.
 */

router.patch(
    '/deactivate/:id',
    PortalMiddleware,
    wrap(async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const { reasonId } = req.query;
            const reason = req.query.reason as string;
            const requestingUserId = req.user?.id;

            logger.info(`User ${requestingUserId} attempting to deactivate patient ${id} with reason ${reasonId}`);

            // Input validation
            if (!reasonId) {
                return res.status(400).json({
                    success: false,
                    message: REASON_ID_REQUIRED
                });
            }

            // Start transaction
            const transaction = await User.sequelize?.transaction();
            
            if (!transaction) {
                throw new Error(TRANSACTION_NOT_STARTED);
            }

            try {
                // Find user
                const user = await User.findByPk(id);
                if (!user) {
                    await transaction.rollback();
                    return res.status(404).json({
                        success: false,
                        message: PATIENT_NOT_FOUND
                    });
                }

                // Update user deactivation status
                await user.update({
                    deactivatedAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    active: false, version: 0
                }, { transaction });

                // Create history record
                await PatientDeleteDeactivateReasonsHistory.create({
                    userId: id,
                    reasonId: reasonId as string,
                    type: 'deactivated'
                }, { transaction });

                // Commit transaction
                await transaction.commit();
                
                logger.info(`Patient ${id} deactivated successfully with reason ${reasonId}`);
                
                return res.status(HttpStatusCode.Ok).json({
                    success: true,
                    message: PATIENT_DEACTIVATED
                });

            } catch (error) {
                await transaction.rollback();
                throw error;
            }

        } catch (error) {
            logger.error(`Error deactivating patient: ${(error as Error).message}`);
            return res.status(HttpStatusCode.InternalServerError).json({
                success: false,
                message: PATIENT_DEACTIVATED_ERROR,
                error: (error as Error).message
            });
        }
    })
);



export default router