import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import { Speciality } from '@/src/models'
import { HttpStatusCode } from 'axios'
import { SpecialityValidator } from '@/src/application/validators'
import { NotFoundError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
// import CacheMiddleware from '@/src/configs/cache.config'
import { getSpecialities } from '@/src/application/repositories/speciality.repository'

const router = express.Router()

/********************************
 * * Get all specialities
 ********************************/
router.get(
	'/',
	// CacheMiddleware('specialities'),
	wrap(async (req: Request, res: Response) => {
		const specialities = await getSpecialities({ req })
		res.send(paginatedData(specialities, req))
	})
)

/********************************
 * * Create a speciality
 ********************************/
router.post(
	'/',
	PortalMiddleware,
	SpecialityValidator,
	// CacheMiddleware('specialities'),
	wrap(async (req: Request, res: Response) => {
		const { name, description } = req.body
		const speciality = await Speciality.create({ name, description })
		res.status(HttpStatusCode.Created).send({
			message: 'Speciality created successfully',
			speciality,
		})
	})
)

/********************************
 * * Update a speciality
 ********************************/
router.put(
	'/:id',
	PortalMiddleware,
	SpecialityValidator,
	// CacheMiddleware('specialities'),
	wrap(async (req: Request, res: Response) => {
		const { name, description } = req.body
		const speciality = await Speciality.findByPk(req.params.id)
		if (!speciality) throw new NotFoundError('Speciality not found')

		await speciality.update({ name, description })

		res.status(HttpStatusCode.Created).send({
			message: 'Speciality updated successfully',
			speciality,
		})
	})
)

/********************************
 * * Delete a speciality
 ********************************/
router.delete(
	'/:id',
	PortalMiddleware,
	// CacheMiddleware('specialities'),
	wrap(async (req: Request, res: Response) => {
		const {id} = req.params;
		const speciality = await Speciality.findByPk(id)
		if (!speciality) throw new NotFoundError('Speciality not found')

		await speciality.destroy()
		//return res.sendStatus(HttpStatusCode.NoContent)
		res.send({ message: 'Speciality deleted' })
	})
)

export default router
