import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { acceptTherapist, getTherapistById, getTherapists, rejectTherapist } from '@/src/application/repositories/therapist.repository'
import { HttpStatusCode } from 'axios'
import { User, UserRegistrationInfo,Appointments,Calendar, ProfileRejectionReason } from '@/src/models'
import { UserType } from '../../helpers/constant.helper'
import { NotFoundError } from '../../handlers/errors'
import { Op,Sequelize } from 'sequelize'
import { USER_DELETE } from './constants';
import { deleteGoogleEvent, deleteOutlookEvent } from '@/src/application/repositories/calendar.repository'
import logger from '@/src/configs/logger.config'
import { getUserWithCalendarInfo } from '../../repositories/user.repository'
import { emailVerification } from '../../repositories/auth.repository'
import { ProfileDeleteDeactivateReason,UserDeleteDeactivateReason,MainHeadingDeleteDeactivate } from '@/src/models'
import { removePatientsFromTherapistWaitlist } from '../../repositories/therapist-waitlist.repository'
import { TherapistDeleteEmail } from '../../emails/therapist-delete.email'
import {TherapistDeleteEmailSentToAdmin} from '../../emails/therapist-deleted-send-to-admin'
import { mail } from '@/src/configs/sendgrid.config'

const router = express.Router()

/********************************
 * * Get all therapists
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
    logger.info(`User ${req.user?.id} accessed therapist list`);

		const therapists = await getTherapists({
			req,
		})
		res.send(paginatedData(therapists, req))
	})
)

/********************************
 * * Update therapist
 ********************************/
router.put(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
    logger.info(`User ${req.user?.id} initiated update request for therapist ID: ${req.params.id}`);

		const therapist = await User.findOne({
			where: {
				id: req.params.id,
				role: UserType.THERAPIST,
			},
		});

		if (!therapist) throw new NotFoundError('Therapist not found')

		const { first_name: firstname, last_name: lastname, dob, gender } = req.body;		

		await therapist.update({
			firstname,
			lastname,
			dob,
			gender,			
		});

    logger.info(`User ${req.user?.id} updated therapist ID: ${req.params.id}`);

		res.status(HttpStatusCode.Ok).send({
			therapist,
		});
	})
);

/********************************
 * * Get Registration Info by Page Name
 ********************************/
router.all(
	'/reg-info',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
    const { user } = req;
    if (!user) return res.forbidden('Invalid Request. User is required');

    logger.info(`User ID ${user.id} accessed registration info.`);

		const { page } = req.query;
    const { pages } = req.body;

    let regInfo;

    if (pages && Array.isArray(pages) && pages.length > 0) {
      // If pages exist, find all matching entries
      regInfo = await UserRegistrationInfo.findAll({
        where: {
          userId: user.id,
          pageName: pages,
        },
      });
    } else if (page) {
      // If page exists, find a single matching entry
      regInfo = await UserRegistrationInfo.findOne({
        where: {
          userId: user.id,
          pageName: page,
        },
      });
    } else {
      return res
        .status(HttpStatusCode.BadRequest)
        .send({ error: "Either 'page' query or 'pages' in body must be provided." });
    }

    return res.send(regInfo);
	})
)

/********************************
 * * Get therapist by id
 ********************************/
router.get(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
    logger.info(`User ${req.user?.id} accessed therapist details for ID: ${req.params.id}`);

		const therapist = await getTherapistById(req)
		res.send(therapist)
	})
)

/********************************
 * * Accept therapist
 ********************************/
router.patch(
	'/accept/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		await acceptTherapist(req)
    logger.info(`User ${req.user?.id} accepted therapist ID: ${req.params.id}`);
    
		res.status(HttpStatusCode.Created).send({ message: 'Therapist accepted' })
	})
)

/********************************
 * * Reject therapist
 ********************************/
router.patch(
	'/reject/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		await rejectTherapist(req)
    logger.info(`User ${req.user?.id} rejected therapist ID: ${req.params.id}`);
		res.status(HttpStatusCode.Created).send({ message: 'Therapist rejected' })
	})
)


/********************************
 * * Delete therapist (Set inactive & delete appointment data)
 ********************************/
router.delete(
  '/profile/:userId',
  PortalMiddleware,
  wrap(async (req: Request, res: Response) => {
    const { userId } = req.params;
    const { deletedAt } = req.query;

    logger.info(`User ID : ${req.user?.id} initiated Delete request for user : ${userId} with deletedAt: ${deletedAt}`);

    const transaction = await User.sequelize?.transaction();

    try {
      if (!transaction) {
        return res.status(500).json({ message: 'Transaction could not be started' });
      }

      const user = await User.findOne({
        where: { id: userId },
        include: [
          {
            model: UserRegistrationInfo,
            as: 'registrationInfo',
            where: { pageName: 'profile-info' },
            attributes: {
              exclude: ['payloadInfo'],
              include: [
                [
                  Sequelize.literal(`(
                    SELECT jsonb_strip_nulls(
                      jsonb_build_object(
                        'user_profile', "payloadInfo"->>'user_profile'
                      )
                    )
                    FROM "user_registration_informations"
                    WHERE "user_registration_informations"."userId" = "registrationInfo"."userId"
                    AND "user_registration_informations"."pageName" = 'profile-info'
                    LIMIT 1
                  )`),
                  'payload_info',
                ],
              ],
            },
            required: false,
          },
          {
            model: Calendar,
            as: 'calendars',
            attributes: ['type', 'credentials'],
            required: false,
          },
        ],
        transaction,
      });
      
      if (!user) {
        throw new Error('User not found');
      }

      logger.info(`Deactivating user account: ${userId}`);
      
      // Update the user with both active=false and the deletedAt timestamp
      await User.update(
        { 
          active: false,
          deletedAt: deletedAt,
          emailVerifiedAt: null ,
          deletedBy:"therapist",
          version:0
        }, 
        { where: { id: userId }, transaction }
      );
      
      if (user.role === 'therapist') {
        const removedCount = await removePatientsFromTherapistWaitlist(user.id);
logger.info(`Successfully removed ${removedCount} patient(s) from the waitlist of therapist ID: ${user.id} during profile deletion.`);
      }
      
      await transaction.commit();
      logger.info(`User ID: ${userId} successfully deleted by Admin ID: ${req.user?.id}`);

      const email = user.email;
      const emailData = TherapistDeleteEmail.compile({
      email,
      user: user.toJSON(),
      });
  
      const mailData = {
      to: emailData.To , 
      subject: emailData.Subject,
      html: emailData. HtmlBody || `<p>Click <a href="${emailData. HtmlBody}">here</a> to deactivate your account.</p>`,
      };

      const email_user = user.email;
      const emailData_user = TherapistDeleteEmailSentToAdmin.compile({
      email_user,
      user: user.toJSON(),
      });
  
      const mailData_user = {
      to: emailData_user.To ,   
      subject: emailData_user.Subject,
      html: emailData_user. HtmlBody || `<p>Click <a href="${emailData_user. HtmlBody}">here</a> to deactivate your account.</p>`,
      };
  
      try {
        await mail.sendMail(mailData_user, true);
        await mail.sendMail(mailData, true);
      } catch (error) {
      logger.error(`Failed to send deactivation email. Error: ${error}`);
      throw new Error('Failed to send email');
      }
      res.status(200).json({
        message: USER_DELETE,
        user: {
          id: user.id,
          email: user.email,
          firstname: user.firstname,
          lastname: user.lastname,
          active: false,
          profile: user.profile,
          deletedAt: deletedAt
        },
      });
    } catch (error) {
      logger.error(`Error deleting user ID: ${userId}: ${error}`);
      await transaction?.rollback();
      res.status(500).json({ message: 'An error occurred while deleting the user' });
    }
  })
);

/********************************
 * * Enable / Disable MFA
 ********************************/
router.patch(
	'/mfa',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
    logger.info(`User ID ${req.user?.id} initiated MFA enable/disable request.`);
		const { enable } = req.body;
    if (typeof enable !== 'boolean') {
      logger.info(`User ID ${req.user?.id} attempted to enable/disable MFA with invalid request body. The 'enable' value is not a boolean.`);
      return res.forbidden("Invalid request. Please provide a valid value for 'enable'.");
    }

    logger.info(`${enable ? 'Enabling' : 'Disabling'} MFA for user ID ${req.user?.id}`);

    await User.update(
      { mfaEnabled: enable },
      { where: { id: req.user?.id } }
    );
    logger.info(`MFA ${enable ? 'enabled' : 'disabled'} for user ID ${req.user?.id}`);

    const userData = await getUserWithCalendarInfo(req.user?.id)
    return res.success(`${enable ? 'Enabled' : 'Disabled'} MFA successfully.`, { user: userData });
	})
)

/********************************
 * * Therapist Rejection Reasons
 ********************************/

router.get(
  '/get-rejected-reasons/:id',
   PortalMiddleware, 
   wrap(async (req: Request, res: Response) => {
  logger.info(`User ${req.user?.id} accessed rejected reasons.`);
  const reasons = await ProfileRejectionReason.findAll({  
    where: {
      userId: req.params.id,
      rejectedAt: {
        [Op.ne]: null,
      },
    },  
    order: [['rejectedAt', 'DESC']],
  });

  console.log('reasons', reasons);
  res.status(HttpStatusCode.Ok).send({
    reasons,
  });
}))

/**
 * @middleware PortalMiddleware
 * @description Reactivates a previously deactivated user account.
 * @param {string} req.params.id - ID of the user to be reactivated
 * @param {Object} req.user - Authenticated user object, extracted via middleware
 * @returns {Object} 200 - Success response containing updated user info
 * @throws {NotFoundError} 404 - If the user with the given ID is not found
 * 
**/
router.put(
  '/restore-account/:id',
   PortalMiddleware, 
   wrap(async (req: Request, res: Response) => {
    logger.info(`User ${req.user?.id} initiated user reactivation for ID: ${req.params.id}`);

    const user = await User.findOne({
      where: { id: req.params.id }
    });

    if (!user) {
      logger.error(`User ID ${req.params.id} not found`);
      throw new NotFoundError('User not found');
    }

    await user.update({
      version: 1,
      active: true,
      deletedAt: null,
      deletedBy:null,
    });

    logger.info(`User ID ${req.params.id} successfully reactivated`);
    
    res.status(HttpStatusCode.Ok).send({
      message: 'User restored successfully',
    });
}))



/**
 * @route GET /reasons/deleted-deactivated
 * @middleware PortalMiddleware
 * @description Retrieves a list of profile deletion reasons with the type "deleted" and "deactivated".
 * @param {Request} req - Express request object, expected to include authenticated user information.
 * @param {Response} res - Express response object used to send the list of deletion reasons.
 * @returns {Array} 200.reasons - List of reasons, each with `id` and `reason` fields.
 *
*/


router.get(
  '/reasons/deleted-deactivated',
  PortalMiddleware, 
  wrap(async (req: Request, res: Response) => {
    logger.info(`User ${req.user?.id} accessed deleted reasons list.`);
    
    const headingsWithSubheadings = await MainHeadingDeleteDeactivate.findAll({
      attributes: ['id', 'heading'],
      include: [{
        model: ProfileDeleteDeactivateReason,
        attributes: ['id', 'subheading'],
        as: 'subheadings'  // Make sure this matches your association name
      }],
      order: [
        ['id', 'ASC'],
        [{ model: ProfileDeleteDeactivateReason, as: 'subheadings' }, 'id', 'ASC']
      ]
    });

    // Format the response
    interface Subheading {
      id: number;
      subheading: string;
    }

    interface HeadingWithSubheadings {
      id: number;
      heading: string;
      subheadings: Subheading[];
    }

    const formattedResponse: HeadingWithSubheadings[] = (headingsWithSubheadings as HeadingWithSubheadings[]).map((heading: HeadingWithSubheadings) => ({
      id: heading.id,
      heading: heading.heading,
      subheadings: heading.subheadings.map((sub: Subheading) => ({
        id: sub.id,
        subheading: sub.subheading
      }))
    }));

    res.status(HttpStatusCode.Ok).send({
      reasons: formattedResponse
    });
}));

router.get(
  '/view-history/reasons/:id',
  PortalMiddleware,
  wrap(async (req: Request, res: Response) => {
    const userId = req.params.id;

    logger.info(`User ${req.user?.id} accessed full history reasons for user ${userId}`);

    // Get 'rejected' reasons
    const rejectedReasons = await ProfileRejectionReason.findAll({
      where: {
        userId,
        rejectedAt: {
          [Op.ne]: null,
        },
      },
      attributes: ['rejectedAt', 'reason'],
      raw: true,
    });

    const formattedRejections = rejectedReasons.map(r => ({
      date: r.rejectedAt,
      reason: r.reason,
      type: 'rejected',
    }));

    // Get 'deleted' & 'deactivated' reasons
    const deleteDeactivateReasons = await UserDeleteDeactivateReason.findAll({
      where: { userId },
      include: [
        {
          model: ProfileDeleteDeactivateReason,
          attributes: ['subheading'],
        },
      ],
      attributes: ['createdAt', 'type'],
      raw: true,
      nest: true,
    });

    const formattedOthers = deleteDeactivateReasons.map(r => ({
      date: r.createdAt,
      reason: r.reason?.subheading || 'Unknown reason',
      type: r.type,
    }));

    const allReasons = [...formattedRejections, ...formattedOthers].sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    res.status(HttpStatusCode.Ok).send({
      reasons: allReasons,
    });
  })
);

export default router
