import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { getTherapistPages } from '../../repositories/therapist-pages.repository'

const router = express.Router()

/********************************
 * * Get all therapist pages
 ********************************/
router.get(
  '/',
  PortalMiddleware,
  wrap(async (req: Request, res: Response) => {
    const therapistPages = await getTherapistPages(req)
    res.send(therapistPages)
  })
)

export default router
