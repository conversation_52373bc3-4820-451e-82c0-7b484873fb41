import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import { User } from '@/src/models';

// Import repositories to stub
import * as eventRepository from '@/src/application/repositories/event.respository';
import * as userRepository from '@/src/application/repositories/user.repository';
import * as appointmentsRepository from '@/src/application/repositories/appointments.repository';
import * as googleConfig from '@/src/configs/google.config';

// Mock user data
const mockUser = {
  id: 'test-user-id',
  firstname: 'Test',
  lastname: 'User',
  email: '<EMAIL>',
  emailVerifiedAt: new Date(),
  role: 'ADMIN',
  version: 1,
  getGoogleCalendarTokens: () => ({ access_token: 'mock-token', refresh_token: 'mock-refresh-token' })
} as any;

// Dummy data for tests
const dummyEvents = {
  count: 2,
  rows: [
    {
      id: 'event-123',
      name: 'Test Event 1',
      description: 'Test description 1',
      startDate: new Date('2025-06-15T10:00:00Z'),
      endDate: new Date('2025-06-15T11:00:00Z'),
      tagId: 'tag-123',
      isAllDay: false,
      isRecurring: false,
      location: 'Test Location 1',
      attendees: ['<EMAIL>', '<EMAIL>'],
      calendarEventId: 'calendar-event-123',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'event-456',
      name: 'Test Event 2',
      description: 'Test description 2',
      startDate: new Date('2025-06-16T14:00:00Z'),
      endDate: new Date('2025-06-16T15:00:00Z'),
      tagId: 'tag-456',
      isAllDay: false,
      isRecurring: false,
      location: 'Test Location 2',
      attendees: ['<EMAIL>', '<EMAIL>'],
      calendarEventId: 'calendar-event-456',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
};

const dummyEvent = {
  id: 'event-123',
  name: 'Test Event',
  description: 'Test description',
  startDate: new Date('2025-06-15T10:00:00Z'),
  endDate: new Date('2025-06-15T11:00:00Z'),
  tagId: 'tag-123',
  isAllDay: false,
  isRecurring: false,
  location: 'Test Location',
  attendees: ['<EMAIL>', '<EMAIL>'],
  calendarEventId: 'calendar-event-123',
  createdAt: new Date(),
  updatedAt: new Date(),
  update: sinon.stub().resolves({
    id: 'event-123',
    name: 'Updated Event',
    description: 'Updated description',
    startDate: new Date('2025-06-15T10:00:00Z'),
    endDate: new Date('2025-06-15T11:00:00Z'),
    tagId: 'tag-123',
    isAllDay: false,
    isRecurring: false,
    location: 'Updated Location',
    attendees: ['<EMAIL>', '<EMAIL>'],
    calendarEventId: 'calendar-event-123',
    createdAt: new Date(),
    updatedAt: new Date()
  }),
  destroy: sinon.stub().resolves(true)
};

const dummyAppointments = {
  count: 2,
  rows: [
    {
      id: 'appointment-123',
      name: 'Test Appointment 1',
      appointmentDate: new Date('2025-06-15T10:00:00Z'),
      appointmentTime: '10:00',
      location: 'Test Location 1',
      locationDetails: 'Room 101',
      therapist: {
        id: 'therapist-123',
        firstname: 'Therapist',
        lastname: 'One'
      },
      cancelledAt: null,
      isMinor: false,
      minorPatient: null
    },
    {
      id: 'appointment-456',
      name: 'Test Appointment 2',
      appointmentDate: new Date('2025-06-16T14:00:00Z'),
      appointmentTime: '14:00',
      location: 'Test Location 2',
      locationDetails: 'Room 202',
      therapist: {
        id: 'therapist-456',
        firstname: 'Therapist',
        lastname: 'Two'
      },
      cancelledAt: null,
      isMinor: true,
      minorPatient: {
        id: 'minor-123',
        firstname: 'Minor',
        lastname: 'Patient'
      }
    }
  ]
};

// Set up Express response methods
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Add auth middleware to set user for all requests
app.use((req: Request, res: Response, next: NextFunction) => {
  req.user = { ...mockUser };
  req.role = mockUser.role;
  next();
});

// Path constants
const portalMiddlewarePath = '../../../application/middlewares/portal.middleware';
const apiMiddlewarePath = '../../../application/middlewares/api.middleware';

// Store original middlewares
let originalPortalMiddleware: any;
let originalApiMiddleware: any;

describe('Web Event Controller Unit Test', function () {
  let sandbox: sinon.SinonSandbox;
  let getEventsStub: sinon.SinonStub;
  let getEventByIdStub: sinon.SinonStub;
  let createEventStub: sinon.SinonStub;
  let updateEventStub: sinon.SinonStub;
  let deleteEventStub: sinon.SinonStub;
  let getAppointmentsByMonthYearStub: sinon.SinonStub;
  let getUserByIdStub: sinon.SinonStub;
  let createGoogleCalendarEventStub: sinon.SinonStub;
  let updateGoogleCalendarEventStub: sinon.SinonStub;
  let deleteGoogleCalendarEventStub: sinon.SinonStub;
  let paginatedDataStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let userFindByPkStub: sinon.SinonStub;

  before(function() {
    // Save original middlewares if they exist
    if (require.cache[require.resolve(portalMiddlewarePath)]) {
      originalPortalMiddleware = require.cache[require.resolve(portalMiddlewarePath)];
    }
    
    if (require.cache[require.resolve(apiMiddlewarePath)]) {
      originalApiMiddleware = require.cache[require.resolve(apiMiddlewarePath)];
    }

    // Create a custom portal middleware function that can be modified in tests
    const customPortalMiddleware = function(req: Request, res: Response, next: NextFunction) {
      // This will be the default behavior for most tests
      req.user = { ...mockUser };
      req.role = mockUser.role;
      next();
    };
    
    // Create a custom API middleware function that can be modified in tests
    const customApiMiddleware = function(req: Request, res: Response, next: NextFunction) {
      // This will be the default behavior for most tests
      req.user = { ...mockUser };
      req.role = mockUser.role;
      next();
    };

    // Override the middleware modules in require.cache
    require.cache[require.resolve(portalMiddlewarePath)] = {
      id: require.resolve(portalMiddlewarePath),
      filename: require.resolve(portalMiddlewarePath),
      loaded: true,
      exports: customPortalMiddleware
    } as NodeModule;
    
    require.cache[require.resolve(apiMiddlewarePath)] = {
      id: require.resolve(apiMiddlewarePath),
      filename: require.resolve(apiMiddlewarePath),
      loaded: true,
      exports: customApiMiddleware
    } as NodeModule;

    // Mount the controller after mocking the middlewares
    const controllerPath = '../../../application/controllers/web/event.controller';
    app.use('/events', require(controllerPath).default);
  });
  
  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    // Stub repository methods
    getEventsStub = sandbox.stub(eventRepository, 'getEvents');
    getEventByIdStub = sandbox.stub(eventRepository, 'getEventById');
    createEventStub = sandbox.stub(eventRepository, 'createEvent');
    updateEventStub = sandbox.stub(eventRepository, 'updateEvent');
    deleteEventStub = sandbox.stub(eventRepository, 'deleteEvent');
    getAppointmentsByMonthYearStub = sandbox.stub(appointmentsRepository, 'getAppointmentsByMonthYear');
    getUserByIdStub = sandbox.stub(userRepository, 'getUserById');
    
    // Stub Google Calendar methods
    createGoogleCalendarEventStub = sandbox.stub(googleConfig, 'createGoogleCalendarEvent');
    updateGoogleCalendarEventStub = sandbox.stub(googleConfig, 'updateGoogleCalendarEvent');
    deleteGoogleCalendarEventStub = sandbox.stub(googleConfig, 'deleteGoogleCalendarEvent');
    
    // Stub pagination helper
    paginatedDataStub = sandbox.stub(require('@/src/application/helpers/pagination.helper'), 'paginatedData');
    
    // JWT stub
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'web',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    
    // User model stub
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockUser);
    
    // Setup getUserById stub with default behavior
    getUserByIdStub.resolves(mockUser);
  });
  
  afterEach(function () {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    const controllerPath = '../../../application/controllers/web/event.controller';
    delete require.cache[require.resolve(controllerPath)];
    
    // Restore original middlewares
    if (originalPortalMiddleware) {
      require.cache[require.resolve(portalMiddlewarePath)] = originalPortalMiddleware;
    } else {
      delete require.cache[require.resolve(portalMiddlewarePath)];
    }
    
    if (originalApiMiddleware) {
      require.cache[require.resolve(apiMiddlewarePath)] = originalApiMiddleware;
    } else {
      delete require.cache[require.resolve(apiMiddlewarePath)];
    }
  });

  describe('GET /events', function () {
    it('should retrieve all events successfully', async function () {
      // Setup stubs
      getEventsStub.resolves(dummyEvents);
      paginatedDataStub.returns({
        data: dummyEvents.rows,
        meta: {
          total: dummyEvents.count,
          currentPage: 1,
          perPage: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });
      
      const response = await request(app)
        .get('/events')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getEventsStub);
      
      // Check response structure
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
      expect(response.body.data).to.be.an('array');
    });
    
    it('should return 401 when user is not authenticated', async function () {
      // Create a new app instance with unauthorized middleware
      const testApp = express();
      testApp.use(express.json());
      
      // Create unauthorized middleware
      const unauthorizedMiddleware = (req: Request, res: Response, next: NextFunction) => {
        return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
      };
      
      // Mount the middleware and a simple route
      testApp.get('/events', unauthorizedMiddleware, (req, res) => {
        // This should never be reached
        res.status(HttpStatusCode.Ok).send({ data: [], meta: {} });
      });
      
      const response = await request(testApp)
        .get('/events')
        .expect(HttpStatusCode.Unauthorized);
      
      expect(response.body).to.have.property('message', 'Unauthorized');
    });
  });

  describe('GET /events/:id', function () {
    it('should retrieve an event by id successfully', async function () {
      // Setup stubs
      getEventByIdStub.resolves(dummyEvent);
      
      const response = await request(app)
        .get('/events/event-123')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getEventByIdStub);
      sinon.assert.calledWith(getEventByIdStub, 'event-123');
      
      // Check response structure
      expect(response.body).to.have.property('event');
      expect(response.body.event).to.have.property('id', 'event-123');
      expect(response.body.event).to.have.property('name', 'Test Event');
    });
    
    it('should return 404 when event is not found', async function () {
      // Setup stubs to throw NotFoundError
      getEventByIdStub.rejects(new Error('Event not found'));
      
      const response = await request(app)
        .get('/events/non-existent-id')
        .expect(HttpStatusCode.InternalServerError);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getEventByIdStub);
      sinon.assert.calledWith(getEventByIdStub, 'non-existent-id');
    });
  });

  describe('POST /events', function () {
    it('should create a new event successfully', async function () {
      // Setup stubs
      createEventStub.resolves(dummyEvent);
      createGoogleCalendarEventStub.resolves({ id: 'calendar-event-123' });
      
      const requestBody = {
        name: 'Test Event',
        description: 'Test description',
        startDate: '2025-06-15T10:00:00Z',
        endDate: '2025-06-15T11:00:00Z',
        tagId: 'tag-123',
        isAllDay: false,
        isRecurring: false,
        location: 'Test Location',
        attendees: ['<EMAIL>', '<EMAIL>']
      };
      
      const response = await request(app)
        .post('/events')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(createEventStub);
      sinon.assert.calledOnce(getUserByIdStub);
      sinon.assert.calledOnce(createGoogleCalendarEventStub);
      
      // Check response structure
      expect(response.body).to.have.property('message', 'Event created successfully');
      expect(response.body).to.have.property('event');
      expect(response.body.event).to.have.property('id', 'event-123');
      expect(response.body.event).to.have.property('name', 'Test Event');
    });
    
    it('should return 401 when user is not authenticated', async function () {
      // Create a new app instance with unauthorized middleware
      const testApp = express();
      testApp.use(express.json());
      
      // Create unauthorized middleware
      const unauthorizedMiddleware = (req: Request, res: Response, next: NextFunction) => {
        return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
      };
      
      // Mount the middleware and a simple route
      testApp.post('/events', unauthorizedMiddleware, (req, res) => {
        // This should never be reached
        res.status(HttpStatusCode.Created).send({ message: 'Event created successfully', event: {} });
      });
      
      const requestBody = {
        name: 'Test Event',
        description: 'Test description',
        startDate: '2025-06-15T10:00:00Z',
        endDate: '2025-06-15T11:00:00Z',
        tagId: 'tag-123',
        isAllDay: false,
        isRecurring: false,
        location: 'Test Location',
        attendees: ['<EMAIL>', '<EMAIL>']
      };
      
      const response = await request(testApp)
        .post('/events')
        .send(requestBody)
        .expect(HttpStatusCode.Unauthorized);
      
      expect(response.body).to.have.property('message', 'Unauthorized');
    });
  });

  describe('PUT /events/:id', function () {
    it('should update an event successfully', async function () {
      // Setup stubs
      updateEventStub.resolves({
        ...dummyEvent,
        name: 'Updated Event',
        description: 'Updated description',
        location: 'Updated Location'
      });
      updateGoogleCalendarEventStub.resolves({ id: 'calendar-event-123' });
      
      const requestBody = {
        name: 'Updated Event',
        description: 'Updated description',
        startDate: '2025-06-15T10:00:00Z',
        endDate: '2025-06-15T11:00:00Z',
        tagId: 'tag-123',
        isAllDay: false,
        isRecurring: false,
        location: 'Updated Location',
        attendees: ['<EMAIL>', '<EMAIL>']
      };
      
      const response = await request(app)
        .put('/events/event-123')
        .send(requestBody)
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(updateEventStub);
      sinon.assert.calledOnce(getUserByIdStub);
      sinon.assert.calledOnce(updateGoogleCalendarEventStub);
      
      // Check response structure
      expect(response.body).to.have.property('message', 'Event updated successfully');
      expect(response.body).to.have.property('event');
      expect(response.body.event).to.have.property('name', 'Updated Event');
      expect(response.body.event).to.have.property('description', 'Updated description');
      expect(response.body.event).to.have.property('location', 'Updated Location');
    });
    
    it('should return 404 when event is not found', async function () {
      // Setup stubs to throw NotFoundError
      updateEventStub.rejects(new Error('Event not found'));
      
      const requestBody = {
        name: 'Updated Event',
        description: 'Updated description',
        startDate: '2025-06-15T10:00:00Z',
        endDate: '2025-06-15T11:00:00Z',
        tagId: 'tag-123',
        isAllDay: false,
        isRecurring: false,
        location: 'Updated Location',
        attendees: ['<EMAIL>', '<EMAIL>']
      };
      
      const response = await request(app)
        .put('/events/non-existent-id')
        .send(requestBody)
        .expect(HttpStatusCode.InternalServerError);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(updateEventStub);
    });
  });

  describe('DELETE /events/:id', function () {
    it('should delete an event successfully', async function () {
      // Setup stubs
      getEventByIdStub.resolves(dummyEvent);
      deleteGoogleCalendarEventStub.resolves({});
      deleteEventStub.resolves(true);
      
      const response = await request(app)
        .delete('/events/event-123')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getEventByIdStub);
      sinon.assert.calledOnce(getUserByIdStub);
      sinon.assert.calledOnce(deleteGoogleCalendarEventStub);
      sinon.assert.calledOnce(deleteEventStub);
      
      // Check response structure
      expect(response.body).to.have.property('message', 'Event deleted successfully');
    });
    
    it('should return 404 when event is not found', async function () {
      // Setup stubs to throw NotFoundError
      getEventByIdStub.rejects(new Error('Event not found'));
      
      const response = await request(app)
        .delete('/events/non-existent-id')
        .expect(HttpStatusCode.InternalServerError);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getEventByIdStub);
    });
  });

  describe('GET /events/monthly-events/detail', function () {
    it('should retrieve monthly events successfully', async function () {
      // Setup stubs
      getAppointmentsByMonthYearStub.resolves(dummyAppointments);
      paginatedDataStub.returns({
        data: dummyAppointments.rows.map((appointment: any) => ({
          cancelledAt: appointment.cancelledAt,
          id: appointment.id,
          name: appointment.name,
          startDate: appointment.appointmentDate,
          endDate: new Date(appointment.appointmentDate),
          location: appointment.location || 'Therapy Center',
          address: appointment.locationDetails || null,
          therapist: appointment.therapist,
          appointmentTime: appointment.appointmentTime,
          minorPatient: appointment.minorPatient,
          isMinor: appointment.isMinor,
        })),
        meta: {
          total: dummyAppointments.count,
          currentPage: 1,
          perPage: 10,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });
      
      const response = await request(app)
        .get('/events/monthly-events/detail?month=6&year=2025')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getAppointmentsByMonthYearStub);
      
      // Check response structure
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
      expect(response.body.data).to.be.an('array');
      expect(response.body.data[0]).to.have.property('id');
      expect(response.body.data[0]).to.have.property('name');
      expect(response.body.data[0]).to.have.property('startDate');
      expect(response.body.data[0]).to.have.property('therapist');
    });
    
    it('should handle errors when retrieving monthly events', async function () {
      // Setup stubs to throw an error
      getAppointmentsByMonthYearStub.rejects(new Error('Database error'));
      
      const response = await request(app)
        .get('/events/monthly-events/detail?month=6&year=2025')
        .expect(HttpStatusCode.InternalServerError);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getAppointmentsByMonthYearStub);
      
      // Check response structure
      expect(response.body).to.have.property('error');
    });
  });
});
