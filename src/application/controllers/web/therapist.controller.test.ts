import sinon from 'sinon';
import express from 'express';
import { MainHeadingDeleteDeactivate, ProfileRejectionReason, User, UserDeleteDeactivateReason, UserRegistrationInfo } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import therapistController from '@/src/application/controllers/web/therapist.controller';
import * as userRepository from '@/src/application/repositories/user.repository';
import { mail } from '@/src/configs/sendgrid.config';
import { TherapistDeleteEmail } from '@/src/application/emails/therapist-delete.email';
import { TherapistDeleteEmailSentToAdmin } from '@/src/application/emails/therapist-deleted-send-to-admin';
import * as therapistRepository from '@/src/application/repositories/therapist.repository';
import { USER_DELETE } from './constants';
import * as therapistWaitlistRepository from '@/src/application/repositories/therapist-waitlist.repository';

const app = express();

app.use(express.json());
app.use(response);
app.use('/therapists', therapistController);

describe('Web Therapist Controller Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userFindOneStub: sinon.SinonStub;
	let userUpdateStub: sinon.SinonStub;
	let userRegInfoFindAllStub: sinon.SinonStub;
	let userRegInfoFindOneStub: sinon.SinonStub;
	let profileRejectionReasonFindAllStub: sinon.SinonStub;
	let mainHeadingDeleteDeactivateFindAllStub: sinon.SinonStub;
	let userDeleteDeactivateReasonFindAllStub: sinon.SinonStub;

	let transactionStub: sinon.SinonStub;
	let commitStub: sinon.SinonStub;
	let rollbackStub: sinon.SinonStub;

	let getTherapistsStub: sinon.SinonStub;
	let getTherapistByIdStub: sinon.SinonStub;
	let acceptTherapistStub: sinon.SinonStub;
	let rejectTherapistStub: sinon.SinonStub;
	let removePatientsFromThWaitlist: sinon.SinonStub;
	let getUserWithCalendarInfoStub: sinon.SinonStub;

	let therapistDeleteEmailCompileStub: sinon.SinonStub;
	let therapistDeleteEmailSentToAdminCompileStub: sinon.SinonStub;

	let mailSendStub: sinon.SinonStub;

	let dummyUser: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userFindOneStub = sandbox.stub(User, 'findOne');
		userUpdateStub = sandbox.stub(User, 'update');
		userRegInfoFindAllStub = sandbox.stub(UserRegistrationInfo, 'findAll');
		userRegInfoFindOneStub = sandbox.stub(UserRegistrationInfo, 'findOne');
		profileRejectionReasonFindAllStub = sandbox.stub(ProfileRejectionReason, 'findAll').resolves([]);
		mainHeadingDeleteDeactivateFindAllStub = sandbox.stub(MainHeadingDeleteDeactivate, 'findAll').resolves([]);
		userDeleteDeactivateReasonFindAllStub = sandbox.stub(UserDeleteDeactivateReason, 'findAll').resolves([]);

		commitStub = sinon.stub().resolves();
		rollbackStub = sinon.stub().resolves();

		const fakeTransaction = {
			commit: commitStub,
			rollback: rollbackStub,
		};
		transactionStub = sandbox.stub(User.sequelize as any, 'transaction').resolves(fakeTransaction);

		getTherapistsStub = sandbox.stub(therapistRepository, 'getTherapists');
		getTherapistByIdStub = sandbox.stub(therapistRepository, 'getTherapistById');
		acceptTherapistStub = sandbox.stub(therapistRepository, 'acceptTherapist');
		rejectTherapistStub = sandbox.stub(therapistRepository, 'rejectTherapist');
		removePatientsFromThWaitlist = sandbox.stub(therapistWaitlistRepository, 'removePatientsFromTherapistWaitlist');
		getUserWithCalendarInfoStub = sandbox.stub(userRepository, 'getUserWithCalendarInfo');

		therapistDeleteEmailCompileStub = sandbox.stub(TherapistDeleteEmail, 'compile');
		therapistDeleteEmailSentToAdminCompileStub = sandbox.stub(TherapistDeleteEmailSentToAdmin, 'compile');

		mailSendStub = sandbox.stub(mail, 'sendMail').resolves();
		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /therapists', function() {
		it('should return paginated therapists list successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			getTherapistsStub.resolves({
				count: 1,
				rows: [dummyUser]
			});

			const response = await request(app)
				.get('/therapists')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').to.be.an('array').of.length(1);
			expect(response.body).to.have.property('meta');
		});
	});

	describe('PUT /therapists/:id', function() {
		it('should return error if therapist is not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.put('/therapists/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'Therapist not found');
		});

		it('should update therapist successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				update: () => Promise.resolve()
			});

			const response = await request(app)
				.put('/therapists/1')
				.set('Authorization', 'Bearer accessToken')
				.send({
					first_name: 'John',
					last_name: 'Doe',
					dob: '1990-01-01',
					gender: 'male',
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('therapist').to.be.an('object');
		});
	});

	describe('ALL /therapists/reg-info', function() {
		it('should return error no page query or pages in body is provided', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/therapists/reg-info')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.BadRequest);
			expect(response.body).to.have.property('error', 'Either \'page\' query or \'pages\' in body must be provided.');
		});

		it('should return reg info of one page successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userRegInfoFindOneStub.resolves({
				id: 1,
				pageName: 'page=-one',
				payloadInfo: { info: 'some info' }
			});

			const response = await request(app)
				.get('/therapists/reg-info')
				.set('Authorization', 'Bearer accessToken')
				.query({ page: 'page-one' });

			expect(userRegInfoFindAllStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object');
		});

		it('should return reg info of multiple pages successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userRegInfoFindAllStub.resolves([
				{
					id: 1,
					pageName: 'page=-one',
					payloadInfo: { info: 'some info' }
				},
				{
					id: 2,
					pageName: 'page=-two',
					payloadInfo: { info: 'some info' }
				}
			]);

			const response = await request(app)
				.post('/therapists/reg-info')
				.set('Authorization', 'Bearer accessToken')
				.send({ pages: ['page-one', 'page-two'] });

			expect(userRegInfoFindOneStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('array').of.length(2);
		});
	});

	describe('GET /therapists/:id', function() {
		it('should return therapist successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			getTherapistByIdStub.resolves(dummyUser);

			const response = await request(app)
				.get('/therapists/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.be.an('object');
		});
	});

	describe('PATCH /therapists/accept/:id', function() {
		it('should accept therapist successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			acceptTherapistStub.resolves();

			const response = await request(app)
				.patch('/therapists/accept/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Therapist accepted');
		});
	});

	describe('PATCH /therapists/reject/:id', function() {
		it('should reject therapist successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			rejectTherapistStub.resolves();

			const response = await request(app)
				.patch('/therapists/reject/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'Therapist rejected');
		});
	});

	describe('DELETE /therapists/profile/:userId', function() {
		it('should not run if transaction is not available', async () => {
			userFindByPkStub.resolves(dummyUser);
			transactionStub.resolves(null);
	
			const response = await request(app)
				.delete('/therapists/profile/1')
				.set('Authorization', 'Bearer accessToken');
	
			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'Transaction could not be started');
		});

		it('should return error if delete operations fail', async function () {
			userFindByPkStub.resolves(dummyUser);
			userUpdateStub.rejects(new Error('Delete operation failed'));

			const response = await request(app)
				.delete('/therapists/profile/1')
				.set('Authorization', 'Bearer accessToken');

			expect(rollbackStub.calledOnce).to.be.true;
			expect(commitStub.notCalled).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'An error occurred while deleting the user');
		});

		it('should return error if email sending fails', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				toJSON: () => dummyUser,
			});
			userUpdateStub.resolves();
			removePatientsFromThWaitlist.resolves(1);
			therapistDeleteEmailCompileStub.returns({
				To: dummyUser.email,
				Subject: 'Therapist Account Deletion',
				HtmlBody: '<p>Your account has been deleted.</p>'
			});
			therapistDeleteEmailSentToAdminCompileStub.returns({
				To: '<EMAIL>',
				Subject: 'Therapist Account Deletion',
				HtmlBody: '<p>A therapist account has been deleted.</p>'
			});
			mailSendStub.rejects(new Error('Email sending failed'));

			const response = await request(app)
				.delete('/therapists/profile/1')
				.set('Authorization', 'Bearer accessToken')
				.query({
					deletedAt: new Date().toISOString(),
				});

			expect(rollbackStub.calledOnce).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.InternalServerError);
			expect(response.body).to.have.property('message', 'An error occurred while deleting the user');
		});

		it('should delete therapist successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				toJSON: () => dummyUser,
			});
			userUpdateStub.resolves();
			removePatientsFromThWaitlist.resolves(1);
			therapistDeleteEmailCompileStub.returns({
				To: dummyUser.email,
				Subject: 'Therapist Account Deletion',
				HtmlBody: '<p>Your account has been deleted.</p>'
			});
			therapistDeleteEmailSentToAdminCompileStub.returns({
				To: '<EMAIL>',
				Subject: 'Therapist Account Deletion',
				HtmlBody: '<p>A therapist account has been deleted.</p>'
			});

			const response = await request(app)
				.delete('/therapists/profile/1')
				.set('Authorization', 'Bearer accessToken')
				.query({
					deletedAt: new Date().toISOString(),
				});

			expect(rollbackStub.notCalled).to.be.true;
			expect(commitStub.calledOnce).to.be.true;
			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', USER_DELETE);
			expect(response.body).to.have.property('user').to.be.an('object');
		});
	});

	describe('PATCH /therapists/mfa', function() {
		it('should return error if non boolean value is provided for enable key', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.patch('/therapists/mfa')
				.set('Authorization', 'Bearer accessToken')
				.send({
					enable: 1,
				});

			expect(response.status).to.equal(HttpStatusCode.Forbidden);
			expect(response.body).to.have.property('message', 'Invalid request. Please provide a valid value for \'enable\'.');
		});

		it('should enable mfa successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userUpdateStub.resolves();
			getUserWithCalendarInfoStub.resolves(dummyUser);

			const response = await request(app)
				.patch('/therapists/mfa')
				.set('Authorization', 'Bearer accessToken')
				.send({
					enable: true,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Enabled MFA successfully.');
		});

		it('should disable mfa successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userUpdateStub.resolves();
			getUserWithCalendarInfoStub.resolves(dummyUser);

			const response = await request(app)
				.patch('/therapists/mfa')
				.set('Authorization', 'Bearer accessToken')
				.send({
					enable: false,
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'Disabled MFA successfully.');
		});
	});

	describe('GET /therapists/get-rejected-reasons/:id', function() {
		it('should return profile rejection reasons successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			profileRejectionReasonFindAllStub.resolves([
				{ id: 1, reason: 'Incomplete profile' },
				{ id: 2, reason: 'Invalid documents' }
			]);

			const response = await request(app)
				.get('/therapists/get-rejected-reasons/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('reasons').to.be.an('array').of.length(2);
		});
	});

	describe('PUT /therapists/restore-account/:id', function() {
		it('should return error if therapist not found', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves(null);

			const response = await request(app)
				.put('/therapists/restore-account/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should restore a therapist account successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				update: () => Promise.resolve()
			});

			const response = await request(app)
				.put('/therapists/restore-account/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'User restored successfully');
		});
	});

	describe('GET /therapists/reasons/deleted-deactivated', function() {
		it('should return profile deletion reasons with the type "deleted" and "deactivated" successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			mainHeadingDeleteDeactivateFindAllStub.resolves([
				{
					id: 1,
					heading: 'Profile Deletion',
					subheadings: [
						{ id: 1, subheading: 'Reason 1' },
						{ id: 2, subheading: 'Reason 2' },
					],
				},
				{
					id: 2,
					heading: 'Profile Deactivation',
					subheadings: [
						{ id: 3, subheading: 'Reason A' },
						{ id: 4, subheading: 'Reason B' },
					],
				}
			]);

			const response = await request(app)
				.get('/therapists/reasons/deleted-deactivated')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('reasons').to.be.an('array').of.length(2);
		});

		it('should restore a therapist account successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userFindOneStub.resolves({
				...dummyUser,
				update: () => Promise.resolve()
			});

			const response = await request(app)
				.put('/therapists/restore-account/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'User restored successfully');
		});
	});

	describe('GET /therapists/view-history/reasons/:id', function() {
		it('should return history of deleted, deactivated and rejected reasons successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			profileRejectionReasonFindAllStub.resolves([
				{ rejectedAt: new Date().toISOString(), reason: 'Incomplete profile' },
				{ rejectedAt: new Date().toISOString(), reason: 'Invalid documents' }
			]);
			userDeleteDeactivateReasonFindAllStub.resolves([
				{
					createdAt: new Date().toISOString(),
					type: 'deleted',
					reason: {
						subheading: 'User requested deletion',
					}
				},
				{
					createdAt: new Date().toISOString(),
					type: 'deactivated',
					reason: {
						subheading: 'User requested deactivation',
					}
				}
			]);

			const response = await request(app)
				.get('/therapists/view-history/reasons/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('reasons').to.be.an('array').of.length(4);
		});
	});
});