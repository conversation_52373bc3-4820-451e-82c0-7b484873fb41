import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import {
	createEvent,
	deleteEvent,
	getEventById,
	getEvents,
	updateEvent} from '@/src/application/repositories/event.respository'
import { getAppointmentsByMonthYear } from '@/src/application/repositories/appointments.repository'
import { HttpStatusCode } from 'axios'
import {
	createGoogleCalendarEvent,
	deleteGoogleCalendarEvent,
	updateGoogleCalendarEvent,
} from '@/src/configs/google.config'
import { getUserById } from '@/src/application/repositories/user.repository'
import APIMiddleware from '../../middlewares/api.middleware'

const router = express.Router()

/********************************
 * * Get all events
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const events = await getEvents({ req })
		res.send(paginatedData(events, req))
	})
)

/********************************
 * * Get event by id
 * * @param id
 * * @returns Event
 * * @throws EventNotFound
 ********************************/
router.get(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const event = await getEventById(req.params.id)
		res.send({ event })
	})
)

/****************************
 * * Create Event
 * @returns Event
 * @throws BadRequestError
 ***************************/
router.post(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const user = await getUserById(req.user?.id)
		const tokens = user.getGoogleCalendarTokens()

		const event = await createEvent(req)
		const eventData = {
			summary: event.name,
			description: event.description,
			start: {
				dateTime: event.startDate,
				timeZone: 'Asia/Kathmandu',
			},
			end: {
				dateTime: event.endDate,
				timeZone: 'Asia/Kathmandu',
			},
			reminders: {
				useDefault: false,
				overrides: [
					{ method: 'email', minutes: 24 * 60 },
					{ method: 'popup', minutes: 10 },
				],
			},
			attendees: [
				{
					email: '<EMAIL>',
				},
				{
					email: '<EMAIL>',
				},
			],
		} as any

		if (event.location) {
			eventData.location = event.location
		}

		const data = await createGoogleCalendarEvent(tokens, eventData)

		await event.update({
			calendarEventId: data.id,
		})

		res.status(HttpStatusCode.Created).send({
			message: 'Event created successfully',
			event,
		})
	})
)

/****************************
 * * Update Event
 * @returns Event
 * @throws BadRequestError
 * @throws NotFoundError
 ***************************/
router.put(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const user = await getUserById(req.user?.id)
		const tokens = user.getGoogleCalendarTokens()

		const event = await updateEvent(req)

		const eventData = {
			summary: event.name,
			description: event.description,
			start: {
				dateTime: event.startDate,
				timeZone: 'Asia/Kathmandu',
			},
			end: {
				dateTime: event.endDate,
				timeZone: 'Asia/Kathmandu',
			},
			reminders: {
				useDefault: false,
				overrides: [
					{ method: 'email', minutes: 24 * 60 },
					{ method: 'popup', minutes: 10 },
				],
			},
			attendees: [
				{
					email: '<EMAIL>',
				},
				{
					email: '<EMAIL>',
				},
			],
		} as any

		if (event.location) {
			eventData.location = event.location
		}

		const data = await updateGoogleCalendarEvent(
			tokens,
			event.calendarEventId,
			eventData
		)

		res.status(HttpStatusCode.Ok).send({
			message: 'Event updated successfully',
			event,
		})
	})
)

/****************************
 * * Delete Event
 * @param id
 * @throws NotFoundError
 * @returns Success Message
 ***************************/
router.delete(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const user = await getUserById(req.user?.id)
		const tokens = user.getGoogleCalendarTokens()

		const event = await getEventById(req.params.id)
		await deleteGoogleCalendarEvent(tokens, event.calendarEventId)
		await deleteEvent(req.params.id)
		res.send({
			message: 'Event deleted successfully',
		})
	})
)

// router.post(
// 	'/book-appointment',
// 	PortalMiddleware,
// 	wrap(async (req: Request, res: Response) => {
// 		const { therapistId, patientId, appointmentDate, appointmentTime, description } = req.body;

// 		if (!therapistId || !patientId || !appointmentDate || !appointmentTime || !description) {
// 			throw new Error('Therapist ID, Patient ID, appointmentDate, and appointmentTime are required.');
// 		}

// 		// Retrieve therapist and patient details
// 		const therapist = await getUserById(therapistId);
// 		const patient = await getUserById(patientId);

// 		const location = 'XYZ';

// 		if (!therapist) {
// 			return res.status(404).send({ message: 'Therapist not found.' });
// 		}

// 		if (!patient) {
// 			return res.status(404).send({ message: 'Patient not found.' });
// 		}

// 		const appointment = await createAppointments(req);

// 		// Fetch emails from the calendar table
// 		const therapistCalendar = await findCalendarByUserIdAndType(therapistId, 'google');
// 		const patientCalendar = await findCalendarByUserIdAndType(patientId, 'google');

// 		const therapistOutlookCalendar = await findCalendarByUserIdAndTypeOutlook(therapistId, 'outlook');
// 		const patientOutlookCalendar = await findCalendarByUserIdAndTypeOutlook(patientId, 'outlook');

// 		// Get attendee emails
// 		const attendees = [];
// 		if (therapistCalendar?.email || therapistOutlookCalendar?.email) {
// 			attendees.push({ email: therapistCalendar?.email || therapistOutlookCalendar?.email });
// 		}
// 		if (patientCalendar?.email || patientOutlookCalendar?.email) {
// 			attendees.push({ email: patientCalendar?.email || patientOutlookCalendar?.email });
// 		}

// 		// Prepare event data
// 		const eventData = {
// 			summary: `Therapy session with ${therapist.firstname} ${therapist.lastname}`,
// 			description: description || `Scheduled via NextTherapist`,
// 			start: { dateTime: `${appointmentDate}T${appointmentTime}+05:30`, timeZone: 'Asia/Kolkata' },
// 			end: { dateTime: `${appointmentDate}T${appointmentTime}+05:30`, timeZone: 'Asia/Kolkata' },
// 			location: 'xyz',
// 			attendees: attendees,
// 			reminders: {
// 				useDefault: false,
// 				overrides: [
// 					{ method: 'email', minutes: 24 * 60 },
// 					{ method: 'popup', minutes: 10 },
// 				],
// 			},
// 		};

// 		// Initialize placeholders for calendar events
// 		let therapistgoogleEvent = null;
// 		let patientgoogleEvent = null;
// 		let therapistOutlookEvent = null;
// 		let patientOutlookEvent = null;

// 		// Create Google or Outlook calendar events based on calendar type
// 		try {
// 			if (therapistCalendar || therapistOutlookCalendar) {
// 				if (therapistCalendar?.type === 'google') {
// 					therapistgoogleEvent = await createGoogleCalendarEvent(eventData);
// 				}
// 				if (therapistOutlookCalendar?.type === 'outlook') {
// 					therapistOutlookEvent = await createOutlookCalendarEvent(therapistOutlookCalendar.credentials, eventData);
// 				}
// 			}

// 			if (patientCalendar || patientOutlookCalendar) {
// 				if (patientCalendar?.type === 'google') {
// 					patientgoogleEvent = await createGoogleCalendarEvent(eventData);
// 				}
// 				if (patientOutlookCalendar?.type === 'outlook') {
// 					patientOutlookEvent = await createOutlookCalendarEvent(patientOutlookCalendar.credentials, eventData);
// 				}
// 			}
// 		} catch (calendarError) {
// 			if (calendarError instanceof Error) {
// 				console.error('Error creating calendar events:', calendarError.message);
// 			} else {
// 				console.error('Unexpected error during calendar event creation:', calendarError);
// 			}
// 		}


// 		res.status(HttpStatusCode.Created).send({
// 			message: 'Appointment scheduled successfully.',
// 			appointment: appointment,
// 			googleEvents: {
// 				therapist: therapistCalendar?.type === 'google' ? therapistgoogleEvent : null,
// 				patient: patientCalendar?.type === 'google' ? patientgoogleEvent : null,
// 			},
// 			outlookEvents: {
// 				therapist: therapistOutlookCalendar?.type === 'outlook' ? therapistOutlookEvent : null,
// 				patient: patientOutlookCalendar?.type === 'outlook' ? therapistOutlookEvent : null,
// 			},
// 		});
// 	})
// );

router.get(
    '/monthly-events/detail',
    APIMiddleware,
    wrap(async (req: Request, res: Response) => {
        const { month, year } = req.query;
        try {
            const appointmentsData = await getAppointmentsByMonthYear({
                req,
				perPage: req.query.perPage ? parseInt(req.query.perPage as string, 10) : undefined,
            });

		// Map and format the appointments data as required in the response
		const events = appointmentsData.rows.map((appointment: any) => ({
			cancelledAt : appointment.cancelledAt,
			id: appointment.id,
			name: appointment.name,
			startDate: appointment.appointmentDate,
			endDate: new Date(appointment.appointmentDate), // Assuming 1-hour duration
			location: appointment.location || 'Therapy Center', // Default location if not provided
			address: appointment.locationDetails || null, // Assuming locationDetails or address is available in the database
			therapist: appointment.therapist, // Includes therapist details
			appointmentTime: appointment.appointmentTime,
			minorPatient: appointment.minorPatient,
			isMinor:appointment.isMinor,
		}));

            res.send(paginatedData({ count: appointmentsData.count, rows: events }, req));
        } catch (error) {
			const errorMessage = error instanceof Error ? error.message : "Internal Server Error";
			res.status(500).send({ error: errorMessage });
		}
		
    })
);
  
export default router;




