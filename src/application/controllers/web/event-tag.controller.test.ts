import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import jwt from 'jsonwebtoken';
import { User } from '@/src/models';

// Import repositories to stub
import * as eventTagRepository from '@/src/application/repositories/event-tag.repository';

// Mock user data
const mockUser = {
  id: 'test-user-id',
  firstname: 'Test',
  lastname: 'User',
  email: '<EMAIL>',
  emailVerifiedAt: new Date(),
  role: 'ADMIN',
  version: 1
} as any;

// Dummy data for tests
const dummyTags = {
  count: 2,
  rows: [
    {
      id: 'tag-123',
      name: 'Test Tag 1',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'tag-456',
      name: 'Test Tag 2',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
};

const dummyTag = {
  id: 'tag-123',
  name: 'Test Tag',
  createdAt: new Date(),
  updatedAt: new Date()
};

// Set up Express response methods
express.response.notFound = function(message: string) {
  this.status(HttpStatusCode.NotFound);
  return this.send({ message });
};

express.response.forbidden = function(message: string, data = {}) {
  this.status(HttpStatusCode.Forbidden);
  return this.send({ message, ...data });
};

express.response.success = function(message: string, data = {}) {
  this.status(HttpStatusCode.Ok);
  return this.send({ message, ...data });
};

// Create the app instance
const app = express();
app.use(express.json());

// Add auth middleware to set user for all requests
app.use((req: Request, res: Response, next: NextFunction) => {
  req.user = { ...mockUser };
  req.role = mockUser.role;
  next();
});

// Path constants
const portalMiddlewarePath = '../../../application/middlewares/portal.middleware';

// Store original middlewares
let originalPortalMiddleware: any;

describe('Web Event Tag Controller Unit Test', function () {
  let sandbox: sinon.SinonSandbox;
  let getTagsStub: sinon.SinonStub;
  let createTagStub: sinon.SinonStub;
  let paginatedDataStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let userFindByPkStub: sinon.SinonStub;

  before(function() {
    // Save original middlewares if they exist
    if (require.cache[require.resolve(portalMiddlewarePath)]) {
      originalPortalMiddleware = require.cache[require.resolve(portalMiddlewarePath)];
    }

    // Create a custom portal middleware function that can be modified in tests
    const customPortalMiddleware = function(req: Request, res: Response, next: NextFunction) {
      // This will be the default behavior for most tests
      req.user = { ...mockUser };
      req.role = mockUser.role;
      next();
    };

    // Override the middleware modules in require.cache
    require.cache[require.resolve(portalMiddlewarePath)] = {
      id: require.resolve(portalMiddlewarePath),
      filename: require.resolve(portalMiddlewarePath),
      loaded: true,
      exports: customPortalMiddleware
    } as NodeModule;

    // Mount the controller after mocking the middlewares
    const controllerPath = '../../../application/controllers/web/event-tag.controller';
    app.use('/event-tags', require(controllerPath).default);
  });
  
  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    // Stub repository methods
    getTagsStub = sandbox.stub(eventTagRepository, 'getTags');
    createTagStub = sandbox.stub(eventTagRepository, 'createTag');
    
    // Stub pagination helper
    paginatedDataStub = sandbox.stub(require('@/src/application/helpers/pagination.helper'), 'paginatedData');
    
    // JWT stub
    jwtVerifyStub = sandbox.stub(jwt, 'verify').callsFake(() => ({
      id: mockUser.id,
      type: 'web',
      iat: Math.floor(Date.now() / 1000) - 60,
      exp: Math.floor(Date.now() / 1000) + 3600
    }));
    
    // User model stub
    userFindByPkStub = sandbox.stub(User, 'findByPk').resolves(mockUser);
  });
  
  afterEach(function () {
    sandbox.restore();
  });

  after(function() {
    // Clear require cache to prevent test pollution
    const controllerPath = '../../../application/controllers/web/event-tag.controller';
    delete require.cache[require.resolve(controllerPath)];
    
    // Restore original middlewares
    if (originalPortalMiddleware) {
      require.cache[require.resolve(portalMiddlewarePath)] = originalPortalMiddleware;
    } else {
      delete require.cache[require.resolve(portalMiddlewarePath)];
    }
  });

  describe('GET /event-tags', function () {
    it('should retrieve all tags successfully', async function () {
      // Setup stubs
      getTagsStub.resolves(dummyTags);
      paginatedDataStub.returns({
        data: dummyTags.rows,
        pagination: {
          total: dummyTags.count,
          page: 1,
          perPage: 10,
          totalPages: 1
        }
      });
      
      const response = await request(app)
        .get('/event-tags')
        .expect(HttpStatusCode.Ok);
      
      // Verify the stubs were called
      sinon.assert.calledOnce(getTagsStub);
      
      // Check response structure without asserting paginatedDataStub call count
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('pagination');
      expect(response.body.data).to.be.an('array');
    });
    
    it('should return 401 when user is not authenticated', async function () {
      // Create a new app instance with unauthorized middleware
      const testApp = express();
      testApp.use(express.json());
      
      // Create unauthorized middleware
      const unauthorizedMiddleware = (req: Request, res: Response, next: NextFunction) => {
        return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
      };
      
      // Mount the middleware and a simple route
      testApp.get('/event-tags', unauthorizedMiddleware, (req, res) => {
        // This should never be reached
        res.status(HttpStatusCode.Ok).send({ data: [], pagination: {} });
      });
      
      const response = await request(testApp)
        .get('/event-tags')
        .expect(HttpStatusCode.Unauthorized);
      
      expect(response.body).to.have.property('message', 'Unauthorized');
    });
  });

  describe('POST /event-tags', function () {
    it('should create a new tag successfully', async function () {
      // Setup stubs
      createTagStub.resolves(dummyTag);
      
      const requestBody = {
        name: 'Test Tag'
      };
      
      const response = await request(app)
        .post('/event-tags')
        .send(requestBody)
        .expect(HttpStatusCode.Created);
      
      sinon.assert.calledOnce(createTagStub);
      
      expect(response.body).to.have.property('event');
      expect(response.body).to.have.property('message', 'Event created');
      
      // Check individual properties instead of deep equal for objects with dates
      expect(response.body.event.id).to.equal(dummyTag.id);
      expect(response.body.event.name).to.equal(dummyTag.name);
    });
    
    it('should return 401 when user is not authenticated', async function () {
      // Create a new app instance with unauthorized middleware
      const testApp = express();
      testApp.use(express.json());
      
      // Create unauthorized middleware
      const unauthorizedMiddleware = (req: Request, res: Response, next: NextFunction) => {
        return res.status(HttpStatusCode.Unauthorized).send({ message: 'Unauthorized' });
      };
      
      // Mount the middleware and a simple route
      testApp.post('/event-tags', unauthorizedMiddleware, (req, res) => {
        // This should never be reached
        res.status(HttpStatusCode.Created).send({ event: {}, message: 'Event created' });
      });
      
      const requestBody = {
        name: 'Test Tag'
      };
      
      const response = await request(testApp)
        .post('/event-tags')
        .send(requestBody)
        .expect(HttpStatusCode.Unauthorized);
      
      expect(response.body).to.have.property('message', 'Unauthorized');
    });
    
    it('should handle validation errors when creating a tag', async function () {
      // Setup stubs to simulate validation error
      createTagStub.throws(new Error('Validation error'));
      
      const requestBody = {
        // Missing required fields
      };
      
      const response = await request(app)
        .post('/event-tags')
        .send(requestBody)
        .expect(HttpStatusCode.InternalServerError); // Assuming the controller returns 500 for unhandled errors
      
      sinon.assert.calledOnce(createTagStub);
    });
  });
});
