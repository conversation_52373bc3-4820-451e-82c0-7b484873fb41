import sinon from 'sinon';
import express from 'express';
import { User } from '@/src/models';
import { response } from '@/src/application/helpers/response.helper';
import jwt from 'jsonwebtoken';
import request from 'supertest';
import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import userController from '@/src/application/controllers/web/user.controller';
import * as userRepository from '@/src/application/repositories/user.repository';
import { mail } from '@/src/configs/sendgrid.config';
import { RegisterMemberEmail } from '@/src/application/emails/register.email'

const app = express();

app.use(express.json());
app.use(response);
app.use('/users', userController);

describe('Web User Controller Test', function () {
	let sandbox: sinon.SinonSandbox;

	let userFindByPkStub: sinon.SinonStub;
	let userCreateStub: sinon.SinonStub;

	let getUsersStub: sinon.SinonStub;
	let deleteUserStub: sinon.SinonStub;
	let activateUserStub: sinon.SinonStub;
	let deactivateUserStub: sinon.SinonStub;

	let registerMemberEmailCompileStub: sinon.SinonStub;

	let dummyUser: any;

	beforeEach(() => {
		sandbox = sinon.createSandbox();

		userFindByPkStub = sandbox.stub(User, 'findByPk');
		userCreateStub = sandbox.stub(User, 'create');

		getUsersStub = sandbox.stub(userRepository, 'getUsers');
		deleteUserStub = sandbox.stub(userRepository, 'deleteUser');
		activateUserStub = sandbox.stub(userRepository, 'activateUser');
		deactivateUserStub = sandbox.stub(userRepository, 'deactivateUser');

		registerMemberEmailCompileStub = sandbox.stub(RegisterMemberEmail, 'compile');

		sandbox.stub(mail, 'sendMail').resolves();
		sandbox.stub(jwt, 'verify').returns({
			id: '1',
			type: 'web',
			iat: Math.floor(Date.now() / 1000),
			exp: Math.floor(Date.now() / 1000) + 3600
		} as any);

		dummyUser = {
			id: 1,
			email: '<EMAIL>',
			role: 'therapist',
		}
	});

	afterEach(function() {
		sandbox.restore();
	});

	describe('GET /users', function() {
		it('should return paginated users list successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			getUsersStub.resolves({
				count: 1,
				rows: [dummyUser]
			});

			const response = await request(app)
				.get('/users')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('data').to.be.an('array').of.length(1);
			expect(response.body).to.have.property('meta');
		});
	});

	describe('GET /users/:id', function() {
		it('should return error if user is not found', async function () {
			userFindByPkStub.onFirstCall().resolves(dummyUser);
			userFindByPkStub.onSecondCall().resolves(null);

			const response = await request(app)
				.get('/users/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should return user successfully', async function () {
			userFindByPkStub.resolves(dummyUser);

			const response = await request(app)
				.get('/users/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('user').to.be.an('object');
		});
	});

	describe('POST /users', function() {
		it('should create user successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			userCreateStub.resolves({
				...dummyUser,
				toJSON: () => dummyUser,
			});
			registerMemberEmailCompileStub.returns({
				To: dummyUser.email,
				Subject: 'Welcome to the platform',
				HtmlBody: '<p>Welcome to the platform</p>'
			});

			const response = await request(app)
				.post('/users')
				.set('Authorization', 'Bearer accessToken')
				.send({
					firstname: 'John',
					lastname: 'Paul',
					email: '<EMAIL>',
					phone: '9845564564',
					dob: new Date().toISOString(),
				});

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'User created');
			expect(response.body).to.have.property('user').to.be.an('object');
		});
	});

	describe('PUT /users/:id', function() {
		it('should return error if user is not found', async function () {
			userFindByPkStub.onFirstCall().resolves(dummyUser);
			userFindByPkStub.onSecondCall().resolves(null);

			const response = await request(app)
				.put('/users/1')
				.set('Authorization', 'Bearer accessToken')
				.send({
					firstname: 'John',
					lastname: 'Paul',
					email: '<EMAIL>',
					phone: '9845564564',
					dob: new Date().toISOString(),
				});

			expect(response.status).to.equal(HttpStatusCode.NotFound);
			expect(response.body).to.have.property('message', 'User not found');
		});

		it('should update user successfully', async function () {
			userFindByPkStub.onFirstCall().resolves(dummyUser);
			userFindByPkStub.onSecondCall().resolves({
				...dummyUser,
				set: () => {},
				save: () => Promise.resolve(),
				reload: () => Promise.resolve(),
			});

			const response = await request(app)
				.put('/users/1')
				.set('Authorization', 'Bearer accessToken')
				.send({
					firstname: 'John',
					lastname: 'Paul',
					email: '<EMAIL>',
					phone: '9845564564',
					dob: new Date().toISOString(),
				});

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'User updated');
			expect(response.body).to.have.property('user').to.be.an('object');
		});
	});

	describe('DELETE /users/:id', function() {
		it('should delete user successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			deleteUserStub.resolves();

			const response = await request(app)
				.delete('/users/1')
				.set('Authorization', 'Bearer accessToken')
				.query({
					reasonId: 1,
					reason: 'User requested deletion',
					currentDate: new Date().toISOString(),
				});

			expect(response.status).to.equal(HttpStatusCode.Ok);
			expect(response.body).to.have.property('message', 'User deleted Successfully');
		});
	});

	describe('PATCH /users/activate/:id', function() {
		it('should activate user successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			activateUserStub.resolves();

			const response = await request(app)
				.patch('/users/activate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'User activated Successfully');
		});
	});

	describe('PATCH /users/deactivate/:id', function() {
		it('should deactivate user successfully', async function () {
			userFindByPkStub.resolves(dummyUser);
			deactivateUserStub.resolves();

			const response = await request(app)
				.patch('/users/deactivate/1')
				.set('Authorization', 'Bearer accessToken');

			expect(response.status).to.equal(HttpStatusCode.Created);
			expect(response.body).to.have.property('message', 'User deactivated Successfully');
		});
	});
});