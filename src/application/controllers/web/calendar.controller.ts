import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { HttpStatusCode } from 'axios'
import { getTokenFromCode, getUserProfile, revokeAccess } from '@/src/configs/google.config'
import { getAuthenticatedClient, getProfile } from '@/src/configs/outlook.config'
import axios from 'axios'
import { Calendar, User } from '@/src/models'
import { ForbiddenError, NotFoundError } from '../../handlers/errors'
import { getUserWithCalendarInfo } from '../../repositories/user.repository'
import { Op } from 'sequelize'
import logger from '@/src/configs/logger.config'


const router = express.Router()

/**
 * Create Google Calendar
 * @returns user with calendar info
 */
router.post(
	'/google',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { code, userToken } = req.body

		if (!code) throw new ForbiddenError('Code is required')
		if (!userToken) throw new ForbiddenError('User Token is required')

		logger.info(`Received Google sync request`);

		const user = await User.findOne({
			where: {
				userToken
			}
		})

		if (!user) throw new ForbiddenError('User not found')
	    logger.info(`User ${user.id} requested Google sync request.`);

		const tokens = await getTokenFromCode(code)
		const profile = await getUserProfile(tokens)

		// Check if the calendar email is already used by another user
		const existingCalendar = await Calendar.findOne({
			where: {
				type: 'google',
				email: profile.email,
				userId: {
					[Op.ne]: user.id,
				}
			}
		})
		if (existingCalendar) throw new ForbiddenError('Calendar with this email already exists. Please use a different email.')

		const credentials = {
			tokens,
			profile
		}

		// Check if the user has a calendar already
		const calendar = await Calendar.findOne({
			where: {
				type: 'google',
				userId: user.id,
			}
		})

		if (!calendar) {
			await Calendar.create({
				type: 'google',
				credentials: credentials,
				email: profile.email,
				userId: user.id,
			})
		} else {
			await calendar.update({
				credentials: credentials,
				email: profile.email,
			})
		}

		const userData = await getUserWithCalendarInfo(user.id)
		logger.info(`User ${user.id} Successfully synced Google Calendar.`);

		return res.status(HttpStatusCode.Created).send({ message: 'Google Calendar synced successfully', userData });
	})
);


/**
 * Remove Google Calendar and revoke access
 * @returns user with calendar info
 */
router.delete(
	'/google',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { userToken } = req.body;
		if (!userToken) throw new ForbiddenError('User Token is required')

		logger.info(`Received request to remove Google Calendar`);

		const user = await User.findOne({
			where: {
				userToken
			}
		})
		if (!user) throw new ForbiddenError('User not found')

		logger.info(`User ${user.id} requested removal of Google Calendar`);

		const userCalendar = await Calendar.findOne({
			where: {
				type: 'google',
				userId: user.id,
			}
		})

		if (!userCalendar) throw new NotFoundError('Google Calendar not found');

		const { tokens } = userCalendar?.credentials as any;
		if (!tokens) throw new NotFoundError('Required credentials not found. Please sync Google Calendar first.');

		try {
			await revokeAccess(tokens);
		} catch {
			// do nothing - this is required to prevent error from being thrown
		} finally {
			await userCalendar.destroy();
		}

		const userData = await getUserWithCalendarInfo(user.id)
		logger.info(`User ${user.id} Successfully removed Google Calendar`);

		return res.status(HttpStatusCode.Ok).send({ message: 'Google Calendar removed successfully.', userData });
	})
);

/**
 * Create Outlook Calendar
 * @returns user with calendar info
 */
router.post(
	'/outlook',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { authorizationCode, codeVerifier, userToken, redirectPath } = req.body;
		if (!authorizationCode || !codeVerifier || !userToken || !redirectPath) throw new ForbiddenError('Required parameters are missing')
		if (!userToken) throw new ForbiddenError('User Token is required')

		const user = await User.findOne({
			where: {
				userToken
			}
		})
		if (!user) throw new ForbiddenError('User not found')

		const tokenUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/token`;
    const clientId = process.env.AZURE_CLIENT_ID;
    const redirectUri = `${process.env.FRONTEND_URL}${redirectPath}`;
    const clientSecret = process.env.AZURE_CLIENT_SECRET;

    const body = {
      client_id: clientId,
      grant_type: 'authorization_code',
      code: authorizationCode,
      redirect_uri: redirectUri,
      code_verifier: codeVerifier,
      scope: 'openid profile offline_access User.Read Calendars.ReadWrite',
      client_secret: clientSecret,
    };

		const response = await axios.post(tokenUrl, body, {
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		});

		const graphClient = await getAuthenticatedClient(response.data.access_token);
		const profile = await getProfile(graphClient);

		// Check if the calendar email is already used by another user
		const existingCalendar = await Calendar.findOne({
			where: {
				type: 'outlook',
				email: profile.mail,
				userId: {
					[Op.ne]: user.id
				}
			}
		})
		if (existingCalendar) throw new ForbiddenError('Calendar with this email already exists. Please use a different email.')

		const credentials = {
			tokens: {
				...response.data,
				access_token_expiry: Math.floor(Date.now() / 1000) + response.data.expires_in,
			},
			profile
		}

		// Check if the user has a calendar already
		const calendar = await Calendar.findOne({
			where: {
				type: 'outlook',
				userId: user.id,
			}
		})

		if (!calendar) {
			await Calendar.create({
				type: 'outlook',
				credentials: credentials,
				email: profile.mail,
				userId: user.id,
			})
		} else {
			await calendar.update({
				credentials: credentials,
				email: profile.mail,
			})
		}

		const userData = await getUserWithCalendarInfo(user.id)
		logger.info(`User ${user.id} Successfully synced Outlook Calendar`);

		return res.status(HttpStatusCode.Created).send({ message: 'Outlook Calendar synced successfully', userData });
	})
);

/**
 * Remove Outlook Calendar and revoke access
 * @returns user with calendar info
 */
router.delete(
	'/outlook',
	// PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { userToken } = req.body;
		if (!userToken) throw new ForbiddenError('User Token is required')

		const user = await User.findOne({
			where: {
				userToken
			}
		})
		if (!user) throw new ForbiddenError('User not found')

		const userCalendar = await Calendar.findOne({
			where: {
				type: 'outlook',
				userId: user.id,
			}
		})

		if (!userCalendar) throw new NotFoundError('Outlook Calendar not found');

		// Delete Outlook Calendar Record
		await userCalendar.destroy();

		const userData = await getUserWithCalendarInfo(user.id)
		logger.info(`User ${user.id} Successfully removed Outlook Calendar`);

		return res.status(HttpStatusCode.Ok).send({ message: 'Outlook Calendar removed successfully.', userData });
	})
);

export default router
