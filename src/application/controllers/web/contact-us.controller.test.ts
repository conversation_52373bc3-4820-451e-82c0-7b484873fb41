import { expect } from 'chai';
import request from 'supertest';
import sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import { HttpStatusCode } from 'axios';
import contactUsController from '@/src/application/controllers/web/contact-us.controller';
import { response } from '@/src/application/helpers/response.helper';
import { User } from '@/src/models';
import logger from '@/src/configs/logger.config';
import * as sendgridConfig from '@/src/configs/sendgrid.config';
import { ContactUsEmail } from '@/src/application/emails/contact-us.email';

// Setup express app with middleware
const app = express();
app.use(express.json());
app.use(response);

// Add auth middleware to bypass authentication if needed
app.use((req: Request, res: Response, next: NextFunction) => {
  // Set up user for the request
  req.user = { 
    id: 'test-user-id',
    firstname: 'Test',
    lastname: 'User',
    email: '<EMAIL>',
    emailVerifiedAt: new Date(),
    role: 'USER'
  } as any;
  req.role = 'USER';
  req.headers.authorization = 'Bearer test-token';
  next();
});

// Mount the controller
app.use('/contact-us', contactUsController);

describe('Web Contact Us Controller Unit Test', function () {
  let sandbox: sinon.SinonSandbox;
  let userFindOneStub: sinon.SinonStub;
  let mailSendMailStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let contactUsEmailCompileStub: sinon.SinonStub;
  
  beforeEach(function () {
    sandbox = sinon.createSandbox();
    
    // Stubs for User model
    userFindOneStub = sandbox.stub(User, 'findOne');
    
    // Stubs for SendGrid mail
    mailSendMailStub = sandbox.stub(sendgridConfig.mail, 'sendMail');
    
    // Stubs for logger
    loggerInfoStub = sandbox.stub(logger, 'info');
    
    // Stubs for ContactUsEmail
    contactUsEmailCompileStub = sandbox.stub(ContactUsEmail, 'compile');
  });
  
  afterEach(function () {
    sandbox.restore();
  });
  
  describe('POST /contact-us', function () {
    it('should send contact us email successfully', async function () {
      // Setup stubs
      const testUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstname: 'Test',
        lastname: 'User'
      };
      
      const emailData = {
        To: '<EMAIL>',
        From: '<EMAIL>',
        Subject: 'Test Subject',
        HtmlBody: '<p>Test content</p>'
      };
      
      userFindOneStub.resolves(testUser);
      contactUsEmailCompileStub.returns(emailData);
      mailSendMailStub.resolves();
      
      const requestBody = {
        subject: 'Test Subject',
        reply_email: '<EMAIL>',
        email_content: 'Test content for contact us'
      };
      
      const response = await request(app)
        .post('/contact-us')
        .send(requestBody)
        .expect(HttpStatusCode.Ok);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(contactUsEmailCompileStub.calledOnce).to.be.true;
      expect(mailSendMailStub.calledOnce).to.be.true;
      expect(loggerInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Email has been sent successfully.');
      
      // Verify email data was passed correctly
      expect(contactUsEmailCompileStub.firstCall.args[0]).to.deep.equal({
        subject: 'Test Subject',
        reply_email: '<EMAIL>',
        email_content: 'Test content for contact us'
      });
      
      // Verify mail sending parameters
      expect(mailSendMailStub.firstCall.args[0]).to.deep.equal({
        to: emailData.To,
        from: emailData.From,
        subject: emailData.Subject,
        html: emailData.HtmlBody
      });
    });
    
    it('should handle non-existent user for reply email', async function () {
      // Setup stubs
      userFindOneStub.resolves(null);
      
      const emailData = {
        To: '<EMAIL>',
        From: '<EMAIL>',
        Subject: 'Test Subject',
        HtmlBody: '<p>Test content</p>'
      };
      
      contactUsEmailCompileStub.returns(emailData);
      mailSendMailStub.resolves();
      
      const requestBody = {
        subject: 'Test Subject',
        reply_email: '<EMAIL>',
        email_content: 'Test content for contact us'
      };
      
      const response = await request(app)
        .post('/contact-us')
        .send(requestBody)
        .expect(HttpStatusCode.Ok);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(contactUsEmailCompileStub.calledOnce).to.be.true;
      expect(mailSendMailStub.calledOnce).to.be.true;
      // Should still log but with undefined user id
      expect(loggerInfoStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Email has been sent successfully.');
    });
    
    it('should handle error when sending email fails', async function () {
      // Setup stubs
      userFindOneStub.resolves({
        id: 'user-123',
        email: '<EMAIL>'
      });
      
      const emailData = {
        To: '<EMAIL>',
        From: '<EMAIL>',
        Subject: 'Test Subject',
        HtmlBody: '<p>Test content</p>'
      };
      
      contactUsEmailCompileStub.returns(emailData);
      mailSendMailStub.rejects(new Error('Failed to send email'));
      
      const consoleErrorStub = sandbox.stub(console, 'error');
      
      const requestBody = {
        subject: 'Test Subject',
        reply_email: '<EMAIL>',
        email_content: 'Test content for contact us'
      };
      
      const response = await request(app)
        .post('/contact-us')
        .send(requestBody)
        .expect(HttpStatusCode.InternalServerError);
      
      expect(userFindOneStub.calledOnce).to.be.true;
      expect(contactUsEmailCompileStub.calledOnce).to.be.true;
      expect(mailSendMailStub.calledOnce).to.be.true;
      expect(consoleErrorStub.calledOnce).to.be.true;
      expect(response.body).to.have.property('message', 'Failed to send email.');
    });
  });
});