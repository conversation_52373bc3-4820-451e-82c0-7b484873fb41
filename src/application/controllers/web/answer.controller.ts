import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import { HttpStatusCode } from 'axios'
import { NotFoundError } from '@/src/application/handlers/errors'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import CacheMiddleware from '@/src/configs/cache.config'
import {
	createAnswer,
	deleteAnswer,
	getAnswerById,
	getAnswers,
	updateAnswer,
	updateAllSlug
} from '@/src/application/repositories/answer.repository'

const router = express.Router()

/********************************
 * * Get all answers
 ********************************/
router.get(
	'/',
	PortalMiddleware,
	// CacheMiddleware('answers'),
	wrap(async (req: Request, res: Response) => {
		const answers = await getAnswers({ req })
		res.send(paginatedData(answers, req))
	})
)

/********************************
 * * Get answer by id
 * * @param id
 * * @returns Answer
 * * @throws AnswerNotFound
 * *******************************/
router.get(
	'/:id',
	PortalMiddleware,
	CacheMiddleware('answers'),
	wrap(async (req: Request, res: Response) => {
		const answer = await getAnswerById(req.params.id)
		if (!answer) throw new NotFoundError('Answer not found')
		res.send({ answer, message: 'Answer found' })
	})
)

/********************************
 * * Create answer
 * * @returns Answer
 * *******************************/
router.post(
	'/',
	PortalMiddleware,
	CacheMiddleware('answers'),
	wrap(async (req: Request, res: Response) => {
		const answer = await createAnswer(req)
		res.status(HttpStatusCode.Created).send({
			answer,
			message: 'Answer created',
		})
	})
)

/********************************
 * * Update slug of all answers
 * * @param id
 * * @returns message
 * * @throws AnswerNotFound
 * *******************************/
router.put(
	'/updateAllSlug',
	PortalMiddleware,
	CacheMiddleware('answers'),
	wrap(async (_req: Request, res: Response) => {
		await updateAllSlug()
		res.status(HttpStatusCode.Created).send({
			message: 'All Answer Slugs updated',
		})
	})
)

/********************************
 * * Update answer
 * * @param id
 * * @returns Answer
 * * @throws AnswerNotFound
 * *******************************/
router.put(
	'/:id',
	PortalMiddleware,
	CacheMiddleware('answers'),
	wrap(async (req: Request, res: Response) => {
		const answer = await updateAnswer(req)
		res.status(HttpStatusCode.Created).send({
			answer,
			message: 'Answer updated',
		})
	})
)



/********************************
 * * Delete answer
 * * @param id
 * * @returns Answer
 * * @throws AnswerNotFound
 * *******************************/
router.delete(
	'/:id',
	PortalMiddleware,
	CacheMiddleware('answers'),
	wrap(async (req: Request, res: Response) => {
		await deleteAnswer(req.params.id)
		res.send({ message: 'Answer deleted' })
	})
)

export default router
