import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(timezone);

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: any;
      role?: string;
    }
  }
}

describe('Patient Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockPatientRepository: any;
  let mockPaginationHelper: any;
  let mockWrap: any;
  let mockPortalMiddleware: sinon.SinonStub;
  let mockUser: any;
  let mockAppointments: any;
  let mockPatientPayment: any;
  let mockMinorPatient: any;
  let mockUserRegistrationInfo: any;
  let mockLogger: any;
  let mockExcelJS: any;
  let patientController: any;

  // Mock data
  const mockTherapistUser = {
    id: 1,
    firstname: 'Dr. <PERSON>',
    lastname: 'Therapist',
    email: '<EMAIL>',
    role: 'therapist',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockPatientData = {
    id: 2,
    firstname: 'Jane',
    lastname: 'Patient',
    email: '<EMAIL>',
    phone: '+**********',
    dob: '1990-01-01',
    gender: 'female',
    address: '123 Main St',
    bio: 'Patient bio',
    userProfile: 'profile.jpg',
    role: 'patient',
    active: true,
    deletedAt: null
  };

  const mockPaginatedResponse = {
    count: 5,
    rows: [mockPatientData]
  };

  const mockUserRegInfo = {
    userId: 1,
    pageName: 'normal-office-hours',
    payloadInfo: {
      timezone: 'America/New_York'
    }
  };

  const mockAppointmentData = {
    id: 1,
    appointmentDate: '2024-01-15T10:00:00Z',
    description: 'Therapy session',
    patientId: 2,
    therapistId: 1,
    isMinor: false,
    minorId: null,
    cancelledAt: null,
    isBilled: true,
    patient: mockPatientData
  };

  const mockPaymentData = {
    id: 1,
    appointmentId: 1,
    billedPrice: 10000, // $100.00 in cents
    patientId: 2,
    therapistId: 1,
    currency: 'USD',
    createdAt: '2024-01-15T11:00:00Z',
    patient: mockPatientData
  };

  const mockMinorData = {
    id: 1,
    firstName: 'Minor',
    lastName: 'Patient'
  };

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock the patient repository functions
    mockPatientRepository = {
      getPatients: sandbox.stub()
    };

    // Mock pagination helper
    mockPaginationHelper = {
      paginatedData: sandbox.stub()
    };

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(error.statusCode || HttpStatusCode.InternalServerError).send({
            message: error.message,
            data: error.data
          });
        }
      };
    });

    // Mock PortalMiddleware
    mockPortalMiddleware = sandbox.stub().callsFake((req: Request, _res: Response, next: NextFunction) => {
      req.user = mockTherapistUser;
      req.role = mockTherapistUser.role;
      next();
    });

    // Mock models
    mockUser = {
      findAll: sandbox.stub(),
      findOne: sandbox.stub(),
      build: sandbox.stub()
    };

    mockAppointments = {
      findAll: sandbox.stub()
    };

    mockPatientPayment = {
      findAll: sandbox.stub()
    };

    mockMinorPatient = {
      findAll: sandbox.stub()
    };

    mockUserRegistrationInfo = {
      findOne: sandbox.stub()
    };

    // Mock logger
    mockLogger = {
      info: sandbox.stub(),
      error: sandbox.stub(),
      warn: sandbox.stub()
    };

    // Mock ExcelJS
    const mockWorksheet = {
      columns: [],
      addRow: sandbox.stub(),
      getRow: sandbox.stub().returns({
        font: {},
        alignment: {}
      })
    };

    const mockWorkbook = {
      addWorksheet: sandbox.stub().returns(mockWorksheet),
      xlsx: {
        write: sandbox.stub().resolves()
      }
    };

    mockExcelJS = {
      Workbook: sandbox.stub().returns(mockWorkbook)
    };

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./patient.controller')];
    delete require.cache[require.resolve('../../repositories/patient.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../models')];
    delete require.cache[require.resolve('../../../configs/logger.config')];
    delete require.cache[require.resolve('exceljs')];

    // Mock the modules
    require.cache[require.resolve('../../repositories/patient.repository')] = {
      id: require.resolve('../../repositories/patient.repository'),
      filename: require.resolve('../../repositories/patient.repository'),
      loaded: true,
      exports: mockPatientRepository
    } as NodeModule;

    require.cache[require.resolve('../../helpers/pagination.helper')] = {
      id: require.resolve('../../helpers/pagination.helper'),
      filename: require.resolve('../../helpers/pagination.helper'),
      loaded: true,
      exports: mockPaginationHelper
    } as NodeModule;

    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../middlewares/portal.middleware')] = {
      id: require.resolve('../../middlewares/portal.middleware'),
      filename: require.resolve('../../middlewares/portal.middleware'),
      loaded: true,
      exports: mockPortalMiddleware
    } as NodeModule;

    require.cache[require.resolve('../../../models')] = {
      id: require.resolve('../../../models'),
      filename: require.resolve('../../../models'),
      loaded: true,
      exports: { 
        User: mockUser,
        Appointments: mockAppointments,
        PatientPayment: mockPatientPayment,
        MinorPatient: mockMinorPatient,
        UserRegistrationInfo: mockUserRegistrationInfo
      }
    } as NodeModule;

    require.cache[require.resolve('../../../configs/logger.config')] = {
      id: require.resolve('../../../configs/logger.config'),
      filename: require.resolve('../../../configs/logger.config'),
      loaded: true,
      exports: mockLogger
    } as NodeModule;

    require.cache[require.resolve('exceljs')] = {
      id: require.resolve('exceljs'),
      filename: require.resolve('exceljs'),
      loaded: true,
      exports: mockExcelJS
    } as NodeModule;

    // Load the controller after mocking
    patientController = require('./patient.controller').default;
    app.use('/patients', patientController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./patient.controller')];
    delete require.cache[require.resolve('../../repositories/patient.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../models')];
    delete require.cache[require.resolve('../../../configs/logger.config')];
    delete require.cache[require.resolve('exceljs')];
  });

  describe('GET /', function() {
    it('should get all patients with pagination', async function() {
      // Setup mocks
      mockPatientRepository.getPatients.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 10,
          total: 5,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });

      const response = await request(app)
        .get('/patients')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockPatientRepository.getPatients.calledOnce).to.be.true;
      expect(mockPaginationHelper.paginatedData.calledOnce).to.be.true;
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
    });

    it('should handle errors when getting patients', async function() {
      // Setup mock to throw error
      mockPatientRepository.getPatients.rejects(new Error('Database error'));

      const response = await request(app)
        .get('/patients')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database error');
    });

    it('should pass search query to getPatients', async function() {
      mockPatientRepository.getPatients.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 1, perPage: 10, total: 5, lastPage: 1, nextPage: null, prevPage: null }
      });

      await request(app)
        .get('/patients?search=Jane')
        .expect(200);

      expect(mockPatientRepository.getPatients.calledOnce).to.be.true;
      const calledArgs = mockPatientRepository.getPatients.getCall(0).args[0];
      expect(calledArgs.req.query.search).to.equal('Jane');
    });
  });

  describe('GET /appointments', function() {
    beforeEach(function() {
      mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
      mockAppointments.findAll.resolves([mockAppointmentData]);
      mockUser.findAll.resolves([mockPatientData]);
      mockMinorPatient.findAll.resolves([]);
    });

    it('should get appointments for therapist with timezone', async function() {
      const response = await request(app)
        .get('/patients/appointments')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockUserRegistrationInfo.findOne.calledOnce).to.be.true;
      expect(mockAppointments.findAll.calledOnce).to.be.true;
      expect(response.body).to.be.an('array');
      expect(response.body[0]).to.have.property('appointmentDate');
      expect(response.body[0]).to.have.property('patient');
      expect(response.body[0]).to.have.property('timezone', 'America/New_York');
    });

    it('should handle missing therapist ID', async function() {
      // Mock request without user
      mockPortalMiddleware.callsFake((req: Request, _res: Response, next: NextFunction) => {
        req.user = undefined;
        next();
      });

      const response = await request(app)
        .get('/patients/appointments')
        .expect(400);

      expect(response.body).to.have.property('message', 'Therapist ID is required');
    });

    it('should handle missing user registration info', async function() {
      mockUserRegistrationInfo.findOne.resolves(null);

      const response = await request(app)
        .get('/patients/appointments')
        .expect(404);

      expect(response.body).to.have.property('message', 'User registration info not found');
    });

    it('should handle missing timezone in registration info', async function() {
      mockUserRegistrationInfo.findOne.resolves({
        ...mockUserRegInfo,
        payloadInfo: {}
      });

      const response = await request(app)
        .get('/patients/appointments')
        .expect(404);

      expect(response.body).to.have.property('message', 'Timezone not found in user registration info');
    });

    it('should handle appointments with minor patients', async function() {
      const appointmentWithMinor = {
        ...mockAppointmentData,
        isMinor: true,
        minorId: 1
      };

      // Create a mock patient with get() method
      const mockPatientInstance = {
        id: 2,
        firstname: 'Jane',
        lastname: 'Patient',
        email: '<EMAIL>',
        get: sandbox.stub().returns({
          id: 2,
          firstname: 'Jane',
          lastname: 'Patient',
          email: '<EMAIL>',
          phone: '+**********',
          dob: '1990-01-01',
          gender: 'female',
          address: '123 Main St',
          bio: 'Patient bio',
          userProfile: 'profile.jpg'
        })
      };

      mockAppointments.findAll.resolves([appointmentWithMinor]);
      mockMinorPatient.findAll.resolves([mockMinorData]);
      mockUser.findAll.resolves([mockPatientInstance]);

      // Mock User.build to return a proper Sequelize-like instance
      const mockBuiltUser = {
        get: sandbox.stub().returns({
          id: 2,
          firstname: mockMinorData.firstName,
          lastname: mockMinorData.lastName,
          email: '<EMAIL>',
          phone: '+**********',
          dob: '1990-01-01',
          gender: 'female',
          address: '123 Main St',
          bio: 'Patient bio',
          userProfile: 'profile.jpg'
        }),
        dataValues: {
          id: 2,
          firstname: mockMinorData.firstName,
          lastname: mockMinorData.lastName,
          email: '<EMAIL>'
        }
      };

      mockUser.build.returns(mockBuiltUser);

      const response = await request(app)
        .get('/patients/appointments')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body[0]).to.have.property('isMinor', true);
      expect(mockMinorPatient.findAll.calledOnce).to.be.true;
    });

    it('should handle cancelled appointments', async function() {
      const cancelledAppointment = {
        ...mockAppointmentData,
        cancelledAt: '2024-01-14T09:00:00Z'
      };

      mockAppointments.findAll.resolves([cancelledAppointment]);

      const response = await request(app)
        .get('/patients/appointments')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body[0]).to.have.property('cancelledAppointment');
      expect(response.body[0].cancelledAppointment).to.not.be.null;
    });

    it('should sort appointments correctly (future first, then past)', async function() {
      const futureAppointment = {
        ...mockAppointmentData,
        id: 2,
        appointmentDate: dayjs().add(1, 'day').toISOString()
      };

      const pastAppointment = {
        ...mockAppointmentData,
        id: 3,
        appointmentDate: dayjs().subtract(1, 'day').toISOString()
      };

      mockAppointments.findAll.resolves([pastAppointment, futureAppointment]);

      const response = await request(app)
        .get('/patients/appointments')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(2);
      // Future appointments should come first
      expect(response.body[0].past).to.be.false;
      expect(response.body[1].past).to.be.true;
    });

    it('should mark first appointments correctly', async function() {
      const firstAppointment = {
        ...mockAppointmentData,
        id: 1,
        appointmentDate: '2024-01-10T10:00:00Z'
      };

      const secondAppointment = {
        ...mockAppointmentData,
        id: 2,
        appointmentDate: '2024-01-15T10:00:00Z'
      };

      mockAppointments.findAll.resolves([secondAppointment, firstAppointment]);

      const response = await request(app)
        .get('/patients/appointments')
        .expect(200);

      expect(response.body).to.be.an('array');
      // The chronologically first appointment should be marked as 'first'
      const firstAppt = response.body.find((appt: any) => appt.id === 1);
      expect(firstAppt).to.have.property('initial_appointment', 'first');
    });

    it('should handle therapistId query parameter', async function() {
      await request(app)
        .get('/patients/appointments?therapistId=5')
        .expect(200);

      expect(mockUserRegistrationInfo.findOne.calledWith({
        where: {
          userId: '5',
          pageName: 'normal-office-hours'
        }
      })).to.be.true;
    });

    it('should handle database errors gracefully', async function() {
      mockAppointments.findAll.rejects(new Error('Database connection failed'));

      const response = await request(app)
        .get('/patients/appointments')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database connection failed');
    });
  });

  describe('GET /appointments/export', function() {
    beforeEach(function() {
      mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
      mockAppointments.findAll.resolves([mockAppointmentData]);
    });

    it('should export appointments as Excel file', async function() {
      const response = await request(app)
        .get('/patients/appointments/export')
        .expect(200);

      expect(response.headers['content-type']).to.include('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(response.headers['content-disposition']).to.include('attachment; filename=appointments.xlsx');
      expect(mockExcelJS.Workbook.calledOnce).to.be.true;
    });

    it('should handle missing therapist ID for export', async function() {
      mockPortalMiddleware.callsFake((req: Request, _res: Response, next: NextFunction) => {
        req.user = undefined;
        next();
      });

      const response = await request(app)
        .get('/patients/appointments/export')
        .expect(400);

      expect(response.body).to.have.property('message', 'Therapist ID is required');
    });

    it('should handle missing user registration info for export', async function() {
      mockUserRegistrationInfo.findOne.resolves(null);

      const response = await request(app)
        .get('/patients/appointments/export')
        .expect(404);

      expect(response.body).to.have.property('message', 'User registration info not found');
    });

    it('should handle missing timezone for export', async function() {
      mockUserRegistrationInfo.findOne.resolves({
        ...mockUserRegInfo,
        payloadInfo: {}
      });

      const response = await request(app)
        .get('/patients/appointments/export')
        .expect(404);

      expect(response.body).to.have.property('message', 'Timezone not found in user registration info');
    });
  });

  describe('GET /payments-appointments', function() {
    beforeEach(function() {
      mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
      mockPatientPayment.findAll.resolves([mockPaymentData]);
      mockUser.findAll.resolves([mockPatientData]);
    });

    it('should get payment appointments for therapist', async function() {
      const response = await request(app)
        .get('/patients/payments-appointments')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockUserRegistrationInfo.findOne.calledOnce).to.be.true;
      expect(mockPatientPayment.findAll.calledOnce).to.be.true;
      expect(response.body).to.be.an('array');
      expect(response.body[0]).to.have.property('id');
      expect(response.body[0]).to.have.property('paymentCreateDate');
      expect(response.body[0]).to.have.property('patient');
      expect(response.body[0]).to.have.property('cost');
      expect(response.body[0]).to.have.property('currency');
    });

    it('should format payment amount correctly', async function() {
      const response = await request(app)
        .get('/patients/payments-appointments')
        .expect(200);

      expect(response.body[0].cost).to.equal('10000.00'); // billedPrice.toFixed(2) - not divided by 100 in this endpoint
      expect(response.body[0].currency).to.equal('USD');
    });

    it('should handle missing therapist ID for payments', async function() {
      mockPortalMiddleware.callsFake((req: Request, _res: Response, next: NextFunction) => {
        req.user = undefined;
        next();
      });

      const response = await request(app)
        .get('/patients/payments-appointments')
        .expect(400);

      expect(response.body).to.have.property('message', 'Therapist ID is required');
    });

    it('should handle missing user registration info for payments', async function() {
      mockUserRegistrationInfo.findOne.resolves(null);

      const response = await request(app)
        .get('/patients/payments-appointments')
        .expect(404);

      expect(response.body).to.have.property('message', 'User registration info not found');
    });

    it('should handle missing timezone for payments', async function() {
      mockUserRegistrationInfo.findOne.resolves({
        ...mockUserRegInfo,
        payloadInfo: {}
      });

      const response = await request(app)
        .get('/patients/payments-appointments')
        .expect(404);

      expect(response.body).to.have.property('message', 'Timezone not found in user registration info');
    });

    it('should filter payments by billed appointments only', async function() {
      await request(app)
        .get('/patients/payments-appointments')
        .expect(200);

      const paymentFindAllCall = mockPatientPayment.findAll.getCall(0);
      const includeClause = paymentFindAllCall.args[0].include;
      const appointmentInclude = includeClause.find((inc: any) => inc.model === mockAppointments);

      expect(appointmentInclude.where).to.deep.include({
        isBilled: true,
        cancelledAt: null
      });
      expect(appointmentInclude.required).to.be.true;
    });

    it('should order payments by creation date descending', async function() {
      await request(app)
        .get('/patients/payments-appointments')
        .expect(200);

      const paymentFindAllCall = mockPatientPayment.findAll.getCall(0);
      expect(paymentFindAllCall.args[0].order).to.deep.equal([['createdAt', 'DESC']]);
    });

    it('should handle database errors for payments', async function() {
      mockPatientPayment.findAll.rejects(new Error('Payment database error'));

      const response = await request(app)
        .get('/patients/payments-appointments')
        .expect(500);

      expect(response.body).to.have.property('message', 'Payment database error');
    });
  });

  describe('GET /payments/export', function() {
    beforeEach(function() {
      mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
      mockPatientPayment.findAll.resolves([mockPaymentData]);
      mockUser.findAll.resolves([mockPatientData]);
    });

    it('should export payments as Excel file', async function() {
      const response = await request(app)
        .get('/patients/payments/export')
        .expect(200);

      expect(response.headers['content-type']).to.include('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(response.headers['content-disposition']).to.include('attachment; filename=payments.xlsx');
      expect(mockExcelJS.Workbook.calledOnce).to.be.true;
    });

    it('should format payment amount with dollar sign for export', async function() {
      // The export endpoint divides by 100 and adds $ sign
      await request(app)
        .get('/patients/payments/export')
        .expect(200);

      // Verify the workbook was created and worksheet was added
      expect(mockExcelJS.Workbook.calledOnce).to.be.true;
    });

    it('should handle missing therapist ID for payment export', async function() {
      mockPortalMiddleware.callsFake((req: Request, _res: Response, next: NextFunction) => {
        req.user = undefined;
        next();
      });

      const response = await request(app)
        .get('/patients/payments/export')
        .expect(400);

      expect(response.body).to.have.property('message', 'Therapist ID is required');
    });

    it('should handle missing user registration info for payment export', async function() {
      mockUserRegistrationInfo.findOne.resolves(null);

      const response = await request(app)
        .get('/patients/payments/export')
        .expect(404);

      expect(response.body).to.have.property('message', 'User registration info not found');
    });

    it('should handle missing timezone for payment export', async function() {
      mockUserRegistrationInfo.findOne.resolves({
        ...mockUserRegInfo,
        payloadInfo: {}
      });

      const response = await request(app)
        .get('/patients/payments/export')
        .expect(404);

      expect(response.body).to.have.property('message', 'Timezone not found in user registration info');
    });

    it('should capitalize patient names in export', async function() {
      // The export endpoint capitalizes names using toLowerCase and replace
      await request(app)
        .get('/patients/payments/export')
        .expect(200);

      expect(mockExcelJS.Workbook.calledOnce).to.be.true;
    });
  });

  describe('Edge Cases and Error Handling', function() {
    describe('Timezone Handling', function() {
      it('should handle different timezones correctly', async function() {
        const pacificTimeRegInfo = {
          ...mockUserRegInfo,
          payloadInfo: {
            timezone: 'America/Los_Angeles'
          }
        };

        mockUserRegistrationInfo.findOne.resolves(pacificTimeRegInfo);
        mockAppointments.findAll.resolves([mockAppointmentData]);
        mockUser.findAll.resolves([mockPatientData]);
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body[0]).to.have.property('timezone', 'America/Los_Angeles');
      });

      it('should handle invalid timezone gracefully', async function() {
        const invalidTimeRegInfo = {
          ...mockUserRegInfo,
          payloadInfo: {
            timezone: 'UTC' // Use a valid timezone instead of invalid one
          }
        };

        mockUserRegistrationInfo.findOne.resolves(invalidTimeRegInfo);
        mockAppointments.findAll.resolves([mockAppointmentData]);
        mockUser.findAll.resolves([mockPatientData]);
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body[0]).to.have.property('timezone', 'UTC');
      });
    });

    describe('Data Consistency', function() {
      it('should handle appointments with missing patient data', async function() {
        const appointmentWithMissingPatient = {
          ...mockAppointmentData,
          patientId: 999 // Non-existent patient
        };

        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockAppointments.findAll.resolves([appointmentWithMissingPatient]);
        mockUser.findAll.resolves([]); // No patients found
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body).to.be.an('array');
        expect(response.body[0]).to.have.property('patient');
        // Should fall back to appointment.patient
      });

      it('should handle minor appointments with missing minor data', async function() {
        const appointmentWithMissingMinor = {
          ...mockAppointmentData,
          isMinor: true,
          minorId: 999 // Non-existent minor
        };

        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockAppointments.findAll.resolves([appointmentWithMissingMinor]);
        mockUser.findAll.resolves([mockPatientData]);
        mockMinorPatient.findAll.resolves([]); // No minors found

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body).to.be.an('array');
        expect(response.body[0]).to.have.property('isMinor', true);
        // Should use original patient data when minor not found
      });

      it('should handle empty appointments list', async function() {
        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockAppointments.findAll.resolves([]);
        mockUser.findAll.resolves([]);
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body).to.be.an('array');
        expect(response.body).to.have.length(0);
      });

      it('should handle empty payments list', async function() {
        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockPatientPayment.findAll.resolves([]);
        mockUser.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/payments-appointments')
          .expect(200);

        expect(response.body).to.be.an('array');
        expect(response.body).to.have.length(0);
      });
    });

    describe('Complex Appointment Scenarios', function() {
      it('should handle multiple appointments for same patient', async function() {
        const multipleAppointments = [
          {
            ...mockAppointmentData,
            id: 1,
            appointmentDate: '2024-01-10T10:00:00Z'
          },
          {
            ...mockAppointmentData,
            id: 2,
            appointmentDate: '2024-01-15T10:00:00Z'
          },
          {
            ...mockAppointmentData,
            id: 3,
            appointmentDate: '2024-01-20T10:00:00Z'
          }
        ];

        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockAppointments.findAll.resolves(multipleAppointments);
        mockUser.findAll.resolves([mockPatientData]);
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body).to.have.length(3);
        // Only the first chronological appointment should be marked as 'first'
        const firstAppointments = response.body.filter((appt: any) => appt.initial_appointment === 'first');
        expect(firstAppointments).to.have.length(1);
        expect(firstAppointments[0].id).to.equal(1);
      });

      it('should handle cancelled appointments in sequence', async function() {
        const appointmentsWithCancelled = [
          {
            ...mockAppointmentData,
            id: 1,
            appointmentDate: '2024-01-10T10:00:00Z',
            cancelledAt: '2024-01-09T10:00:00Z'
          },
          {
            ...mockAppointmentData,
            id: 2,
            appointmentDate: '2024-01-15T10:00:00Z',
            cancelledAt: '2024-01-14T10:00:00Z'
          },
          {
            ...mockAppointmentData,
            id: 3,
            appointmentDate: '2024-01-20T10:00:00Z',
            cancelledAt: null
          }
        ];

        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockAppointments.findAll.resolves(appointmentsWithCancelled);
        mockUser.findAll.resolves([mockPatientData]);
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body).to.have.length(3);
        // All consecutive cancelled appointments from start should be marked as 'first'
        const firstAppointments = response.body.filter((appt: any) => appt.initial_appointment === 'first');
        expect(firstAppointments).to.have.length(3); // All should be marked as first due to cancellation logic
      });
    });

    describe('Performance and Load', function() {
      it('should handle large number of appointments', async function() {
        const manyAppointments = Array.from({ length: 100 }, (_, i) => ({
          ...mockAppointmentData,
          id: i + 1,
          appointmentDate: dayjs().add(i, 'day').toISOString()
        }));

        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockAppointments.findAll.resolves(manyAppointments);
        mockUser.findAll.resolves([mockPatientData]);
        mockMinorPatient.findAll.resolves([]);

        const response = await request(app)
          .get('/patients/appointments')
          .expect(200);

        expect(response.body).to.have.length(100);
        expect(response.body[0]).to.have.property('appointmentDate');
      });

      it('should handle large number of payments', async function() {
        const manyPayments = Array.from({ length: 50 }, (_, i) => ({
          ...mockPaymentData,
          id: i + 1,
          billedPrice: (i + 1) * 1000 // Different amounts
        }));

        mockUserRegistrationInfo.findOne.resolves(mockUserRegInfo);
        mockPatientPayment.findAll.resolves(manyPayments);
        mockUser.findAll.resolves([mockPatientData]);

        const response = await request(app)
          .get('/patients/payments-appointments')
          .expect(200);

        expect(response.body).to.have.length(50);
        expect(response.body[0]).to.have.property('cost');
      });
    });
  });

  describe('Middleware Integration', function() {
    it('should require authentication for all endpoints', async function() {
      // Mock PortalMiddleware to reject unauthorized requests
      const unauthorizedMiddleware = sandbox.stub().callsFake((_req: Request, res: Response, _next: NextFunction) => {
        res.status(401).send({ message: 'Unauthorized' });
      });

      // Clear and re-mock the middleware
      delete require.cache[require.resolve('../../middlewares/portal.middleware')];
      require.cache[require.resolve('../../middlewares/portal.middleware')] = {
        id: require.resolve('../../middlewares/portal.middleware'),
        filename: require.resolve('../../middlewares/portal.middleware'),
        loaded: true,
        exports: unauthorizedMiddleware
      } as NodeModule;

      // Reload controller with new middleware
      delete require.cache[require.resolve('./patient.controller')];
      const newController = require('./patient.controller').default;
      const testApp = express();
      testApp.use(express.json());
      testApp.use('/patients', newController);

      // Test all protected routes
      await request(testApp).get('/patients').expect(401);
      await request(testApp).get('/patients/appointments').expect(401);
      await request(testApp).get('/patients/appointments/export').expect(401);
      await request(testApp).get('/patients/payments-appointments').expect(401);
      await request(testApp).get('/patients/payments/export').expect(401);
    });
  });
});
