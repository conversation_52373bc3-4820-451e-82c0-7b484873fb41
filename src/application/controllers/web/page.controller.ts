import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import { paginatedData } from '@/src/application/helpers/pagination.helper'
import {
	createPage,
	deletePage,
	getAllPages,
	getPageById,
	matcherInvolvement,
	startPage,
	updatePage,
} from '@/src/application/repositories/page.repository'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { HttpStatusCode } from 'axios'
import { Page } from '@/src/models'

const router = express.Router()

/********************************
 * * Get all pages
 ********************************/
router.get(
	'/',
	wrap(async (req: Request, res: Response) => {
		const pages = await getAllPages(req)
		res.send(paginatedData(pages, req))
	})
)

/********************************
 * * Get page by id
 * * @param id
 * * @returns Page
 * *******************************/
router.get(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const page = await getPageById(req.params.id)
		res.send({ page })
	})
)

/********************************
 * * Create page
 * * @param id
 * * @returns Page
 * * @throws PageNotFound
 * *******************************/
router.post(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const page = await createPage(req)
		res.status(HttpStatusCode.Created).send({
			page,
			message: 'Page created',
		})
	})
)

/********************************
 * * Update page order
 * * @param id
 * * @returns Page
 * * @throws PageNotFound
 * *******************************/
router.post(
	'/sort/order',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const { data } = req.body
		await Promise.all(
			data.map(async ({ id, order, initialPage }: any, index: number) => {
				Page.update(
					{
						sortOrder: order,
						initialPage,
						buttonAction:
							index + 1 === data.length ? 'end' : 'next-page',
						nextPageId:
							index + 1 === data.length
								? null
								: data[index + 1].id,
					},
					{ where: { id } }
				)
			})
		)
		res.send({
			message: 'Page order updated.',
		})
	})
)

/********************************
 * * Update page
 * * @param id
 * * @returns Page
 * * @throws PageNotFound
 * *******************************/
router.put(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const page = await updatePage(req)
		res.status(HttpStatusCode.Created).send({
			page,
			message: 'Page updated',
		})
	})
)

/********************************
 * * Update initial page
 * * @param id
 * * @returns Page
 * * @throws PageNotFound
 * *******************************/
router.put(
	'/:id/start',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const page = await startPage(req)
		res.send({
			page,
			message: 'Page updated',
		})
	})
)

/********************************
 * * Update matcher involvement
 * * @param id
 * * @returns Page
 * * @throws PageNotFound
 * *******************************/
router.put(
	'/:id/matcher',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		const page = await matcherInvolvement(req)
		res.send({
			page,
			message: 'Page updated',
		})
	})
)

/********************************
 * * Delete page
 * * @param id
 * * @throws PageNotFound
 * *******************************/
router.delete(
	'/:id',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		await deletePage(req)
		res.send({
			message: 'Page deleted',
		})
	})
)

export default router
