import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';

describe('States Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockWrap: any;
  let mockState: any;
  let statesController: any;

  // Mock data
  const mockStatesData = [
    {
      id: 1,
      name: 'Alabama',
      abbrev: 'AL',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 2,
      name: 'Alaska',
      abbrev: 'AK',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 3,
      name: 'Arizona',
      abbrev: 'AZ',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 4,
      name: 'California',
      abbrev: 'CA',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 5,
      name: 'New York',
      abbrev: 'NY',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    }
  ];

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(500).send({
            message: error.message || 'Internal server error'
          });
        }
      };
    });

    // Mock State model
    mockState = {
      findAll: sandbox.stub()
    };

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./states.controller')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../../models')];

    // Mock the modules
    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../../models')] = {
      id: require.resolve('../../../models'),
      filename: require.resolve('../../../models'),
      loaded: true,
      exports: { State: mockState }
    } as NodeModule;

    // Load the controller after mocking
    statesController = require('./states.controller').default;
    app.use('/', statesController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./states.controller')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../../models')];
  });

  describe('GET /states', function() {
    it('should get all states successfully', async function() {
      // Setup mock to return only name and abbrev attributes
      const expectedStatesData = mockStatesData.map(state => ({
        name: state.name,
        abbrev: state.abbrev
      }));

      mockState.findAll.resolves(expectedStatesData);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(mockState.findAll.calledOnce).to.be.true;
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(5);
      expect(response.body[0]).to.have.property('name', 'Alabama');
      expect(response.body[0]).to.have.property('abbrev', 'AL');
      expect(response.body[0]).to.not.have.property('id');
      expect(response.body[0]).to.not.have.property('createdAt');
      expect(response.body[0]).to.not.have.property('updatedAt');
    });

    it('should verify correct query parameters are used', async function() {
      const expectedStatesData = mockStatesData.map(state => ({
        name: state.name,
        abbrev: state.abbrev
      }));

      mockState.findAll.resolves(expectedStatesData);

      await request(app)
        .get('/states')
        .expect(200);

      expect(mockState.findAll.calledOnce).to.be.true;
      const callArgs = mockState.findAll.getCall(0).args[0];
      
      // Verify only name and abbrev attributes are requested
      expect(callArgs.attributes).to.deep.equal(['name', 'abbrev']);
      
      // Verify ordering by name ASC
      expect(callArgs.order).to.deep.equal([['name', 'ASC']]);
    });

    it('should handle empty states list', async function() {
      mockState.findAll.resolves([]);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(0);
    });

    it('should handle database errors', async function() {
      mockState.findAll.rejects(new Error('Database connection failed'));

      const response = await request(app)
        .get('/states')
        .expect(500);

      expect(response.body).to.have.property('message', 'Internal server error');
    });

    it('should return states in alphabetical order', async function() {
      const unorderedStates = [
        { name: 'Wyoming', abbrev: 'WY' },
        { name: 'Alabama', abbrev: 'AL' },
        { name: 'California', abbrev: 'CA' },
        { name: 'Alaska', abbrev: 'AK' }
      ];

      // Mock should return ordered states (as if database did the ordering)
      const orderedStates = [
        { name: 'Alabama', abbrev: 'AL' },
        { name: 'Alaska', abbrev: 'AK' },
        { name: 'California', abbrev: 'CA' },
        { name: 'Wyoming', abbrev: 'WY' }
      ];

      mockState.findAll.resolves(orderedStates);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body[0].name).to.equal('Alabama');
      expect(response.body[1].name).to.equal('Alaska');
      expect(response.body[2].name).to.equal('California');
      expect(response.body[3].name).to.equal('Wyoming');
    });

    it('should handle large number of states', async function() {
      const manyStates = Array.from({ length: 100 }, (_, i) => ({
        name: `State ${i + 1}`,
        abbrev: `S${i.toString().padStart(2, '0')}`
      }));

      mockState.findAll.resolves(manyStates);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(100);
      expect(response.body[0]).to.have.property('name', 'State 1');
      expect(response.body[0]).to.have.property('abbrev', 'S00');
    });

    it('should handle states with special characters in names', async function() {
      const specialStates = [
        { name: "State with 'Apostrophe'", abbrev: 'SA' },
        { name: 'State with "Quotes"', abbrev: 'SQ' },
        { name: 'State with & Ampersand', abbrev: 'SA' },
        { name: 'State with - Dash', abbrev: 'SD' }
      ];

      mockState.findAll.resolves(specialStates);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(4);
      expect(response.body[0].name).to.include("'Apostrophe'");
      expect(response.body[1].name).to.include('"Quotes"');
      expect(response.body[2].name).to.include('& Ampersand');
      expect(response.body[3].name).to.include('- Dash');
    });

    it('should handle null or undefined values gracefully', async function() {
      const statesWithNulls = [
        { name: 'Valid State', abbrev: 'VS' },
        { name: null, abbrev: 'NS' },
        { name: 'Another State', abbrev: null },
        { name: undefined, abbrev: 'US' }
      ];

      mockState.findAll.resolves(statesWithNulls);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(4);
      expect(response.body[0]).to.have.property('name', 'Valid State');
      expect(response.body[1]).to.have.property('name', null);
      expect(response.body[2]).to.have.property('abbrev', null);
    });

    it('should not require authentication', async function() {
      // This test verifies that the endpoint is publicly accessible
      const expectedStatesData = mockStatesData.map(state => ({
        name: state.name,
        abbrev: state.abbrev
      }));

      mockState.findAll.resolves(expectedStatesData);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(5);
    });

    it('should handle concurrent requests', async function() {
      const expectedStatesData = mockStatesData.map(state => ({
        name: state.name,
        abbrev: state.abbrev
      }));

      mockState.findAll.resolves(expectedStatesData);

      // Make multiple concurrent requests
      const promises = Array.from({ length: 5 }, () => 
        request(app).get('/states')
      );

      const responses = await Promise.all(promises);

      responses.forEach(response => {
        expect(response.status).to.equal(200);
        expect(response.body).to.be.an('array');
        expect(response.body).to.have.length(5);
      });

      expect(mockState.findAll.callCount).to.equal(5);
    });

    it('should return correct content type', async function() {
      const expectedStatesData = mockStatesData.map(state => ({
        name: state.name,
        abbrev: state.abbrev
      }));

      mockState.findAll.resolves(expectedStatesData);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.headers['content-type']).to.include('application/json');
    });

    it('should handle very long state names', async function() {
      const longNameStates = [
        { 
          name: 'A'.repeat(255), 
          abbrev: 'LN' 
        },
        { 
          name: 'State with a very long name that exceeds normal expectations', 
          abbrev: 'VL' 
        }
      ];

      mockState.findAll.resolves(longNameStates);

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(2);
      expect(response.body[0].name).to.have.length(255);
    });
  });

  describe('Error Handling Edge Cases', function() {
    it('should handle malformed database response', async function() {
      // Mock returns non-array response
      mockState.findAll.resolves('invalid response');

      const response = await request(app)
        .get('/states')
        .expect(200);

      expect(response.body).to.equal('invalid response');
    });
  });
});
