import express, { Request, Response } from 'express'
import { wrap } from '@/src/application/helpers'
import PortalMiddleware from '@/src/application/middlewares/portal.middleware'
import { HttpStatusCode } from 'axios'
import { createMatchingAlgoResource, getAllMatchingAlgoResources } from '../../repositories/matching-algo.repository'

const router = express.Router()

/********************************
 * * Create matching algo resource
 * *******************************/
router.post(
	'/',
	PortalMiddleware,
	wrap(async (req: Request, res: Response) => {
		await createMatchingAlgoResource(req)
		res.status(HttpStatusCode.Created).send({
			message: 'Matching Algo Resource created',
		})
	})
)

/********************************
 * * Get all matching algo resources
 ********************************/
router.get(
	'/',
	wrap(async (req: Request, res: Response) => {
		const algoResources = await getAllMatchingAlgoResources(req)
		res.send(algoResources)
	})
)

export default router;
