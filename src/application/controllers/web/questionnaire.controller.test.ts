import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: any;
      role?: string;
    }
  }
}

describe('Questionnaire Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockQuestionnaireRepository: any;
  let mockPaginationHelper: any;
  let mockWrap: any;
  let mockPortalMiddleware: sinon.SinonStub;
  let mockCacheMiddleware: sinon.SinonStub;
  let mockQuestionnaire: any;
  let questionnaireController: any;

  // Mock data
  const mockAdminUser = {
    id: 1,
    firstname: 'Admin',
    lastname: 'User',
    email: '<EMAIL>',
    role: 'admin',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockQuestionnaireData = {
    id: 1,
    question: 'What is your primary concern?',
    info: 'This helps us understand your needs better',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  };

  const mockPaginatedResponse = {
    count: 3,
    rows: [mockQuestionnaireData]
  };

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock the questionnaire repository functions
    mockQuestionnaireRepository = {
      getQuestionnaires: sandbox.stub(),
      getQuestionnaireById: sandbox.stub(),
      createQuestionnaire: sandbox.stub(),
      updateQuestionnaire: sandbox.stub(),
      deleteQuestionnaire: sandbox.stub()
    };

    // Mock pagination helper
    mockPaginationHelper = {
      paginatedData: sandbox.stub()
    };

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(error.statusCode || HttpStatusCode.InternalServerError).send({
            message: error.message,
            data: error.data
          });
        }
      };
    });

    // Mock PortalMiddleware
    mockPortalMiddleware = sandbox.stub().callsFake((req: Request, _res: Response, next: NextFunction) => {
      req.user = mockAdminUser;
      req.role = mockAdminUser.role;
      next();
    });

    // Mock CacheMiddleware
    mockCacheMiddleware = sandbox.stub().callsFake((_cacheKey: string) => {
      return (_req: Request, _res: Response, next: NextFunction) => {
        next();
      };
    });

    // Mock Questionnaire model
    mockQuestionnaire = {
      update: sandbox.stub().resolves()
    };

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./questionnaire.controller')];
    delete require.cache[require.resolve('../../repositories/questionnaire.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../configs/cache.config')];
    delete require.cache[require.resolve('../../../models/questionnaire.model')];

    // Mock the modules
    require.cache[require.resolve('../../repositories/questionnaire.repository')] = {
      id: require.resolve('../../repositories/questionnaire.repository'),
      filename: require.resolve('../../repositories/questionnaire.repository'),
      loaded: true,
      exports: mockQuestionnaireRepository
    } as NodeModule;

    require.cache[require.resolve('../../helpers/pagination.helper')] = {
      id: require.resolve('../../helpers/pagination.helper'),
      filename: require.resolve('../../helpers/pagination.helper'),
      loaded: true,
      exports: mockPaginationHelper
    } as NodeModule;

    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../middlewares/portal.middleware')] = {
      id: require.resolve('../../middlewares/portal.middleware'),
      filename: require.resolve('../../middlewares/portal.middleware'),
      loaded: true,
      exports: mockPortalMiddleware
    } as NodeModule;

    require.cache[require.resolve('../../../configs/cache.config')] = {
      id: require.resolve('../../../configs/cache.config'),
      filename: require.resolve('../../../configs/cache.config'),
      loaded: true,
      exports: mockCacheMiddleware
    } as NodeModule;

    require.cache[require.resolve('../../../models/questionnaire.model')] = {
      id: require.resolve('../../../models/questionnaire.model'),
      filename: require.resolve('../../../models/questionnaire.model'),
      loaded: true,
      exports: mockQuestionnaire
    } as NodeModule;

    // Load the controller after mocking
    questionnaireController = require('./questionnaire.controller').default;
    app.use('/questionnaires', questionnaireController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./questionnaire.controller')];
    delete require.cache[require.resolve('../../repositories/questionnaire.repository')];
    delete require.cache[require.resolve('../../helpers/pagination.helper')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
    delete require.cache[require.resolve('../../../configs/cache.config')];
    delete require.cache[require.resolve('../../../models/questionnaire.model')];
  });

  describe('GET /', function() {
    it('should get all questionnaires with pagination', async function() {
      // Setup mocks
      mockQuestionnaireRepository.getQuestionnaires.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 20,
          total: 3,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      });

      const response = await request(app)
        .get('/questionnaires')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockQuestionnaireRepository.getQuestionnaires.calledOnce).to.be.true;
      expect(mockPaginationHelper.paginatedData.calledOnce).to.be.true;
      expect(response.body).to.have.property('data');
      expect(response.body).to.have.property('meta');
    });

    it('should handle search query parameter', async function() {
      mockQuestionnaireRepository.getQuestionnaires.resolves(mockPaginatedResponse);
      mockPaginationHelper.paginatedData.returns({
        data: mockPaginatedResponse.rows,
        meta: { currentPage: 1, perPage: 20, total: 3, lastPage: 1, nextPage: null, prevPage: null }
      });

      await request(app)
        .get('/questionnaires?search=concern')
        .expect(200);

      expect(mockQuestionnaireRepository.getQuestionnaires.calledOnce).to.be.true;
      const calledRequest = mockQuestionnaireRepository.getQuestionnaires.getCall(0).args[0];
      expect(calledRequest.query.search).to.equal('concern');
    });

    it('should handle errors when getting questionnaires', async function() {
      // Setup mock to throw error
      mockQuestionnaireRepository.getQuestionnaires.rejects(new Error('Database error'));

      const response = await request(app)
        .get('/questionnaires')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database error');
    });
  });

  describe('GET /:id', function() {
    it('should get questionnaire by id successfully', async function() {
      // Setup mock
      mockQuestionnaireRepository.getQuestionnaireById.resolves(mockQuestionnaireData);

      const response = await request(app)
        .get('/questionnaires/1')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockQuestionnaireRepository.getQuestionnaireById.calledWith('1')).to.be.true;
      expect(response.body).to.have.property('questionnaire');
      expect(response.body.questionnaire).to.deep.equal(mockQuestionnaireData);
    });

    it('should handle questionnaire not found', async function() {
      // Setup mock to throw NotFoundError
      const notFoundError = new Error('Questionnaire not found');
      notFoundError.name = 'NotFoundError';
      mockQuestionnaireRepository.getQuestionnaireById.rejects(notFoundError);

      const response = await request(app)
        .get('/questionnaires/999')
        .expect(500);

      expect(response.body).to.have.property('message', 'Questionnaire not found');
    });

    it('should handle invalid ID format', async function() {
      mockQuestionnaireRepository.getQuestionnaireById.rejects(new Error('Invalid ID format'));

      const response = await request(app)
        .get('/questionnaires/invalid-id')
        .expect(500);

      expect(response.body).to.have.property('message', 'Invalid ID format');
    });
  });

  describe('POST /', function() {
    const createQuestionnaireData = {
      question: 'How are you feeling today?',
      info: 'This helps us assess your current state'
    };

    it('should create questionnaire successfully', async function() {
      // Setup mock
      mockQuestionnaireRepository.createQuestionnaire.resolves(mockQuestionnaireData);

      const response = await request(app)
        .post('/questionnaires')
        .send(createQuestionnaireData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockQuestionnaireRepository.createQuestionnaire.calledOnce).to.be.true;
      expect(response.body).to.have.property('questionnaire');
      expect(response.body).to.have.property('message', 'Questionnaire created');
    });

    it('should handle missing required fields', async function() {
      const incompleteData = {
        info: 'Missing question field'
      };

      mockQuestionnaireRepository.createQuestionnaire.rejects(new Error('Question is required'));

      const response = await request(app)
        .post('/questionnaires')
        .send(incompleteData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Question is required');
    });

    it('should handle database errors during creation', async function() {
      mockQuestionnaireRepository.createQuestionnaire.rejects(new Error('Database connection failed'));

      const response = await request(app)
        .post('/questionnaires')
        .send(createQuestionnaireData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Database connection failed');
    });

    it('should create questionnaire with only question field', async function() {
      const minimalData = {
        question: 'Minimal questionnaire'
      };

      const createdQuestionnaire = {
        ...mockQuestionnaireData,
        question: 'Minimal questionnaire',
        info: null
      };

      mockQuestionnaireRepository.createQuestionnaire.resolves(createdQuestionnaire);

      const response = await request(app)
        .post('/questionnaires')
        .send(minimalData)
        .expect(201);

      expect(response.body).to.have.property('questionnaire');
      expect(response.body.questionnaire.question).to.equal('Minimal questionnaire');
    });
  });

  describe('POST /sort', function() {
    const sortData = {
      category: 'patient',
      '1': 1,
      '2': 2,
      '3': 3
    };

    it('should sort questionnaires successfully', async function() {
      mockQuestionnaire.update.resolves();

      const response = await request(app)
        .post('/questionnaires/sort')
        .send(sortData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockQuestionnaire.update.callCount).to.equal(3);
      expect(response.body).to.have.property('message', 'Questionnaire created');
    });

    it('should handle sort with different category', async function() {
      const therapistSortData = {
        category: 'therapist',
        '1': 3,
        '2': 1,
        '3': 2
      };

      mockQuestionnaire.update.resolves();

      const response = await request(app)
        .post('/questionnaires/sort')
        .send(therapistSortData)
        .expect(201);

      expect(mockQuestionnaire.update.callCount).to.equal(3);

      // Verify the correct sort field is being updated
      const firstCall = mockQuestionnaire.update.getCall(0);
      expect(firstCall.args[0]).to.have.property('therapistSort');
    });

    it('should handle empty sort data', async function() {
      const emptySortData = {
        category: 'patient'
      };

      const response = await request(app)
        .post('/questionnaires/sort')
        .send(emptySortData)
        .expect(201);

      expect(mockQuestionnaire.update.called).to.be.false;
      expect(response.body).to.have.property('message', 'Questionnaire created');
    });

    it('should handle sort update errors', async function() {
      mockQuestionnaire.update.rejects(new Error('Update failed'));

      const response = await request(app)
        .post('/questionnaires/sort')
        .send(sortData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Update failed');
    });

    it('should handle missing category in sort data', async function() {
      const invalidSortData = {
        '1': 1,
        '2': 2
      };

      // This should still work but create undefined sort fields
      mockQuestionnaire.update.resolves();

      const response = await request(app)
        .post('/questionnaires/sort')
        .send(invalidSortData)
        .expect(201);

      expect(mockQuestionnaire.update.callCount).to.equal(2);
    });
  });

  describe('PUT /:id', function() {
    const updateQuestionnaireData = {
      question: 'Updated question text',
      info: 'Updated info text'
    };

    it('should update questionnaire successfully', async function() {
      const updatedQuestionnaire = {
        ...mockQuestionnaireData,
        ...updateQuestionnaireData
      };

      mockQuestionnaireRepository.updateQuestionnaire.resolves(updatedQuestionnaire);

      const response = await request(app)
        .put('/questionnaires/1')
        .send(updateQuestionnaireData)
        .expect(201);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockQuestionnaireRepository.updateQuestionnaire.calledOnce).to.be.true;
      expect(response.body).to.have.property('questionnaire');
      expect(response.body).to.have.property('message', 'Questionnaire updated');
    });

    it('should handle questionnaire not found during update', async function() {
      const notFoundError = new Error('Questionnaire not found');
      notFoundError.name = 'NotFoundError';
      mockQuestionnaireRepository.updateQuestionnaire.rejects(notFoundError);

      const response = await request(app)
        .put('/questionnaires/999')
        .send(updateQuestionnaireData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Questionnaire not found');
    });

    it('should handle partial updates', async function() {
      const partialUpdateData = {
        question: 'Only updating question'
      };

      const partiallyUpdatedQuestionnaire = {
        ...mockQuestionnaireData,
        question: 'Only updating question'
      };

      mockQuestionnaireRepository.updateQuestionnaire.resolves(partiallyUpdatedQuestionnaire);

      const response = await request(app)
        .put('/questionnaires/1')
        .send(partialUpdateData)
        .expect(201);

      expect(response.body).to.have.property('questionnaire');
      expect(response.body.questionnaire.question).to.equal('Only updating question');
    });

    it('should handle update with empty data', async function() {
      mockQuestionnaireRepository.updateQuestionnaire.resolves(mockQuestionnaireData);

      const response = await request(app)
        .put('/questionnaires/1')
        .send({})
        .expect(201);

      expect(response.body).to.have.property('questionnaire');
    });

    it('should handle database errors during update', async function() {
      mockQuestionnaireRepository.updateQuestionnaire.rejects(new Error('Database update failed'));

      const response = await request(app)
        .put('/questionnaires/1')
        .send(updateQuestionnaireData)
        .expect(500);

      expect(response.body).to.have.property('message', 'Database update failed');
    });
  });

  describe('DELETE /:id', function() {
    it('should delete questionnaire successfully', async function() {
      mockQuestionnaireRepository.deleteQuestionnaire.resolves();

      const response = await request(app)
        .delete('/questionnaires/1')
        .expect(200);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockCacheMiddleware.calledWith('questionnaires')).to.be.true;
      expect(mockQuestionnaireRepository.deleteQuestionnaire.calledWith('1')).to.be.true;
      expect(response.body).to.have.property('message', 'Questionnaire deleted');
    });

    it('should handle questionnaire not found during deletion', async function() {
      const notFoundError = new Error('Questionnaire not found');
      notFoundError.name = 'NotFoundError';
      mockQuestionnaireRepository.deleteQuestionnaire.rejects(notFoundError);

      const response = await request(app)
        .delete('/questionnaires/999')
        .expect(500);

      expect(response.body).to.have.property('message', 'Questionnaire not found');
    });

    it('should handle database errors during deletion', async function() {
      mockQuestionnaireRepository.deleteQuestionnaire.rejects(new Error('Database deletion failed'));

      const response = await request(app)
        .delete('/questionnaires/1')
        .expect(500);

      expect(response.body).to.have.property('message', 'Database deletion failed');
    });

    it('should handle invalid ID format for deletion', async function() {
      mockQuestionnaireRepository.deleteQuestionnaire.rejects(new Error('Invalid ID format'));

      const response = await request(app)
        .delete('/questionnaires/invalid-id')
        .expect(500);

      expect(response.body).to.have.property('message', 'Invalid ID format');
    });
  });

  describe('Edge Cases and Error Handling', function() {
    describe('Input Validation', function() {
      it('should handle very long question text', async function() {
        const longQuestionData = {
          question: 'A'.repeat(1000),
          info: 'Long question test'
        };

        mockQuestionnaireRepository.createQuestionnaire.resolves({
          ...mockQuestionnaireData,
          question: longQuestionData.question
        });

        const response = await request(app)
          .post('/questionnaires')
          .send(longQuestionData)
          .expect(201);

        expect(response.body).to.have.property('questionnaire');
      });

      it('should handle special characters in question', async function() {
        const specialCharData = {
          question: 'What is your primary concern? (e.g., anxiety, depression, etc.)',
          info: 'Special chars: @#$%^&*()_+-=[]{}|;:,.<>?'
        };

        mockQuestionnaireRepository.createQuestionnaire.resolves({
          ...mockQuestionnaireData,
          ...specialCharData
        });

        const response = await request(app)
          .post('/questionnaires')
          .send(specialCharData)
          .expect(201);

        expect(response.body).to.have.property('questionnaire');
      });

      it('should handle null and undefined values', async function() {
        const nullData = {
          question: 'Valid question',
          info: null
        };

        mockQuestionnaireRepository.createQuestionnaire.resolves({
          ...mockQuestionnaireData,
          info: null
        });

        const response = await request(app)
          .post('/questionnaires')
          .send(nullData)
          .expect(201);

        expect(response.body).to.have.property('questionnaire');
      });
    });

    describe('Concurrent Operations', function() {
      it('should handle multiple simultaneous questionnaire retrievals', async function() {
        mockQuestionnaireRepository.getQuestionnaireById.resolves(mockQuestionnaireData);

        const promises = Array.from({ length: 5 }, (_, i) =>
          request(app).get(`/questionnaires/${i + 1}`)
        );

        const responses = await Promise.all(promises);

        responses.forEach(response => {
          expect(response.status).to.equal(200);
          expect(response.body).to.have.property('questionnaire');
        });

        expect(mockQuestionnaireRepository.getQuestionnaireById.callCount).to.equal(5);
      });

      it('should handle multiple simultaneous sort operations', async function() {
        mockQuestionnaire.update.resolves();

        const sortPromises = Array.from({ length: 3 }, (_, i) =>
          request(app)
            .post('/questionnaires/sort')
            .send({
              category: `category${i}`,
              '1': i + 1,
              '2': i + 2
            })
        );

        const responses = await Promise.all(sortPromises);

        responses.forEach(response => {
          expect(response.status).to.equal(201);
        });
      });
    });

    describe('Large Data Handling', function() {
      it('should handle large number of questionnaires', async function() {
        const manyQuestionnaires = Array.from({ length: 100 }, (_, i) => ({
          ...mockQuestionnaireData,
          id: i + 1,
          question: `Question ${i + 1}`
        }));

        const largePaginatedResponse = {
          count: 100,
          rows: manyQuestionnaires
        };

        mockQuestionnaireRepository.getQuestionnaires.resolves(largePaginatedResponse);
        mockPaginationHelper.paginatedData.returns({
          data: manyQuestionnaires,
          meta: {
            currentPage: 1,
            perPage: 20,
            total: 100,
            lastPage: 5,
            nextPage: 2,
            prevPage: null
          }
        });

        const response = await request(app)
          .get('/questionnaires')
          .expect(200);

        expect(response.body.data).to.have.length(100);
        expect(response.body.meta.total).to.equal(100);
      });

      it('should handle large sort operations', async function() {
        const largeSortData = {
          category: 'patient',
          ...Object.fromEntries(Array.from({ length: 50 }, (_, i) => [`${i + 1}`, i + 1]))
        };

        mockQuestionnaire.update.resolves();

        const response = await request(app)
          .post('/questionnaires/sort')
          .send(largeSortData)
          .expect(201);

        expect(mockQuestionnaire.update.callCount).to.equal(50);
        expect(response.body).to.have.property('message', 'Questionnaire created');
      });
    });
  });

  describe('Middleware Integration', function() {
    it('should require authentication for all endpoints', async function() {
      // Mock PortalMiddleware to reject unauthorized requests
      const unauthorizedMiddleware = sandbox.stub().callsFake((_req: Request, res: Response, _next: NextFunction) => {
        res.status(401).send({ message: 'Unauthorized' });
      });

      // Clear and re-mock the middleware
      delete require.cache[require.resolve('../../middlewares/portal.middleware')];
      require.cache[require.resolve('../../middlewares/portal.middleware')] = {
        id: require.resolve('../../middlewares/portal.middleware'),
        filename: require.resolve('../../middlewares/portal.middleware'),
        loaded: true,
        exports: unauthorizedMiddleware
      } as NodeModule;

      // Reload controller with new middleware
      delete require.cache[require.resolve('./questionnaire.controller')];
      const newController = require('./questionnaire.controller').default;
      const testApp = express();
      testApp.use(express.json());
      testApp.use('/questionnaires', newController);

      // Test all protected routes
      await request(testApp).get('/questionnaires').expect(401);
      await request(testApp).get('/questionnaires/1').expect(401);
      await request(testApp).post('/questionnaires').send({}).expect(401);
      await request(testApp).post('/questionnaires/sort').send({}).expect(401);
      await request(testApp).put('/questionnaires/1').send({}).expect(401);
      await request(testApp).delete('/questionnaires/1').expect(401);
    });

    it('should apply cache middleware only to delete endpoint', async function() {
      await request(app)
        .delete('/questionnaires/1')
        .expect(200);

      expect(mockCacheMiddleware.calledWith('questionnaires')).to.be.true;
    });
  });

  describe('Response Format Tests', function() {
    it('should return correct response format for GET /', async function() {
      mockQuestionnaireRepository.getQuestionnaires.resolves(mockPaginatedResponse);
      const expectedPaginatedData = {
        data: mockPaginatedResponse.rows,
        meta: {
          currentPage: 1,
          perPage: 20,
          total: 3,
          lastPage: 1,
          nextPage: null,
          prevPage: null
        }
      };
      mockPaginationHelper.paginatedData.returns(expectedPaginatedData);

      const response = await request(app)
        .get('/questionnaires')
        .expect(200);

      expect(response.body).to.deep.equal(expectedPaginatedData);
    });

    it('should return correct response format for GET /:id', async function() {
      mockQuestionnaireRepository.getQuestionnaireById.resolves(mockQuestionnaireData);

      const response = await request(app)
        .get('/questionnaires/1')
        .expect(200);

      expect(response.body).to.have.property('questionnaire');
      expect(response.body.questionnaire).to.deep.equal(mockQuestionnaireData);
    });

    it('should return correct response format for POST /', async function() {
      mockQuestionnaireRepository.createQuestionnaire.resolves(mockQuestionnaireData);

      const response = await request(app)
        .post('/questionnaires')
        .send({
          question: 'Test question',
          info: 'Test info'
        })
        .expect(201);

      expect(response.body).to.have.property('questionnaire');
      expect(response.body).to.have.property('message', 'Questionnaire created');
      expect(response.body.questionnaire).to.deep.equal(mockQuestionnaireData);
    });

    it('should return correct response format for DELETE /:id', async function() {
      mockQuestionnaireRepository.deleteQuestionnaire.resolves();

      const response = await request(app)
        .delete('/questionnaires/1')
        .expect(200);

      expect(response.body).to.have.property('message', 'Questionnaire deleted');
      expect(response.body).to.not.have.property('questionnaire');
    });
  });
});
