import { expect } from 'chai';
import * as sinon from 'sinon';
import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';

// Extend Express Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      user?: any;
      role?: string;
    }
  }
}

// Define interfaces for type safety
interface TherapistPage {
  id: number;
  therapistId: number;
  title: string;
  content: string;
  slug: string;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

describe('Therapist Pages Controller', function() {
  let app: express.Application;
  let sandbox: sinon.SinonSandbox;
  let mockTherapistPagesRepository: any;
  let mockWrap: any;
  let mockPortalMiddleware: sinon.SinonStub;
  let therapistPagesController: any;

  // Mock data with any type to avoid User interface conflicts
  const mockTherapistUser = {
    id: 1,
    firstname: 'John',
    lastname: 'Therapist',
    email: '<EMAIL>',
    role: 'therapist',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockAdminUser = {
    id: 2,
    firstname: 'Admin',
    lastname: 'User',
    email: '<EMAIL>',
    role: 'admin',
    emailVerifiedAt: new Date(),
    version: 1
  } as any;

  const mockTherapistPagesData: TherapistPage[] = [
    {
      id: 1,
      therapistId: 1,
      title: 'About Me',
      content: 'I am a licensed therapist with 10 years of experience.',
      slug: 'about-me',
      isPublished: true,
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z'
    },
    {
      id: 2,
      therapistId: 1,
      title: 'My Approach',
      content: 'I use cognitive behavioral therapy techniques.',
      slug: 'my-approach',
      isPublished: true,
      createdAt: '2024-01-02T00:00:00.000Z',
      updatedAt: '2024-01-02T00:00:00.000Z'
    }
  ];

  const mockEmptyPagesData: TherapistPage[] = [];

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    app = express();
    app.use(express.json());

    // Mock the therapist pages repository functions
    mockTherapistPagesRepository = {
      getTherapistPages: sandbox.stub()
    };

    // Mock wrap helper
    mockWrap = sandbox.stub().callsFake((callback: Function) => {
      return async (req: Request, res: Response, next: NextFunction) => {
        try {
          await callback(req, res, next);
        } catch (error: any) {
          res.status(error.statusCode || HttpStatusCode.InternalServerError).send({
            message: error.message,
            data: error.data
          });
        }
      };
    });

    // Mock PortalMiddleware
    mockPortalMiddleware = sandbox.stub().callsFake((req: Request, _res: Response, next: NextFunction) => {
      req.user = mockTherapistUser;
      req.role = mockTherapistUser.role;
      next();
    });

    // Clear require cache and mock modules
    delete require.cache[require.resolve('./therapist-pages.controller')];
    delete require.cache[require.resolve('../../repositories/therapist-pages.repository')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];

    // Mock the modules
    require.cache[require.resolve('../../repositories/therapist-pages.repository')] = {
      id: require.resolve('../../repositories/therapist-pages.repository'),
      filename: require.resolve('../../repositories/therapist-pages.repository'),
      loaded: true,
      exports: mockTherapistPagesRepository
    } as NodeModule;

    require.cache[require.resolve('../../helpers')] = {
      id: require.resolve('../../helpers'),
      filename: require.resolve('../../helpers'),
      loaded: true,
      exports: { wrap: mockWrap }
    } as NodeModule;

    require.cache[require.resolve('../../middlewares/portal.middleware')] = {
      id: require.resolve('../../middlewares/portal.middleware'),
      filename: require.resolve('../../middlewares/portal.middleware'),
      loaded: true,
      exports: mockPortalMiddleware
    } as NodeModule;

    // Load the controller after mocking
    therapistPagesController = require('./therapist-pages.controller').default;
    app.use('/therapist-pages', therapistPagesController);
  });

  afterEach(function() {
    sandbox.restore();
    // Clear require cache
    delete require.cache[require.resolve('./therapist-pages.controller')];
    delete require.cache[require.resolve('../../repositories/therapist-pages.repository')];
    delete require.cache[require.resolve('../../helpers')];
    delete require.cache[require.resolve('../../middlewares/portal.middleware')];
  });

  describe('GET /', function() {
    it('should get all therapist pages successfully', async function() {
      // Setup mocks
      mockTherapistPagesRepository.getTherapistPages.resolves(mockTherapistPagesData);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.Ok);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockTherapistPagesRepository.getTherapistPages.calledOnce).to.be.true;
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(2);
      expect(response.body[0]).to.have.property('id', 1);
      expect(response.body[0]).to.have.property('title', 'About Me');
      expect(response.body[1]).to.have.property('id', 2);
      expect(response.body[1]).to.have.property('title', 'My Approach');
    });

    it('should return empty array when no pages exist', async function() {
      // Setup mocks
      mockTherapistPagesRepository.getTherapistPages.resolves(mockEmptyPagesData);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.Ok);

      expect(mockPortalMiddleware.calledOnce).to.be.true;
      expect(mockTherapistPagesRepository.getTherapistPages.calledOnce).to.be.true;
      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(0);
    });

    it('should pass correct request object to repository', async function() {
      mockTherapistPagesRepository.getTherapistPages.resolves(mockTherapistPagesData);

      await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.Ok);

      const calledRequest = mockTherapistPagesRepository.getTherapistPages.getCall(0).args[0];
      expect(calledRequest.user).to.deep.equal(mockTherapistUser);
      expect(calledRequest.role).to.equal('therapist');
    });

    it('should handle query parameters', async function() {
      mockTherapistPagesRepository.getTherapistPages.resolves(mockTherapistPagesData);

      await request(app)
        .get('/therapist-pages?published=true&sort=title')
        .expect(HttpStatusCode.Ok);

      const calledRequest = mockTherapistPagesRepository.getTherapistPages.getCall(0).args[0];
      expect(calledRequest.query.published).to.equal('true');
      expect(calledRequest.query.sort).to.equal('title');
    });

    it('should handle search query parameter', async function() {
      mockTherapistPagesRepository.getTherapistPages.resolves(mockTherapistPagesData);

      await request(app)
        .get('/therapist-pages?search=therapy')
        .expect(HttpStatusCode.Ok);

      const calledRequest = mockTherapistPagesRepository.getTherapistPages.getCall(0).args[0];
      expect(calledRequest.query.search).to.equal('therapy');
    });

    it('should handle multiple query parameters', async function() {
      mockTherapistPagesRepository.getTherapistPages.resolves(mockTherapistPagesData);

      await request(app)
        .get('/therapist-pages?published=true&sort=title&search=therapy&limit=10')
        .expect(HttpStatusCode.Ok);

      const calledRequest = mockTherapistPagesRepository.getTherapistPages.getCall(0).args[0];
      expect(calledRequest.query.published).to.equal('true');
      expect(calledRequest.query.sort).to.equal('title');
      expect(calledRequest.query.search).to.equal('therapy');
      expect(calledRequest.query.limit).to.equal('10');
    });

    it('should handle database errors', async function() {
      // Setup mock to throw database error
      mockTherapistPagesRepository.getTherapistPages.rejects(new Error('Database connection failed'));

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Database connection failed');
    });

    it('should handle repository timeout errors', async function() {
      // Setup mock to throw timeout error
      const timeoutError = new Error('Query timeout');
      timeoutError.name = 'TimeoutError';
      mockTherapistPagesRepository.getTherapistPages.rejects(timeoutError);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Query timeout');
    });

    it('should handle unauthorized access', async function() {
      // Setup mock to throw unauthorized error
      const unauthorizedError = new Error('Access denied');
      unauthorizedError.name = 'UnauthorizedError';
      mockTherapistPagesRepository.getTherapistPages.rejects(unauthorizedError);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Access denied');
    });

    it('should handle not found errors', async function() {
      const notFoundError = new Error('Therapist pages not found');
      notFoundError.name = 'NotFoundError';
      mockTherapistPagesRepository.getTherapistPages.rejects(notFoundError);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.InternalServerError);

      expect(response.body).to.have.property('message', 'Therapist pages not found');
    });

    it('should handle large dataset response', async function() {
      // Create large dataset with proper typing
      const largeDataset: TherapistPage[] = Array.from({ length: 100 }, (_, i) => ({
        id: i + 1,
        therapistId: 1,
        title: `Page ${i + 1}`,
        content: `Content for page ${i + 1}`,
        slug: `page-${i + 1}`,
        isPublished: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      }));

      mockTherapistPagesRepository.getTherapistPages.resolves(largeDataset);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.Ok);

      expect(response.body).to.be.an('array');
      expect(response.body).to.have.length(100);
      expect(response.body[0]).to.have.property('title', 'Page 1');
      expect(response.body[99]).to.have.property('title', 'Page 100');
    });

    it('should handle pages with special characters in content', async function() {
      const specialCharPages: TherapistPage[] = [
        {
          id: 1,
          therapistId: 1,
          title: 'Special Characters & Symbols',
          content: 'Content with special chars: @#$%^&*()_+-=[]{}|;:,.<>?',
          slug: 'special-characters-symbols',
          isPublished: true,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z'
        }
      ];

      mockTherapistPagesRepository.getTherapistPages.resolves(specialCharPages);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.Ok);

      expect(response.body).to.be.an('array');
      expect(response.body[0]).to.have.property('title', 'Special Characters & Symbols');
      expect(response.body[0].content).to.include('@#$%^&*()_+-=[]{}|;:,.<>?');
    });

    it('should handle pages with long content', async function() {
      const longContentPages: TherapistPage[] = [
        {
          id: 1,
          therapistId: 1,
          title: 'Long Content Page',
          content: 'A'.repeat(5000),
          slug: 'long-content-page',
          isPublished: true,
          createdAt: '2024-01-01T00:00:00.000Z',
          updatedAt: '2024-01-01T00:00:00.000Z'
        }
      ];

      mockTherapistPagesRepository.getTherapistPages.resolves(longContentPages);

      const response = await request(app)
        .get('/therapist-pages')
        .expect(HttpStatusCode.Ok);

      expect(response.body).to.be.an('array');
      expect(response.body[0].content).to.have.length(5000);
    })
});
    });