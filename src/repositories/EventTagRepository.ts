import BaseRepository from "./BaseRepository";
import { User } from "@/types/user.interface";
import { HttpStatusCode } from "axios";
import { Event } from "@/types/event.interface";

class EventTagRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/event-tags", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(data: any): Promise<User | undefined> {
    try {
      const response = await this.client.post("/event-tags", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Event added successfully");
        return response.data.event;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(id: number, data: any): Promise<Event | undefined> {
    try {
      const response = await this.client.put(`/event-tags/${id}`, data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast(
          response.data.message ?? "Event updated successfully"
        );
        return response.data.event;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }
}

export default EventTagRepository;
