import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";

class MatchingAlgoRepository extends BaseRepository {
  async create(data: any): Promise<boolean | undefined> {
    try {
      const response = await this.client.post("/matching-algo", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Matching algo data added successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/matching-algo", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }
}

export default MatchingAlgoRepository;
