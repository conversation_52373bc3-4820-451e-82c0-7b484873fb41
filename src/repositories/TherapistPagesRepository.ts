import BaseRepository from "./BaseRepository";

class TherapistPagesRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/therapist-pages", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message || 
          "Something went wrong"
      );
    }
  }
}

export default TherapistPagesRepository;
