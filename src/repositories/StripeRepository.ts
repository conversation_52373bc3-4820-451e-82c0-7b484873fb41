import BaseRepository from "./BaseRepository";

class StripeRepository extends BaseRepository {
  async connect() {

    try {
      const { data } = await this.client.post("/stripe/subscribe", {
        data: {
          a: 10
        },
      });
      return data;
    } catch (error: any) {
      this.errorToast(error?.response?.data?.message || error?.message || "Something went wrong");
    }
  }
}

export default StripeRepository;
