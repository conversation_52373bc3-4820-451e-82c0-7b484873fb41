import BaseRepository from "./BaseRepository";
import { User } from "@/types/user.interface";
import { HttpStatusCode } from "axios";
import { Answer } from "@/types/answer.interface";

class AnswerRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/answers", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(error?.response?.data?.message || error?.message || "Something went wrong");
    }
  }

  async getOne(id: number) {
    try {
      const { data } = await this.client.get(`/answers/${id}`);
      return data.answer;
    } catch (error: any) {
      this.errorToast(error?.response?.data?.message || error?.message || "Something went wrong");
    }
  }

  async create(answer: any): Promise<User | undefined> {
    try {
      const response = await this.client.post("/answers", answer);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Answer added successfully");
        return response.data.answer;
      }
    } catch (error: any) {
      this.errorToast(error?.response?.data?.message || error?.message || "Something went wrong");
    }
    return undefined;
  }

  async update(id: number, answer: any): Promise<Answer | undefined> {
    try {
      const response = await this.client.put(`/answers/${id}`, answer);
      if (response.status === HttpStatusCode.Created) {
        this.successToast(response.data.message ?? "Answer updated successfully");
        return response.data.answer;
      }
    } catch (error: any) {
      this.errorToast(error?.response?.data?.message || error?.message || "Something went wrong");
    }
    return undefined;
  }

  async sortOrder(data: any): Promise<boolean> {
    try {
      const response = await this.client.post("/answers/sort", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Answer sorted successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast(error?.response?.data?.message || error?.message || "Something went wrong");
    }
    return false;
  }
}

export default AnswerRepository;
