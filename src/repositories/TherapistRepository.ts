import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";
import { RESTORE_SUCCESS_MESSAGE, RESTORE_ERROR_MESSAGE } from "@/pages/auth/constants";

class TherapistRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/therapists", {
        params,
      });     
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }
  
  async getDeleteReasons() {
    try {
      const { data } = await this.client.get("/therapists/reasons/deleted-deactivated");
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Failed to fetch delete reasons"
      );
      return { reasons: [] };
    }
  }

  async getDeactivateReasons() {
    try {
      const { data } = await this.client.get("/therapists/reasons/deleted-deactivated");
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Failed to fetch deactivate reasons"
      );
      return { reasons: [] };
    }
  }

  async create(user: FormData) {
    try {
      user.append("password", "default1");
      const { status }  = await this.client.post("/therapists", user, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (status === HttpStatusCode.Created) {
        this.successToast("User created successfully");
        return true;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async update(id: number, data: any) {
    try {
      const response = await this.client.put(`/therapists/${id}`, data);
      if (response.status === HttpStatusCode.Ok) {
        this.successToast(response.data.message ?? "Therapist updated successfully");
        return response;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async accept(id: number) {
    try {
      const response = await this.client.patch(`/therapists/accept/${id}`);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Therapist account accepted successfully");
        return true;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async reject(id: number, reason: string) {
    try {
      const response = await this.client.patch(`/therapists/reject/${id}`, { reason });
      if (response.status === HttpStatusCode.Created) {
        return true;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async rejectWithReasonId(id: number, reasonId: number) {
    try {
      // Fetch the reason text based on the ID
      const reasonsData = await this.getDeleteReasons();
      let reasonText = "";
      
      // Find the selected reason text from the reason groups
      if (reasonsData && reasonsData.reasons) {
        for (const group of reasonsData.reasons) {
          const subheading = group.subheadings.find((sh: any) => sh.id === reasonId);
          if (subheading) {
            reasonText = subheading.subheading;
            break;
          }
        }
      }
      
      if (!reasonText) {
        this.errorToast("Invalid reason selected");
        return { status: HttpStatusCode.BadRequest };
      }
      
      // Call the reject API
      const response = await this.client.patch(`/therapists/reject/${id}`, { reason: reasonText });
      
      if (response.status === HttpStatusCode.Created || response.status === HttpStatusCode.Ok) {
        // Return a response object that matches what DeleteDialog expects
        return {
          status: response.status,
          data: {
            message: "Therapist account rejected successfully"
          }
        };
      }
      
      return response;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return { status: HttpStatusCode.InternalServerError };
    }
  }

  async getOne(id: number) {
    try {
      const { data } = await this.client.get(`/therapists/${id}`);
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return null;
    }
  }

  async durationCost(data: any) {
    try {
      return await this.client.post(`/api/v1/therapists/duration-cost`,data);
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return null;
    }
  }

  async getdurationCost() {
    try {
      return await this.client.get(`/api/v1/therapists/duration-cost/detail`);
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return null;
    }
  }

  async getProfileRejectionReasons(therapistsId: number) {
    try {
      return await this.client.get(`/therapists/get-rejected-reasons/${therapistsId}`);
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async restore(therapistId: number): Promise<boolean> {
    try {
      const response = await this.client.put(`/therapists/restore-account/${therapistId}`, { therapistId });
      if (response.status === HttpStatusCode.Created || response.status === HttpStatusCode.Ok) {
        this.successToast(RESTORE_SUCCESS_MESSAGE);
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          RESTORE_ERROR_MESSAGE
      );
    }
    return false;
  }

  async getViewHistoryReasons(therapistsId: number) {
    try {
      return await this.client.get(`/therapists/view-history/reasons/${therapistsId}`);
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }


}

export default TherapistRepository;
