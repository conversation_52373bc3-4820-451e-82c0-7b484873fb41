import BaseRepository from "./BaseRepository";
import { User } from "@/types/user.interface";
import { HttpStatusCode } from "axios";
import { Speciality } from "@/types/speciality.interface";

class SpecialityRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/specialities", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(data: any): Promise<User | undefined> {
    try {
      const response = await this.client.post("/specialities", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Speciality added successfully");
        return response.data.speciality;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(id: number, data: any): Promise<Speciality | undefined> {
    try {
      const response = await this.client.put(`/specialities/${id}`, data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast(
          response.data.message ?? "Speciality updated successfully"
        );
        return response.data.speciality;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }
}

export default SpecialityRepository;
