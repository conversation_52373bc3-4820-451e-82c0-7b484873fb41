import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";
import { Event } from "@/types/event.interface";

class CalendarRepository extends BaseRepository {
  async create(data: any) {
    try {
      const response = await this.client.post("/calendars", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Calendar synced successfully");
        return response.data.user;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async syncGoogleCalendar(data: any) {
    try {
      const response = await this.client.post("/calendars/google", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Google Calendar synced successfully");
        return response;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async deleteGoogleCalendar() {
    try {
      const response = await this.client.delete("/calendars/google");
      if (response.status === HttpStatusCode.Ok) {
        this.successToast("Google Calendar removed successfully");
        return response;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async syncOutlookCalendar(data: any) {
    try {
      const response = await this.client.post("/calendars/microsoft", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Outlook Calendar synced successfully");
        return response.data.user;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(id: number, data: any): Promise<Event | undefined> {
    try {
      const response = await this.client.put(`/events/${id}`, data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast(
          response.data.message ?? "Event updated successfully"
        );
        return response.data.event;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async test(data: any) {
    try {
      const response = await this.client.post("/calendars/test", data);
      if (response.status === 200) {
        this.successToast("Calendar test successful");
        return response.data;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async getGoogleCalendarEvents() {
    try {
      const response = await this.client.get("/calendars/google/events");
      if (response.status === 200) {
        this.successToast("Google Calendar Events fetched successfully");
        return response.data;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async getOutlookCalendarEvents() {
    try {
      const response = await this.client.get("/calendars/outlook/events");
      if (response.status === 200) {
        this.successToast("Outlook Calendar Events fetched successfully");
        return response.data;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async testMicrosoft(data: any) {
    try {
      const response = await this.client.post("/calendars/test/outlook", data);
      if (response.status === 200) {
        this.successToast("Microsoft Calendar test successful");
        return response.data;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }
}

export default CalendarRepository;
