import { useApiClient } from "@/utils/api.util";
import notification from "@/utils/notification.util";

class BaseRepository {
  protected client = useApiClient();

  public successToast(message: string) {
    notification.success(message);
  }

  public errorToast(message: string) {
    notification.error(message);
  }

  public async delete(url: string) {
    return this.client.delete(url);
  }

  public async patch(url: string, data: any) {
    return this.client.patch(url, data);
  }

  public async post(url: string, data: any = {}) {
    return this.client.post(url, data);
  }

}

export default BaseRepository;
