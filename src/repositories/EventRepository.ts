import BaseRepository from "./BaseRepository";
import { User } from "@/types/user.interface";
import { HttpStatusCode } from "axios";
import { Event } from "@/types/event.interface";

class EventRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/events", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(data: any): Promise<User | undefined> {
    try {
      const response = await this.client.post("/events", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Event added successfully");
        return response.data.event;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(id: number, data: any): Promise<Event | undefined> {
    try {
      const response = await this.client.put(`/events/${id}`, data);
      if (response.status === HttpStatusCode.Ok) {
        this.successToast(
          response.data.message ?? "Event updated successfully"
        );
        return response.data.event;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async destroy(id: number): Promise<boolean> {
    try {
      await this.client.delete(`/events/${id}`);
      this.successToast("Event removed successfully");
      return true;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return false;
  }
}

export default EventRepository;
