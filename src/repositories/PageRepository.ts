import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";

class PageRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/pages", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async getOne(id: number) {
    try {
      const {
        data: { page },
      } = await this.client.get(`/pages/${id}`);
      return page;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(data: any): Promise<boolean | undefined> {
    try {
      const response = await this.client.post("/pages", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Page added successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(id: number, data: any): Promise<boolean | undefined> {
    try {
      const response = await this.client.put(`/pages/${id}`, data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast(response.data.message ?? "Page updated successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async startPage(id: number): Promise<boolean | undefined> {
    try {
      const response = await this.client.put(`/pages/${id}/start`);
      if (response.status === HttpStatusCode.Ok) {
        this.successToast(
          response.data.message ?? "Start page updated successfully"
        );
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async matcherInvolvement(id: number): Promise<boolean | undefined> {
    try {
      const response = await this.client.put(`/pages/${id}/matcher`);
      if (response.status === HttpStatusCode.Ok) {
        this.successToast(
          response.data.message ?? "Matcher Involvement updated successfully"
        );
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async updateOrder(formData: any) {
    try {
      const response = await this.client.post("/pages/sort/order", {
        data: formData,
      });
      if (response.status === HttpStatusCode.Ok) {
        this.successToast("Page order updated successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }
}

export default PageRepository;
