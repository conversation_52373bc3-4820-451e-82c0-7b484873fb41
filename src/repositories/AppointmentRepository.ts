import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";

class AppointmentRepository extends BaseRepository {
  async makeAppointment() {
    try {
      const response = await this.client.post("/appointment");
      if (response.status === HttpStatusCode.Created) {
        this.successToast(response.data.message);
        return true;
      } else if (response.status === HttpStatusCode.BadRequest) {
        this.errorToast(response.data.message);
        return false;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }
}

export default AppointmentRepository;
