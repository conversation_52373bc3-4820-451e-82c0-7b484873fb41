import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";
import { RESTORE_SUCCESS_MESSAGE, RESTORE_ERROR_MESSAGE } from "@/pages/auth/constants";

class PatientRepository extends BaseRepository {
  async getDeleteDeactivateReasons() {
    try {
      const { data } = await this.client.get("/patients/delete-deactivate/reasons");
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Failed to fetch delete/deactivate reasons"
      );
      return { reasons: [] };
    }
  }
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/patients", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(user: FormData) {
    try {
      user.append("password", "default1");
      const response = await this.client.post("/patients", user, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response.status === HttpStatusCode.Created) {
        this.successToast("User created successfully");
        return true;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async update(id: number, user: FormData) {
    try {
      user.append("password", "default1");
      const response = await this.client.put(`/patients/${id}`, user, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      if (response.status === HttpStatusCode.Created) {
        this.successToast(response.data.message ?? "User updated successfully");
        return true;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
      return false;
    }
  }

  async restore(patientId: number): Promise<boolean> {
    try {
      const response = await this.client.put(`/therapists/restore-account/${patientId}`);
      if (response.status === HttpStatusCode.Ok) {
        this.successToast(RESTORE_SUCCESS_MESSAGE);
        return true;
      }
      return false;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          RESTORE_ERROR_MESSAGE
      );
      return false;
    }
  }
}

export default PatientRepository;
