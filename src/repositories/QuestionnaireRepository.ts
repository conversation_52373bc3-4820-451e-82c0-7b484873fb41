import BaseRepository from "./BaseRepository";
import { HttpStatusCode } from "axios";
import { Questionnaire } from "@/types/questionnaire.interface";

class QuestionnaireRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/questionnaires", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async getOne(id: number) {
    try {
      const { data } = await this.client.get(`/questionnaires/${id}`);
      return data.questionnaire;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(questionnaire: any): Promise<Questionnaire | undefined> {
    try {
      const response = await this.client.post("/questionnaires", questionnaire);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Question added successfully");
        return response.data.questionnaire;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(
    id: number,
    questionnaire: any
  ): Promise<Questionnaire | undefined> {
    try {
      const response = await this.client.put(
        `/questionnaires/${id}`,
        questionnaire
      );
      if (response.status === HttpStatusCode.Created) {
        this.successToast(
          response.data.message ?? "Question updated successfully"
        );
        return response.data.questionnaire;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async sortOrder(data: any): Promise<boolean> {
    try {
      const response = await this.client.post("/questionnaires/sort", data);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Questionnaire sorted successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return false;
  }
}

export default QuestionnaireRepository;
