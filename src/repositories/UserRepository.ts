import BaseRepository from "./BaseRepository";
import { User } from "@/types/user.interface";
import { HttpStatusCode } from "axios";
import {RESTORE_SUCCESS_MESSAGE,RESTORE_ERROR_MESSAGE} from "@/pages/auth/constants";


class UserRepository extends BaseRepository {
  async getAll(params: any = {}) {
    try {
      const { data } = await this.client.get("/users", {
        params,
      });
      return data;
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
  }

  async create(user: any): Promise<User | undefined> {
    try {
      const response = await this.client.post("/users", user);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("User created successfully");
        return response.data.user;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async update(id: number, user: any): Promise<User | undefined> {
    try {
      const response = await this.client.put(`/users/${id}`, user);
      if (response.status === HttpStatusCode.Created) {
        this.successToast(response.data.message ?? "User updated successfully");
        return response.data.user;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Something went wrong"
      );
    }
    return undefined;
  }

  async activate(url: string): Promise<boolean> {
    try {
      const response = await this.client.patch(url);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("User Activation successful");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Activation failed"
      );
    }
    return false;
  }

  async deactivate(url: string): Promise<boolean> {
    try {
      const response = await this.client.patch(url);
      if (response.status === HttpStatusCode.Created) {
        this.successToast("User deactivation successful");
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          "Deactivation failed"
      );
    }
    return false;
  }

  async resetPassword(url: string, email: string): Promise<boolean> {
    try {
      const response =  await this.client.post(url, { email: email });
      if (response.status === HttpStatusCode.Created) {
        this.successToast("Password reset link sent successfully");
        return true;
      }
    } catch (error: any) {
      this.errorToast("Password reset failed");
    }
    return false;
  }

  async restore(userId: number): Promise<boolean> {
    try {
      const response = await this.client.put(`therapists/restore-account/${userId}`, { userId });
      if (response.status === HttpStatusCode.Created || response.status === HttpStatusCode.Ok) {
        this.successToast(RESTORE_SUCCESS_MESSAGE);
        return true;
      }
    } catch (error: any) {
      this.errorToast(
        error?.response?.data?.message ||
          error?.message ||
          RESTORE_ERROR_MESSAGE
      );
    }
    return false;
  }
}

export default UserRepository;
