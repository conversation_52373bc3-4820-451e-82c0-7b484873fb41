import { OptionType, StepInterface } from "@/src/types/registration.interface";

export const states = [
  { name: 'Alabama', abbrev: 'AL' },
  { name: 'Alaska', abbrev: 'AK' },
  { name: 'Arizona', abbrev: 'AZ' },
  { name: 'Arkansas', abbrev: 'AR' },
  { name: 'California', abbrev: 'CA' },
  { name: 'Colorado', abbrev: 'CO' },
  { name: 'Connecticut', abbrev: 'CT' },
  { name: 'Delaware', abbrev: 'DE' },
  { name: 'Florida', abbrev: 'FL' },
  { name: 'Georgia', abbrev: 'GA' },
  { name: 'Hawaii', abbrev: 'HI' },
  { name: 'Idaho', abbrev: 'ID' },
  { name: 'Illinois', abbrev: 'IL' },
  { name: 'Indiana', abbrev: 'IN' },
  { name: 'Iowa', abbrev: 'IA' },
  { name: 'Kansas', abbrev: 'KS' },
  { name: 'Kentucky', abbrev: 'KY' },
  { name: 'Louisiana', abbrev: 'LA' },
  { name: 'Maine', abbrev: 'ME' },
  { name: 'Maryland', abbrev: 'MD' },
  { name: 'Massachusetts', abbrev: 'MA' },
  { name: 'Michigan', abbrev: 'MI' },
  { name: 'Minnesota', abbrev: 'MN' },
  { name: 'Mississippi', abbrev: 'MS' },
  { name: 'Missouri', abbrev: 'MO' },
  { name: 'Montana', abbrev: 'MT' },
  { name: 'Nebraska', abbrev: 'NE' },
  { name: 'Nevada', abbrev: 'NV' },
  { name: 'New Hampshire', abbrev: 'NH' },
  { name: 'New Jersey', abbrev: 'NJ' },
  { name: 'New Mexico', abbrev: 'NM' },
  { name: 'New York', abbrev: 'NY' },
  { name: 'North Carolina', abbrev: 'NC' },
  { name: 'North Dakota', abbrev: 'ND' },
  { name: 'Ohio', abbrev: 'OH' },
  { name: 'Oklahoma', abbrev: 'OK' },
  { name: 'Oregon', abbrev: 'OR' },
  { name: 'Pennsylvania', abbrev: 'PA' },
  { name: 'Rhode Island', abbrev: 'RI' },
  { name: 'South Carolina', abbrev: 'SC' },
  { name: 'South Dakota', abbrev: 'SD' },
  { name: 'Tennessee', abbrev: 'TN' },
  { name: 'Texas', abbrev: 'TX' },
  { name: 'Utah', abbrev: 'UT' },
  { name: 'Vermont', abbrev: 'VT' },
  { name: 'Virginia', abbrev: 'VA' },
  { name: 'Washington', abbrev: 'WA' },
  { name: 'West Virginia', abbrev: 'WV' },
  { name: 'Wisconsin', abbrev: 'WI' },
  { name: 'Wyoming', abbrev: 'WY' }
];

export const appointmentDurations: OptionType[] = [
  {
    label: "30 Minutes",
    value: 30,
    name: "appointment_duration",
    id: "30_minutes",
  },
  {
    label: "60 Minutes",
    value: 60,
    name: "appointment_duration",
    id: "60_minutes",
  },
  {
    label: "90 Minutes",
    value: 90,
    name: "appointment_duration",
    id: "90_minutes",
  },
  {
    label: "120 Minutes",
    value: 120,
    name: "appointment_duration",
    id: "120_minutes",
  },
];

export const licenseType: OptionType[] = [
  {
    label: "Yes, under clinical supervision",
    value: "supervision",
    name: "license",
    reserved_page: "opt-supervision",
    id: "supervision",
  },
  {
    label: "Yes, fully licensed",
    value: "full",
    name: "license",
    reserved_page: "opt-fully-liscensed",
    id: "full",
  },
  {
    label: "No",
    value: "no",
    name: "license",
    id: "no",
    prevent_navigation: true,
  },
];

export const meRadioOptions: OptionType[] = [
  {
    label: "Most",
    value: "-2",
    id: "most_1",
    name: "radio_option_me",
  },
  {
    label: "Slightly More",
    value: "-1",
    id: "slightly_more_2",
    name: "radio_option_me",
  },
  {
    label: "Both Equally",
    value: "0",
    id: "both_equally_3",
    name: "radio_option_me",
  },
  {
    label: "Slightly More",
    value: "1",
    id: "slightly_more_4",
    name: "radio_option_me",
  },
  {
    label: "Most",
    value: "2",
    id: "most_5",
    name: "radio_option_me",
  },
];

export const appointmentNotifications: OptionType[] = [
  // {
  //   label: "App Notification",
  //   value: "app_notification",
  // },
  {
    label: "Email",
    value: "email",
  },
  {
    label: "Text",
    value: "text",
  },
];

export const primaryPracticeTypes: OptionType[] = [
  {
    label: "Private Individual Practice",
    value: "private_practice",
    name: "primary_practice",
    id: "private_practice",
  },
  {
    label: "Small Group of Therapists (2-15 in office)",
    value: "small_group",
    name: "primary_practice",
    id: "small_group",
  },
  {
    label: "Large Group (15+)",
    value: "large_group",
    name: "primary_practice",
    id: "large_group",
  },
  {
    label: "Corporate Group",
    value: "corporate_group",
    name: "primary_practice",
    id: "corporate_group",
  },
];

export const practiceFocus: OptionType[] = [
  {
    label: "Individual",
    value: "self",
    name: "practice_focus",
    id: "self",
  },
  {
    label: "Couple / Family",
    value: "couple",
    name: "practice_focus",
    id: "couple",
  },
  {
    label: "Child (aged 3-12)",
    value: "child",
    name: "practice_focus",
    id: "child",
  },
  {
    label: "Teen (aged 13-17)",
    value: "teen",
    name: "practice_focus",
    id: "teen",
  },
];

export const race: OptionType[] = [
  {
    label: "American Indian or Alaska Native",
    value: "american-indian-alaska",
    id: "american-indian-alaska",
  },
  {
    label: "Asian",
    value: "asian",
    id: "asian",
  },
  {
    label: "Black",
    value: "black",
    id: "black",
  },
  {
    label: "Caucasian",
    value: "caucasian",
    id: "caucasian",
  },
  {
    label: "Latino",
    value: "latino",
    id: "latino",
  },
  {
    label: "Pacific/Islander",
    value: "pacific-islander",
    id: "pacific-islander",
  },
  {
    label: "Other/Mixed",
    value: "other-mixed",
    id: "other-mixed",
  },
  {
    label: "Other (please specify)",
    value: "other",
    id: "other",
  },
];

export const fluentLanguage: OptionType[] = [
  {
    label: "Arabic",
    value: "arabic",
    id: "arabic",
  },
  {
    label: "ASL (American Sign Language)",
    value: "asl",
    id: "asl",
  },
  {
    label: "Chinese",
    value: "chinese",
    id: "chinese",
  },
  {
    label: "Spanish",
    value: "spanish",
    id: "spanish",
  },
  {
    label: "Tagalog",
    value: "tagalog",
    id: "tagalog",
  },
  {
    label: "Vietnamese",
    value: "vietnamese",
    id: "vietnamese",
  },
  {
    label: "Other (please specify)",
    value: "others",
    id: "others",
  },
];

export const religions: OptionType[] = [
  {
    label: "Aethism",
    value: "aethism",
    name: "religions",
    id: "aethism",
  },
  {
    label: "Buddhist",
    value: "buddhist",
    name: "religions",
    id: "buddhist",
  },
  {
    label: "Catholic",
    value: "catholic",
    name: "religions",
    id: "catholic",
  },
  {
    label: "Christian (Non-denominational)",
    value: "christian",
    name: "religions",
    id: "christian",
  },
  {
    label: "Church of Jesus Christ of Latter-day Saints (Mormon)",
    value: "mormon",
    name: "religions",
    id: "mormon",
  },
  {
    label: "Evangelical Protestant",
    value: "evangelical",
    name: "religions",
    id: "evangelical",
  },
  {
    label: "Hindu",
    value: "hindu",
    name: "religions",
    id: "hindu",
  },
  {
    label: "Jewish",
    value: "jewish",
    name: "religions",
    id: "jewish",
  },
  {
    label: "Muslim",
    value: "muslim",
    name: "religions",
    id: "muslim",
  },
  {
    label: "Protestant",
    value: "protestant",
    name: "religions",
    id: "protestant",
  },
  {
    label: "Spiritual/Not Religious",
    value: "spiritual",
    name: "religions",
    id: "spiritual",
  },
  {
    label: "Others (Please spicify)",
    value: "others",
    name: "religions",
    id: "others",
  },
];

export const genders: OptionType[] = [
  {
    label: "All Genders",
    value: "all",
    name: "gender",
    id: "all_gen",
  },
  {
    label: "Male",
    value: "male",
    name: "gender",
    id: "male",
  },
  {
    label: "Female",
    value: "female",
    name: "gender",
    id: "female",
  },
  {
    label: "Trans-Male",
    value: "trans-male",
    name: "gender",
    id: "trans-male",
  },
  {
    label: "Trans-Female",
    value: "trans-female",
    name: "gender",
    id: "trans-female",
  },
  {
    label: "Non-Binary",
    value: "non-binary",
    name: "gender",
    id: "non-binary",
  },
];

export const specialities: OptionType[] = [
  {
    label: "Depressive Disorders",
    value: "depression",
    name: "specialities",
    id: "supervision",
    children: [
      {
        label: "Major Depression",
        value: "major-depression",
        id: "major-depression",
        name: "major-depression"
      },
      {
        label: "Parental Depression",
        value: "parental-depression",
        id: "parental-depression",
        name: "parental-depression"
      },
    ]
  },
  {
    label: "Anxiety Disorders",
    value: "anxiety",
    name: "specialities",
    id: "anxiety",
    children: [
      {
        label: "Generalized Anxiety Disorder",
        value: "generalized-anxiety-disorder",
        id: "generalized-anxiety-disorder",
        name: "generalized-anxiety-disorder"
      },
      {
        label: "Social Anxiety",
        value: "social-anxiety",
        id: "social-anxiety",
        name: "social-anxiety"
      },
      {
        label: "Panic Disorder",
        value: "panic-disorder",
        id: "panic-disorder",
        name: "panic-disorder"
      },
      {
        label: "Phobias",
        value: "phobias",
        id: "phobias",
        name: "phobias"
      },
    ]
  },
  {
    label: "Adjustment Disorders",
    value: "adjustment",
    name: "specialities",
    id: "adjustment",
    children: [
      {
        label: "Stress/Trauma",
        value: "stress/trauma",
        id: "stress/trauma",
        name: "stress/trauma"
      }
    ]
  },
  {
    label: "Domestic Violence",
    value: "domestic",
    name: "specialities",
    id: "domestic",
    children: [
      {
        label: "Domestic Violence",
        value: "domestic-violence",
        id: "domestic-violence",
        name: "domestic-violence"
      }
    ]
  },
  {
    label: "Relationship Counseling",
    value: "relationship",
    name: "specialities",
    id: "relationship",
    children: [
      {
        label: "Couple/Marriage Relationship",
        value: "couple/marriage-relationship",
        name: "couple/marriage-relationship",
        id: "couple/marriage-relationship"
      },
      {
        label: "LGBTQ+",
        value: "lgbtq+",
        name: "lgbtq+",
        id: "lgbtq+"
      },
      {
        label: "Polyamorous",
        value: "polyamorous",
        name: "polyamorous",
        id: "polyamorous"
      },
      {
        label: "Family",
        value: "family",
        name: "family",
        id: "family"
      },
      {
        label: "Attachment/Dysfunction",
        value: "attachment/dysfunction",
        name: "attachment/dysfunction",
        id: "attachment/dysfunction"
      },
      {
        label: "Adoption",
        value: "adoption",
        name: "adoption",
        id: "adoption"
      },
      {
        label: "Foster Care",
        value: "foster-care",
        name: "foster-care",
        id: "foster-care"
      },
      {
        label: "Sex Therapy",
        value: "sex-therapy",
        name: "sex-therapy",
        id: "sex-therapy"
      },
      {
        label: "Codependency",
        value: "codependency",
        name: "codependency",
        id: "codependency"
      },
      {
        label: "Infertility",
        value: "infertility",
        name: "infertility",
        id: "infertility"
      },
      {
        label: "Birth/Adoption",
        value: "birth/adoption",
        name: "birth/adoption",
        id: "birth/adoption"
      }
    ]
  },
  {
    label: "Substance Related & Addictive Disorders",
    value: "substance",
    name: "specialities",
    id: "substance",
    children: [
      {
        label: "Substance Abuse",
        value: "substance-abuse",
        name: "substance-abuse",
        id: "substance-abuse"
      },
      {
        label: "Gambling",
        value: "gambling",
        name: "gambling",
        id: "gambling"
      },
      {
        label: "Pornography",
        value: "pornography",
        name: "pornography",
        id: "pornography"
      }
    ]
  },
  {
    label: "Eating Disorders",
    value: "eating",
    name: "specialities",
    id: "eating",
    children: [
      {
        label: "Binging",
        value: "binging",
        name: "binging",
        id: "binging"
      },
      {
        label: "Anorexia & Bulimia",
        value: "anorexia-bulimia",
        name: "anorexia-bulimia",
        id: "anorexia-bulimia"
      }
    ]
  },
  {
    label: "LGBTQ+ Affirming Care",
    value: "lgbtq",
    name: "specialities",
    id: "lgbtq",
    children: [
      {
        label: "LGBTQ+ Affirming Care",
        value: "lgbtq+-affirming-care",
        name: "lgbtq+-affirming-care",
        id: "lgbtq+-affirming-care"
      }
    ]
  },
  {
    label: "Gender Dysphoria",
    value: "gender",
    name: "specialities",
    id: "gender",
    children: [
      {
        label: "Gender Dysphoria",
        value: "gender-dysphoria",
        name: "gender-dysphoria",
        id: "gender-dysphoria"
      }
    ]
  },
  {
    label: "Trauma",
    value: "trauma",
    name: "specialities",
    id: "trauma",
    children: [
      {
        label: "PTSD",
        value: "ptsd",
        name: "ptsd",
        id: "ptsd"
      },
      {
        label: "Grief",
        value: "grief",
        name: "grief",
        id: "grief"
      },
      {
        label: "Multuculturalism",
        value: "multiculturalism",
        name: "multiculturalism",
        id: "multiculturalism"
      }
    ]
  },
  {
    label: "NSSID - Non-Suicidal Self-Injury Disorder",
    value: "nssid",
    name: "specialities",
    id: "nssid",
    children: [
      {
        label: "Self-Harm",
        value: "self-harm",
        name: "self-harm",
        id: "self-harm"
      }
    ]
  },
  {
    label: "Chronic Pain",
    value: "chronic",
    name: "specialities",
    id: "chronic",
    children: [
      {
        label: "Chronic Pain",
        value: "chronic-pain",
        name: "chronic-pain",
        id: "chronic-pain"
      }
    ]
  },
  {
    label: "Obsessive Compulsive Disorder",
    value: "obsessive",
    name: "specialities",
    id: "obsessive",
    children: [
      {
        label: "Obsessive Compulsive Disorder",
        value: "obsessive-compulsive-disorder",
        name: "obsessive-compulsive-disorder",
        id: "obsessive-compulsive-disorder"
      },
      {
        label: "Body Dysmorphic Disorder",
        value: "body-dysmorphic-disorder",
        name: "body-dysmorphic-disorder",
        id: "body-dysmorphic-disorder"
      },
      {
        label: "Hoarding",
        value: "hoarding",
        name: "hoarding",
        id: "hoarding"
      },
      {
        label: "Trichotillomania",
        value: "trichotillomania",
        name: "trichotillomania",
        id: "trichotillomania"
      },
      {
        label: "Excoriation",
        value: "excoriation",
        name: "excoriation",
        id: "excoriation"
      }
    ]
  },
  {
    label: "Neurodivergent Disorders",
    value: "neurodivergent",
    name: "specialities",
    id: "neurodivergent",
    children: [
      {
        label: "ADHD",
        value: "adhd",
        name: "adhd",
        id: "adhd"
      },
      {
        label: "Autism",
        value: "autism",
        name: "autism",
        id: "autism"
      }
    ]
  },
  {
    label: "Bipolar",
    value: "bipolar",
    name: "specialities",
    id: "bipolar",
    children: [
      {
        label: "Bipolar",
        value: "bipolar",
        name: "bipolar",
        id: "bipolar"
      }
    ]
  },
  {
    label: "Paranoia",
    value: "paranoia",
    name: "specialities",
    id: "paranoia",
    children: [
      {
        label: "Paranoia",
        value: "paranoia",
        name: "paranoia",
        id: "paranoia"
      }
    ]
  },
  {
    label: "Schizophrenia",
    value: "schizophrenia",
    name: "specialities",
    id: "schizophrenia",
    children: [
      {
        label: "Schizophrenia",
        value: "schizophrenia",
        name: "schizophrenia",
        id: "schizoprenia"
      }
    ]
  },
  {
    label: "Personality Disorders",
    value: "personality",
    name: "specialities",
    id: "personality",
    children: [
      {
        label: "Antisocial Personality",
        value: "antisocial-personality",
        name: "antisocial-personality",
        id: "antisocial-personality"
      }
    ]
  },
];

export const areaOfFocus: OptionType[] = [
  {
    label: "Women's Issues",
    value: "women-issues",
    name: "area_of_focus",
    id: "women-issues",
  },
  {
    label: "Men's Issues",
    value: "men-issues",
    name: "area_of_focus",
    id: "men-issues",
  },
  {
    label: "Faith Crisis",
    value: "faith-crisis",
    name: "area_of_focus",
    id: "faith-crisis",
  },
  {
    label: "Adjustment Disorder",
    value: "adjustment-disorder",
    name: "area_of_focus",
    id: "adjustment-disorder",
  },
  {
    label: "Race Issues",
    value: "race-issues",
    name: "area_of_focus",
    id: "race-issues",
  },
  {
    label: "Disability",
    value: "disability",
    name: "area_of_focus",
    id: "disability",
  },
  {
    label: "Accident, Injury, Illness",
    value: "accident",
    name: "area_of_focus",
    id: "accident",
  },
  {
    label: "Eating Disorders",
    value: "eating",
    name: "area_of_focus",
    id: "eating",
  },
  {
    label: "Veterans Issues",
    value: "veterans",
    name: "area_of_focus",
    id: "veterans",
  },
  {
    label: "Isolation or Loneliness",
    value: "isolation",
    name: "area_of_focus",
    id: "isolation",
  },
  {
    label: "Caregiver",
    value: "care-giver",
    name: "area_of_focus",
    id: "caregiver",
  },
  {
    label: "Others",
    value: "others",
    name: "area_of_focus",
    id: "others",
  },
];

export const socialMedias: OptionType[] = [
  {
    label: "Instagram",
    value: "instagram",
  },
  {
    label: "Facebook",
    value: "facebook",
  },
  {
    label: "LinkedIn",
    value: "linkedin",
  },
  {
    label: "TikTok",
    value: "tiktok",
  },
  {
    label: "Pinterest",
    value: "pinterest",
  },
  {
    label: "X (formerly Twitter)",
    value: "twitter",
  },
];

export const sessionScheduled: OptionType[] = [
  {
    label: "1 hour",
    value: "1",
    name: "session",
    id: "1_hour"
  },
  {
    label: "3 hours",
    value: "3",
    name: "session",
    id: "3_hours"
  },
  {
    label: "6 hours",
    value: "6",
    name: "session",
    id: "6_hours"
  },
  {
    label: "12 hours",
    value: "12",
    name: "session",
    id: "12_hours"
  },
  {
    label: "24 hours",
    value: "24",
    name: "session",
    id: "24_hours"
  },
  {
    label: "36 hours",
    value: "36",
    name: "session",
    id: "36_hours"
  },
  {
    label: "48 hours",
    value: "48",
    name: "session",
    id: "48_hours"
  },

];

export const licenseTypeIndependent: OptionType[] = [
  {
    label: "Licensed Clinical Mental Health Counselor / Professional Counselor",
    value: "professional",
    id: "professional",
  },
  {
    label: "Licensed Clinical Social Worker",
    value: "social",
    id: "social",
  },
  {
    label: "Licensed Marriage & Family Therapist",
    value: "marriage-family",
    id: "marriage-family",
  },
  {
    label: "Licensed Psychologist",
    value: "psychologist",
    id: "psychologist",
  },
  {
    label: "Licensed Substance Use Disorder Counselor",
    value: "substance-use",
    id: "substance-use",
  },
];

export const insuranceTypes: OptionType[] = [
  {
    label: "Aetna",
    value: "aetna",
    name: "insurance-type",
  },
  {
    label: "Blue Cross / Blue Shield",
    value: "blue-cross",
    name: "insurance-type",
  },
  {
    label: "Cigna",
    value: "cigna",
    name: "insurance-type",
  },
  {
    label: "Healthcare Service Corporation",
    value: "health",
    name: "insurance-type",
  },
  {
    label: "Humana",
    value: "humana",
    name: "insurance-type",
  },
  {
    label: "Kaiser Permante",
    value: "kaiser",
    name: "insurance-type",
  },
  {
    label: "Medicaid",
    value: "midecaid",
    name: "insurance-type",
  },
  {
    label: "Medicare",
    value: "medicare",
    name: "insurance-type",
  },
  {
    label: "Medicaid",
    value: "medicaid",
    name: "insurance-type",
  },
  {
    label: "Select Health",
    value: "select-health",
    name: "insurance-type",
  },
  {
    label: "TriCare",
    value: "tricare",
    name: "insurance-type",
  },
  {
    label: "United Healthcare",
    value: "united-healthcare",
    name: "insurance-type",
  },
  {
    label: "Others",
    value: "others",
    name: "insurance-type",
  },
];

export const modalities: OptionType[] = [
  {
    name: "Acceptance and Commitment Therapy (ACT)",
    label: "acceptance-and-commitment-therapy",
    value: "acceptance",
  },
  {
    name: "Applied Behavioral Analysis (ABA)",
    label: "applied-behavioral-analysis",
    value: "applied",
  },
  {
    name: "Art Therapy",
    label: "art-therapy",
    value: "art",
  },
  {
    name: "Attachment-Based Therapy",
    label: "attachment-based-therapy",
    value: "attachment",
  },
  {
    name: "Animal-Assisted Therapy",
    label: "animal-assisted-therapy",
    value: "animal-assisted",
  },
  {
    name: "Biofeedback",
    label: "biofeedback",
    value: "biofeedback",
  },
  {
    name: "Cognitive Behavioral Therapy (CBT)",
    label: "cognitive-behavioral-therapy",
    value: "cognitive-behavioral",
  },
  {
    name: "Cognitive Processing Therapy (CPT)",
    label: "cognitive-processing-therapy",
    value: "cognitive-processing",
  },
  {
    name: "Culturally-Sensitive Therapy",
    label: "culturally-sensitive-therapy",
    value: "curturally-sensitive",
  },
  {
    name: "Dialectical Behavioral Therapy (DBT)",
    label: "dialectical-behavioral-therapy",
    value: "dialectical",
  },
  {
    name: "Emotionally Focused Therapy (EFT)",
    label: "emotionally-focused-therapy",
    value: "emotionally",
  },
  {
    name: "Existential Therapy",
    label: "existential-therapy",
    value: "existential",
  },
  {
    name: "Exposure and Response Prevention (ERP)",
    label: "exposure-and-response-prevention",
    value: "exposure",
  },
  {
    name: "Feminist Therapy",
    label: "feminist-therapy",
    value: "feminist",
  },
  {
    name: "Gestalt Therapy",
    label: "gestalt-therapy",
    value: "gestalt",
  },
  {
    name: "Gottman Method for Healthy Relationships Humanistic Therapy",
    label: "gottman-method-for-healthy-relationships-humanistic-therapy",
    value: "gottman",
  },
  {
    name: "Internal Family Systems Therapy",
    label: "internal-family-systems-therapy",
    value: "internal-familty",
  },
  {
    name: "Interpersonal Psychotherapy",
    label: "interpersonal-psychotherapy",
    value: "interpersonal",
  },
  {
    name: "Marriage and Family Therapy",
    label: "marriage-and-family-therapy",
    value: "marriage-family",
  },
  {
    name: "Mindfulness-Based Cognitive Therapy",
    label: "mindfulness-based-cognitive-therapy",
    value: "mindfulness",
  },
  {
    name: "Motivational Interviewing",
    label: "motivational-interviewing",
    value: "motivational",
  },
  {
    name: "Multicultural Therapy",
    label: "multicultural-therapy",
    value: "multicultural-theory",
  },
  {
    name: "Music Therapy",
    label: "music-therapy",
    value: "music-therapy",
  },
  {
    name: "Narrative Therapy",
    label: "narrative-therapy",
    value: "narrative-therapy",
  },
  {
    name: "Neurofeedback",
    label: "neurofeedback",
    value: "neuro-feedback",
  },
  {
    name: "Parent-Child Interaction Therapy (PCIT)",
    label: "parent-child-interaction-therapy",
    value: "parent-child",
  },
  {
    name: "Person-Centered Therapy",
    label: "person-centered-therapy",
    value: "person-centered",
  },
  {
    name: "Play Therapy",
    label: "play-therapy",
    value: "play-therapy",
  },
  {
    name: "Positive Psychology",
    label: "positive-predisposition",
    value: "positive-psychology",
  },
  {
    name: "Prolonged Exposure Therapy",
    label: "prolonged-exposure-therapy",
    value: "prolonged-exposure",
  },
  {
    name: "Psychoanalytic Therapy",
    label: "psychoanalytic-therapy",
    value: "psychoanalytic",
  },
  {
    name: "Psychodynamic Therapy",
    label: "psychodynamic-therapy",
    value: "psychodynamic",
  },
  {
    name: "Psychological Testing and Evaluation",
    label: "psychological-testing-and-evaluation",
    value: "psychological-testing",
  },
  {
    name: "Rational Emotive Behavior Therapy",
    label: "rational-emotive-behavior-therapy",
    value: "rational-emotive",
  },
  {
    name: "Relational Therapy",
    label: "relational-therapy",
    value: "relational-therapy",
  },
  {
    name: "Sandplay Therapy",
    label: "sandplay-therapy",
    value: "sandplay",
  },
  {
    name: "Schema Therapy",
    label: "schema-therapy",
    value: "schema-therapy",
  },
  {
    name: "Solution-Focused Brief Therapy (SFBT)",
    label: "solution-focused-brief-therapy",
    value: "solution-focused",
  },
  {
    name: "Somatic Therapy",
    label: "somatic-therapy",
    value: "somatic-therapy",
  },
  {
    name: "Strength-Based Therapy",
    label: "strength-based-therapy",
    value: "strength-based",
  },
  {
    name: "Trauma-Focused Cognitive Behavioral Therapy (TF-CBT)",
    label: "trauma-focused-cognitive-behavioral-therapy",
    value: "trauma-focused",
  },
  {
    name: "Structural Family Therapy",
    label: "structural-family-therapy",
    value: "structural-family",
  },
  {
    name: "No Preference",
    label: "no-preference",
    value: "no-preference",
  },
];

export const registrationPageSchemaList: StepInterface[] = [
  {
    followed_by: null,
    page_id: "create-account",
    title: "Create your account",
    form: [
      {
        label: "Email Address",
        type: "email",
      },
      {
        label: "Passowrd",
        type: "password",
      },
      {
        label: "Confirm Passowrd",
        type: "password",
      },
    ],
  },
  {
    followed_by: ["create-account"],
    page_id: "profile-information",
    title: "Enter your Profile Information",
    form: [
      {
        label: "First Name",
        type: "text",
      },
      {
        label: "Last Name",
        type: "text",
      },
      {
        label: "Date of Birth",
        type: "date",
      },
      {
        label: "Profile Photo",
        type: "image",
      },
    ],
  },
  {
    followed_by: ["profile-information"],
    page_id: "has-license",
    title: "Are you independently licensed?",
    form: [
      {
        label: "",
        type: "radio",
        options: [
          {
            label: "Yes, under clinical supervision",
            value: "supervision",
            name: "license",
            reserved_page: "opt-supervision",
            id: "supervision",
          },
          {
            label: "Yes, fully licensed",
            value: "full",
            name: "license",
            reserved_page: "opt-fully-liscensed",
            id: "full",
          },
          {
            label: "No",
            value: "no",
            name: "license",
            id: "no",
            prevent_navigation: true,
          },
        ],
      },
    ],
  },
  {
    followed_by: ["opt-supervision"],
    page_id: "supervisor-information",
    title: "Enter your Clinical Supervisor information:",
    form: [
      {
        label: "Full Name of Clinical Supervisor",
        type: "text",
      },
      {
        label: "Clinical Supervisor is licensed in",
        type: "select",
        options: states.map((state) => ({ label: state.name, value: state.abbrev })),
      },
      {
        label: "License # of Clinical Supervisor",
        type: "text",
      },
    ],
  },
  {
    followed_by: ["opt-fully-liscensed"],
    page_id: "independent-license-type",
    title: "Which type of Independent License do you have?:",
    form: [
      {
        label: "",
        type: "checkbox",
        options: licenseType,
      },
    ],
  },
  {
    followed_by: ["independent-license-type", "license-type"],
    page_id: "licensed-state",
    title: "What state(s) are you licensed in?:",
    form: [
      {
        label: "",
        type: "checkbox",
        options: states.map((state) => ({ label: state.name, value: state.abbrev, id: state.abbrev })),
      },
    ],
  },
  {
    followed_by: ["supervisor-information"],
    page_id: "license-type",
    title: "What type of license are you pursuing?:",
    form: [
      {
        label: "",
        type: "checkbox",
        options: licenseType,
      },
    ],
  },
  {
    followed_by: ["licensed-state"],
    page_id: "license-information",
    title: "Enter your License Information:",
    form: [
      {
        label: "Alabama license number",
        type: "text",
      },
      {
        label: "Utah licensed number",
        type: "text",
      },
      {
        label: "NPI number",
        type: "text",
      },
    ],
  },
];

export const APPOINTMENT_METHODS: OptionType[] = [
  {
    label: 'In-Person',
    value: 'in-person',
    name: 'appointment_method',
  },
  {
    label: 'Telehealth',
    value: 'telehealth',
    name: 'appointment_method',
  }
];
