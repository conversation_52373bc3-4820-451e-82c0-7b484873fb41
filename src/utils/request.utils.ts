import axios from "axios";
import notification from "./notification.util";
import store from "../store";
import { setTokens, signOut } from "@/store/slicers/auth.slicer";

declare module 'axios' {
  export interface AxiosRequestConfig {
    suppressError?: boolean;
  }
}

const refreshAccessToken = async () => {
  const { refreshToken } = store.getState().auth;
  const { data } = await request.post("/auth/refresh", {
    refreshToken: refreshToken,
  });
  store.dispatch(setTokens(data));
  return data.accessToken;
};

export const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

request.interceptors.request.use((config) => {
  const { token } = store.getState().auth;
  if (token) {
    config.headers["Authorization"] = `Bearer ${token}`;
  }
  return config;
});

request.interceptors.response.use(
  function (response) {
    if (response.data && response.data.message) {
      notification.success(response.data.message);
    }
    return response;
  },
  async function (error) {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const token = await refreshAccessToken();
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return await axios(originalRequest);
      } catch {
        store.dispatch(signOut());
        return Promise.reject(error);
      }
    }
    const suppressError = originalRequest?.suppressError;

    if (!suppressError && error.response?.data?.message) {
      notification.error(error.response.data.message);
    }

    return Promise.reject(error);
  }
);

