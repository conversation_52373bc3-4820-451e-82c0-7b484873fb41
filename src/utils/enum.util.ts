export enum QuestionnaireType {
  checkbox = "Multiple Choice",
  radio = "Single Choice",
  text = "Text",
}

export const getQuestionnaireType = (type: string) => {
  switch (type) {
    case "checkbox":
      return QuestionnaireType.checkbox;
    case "radio":
      return QuestionnaireType.radio;
    case "text":
      return QuestionnaireType.text;
    default:
      return "";
  }
};

export const questionnaireTypeList = [
  {
    value: "checkbox",
    label: QuestionnaireType.checkbox,
  },
  {
    value: "radio",
    label: QuestionnaireType.radio,
  },
];

export enum UserType {
  SYSTEM = "system",
  PATIENT = "patient",
  THERAPIST = "therapist",
}
