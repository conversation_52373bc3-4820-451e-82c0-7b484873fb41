import toast, { type Toast } from "react-hot-toast"

const notification = {
  success: (message: string, avatar?: string) => {
    toast(
      (t: Toast) => (
        <div key={t.id} className="w-[280px] sm:w-[320px] flex py-2">
          <div className="flex-1 min-w-0">
            <div className="flex items-start gap-2 sm:gap-3">
              {avatar && (
                <div className="flex-shrink-0 pt-0.5">
                  <img
                    className="h-8 w-8 sm:h-10 sm:w-10 rounded-full object-cover"
                    src={avatar || "/placeholder.svg"}
                    alt=""
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-green-900 truncate">Success</p>
                <p className="mt-1 text-sm text-gray-500 break-words">{message}</p>
              </div>
            </div>
          </div>
          <div className="flex border-l border-gray-50 ml-2">
            <button
              onClick={() => toast.dismiss(t.id)}
              className="w-full border border-transparent rounded-none rounded-r-lg px-2 flex items-center justify-center text-sm font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none focus:ring-0"
            >
              Close
            </button>
          </div>
        </div>
      ),
      {
        style: {
          border: "1px solid #15803d",
          color: "#15803d",
        },
      },
    )
  },
  error: (message: string, avatar?: string) => {
    toast(
      (t: Toast) => (
        <div key={t.id} className="w-[280px] sm:w-[320px] flex py-2">
          <div className="flex-1 min-w-0">
            <div className="flex items-start gap-2 sm:gap-3">
              {avatar && (
                <div className="flex-shrink-0 pt-0.5">
                  <img
                    className="h-8 w-8 sm:h-10 sm:w-10 rounded-full object-cover"
                    src={avatar || "/placeholder.svg"}
                    alt=""
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-red-700 truncate">Error</p>
                <p className="mt-1 text-sm text-gray-500 break-words">{message}</p>
              </div>
            </div>
          </div>
          <div className="flex border-l border-gray-50 ml-2">
            <button
              onClick={() => toast.dismiss(t.id)}
              className="w-full border border-transparent rounded-none rounded-r-lg px-2 flex items-center justify-center text-sm font-medium text-red-600 hover:text-red-500 focus:outline-none focus:ring-0"
            >
              Close
            </button>
          </div>
        </div>
      ),
      {
        style: {
          border: "1px solid #b91c1c",
          color: "#b91c1c",
        },
      }
    );
  },
  info: (message: string, avatar?: string) => {
    toast(
      (t: Toast) => (
        <div key={t.id} className={`w-[320px] flex py-2`}>
          <div className="flex-1 w-0">
            <div className="flex items-start">
              {avatar && (
                <div className="flex-shrink-0 pt-0.5">
                  <img className="h-10 w-10 rounded-full" src={avatar} alt="" />
                </div>
              )}
              <div className="ml-3 flex-1">
                <p className="text-sm font-medium text-blue-700">Info</p>
                <p className="mt-1 text-sm text-gray-500">{message}</p>
              </div>
            </div>
          </div>
          <div className="flex border-l border-gray-50">
            <button
              onClick={() => toast.dismiss(t.id)}
              className="w-full border border-transparent rounded-none rounded-r-lg px-2 flex items-center justify-center text-sm font-medium text-red-600 hover:text-red-500 focus:outline-none focus:ring-0"
            >
              Close
            </button>
          </div>
        </div>
      ),
      {
        style: {
          border: "1px solid #2596be",
          color: "#2596be",
        },
      }
    );
  },
};

export default notification;
