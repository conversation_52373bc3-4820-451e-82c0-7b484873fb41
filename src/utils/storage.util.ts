/**
 * Store page data in local storage
 * 
 * @param pageData - An object containing the page data (id and nextPageId).
 */
export const storePageData = (pageData: { id: any; nextPageId: any }) => {
  let pages = JSON.parse(localStorage.getItem('pages') || '[]');

  // Remove pages where id matches the new pageData's id
  // or nextPageId matches the new pageData's nextPageId
  pages = pages.filter((page: { id: any; nextPageId: any }) => 
    page.id !== pageData.id && page.nextPageId !== pageData.nextPageId
  );

  // Add the new page
  pages.push(pageData);

  // Sort pages by id to maintain order
  pages.sort((a: { id: any }, b: { id: any }) => a.id - b.id);

  localStorage.setItem('pages', JSON.stringify(pages));
};

/**
 * Retrieve page data from local storage.
 * 
 * @returns An array of objects containing page data (id and nextPageId).
 */
export const getPageData = (): { id: any; nextPageId: any }[] => {
  return JSON.parse(localStorage.getItem('pages') || '[]');
};

/**
 * Get the previous page ID based on the current page ID
 * 
 * @param currentPageId - The ID of the current page
 * @returns The ID of the previous page, or null if not found
 */
export const getPreviousPageId = (currentPageId: number): number | null => {
  const pages = getPageData();
  const previousPage = pages.find(page => page.nextPageId === currentPageId);
  return previousPage ? previousPage.id : null;
};


/**
 * Store conditional page data in local storage
 * 
 * @param pageData - An object containing the page data (id and pending status).
 */
export const storeConditionalPageData = (pageData: { id: any; pending: boolean }) => {
  const conditionalPages = JSON.parse(localStorage.getItem('conditionalPages') || '[]');

  // Check if the page already exists
  const pageIndex = conditionalPages.findIndex((page: { id: any }) => page.id === pageData.id);

  if (pageIndex !== -1) {
    // If it exists, remove it
    conditionalPages.splice(pageIndex, 1);
  } else {
    // If it doesn't exist, add it
    conditionalPages.push(pageData);
  }

  // Store the updated conditionalPages array back in local storage
  localStorage.setItem('conditionalPages', JSON.stringify(conditionalPages));
};

/**
 * Update the pending status of a conditional page by its ID.
 * 
 * @param pageId - The ID of the page to update.
 * @param pending - The new pending status.
 */
export const updateConditionalPageData = (pageId: any, pending: boolean) => {
  const conditionalPages = JSON.parse(localStorage.getItem('conditionalPages') || '[]');

  // Find the page by ID
  const pageIndex = conditionalPages.findIndex((page: { id: any }) => page.id === pageId);

  if (pageIndex !== -1) {
    // If the page exists, update its pending status
    conditionalPages[pageIndex].pending = pending;
    localStorage.setItem('conditionalPages', JSON.stringify(conditionalPages));
  }
};

/**
 * Get the ID of the first pending page (where pending is true).
 * 
 * @returns The ID of the first page with pending set to true, or null if none found.
 */
export const getPendingPageId = (): any | null => {
  const conditionalPages = JSON.parse(localStorage.getItem('conditionalPages') || '[]');

  // Find the first page where pending is true
  const pendingPage = conditionalPages.find((page: { id: any; pending: boolean }) => page.pending);

  // Return the ID if found, otherwise return null
  return pendingPage ? pendingPage.id : null;
};

/**
 * Get the IDs of all pages stored in conditionalPages.
 * 
 * @returns An array of IDs of all pages, or an empty array if none found.
 */
export const getAllConditionalPageIds = (): any[] => {
  const conditionalPages = JSON.parse(localStorage.getItem('conditionalPages') || '[]');

  // Map to get all page IDs
  return conditionalPages.map((page: { id: any }) => page.id);
};

/**
 * Delete the conditionalPages item from local storage.
 */
export const deleteConditionalPageData = () => {
  localStorage.removeItem('conditionalPages');
};


/**
 * Store the mainPageId and followingPageId in local storage.
 * @param mainPageId 
 * @param followingPageId 
 */
export const storeFollowThroughPages = (mainPageId: number, followingPageId: number) => {
  // Retrieve existing data or initialize with an empty array
  const followThroughPagesData = JSON.parse(localStorage.getItem('followThroughPages') || '[]');

  // Check if the mainPageId already exists
  const existingEntries = followThroughPagesData.filter(
    (entry: { mainPageId: number; followingPageId: number }) => entry.mainPageId === mainPageId
  );

  // Check if the specific mainPageId and followingPageId combination exists
  const isDuplicate = existingEntries.some(
    (entry: { mainPageId: number; followingPageId: number }) => entry.followingPageId === followingPageId
  );

  if (!isDuplicate) {
    // Add the new entry if no duplicate exists
    followThroughPagesData.push({ mainPageId, followingPageId });
  }

  // Save the updated data back to localStorage
  localStorage.setItem('followThroughPages', JSON.stringify(followThroughPagesData));
};

/**
 * Get the mainPageId from the followThroughPages array
 * @param followingPageId 
 * @returns 
 */
export const getMainPageId = (followingPageId: number): number | null => {
  // Retrieve existing data or initialize with an empty array
  const followThroughPagesData = JSON.parse(localStorage.getItem('followThroughPages') || '[]');

  // Find the entry with the matching followingPageId
  const entry = followThroughPagesData.find(
    (item: { mainPageId: number; followingPageId: number }) => item.followingPageId === followingPageId
  );

  // Return the mainPageId if found, otherwise return null
  return entry ? entry.mainPageId : null;
};

