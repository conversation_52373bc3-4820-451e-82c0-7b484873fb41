import Compressor from 'compressorjs';
import notification from './notification.util';
import { request } from "@/utils/request.utils";

export const handleFileUpload = async (files: File[]): Promise<any[] | null> => {
  const processedFiles: File[] = [];

  for (const file of files) {
    const isImage = file.type.startsWith('image/');

    if (!isImage) {
      if (file.size > 5 * 1024 * 1024) {
        notification.error(`${file.name} exceeds 5MB.`);
        return null;
      }
      processedFiles.push(file);
      continue;
    }

    const compressedFile = await new Promise<File | null>((resolve) => {
      const compress =  new Compressor(file, {
        quality: 0.3,
        success(result) {
          if (result.size > 5 * 1024 * 1024) {
            notification.error(`${file.name} exceeds 5MB after compression.`);
            return resolve(null);
          } 
          const newFile = new File([result], file.name, {
            type: result.type,
            lastModified: Date.now(),
          });
          return resolve(newFile);
        },
        error() {
          notification.error(`Could not compress ${file.name}.`);
          resolve(null);
        },
      });
      // eslint-disable-next-line no-console
      console.log(compress)
    });

    if (!compressedFile) return null;
    processedFiles.push(compressedFile);
  }

  const formData = new FormData();
  processedFiles.forEach((file) => {
    formData.append('file', file);
  });

  try {
    const response = await request.post('/temps/upload-file', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    if(response?.status !==200) {
      notification.error('File upload failed.')
      return null
    }

    return response.data.uploaded || null;
    
  } catch (error) {
    notification.error('An error occurred during file upload.');
    // eslint-disable-next-line no-console
    console.error('Upload error:', error);
  }

  return null;
};
