import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import localizedFormat from "dayjs/plugin/localizedFormat";
import { THERAPIST_REGISTRATION_PAGES } from "@/configs/therapist-registration.configs";
import { FormContentType } from "@/store/slicers/auth.slicer";

dayjs.extend(utc);
dayjs.extend(localizedFormat);

export const getFullName = (firstname: string, lastname: string) => {
  return `${firstname} ${lastname}`;
};

export const dateToDateTime = (date: Date) => {
  const formattedDate = dayjs.utc(date);
  return {
    // Jan 1, 2021
    date: formattedDate.format("MMM D, YYYY"),
    time: formattedDate.format("HH:mm A"),
  };
};

export const getInitials = (name: string) => {
  const names = name.split(" ");
  if (names.length > 1) {
    return names[0][0] + names[1][0];
  }
  return names[0][0];
};

export const jsonToFormData = (json: any) => {
  const formData = new FormData();
  Object.keys(json).forEach((key) => {   
    formData.append(key, json[key]);
  });
  return formData;
};

export const classNames = (...classes: (string | boolean)[]) => {
  return classes.filter(Boolean).join(" ");
};

/**
 * 
 * @param str or str[]
 * @returns formatted string
 */
export const formatValue = <T extends string | string[]>(input: T): T => {
  const formatSingle = (str: string): string => {
    return str.toLowerCase().replace(/_/g, ' ').replace(/(?:^|\s)\S/g, (char) => char.toUpperCase());
  };

  if (Array.isArray(input)) {
    return input.map(formatSingle) as T;
  } else {
    return formatSingle(input) as T;
  }
};

/**
 * @param email 
 * @returns boolean
 */
export const isEmail = (email: string): boolean => {
  return /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(email);
}

/**
 * 
 * @param phone 
 * @returns boolean
 */
export const isPhone = (phone: string): boolean => {
  return /^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s./0-9]*$/.test(phone);
}

/**
 * 
 * @param str: string
 * @returns slug: string
 */
export const generateSlug = (str: string): string => {
  return str
      .replace(/\(.*?\)/g, '') // Remove any content inside parentheses
      .trim() // Remove leading and trailing whitespace
      .replace(/[^a-zA-Z0-9\s]/g, '-') // Replace non-word characters with hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .toLowerCase() // Convert to lowercase
      .replace(/^-+|-+$/g, '') // Trim leading/trailing hyphens
      .replace(/-+/g, '-'); // Replace consecutive hyphens with a single hyphen
}

/**
 * 
 * @param buffer 
 * @returns string
 */
export async function base64URLEncode(buffer: ArrayBuffer): Promise<string> {
  return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer) as any))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * 
 * @param message 
 * @returns ArrayBuffer
 */
export async function sha256(message: string): Promise<ArrayBuffer> {
  const encoder = new TextEncoder();
  const data = encoder.encode(message);
  return window.crypto.subtle.digest('SHA-256', data);
}


/**
 * Generate URL encoded string
 * @returns string
 */
export async function generateURLEncodedString(): Promise<string> {
  const verifier = window.crypto.getRandomValues(new Uint8Array(32));
  return base64URLEncode(verifier.buffer)
}



/**
 * 
 * @param codeVerifier 
 * @returns string
 */
export async function createCodeChallenge(codeVerifier: string): Promise<string> {
  const hashedVerifier = await sha256(codeVerifier);
  return base64URLEncode(hashedVerifier);
}

/**
 * @param value
 * @returns string
 */
export const sanitizeInputValue = (value: string): string => {
  return value
    .replace(/[^a-zA-Z ]/g, "") // Allow only letters and spaces
    .replace(/^\s+/, "") // Remove leading spaces
    .replace(/\s{2,}/g, " "); // Replace multiple spaces with a single space
};

/**
 * 
 * @param currentPage 
 * @param invalidPages 
 * @returns next invalid page or null
 */
export const getNextInvalidPage = (
  currentPage: string,
  invalidPages: Set<string>,
): string | null => {
  for (const page of THERAPIST_REGISTRATION_PAGES) {
    if (page !== currentPage && invalidPages.has(page)) {
      return page;
    }
  }
  return null;
};

/**
 * 
 * @param userRegInfo 
 * @returns FormContentType
 */
export const generateRegFormContent = (userRegInfo: Record<string, any>): FormContentType => {
  const formContent: FormContentType = {};

  // Step 1: Add entries from registrationInfo array
  userRegInfo.registrationInfo?.forEach((entry: any) => {
    formContent[entry.pageName] = entry.payloadInfo;
  });

  // Step 2: Add create-account manually if info is present
  formContent['create-account'] = {
    first_name: userRegInfo.firstname || '',
    last_name: userRegInfo.lastname || '',
    email: userRegInfo.email || '',
    password: '',
    confirm_password: ''
  };

  // Step 3: Add calendar-add manually if calendars exist
  const calendarEmails: { google_calendar?: string; outlook_calendar?: string } = {};

  userRegInfo.calendars?.forEach((calendar: any) => {
    if (calendar.type === 'google') {
      calendarEmails.google_calendar = calendar.email;
    } else if (calendar.type === 'outlook') {
      calendarEmails.outlook_calendar = calendar.email;
    }
  });

  if (Object.keys(calendarEmails).length > 0) {
    formContent['calendar-add'] = calendarEmails;
  }

  return formContent;
};

/**
 * 
 * @param formData 
 * @returns boolean
 */
export const validateTherapistRegistration = (
  formData: Record<string, any>
): boolean => {
  for (const page of THERAPIST_REGISTRATION_PAGES) {
    if (page === "modalities") continue;

    const pageData = formData[page];
    const isMissingOrEmpty = !pageData || Object.keys(pageData).length === 0;
    const hasInvalidFlag = pageData?.isInvalid === true;

    if (isMissingOrEmpty || hasInvalidFlag) {
      return false;
    }
  }
  return true;
};


/**
 * 
 * @param price 
 * @returns message or undefined
 */
export const validatePrice = (price?: string): string | undefined => {
  if (!price) return "Price is required.";
  
  // Check if it's a valid number
  const numberRegex = /^\d+(\.\d+)?$/;
  if (!numberRegex.test(price)) {
    return "Price must be a valid number.";
  }

  // Check if it has up to 2 decimal places
  const decimalPlacesRegex = /^\d+(\.\d{1,2})?$/;
  if (!decimalPlacesRegex.test(price)) {
    return "Price can have up to 2 decimal places.";
  }

  // Check if total length is at most 8 characters
  if (price.length > 8) {
    return "Price must be at most 8 characters long.";
  }

  return undefined;
};


	export const formatDateMDY = (date: Date | string): string => {
  const d = new Date(date);
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const year = d.getFullYear();
  return `${month}-${day}-${year}`;
};
