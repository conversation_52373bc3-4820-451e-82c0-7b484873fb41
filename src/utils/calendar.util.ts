import dayjs from "dayjs";
import WeekOfYear from "dayjs/plugin/weekOfYear";
import IsToday from "dayjs/plugin/isToday";

dayjs.extend(WeekOfYear);
dayjs.extend(IsToday);

export type TherapyDate = {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  day: string;
  dayOfMoth: string;
  time: string;
  isSelected: boolean;
};

/** function that returns all day list of current month */
export const getMonthDayList = (date: Date) => {
  const startWeek = dayjs(date).startOf("month").week();
  const endWeek =
    dayjs(date).endOf("month").week() === 1
      ? 53
      : dayjs(date).endOf("month").week();
  let calendar: any = [];
  for (let week = startWeek; week <= endWeek; week++) {
    calendar = calendar.concat(
      Array(7)
        .fill(0)
        .map((n, i) => {
          const d = dayjs(date)
            .week(week)
            .startOf("week")
            .add(n + i, "day")
            .toDate();
          return {
            date: d,
            isCurrentMonth: d.getMonth() === date.getMonth(),
            isToday: dayjs(d).isToday(),
            day: dayjs(d).format("D"),
            dayOfMoth: dayjs(d).format("DD"),
            time: dayjs(d).format("HH:mm A"),
            isSelected: false,
          } as TherapyDate;
        })
    );
  }
  return calendar;
};
