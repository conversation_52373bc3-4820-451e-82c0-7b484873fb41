import { useAppSelector } from "@/store/hooks";
import { setTokens, signOut } from "@/store/slicers/auth.slicer";
import axios from "axios";
import { useDispatch } from "react-redux";

export const useApiClient = () => {
  const { token, refreshToken } = useAppSelector((state) => state.auth);
  const dispatch = useDispatch();

  const headers = token ? { Authorization: `Bearer ${token}` } : {};
  const instance = axios.create({
    baseURL: `${import.meta.env.VITE_API_URL}`,
    headers,
  });

  const refreshAccessToken = async () => {
    const { data } = await instance.post("/auth/refresh", {
      refreshToken: refreshToken,
    });
    dispatch(setTokens(data));
    return data.accessToken;
  };

  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      const originalRequest = error.config;
      if (error.response.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;
        return refreshAccessToken()
          .then((refreshTokenResult) => {
            originalRequest.headers.Authorization = `Bearer ${refreshTokenResult}`;
            return axios(originalRequest);
          })
          .catch(() => {
            dispatch(signOut());
          });
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

export const makeParams = (params: any) => {
  Object.keys(params).forEach((k) => params[k] == null && delete params[k]);
  return params;
};
