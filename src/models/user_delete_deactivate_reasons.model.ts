import {
    AllowNull,
    BelongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    Table,
} from 'sequelize-typescript';
import { User } from '.';
import { ProfileDeleteDeactivateReason } from '.';

export interface IUserDeleteDeactivateReason {
    id?: number;
    userId: string;
     type: 'deleted' | 'deactivated' | 'rejected';
    reasonId: string;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: 'user_delete_deactivate_reasons',
    timestamps: true,
})
class UserDeleteDeactivateReason extends Model<IUserDeleteDeactivateReason> {
    @AllowNull(false)
    @Column({
        primaryKey: true,
        autoIncrement: true,
        type: DataType.INTEGER,
    })
    id!: number;

    @AllowNull(false)
    @ForeignKey(() => User)
    @Column(DataType.INTEGER)
    userId!: string;

    @AllowNull(false)
    @ForeignKey(() => ProfileDeleteDeactivateReason)
    @Column(DataType.INTEGER)
    reasonId!: string;

    @BelongsTo(() => User, 'userId')
    user!: User;

    @BelongsTo(() => ProfileDeleteDeactivateReason, 'reasonId')
    reason!: ProfileDeleteDeactivateReason;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW,
    })
    createdAt!: Date;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW,
    })
    updatedAt!: Date;

    @AllowNull(false)
    @Column(DataType.ENUM('deleted', 'deactivated','rejected'))
    declare type: 'deleted' | 'deactivated'| 'rejected';
}

export default UserDeleteDeactivateReason;