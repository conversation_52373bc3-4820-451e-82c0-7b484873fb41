import {
  AllowNull,
  Column,
  Model,
  Table,
  DataType,
} from 'sequelize-typescript'

export interface ITherapistPage {
  pageName: string
  orderingRank?: number
  is_active?: boolean
}

@Table({
  tableName: 'therapist_pages',
  timestamps: false,
  paranoid: false,
  name: {
    singular: 'therapist_page',
    plural: 'therapist_pages',
  }
})
class TherapistPage extends Model implements ITherapistPage {
  @AllowNull(false)
  @Column(DataType.STRING)
  pageName!: string

  @AllowNull(true)
  @Column(DataType.INTEGER)
  orderingRank!: number

  @AllowNull(false)
  @Column(DataType.BOOLEAN)
  is_active!: boolean
}

export default TherapistPage
