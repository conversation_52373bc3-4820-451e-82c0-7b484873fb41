import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
	BelongsTo,
	BelongsToMany,
} from 'sequelize-typescript'
import User from './user.model'
import { Answer, Page, PageAnswer, Questionnaire } from '.'

export interface IUserQuestionnaire {
	pageId: number
	questionnaireId: number
	userId: number
	answerId: number
	points: number
}

@Table({
	tableName: 'user_questionnaire',
	timestamps: true,
	paranoid: false,
})
class UserQuestionnaire extends Model implements IUserQuestionnaire {
	@ForeignKey(() => Page)
	@Column(DataType.INTEGER)
	pageId!: number

	@ForeignKey(() => Questionnaire)
	@Column(DataType.INTEGER)
	questionnaireId!: number

	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	userId!: number

	@ForeignKey(() => Answer)
	@Column(DataType.INTEGER)
	answerId!: number

	@Column(DataType.INTEGER)
	points!: number

	@BelongsTo(() => Questionnaire, {
		foreignKey: 'questionnaireId',
	})
	questionnaire!: Questionnaire

	@BelongsTo(() => Answer, {
		foreignKey: 'answerId',
	})
	answer!: Answer

	@BelongsTo(() => Page, {
		foreignKey: 'pageId',
	})
	page!: Page
}

export default UserQuestionnaire
