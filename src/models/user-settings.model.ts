import { AllowNull, AutoIncrement, Column, DataType, Foreign<PERSON>ey, Model, PrimaryKey, Table, Unique } from "sequelize-typescript"
import { User } from "."


export interface IUserSettings {
	id: number
	userId: number
	notificationSettings: JSON
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'user_settings',
	timestamps: true,
	paranoid: false,
})
class UserSettings extends Model implements IUserSettings {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@Unique
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare userId: number

	@AllowNull(false)
	@Column(DataType.JSONB)
	declare notificationSettings: JSON

	@AllowNull(false)
	@Column(DataType.DATE)
	declare createdAt: Date

	@AllowNull(false)
	@Column(DataType.DATE)
	declare updatedAt: Date
}

export default UserSettings