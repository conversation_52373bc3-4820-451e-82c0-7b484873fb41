import {
    AllowNull,
    Column,
    DataType,
    Model,
    Table,
    CreatedAt,
    UpdatedAt,
    PrimaryKey,
    AutoIncrement,
} from 'sequelize-typescript';

export interface IState {
    id: number;
    name: string;
    abbrev: string;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: 'states',
    timestamps: true,
    paranoid: false,
    name: {
        singular: 'state',
        plural: 'states',
    },
})
class State extends Model<IState> implements IState {
    @PrimaryKey
    @AutoIncrement
    @Column(DataType.INTEGER)
    id!: number;

    @AllowNull(false)
    @Column(DataType.STRING)
    name!: string;

    @AllowNull(false)
    @Column(DataType.STRING)
    abbrev!: string;

    @CreatedAt
    @Column(DataType.DATE)
    createdAt!: Date;

    @UpdatedAt
    @Column(DataType.DATE)
    updatedAt!: Date;
}

export default State;