import {
	AllowNull,
	<PERSON><PERSON>sTo,
	Column,
	DataType,
	<PERSON><PERSON>ult,
	Foreign<PERSON>ey,
	HasOne,
	Model,
	Table,
} from 'sequelize-typescript'
import { User, NormalOfficeHours, PatientPayment, MinorPatient, PatientCard } from '.'
import { TherapistProfile, UserRegistrationInfo } from '.';

export interface IAppointment {
	name: string;
	description: string | null;
	appointmentDate: Date;
	therapistId: number;
	patientId: number;
	workingHourId: number;
	googleEventId?: string;
	outlookEventId?: string;
	duration?: number;
	isBilled: boolean;
	cancelledAt?: Date;
	type?: string;
	isFromWaitlist: boolean;
	lockedByUserId?: number;
	lockedAt?: Date;
	amount: number;
	patientCardId: number | null;
}

@Table({
	tableName: 'appointments',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'appointment',
		plural: 'appointments',
	},
})
class appointments extends Model implements IAppointment {
	@AllowNull(false)
	@Column(DataType.STRING)
	declare name: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare description: string

	@AllowNull(false)
	@Column(DataType.DATE)
	declare appointmentDate: Date

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare therapistId: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare patientId: number
	
	@AllowNull(false)
	@ForeignKey(() => NormalOfficeHours)
	@Column(DataType.INTEGER)
	declare workingHourId: number

	@AllowNull(true)
	@Column(DataType.STRING)
	declare googleEventId: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare outlookEventId: string

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare duration: number

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isBilled: boolean

	@AllowNull(true)
	@Column(DataType.DATE)
	declare cancelledAt: Date;

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare minorId: number

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	declare isMinor: boolean

	@AllowNull(true)
	@Column(DataType.ENUM('in-person', 'telehealth'))
	declare type: string

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isFromWaitlist: boolean

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare lockedByUserId: number

	@AllowNull(true)
	@Column(DataType.DATE)
	declare lockedAt: Date;	  

	@AllowNull(true)
	@Column(DataType.DECIMAL(10, 2))
	declare amount: number

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare patientCardId: number | null;

	@BelongsTo(() => User, "patientId")
	declare patient: User

	@BelongsTo(() => User, 'therapistId')
	declare therapist: User
	
	@BelongsTo(() => NormalOfficeHours, 'workingHourId')
	declare officeHour: NormalOfficeHours

	@BelongsTo(() => UserRegistrationInfo, { foreignKey: 'therapistId', targetKey: 'userId' })
	profile!: TherapistProfile;

    @HasOne(() => PatientPayment, {
        foreignKey: 'appointmentId',
    })
    declare patientPayment: PatientPayment

    @BelongsTo(() => MinorPatient, { foreignKey: 'minorId', targetKey: 'id', as: 'minorPatient' })
    declare minorPatient: MinorPatient;

	@BelongsTo(() => PatientCard, {
		foreignKey: 'patientCardId',
		targetKey: 'id',
		as: 'patientCard',
	})
	declare patientCard: PatientCard;
}

appointments.prototype.toJSON = function () {
	const record = this.get()
	delete record.createdAt
	delete record.updatedAt
	return record
}

export default appointments
