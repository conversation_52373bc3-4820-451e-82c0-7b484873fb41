import { Model, Table, PrimaryKey, AutoIncrement, Column, DataType, ForeignKey, AllowNull, CreatedAt, UpdatedAt, BelongsTo } from "sequelize-typescript"
import { User, PageAnswer } from "."

export interface IUserValues {
	id: number
	userId: number
	answerGroup: string
	selectedValue: number
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'user_values',
	timestamps: true,
	paranoid: false,
})

class UserValues extends Model implements IUserValues {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare userId: number

	@AllowNull(false)
	@Column(DataType.STRING)
	declare answerGroup: string

	@AllowNull(false)
	@Column(DataType.FLOAT)
	declare selectedValue: number

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'userId',
		as: 'user',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare user: User
}

UserValues.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default UserValues
