import {
    AllowNull,
    BelongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    Table,
    Index,
  } from 'sequelize-typescript';
  import User from './user.model';
  
  @Table({
    tableName: 'minor_patient',
    timestamps: true,
  })
  class MinorPatient extends Model {
    @AllowNull(false)
    @Column({
      type: DataType.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    })
    declare id: number;
  
    @ForeignKey(() => User)
    @Index
    @AllowNull(true)
    @Column(DataType.INTEGER)
    declare userId: number;
  
    @BelongsTo(() => User)
    declare user: User;
  
    @AllowNull(false)
    @Column(DataType.STRING)
    declare firstName: string;
  
    @AllowNull(false)
    @Column(DataType.STRING)
    declare lastName: string;
  
    @AllowNull(true)
    @Column(DataType.DATE)
    declare dob: Date;
  
    @AllowNull(false)
    @Column({
      type: DataType.DATE,
      defaultValue: DataType.NOW,
    })
    declare createdAt: Date;
  
    @AllowNull(false)
    @Column({
      type: DataType.DATE,
      defaultValue: DataType.NOW,
    })
    declare updatedAt: Date;
    
    @AllowNull(true)
    @Column({
      type: DataType.STRING,
      defaultValue: null,
    })
    declare userProfile: string | null;
  }
  
  export default MinorPatient;