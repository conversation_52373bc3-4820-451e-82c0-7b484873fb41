import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
	AllowNull,
	AutoIncrement,
	PrimaryKey,
	CreatedAt,
	UpdatedAt,
	BelongsTo,
} from 'sequelize-typescript'
import { TherapistSubscription, User } from '.'

export interface IStripeInfo {
	id: number
	therapistId: number
	therapistSubscriptionId: number
	stripeSubscriptionId: string
	stripeCustomerId: string
	stripeCurrentPeriodStart: Date
	stripeCurrentPeriodEnd: Date
	stripeCancelAtPeriodEnd: boolean
	billedPrice: number
	currency: string
	status: string
	stripeSubscriptionScheduleId: string
	nextScheduledSubscriptionType: string
	canceledAt: Date
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'stripe_info',
	timestamps: true,
	paranoid: false,
})
class StripeInfo extends Model implements IStripeInfo {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare therapistId: number

	@AllowNull(false)
	@ForeignKey(() => TherapistSubscription)
	@Column(DataType.INTEGER)
	declare therapistSubscriptionId: number

	@AllowNull(false)
	@Column(DataType.STRING)
	declare stripeSubscriptionId: string

	@AllowNull(false)
	@Column(DataType.STRING)
	declare stripeCustomerId: string

	@AllowNull(false)
	@Column(DataType.DATE)
	declare stripeCurrentPeriodStart: Date

	@AllowNull(false)
	@Column(DataType.DATE)
	declare stripeCurrentPeriodEnd: Date

	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	declare stripeCancelAtPeriodEnd: boolean

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare billedPrice: number

	@AllowNull(true)
	@Column(DataType.STRING)
	declare currency: string

	@AllowNull(false)
	@Column(DataType.STRING)
	declare status: string

	@AllowNull(true)
	@Column(DataType.DATE)
	declare canceledAt: Date

	@AllowNull(true)
	@Column(DataType.STRING)
	declare stripeSubscriptionScheduleId: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare nextScheduledSubscriptionType: string

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'therapistId',
		as: 'therapist',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare therapist: User

	@BelongsTo(() => TherapistSubscription, {
		foreignKey: 'therapistSubscriptionId',
		as: 'therapistSubscription',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare therapistSubscription: TherapistSubscription
}

StripeInfo.prototype.toJSON = function () {
	const item = this.get()
	delete item.stripeSubscriptionId
	delete item.stripeCustomerId
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default StripeInfo
