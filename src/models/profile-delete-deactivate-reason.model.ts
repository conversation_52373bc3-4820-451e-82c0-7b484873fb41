import {
  AllowNull,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript'
import { MainHeadingDeleteDeactivate } from '.'

export interface IProfileDeleteDeactivateReason {
  id: number
  subheading: string
  headingId: number
  createdAt: Date
  updatedAt: Date
}

@Table({
  tableName: 'profile_delete_deactivate_reasons',
  timestamps: true,
  paranoid: false,
})
class ProfileDeleteDeactivateReason extends Model implements IProfileDeleteDeactivateReason {
  @AllowNull(false)
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  declare id: number

  @AllowNull(false)
  @Column(DataType.TEXT)
  declare subheading: string

  @AllowNull(false)
  @ForeignKey(() => MainHeadingDeleteDeactivate)
  @Column(DataType.INTEGER)
  declare headingId: number

  @AllowNull(false)
  @Column(DataType.DATE)
  declare createdAt: Date

  @AllowNull(false)
  @Column(DataType.DATE)
  declare updatedAt: Date

  @BelongsTo(() => MainHeadingDeleteDeactivate)
  heading!: MainHeadingDeleteDeactivate
}

export default ProfileDeleteDeactivateReason