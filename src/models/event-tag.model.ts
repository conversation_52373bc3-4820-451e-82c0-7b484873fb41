import { AllowNull, Column, DataType, Model, Table } from 'sequelize-typescript'

export interface IEventTag {
	name: string
	description: string
	color: string
}

@Table({
	tableName: 'event_tags',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'event_tag',
		plural: 'event_tags',
	},
})
class EventTag extends Model implements IEventTag {
	@AllowNull(false)
	@Column(DataType.STRING)
	name!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	description!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	color!: string
}

EventTag.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default EventTag
