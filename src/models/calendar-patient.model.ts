import {
    AllowNull,
    BelongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    Table,
} from 'sequelize-typescript';
import User from './user.model';
import { Appointments } from '.';

export interface ICalendarPatient {
    id?: number;
    userId: number;
    isGoogleCalendar: boolean;
    isMicrosoftCalendar: boolean;
    isAppleCalendar: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    appointmentId?: number;
}

@Table({
    tableName: 'calendar_patient',
    timestamps: true,
})
class CalendarPatient extends Model<ICalendarPatient> {
    @Column({
        type: DataType.INTEGER,
        autoIncrement: true,
        primaryKey: true,
    })
    declare id: number;

    @ForeignKey(() => User)
    @AllowNull(false)
    @Column(DataType.INTEGER)
    declare userId: number;

    @BelongsTo(() => User, {
        foreignKey: 'userId',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
    })
    user!: User;

    @AllowNull(true)
	@ForeignKey(() => Appointments)
	@Column(DataType.INTEGER)
	appointmentId!: number


    @AllowNull(false)
    @Column({
        type: DataType.BOOLEAN,
        defaultValue: false,
    })
    declare isGoogleCalendar: boolean;

    @AllowNull(false)
    @Column({
        type: DataType.BOOLEAN,
        defaultValue: false,
    })
    declare isMicrosoftCalendar: boolean;

    @AllowNull(false)
    @Column({
        type: DataType.BOOLEAN,
        defaultValue: false,
    })
    declare isAppleCalendar: boolean;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW,
    })
    declare createdAt: Date;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW,
    })
    declare updatedAt: Date;

    @BelongsTo(() => Appointments, 'appointmentId')
	appointment!: Appointments
}

export default CalendarPatient;