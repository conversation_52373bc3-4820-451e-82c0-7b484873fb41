import {
	AllowNull,
	
	Column,
	DataType,
	Model,
	Table,
} from 'sequelize-typescript'

export interface ITempUpload {
	uploaded_file_key: string
}

@Table({
	tableName: 'temp_uploads',
	timestamps: false,
	paranoid: false,
	name: {
		singular: 'temp_upload',
		plural: 'temp_uploads',
	},
})
class TempUpload extends Model implements ITempUpload {
	@AllowNull(false)
	@Column(DataType.STRING)
	declare uploaded_file_key: string
}

export default TempUpload
