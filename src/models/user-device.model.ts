import {
	AllowNull,
	AutoIncrement,
	BelongsTo,
	Column,
	CreatedAt,
	DataType,
	ForeignKey,
	Model,
	PrimaryKey,
	Table,
	UpdatedAt,
} from 'sequelize-typescript'
import { User } from '.'

export interface IUserDevice {
	id: number
	userId?: number
	deviceId: string
	deviceType: string
	additionalPayload?: JSON
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'user_devices',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'user_device',
		plural: 'user_devices',
	},
})

class UserDevice extends Model implements IUserDevice {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(true)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare userId: number

	@AllowNull(false)
	@Column(DataType.TEXT)
	declare deviceId: string

	@AllowNull(false)
	@Column(DataType.ENUM('ios', 'android'))
	declare deviceType: string

	@AllowNull(true)
	@Column(DataType.JSON)
	declare additionalPayload: JSON

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'userId',
		onDelete: 'cascade',
	})
	declare user: User
}

UserDevice.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default UserDevice
