import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  AllowNull,
  BelongsTo,
  Default,
} from 'sequelize-typescript';
import { User,Appointments } from '.';

export interface ICalendarInvitePatient {
  id?: number;
  userId: number;
  calendarId?: string;
  dateTime: Date;
  calendarType: string | null;
  createdAt?: Date;
  updatedAt?: Date;
  appointmentId?: number;
}

@Table({
  tableName: 'calendar_invite_patient',
  timestamps: true,
  paranoid: false,
  name: {
    singular: 'calendar_invite_patient',
    plural: 'calendar_invite_patients',
  },
})
class CalendarInvitePatient extends Model<ICalendarInvitePatient> implements ICalendarInvitePatient {
  @AllowNull(false)
  @ForeignKey(() => User)
  @Column(DataType.INTEGER)
  declare userId: number;

  @AllowNull(false)
  @Column(DataType.STRING)
  declare calendarId: string;

  @AllowNull(false)
  @Column(DataType.DATE)
  declare dateTime: Date;

  @AllowNull(true)
  @Column(DataType.STRING)
  @Column(DataType.ENUM('Google','Apple','Microsoft'))
  declare calendarType: string;

  @BelongsTo(() => User)
  declare user: User;

  @Default(DataType.NOW)
  @AllowNull(false)
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Default(DataType.NOW)
  @AllowNull(false)
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @AllowNull(false)
	@ForeignKey(() => Appointments)
	@Column(DataType.INTEGER)
	declare appointmentId: number
}

CalendarInvitePatient.prototype.toJSON = function () {
  const values = this.get();
  delete values.createdAt;
  delete values.updatedAt;
  return values;
};

export default CalendarInvitePatient;
