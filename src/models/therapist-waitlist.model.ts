import {
	<PERSON>umn,
	Foreign<PERSON><PERSON>,
	Model,
	Table,
	DataType,
	AllowNull,
	AutoIncrement,
	PrimaryKey,
	CreatedAt,
	UpdatedAt,
	BelongsTo,
	Default,
} from 'sequelize-typescript'
import { User } from '.'

export interface ITherapistWaitlist {
	id: number
	therapistId: number
	patientId: number
	notificationPreferences: string[]
	phoneNumber: string
	status: string
	notifiedAt: Date
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'therapist_waitlist',
	timestamps: true,
	paranoid: false,
})
class TherapistWaitlist extends Model implements ITherapistWaitlist {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare therapistId: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare patientId: number

	@AllowNull(false)
	@Column(DataType.ARRAY(DataType.STRING))
	declare notificationPreferences: string[]

	@AllowNull(true)
	@Column(DataType.STRING)
	declare phoneNumber: string

	@AllowNull(false)
	@Default('waiting')
	@Column(DataType.ENUM('waiting', 'notified', 'booked', 'cancelled'))
	declare status: string

	@AllowNull(true)
	@Column(DataType.DATE)
	declare notifiedAt: Date

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'therapistId',
		as: 'therapist',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare therapist: User

	@BelongsTo(() => User, {
		foreignKey: 'patientId',
		as: 'patient',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare patient: User
}

TherapistWaitlist.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default TherapistWaitlist
