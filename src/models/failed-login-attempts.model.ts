import {
    AllowNull,
    AutoIncrement,
    Column,
    DataType,
    Foreign<PERSON>ey,
    Model,
    PrimaryKey,
    Table,
    CreatedAt,
    UpdatedAt,
  } from "sequelize-typescript";
  import { User } from ".";
  
  export interface IFailedLoginAttempt {
    userId?: number;
    ipAddress: string;
    reason: string;
    createdAt?: Date;
    updatedAt?: Date;
    type:string;
  }
  
  @Table({
    tableName: "failed_login_attempts",
    timestamps: true,
    paranoid: false,
  })
  class FailedLoginAttempt extends Model<IFailedLoginAttempt> {
    @PrimaryKey
    @AutoIncrement
    @Column(DataType.INTEGER)
    declare id: number;
  
    @AllowNull(false)
    @ForeignKey(() => User)
    @Column(DataType.INTEGER)
    declare userId?: number;  
  
    @AllowNull(false)
    @Column(DataType.STRING)
    declare ipAddress: string;
  
    @AllowNull(false)
    @Column(DataType.STRING)
    declare reason: string;

    @AllowNull(false)
    @Column(DataType.STRING)
    declare type: string;
  
    @CreatedAt
    @AllowNull(false)
    @Column(DataType.DATE)
    declare createdAt: Date;
  
    @UpdatedAt
    @AllowNull(false)
    @Column(DataType.DATE)
    declare updatedAt: Date;
  }
  
  export default FailedLoginAttempt;
  