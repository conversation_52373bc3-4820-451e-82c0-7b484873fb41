import { <PERSON>, Table, PrimaryKey, AutoIncrement, Column, <PERSON>Type, AllowNull, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "sequelize-typescript"
import TherapistSubscription from "./therapist-subscription.model"

export interface ISubscriptionPlan {
	id: number
	annualPrice: number
	monthlyPrice: number
	isActive: boolean
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'subscription_plan',
	timestamps: true,
	paranoid: false,
})

class SubscriptionPlan extends Model implements ISubscriptionPlan {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@Column(DataType.DECIMAL(8, 2))
	declare annualPrice: number

	@AllowNull(false)
	@Column(DataType.DECIMAL(8, 2))
	declare monthlyPrice: number

	@AllowNull(false)
	@Default(true)
	@Column(DataType.BOOLEAN)
	declare isActive: boolean

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@HasMany(() => TherapistSubscription, {
		foreignKey: 'subscriptionPlanId',
		as: 'subscriptions',
	})
	declare subscriptions: TherapistSubscription[]
}

SubscriptionPlan.prototype.toJSON = function () {
	const item = this.get()
	delete item.updatedAt
	return item
}

export default SubscriptionPlan
