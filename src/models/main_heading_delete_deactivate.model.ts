import {
  AllowNull,
  Column,
  DataType,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript'
import { ProfileDeleteDeactivateReason } from '.'

export interface IMainHeadingDeleteDeactivate {
  id: number
  heading: string
  createdAt: Date
  updatedAt: Date
}

@Table({
  tableName: 'main_heading_delete_deactivate',
  timestamps: true,
  paranoid: false,
})
class MainHeadingDeleteDeactivate extends Model implements IMainHeadingDeleteDeactivate {
  @AllowNull(false)
  @Column({
    type: DataType.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  })
  declare id: number

  @AllowNull(false)
  @Column(DataType.TEXT)
  declare heading: string

  @AllowNull(false)
  @Column(DataType.DATE)
  declare createdAt: Date

  @AllowNull(false)
  @Column(DataType.DATE)
  declare updatedAt: Date

  @HasMany(() => ProfileDeleteDeactivateReason)
  subheadings!: ProfileDeleteDeactivateReason[]
}

export default MainHeadingDeleteDeactivate