import {
	Column,
	Foreign<PERSON>ey,
	Model,
	Table,
	DataType,
	AllowNull,
	AutoIncrement,
	PrimaryKey,
	CreatedAt,
	UpdatedAt,
	BelongsTo,
	HasOne,
} from 'sequelize-typescript'
import { StripeInfo, SubscriptionPlan, User } from '.'

export interface ITherapistSubscription {
	id: number
	therapistId: number
	subscriptionPlanId: number
	subscriptionType: string
	isActive: boolean
	subscribedAt?: Date
	expiredAt?: Date
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'therapist_subscription',
	timestamps: true,
	paranoid: false,
})
class TherapistSubscription extends Model implements ITherapistSubscription {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare therapistId: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare subscriptionPlanId: number

	@AllowNull(false)
	@Column(DataType.ENUM('monthly', 'yearly'))
	declare subscriptionType: string

	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	declare isActive: boolean

	@AllowNull(true)
	@Column(DataType.DATE)
	declare subscribedAt: Date

	@AllowNull(true)
	@Column(DataType.DATE)
	declare expiredAt: Date

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'therapistId',
		as: 'therapist',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare therapist: User

	@BelongsTo(() => SubscriptionPlan, {
		foreignKey: 'subscriptionPlanId',
		as: 'subscriptionPlan',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare subscriptionPlan: SubscriptionPlan

	@HasOne(() => StripeInfo, {
		foreignKey: 'therapistSubscriptionId',
		as: 'stripeInfo',
	})
	declare stripeInfo: StripeInfo
}

TherapistSubscription.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default TherapistSubscription
