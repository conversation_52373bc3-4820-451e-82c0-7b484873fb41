import {
    AllowNull,
    Column,
    DataType,
    Model,
    Table,
} from 'sequelize-typescript';

export interface IPatientDeleteDeactivateReason {
    id?: number;
    heading: string;
    createdAt?: Date;
    updatedAt?: Date;
}

@Table({
    tableName: 'patient_delete_deactivate_reasons',
    timestamps: true,
})
class PatientDeleteDeactivateReason extends Model<IPatientDeleteDeactivateReason> {
    @AllowNull(false)
    @Column({
        primaryKey: true,
        autoIncrement: true,
        type: DataType.INTEGER,
    })
    id!: number;

    @AllowNull(false)
    @Column(DataType.TEXT)
    heading!: string;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW,
    })
    createdAt!: Date;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW,
    })
    updatedAt!: Date;
}

export default PatientDeleteDeactivateReason;