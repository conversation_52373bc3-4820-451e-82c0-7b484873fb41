import {
	AllowNull,
	BelongsTo,
	BelongsToMany,
	Column,
	DataType,
	HasMany,
	HasOne,
	Model,
	Table,
} from 'sequelize-typescript'
import { AnswerFollowThrough, Page, PageAnswer } from '.'

export interface IAnswer {
	answer: string
	info: string,
	slug: string,
	// followThroughId?:  number,
}

@Table({
	tableName: 'answers',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'answer',
		plural: 'answers',
	},
})
class Answer extends Model implements IAnswer {
	@AllowNull(false)
	@Column(DataType.STRING)
	declare answer: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare info: string


	@AllowNull(false)
	@Column(DataType.STRING)
	declare slug: string

	// @AllowNull(true)
	// @Column(DataType.INTEGER)
	// declare followThroughId?: number

	@BelongsToMany(() => Page, () => PageAnswer)
	pages!: Page[]

	declare PageAnswer?: PageAnswer
	// @BelongsTo(() => Page, {
	// 	foreignKey: 'copiedFromPage',
	// })
	// page!: Page
	// @BelongsTo(() => Answer, {
	// 	foreignKey: 'followThroughId',
	// })
	// followThroughPage!: Answer

	@HasMany(() => AnswerFollowThrough, {
		foreignKey: 'followingBy',
		as: 'followingByAnswers',
	})
	declare followingByAnswers?: AnswerFollowThrough[]

	@HasMany(() => AnswerFollowThrough, {
		foreignKey: 'followingTo',
		as: 'followingToAnswers',
	})
	declare followingToAnswers?: AnswerFollowThrough[]
}

Answer.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default Answer
