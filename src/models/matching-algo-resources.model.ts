import {
  AllowNull,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
  BelongsTo
} from 'sequelize-typescript'
import { Page, Questionnaire, Answer, TherapistPage } from '.'

export interface IMatchingAlgoResource {
  therapistPageId: number
  patientPageId: number
  category: string
  questionId: number
  answerId?: number | null
}

@Table({ 
  tableName: 'matching_algo_resources', 
  timestamps: false, 
  paranoid: false ,
  name: {
    singular: 'matching_algo_resource',
    plural: 'matching_algo_resources',
  }
})
class MatchingAlgoResource extends Model implements IMatchingAlgoResource {
  @AllowNull(false)
  @ForeignKey(() => TherapistPage)
  @Column(DataType.INTEGER)
  therapistPageId!: number;

  @AllowNull(false)
  @ForeignKey(() => Page)
  @Column(DataType.INTEGER)
  patientPageId!: number;

  @AllowNull(false)
	@Column(DataType.STRING)
	category!: string

  @AllowNull(false)
  @ForeignKey(() => Questionnaire)
  @Column(DataType.INTEGER)
  questionId!: number;

  @AllowNull(true)
  @ForeignKey(() => Answer)
  @Column(DataType.INTEGER)
  answerId!: number

  @BelongsTo(() => TherapistPage, {
    foreignKey: 'therapistPageId',
  })
  therapistPage!: TherapistPage

  @BelongsTo(() => Page, {
    foreignKey: 'patientPageId',
  })
  patientPage!: Page

  @BelongsTo(() => Questionnaire, {
		foreignKey: 'questionId',
	})
	questionnaire!: Questionnaire

  @BelongsTo(() => Answer, {
		foreignKey: 'answerId',
	})
	answer!: Answer
}

export default MatchingAlgoResource
