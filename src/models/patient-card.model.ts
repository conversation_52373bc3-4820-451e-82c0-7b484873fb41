import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, ForeignKey, Model, PrimaryKey, Table } from "sequelize-typescript";
import User from "./user.model";

export interface IPatientCard {
	id: number;
	patientId: number;
	stripeSetupIntentId: string;
	stripePaymentMethodId: string;
	cardLastFourDigits: string | null;
	cardBrand: string | null;
	cardExpMonth: number | null;
	cardExpYear: number | null;
	cardFingerprint: string | null;
	createdAt: Date;
	updatedAt: Date;
}

@Table({
	tableName: 'patient_cards',
	timestamps: true,
	paranoid: false,
})

class PatientCard extends Model implements IPatientCard {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare patientId: number

	@AllowNull(false)
	@Column(DataType.STRING)
	declare stripeSetupIntentId: string

	@AllowNull(false)
	@Column(DataType.STRING)
	declare stripePaymentMethodId: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare cardLastFourDigits: string | null

	@AllowNull(true)
	@Column(DataType.STRING)
	declare cardBrand: string | null

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare cardExpMonth: number | null

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare cardExpYear: number | null

	@AllowNull(true)
	@Column(DataType.STRING)
	declare cardFingerprint: string | null

	@AllowNull(false)
	@Column(DataType.DATE)
	declare createdAt: Date

	@AllowNull(false)
	@Column(DataType.DATE)
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'patientId',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare patient: User
}

export default PatientCard;