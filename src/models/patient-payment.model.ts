import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
	AllowNull,
	AutoIncrement,
	PrimaryKey,
	CreatedAt,
	UpdatedAt,
	BelongsTo,
} from 'sequelize-typescript'
import { Appointments, TherapistSubscription, User } from '.'

export interface IPatientPayment {
	id: number
	patientId: number
	therapistId: number
	appointmentId: number
	stripePaymentId?: string
	stripeCustomerId?: string
	billedPrice?: number
	currency?: string
	createdAt: Date
	updatedAt: Date
}

@Table({
	tableName: 'patient_payments',
	timestamps: true,
	paranoid: false,
})

class PatientPayment extends Model implements IPatientPayment {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare patientId: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare therapistId: number

	@AllowNull(false)
	@ForeignKey(() => Appointments)
	@Column(DataType.INTEGER)
	declare appointmentId: number

	@AllowNull(true)
	@Column(DataType.STRING)
	declare stripePaymentId: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare stripeCustomerId: string

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare billedPrice: number

	@AllowNull(true)
	@Column(DataType.STRING)
	declare currency: string

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'patientId',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare patient: User

	@BelongsTo(() => User, {
		foreignKey: 'therapistId',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare therapist: User

	@BelongsTo(() => Appointments, {
		foreignKey: 'appointmentId',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare appointment: TherapistSubscription
}

PatientPayment.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default PatientPayment
