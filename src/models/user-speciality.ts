import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
} from 'sequelize-typescript'
import User from './user.model'
import { Speciality } from '.'

export interface IUserSpeciality {
	specialityId: number
	userId: number
}

@Table({
	tableName: 'user_speciality',
	timestamps: true,
	paranoid: false,
})
class UserSpeciality extends Model implements IUserSpeciality {
	@ForeignKey(() => Speciality)
	@Column(DataType.INTEGER)
	specialityId!: number

	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	userId!: number
}

export default UserSpeciality
