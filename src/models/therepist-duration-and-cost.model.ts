import {
	AllowNull,
	BelongsTo,
	Column,
	DataType,
	ForeignKey,
	Model,
	Table,
} from 'sequelize-typescript';
import { User } from '.';

export interface ITherapistDurationAndCost {
	id: number;
	therapistId: number | null;
	category: string;
	duration: number;
	amount: number;
	createdAt: Date;
	updatedAt: Date;
}

@Table({
	tableName: 'therapist_duration_and_cost',
	timestamps: true,
})
class TherapistDurationAndCost extends Model<ITherapistDurationAndCost> {
	@AllowNull(false)
	@Column({
		primaryKey: true,
		autoIncrement: true,
		type: DataType.INTEGER,
	})
	id!: number;

	@AllowNull(true)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	therapistId!: number | null;

	@AllowNull(false)
	@Column(DataType.STRING)
	category!: string;

	@AllowNull(false)
	@Column(DataType.INTEGER)
	duration!: number;

	@AllowNull(false)
	@Column(DataType.DECIMAL(10, 2))
	amount!: number;

	@BelongsTo(() => User, 'therapistId')
	therapist!: User;

	@AllowNull(false)
	@Column({
		type: DataType.DATE,
		defaultValue: DataType.NOW,
	})
	createdAt!: Date;

	@AllowNull(false)
	@Column({
		type: DataType.DATE,
		defaultValue: DataType.NOW,
	})
	updatedAt!: Date;
}

export default TherapistDurationAndCost;
