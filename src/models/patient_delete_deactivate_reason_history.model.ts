import {
  AllowNull,
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table
} from 'sequelize-typescript';
import PatientDeleteDeactivateReason from './patient_delete_deactivate_reason.model';
import User from './user.model';

interface IPatientDeleteDeactivateReasonsHistory {
  id: number;
  reasonId: number;
  type: 'deleted' | 'deactivated';
  createdAt: Date;
  updatedAt: Date;
}

@Table({
  tableName: 'patient_delete_deactivate_reasons_history',
  timestamps: true
})
class PatientDeleteDeactivateReasonsHistory extends Model implements IPatientDeleteDeactivateReasonsHistory {
  @AllowNull(false)
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true
  })
  declare id: number;

  @AllowNull(false)
  @ForeignKey(() => PatientDeleteDeactivateReason)
  @Column(DataType.INTEGER)
  declare reasonId: number;

   @AllowNull(false)
  @ForeignKey(() => User)
  @Column(DataType.INTEGER)
  declare userId: number;

  @AllowNull(false)
  @Column(DataType.ENUM('deleted', 'deactivated'))
  declare type: 'deleted' | 'deactivated';

  @BelongsTo(() => PatientDeleteDeactivateReason, {
    foreignKey: 'reasonId',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  })
  declare reason: PatientDeleteDeactivateReason;

  @AllowNull(false)
  @Column(DataType.DATE)
  declare createdAt: Date;

  @AllowNull(false)
  @Column(DataType.DATE)
  declare updatedAt: Date;
}

export default PatientDeleteDeactivateReasonsHistory;