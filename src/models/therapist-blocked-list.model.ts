import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
	AllowNull,
	BelongsTo
} from 'sequelize-typescript'
import { User } from '.'

export interface ITherapistBlockedList {
	therapistId: number
	patientId: number
}

@Table({
	tableName: 'therapist_blocked_list',
	timestamps: true,
	paranoid: false,
})
class TherapistBlockedList extends Model implements ITherapistBlockedList {
	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare therapistId: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare patientId: number

	@BelongsTo(() => User, {
		foreignKey: 'therapistId',
		onDelete: 'CASCADE',
	})
	declare therapist: User

	@BelongsTo(() => User, {
		foreignKey: 'patientId',
		onDelete: 'CASCADE',
	})
	declare patient: User
}

export default TherapistBlockedList
