import {
	AllowNull,
	Column,
	DataType,
	Foreign<PERSON>ey,
	HasMany,
	Model,
	Table,
} from 'sequelize-typescript'
import { Answer, Page, UserValues } from '.'

export interface IPageAnswer {
	answerId: number
	pageId: number
	conditions: JSON
	answerGroup?: string,
	copiedFromPage?: number,
	position?: string,
	followedByAns?: number,
	ansOrder? :number
}

@Table({
	tableName: 'page_answer',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'page_answer',
		plural: 'page_answer',
	},
})
class PageAnswer extends Model implements IPageAnswer {
	@ForeignKey(() => Answer)
	@Column(DataType.INTEGER)
	answerId!: number

	@ForeignKey(() => Page)
	@Column(DataType.INTEGER)
	pageId!: number

	@AllowNull
	@Column(DataType.JSONB)
	conditions!: JSON

	@AllowNull
	@Column(DataType.STRING)
	answerGroup!: string

	@AllowNull
	@Column(DataType.STRING)
	declare position: string

	@AllowNull
	@Column(DataType.INTEGER)
	declare ansOrder?: number

	@AllowNull
	@ForeignKey(() => Page)
	@Column(DataType.INTEGER)
	copiedFromPage!: number

	@AllowNull
	@ForeignKey(() => Answer)
	@Column(DataType.INTEGER)
	followedByAns!: number
}

PageAnswer.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default PageAnswer
