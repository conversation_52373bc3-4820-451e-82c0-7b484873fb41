import { <PERSON>, Table, PrimaryKey, AutoIncrement, Column, DataType, ForeignKey, AllowNull, CreatedAt, UpdatedAt, BelongsTo, HasMany, <PERSON><PERSON><PERSON> } from "sequelize-typescript"
import User from "./user.model"
import { Appointments } from "."

export interface INormalOfficeHours {
	id: number
	userId: number
	workingDay: string
	availabilityHours: string
	availabilityHoursPlain: string
	appointmentMethod: string
	isDisabled: boolean
	createdAt: Date
	updatedAt: Date
	deletedAt: Date
}

@Table({
	tableName: 'normal_office_hours',
	timestamps: true,
	paranoid: false,
})

class NormalOfficeHours extends Model implements INormalOfficeHours {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare userId: number

	@AllowNull(false)
	@Column(DataType.STRING)
	declare workingDay: string

	@AllowNull(false)
	@Column(DataType.STRING)
	declare availabilityHours: string

	@AllowNull(false)
	@Column(DataType.STRING)
	declare availabilityHoursPlain: string

	@AllowNull(true)
	@Column(DataType.ENUM('in-person', 'telehealth', 'in-person-and-telehealth'))
	declare appointmentMethod: string;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isDisabled: boolean;

	@CreatedAt
	@AllowNull(false)
	@Column
	declare createdAt: Date

	@UpdatedAt
	@AllowNull(false)
	@Column
	declare updatedAt: Date

	@AllowNull(true)
	@Column(DataType.DATE)
	declare deletedAt: Date

	@BelongsTo(() => User, {
		foreignKey: 'userId',
		as: 'user',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare user: User

	@HasMany(() => Appointments, {
		foreignKey: 'workingHourId',
    as: 'appointments',
		onDelete: 'set null',
		onUpdate: 'cascade',
	})
	declare appointments: Appointments[]
}

NormalOfficeHours.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default NormalOfficeHours
