import { AllowNull, Column, <PERSON>Type, <PERSON><PERSON>any, Model, Table } from 'sequelize-typescript'
import UserQuestionnaire from './user-questionnaire.model'

export interface IQuestionnaire {
	question: string
	info: string
}

@Table({
	tableName: 'questionnaires',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'questionnaire',
		plural: 'questionnaires',
	},
})
class Questionnaire extends Model implements IQuestionnaire {
	@AllowNull(false)
	@Column(DataType.STRING)
	question!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	info!: string

	@HasMany(() => UserQuestionnaire, {
		foreignKey: 'questionnaireId',
		onDelete: 'CASCADE',
		as: 'user_answer'
	})
	declare user_answer: UserQuestionnaire[]
}

Questionnaire.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default Questionnaire
