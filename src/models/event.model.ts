import {
	AllowNull,
	BelongsTo,
	Column,
	DataType,
	ForeignKey,
	Model,
	Table,
} from 'sequelize-typescript'
import { EventTag } from '.'

export interface IEvent {
	tagId: number
	name: string
	description: string
	startDate: Date
	endDate: Date
	isAllDay: boolean
	isRecurring: boolean
	recurringType: string
	calendarEventId: string
	location: string
	attendees: JSON
}

@Table({
	tableName: 'events',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'event',
		plural: 'events',
	},
})
class Event extends Model implements IEvent {
	@AllowNull(true)
	@ForeignKey(() => EventTag)
	@Column(DataType.INTEGER)
	tagId!: number

	@AllowNull(false)
	@Column(DataType.STRING)
	name!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	description!: string

	@AllowNull(false)
	@Column(DataType.DATE)
	startDate!: Date

	@AllowNull(true)
	@Column(DataType.DATE)
	endDate!: Date

	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	isAllDay!: boolean

	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	isRecurring!: boolean

	@AllowNull(true)
	@Column(DataType.STRING)
	calendarEventId!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	recurringType!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	location!: string

	@AllowNull(true)
	@Column(DataType.JSONB)
	attendees!: JSON

	@BelongsTo(() => EventTag, 'tagId')
	tag!: EventTag
}

Event.prototype.toJSON = function () {
	const record = this.get()
	delete record.createdAt
	delete record.updatedAt
	record.attendees = record.attendees || []
	return record
}

export default Event
