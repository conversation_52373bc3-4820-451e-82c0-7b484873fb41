import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
} from 'sequelize-typescript'
import User from './user.model'

export interface IPatientTherapist {
	patientId: number
	therapistId: number
}

@Table({
	tableName: 'patient_therapist',
	timestamps: true,
	paranoid: false,
})
class PatientTherapist extends Model implements IPatientTherapist {
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	patientId!: number

	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	therapistId!: number
}

export default PatientTherapist
