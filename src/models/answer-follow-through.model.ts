import {
	AllowNull,
	BelongsTo,
	Column,
	<PERSON>T<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Model,
	Table,
} from 'sequelize-typescript'
import Answer from './answer.model';

export interface IAnswerFollowThrough {
	followingBy: number;
	followingTo: number;
	isPrimary?: boolean;

	followingToAnswer?:  Answer
	followingByAnswer?:  Answer
}

@Table({
	tableName: 'answer_follow_through',
	timestamps: false,
	paranoid: false,
	name: {
		singular: 'answer_follow_through',
		plural: 'answer_follow_through',
	},
})
class AnswerFollowThrough extends Model implements IAnswerFollowThrough {
	@AllowNull(false)
	@Column(DataType.INTEGER)
	declare followingBy: number;

	@AllowNull(false)
	@Column(DataType.INTEGER)
	declare followingTo: number;

	@Default(true)
	@AllowNull(false)
	@Column(DataType.BOOLEAN)
	declare isPrimary: boolean;

	@BelongsTo(() => Answer, {
		foreignKey: 'followingTo',
		as: 'followingToAnswer',
	})
	declare followingToAnswer?: Answer

	@BelongsTo(() => Answer, {
		foreignKey: 'followingBy',
		as: 'followingByAnswer',
	})
	declare followingByAnswer?: Answer
}

export default AnswerFollowThrough
