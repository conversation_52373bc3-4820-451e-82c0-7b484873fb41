import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
	AllowNull,
	BelongsTo
} from 'sequelize-typescript'
import { User } from '.'

export interface IPatientProfile {
	userId: number
	paymentPreference?: string
	preferredDistance?: string
	telehealth?: boolean
	seekingTherapyFor?: string
}

@Table({
	tableName: 'patient_profile',
	timestamps: true,
	paranoid: false,
})
class PatientProfile extends Model implements IPatientProfile {
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare userId: number

	@AllowNull(true)
	@Column(DataType.STRING)
	declare paymentPreference?: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare preferredDistance?: string

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	declare telehealth?: boolean

	@AllowNull(true)
	@Column(DataType.ENUM('self', 'couple', 'child', 'teen'))
	declare seekingTherapyFor?: string

	@AllowNull(true)
	@Column({
		type: DataType.STRING,
		defaultValue: null
	})
	declare bio?: string

	@AllowNull(true)
	@Column({
		type: DataType.STRING,
		defaultValue: null
	})
	declare userProfile?: string

	@BelongsTo(() => User, {
		foreignKey: 'userId',
		onDelete: 'CASCADE',
	})
	declare patient: User
}

export default PatientProfile
