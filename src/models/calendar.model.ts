import {
	AllowNull,
	BelongsTo,
	Column,
	DataType,
	ForeignKey,
	Model,
	Table,
} from 'sequelize-typescript'
import { User } from '.'

export interface ICalendar {
	userId: number
	type: string
	email: string
	credentials: JSO<PERSON>
}

@Table({
	tableName: 'calendars',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'calendar',
		plural: 'calendars',
	},
})
class Calendar extends Model implements ICalendar {
	@AllowNull(true)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	userId!: number

	@AllowNull(false)
	@Column(DataType.STRING)
	type!: string

	@AllowNull(false)
	@Column(DataType.STRING)
	email!: string

	@AllowNull(true)
	@Column(DataType.JSONB)
	credentials!: JSON

	@BelongsTo(() => User, 'userId')
	user!: User
}

Calendar.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default Calendar
