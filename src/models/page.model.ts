import {
	AllowNull,
	BelongsTo,
	BelongsToMany,
	Column,
	DataType,
	De<PERSON>ult,
	Foreign<PERSON>ey,
	Model,
	Table,
} from 'sequelize-typescript'
import { Answer, PageAnswer, Questionnaire } from '.'

export interface IPage {
	code: string
	title: string
	info: string
	extra: string
	button: string
	buttonAction: string
	nextPageId?: number | null
	skipToPageId: number
	type: string
	category: string
	sortOrder: number
	questionnaireId: number
	questionnaireType: string
	initialPage: boolean
	required: boolean
	isFollowedThrough?: boolean
	matcherInvolvement?: boolean
}

@Table({
	tableName: 'pages',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'page',
		plural: 'pages',
	},
})
class Page extends Model implements IPage {
	@AllowNull(false)
	@Column(DataType.STRING)
	code!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	title!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	info!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	extra!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	button!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	buttonAction!: string // next-page, register, end...

	@AllowNull(true)
	@Column(DataType.INTEGER)
	nextPageId!: number

	@AllowNull(true)
	@Column(DataType.INTEGER)
	skipToPageId!: number

	@AllowNull(false)
	@Column(DataType.STRING)
	type!: string

	@AllowNull(false)
	@Column(DataType.STRING)
	category!: string

	@AllowNull(false)
	@Column(DataType.INTEGER)
	sortOrder!: number

	@Column(DataType.BOOLEAN)
	required!: boolean

	@AllowNull(true)
	@ForeignKey(() => Questionnaire)
	@Column(DataType.INTEGER)
	questionnaireId!: number

	@AllowNull(true)
	@Column(DataType.STRING)
	questionnaireType!: string

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	initialPage!: boolean

	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isFollowedThrough?: boolean

	@Default(false)
	@Column(DataType.BOOLEAN)
	declare matcherInvolvement?: boolean

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	declare isPrimaryFollowThrough?: boolean

	@BelongsTo(() => Questionnaire, {
		foreignKey: 'questionnaireId',
	})
	questionnaire!: Questionnaire

	@BelongsTo(() => Page, {
		foreignKey: 'nextPageId',
	})
	nextPage!: Page

	@BelongsTo(() => Page, {
		foreignKey: 'skipToPageId',
	})
	skipPage!: Page

	@BelongsToMany(() => Answer, {
		through: {
			model: () => PageAnswer,
		},
		foreignKey: 'pageId',
		otherKey: 'answerId',
	})
	answers!: Answer[]
}

Page.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default Page
