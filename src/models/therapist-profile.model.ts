import {
	Column,
	ForeignKey,
	Model,
	Table,
	DataType,
	AllowNull,
} from 'sequelize-typescript'
import { User } from '.'

export interface ITherapistProfile {
	userId: number
	bio: string
	acceptingPatients: boolean
	acceptsInsurance: boolean
	telehealth: boolean
	InPerson: boolean
	education: JSON
	license: string
	experience: JSON
	therapyStyle: JSON
}

@Table({
	tableName: 'therapist_profile',
	timestamps: true,
	paranoid: false,
})
class TherapistProfile extends Model implements ITherapistProfile {
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	userId!: number

	@AllowNull(true)
	@Column(DataType.STRING)
	bio!: string

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	acceptingPatients!: boolean

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	acceptsInsurance!: boolean

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	telehealth!: boolean

	@AllowNull(true)
	@Column(DataType.BOOLEAN)
	InPerson!: boolean

	@AllowNull(true)
	@Column(DataType.JSONB)
	education!: JSON

	@AllowNull(true)
	@Column(DataType.STRING)
	license!: string

	@AllowNull(true)
	@Column(DataType.JSONB)
	experience!: JSON

	@AllowNull(true)
	@Column(DataType.JSONB)
	therapyStyle!: JSON
}

TherapistProfile.prototype.toJSON = function () {
	const item = this.get()
	delete item.createdAt
	delete item.updatedAt
	return item
}

export default TherapistProfile
