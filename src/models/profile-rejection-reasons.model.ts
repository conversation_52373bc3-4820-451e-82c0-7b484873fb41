import {
    AllowNull,
    AutoIncrement,
    Column,
    DataType,
    Foreign<PERSON>ey,
    Model,
    PrimaryKey,
    Table
} from "sequelize-typescript";
import { User } from ".";

export interface IProfileRejectionReason {
    id: number;
    userId: number;
    reason: string;
    rejectedAt: Date;
}

@Table({
    tableName: 'profile_rejection_reasons',
    timestamps: false,
    paranoid: false
})
class ProfileRejectionReason extends Model implements IProfileRejectionReason {
    @PrimaryKey
    @AutoIncrement
    @Column(DataType.INTEGER)
    declare id: number;

    @AllowNull(false)
    @ForeignKey(() => User)
    @Column(DataType.INTEGER)
    declare userId: number;

    @AllowNull(false)
    @Column(DataType.TEXT)
    declare reason: string;

    @AllowNull(false)
    @Column({
        type: DataType.DATE,
        defaultValue: DataType.NOW
    })
    declare rejectedAt: Date;
}

export default ProfileRejectionReason;
