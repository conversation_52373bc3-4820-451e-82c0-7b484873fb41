import {
	<PERSON>ow<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>umn,
	<PERSON>T<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>any,
	Has<PERSON>ne,
	Model,
	NotEmpty,
	Table,
	Unique,
} from 'sequelize-typescript'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import Speciality from './speciality.model'
import UserSpeciality from './user-speciality'
import Appointments from './appointments.model' 
import PatientTherapist from './patient-therapist.model'
import { Calendar, TherapistBlockedList, TherapistProfile, UserRegistrationInfo, UserSettings, NormalOfficeHours, PatientProfile, TherapistSubscription, StripeInfo,TherapistDurationAndCost, UserDevice, PatientPayment, MinorPatient, TherapistWaitlist, UserDeleteDeactivateReason, ProfileRejectionReason } from '.'
import { ForbiddenError } from '../application/handlers/errors'
import {decryptData,encryptData} from '@/src/cryptoUtil'

export interface IUser {
	firstname: string
	lastname: string
	email: string
	emailVerifiedAt?: Date| null
	deactivatedAt: Date
	password: string
	dob: Date
	gender: string
	phone: string
	phoneVerifiedAt: Date
	role: string
	passwordResetToken: string
	address: JSON
	coordinate: string
	acceptedAt?: Date | null
	rejectedAt?: Date | null
	currentPage: string
	userToken: string
	passwordUpdatedAt: Date
	timeZone?: string
	country_code:string
	mfaEnabled: boolean
	isUnderReview: boolean
	stripeCustomerId: string
	deletedAt: Date | null
	deletedBy: string | null
	therapy_for: string | null
	reason_for_help: string | null
	stripeConnectAccountId: string | null
}

@Table({
	tableName: 'users',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'user',
		plural: 'users',
	},
})
class User extends Model implements IUser {
	@AllowNull(false)
	@Column(DataType.STRING)
	declare firstname: string

	@AllowNull(false)
	@Column(DataType.STRING)
	declare lastname: string

	@AllowNull(true)
	@NotEmpty({ msg: 'Email is required' })
	@Unique({ name: 'E-mail', msg: 'Email already exists' })
	@Column({
	type: (DataType.STRING(1024)),
		get() {
			const encryptedEmail = this.getDataValue('email');
			return encryptedEmail ? decryptData(encryptedEmail) : null;
		},
			set(value: string) {
				if (value) {
					this.setDataValue('email', encryptData(value.toLowerCase().trim()));
				}
			}
		})
		declare email: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare emailVerifiedAt: Date | null

	@AllowNull(true)
	@Column(DataType.DATE)
	declare deactivatedAt: Date

	@AllowNull(true)
	@Column(DataType.DATE)
	declare deletedAt: Date | null

	@AllowNull(true)
	@Column(DataType.STRING)
	declare deletedBy: string | null

	@AllowNull(true)
	@Column(DataType.STRING)
	password!: string

	@AllowNull(true)
	@Column(DataType.DATE)
	dob!: Date

	@AllowNull(true)
	@Column(DataType.STRING)
	gender!: string

	@AllowNull(true)
	@Column({
		type: DataType.STRING,
		get(): string | null {
			const encryptedPhone: string | null = this.getDataValue('phone');
			return encryptedPhone ? decryptData(encryptedPhone) : null;
		},
		set(value: string | null): void {
			if (value) {
				this.setDataValue('phone', encryptData(value.trim()));
			} else {
				this.setDataValue('phone', null);
			}
		}
	})
	declare phone: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	phoneVerifiedAt!: Date

	@AllowNull(false)
	@Column(DataType.STRING)
	role!: string

	@AllowNull(true)
	@Column(DataType.STRING)
	passwordResetToken!: string

	@AllowNull(true)
	@Column(DataType.JSONB)
	address!: JSON

	@AllowNull(true)
	@Column(DataType.GEOMETRY('POINT'))
	coordinate!: string

	@AllowNull(true)
	@Column(DataType.DATE)
	declare acceptedAt: Date | null

	@AllowNull(true)
	@Column(DataType.DATE)
	declare rejectedAt: Date | null

	@AllowNull(true)
	@Column(DataType.STRING)
	declare currentPage: string

	@AllowNull(true)
	@Unique
	@Column(DataType.STRING)
	declare userToken: string

	@AllowNull(true)
	@Column(DataType.DATE)
	declare passwordUpdatedAt: Date
	
	@AllowNull(true)
	@Column({
		type: DataType.STRING,
		defaultValue: null
	})
	declare bio?: string

	@AllowNull(true)
	@Column({
		type: DataType.STRING,
		defaultValue: null
	})
	declare userProfile?: string

	@AllowNull(true)
	@Column(DataType.STRING)
	declare timeZone: string

	@AllowNull(true)
	@Column(DataType.INTEGER)
	declare version: number

	@AllowNull(false)
	@Column({
	type: DataType.BOOLEAN,
	defaultValue: true,
	})
	declare active: boolean;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare mfaCode: string

	@AllowNull(true)
	@Column(DataType.DATE)
	declare mfaExpiresAt: Date

	@AllowNull(true)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare mfaEnabled: boolean

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare email_hash: string;

	@AllowNull(true)
	@Column(DataType.TEXT)
	declare phone_hash: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare otp: string;

	@AllowNull(true)
	@Column(DataType.DATE)
	declare otpExpiresAt: Date;
	
	@AllowNull(true)
	@Column({
		type: DataType.STRING,
		defaultValue: '+1',
	})
	declare country_code: string;

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isUnderReview: boolean;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare stripeCustomerId: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare therapy_for: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare reason_for_help: string;

	@AllowNull(true)
	@Column(DataType.STRING)
	declare stripeConnectAccountId: string;

	@HasOne(() => TherapistProfile, {
		foreignKey: 'userId',
		as: 'profile',
	})
	profile!: TherapistProfile

	@HasOne(() => PatientProfile, {
		foreignKey: 'userId',
		as: 'patient_profile',
	})
	declare patient_profile: PatientProfile

	@HasOne(() => UserSettings, {
		foreignKey: 'userId',
		as: 'settings',
	})
	declare settings: UserSettings

	@HasMany(() => Appointments, {
		foreignKey: 'patientId',
		sourceKey:'id',
		as: 'patientAppointments',
		onDelete: 'cascade',
	})
	declare patientAppointments: Appointments[];
	
	@HasMany(() => Appointments, {
		foreignKey: 'therapistId',
		sourceKey:'id',
		as: 'therapistAppointments',
		onDelete: 'cascade',
	})
	declare therapistAppointments: Appointments[];

	@HasMany(() => UserDevice, {
		foreignKey: 'userId',
		as: 'userDevices',
	})
	declare userDevices: UserDevice[]

	@HasMany(() => TherapistDurationAndCost, {
		foreignKey: 'therapistId',
		as: 'durationAndCost'
	})
	declare durationAndCost: TherapistDurationAndCost[]

	// Payments made by patients
	@HasMany(() => PatientPayment, {
		foreignKey: 'patientId',
	})
	declare patientPayments: PatientPayment[]
	
	// Payments received by therapists
	@HasMany(() => PatientPayment, {
		foreignKey: 'therapistId',
	})
	declare therapistPayments: PatientPayment[]

	@HasMany(() => TherapistWaitlist, {
		foreignKey: 'therapistId',
		sourceKey:'id',
		as: 'therapistWaitlist',
		onDelete: 'cascade',
		onUpdate: 'cascade',
	})
	declare therapistWaitlist: TherapistWaitlist[];

	@HasMany(() => ProfileRejectionReason, {
		foreignKey: 'userId',
		sourceKey: 'id',
		as: 'rejectionReasons',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare rejectionReasons: ProfileRejectionReason[];

	@HasMany(() => UserDeleteDeactivateReason, {
		foreignKey: 'userId',
		sourceKey: 'id',
		as: 'deleteDeactivateReasons',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare deleteDeactivateReasons: UserDeleteDeactivateReason[];

	/**
	 * * Compare password
	 * @param password string
	 * @returns boolean
	 */
	comparePassword(password: string) {
		return bcrypt.compareSync(password, this.password)
	}

	/**
	 * * Generate token
	 * @returns string
	 */
	generateToken(platform: string = 'web', expiresIn: number = 48) {
		const JwtKey = process.env.JWT_SECRET_KEY || 'secret'
		const token = jwt.sign({ id: this.id, type: platform }, JwtKey, {
			expiresIn: expiresIn * 60 * 60, //24 hours
		})
		return token
	}

	/** Get Google Auth */
	getGoogleCalendarTokens() {
		const calendar = this.calendars.find(
			(calendar) => calendar.type === 'google'
		)
		
		if(!calendar) throw new ForbiddenError("Calendar credentials were not configured");
		const { tokens } = calendar.credentials as any;

		if(!tokens) throw new ForbiddenError("Calendar credentials were not configured");

		return tokens;
	}
	
	@BelongsToMany(() => Speciality, {
		foreignKey: 'userId',
		otherKey: 'specialityId',
		through: {
			model: () => UserSpeciality,
		},
	})
	specialities!: Speciality[]

	@BelongsToMany(() => User, {
		foreignKey: 'patientId',
		otherKey: 'therapistId',
		through: {
			model: () => PatientTherapist,
		},
	})
	therapists!: User[]

	@BelongsToMany(() => User, {
		foreignKey: 'therapistId',
		otherKey: 'patientId',
		through: {
			model: () => PatientTherapist,
		},
	})
	patients!: User[]

	@HasMany(() => Calendar, {
		foreignKey: 'userId',
	})
	calendars!: Calendar[]

	@HasMany(() => TherapistBlockedList, {
		foreignKey: 'therapistId',
		as: 'blockedPatients',
		onDelete: 'CASCADE',
	})
	declare blockedPatients: TherapistBlockedList[]

	@HasMany(() => TherapistBlockedList, {
		foreignKey: 'patientId',
		as: 'blockedByTherapists',
		onDelete: 'CASCADE',
	})
	declare blockedByTherapists: TherapistBlockedList[]

	@HasMany(() => UserRegistrationInfo, {
		foreignKey: 'userId',
		as: 'registrationInfo',
		onDelete: 'CASCADE',
	})
	declare registrationInfo: UserRegistrationInfo[]


	@HasMany(() => NormalOfficeHours, {
		foreignKey: 'userId',
		as: 'normalOfficeHours',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare normalOfficeHours: NormalOfficeHours[]

	@HasMany(() => TherapistSubscription, {
		foreignKey: 'therapistId',
		as: 'subscription',
	})
	declare subscription: TherapistSubscription[]

	@HasOne(() => StripeInfo, {
		foreignKey: 'therapistId',
		as: 'stripeInfo',
	})
	declare stripeInfo: StripeInfo

	@HasMany(() => MinorPatient, {
		foreignKey: 'userId',
		as: 'minor_patients',
	})
	declare minor_patient: MinorPatient
}

User.prototype.toJSON = function () {
	const item = this.get()
	if (item.firstname) {
		item.firstname = item.firstname
			.toLowerCase()
			.replace(/\b\w/g, (char:string) => char.toUpperCase())
			.trim(); 
	}
	
	if (item.lastname) {
		item.lastname = item.lastname
			.toLowerCase()
			.replace(/\b\w/g, (char:string) => char.toUpperCase())
			.trim();
	}	
	delete item.password
	delete item.createdAt
	delete item.updatedAt
	delete item.deletedAt
	delete item.passwordResetToken
	delete item.coordinate
	delete item.stripeCustomerId
	delete item.stripeConnectAccountId
	return item
}

export default User