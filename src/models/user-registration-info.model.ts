import { AllowNull, AutoIncrement, BelongsTo, Column, DataType, Index, Model, PrimaryKey, Table } from 'sequelize-typescript';
import User from './user.model';

export interface IUserRegistrationInfo {
	id: number;
	userId: number;
	pageName: string;
	payloadInfo: JSON;
}

@Table({
	tableName: 'user_registration_informations',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'user_registration_information',
		plural: 'user_registration_informations',
	},
})
class UserRegistrationInfo extends Model implements IUserRegistrationInfo {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number;

	@AllowNull(false)
	@Index 
	@Column(DataType.INTEGER)
	declare userId: number;

	@AllowNull(false)
	@Column(DataType.STRING)
	declare pageName: string;

	@AllowNull(false)
	@Column(DataType.JSON)
	declare payloadInfo: JSON;

	@BelongsTo(() => User, {
		foreignKey: 'userId',
		onDelete: 'CASCADE',
	})
	declare user: User;
}

export default UserRegistrationInfo;
