import { AllowNull, Column, DataType, Model, Table } from 'sequelize-typescript'

export interface ISpeciality {
	name: string
	description: string
}

@Table({
	tableName: 'specialities',
	timestamps: true,
	paranoid: false,
	name: {
		singular: 'speciality',
		plural: 'specialities',
	},
})
class Speciality extends Model implements ISpeciality {
	@AllowNull(false)
	@Column(DataType.STRING)
	name!: string

	@AllowNull(true)
	@Column(DataType.TEXT)
	description!: string
}

export default Speciality
