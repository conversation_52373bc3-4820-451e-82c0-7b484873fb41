import {
	Column,
	Foreign<PERSON>ey,
	Model,
	Table,
	DataType,
	AllowNull,
	AutoIncrement,
	PrimaryKey,
	BelongsTo,
	<PERSON><PERSON><PERSON>,
} from 'sequelize-typescript'
import { SubscriptionPlan, TherapistSubscription, User } from '.'

export interface INotifySubscriptionUpdate {
	id: number
	userId: number
	newSubscriptionPlanId: number
	isNotified: boolean
	notifiedAt: Date
	isUpdated: boolean
	updatedAt: Date
	oldSubscriptionPlanId: number
	therapistSubscriptionId: number
	isFailed: boolean
}

@Table({
	tableName: 'notify_subscription_update',
	timestamps: false,
	paranoid: false,
})
class NotifySubscriptionUpdate extends Model implements INotifySubscriptionUpdate {
	@PrimaryKey
	@AutoIncrement
	@Column(DataType.INTEGER)
	declare id: number

	@AllowNull(false)
	@ForeignKey(() => User)
	@Column(DataType.INTEGER)
	declare userId: number

	@AllowNull(false)
	@ForeignKey(() => SubscriptionPlan)
	@Column(DataType.INTEGER)
	declare newSubscriptionPlanId: number

	@AllowNull(false)
	@ForeignKey(() => SubscriptionPlan)
	@Column(DataType.INTEGER)
	declare oldSubscriptionPlanId: number

	@AllowNull(false)
	@ForeignKey(() => TherapistSubscription)
	@Column(DataType.INTEGER)
	declare therapistSubscriptionId: number

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isNotified: boolean

	@AllowNull(true)
	@Column(DataType.DATE)
	declare notifiedAt: Date

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isUpdated: boolean

	@AllowNull(true)
	@Column(DataType.DATE)
	declare updatedAt: Date

	@AllowNull(false)
	@Default(false)
	@Column(DataType.BOOLEAN)
	declare isFailed: boolean

	@BelongsTo(() => User, {
		foreignKey: 'userId',
		as: 'user',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare user: User

	@BelongsTo(() => SubscriptionPlan, {
		foreignKey: 'newSubscriptionPlanId',
		as: 'newSubscriptionPlan',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare newSubscriptionPlan: SubscriptionPlan

	@BelongsTo(() => SubscriptionPlan, {
		foreignKey: 'oldSubscriptionPlanId',
		as: 'oldSubscriptionPlan',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare oldSubscriptionPlan: SubscriptionPlan

	@BelongsTo(() => TherapistSubscription, {
		foreignKey: 'therapistSubscriptionId',
		as: 'therapistSubscription',
		onDelete: 'CASCADE',
		onUpdate: 'CASCADE',
	})
	declare therapistSubscription: TherapistSubscription
}

export default NotifySubscriptionUpdate
