// import dotenv from 'dotenv';
// dotenv.config();

// import request from 'supertest';
// import { expect } from 'chai';
// import SetupApplication from '@/src/app';

// describe.only('App Initialization', () => {
// 	const app = SetupApplication();

// 	it('should return 404 for unknown routes', async () => {
// 		const res = await request(app).get('/some-unknown-route');
// 		expect(res.status).to.equal(404);
// 	});

// 	it('should return 200 for /status route (bodyParser.skip)', async () => {
// 		const res = await request(app).get('/status');
// 		expect(res.status).to.not.equal(500);
// 	});

// 	it('should respond with 200 for /email-hash if email is provided', async () => {
// 		const res = await request(app).get('/email-hash?email=<EMAIL>');
// 		expect(res.status).to.equal(200);
// 	});
// });
