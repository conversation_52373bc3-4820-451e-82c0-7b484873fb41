name: Deploy Therapy

on:
    # Runs on pushes targeting the default branch
    push:
        branches: ['dev']

    # Allows you to run this workflow manually from the Actions tab
    workflow_dispatch:

jobs:
    # Build job
    build:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v3
            - name: Detect package manager
              id: detect-package-manager
              run: |
                  if [ -f "${{ github.workspace }}/yarn.lock" ]; then
                    echo "manager=yarn" >> $GITHUB_OUTPUT
                    echo "command=install" >> $GITHUB_OUTPUT
                    echo "runner=yarn" >> $GITHUB_OUTPUT
                    exit 0
                  elif [ -f "${{ github.workspace }}/package.json" ]; then
                    echo "manager=npm" >> $GITHUB_OUTPUT
                    echo "command=ci" >> $GITHUB_OUTPUT
                    echo "runner=npx --no-install" >> $GITHUB_OUTPUT
                    exit 0
                  else
                    echo "Unable to determine package manager"
                    exit 1
                  fi
            - name: Setup Node
              uses: actions/setup-node@v3
              with:
                  node-version: '16'
                  cache: ${{ steps.detect-package-manager.outputs.manager }}
            - name: Install dependencies
              run: ${{ steps.detect-package-manager.outputs.manager }} ${{ steps.detect-package-manager.outputs.command }}
            - name: Generate .env file
              run: |
                  echo "${{ secrets.DEV_ENV }}" | base64 --decode > .env
            - name: Build
              run: npm run build --no-install
            - name: Copy Build to Server
              uses: appleboy/scp-action@v0.1.4
              with:
                  host: ${{ secrets.SSH_HOST }}
                  username: ${{ secrets.SSH_USERNAME }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  source: 'build,package.json,package-lock.json,database.js,.sequelizerc,database,deployment.config.js,worker.config.js,src/templates,.env'
                  target: /home/<USER>/therapy/backend
                  overwrite: true
            - name: Spin up server
              uses: appleboy/ssh-action@master
              with:
                  host: ${{ secrets.SSH_HOST }}
                  username: ${{ secrets.SSH_USERNAME }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  script: |
                      cd /home/<USER>/therapy/backend
                      npm i
                      npm run migrate
                      cp -r src/templates build/
                      npm run up
