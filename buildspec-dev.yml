version: 0.2
environment_variables:
  plaintext:
    S3_BUCKET: "nexttherapist-dev-artifacts"
    repository_url: "767397971879.dkr.ecr.us-east-2.amazonaws.com/next-therapist-dev" 
    projectKey: "Next-therapist-Dev-BE"
    projectVersion: "Sprint-2"
    projectName: "Next-therapist-Dev-BE"
    
env:
  parameter-store:
    SONAR_TOKEN: "SONAR_TOKEN"
    SONAR_HOST: "SONAR_HOST"    

phases:
  pre_build:
    commands:
      - echo aws --version
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 767397971879.dkr.ecr.us-east-2.amazonaws.com
      - REPOSITORY_URI=767397971879.dkr.ecr.us-east-2.amazonaws.com/next-therapist-dev
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - echo Entered the pre_build phase...
      - aws s3 cp s3://nexttherapist-dev-artifacts/config/development.env .
      - aws s3 cp s3://nexttherapist-dev-artifacts/config/sonar-scanner.properties .
      - ls
      - echo Installing Sonar Scanner...
      - wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-3.2.0.1227-linux.zip
      - unzip sonar-scanner-cli-3.2.0.1227-linux.zip
      - mv sonar-scanner.properties sonar-scanner-3.2.0.1227-linux/conf/sonar-scanner.properties

  build:
    commands:
      - echo "Installing dependencies..."
      - npm install    
      - echo "Running unit tests ..."
      - set -a && . ./development.env && set +a
      - echo "REDIS_URL set to $REDIS_URL"
      - export NODE_OPTIONS="--max-old-space-size=4096"
      - if npm run | grep -q " test"; then npm test -- --coverage; else echo "Skipping tests, no test script found."; fi
      - echo "Building Docker image..."
      - docker build -f Dockerfile -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
      - echo Running SonarQube analysis...
      - ./sonar-scanner-3.2.0.1227-linux/bin/sonar-scanner -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectKey=$projectKey -Dsonar.projectVersion=$projectVersion

  post_build:
    commands:
      - echo SonarQube analysis completed    
      - echo Build completed on date
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...  
      - printf '[{"name":"next-therapist-dev","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json

cache:
  paths:
    - '/root/.cache/docker/**/*'
    - 'node_modules/**/*' 

artifacts:
  files:
    - imagedefinitions.json
    - "sonar-scanner-3.2.0.1227-linux/**/*"  
  discard-paths: yes
  name: ${CODEBUILD_BUILD_NUMBER}
