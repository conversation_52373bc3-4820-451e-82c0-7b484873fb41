import { describe, it, expect, beforeEach, vi } from "vitest";
import CalendarRepository from "../../src/repositories/CalendarRepository";
import { HttpStatusCode } from "axios";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";

// ✅ Mock useApiClient
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

// ✅ Mock notification util
vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));


describe("CalendarRepository", () => {

    const post = vi.fn();
    const get = vi.fn();
    const put = vi.fn();
    const del = vi.fn();
  let repo: CalendarRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ post ,get,put,delete: del});
    vi.clearAllMocks();
    repo = new CalendarRepository();
  });


  it("create - success", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { user: { name: "Test" } },
    });
    const result = await repo.create({});
     expect(result).toEqual({ name: "Test" });
    expect(notification.success).toHaveBeenCalledWith("Calendar synced successfully");
   
  });

  it("create - error", async () => {
    post.mockRejectedValue({ message: "Create Error" });
    const result = await repo.create({});
    expect(notification.error).toHaveBeenCalledWith("Create Error");
    expect(result).toBeUndefined();
  });

  it("syncGoogleCalendar - success", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Created, data: {} });
    const result = await repo.syncGoogleCalendar({});
    expect(notification.success).toHaveBeenCalledWith("Google Calendar synced successfully");
    expect(result).toBeDefined();
  });

  it("syncGoogleCalendar - error", async () => {
    post.mockRejectedValue({ message: "Google sync error" });
    const result = await repo.syncGoogleCalendar({});
    expect(notification.error).toHaveBeenCalledWith("Google sync error");
    expect(result).toBeUndefined();
  });


  it("syncOutlookCalendar - success", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { user: { name: "OutlookUser" } },
    });
    const result = await repo.syncOutlookCalendar({});
    expect(notification.success).toHaveBeenCalledWith("Outlook Calendar synced successfully");
    expect(result).toEqual({ name: "OutlookUser" });
  });

  it("syncOutlookCalendar - error", async () => {
    post.mockRejectedValue({ message: "Outlook Error" });
    const result = await repo.syncOutlookCalendar({});
    expect(notification.error).toHaveBeenCalledWith("Outlook Error");
    expect(result).toBeUndefined();
  });



  it("test - success", async () => {
    post.mockResolvedValue({ status: 200, data: { id: 123 } });
    const result = await repo.test({});
    expect(notification.success).toHaveBeenCalledWith("Calendar test successful");
    expect(result).toEqual({ id: 123 });
  });

  it("test - error", async () => {
    post.mockRejectedValue({ message: "Test Error" });
    const result = await repo.test({});
    expect(notification.error).toHaveBeenCalledWith("Test Error");
    expect(result).toBeUndefined();
  });

  it("testMicrosoft - success", async () => {
    post.mockResolvedValue({ status: 200, data: { id: "ms-test" } });
    const result = await repo.testMicrosoft({});
    expect(notification.success).toHaveBeenCalledWith("Microsoft Calendar test successful");
    expect(result).toEqual({ id: "ms-test" });
  });

  it("testMicrosoft - error", async () => {
    post.mockRejectedValue({ message: "MS Test Error" });
    const result = await repo.testMicrosoft({});
    expect(notification.error).toHaveBeenCalledWith("MS Test Error");
    expect(result).toBeUndefined();
  });


  it("deleteGoogleCalendar - success", async () => {
    del.mockResolvedValue({ status: HttpStatusCode.Ok, data: {} });
    const result = await repo.deleteGoogleCalendar();
    expect(notification.success).toHaveBeenCalledWith("Google Calendar removed successfully");
    expect(result).toBeDefined();
  });

  it("deleteGoogleCalendar - error", async () => {
    del.mockRejectedValue({ message: "Delete Error" });
    const result = await repo.deleteGoogleCalendar();
    expect(notification.error).toHaveBeenCalledWith("Delete Error");
    expect(result).toBeUndefined();
  });

  it("update - success", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { message: "Updated!", event: { id: 1 } },
    });
    const result = await repo.update(1, {});
    expect(notification.success).toHaveBeenCalledWith("Updated!");
    expect(result).toEqual({ id: 1 });
  });

  it("update - error", async () => {
    put.mockRejectedValue({ message: "Update Error" });
    const result = await repo.update(1, {});
    expect(notification.error).toHaveBeenCalledWith("Update Error");
    expect(result).toBeUndefined();
  });

  it("getGoogleCalendarEvents - success", async () => {
    get.mockResolvedValue({ status: 200, data: ["event1"] });
    const result = await repo.getGoogleCalendarEvents();
    expect(notification.success).toHaveBeenCalledWith("Google Calendar Events fetched successfully");
    expect(result).toEqual(["event1"]);
  });

  it("getGoogleCalendarEvents - error", async () => {
    get.mockRejectedValue({ message: "GCal Events Error" });
    const result = await repo.getGoogleCalendarEvents();
    expect(notification.error).toHaveBeenCalledWith("GCal Events Error");
    expect(result).toBeUndefined();
  });

  it("getOutlookCalendarEvents - success", async () => {
    get.mockResolvedValue({ status: 200, data: ["event2"] });
    const result = await repo.getOutlookCalendarEvents();
    expect(notification.success).toHaveBeenCalledWith("Outlook Calendar Events fetched successfully");
    expect(result).toEqual(["event2"]);
  });

  it("getOutlookCalendarEvents - error", async () => {
    get.mockRejectedValue({ message: "Outlook Events Error" });
    const result = await repo.getOutlookCalendarEvents();
    expect(notification.error).toHaveBeenCalledWith("Outlook Events Error");
    expect(result).toBeUndefined();
  });
});
