import { describe, it, expect, vi, beforeEach } from "vitest";
import TherapistPagesRepository from "../../src/repositories/TherapistPagesRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";

// Mocks
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("TherapistPagesRepository", () => {
  const get = vi.fn();
  let repo: TherapistPagesRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get });
    vi.clearAllMocks();
    repo = new TherapistPagesRepository();
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["Page1", "Page2"] });
    const result = await repo.getAll({ search: "abc" });
    expect(result).toEqual(["Page1", "Page2"]);
    expect(get).toHaveBeenCalledWith("/therapist-pages", { params: { search: "abc" } });
  });

  it("getAll - error with message", async () => {
    get.mockRejectedValue({ response: { data: { message: "Fetch error" } } });
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Fetch error");
  });

  it("getAll - error fallback", async () => {
    get.mockRejectedValue({});
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });
});
