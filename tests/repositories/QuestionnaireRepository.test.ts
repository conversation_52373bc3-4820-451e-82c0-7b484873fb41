import { describe, it, expect, vi, beforeEach } from "vitest";
import QuestionnaireRepository from "../../src/repositories/QuestionnaireRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

// Mocks
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("QuestionnaireRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  let repo: QuestionnaireRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put });
    vi.clearAllMocks();
    repo = new QuestionnaireRepository();
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["q1", "q2"] });
    const result = await repo.getAll();
    expect(result).toEqual(["q1", "q2"]);
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ message: "Get error" });
    const result = await repo.getAll();
    expect(notification.error).toHaveBeenCalledWith("Get error");
    expect(result).toBeUndefined();
  });

  it("getOne - success", async () => {
    get.mockResolvedValue({ data: { questionnaire: { id: 1 } } });
    const result = await repo.getOne(1);
    expect(result).toEqual({ id: 1 });
  });

  it("getOne - error", async () => {
    get.mockRejectedValue({ message: "Fetch failed" });
    const result = await repo.getOne(1);
    expect(notification.error).toHaveBeenCalledWith("Fetch failed");
    expect(result).toBeUndefined();
  });

  it("create - success", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { questionnaire: { id: 2 } },
    });
    const result = await repo.create({ name: "New" });
    expect(notification.success).toHaveBeenCalledWith("Question added successfully");
    expect(result).toEqual({ id: 2 });
  });

  it("create - failure status", async () => {
    post.mockResolvedValue({ status: 400 });
    const result = await repo.create({ name: "Bad" });
    expect(result).toBeUndefined();
  });

  it("create - error", async () => {
    post.mockRejectedValue({ message: "Create error" });
    const result = await repo.create({ name: "Fail" });
    expect(notification.error).toHaveBeenCalledWith("Create error");
    expect(result).toBeUndefined();
  });

  it("update - success", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { message: "Updated", questionnaire: { id: 3 } },
    });
    const result = await repo.update(3, { name: "Updated" });
    expect(notification.success).toHaveBeenCalledWith("Updated");
    expect(result).toEqual({ id: 3 });
  });

  it("update - no message", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { questionnaire: { id: 4 } },
    });
    const result = await repo.update(4, {});
    expect(notification.success).toHaveBeenCalledWith("Question updated successfully");
    expect(result).toEqual({ id: 4 });
  });

  it("update - failure status", async () => {
    put.mockResolvedValue({ status: 400 });
    const result = await repo.update(5, {});
    expect(result).toBeUndefined();
  });

  it("update - error", async () => {
    put.mockRejectedValue({ message: "Update failed" });
    const result = await repo.update(6, {});
    expect(notification.error).toHaveBeenCalledWith("Update failed");
    expect(result).toBeUndefined();
  });

  it("sortOrder - success", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.sortOrder({ order: [1, 2, 3] });
    expect(notification.success).toHaveBeenCalledWith("Questionnaire sorted successfully");
    expect(result).toBe(true);
  });

  it("sortOrder - failure status", async () => {
    post.mockResolvedValue({ status: 400 });
    const result = await repo.sortOrder({});
    expect(result).toBe(false);
  });

  it("sortOrder - error", async () => {
    post.mockRejectedValue({ message: "Sort error" });
    const result = await repo.sortOrder({});
    expect(notification.error).toHaveBeenCalledWith("Sort error");
    expect(result).toBe(false);
  });
});
