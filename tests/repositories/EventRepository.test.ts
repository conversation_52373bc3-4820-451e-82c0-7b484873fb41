import { describe, it, expect, vi, beforeEach } from "vitest";
import EventRepository from "../../src/repositories/EventRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

// ✅ Mock dependencies
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("EventRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  const del = vi.fn();

  let repo: EventRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put, delete: del });
    vi.clearAllMocks();
    repo = new EventRepository();
  });

  // --- getAll ---
  it("getAll - success", async () => {
    get.mockResolvedValue({ data: [{ id: 1 }] });
    const result = await repo.getAll({ page: 1 });
    expect(result).toEqual([{ id: 1 }]);
  });

  it("getAll - error with message", async () => {
    get.mockRejectedValue({ response: { data: { message: "Fetch failed" } } });
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Fetch failed");
  });

  it("getAll - generic error fallback", async () => {
    get.mockRejectedValue({});
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  // --- create ---
  it("create - success (201 Created)", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { event: { id: 2 } },
    });
    const result = await repo.create({ name: "Test Event" });
    expect(result).toEqual({ id: 2 });
    expect(notification.success).toHaveBeenCalledWith("Event added successfully");
  });

  it("create - error with message", async () => {
    post.mockRejectedValue({ response: { data: { message: "Create failed" } } });
    const result = await repo.create({});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Create failed");
  });

  it("create - generic error fallback", async () => {
    post.mockRejectedValue({});
    const result = await repo.create({});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  // --- update ---
  it("update - success (200 OK)", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Ok,
      data: { message: "Updated!", event: { id: 3 } },
    });
    const result = await repo.update(3, { name: "Updated Event" });
    expect(result).toEqual({ id: 3 });
    expect(notification.success).toHaveBeenCalledWith("Updated!");
  });

  it("update - success with fallback message", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Ok,
      data: { event: { id: 4 } },
    });
    const result = await repo.update(4, {});
    expect(result).toEqual({ id: 4 });
    expect(notification.success).toHaveBeenCalledWith("Event updated successfully");
  });

  it("update - error with message", async () => {
    put.mockRejectedValue({ response: { data: { message: "Update failed" } } });
    const result = await repo.update(4, {});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Update failed");
  });

  it("update - generic error fallback", async () => {
    put.mockRejectedValue({});
    const result = await repo.update(4, {});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  // --- destroy ---
  it("destroy - success", async () => {
    del.mockResolvedValue({ status: HttpStatusCode.Ok });
    const result = await repo.destroy(5);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Event removed successfully");
  });

  it("destroy - error with message", async () => {
    del.mockRejectedValue({ response: { data: { message: "Delete failed" } } });
    const result = await repo.destroy(5);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Delete failed");
  });

  it("destroy - generic error fallback", async () => {
    del.mockRejectedValue({});
    const result = await repo.destroy(5);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });
});
