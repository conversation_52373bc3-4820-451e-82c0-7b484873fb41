import { describe, it, expect, vi, beforeEach } from "vitest";
import TherapistRepository from "../../src/repositories/TherapistRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("TherapistRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  const patch = vi.fn();

  let repo: TherapistRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put, patch });
    vi.clearAllMocks();
    repo = new TherapistRepository();
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["t1"] });
    const result = await repo.getAll();
    expect(result).toEqual(["t1"]);
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ message: "fail" });
    const result = await repo.getAll();
    expect(notification.error).toHaveBeenCalledWith("fail");
    expect(result).toBeUndefined();
  });

  it("getDeactivateReasons - success", async () => {
    get.mockResolvedValue({ data: { reasons: ["Reason A"] } });
    const result = await repo.getDeactivateReasons();
    expect(result).toEqual({ reasons: ["Reason A"] });
  });

  it("getDeactivateReasons - error", async () => {
    get.mockRejectedValue({ message: "fail" });
    const result = await repo.getDeactivateReasons();
    expect(notification.error).toHaveBeenCalledWith("fail");
    expect(result).toEqual({ reasons: [] });
  });

  it("create - success", async () => {
    const form = new FormData();
    post.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.create(form);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalled();
  });

  it("create - error", async () => {
    const form = new FormData();
    post.mockRejectedValue({ message: "error" });
    const result = await repo.create(form);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("update - success", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Ok, data: { message: "Updated" } });
    const result = await repo.update(1, {});
    expect(result.status).toBe(HttpStatusCode.Ok);
    expect(notification.success).toHaveBeenCalledWith("Updated");
  });

  it("update - error", async () => {
    put.mockRejectedValue({ message: "error" });
    const result = await repo.update(1, {});
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("reject - success", async () => {
    patch.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.reject(1, "reason");
    expect(result).toBe(true);
  });

  it("reject - error", async () => {
    patch.mockRejectedValue({ message: "error" });
    const result = await repo.reject(1, "reason");
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("getOne - success", async () => {
    get.mockResolvedValue({ data: { id: 1, name: "Therapist" } });
    const result = await repo.getOne(1);
    expect(result).toEqual({ id: 1, name: "Therapist" });
  });

  it("getOne - error", async () => {
    get.mockRejectedValue({ message: "error" });
    const result = await repo.getOne(1);
    expect(result).toBeNull();
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("durationCost - success", async () => {
    post.mockResolvedValue({ data: { cost: 100 } });
    const result = await repo.durationCost({});
    expect(result).toEqual({ data: { cost: 100 } });
  });

  it("durationCost - error", async () => {
    post.mockRejectedValue({ message: "error" });
    const result = await repo.durationCost({});
    expect(result).toBeNull();
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("getdurationCost - success", async () => {
    get.mockResolvedValue({ data: { details: "ok" } });
    const result = await repo.getdurationCost();
    expect(result).toEqual({ data: { details: "ok" } });
  });

  it("getdurationCost - error", async () => {
    get.mockRejectedValue({ message: "error" });
    const result = await repo.getdurationCost();
    expect(result).toBeNull();
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("getProfileRejectionReasons - success", async () => {
    get.mockResolvedValue({ data: ["Reason"] });
    const result = await repo.getProfileRejectionReasons(1);
    expect(result).toEqual({ data: ["Reason"] });
  });

  it("getProfileRejectionReasons - error", async () => {
    get.mockRejectedValue({ message: "error" });
    const result = await repo.getProfileRejectionReasons(1);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("error");
  });

  it("accept - success", async () => {
    patch.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.accept(1);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalled();
  });

  it("rejectWithReasonId - valid reason", async () => {
    const reasons = { reasons: [{ subheadings: [{ id: 1, subheading: "Reason A" }] }] };
    get.mockResolvedValueOnce({ data: reasons });
    patch.mockResolvedValueOnce({ status: HttpStatusCode.Created });
    const result = await repo.rejectWithReasonId(10, 1);
    expect(result.status).toBe(HttpStatusCode.Created);
  });

  it("rejectWithReasonId - reason not found", async () => {
    get.mockResolvedValueOnce({ data: { reasons: [] } });
    const result = await repo.rejectWithReasonId(10, 99);
    expect(result.status).toBe(HttpStatusCode.BadRequest);
    expect(notification.error).toHaveBeenCalledWith("Invalid reason selected");
  });

  it("restore - success", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Ok });
    const result = await repo.restore(1);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalled();
  });

  it("restore - error", async () => {
    put.mockRejectedValue({});
    const result = await repo.restore(1);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalled();
  });

    it("getViewHistoryReasons - success", async () => {
    get.mockResolvedValueOnce({ data: ["reason1"] });
    const result = await repo.getViewHistoryReasons(123);
    expect(result).toEqual({ data: ["reason1"] });
  });

  it("getViewHistoryReasons - error", async () => {
    get.mockRejectedValueOnce({ message: "fetch error" });
    const result = await repo.getViewHistoryReasons(123);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("fetch error");
  });
});