import { describe, it, expect, vi, beforeEach } from "vitest";
import MatchingAlgoRepository from "../../src/repositories/MatchingAlgoRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

// ✅ Mock dependencies
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("MatchingAlgoRepository", () => {
  const post = vi.fn();
  const get = vi.fn();
  let repo: MatchingAlgoRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ post, get });
    vi.clearAllMocks();
    repo = new MatchingAlgoRepository();
  });

  // --- create ---
  it("create - success (201 Created)", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.create({ key: "value" });
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Matching algo data added successfully");
  });

  it("create - API error with custom message", async () => {
    post.mockRejectedValue({ response: { data: { message: "Create failed" } } });
    const result = await repo.create({});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Create failed");
  });

  it("create - fallback error", async () => {
    post.mockRejectedValue({});
    const result = await repo.create({});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  // --- getAll ---
  it("getAll - success", async () => {
    get.mockResolvedValue({ data: [{ id: 1 }] });
    const result = await repo.getAll({ page: 1 });
    expect(result).toEqual([{ id: 1 }]);
  });

  it("getAll - API error with message", async () => {
    get.mockRejectedValue({ response: { data: { message: "Fetch error" } } });
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Fetch error");
  });

  it("getAll - fallback error", async () => {
    get.mockRejectedValue({});
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });
});
