import { describe, it, expect, vi, beforeEach } from "vitest";
import PageRepository from "../../src/repositories/PageRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

// Mock dependencies
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("PageRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  let repo: PageRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put });
    vi.clearAllMocks();
    repo = new PageRepository();
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["page1"] });
    const result = await repo.getAll({ page: 1 });
    expect(result).toEqual(["page1"]);
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ response: { data: { message: "Failed to load" } } });
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Failed to load");
  });

  it("getOne - success", async () => {
    get.mockResolvedValue({ data: { page: { id: 1 } } });
    const result = await repo.getOne(1);
    expect(result).toEqual({ id: 1 });
  });

  it("getOne - error", async () => {
    get.mockRejectedValue({ message: "Page not found" });
    const result = await repo.getOne(1);
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Page not found");
  });

  it("create - success", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.create({ title: "Page" });
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Page added successfully");
  });

  it("create - error", async () => {
    post.mockRejectedValue({});
    const result = await repo.create({});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  it("update - success", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Created, data: { message: "Updated" } });
    const result = await repo.update(1, { title: "Updated Title" });
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Updated");
  });

  it("update - error", async () => {
    put.mockRejectedValue({ message: "Update failed" });
    const result = await repo.update(1, {});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Update failed");
  });

  it("startPage - success", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Ok, data: { message: "Started" } });
    const result = await repo.startPage(5);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Started");
  });

  it("startPage - error", async () => {
    put.mockRejectedValue({});
    const result = await repo.startPage(5);
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  it("matcherInvolvement - success", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Ok, data: { message: "Matcher updated" } });
    const result = await repo.matcherInvolvement(9);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Matcher updated");
  });

  it("matcherInvolvement - error", async () => {
    put.mockRejectedValue({});
    const result = await repo.matcherInvolvement(9);
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  it("updateOrder - success", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Ok });
    const result = await repo.updateOrder([{ id: 1, order: 2 }]);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Page order updated successfully");
  });

  it("updateOrder - error", async () => {
    post.mockRejectedValue({ response: { data: { message: "Sort error" } } });
    const result = await repo.updateOrder([]);
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Sort error");
  });
});
