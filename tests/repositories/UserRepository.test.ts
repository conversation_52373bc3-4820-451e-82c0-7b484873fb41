import { describe, it, expect, vi, beforeEach } from "vitest";
import UserRepository from "../../src/repositories/UserRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";
import { RESTORE_SUCCESS_MESSAGE, RESTORE_ERROR_MESSAGE } from "../../src/pages/auth/constants";

// Mocks
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("UserRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  const patch = vi.fn();
  let repo: UserRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put, patch });
    vi.clearAllMocks();
    repo = new UserRepository();
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["user1"] });
    const result = await repo.getAll();
    expect(result).toEqual(["user1"]);
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ response: { data: { message: "Error fetching users" } } });
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Error fetching users");
  });

  it("create - success", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { user: { name: "User1" } },
    });
    const result = await repo.create({ name: "User1" });
    expect(result).toEqual({ name: "User1" });
    expect(notification.success).toHaveBeenCalledWith("User created successfully");
  });

  it("create - error", async () => {
    post.mockRejectedValue({});
    const result = await repo.create({});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  it("update - success", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { user: { name: "UpdatedUser" }, message: "Updated" },
    });
    const result = await repo.update(1, {});
    expect(result).toEqual({ name: "UpdatedUser" });
    expect(notification.success).toHaveBeenCalledWith("Updated");
  });

  it("update - error", async () => {
    put.mockRejectedValue({});
    const result = await repo.update(1, {});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  it("activate - success", async () => {
    patch.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.activate("/activate/1");
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("User Activation successful");
  });

  it("activate - error", async () => {
    patch.mockRejectedValue({ message: "Activation error" });
    const result = await repo.activate("/activate/1");
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Activation error");
  });

  it("deactivate - success", async () => {
    patch.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.deactivate("/deactivate/1");
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("User deactivation successful");
  });

  it("deactivate - error", async () => {
    patch.mockRejectedValue({});
    const result = await repo.deactivate("/deactivate/1");
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Deactivation failed");
  });

  it("resetPassword - success", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.resetPassword("/reset", "<EMAIL>");
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Password reset link sent successfully");
  });

  it("resetPassword - error", async () => {
    post.mockRejectedValue({});
    const result = await repo.resetPassword("/reset", "<EMAIL>");
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Password reset failed");
  });

  it("restore - success (201 Created)", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.restore(123);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith(RESTORE_SUCCESS_MESSAGE);
  });

  it("restore - success (200 OK)", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Ok });
    const result = await repo.restore(123);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith(RESTORE_SUCCESS_MESSAGE);
  });

  it("restore - error with message", async () => {
    put.mockRejectedValue({ response: { data: { message: "Restore failed" } } });
    const result = await repo.restore(123);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Restore failed");
  });

  it("restore - error fallback", async () => {
    put.mockRejectedValue({});
    const result = await repo.restore(123);
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith(RESTORE_ERROR_MESSAGE);
  });
});
