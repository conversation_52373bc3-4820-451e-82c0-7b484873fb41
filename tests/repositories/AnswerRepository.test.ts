import { describe, it, vi, expect, beforeEach } from "vitest";
import AnswerRepository from "../../src/repositories/AnswerRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));
vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("AnswerRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  let repo: AnswerRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put });
    vi.clearAllMocks();
    repo = new AnswerRepository(); // ⬅️ Move this here!
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["answer1", "answer2"] });
    const result = await repo.getAll({ page: 1 });
    expect(result).toEqual(["answer1", "answer2"]);
    expect(get).toHaveBeenCalledWith("/answers", { params: { page: 1 } });
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ response: { data: { message: "Fail" } } });
    const result = await repo.getAll();
    expect(notification.error).toHaveBeenCalledWith("Fail");
    expect(result).toBeUndefined();
  });

  it("getOne - success", async () => {
    get.mockResolvedValue({ data: { answer: "oneAnswer" } });
    const result = await repo.getOne(1);
    expect(result).toBe("oneAnswer");
  });

  it("getOne - error", async () => {
    get.mockRejectedValue({ message: "Not Found" });
    const result = await repo.getOne(1);
    expect(notification.error).toHaveBeenCalledWith("Not Found");
    expect(result).toBeUndefined();
  });

  it("create - success", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { answer: "createdAnswer" },
    });
    const result = await repo.create({ title: "New" });
    expect(notification.success).toHaveBeenCalledWith("Answer added successfully");
    expect(result).toBe("createdAnswer");
  });

  it("create - error", async () => {
    post.mockRejectedValue({ message: "Create Error" });
    const result = await repo.create({ title: "New" });
    expect(notification.error).toHaveBeenCalledWith("Create Error");
    expect(result).toBeUndefined();
  });

  it("update - success", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { answer: "updated", message: "Updated" },
    });
    const result = await repo.update(2, { title: "Edit" });
    expect(notification.success).toHaveBeenCalledWith("Updated");
    expect(result).toBe("updated");
  });

  it("update - error", async () => {
    put.mockRejectedValue({ message: "Update Error" });
    const result = await repo.update(2, { title: "Edit" });
    expect(notification.error).toHaveBeenCalledWith("Update Error");
    expect(result).toBeUndefined();
  });

  it("sortOrder - success", async () => {
    post.mockResolvedValue({ status: HttpStatusCode.Created });
    const result = await repo.sortOrder([{ id: 1 }]);
    expect(notification.success).toHaveBeenCalledWith("Answer sorted successfully");
    expect(result).toBe(true);
  });

  it("sortOrder - error", async () => {
    post.mockRejectedValue({ message: "Sort Error" });
    const result = await repo.sortOrder([]);
    expect(notification.error).toHaveBeenCalledWith("Sort Error");
    expect(result).toBe(false);
  });
});

