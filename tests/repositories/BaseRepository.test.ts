import { describe, it, vi, expect, beforeEach } from "vitest";
import BaseRepository from "../../src/repositories/BaseRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";


vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));
vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("BaseRepository", () => {
  const mockDelete = vi.fn();
  const mockPatch = vi.fn();
  const mockPost = vi.fn();

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({
      delete: mockDelete,
      patch: mockPatch,
      post: mockPost,
    });
  });

  it("should call successToast", () => {
    const repo = new BaseRepository();
    repo.successToast("Success!");
    expect(notification.success).toHaveBeenCalledWith("Success!");
  });

  it("should call errorToast", () => {
    const repo = new BaseRepository();
    repo.errorToast("Error!");
    expect(notification.error).toHaveBeenCalledWith("Error!");
  });

  it("should call delete", async () => {
    const repo = new BaseRepository();
    await repo.delete("/test");
    expect(mockDelete).toHaveBeenCalledWith("/test");
  });

  it("should call patch", async () => {
    const repo = new BaseRepository();
    await repo.patch("/test", { foo: "bar" });
    expect(mockPatch).toHaveBeenCalledWith("/test", { foo: "bar" });
  });

  it("should call post", async () => {
    const repo = new BaseRepository();
    await repo.post("/test", { foo: "bar" });
    expect(mockPost).toHaveBeenCalledWith("/test", { foo: "bar" });
  });
});
