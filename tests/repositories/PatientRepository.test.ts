import { describe, it, expect, beforeEach, vi } from "vitest";
import PatientRepository from "../../src/repositories/PatientRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";
import { RESTORE_SUCCESS_MESSAGE, RESTORE_ERROR_MESSAGE } from "../../src/pages/auth/constants";

// Mock dependencies
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("PatientRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  let repo: PatientRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put });
    vi.clearAllMocks();
    repo = new PatientRepository();
  });

  it("getDeleteDeactivateReasons - success", async () => {
    get.mockResolvedValue({ data: { reasons: ["reason1"] } });
    const result = await repo.getDeleteDeactivateReasons();
    expect(result).toEqual({ reasons: ["reason1"] });
  });

  it("getDeleteDeactivateReasons - error", async () => {
    get.mockRejectedValue({ message: "Fail" });
    const result = await repo.getDeleteDeactivateReasons();
    expect(notification.error).toHaveBeenCalledWith("Fail");
    expect(result).toEqual({ reasons: [] });
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: [{ name: "Patient A" }] });
    const result = await repo.getAll({ search: "A" });
    expect(result).toEqual([{ name: "Patient A" }]);
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ message: "Get Error" });
    const result = await repo.getAll();
    expect(notification.error).toHaveBeenCalledWith("Get Error");
    expect(result).toBeUndefined();
  });

  it("create - success", async () => {
    const formData = new FormData();
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: {},
    });
    const result = await repo.create(formData);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("User created successfully");
  });

  it("create - failure (status not Created)", async () => {
    const formData = new FormData();
    post.mockResolvedValue({ status: 400 });
    const result = await repo.create(formData);
    expect(result).toBe(false);
  });

  it("create - error", async () => {
    const formData = new FormData();
    post.mockRejectedValue({ message: "Create Error" });
    const result = await repo.create(formData);
    expect(notification.error).toHaveBeenCalledWith("Create Error");
    expect(result).toBe(false);
  });

  it("update - success", async () => {
    const formData = new FormData();
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { message: "User updated" },
    });
    const result = await repo.update(1, formData);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("User updated");
  });

  it("update - failure (status not Created)", async () => {
    const formData = new FormData();
    put.mockResolvedValue({ status: 400 });
    const result = await repo.update(1, formData);
    expect(result).toBe(false);
  });

  it("update - error", async () => {
    const formData = new FormData();
    put.mockRejectedValue({ message: "Update Error" });
    const result = await repo.update(1, formData);
    expect(notification.error).toHaveBeenCalledWith("Update Error");
    expect(result).toBe(false);
  });

  it("restore - success", async () => {
    put.mockResolvedValue({ status: HttpStatusCode.Ok });
    const result = await repo.restore(1);
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith(RESTORE_SUCCESS_MESSAGE);
  });

  it("restore - failure (status not Ok)", async () => {
    put.mockResolvedValue({ status: 400 });
    const result = await repo.restore(1);
    expect(result).toBe(false);
  });

  it("restore - error", async () => {
    put.mockRejectedValue({ message: "Restore Failed" });
    const result = await repo.restore(1);
    expect(notification.error).toHaveBeenCalledWith("Restore Failed");
    expect(result).toBe(false);
  });

  it("restore - fallback message", async () => {
    put.mockRejectedValue({});
    const result = await repo.restore(1);
    expect(notification.error).toHaveBeenCalledWith(RESTORE_ERROR_MESSAGE);
    expect(result).toBe(false);
  });
});
