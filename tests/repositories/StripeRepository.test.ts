import { describe, it, expect, vi, beforeEach } from "vitest";
import StripeRepository from "../../src/repositories/StripeRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";

// Mocks
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("StripeRepository", () => {
  const post = vi.fn();
  let repo: StripeRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ post });
    vi.clearAllMocks();
    repo = new StripeRepository();
  });

  it("connect - success", async () => {
    const responseData = { subscriptionId: "sub_123" };
    post.mockResolvedValue({ data: responseData });

    const result = await repo.connect();

    expect(post).toHaveBeenCalledWith("/stripe/subscribe", {
      data: { a: 10 },
    });
    expect(result).toEqual(responseData);
  });

  it("connect - error", async () => {
    const errorMessage = "Stripe error";
    post.mockRejectedValue({
      response: { data: { message: errorMessage } },
    });

    const result = await repo.connect();

    expect(notification.error).toHaveBeenCalledWith(errorMessage);
    expect(result).toBeUndefined();
  });

  it("connect - generic fallback error", async () => {
    post.mockRejectedValue({});

    const result = await repo.connect();

    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
    expect(result).toBeUndefined();
  });
});
