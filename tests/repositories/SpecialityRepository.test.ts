import { describe, it, expect, vi, beforeEach } from "vitest";
import SpecialityRepository from "../../src/repositories/SpecialityRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

// Mocks
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("SpecialityRepository", () => {
  const get = vi.fn();
  const post = vi.fn();
  const put = vi.fn();
  let repo: SpecialityRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ get, post, put });
    vi.clearAllMocks();
    repo = new SpecialityRepository();
  });

  it("getAll - success", async () => {
    get.mockResolvedValue({ data: ["Speciality1", "Speciality2"] });
    const result = await repo.getAll();
    expect(result).toEqual(["Speciality1", "Speciality2"]);
  });

  it("getAll - error", async () => {
    get.mockRejectedValue({ message: "Fetch error" });
    const result = await repo.getAll();
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Fetch error");
  });

  it("create - success", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { speciality: { id: 1, name: "Test" } },
    });
    const result = await repo.create({ name: "Test" });
    expect(result).toEqual({ id: 1, name: "Test" });
    expect(notification.success).toHaveBeenCalledWith("Speciality added successfully");
  });

  it("create - error with response message", async () => {
    post.mockRejectedValue({ response: { data: { message: "Create failed" } } });
    const result = await repo.create({ name: "Error" });
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Create failed");
  });

  it("create - error with fallback", async () => {
    post.mockRejectedValue({});
    const result = await repo.create({ name: "Error" });
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });

  it("update - success", async () => {
    put.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: {
        message: "Updated!",
        speciality: { id: 2, name: "Updated Name" },
      },
    });
    const result = await repo.update(2, { name: "Updated Name" });
    expect(result).toEqual({ id: 2, name: "Updated Name" });
    expect(notification.success).toHaveBeenCalledWith("Updated!");
  });

  it("update - error with message", async () => {
    put.mockRejectedValue({ response: { data: { message: "Update failed" } } });
    const result = await repo.update(2, {});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Update failed");
  });

  it("update - error with fallback", async () => {
    put.mockRejectedValue({});
    const result = await repo.update(2, {});
    expect(result).toBeUndefined();
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });
});
