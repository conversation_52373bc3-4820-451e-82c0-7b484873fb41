import { describe, it, expect, vi, beforeEach } from "vitest";
import AppointmentRepository from "../../src/repositories/AppointmentRepository";
import { useApiClient } from "../../src/utils/api.util";
import notification from "../../src/utils/notification.util";
import { HttpStatusCode } from "axios";

// Mock dependencies
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("AppointmentRepository", () => {
  const post = vi.fn();
  let repo: AppointmentRepository;

  beforeEach(() => {
    (useApiClient as any).mockReturnValue({ post });
    vi.clearAllMocks();
    repo = new AppointmentRepository();
  });

  it("makeAppointment - success (201 Created)", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.Created,
      data: { message: "Appointment booked" },
    });

    const result = await repo.makeAppointment();
    expect(result).toBe(true);
    expect(notification.success).toHaveBeenCalledWith("Appointment booked");
  });

  it("makeAppointment - bad request (400)", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.BadRequest,
      data: { message: "Invalid data" },
    });

    const result = await repo.makeAppointment();
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Invalid data");
  });

  it("makeAppointment - unexpected status (e.g., 500)", async () => {
    post.mockResolvedValue({
      status: HttpStatusCode.InternalServerError,
      data: { message: "Unexpected error" },
    });

    const result = await repo.makeAppointment();
    expect(result).toBe(false);
    expect(notification.success).not.toHaveBeenCalled();
    expect(notification.error).not.toHaveBeenCalled();
  });

  it("makeAppointment - network or API error", async () => {
    post.mockRejectedValue({
      response: { data: { message: "Server crashed" } },
    });

    const result = await repo.makeAppointment();
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Server crashed");
  });

  it("makeAppointment - generic error fallback", async () => {
    post.mockRejectedValue({});

    const result = await repo.makeAppointment();
    expect(result).toBe(false);
    expect(notification.error).toHaveBeenCalledWith("Something went wrong");
  });
});
