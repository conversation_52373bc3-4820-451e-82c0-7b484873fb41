import {
  getFullName,
  dateToDateTime,
  getInitials,
  jsonToFormData,
  classNames,
  formatValue,
  isEmail,
  isPhone,
  generateSlug,
  sanitizeInputValue,
  getNextInvalidPage,
  generateRegFormContent,
  validateTherapistRegistration,
  validatePrice,
  formatDateMDY,
  generateURLEncodedString,
  createCodeChallenge,
  base64URLEncode,
  sha256
} from '../../src/utils/app.util';
import { THERAPIST_REGISTRATION_PAGES } from "../../src/configs/therapist-registration.configs";
import { describe, it, expect, beforeAll, vi } from 'vitest';

describe('Utility Functions', () => {
  it('getFullName returns full name', () => {
    expect(getFullName('John', 'Doe')).toBe('<PERSON>');
  });

  it('dateToDateTime returns formatted date and time', () => {
    const result = dateToDateTime(new Date('2023-01-01T12:30:00Z'));
    expect(result).toEqual({
      date: 'Jan 1, 2023',
      time: '12:30 PM',
    });
  });

  it('getInitials returns correct initials', () => {
    expect(getInitials('John Doe')).toBe('JD');
    expect(getInitials('Alice')).toBe('A');
  });

  it('jsonToFormData converts object to FormData', () => {
    const data = { name: 'test', age: '30' };
    const formData = jsonToFormData(data);
    expect(formData.get('name')).toBe('test');
    expect(formData.get('age')).toBe('30');
  });

  it('classNames filters falsy values and joins classes', () => {
    expect(classNames('a', false, 'b', null, '', 'c')).toBe('a b c');
  });

  it('formatValue formats strings and arrays', () => {
    expect(formatValue('first_name')).toBe('First Name');
    expect(formatValue(['first_name', 'last_name'])).toEqual(['First Name', 'Last Name']);
  });

  it('isEmail validates email addresses', () => {
    expect(isEmail('<EMAIL>')).toBe(true);
    expect(isEmail('invalid-email')).toBe(false);
  });

  it('isPhone validates phone numbers', () => {
    expect(isPhone('+91-1234567890')).toBe(true);
    expect(isPhone('abc')).toBe(false);
  });

  it('generateSlug returns clean slug', () => {
    expect(generateSlug(' Test (Hello) @123 ')).toBe('test-123');
  });

  it('sanitizeInputValue removes invalid characters and trims spaces', () => {
    expect(sanitizeInputValue('  hello!!  ')).toBe('hello ');
    expect(sanitizeInputValue('hello   world')).toBe('hello world');
  });

  it('formatDateMDY formats date in MM-DD-YYYY format', () => {
    expect(formatDateMDY('2023-06-01')).toBe('06-01-2023');
  });

  it('getNextInvalidPage returns next invalid page', () => {
    const result = getNextInvalidPage('profile', new Set(['experience', 'calendar-add']));
    expect(result).toBe('calendar-add');
  });

  it('generateRegFormContent builds form content correctly', () => {
    const input = {
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
      registrationInfo: [
        { pageName: 'profile', payloadInfo: { bio: 'test' } }
      ],
      calendars: [
        { type: 'google', email: '<EMAIL>' },
        { type: 'outlook', email: '<EMAIL>' },
      ]
    };

    const content = generateRegFormContent(input);
    expect(content['create-account']).toEqual({
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>',
      password: '',
      confirm_password: ''
    });
    expect(content['calendar-add']).toEqual({
      google_calendar: '<EMAIL>',
      outlook_calendar: '<EMAIL>'
    });
    expect(content['profile']).toEqual({ bio: 'test' });
  });

  it('validateTherapistRegistration returns false if required pages are missing or invalid', () => {
    const formData = {
      profile: { name: 'test' },
      bio: { isInvalid: true },
      // 'modalities' will be skipped
    };
    expect(validateTherapistRegistration(formData)).toBe(false);
  });

it("validateTherapistRegistration returns true if all required pages are valid", () => {
  const formData: Record<string, any> = {};

  for (const page of THERAPIST_REGISTRATION_PAGES) {
    if (page === "modalities") continue;

    formData[page] = { valid: true }; // ✅ non-empty, no isInvalid flag
  }

  expect(validateTherapistRegistration(formData)).toBe(true);
});

  it('validatePrice checks for required, format, and length', () => {
    expect(validatePrice()).toBe('Price is required.');
    expect(validatePrice('abc')).toBe('Price must be a valid number.');
    expect(validatePrice('123.456')).toBe('Price can have up to 2 decimal places.');
    expect(validatePrice('123456789')).toBe('Price must be at most 8 characters long.');
    expect(validatePrice('123.45')).toBeUndefined();
  });
});

describe("Crypto Utilities", () => {
  const mockDigest = vi.fn();
  const mockGetRandomValues = vi.fn((arr: Uint8Array) => {
    arr.set(Array(arr.length).fill(1));
    return arr;
  });

  beforeAll(() => {
    vi.stubGlobal("crypto", {
      getRandomValues: mockGetRandomValues,
      subtle: {
        digest: mockDigest,
      },
    });
  });

  it("base64URLEncode should convert buffer to base64-url-safe string", async () => {
    const buffer = new Uint8Array([255, 254, 253]).buffer;
    const encoded = await base64URLEncode(buffer);
    expect(encoded).toBe("__79");
  });

  it("sha256 should return hash digest", async () => {
    const message = "test";
    const dummyHash = new Uint8Array([10, 20, 30]).buffer;

    mockDigest.mockResolvedValueOnce(dummyHash);
    const result = await sha256(message);

    expect(mockDigest).toHaveBeenCalled();
    expect(result).toBe(dummyHash);
  });

  it("generateURLEncodedString returns base64 encoded random string", async () => {
    const result = await generateURLEncodedString();
    expect(result).toBe(await base64URLEncode(new Uint8Array(32).fill(1).buffer));
    expect(mockGetRandomValues).toHaveBeenCalled();
  });

  it("createCodeChallenge returns hashed and encoded verifier", async () => {
    const fakeHash = new Uint8Array([11, 22, 33]).buffer;
    mockDigest.mockResolvedValueOnce(fakeHash);

    const result = await createCodeChallenge("verifier");
    expect(result).toBe(await base64URLEncode(fakeHash));
    expect(mockDigest).toHaveBeenCalled();
  });
});
