import { describe, it, expect } from "vitest";
import { getMonthDayList } from "../../src/utils/calendar.util";

describe("getMonthDayList", () => {
  it("returns all days for a month as TherapyDate[]", () => {
    const inputDate = new Date("2023-12-15"); // arbitrary mid-month date
    const result = getMonthDayList(inputDate);

    expect(Array.isArray(result)).toBe(true);
    expect(result.length).toBeGreaterThan(27); // covers at least 4 weeks

    for (const day of result) {
      expect(day).toHaveProperty("date");
      expect(day).toHaveProperty("isCurrentMonth");
      expect(day).toHaveProperty("isToday");
      expect(day).toHaveProperty("day");
      expect(day).toHaveProperty("dayOfMoth");
      expect(day).toHaveProperty("time");
      expect(day).toHaveProperty("isSelected");

      expect(typeof day.day).toBe("string");
      expect(typeof day.dayOfMoth).toBe("string");
      expect(typeof day.time).toBe("string");
      expect(typeof day.isCurrentMonth).toBe("boolean");
      expect(typeof day.isToday).toBe("boolean");
      expect(typeof day.isSelected).toBe("boolean");
    }
  });

  it("marks today correctly in isToday field", () => {
    const today = new Date();
    const list = getMonthDayList(today);

    const todayFound = list.find((d) =>
      new Date(d.date).toDateString() === today.toDateString()
    );

    expect(todayFound).toBeDefined();
    expect(todayFound?.isToday).toBe(true);
  });

  it("handles months that end in next year's week 1 (edge case)", () => {
    // December 2023 ends in week 1 of 2024
    const edgeCaseDate = new Date("2023-12-15");
    const list = getMonthDayList(edgeCaseDate);

    // Should include dates from late November and early January
    const first = list[0];
    const last = list[list.length - 1];

    expect(first.date.getMonth()).toBeLessThanOrEqual(11);
    expect(last.date.getMonth()).toBeGreaterThanOrEqual(0);
  });
});
