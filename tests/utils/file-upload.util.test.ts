import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleFileUpload } from '../../src/utils/file-upload.util';
import notification from '../../src/utils/notification.util';
import { request } from '../../src/utils/request.utils';

vi.mock('@/utils/notification.util', () => ({
  default: {
    error: vi.fn(),
  },
}));

vi.mock('@/utils/request.utils', () => ({
  request: {
    post: vi.fn(),
  },
}));

vi.mock('compressorjs', async () => {
  return {
    default: vi.fn((file, options) => {
      if (file.name.includes('error')) {
        options.error(new Error("Compression failed"));
      } else if (file.name.includes('large')) {
        const result = new Blob(['a'.repeat(6 * 1024 * 1024)], { type: 'image/jpeg' }); // > 5MB
        options.success(result);
      } else {
        const result = new Blob(['compressed'], { type: 'image/jpeg' }); // < 5MB
        options.success(result);
      }
    }),
  };
});

describe('handleFileUpload', () => {
  const mockPost = request.post as unknown as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const createFile = (name: string, type: string, size: number) =>
    new File(['a'.repeat(size)], name, { type });

  it('uploads non-image file < 5MB', async () => {
    const file = createFile('doc.pdf', 'application/pdf', 1024);
    mockPost.mockResolvedValue({ status: 200, data: { uploaded: ['file.pdf'] } });

    const result = await handleFileUpload([file]);
    expect(result).toEqual(['file.pdf']);
  });

  it('fails if non-image file > 5MB', async () => {
    const file = createFile('big.pdf', 'application/pdf', 6 * 1024 * 1024);
    const result = await handleFileUpload([file]);

    expect(notification.error).toHaveBeenCalledWith('big.pdf exceeds 5MB.');
    expect(result).toBeNull();
  });

  it('compresses image and uploads if < 5MB after compression', async () => {
    const file = createFile('photo.jpg', 'image/jpeg', 4 * 1024 * 1024);
    mockPost.mockResolvedValue({ status: 200, data: { uploaded: ['photo.jpg'] } });

    const result = await handleFileUpload([file]);
    expect(result).toEqual(['photo.jpg']);
  });

  it('fails if compressed image > 5MB', async () => {
    const file = createFile('large-photo.jpg', 'image/jpeg', 6 * 1024 * 1024);
    const result = await handleFileUpload([file]);

    expect(notification.error).toHaveBeenCalledWith('large-photo.jpg exceeds 5MB after compression.');
    expect(result).toBeNull();
  });

  it('shows error if compression fails', async () => {
    const file = createFile('error-photo.jpg', 'image/jpeg', 1024);
    const result = await handleFileUpload([file]);

    expect(notification.error).toHaveBeenCalledWith('Could not compress error-photo.jpg.');
    expect(result).toBeNull();
  });

  it('shows error if upload response is not 200', async () => {
    const file = createFile('photo.jpg', 'image/jpeg', 1024);
    mockPost.mockResolvedValue({ status: 500 });

    const result = await handleFileUpload([file]);
    expect(notification.error).toHaveBeenCalledWith('File upload failed.');
    expect(result).toBeNull();
  });

  it('shows error if upload throws exception', async () => {
    const file = createFile('photo.jpg', 'image/jpeg', 1024);
    mockPost.mockRejectedValue(new Error('upload failed'));

    const result = await handleFileUpload([file]);
    expect(notification.error).toHaveBeenCalledWith('An error occurred during file upload.');
    expect(result).toBeNull();
  });
});
