import { describe, it, expect, vi, beforeEach } from "vitest";
import notification from "../../src/utils/notification.util";

// 👇 Proper full mock
vi.mock("react-hot-toast", async () => {
  const dismissMock = vi.fn();
  const toastMock = vi.fn((componentFn: any) => {
    const fakeToast = { id: "test-id" };
    // simulate rendering
    componentFn?.(fakeToast);
  });
  // attach dismiss to the toast function
  toastMock.dismiss = dismissMock;

  return {
    default: toastMock,
    dismiss: dismissMock,
  };
});

import toast from "react-hot-toast";

describe("notification util", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("calls toast.success with correct message", () => {
    notification.success("Success message");
    expect(toast).toHaveBeenCalled();
  });

  it("calls toast.error with correct message", () => {
    notification.error("Error message");
    expect(toast).toHaveBeenCalled();
  });

  it("calls toast.info with correct message", () => {
    notification.info("Info message");
    expect(toast).toHaveBeenCalled();
  });

  it("dismisses toast when close button clicked", () => {
    const mockDismiss = vi.mocked(toast.dismiss);
    const toastFn = vi.mocked(toast);

    // Reassign toast to a real function with dismiss attached
    toastFn.mockImplementation((componentFn: any) => {
      const fakeToast = { id: "dismiss-id" };
      const jsx = componentFn(fakeToast);
      const closeButton = jsx.props.children[1].props.children;

      // Simulate click
      closeButton.props.onClick();

      expect(mockDismiss).toHaveBeenCalledWith("dismiss-id");
    });

    notification.success("Test dismiss");
  });
});
