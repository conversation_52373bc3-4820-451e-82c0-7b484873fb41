import { describe, it, expect, vi, beforeEach } from "vitest";
import axios from "axios";
import { makeParams, useApiClient } from "../../src/utils/api.util";
import { useAppSelector } from "../../src/store/hooks";
import { useDispatch } from "react-redux";
import { setTokens, signOut } from "../../src/store/slicers/auth.slicer";

// Mock imports
vi.mock("@/store/hooks", () => ({
  useAppSelector: vi.fn(),
}));

vi.mock("react-redux", () => ({
  useDispatch: vi.fn(),
}));

vi.mock("@/store/slicers/auth.slicer", () => ({
  setTokens: vi.fn((tokens) => ({ type: "SET_TOKENS", payload: tokens })),
  signOut: vi.fn(() => ({ type: "SIGN_OUT" })),
}));

vi.mock("axios");

describe("makeParams", () => {
  it("removes null and undefined keys", () => {
    const params = { a: 1, b: null, c: undefined, d: "test" };
    expect(makeParams(params)).toEqual({ a: 1, d: "test" });
  });

  it("returns empty object when all values are nullish", () => {
    expect(makeParams({ a: null, b: undefined })).toEqual({});
  });
});

describe("useApiClient", () => {
  const mockDispatch = vi.fn();
  const postMock = vi.fn();
  const getMock = vi.fn();

  const axiosCreateMock = {
    interceptors: {
      response: {
        use: vi.fn(),
      },
    },
    post: postMock,
    get: getMock,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useAppSelector as any).mockImplementation(() => ({
      token: "test-token",
      refreshToken: "refresh-token",
    }));
    (useDispatch as any).mockReturnValue(mockDispatch);
    (axios.create as any).mockReturnValue(axiosCreateMock);
  });

  it("creates axios instance with auth header", () => {
    useApiClient();
    expect(axios.create).toHaveBeenCalledWith({
      baseURL: `${import.meta.env.VITE_API_URL}`,
      headers: { Authorization: "Bearer test-token" },
    });
  });

  it("registers interceptor", () => {
    useApiClient();
    expect(axiosCreateMock.interceptors.response.use).toHaveBeenCalled();
  });

  it("handles 401 error and refreshes token successfully", async () => {
    const client = useApiClient();

    const interceptor = axiosCreateMock.interceptors.response.use.mock.calls[0][1];

    const mockError = {
      config: {
        _retry: false,
        headers: {},
      },
      response: {
        status: 401,
      },
    };

    postMock.mockResolvedValueOnce({ data: { accessToken: "new-token" } });
    const originalRequest = mockError.config;

    (axios as any).mockResolvedValue("retry-success");

    const result = await interceptor(mockError);

    expect(postMock).toHaveBeenCalledWith("/auth/refresh", {
      refreshToken: "refresh-token",
    });
    expect(setTokens).toHaveBeenCalledWith({ accessToken: "new-token" });
    expect(originalRequest.headers.Authorization).toBe("Bearer new-token");
    expect(result).toBe("retry-success");
  });

  it("handles refresh failure and dispatches signOut", async () => {
  const client = useApiClient();
  const interceptor = axiosCreateMock.interceptors.response.use.mock.calls[0][1];

  const mockError = {
    config: {
      _retry: false,
      headers: {},
    },
    response: {
      status: 401,
    },
  };

  postMock.mockRejectedValueOnce(new Error("Refresh failed"));

  const result = await interceptor(mockError).catch((e: any) => e);

  expect(postMock).toHaveBeenCalled();
  expect(signOut).toHaveBeenCalled();
  expect(result).toBeUndefined(); // ✅ changed this line
});


  it("passes through non-401 errors", async () => {
    const client = useApiClient();

    const interceptor = axiosCreateMock.interceptors.response.use.mock.calls[0][1];

    const mockError = {
      config: {},
      response: {
        status: 500,
      },
    };

    const result = await interceptor(mockError).catch((e: any) => e);
    expect(result).toBe(mockError);
  });
});
