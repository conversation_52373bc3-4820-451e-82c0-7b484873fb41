// tests/utils/pageStorage.utils.test.ts
import { describe, it, beforeEach, afterEach, expect } from 'vitest';
import {
  storePageData,
  getPageData,
  getPreviousPageId,
  storeConditionalPageData,
  updateConditionalPageData,
  getPendingPageId,
  getAllConditionalPageIds,
  deleteConditionalPageData,
  storeFollowThroughPages,
  getMainPageId,
} from '../../src/utils/storage.util';

beforeEach(() => {
  localStorage.clear();
});

afterEach(() => {
  localStorage.clear();
});

describe('Page Storage Utilities', () => {
  it('stores and retrieves page data', () => {
    storePageData({ id: 1, nextPageId: 2 });
    expect(getPageData()).toEqual([{ id: 1, nextPageId: 2 }]);
  });

  it('removes duplicates and sorts pages by id', () => {
    storePageData({ id: 2, nextPageId: 3 });
    storePageData({ id: 1, nextPageId: 2 });
    storePageData({ id: 1, nextPageId: 2 }); // duplicate
    const data = getPageData();
    expect(data).toEqual([
      { id: 2, nextPageId: 3 },
      { id: 1, nextPageId: 2 },
    ].sort((a, b) => a.id - b.id));
  });

  it('returns the previous page ID', () => {
    storePageData({ id: 1, nextPageId: 2 });
    storePageData({ id: 2, nextPageId: 3 });
    expect(getPreviousPageId(3)).toBe(2);
    expect(getPreviousPageId(2)).toBe(1);
    expect(getPreviousPageId(99)).toBeNull();
  });

  it('stores and toggles conditional page data', () => {
    storeConditionalPageData({ id: 1, pending: true });
    expect(getAllConditionalPageIds()).toEqual([1]);

    storeConditionalPageData({ id: 1, pending: true }); // toggle off
    expect(getAllConditionalPageIds()).toEqual([]);
  });

  it('updates conditional page pending status', () => {
    storeConditionalPageData({ id: 1, pending: false });
    updateConditionalPageData(1, true);
    const updated = JSON.parse(localStorage.getItem('conditionalPages')!);
    expect(updated[0].pending).toBe(true);
  });

  it('returns first pending page ID', () => {
    storeConditionalPageData({ id: 1, pending: false });
    storeConditionalPageData({ id: 2, pending: true });
    expect(getPendingPageId()).toBe(2);
    updateConditionalPageData(2, false);
    expect(getPendingPageId()).toBeNull();
  });

  it('deletes conditional page data', () => {
    storeConditionalPageData({ id: 1, pending: true });
    deleteConditionalPageData();
    expect(getAllConditionalPageIds()).toEqual([]);
  });

  it('stores and retrieves followThroughPages without duplication', () => {
    storeFollowThroughPages(1, 100);
    storeFollowThroughPages(1, 100); // duplicate
    storeFollowThroughPages(2, 200);

    const stored = JSON.parse(localStorage.getItem('followThroughPages')!);
    expect(stored.length).toBe(2);
    expect(stored).toContainEqual({ mainPageId: 1, followingPageId: 100 });
    expect(stored).toContainEqual({ mainPageId: 2, followingPageId: 200 });
  });

  it('retrieves mainPageId from followingPageId', () => {
    storeFollowThroughPages(1, 101);
    storeFollowThroughPages(2, 202);
    expect(getMainPageId(202)).toBe(2);
    expect(getMainPageId(999)).toBeNull();
  });
});
