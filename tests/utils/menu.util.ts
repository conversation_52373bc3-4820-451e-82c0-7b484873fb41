import { Menu } from "@/types/menu.interface";
import { UserType } from "./enum.util";

const menus: Menu[] = [
  {
    label: "Menu",
    isTitle: true,
    roles: [UserType.SYSTEM],
  },
  {
    label: "Dashboard",
    isTitle: false,
    link: "/",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Users",
    isTitle: false,
    link: "/users",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Patients",
    isTitle: false,
    link: "/patients",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Therapists",
    isTitle: false,
    link: "/therapists",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Speciality",
    isTitle: false,
    link: "/specialities",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Questionnaires",
    isTitle: false,
    link: "/questionnaires",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Answers",
    isTitle: false,
    link: "/answers",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Pages",
    isTitle: false,
    link: "/pages",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Events",
    isTitle: false,
    link: "/events",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Subscription Plans",
    isTitle: false,
    link: "/subscription-plans",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Matching Algo",
    isTitle: false,
    link: "/matching-algo",
    roles: [UserType.SYSTEM],
  },
  {
    label: "",
    isTitle: false,
    isSpacer: true,
    roles: [UserType.SYSTEM],
  },
  {
    label: "Settings",
    isTitle: false,
    link: "/settings",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Support",
    isTitle: false,
    link: "/support",
    roles: [UserType.SYSTEM],
  },
  {
    label: "Home",
    isTitle: false,
    icon : {
      inactive: "/home_inactive.svg",
      active: "/home_active.svg",
    },
    link: "/",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Personal Details",
    isTitle: false,
    icon : {
      inactive: "/personalDetail_inactive.svg",
      active: "/personalDetail_active.svg",
    },
    link: "/personal-details",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Your Profile",
    isTitle: false,
    icon : {
      inactive: "/profile_inactive.svg",
      active: "/profile_active.svg",
    },
    link: "/profile",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Calendar & Appointments",
    isTitle: false,
    icon : {
      inactive: "/calender_inactive.svg",
      active: "/calender_active.svg",
    },
    link: "/calendars-appointments",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Subscriptions & Payment",
    isTitle: false,
    icon : {
      inactive: "/subscription_inactive.svg",
      active: "/subscription_active.svg",
    },
    link: "/subscriptions-payments",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Reporting",
    isTitle: false,
    icon : {
      inactive: "/reporting_inactive.svg",
      active: "/reporting_active.svg",
    },
    link: "/reporting",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Password & Security",
    isTitle: false,
    icon : {
      inactive: "/password_inactive.svg",
      active: "/password_active.svg",
    },
    link: "/security",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Notifications",
    isTitle: false,
    icon : {
      inactive: "/notification_inactive.svg",
      active: "/notification_active.svg",
    },
    link: "/notifications",
    roles: [UserType.THERAPIST],
  },
  {
    label: "Help Center",
    isTitle: false,
    icon : {
      inactive: "/helpcenter_inactive.svg",
      active: "/helpcenter_active.svg",
    },
    link: "/help-center",
    roles: [UserType.THERAPIST],
  }
];

export const prepareMenus = (role: string, unverifiedCount = 0) => {
  return menus.map((menu) => {
    if (menu.label === "Therapists" && role === UserType.SYSTEM && unverifiedCount > 0) {
      return {
        ...menu,
        label: `${menu.label} (${unverifiedCount})`,
      };
    }
    return menu;
  }).filter((menu) => {
    return menu.roles?.some((r) => [role, "*"].includes(r));
  });
};

