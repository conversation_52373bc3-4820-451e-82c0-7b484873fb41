import { describe, it, expect, vi, beforeEach } from 'vitest';
import { request } from '../../src/utils/request.utils';
import notification from '../../src/utils/notification.util';
import store from '../../src/store';
import { setTokens, signOut } from '../../src/store/slicers/auth.slicer';

vi.mock('@/utils/notification.util', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('@/store', () => ({
  default: {
    dispatch: vi.fn(),
    getState: vi.fn(),
  },
}));

vi.mock('@/store/slicers/auth.slicer', () => ({
  setTokens: vi.fn(),
  signOut: vi.fn(),
}));

const mockStore = store as unknown as {
  dispatch: typeof vi.fn;
  getState: typeof vi.fn;
};

describe('Axios Interceptors (No backend, no axios-mock-adapter)', () => {
  beforeEach(() => {
    vi.restoreAllMocks();
    vi.clearAllMocks();
  });

  it('adds Authorization header if token exists', async () => {
    mockStore.getState.mockReturnValue({ auth: { token: 'abc' } });

    const mockFn = vi.spyOn(request, 'get').mockResolvedValue({ config: { headers: {} } });

    await request.get('/test');

    expect(mockFn).toHaveBeenCalled();
  });

  it('suppresses error if suppressError is true', async () => {
    mockStore.getState.mockReturnValue({ auth: {} });

    vi.spyOn(request, 'get').mockRejectedValueOnce({
      response: { data: { message: 'Hidden error' } },
      config: { suppressError: true },
    });

    await expect(request.get('/error', { suppressError: true })).rejects.toThrow();
    expect(notification.error).not.toHaveBeenCalled();
  });

  it('shows success message if response contains a message', async () => {
  mockStore.getState.mockReturnValue({ auth: {} });

  // Patch Axios adapter to simulate real network response
  request.defaults.adapter = async (config) => {
    return {
      data: { message: 'Success' },
      status: 200,
      statusText: 'OK',
      headers: {},
      config,
    };
  };

  await request.get('/test');

  expect(notification.success).toHaveBeenCalledWith('Success');
});


it('retries request on 401 and sets new token', async () => {
  const refreshToken = 'refresh-token';
  const newAccessToken = 'new-token';

  let callCount = 0;

  // Mock store behavior for each getState call
  mockStore.getState
    .mockReturnValueOnce({ auth: { token: 'expired-token' } }) // original request
    .mockReturnValueOnce({ auth: { refreshToken } })           // for refresh token
    .mockReturnValueOnce({ auth: { token: newAccessToken } }); // after refresh

  // Mock refresh token API call
  vi.spyOn(request, 'post').mockResolvedValueOnce({
    data: { accessToken: newAccessToken }
  });

  // Patch the adapter to simulate 401 on first call and success on retry
  request.defaults.adapter = async (config) => {
    callCount++;
    if (callCount === 1) {
      const error: any = new Error('Unauthorized');
      error.response = { status: 401 };
      error.config = { ...config, _retry: false, headers: {} };
      throw error;
    }

    return {
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config,
    };
  };

  await request.get('/secure');

  expect(setTokens).toHaveBeenCalledWith({ accessToken: newAccessToken });
});


it('signs out if token refresh fails', async () => {
  // 1. Simulate token state and refresh token
  mockStore.getState
    .mockReturnValueOnce({ auth: { token: 'expired-token' } })     // first request
    .mockReturnValueOnce({ auth: { refreshToken: 'bad-token' } }); // refresh attempt

  // 2. Simulate refresh token failure
  vi.spyOn(request, 'post').mockRejectedValueOnce(new Error('Refresh failed'));

  // 3. Simulate 401 response from first request
  request.defaults.adapter = async (config) => {
    const error: any = new Error('Unauthorized');
    error.response = { status: 401 };
    error.config = { ...config, _retry: false, headers: {} };
    throw error;
  };

  await expect(request.get('/fail')).rejects.toThrow();
  expect(signOut).toHaveBeenCalled();
});

it('shows error if suppressError is false', async () => {
  mockStore.getState.mockReturnValue({ auth: {} });

   request.defaults.adapter = async (config) => {
    const error: any = new Error('Some error');
    error.response = {
      data: { message: 'Some error' },
      status: 400,
    };
    error.config = { ...config };
    throw error;
  };

  await expect(request.get('/error')).rejects.toThrow();
  expect(notification.error).toHaveBeenCalledWith('Some error');
});


});
