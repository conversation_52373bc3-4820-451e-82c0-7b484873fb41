import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";
import { MemoryRouter } from "react-router-dom";
import { describe, it, beforeEach, expect } from "vitest";
import "@testing-library/jest-dom";
import Application from "../src/Application";
import React from "react";
import toast from "react-hot-toast";

const mockStore = configureStore([]);
let store: any;


describe("Application Component", () => {
  beforeEach(() => {
    store = null;
  });



  it("renders TherapySidebar and TherapyAppRoutes when user is therapist", () => {
    store = mockStore({
      auth: { isAuthenticated: true, user: { role: "therapist" } },
      sidebar: {},
      calendar: {},
      therapist: {},
    });

    render(
      <Provider store={store}>
        <MemoryRouter>
          <Application />
        </MemoryRouter>
      </Provider>
    );

    const therapySidebar = screen.queryByTestId("therapy-sidebar");
    const therapyAppRoutes = screen.queryByTestId("therapy-app-routes");
  
    expect(therapySidebar).toBeInTheDocument();
    expect(therapyAppRoutes).toBeInTheDocument();   
  });

it("renders toast and AuthRoutes when user is not authenticated", async () => {
  store = mockStore({
    auth: { isAuthenticated: false, user: null },
    sidebar: {},
    calendar: {},
    therapist: {},
  });

  render(
    <Provider store={store}>
      <MemoryRouter>
        <Application />
      </MemoryRouter>
    </Provider>
  );

  toast.success("Test Toast");
  expect(await screen.findByText("Test Toast")).toBeInTheDocument();

});

it("renders Sidebar and AppRoutes when user is authenticated and not a therapist", () => {
  store = mockStore({
    auth: { isAuthenticated: true, user: { role: "admin" } },
    sidebar: {},
    calendar: {},
    therapist: {},
  });

  render(
    <Provider store={store}>
      <MemoryRouter>
        <Application />
      </MemoryRouter>
    </Provider>
  );

  const sidebar = screen.getByTestId("sidebar");
  const appRoutes = screen.getByTestId("app-routes");

  expect(sidebar).toBeInTheDocument();
  expect(appRoutes).toBeInTheDocument();
});

});
