import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import UserListPage from '../../../src/pages/users/UserListPage'
import '@testing-library/jest-dom'

// ---- Mocked Redux Store ----
const createMockStore = () =>
  configureStore({
    reducer: {
      auth: () => ({
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
      }),
    },
  })

// ---- Mocks ----
vi.mock('@/utils/app.util', () => ({
  getFullName: (f: string, l: string) => `${f}-${l}`,
}))

vi.mock('@/components/VerificationDateComponent', () => ({
  __esModule: true,
  default: (value: any) =>
    value ? <span data-testid="verified">✔</span> : null,
}))

vi.mock('@/repositories/UserRepository', async () => {
  const users = [
    {
      id: '1',
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
      phone: '123',
      role: 'admin',
      emailVerifiedAt: '2022',
      phoneVerifiedAt: null,
      deactivatedAt: null,
      acceptedAt: true,
    },
    {
      id: '2',
      firstname: 'Jane',
      lastname: 'Smith',
      email: '<EMAIL>',
      phone: null,
      role: 'user',
      emailVerifiedAt: null,
      phoneVerifiedAt: null,
      deactivatedAt: '2020-01-01',
      acceptedAt: true,
    },
  ]

  return {
    default: class {
      getAll = vi.fn().mockResolvedValue({
        data: users,
        meta: { nextPage: undefined },
      })
      resetPassword = vi.fn().mockImplementation((url, email) =>
        email === '<EMAIL>' ? Promise.resolve(true) : Promise.resolve(false)
      )
      deactivate = vi.fn().mockResolvedValue(true)
      activate = vi.fn().mockResolvedValue(true)
    },
  }
})


function renderWithProviders(ui: React.ReactElement) {
  const client = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  })
  const store = createMockStore()

  return render(
    <Provider store={store}>
      <QueryClientProvider client={client}>{ui}</QueryClientProvider>
    </Provider>
  )
}

describe('<UserListPage />', () => {
  it('renders user list with proper content', async () => {
    renderWithProviders(<UserListPage />)
    await screen.findByText('John-Doe')
    expect(screen.getByText('Jane-Smith')).toBeInTheDocument()
    expect(screen.getByText('(Deactivated)')).toBeInTheDocument()
    expect(screen.getByText('Phone number not available')).toBeInTheDocument()
    expect(screen.getAllByTestId('verified')).toHaveLength(1)
  })

  it('calls debounced search when typing in the search input', async () => {
  renderWithProviders(<UserListPage />)

  const input = screen.getByPlaceholderText('Search...')

  fireEvent.change(input, { target: { value: 'Jane' } })

  await waitFor(() => {
    // Wait for debounce (>= 500ms)
    expect(input).toHaveValue('Jane')
  }, { timeout: 1000 }) // allow buffer time
})


})
