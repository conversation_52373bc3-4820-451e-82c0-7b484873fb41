import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import UserForm from "../../../src/pages/users/UserForm";
import { OverlayTriggerState } from "react-stately";
import "@testing-library/jest-dom";

// Mock components
vi.mock("@/components/dialogs/FormDialog", () => ({
  default: ({ children, onSubmit, onCancel, confirmLabel, loading }: any) => (
    <form onSubmit={onSubmit}>
      {children}
      <button type="submit" disabled={loading}>
        {confirmLabel}
      </button>
      <button type="button" onClick={onCancel}>
        Cancel
      </button>
    </form>
  ),
}));

vi.mock("@/components/forms/InputComponent", () => ({
  default: ({ label, id, value, onChange }: any) => (
    <input
      aria-label={label}
      id={id}
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  ),
}));

vi.mock("@/components/forms/DateComponent", () => ({
  default: ({ label, id, value, onChange }: any) => (
    <input
      aria-label={label}
      id={id}
      value={value}
      type="date"
      onChange={(e) => onChange(e.target.value)}
    />
  ),
}));

describe("UserForm", () => {
  const mockState: OverlayTriggerState = {
    isOpen: true,
    open: vi.fn(),
    close: vi.fn(),
    toggle: vi.fn(),
  };

  const mockRepo = {
    create: vi.fn().mockResolvedValue({ id: 1 }),
    update: vi.fn().mockResolvedValue(true),
  };

  const refetch = vi.fn();

  it("renders empty form for new user", () => {
    render(<UserForm state={mockState} repo={mockRepo} />);
    expect(screen.getByLabelText("Firstname")).toHaveValue("");
    expect(screen.getByLabelText("Lastname")).toHaveValue("");
    expect(screen.getByLabelText("E-mail")).toHaveValue("");
    expect(screen.getByLabelText("Phone Number")).toHaveValue("");
    expect(screen.getByLabelText("Date of Birth")).toHaveValue("");
  });

  it("populates form when user prop is provided", () => {
    const user = {
      id: 1,
      firstname: "John",
      lastname: "Doe",
      email: "<EMAIL>",
      phone: "1234567890",
      dob: "1990-01-01",
    };

    render(<UserForm state={mockState} repo={mockRepo} user={user} />);
    expect(screen.getByLabelText("Firstname")).toHaveValue("John");
    expect(screen.getByLabelText("Lastname")).toHaveValue("Doe");
    expect(screen.getByLabelText("E-mail")).toHaveValue("<EMAIL>");
    expect(screen.getByLabelText("Phone Number")).toHaveValue("1234567890");
    expect(screen.getByLabelText("Date of Birth")).toHaveValue("1990-01-01");
  });

  it("calls repo.create on submit for new user", async () => {
    render(<UserForm state={mockState} repo={mockRepo} refetch={refetch} />);

    fireEvent.change(screen.getByLabelText("Firstname"), {
      target: { value: "Alice" },
    });
    fireEvent.change(screen.getByLabelText("Lastname"), {
      target: { value: "Smith" },
    });
    fireEvent.change(screen.getByLabelText("E-mail"), {
      target: { value: "<EMAIL>" },
    });

    fireEvent.submit(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockRepo.create).toHaveBeenCalledWith({
        firstname: "Alice",
        lastname: "Smith",
        email: "<EMAIL>",       
      });
    });

    expect(refetch).toHaveBeenCalled();
    expect(mockState.close).toHaveBeenCalled();
  });

  it("calls repo.update on submit for existing user", async () => {
    const user = {
      id: 2,
      firstname: "Bob",
      lastname: "Marley",
      email: "<EMAIL>",
      phone: "9999999999",
      dob: "1980-05-11",
    };

    render(
      <UserForm state={mockState} repo={mockRepo} user={user} refetch={refetch} />
    );

    fireEvent.change(screen.getByLabelText("Firstname"), {
      target: { value: "Bobbie" },
    });

    fireEvent.submit(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockRepo.update).toHaveBeenCalledWith(2, {
        firstname: "Bobbie",
        lastname: "Marley",
        email: "<EMAIL>",
        phone: "9999999999",
        dob: "1980-05-11",
      });
    });

    expect(refetch).toHaveBeenCalled();
    expect(mockState.close).toHaveBeenCalled();
  });

  it("calls onCancel correctly", () => {
    render(<UserForm state={mockState} repo={mockRepo} />);
    fireEvent.click(screen.getByRole("button", { name: /cancel/i }));
    expect(mockState.close).toHaveBeenCalled();
  });
});
