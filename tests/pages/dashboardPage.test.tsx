import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom";
import DashboardPage from "../../src/pages/DashboardPage";
import React from "react";


vi.mock("react-intersection-observer", () => ({
  useInView: vi.fn(() => ({ ref: vi.fn() })),
}));

vi.mock("@/components/calendar/CalendarExample", () => ({
  default: () => <div data-testid="calendar-example">CalendarExample</div>,
}));

describe("DashboardPage", () => {
  it("renders the DashboardPage component correctly", () => {
    render(<DashboardPage />);

    const mainContainer = screen.getByTestId("dashboard-container");
    expect(mainContainer).toBeInTheDocument();

    const calendarExample = screen.getByTestId("calendar-example");
    expect(calendarExample).toBeInTheDocument();
  });

  it("applies the correct class names to the main container", () => {
    render(<DashboardPage />);

    const mainContainer = screen.getByTestId("dashboard-container");
    expect(mainContainer).toHaveClass("px-16 h-full flex flex-col flex-grow");
  });
});