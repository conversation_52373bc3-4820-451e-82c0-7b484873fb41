import { render, screen, waitFor } from "@testing-library/react";
import QuestionnaireTypePage from "../../../src/pages/therapist-pages/QuestionnaireTypePage";
import { describe, expect, it, vi } from "vitest";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";

vi.mock("react-perfect-scrollbar", () => ({
  __esModule: true,
  default: (props: any) => <div>{props.children}</div>,
}));

vi.mock("@/pages/therapist-pages/AnswerForm", () => ({
  __esModule: true,
  default: () => <div data-testid="condition-page">ConditionPage</div>,
}));

// Helper to wrap with Redux Provider
const renderWithProvider = (ui: React.ReactElement) => {
  const store = configureStore({
    reducer: {
      auth: () =>
        ({
          token: "mock-token",
          refreshToken: "mock-refresh-token",
        } as any),
    },
  });

  return render(<Provider store={store}>{ui}</Provider>);
};

describe("QuestionnaireTypePage", () => {
  const setup = (overrideFormData = {}) => {
    const formData = {
      category: { value: "sample-cat", label: "Sample Category" },
      questionnaireType: null,
      ...overrideFormData,
    };

    const props = {
      formData,
      setFormData: vi.fn(),
      question: null,
      setQuestion: vi.fn(),
      answers: [],
      setAnswers: vi.fn(),
      answer: undefined,
      setAnswer: vi.fn(),
      removedAnswers: [],
      setRemovedAnswers: vi.fn(),
    };

    renderWithProvider(<QuestionnaireTypePage {...props} />);
    return props;
  };

  it("renders all select labels", () => {
    setup();
    expect(screen.getByText("Question")).toBeInTheDocument();
    expect(screen.getByText("Select Type")).toBeInTheDocument();
  expect(
  screen.getAllByText("Answer").find((el) => el.tagName === "LABEL")
).toBeInTheDocument();
  });

it("disables selects when no category", () => {
  setup({ category: null });

  const question = screen.getByText("Select question").closest('[aria-disabled="true"]');
  expect(question).toBeInTheDocument();

  const type = screen.getByText("Select").closest('[aria-disabled="true"]');
  expect(type).toBeInTheDocument();

  const answerLabel = screen.getAllByText("Answer").find((el) => el.tagName.toLowerCase() === "label");
  const answer = answerLabel?.closest("section")?.querySelector('[aria-disabled="true"]');
  expect(answer).toBeInTheDocument();
});


  // it("loads and selects a question", async () => {
  //   const { setQuestion } = setup();
  //   const questionSelect = screen.getByText("Select question");
  //   await userEvent.click(questionSelect);
  //   await waitFor(() => screen.getByText("Test Question"));
  //   await userEvent.click(screen.getByText("Test Question"));
  //   expect(setQuestion).toHaveBeenCalledWith({ label: "Test Question", value: 1 });
  // });

 
  it("shows empty state message when no answers", () => {
    setup();
    expect(screen.getByText("No answers selected yet.")).toBeInTheDocument();
  });

  it("renders condition page when answer is present", () => {
    renderWithProvider(
      <QuestionnaireTypePage
        formData={{ category: { value: "cat" } }}
        setFormData={vi.fn()}
        question={null}
        setQuestion={vi.fn()}
        answers={[{ label: "Test", value: 1 }]}
        setAnswers={vi.fn()}
        answer={{ label: "Test", value: 1 }}
        setAnswer={vi.fn()}
        removedAnswers={[]}
        setRemovedAnswers={vi.fn()}
      />
    );
    expect(screen.getByTestId("condition-page")).toBeInTheDocument();
  });

  it("deletes an answer via dialog", async () => {
    const mockAnswer = { label: "Answer 1", value: 1 };
    const setAnswer = vi.fn();
    const setAnswers = vi.fn();
    const setRemovedAnswers = vi.fn();

    renderWithProvider(
      <QuestionnaireTypePage
        formData={{ category: { value: "cat" } }}
        setFormData={vi.fn()}
        question={null}
        setQuestion={vi.fn()}
        answers={[mockAnswer]}
        setAnswers={setAnswers}
        answer={mockAnswer}
        setAnswer={setAnswer}
        removedAnswers={[]}
        setRemovedAnswers={setRemovedAnswers}
      />
    );

    const deleteIcon = screen.getByTestId("delete-answer-icon");
    await userEvent.click(deleteIcon);

    await waitFor(() =>
      expect(
        screen.getByText(/Are you sure you want to remove this answer/i)
      ).toBeInTheDocument()
    );

    await userEvent.click(screen.getByText("Delete"));

    expect(setAnswers).toHaveBeenCalled();
    expect(setRemovedAnswers).toHaveBeenCalledWith([1]);
    expect(setAnswer).toHaveBeenCalledWith(undefined);
  });
});
