import { render, screen, fireEvent } from "@testing-library/react";
import InfoTypePage from "../../../src/pages/therapist-pages/InfoTypePage";
import { describe, expect, it, vi } from "vitest";
import "@testing-library/jest-dom"

// Mock InputComponent to control and assert behavior
vi.mock("@/components/forms/InputComponent", () => ({
  default: ({ label, value, onChange, isRequired }: any) => (
    <div>
      <label>
        {label}
        {isRequired && "*"}
        <input
          aria-label={label}
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
      </label>
    </div>
  ),
}));

describe("InfoTypePage", () => {
  it("renders both input fields with initial values", () => {
    const formData = { info: "Initial Info", extra: "Extra Detail" };
    const setFormData = vi.fn();

    render(<InfoTypePage formData={formData} setFormData={setFormData} />);

    expect(screen.getByLabelText("Info")).toHaveValue("Initial Info");
    expect(screen.getByLabelText("Extra Info.")).toHaveValue("Extra Detail");
  });

  it("calls setFormData on info input change", () => {
    const formData = { info: "", extra: "" };
    const setFormData = vi.fn();

    render(<InfoTypePage formData={formData} setFormData={setFormData} />);
    const input = screen.getByLabelText("Info");

    fireEvent.change(input, { target: { value: "Updated Info" } });

    expect(setFormData).toHaveBeenCalledWith({ info: "Updated Info", extra: "" });
  });

  it("calls setFormData on extra input change", () => {
    const formData = { info: "", extra: "" };
    const setFormData = vi.fn();

    render(<InfoTypePage formData={formData} setFormData={setFormData} />);
    const input = screen.getByLabelText("Extra Info.");

    fireEvent.change(input, { target: { value: "Updated Extra" } });

    expect(setFormData).toHaveBeenCalledWith({ info: "", extra: "Updated Extra" });
  });

  it("handles missing formData fields", () => {
    const formData = {};
    const setFormData = vi.fn();

    render(<InfoTypePage formData={formData} setFormData={setFormData} />);

    expect(screen.getByLabelText("Info")).toHaveValue("");
    expect(screen.getByLabelText("Extra Info.")).toHaveValue("");
  });
});
