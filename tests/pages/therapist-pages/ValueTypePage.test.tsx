import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import ValueTypePage from "../../../src/pages/therapist-pages/ValueTypePage";
import React from "react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import thunk from "redux-thunk";
import { act } from "react-dom/test-utils";
import "@testing-library/jest-dom";

vi.mock("react-redux", async () => {
  const actual = await vi.importActual<typeof import("react-redux")>("react-redux");
  return {
    ...actual,
    useAppSelector: vi.fn().mockImplementation(() => ({
      token: "mock-token",
      refreshToken: "mock-refresh-token",
    })),
    useDispatch: () => vi.fn(), // if needed
  };
});


const dummyReducer = () => ({});
const createTestStore = () =>
  configureStore({
    reducer: dummyReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(thunk),
  });

const renderWithProvider = (ui: React.ReactElement) =>
  render(<Provider store={createTestStore()}>{ui}</Provider>);

// Default props generator
const createProps = (overrides = {}) => ({
  formData: { category: { value: "cat" } },
  setFormData: vi.fn(),
  question: null,
  setQuestion: vi.fn(),
  answers: [],
  setAnswers: vi.fn(),
  answer: undefined,
  setAnswer: vi.fn(),
  removedAnswers: [],
  setRemovedAnswers: vi.fn(),
  ...overrides,
});

// ---------- Mocks ----------

vi.mock("uuid", () => ({
  v4: vi.fn(() => "mock-uuid"),
}));

vi.mock("lodash", async () => {
  const mod = await vi.importActual("lodash");
  return {
    ...mod,
    debounce: (fn: any) => fn,
  };
});

vi.mock("react-perfect-scrollbar", () => ({
  __esModule: true,
  default: ({ children }: any) => <div>{children}</div>,
}));

vi.mock("@/repositories/QuestionnaireRepository", () => ({
  default: vi.fn().mockImplementation(() => ({
    getAll: vi.fn().mockResolvedValue({
      data: [{ id: 1, question: "Q1" }],
    }),
  })),
}));

vi.mock("@/repositories/AnswerRepository", () => ({
  default: vi.fn().mockImplementation(() => ({
    getAll: vi.fn().mockResolvedValue({
      data: [
        { id: 1, answer: "A1", answerGroup: "G1" },
        { id: 2, answer: "A2", answerGroup: "G1" },
      ],
    }),
  })),
}));

vi.mock("react-select/async", () => ({
  __esModule: true,
  default: ({ loadOptions, onChange, value, placeholder, ...props }: any) => {
    return (
      <select
        data-testid={props.id}
        value={value?.value || ""}
        onChange={(e) => {
          const val = e.target.value;
          loadOptions("", (options: any[]) => {
            const option = options.find((o) => o.value == val);
            onChange(option);
          });
        }}
      >
        <option value="">{placeholder}</option>
        <option value="1">Option 1</option>
      </select>
    );
  },
}));

vi.mock("@/store/hooks", () => ({
  useAppSelector: vi.fn(() => ({
    token: "mock-token",
    refreshToken: "mock-refresh-token",
  })),
  useAppDispatch: () => vi.fn(),
}));

// ---------- Tests ----------

describe("ValueTypePage", () => {
  it("shows blocked UI when no category/question is selected", () => {
    const props = createProps({ formData: {} });
    renderWithProvider(<ValueTypePage {...props} />);
    expect(
      screen.getByText(/Select `Page for:` and `Question` to add section/i)
    ).toBeInTheDocument();
  });

  it("loads and selects a question from AsyncSelect", async () => {
    const setQuestion = vi.fn();
    renderWithProvider(<ValueTypePage {...createProps({ setQuestion })} />);
    await act(async () => {
      fireEvent.change(screen.getByTestId("question"), {
        target: { value: "1" },
      });
    });
    expect(setQuestion).toHaveBeenCalledWith({ label: "Q1", value: 1 });
  });

  it("adds new answer section", () => {
    const setAnswers = vi.fn();
    const props = createProps({
      question: { value: "q1", label: "Q1" },
      setAnswers,
    });
    renderWithProvider(<ValueTypePage {...props} />);
    fireEvent.click(screen.getByRole("button", { name: "Add Section" }));
    expect(setAnswers).toHaveBeenCalled();
    const [newAnswers] = setAnswers.mock.calls[0];
    expect(newAnswers.length).toBe(2);
    expect(newAnswers[0].answerGroup).toBe("mock-uuid");
  });

  it("renders 'No sections' if no answers", () => {
    renderWithProvider(<ValueTypePage {...createProps()} />);
    expect(screen.getByText("No sections added yet.")).toBeInTheDocument();
  });

  it("renders and updates grouped answers", async () => {
  const mockAnswers = [
    { uid: "u1", label: "Old A1", value: "", answerGroup: "G1", conditions: {} },
    { uid: "u2", label: "Old A2", value: "", answerGroup: "G1", conditions: {} },
  ];

  const setAnswers = vi.fn();
  const setAnswer = vi.fn();

  // Provide a valid answer for the delete dialog
  const answer = mockAnswers[0];

  renderWithProvider(
    <ValueTypePage
      {...createProps({
        answers: mockAnswers,
        setAnswers,
        answer,
        setAnswer,
      })}
    />
  );

  await act(async () => {
    fireEvent.change(screen.getByTestId("G1-0"), { target: { value: "1" } });
  });

  expect(setAnswers).toHaveBeenCalled();
});




});
