import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import AnswerForm from "../../../src/pages/therapist-pages/AnswerForm";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import userEvent from "@testing-library/user-event";
import "@testing-library/jest-dom";

// ✅ Hoisted mock API object
const mockApi = vi.hoisted(() => ({
  getAll: vi.fn(),
  
}));

// ✅ Mock the class-based PageRepository properly
vi.mock("@/repositories/PageRepository", () => {
  return {
    __esModule: true,
    default: vi.fn().mockImplementation(() => ({
      getAll: mockApi.getAll,
    })),
  };
});

const renderForm = (props = {}) => {
  const defaultState = { isOpen: true, close: vi.fn() };
  const defaultAnswer = undefined;
  const setAnswer = vi.fn();

  render(
    <MemoryRouter initialEntries={["/page/123"]}>
      <Routes>
        <Route
          path="/page/:pageId"
          element={
            <AnswerForm
              state={defaultState}
              answer={defaultAnswer}
              category={1}
              setAnswer={setAnswer}
              {...props}
            />
          }
        />
      </Routes>
    </MemoryRouter>
  );

  return { setAnswer, state: defaultState };
};

describe("AnswerForm", () => {
  beforeEach(() => {
    mockApi.getAll.mockReset();
    mockApi.getAll.mockResolvedValue({
      data: [
        { id: 1, code: "P1", title: "Page One" },
        { id: 2, code: "P2", title: "Page Two" },
      ],
    });
  });

  it("renders with checkboxes and text", () => {
    renderForm();
    expect(screen.getByText("Info Dialog")).toBeInTheDocument();
    expect(screen.getByText("Page Jump")).toBeInTheDocument();
    expect(screen.getByText(/Add conditions/i)).toBeInTheDocument();
  });

  it("shows title and info fields when Info Dialog is checked", async () => {
    renderForm();
    fireEvent.click(screen.getByText("Info Dialog"));
    expect(await screen.findByText("Title")).toBeInTheDocument();
    expect(await screen.findByLabelText("Info")).toBeInTheDocument();
  });

  it("selects a page from AsyncSelect", async () => {
    renderForm();
    fireEvent.click(screen.getByText("Page Jump"));
    const select = screen.getByRole("combobox");
    userEvent.type(select, "Page");

    await waitFor(() => {
      expect(mockApi.getAll).toHaveBeenCalledWith({
        search: "Page",
        perPage: 10,
        exclude: ["123"],
        category: 1,
      });
    });
  });

  it("shows page selector when Page Jump is checked", async () => {
    renderForm();
    fireEvent.click(screen.getByText("Page Jump"));
    expect(await screen.findByRole("combobox")).toBeInTheDocument();
  });

  it("calls setAnswer and closes on submit", async () => {
    const { setAnswer, state } = renderForm();
    fireEvent.click(screen.getByText("Info Dialog"));
    fireEvent.change(screen.getByLabelText(/Title/i), {
      target: { value: "My Title" },
    });
    fireEvent.change(screen.getByLabelText("Info"), {
      target: { value: "Info content" },
    });

    fireEvent.click(screen.getByText("Save"));

    await waitFor(() => {
      expect(setAnswer).toHaveBeenCalledWith({
        conditions: {
          info: {
            checked: true,
            title: "My Title",
            info: "Info content",
          },
          page: {
            checked: false,
            pageId: null,
            pageTitle: null,
          },
        },
      });
      expect(state.close).toHaveBeenCalled();
    });
  });

  it("resets form and closes on cancel", async () => {
    const { state } = renderForm();
    fireEvent.click(screen.getByText("Save")); // triggers reset
    fireEvent.click(screen.getByText("Cancel")); // triggers close
    expect(state.close).toHaveBeenCalled();
  });

  it("loads initial answer conditions on mount", async () => {
    const answer = {
      conditions: {
        info: { checked: true, title: "T", info: "I" },
        page: { checked: true, pageId: 9, pageTitle: "PT" },
      },
    };

    renderForm({ answer });

    expect(await screen.getByLabelText(/Title/i)).toHaveValue("T");
    expect(screen.getByLabelText("Info")).toHaveValue("I");
    expect(screen.getByText("PT")).toBeInTheDocument();
  });

  it("handles page load failure", async () => {
    mockApi.getAll.mockRejectedValueOnce(new Error("Fetch failed"));
    renderForm();
    fireEvent.click(screen.getByText("Page Jump"));
    const select = screen.getByRole("combobox");
    userEvent.type(select, "x");

    await waitFor(() => {
      expect(mockApi.getAll).toHaveBeenCalled();
    });
  });
});
