import { render, screen } from "@testing-library/react";
import Device<PERSON>enderer from "../../../src/pages/therapist-pages/DeviceRenderer";
import { describe, it, expect } from "vitest";
import "@testing-library/jest-dom"

const baseProps = {
  formData: {
    title: "Test Title",
    button: "Continue",
    type: { value: "" },
  },
  question: {
    label: "Question label?",
  },
  answers: [
    { label: "Option 1", answerGroup: "Group A" },
    { label: "Option 2", answerGroup: "Group A" },
    { label: "Option 3", answerGroup: "Group B" },
    { label: "Option 4", answerGroup: "Group B" },
  ],
};

describe("DeviceRenderer", () => {
  it("renders InfoSection correctly", () => {
    render(
      <DeviceRenderer
        {...baseProps}
        formData={{
          ...baseProps.formData,
          type: { value: "info" },
          title: "Info Title",
          info: "Some informational text",
          extra: "Extra Info",
        }}
      />
    );
    expect(screen.getByText("Info Title")).toBeInTheDocument();
    expect(screen.getByText("Some informational text")).toBeInTheDocument();
    expect(screen.getByText("Extra Info")).toBeInTheDocument();
    expect(screen.getByText("Continue")).toBeInTheDocument();
  });

  it("renders QuestionnaireSection correctly", () => {
    render(
      <DeviceRenderer
        {...baseProps}
        formData={{
          ...baseProps.formData,
          type: { value: "questionnaire" },
        }}
      />
    );
    expect(screen.getByText("Question label?")).toBeInTheDocument();
    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.getByText("Continue")).toBeInTheDocument();
  });

  it("renders ValueSection correctly with answer groups", () => {
    render(
      <DeviceRenderer
        {...baseProps}
        formData={{
          ...baseProps.formData,
          type: { value: "value" },
        }}
      />
    );

    // Headline
    expect(screen.getByText("Question label?")).toBeInTheDocument();

    // Group labels
    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();

    // Scale labels
    [
      "Most",
      "Slightly More",
      "Both Equally",
      "Slightly More",
      "Most",
    ].forEach((text) => {
      expect(screen.getAllByText(text)[0]).toBeInTheDocument();
    });

    // Button
    expect(screen.getByText("Continue")).toBeInTheDocument();
  });

  it("renders default fallback for unknown type", () => {
    render(
      <DeviceRenderer
        {...baseProps}
        formData={{
          ...baseProps.formData,
          type: { value: "registration" },
        }}
      />
    );
    expect(screen.getByText("Registration Page")).toBeInTheDocument();
    expect(screen.getByText("Preview not available")).toBeInTheDocument();
  });

  it("renders 'Page not selected' when type is undefined", () => {
    render(
      <DeviceRenderer
        {...baseProps}
        formData={{
          ...baseProps.formData,
          type: undefined,
        }}
      />
    );
    expect(screen.getByText("Page not selected")).toBeInTheDocument();
  });

  it("applies 'rounded' style when questionnaireType is 'multiple'", () => {
  render(
    <DeviceRenderer
      {...baseProps}
      formData={{
        ...baseProps.formData,
        type: { value: "questionnaire" },
        questionnaireType: { value: "multiple" },
      }}
    />
  );

  const roundedEl = screen.getAllByRole("presentation")[0]; // fallback since it's just a styled <div>
  expect(roundedEl.className.includes("rounded")).toBe(true);
  expect(roundedEl.className.includes("rounded-full")).toBe(false);
});

it("applies 'rounded-full' style when questionnaireType is not 'multiple'", () => {
  render(
    <DeviceRenderer
      {...baseProps}
      formData={{
        ...baseProps.formData,
        type: { value: "questionnaire" },
        questionnaireType: { value: "single" },
      }}
    />
  );

  const fullEl = screen.getAllByRole("presentation")[0];
  expect(fullEl.className.includes("rounded-full")).toBe(true);
  expect(fullEl.className.includes("rounded")).toBe(true);
});



});
