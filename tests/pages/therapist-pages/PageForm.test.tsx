import { describe, it, expect, vi, beforeEach } from 'vitest';
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import PageForm from '../../../src/pages/therapist-pages/PageForm';
import PageRepository from '../../../src/repositories/PageRepository';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import configureMockStore from 'redux-mock-store';
import * as ReactRouterDom from 'react-router-dom';

const mockStore = configureMockStore();
const store = mockStore({
  auth: {
    token: 'mock-token',
    refreshToken: 'mock-refresh',
  },
});

vi.mock('@/repositories/PageRepository');

vi.mock('react-intersection-observer', () => ({
  useInView: () => ({ ref: vi.fn(), inView: true }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
   useParams: vi.fn(() => ({ pageId: undefined })),
  };
});

vi.mock('react-select', async () => {
  const actual = await vi.importActual<typeof import('react-select')>('react-select');
  return {
    ...actual,
    default: (props) => (
      <select
        data-testid={props.id}
        value={props.value?.value || ''}
        onChange={(e) => {
          const selected = props.options.find(o => o.value === e.target.value);
          props.onChange(selected);
        }}
      >
        <option value="">--</option>
        {props.options.map(o => (
          <option key={o.value} value={o.value}>{o.label}</option>
        ))}
      </select>
    ),
    createFilter: actual.createFilter,
  };
});

vi.mock('react-select/async', () => ({
  __esModule: true,
  default: (props: any) => (
    <select
      data-testid="async-select"
      value={props.value?.value || ''}
      onChange={(e) => {
        const selected = [{ value: 'end', label: 'End Page' }].find(
          (o) => o.value === e.target.value
        );
        props.onChange(selected);
      }}
    >
      <option value="">--</option>
      <option value="end">End Page</option>
    </select>
  ),
}));

vi.mock('react-intersection-observer', () => ({
  useInView: () => ({ ref: vi.fn(), inView: true }),
}));

describe('PageForm', () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderForm = () =>
    render(
      <Provider store={store}>
        <BrowserRouter>
          <QueryClientProvider client={queryClient}>
            <PageForm />
          </QueryClientProvider>
        </BrowserRouter>
      </Provider>
    );

  it('renders initial form', () => {
    renderForm();
    expect(screen.getByText('Page Form')).toBeDefined();
    expect(screen.getByTestId('type')).toBeDefined();
    expect(screen.getByTestId('category')).toBeDefined();
  });

  it('shows errors when required fields missing', async () => {
    renderForm();
    fireEvent.click(screen.getByText('Save Page'));
    await waitFor(() => {
      expect(PageRepository.prototype.errorToast).toHaveBeenCalledWith('Please select page type.');
    });
  });

  it('sets form data from fetched data (nextPageId)', async () => {
    (ReactRouterDom.useParams as vi.Mock).mockReturnValue({ pageId: '101' });

    const mockData = {
      nextPageId: 999,
      nextPage: { code: 'NEXT', title: 'Next Title' },
      buttonAction: null,
      answers: [],
      questionnaireId: 1,
      code: 'PG1',
      title: 'Test Page',
      info: 'Some info',
      extra: '',
      button: 'Next',
      type: 'info',
      category: 'self',
      questionnaireType: 'single',
      questionnaire: { question: 'Q1', id: 1 },
    };

    vi.spyOn(PageRepository.prototype, 'getOne').mockResolvedValueOnce(mockData);

    renderForm();

    await waitFor(() => {
      expect(screen.getByDisplayValue('PG1')).toBeDefined();
      expect(screen.getByDisplayValue('Test Page')).toBeDefined();
    });
  });

  it('sets buttonClick to Register Page if buttonAction is register', async () => {
    (ReactRouterDom.useParams as vi.Mock).mockReturnValue({ pageId: '102' });

    const mockData = {
      buttonAction: 'register',
      answers: [],
      questionnaireId: 2,
      code: 'PG2',
      title: 'Reg Page',
      info: '',
      extra: '',
      button: 'Continue',
      type: 'info',
      category: 'self',
      questionnaireType: 'multiple',
    };

    vi.spyOn(PageRepository.prototype, 'getOne').mockResolvedValueOnce(mockData);

    renderForm();

    await waitFor(() => {
      expect(screen.getByDisplayValue('PG2')).toBeDefined();
    });
  });

  it('sets buttonClick to End Page if no nextPageId or register', async () => {
    (ReactRouterDom.useParams as vi.Mock).mockReturnValue({ pageId: '103' });

    const mockData = {
      buttonAction: '',
      answers: [],
      questionnaireId: 3,
      code: 'PG3',
      title: 'End Page',
      info: '',
      extra: '',
      button: 'Done',
      type: 'info',
      category: 'self',
      questionnaireType: 'single',
    };

    vi.spyOn(PageRepository.prototype, 'getOne').mockResolvedValueOnce(mockData);

    renderForm();

    await waitFor(() => {
      expect(screen.getByDisplayValue('PG3')).toBeDefined();
    });
  });
});

describe('PageForm Validation Logic', () => {
  const queryClient = new QueryClient();

  const renderForm = () =>
    render(
      <Provider store={store}>
        <BrowserRouter>
          <QueryClientProvider client={queryClient}>
            <PageForm />
          </QueryClientProvider>
        </BrowserRouter>
      </Provider>
    );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('shows error when category is missing', async () => {
    renderForm();
    fireEvent.change(screen.getByTestId('type'), { target: { value: 'info' } });
    fireEvent.click(screen.getByText('Save Page'));

    await waitFor(() => {
      expect(PageRepository.prototype.errorToast).toHaveBeenCalledWith('Please select page category.');
    });
  });

  it('shows error when question is missing for questionnaire type', async () => {
    renderForm();
    fireEvent.change(screen.getByTestId('type'), { target: { value: 'questionnaire' } });
    fireEvent.change(screen.getByTestId('category'), { target: { value: 'self' } });
    fireEvent.click(screen.getByText('Save Page'));

    await waitFor(() => {
      expect(PageRepository.prototype.errorToast).toHaveBeenCalledWith('Please select question.');
    });
  });

  it('shows error when questionnaire type is missing', async () => {
    renderForm();

    // simulate question manually
    (screen.getByText('Save Page') as HTMLButtonElement).onclick = () => {
      (document as any).question = { label: 'Q1', value: 1 };
    };

    fireEvent.change(screen.getByTestId('type'), { target: { value: 'questionnaire' } });
    fireEvent.change(screen.getByTestId('category'), { target: { value: 'self' } });

    await waitFor(() => {
      fireEvent.click(screen.getByText('Save Page'));
      expect(PageRepository.prototype.errorToast).toHaveBeenCalledWith('Please select question.');
    });
  });

  it('shows error when no answers are selected', async () => {
    renderForm();

    // simulate question & type manually
    (document as any).question = { label: 'Q1', value: 1 };

    fireEvent.change(screen.getByTestId('type'), { target: { value: 'questionnaire' } });
    fireEvent.change(screen.getByTestId('category'), { target: { value: 'self' } });

    await waitFor(() => {
      fireEvent.click(screen.getByText('Save Page'));
      expect(PageRepository.prototype.errorToast).toHaveBeenCalledWith('Please select question.');
    });
  });




});
