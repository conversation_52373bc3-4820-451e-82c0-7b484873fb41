import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import PageListPage from "../../../src/pages/therapist-pages/PageListPage";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useInfiniteQuery } from "@tanstack/react-query";
import { MemoryRouter } from "react-router-dom";
import PageRepository from "../../../src/repositories/PageRepository";
import "@testing-library/jest-dom"; 
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";

vi.mock("@/repositories/PageRepository");
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});
vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual("@tanstack/react-query");
  return {
    ...actual,
    useInfiniteQuery: vi.fn(),
  };
});
vi.mock("react-intersection-observer", () => ({
  useInView: () => ({ ref: vi.fn(), inView: true }),
}));
const authReducer = (state = { token: "mock-token", refreshToken: "mock-refresh" }, _action: any) => state;

export const renderWithProviders = (ui: React.ReactElement, { preloadedState = {}, store } = {}) => {
  const testStore =
    store ||
    configureStore({
      reducer: { auth: authReducer },
      preloadedState,
    });

  return render(<Provider store={testStore}><MemoryRouter>{ui}</MemoryRouter></Provider>);
};


describe("PageListPage", () => {
  const mockRefetch = vi.fn();
  const mockStartPage = vi.fn().mockResolvedValue(true);
  const mockPages = [
    {
      id: 1,
      code: "P001",
      title: "Test Page",
      type: "info",
      category: "self",
      initialPage: false,
    },
  ];

  beforeEach(() => {
    (useInfiniteQuery as any).mockReturnValue({
      data: {
        pages: [
          {
            data: mockPages,
            meta: {
              nextPage: null,
            },
          },
        ],
      },
      isLoading: false,
      fetchNextPage: vi.fn(),
      error: null,
      refetch: mockRefetch,
    });

    (PageRepository as any).mockImplementation(() => ({
      getAll: vi.fn(),
      startPage: mockStartPage,
    }));
  });

   it("shows error message if error occurs", () => {
    (useInfiniteQuery as any).mockReturnValue({
      error: { message: "Error loading" },
      isLoading: false,
      data: undefined,
      fetchNextPage: vi.fn(),
    });

   renderWithProviders(<PageListPage />);

    expect(screen.getByText(/Error:/)).toBeInTheDocument();
  });

  it("handles select change", () => {
   renderWithProviders(<PageListPage />);

    const select = screen.getByText("Select");
    fireEvent.keyDown(select, { key: "ArrowDown" });
  });

  it("handles search input", async () => {
   renderWithProviders(<PageListPage />);

    const input = screen.getByPlaceholderText("Search...");
    fireEvent.change(input, { target: { value: "test" } });

    await waitFor(() =>
      expect(input).toHaveValue("test")
    );
  });

  it("renders page title and add button", () => {
   renderWithProviders(<PageListPage />);

    expect(screen.getByText("Manage Pages")).toBeInTheDocument();
    expect(screen.getByText("Add Page")).toBeInTheDocument();
  });

  it("renders search and category input", () => {
    renderWithProviders(<PageListPage />);

    expect(screen.getByPlaceholderText("Search...")).toBeInTheDocument();
    expect(screen.getByText("Select")).toBeInTheDocument();
  });

  it("renders page rows", () => {
   renderWithProviders(<PageListPage />);

    expect(screen.getByText("P001")).toBeInTheDocument();
    expect(screen.getByText("Test Page")).toBeInTheDocument();
    expect(screen.getByText("info")).toBeInTheDocument();
  });

  it("opens and cancels normal dialog", async () => {
    renderWithProviders(<PageListPage />);

    const checkbox = screen.getByText("Start Page");
    fireEvent.click(checkbox);

    await waitFor(() =>
      expect(screen.getByText("Set Start Page")).toBeInTheDocument()
    );

    fireEvent.click(screen.getByText("Cancel"));
    await waitFor(() =>
      expect(
        screen.queryByText("Set Start Page")
      ).not.toBeInTheDocument()
    );
  });

it("opens delete dialog", async () => {
  renderWithProviders(<PageListPage />);

  const gearButton = screen.getByRole("button", {
    name: (_, el) => el?.querySelector("i.fa-cogs") !== null,
  });
  fireEvent.click(gearButton);

  await waitFor(() =>
    expect(screen.getByTestId("delete-button")).toBeInTheDocument()
  );

  fireEvent.click(screen.getByTestId("delete-button"));

  await waitFor(() =>
    expect(
      screen.getByText((content) => content.includes("Delete Page"))
    ).toBeInTheDocument()
  );
});

});


