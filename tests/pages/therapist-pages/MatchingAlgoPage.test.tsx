import { describe, it, vi, beforeEach, expect } from 'vitest';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../../../src/store/slicers/auth.slicer'; 
import MatchingAlgoPage from '../../../src/pages/therapist-pages/MatchingAlgoPage';
import PageRepository from '../../../src/repositories/PageRepository';
import MatchingAlgoRepository from '../../../src/repositories/MatchingAlgoRepository';
import { useQuery } from '@tanstack/react-query';
import "@testing-library/jest-dom"

// Mocks
vi.mock('lodash', () => ({
  debounce: (fn: any) => fn,
}));

vi.mock('framer-motion', () => ({
  motion: { div: (props: any) => <div {...props} /> },
}));

vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query');
  return {
    ...actual,
    useQuery: vi.fn(),
  };
});

// Wrap with Redux Provider
function renderWithProviders(ui: React.ReactNode, preloadedState: any = {}) {
  const store = configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        token: 'test-token',
        refreshToken: 'test-refresh',
        ...preloadedState.auth,
      },
    },
  });

  return render(<Provider store={store}>{ui}</Provider>);
}

describe('MatchingAlgoPage', () => {
  let fakeTherapistPages: any[];
  let fakeMatchingAlgoData: any[];
  let createMatching: any;
  let getAllPage: any;
  let getOnePage: any;

  beforeEach(() => {
    fakeTherapistPages = [
      { id: 1, pageName: 'T1' },
      { id: 2, pageName: 'T2' },
    ];

    fakeMatchingAlgoData = [
      {
        therapistPageId: 1,
        therapistPage: { id: 1, pageName: 'T1' },
        category: 'child',
        patientPage: { id: 10, code: 'P1', title: 'Page1', questionnaireId: 100 },
        questionnaire: { id: 100, question: 'Q1' },
        answer: { id: 1000, answer: 'A1' },
      },
    ];


    createMatching = vi
      .spyOn(MatchingAlgoRepository.prototype, 'create')
      .mockResolvedValue(true)

    getAllPage = vi
      .spyOn(PageRepository.prototype, 'getAll')
      .mockResolvedValue({
        data: [{ id: 10, code: 'P1', title: 'Page1', questionnaireId: 100 }],
      });

    getOnePage = vi
      .spyOn(PageRepository.prototype, 'getOne')
      .mockResolvedValue({
        questionnaire: { id: 100, question: 'Q1' },
        answers: [{ id: 1000, answer: 'A1' }],
      });

    const mockedUseQuery = useQuery as unknown as vi.Mock;
    mockedUseQuery.mockImplementation(({ queryKey }) => {
      if (queryKey[0] === 'therapistPages')
        return { data: fakeTherapistPages, error: null };
      if (queryKey[0] === 'matchingAlgoResources')
        return { data: fakeMatchingAlgoData, error: null };
      return { data: null, error: null };
    });
  });

it('loads patient pages using AsyncSelect', async () => {
  renderWithProviders(<MatchingAlgoPage />);
  
  // Select the first AsyncSelect for patient-page
  const patientSelects = screen.getAllByTestId('patient-page-select');
  const input = patientSelects[0].querySelector('input')!;
  
  fireEvent.focus(input);
  await waitFor(() => expect(getAllPage).toHaveBeenCalled());
});

it('loads questions using patient page ID', async () => {
  renderWithProviders(<MatchingAlgoPage />);
  
  const questionSelects = screen.getAllByTestId('question-select');
  const input = questionSelects[0].querySelector('input')!;
  
  fireEvent.focus(input);
  await waitFor(() => expect(getOnePage).toHaveBeenCalled());
});


  it('loads answers using patient page ID', async () => {
    renderWithProviders(<MatchingAlgoPage />);
    const questionSelects = screen.getAllByTestId('answer-select');
    const input = questionSelects[0].querySelector('input')!;
  
    fireEvent.focus(input);
      await waitFor(() => expect(getOnePage).toHaveBeenCalled());
    });

  it('handles Save Data click and shows loading spinner', async () => {
    renderWithProviders(<MatchingAlgoPage />);
    const button = screen.getByRole('button', { name: /Save Data/i });
    fireEvent.click(button);

    await waitFor(() => expect(createMatching).toHaveBeenCalled());
    expect(button.querySelector('i')).toBeNull(); // spinner disappears after submission
  });

  it('renders error state when therapist query fails', async () => {
    const mockedUseQuery = useQuery as unknown as vi.Mock;
    mockedUseQuery.mockImplementation(({ queryKey }) => {
      if (queryKey[0] === 'therapistPages')
        return { data: null, error: new Error('Failed') };
      if (queryKey[0] === 'matchingAlgoResources')
        return { data: null, error: null };
      return { data: null, error: null };
    });

    renderWithProviders(<MatchingAlgoPage />);
    await waitFor(() =>
      expect(screen.getByText(/Error: /)).toBeInTheDocument()
    );
  });
});
