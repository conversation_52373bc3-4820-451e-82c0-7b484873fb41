import React from "react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent} from "@testing-library/react";
import AnswerListPage from "../../../src/pages/answers/AnswerListPage"; 
import * as reactQuery from "@tanstack/react-query";
import * as reactIntersectionObserver from "react-intersection-observer";
import * as reactStately from "react-stately";
import { debounce as lodashDebounce } from "lodash";
import * as lodash from "lodash";
import { configureStore } from "@reduxjs/toolkit";
import { Provider } from "react-redux";
// Create a simple mock reducer for your slice(s)
const mockReducer = (state = { auth: { token: "", refreshToken: "" } }) => state;

// Create a mock store with initial state as needed
const store = configureStore({ reducer: mockReducer });


vi.mock("@/repositories/AnswerRepository");
vi.mock("@tanstack/react-query");
vi.mock("react-intersection-observer");
vi.mock("react-stately");
vi.mock("lodash", () => ({
  debounce: vi.fn((fn) => fn),
}));

describe("AnswerListPage", () => {
  let useInfiniteQueryMock: any;
  let useInViewMock: any;
  let useOverlayTriggerStateMock: any;

  const mockAnswers = [
    { id: "1", answer: "Answer One" },
    { id: "2", answer: "Answer Two" },
  ];

  beforeEach(() => {
    useInfiniteQueryMock = vi.spyOn(reactQuery, "useInfiniteQuery");
    useInViewMock = vi.spyOn(reactIntersectionObserver, "useInView");
    useOverlayTriggerStateMock = vi.spyOn(reactStately, "useOverlayTriggerState");
     vi.spyOn(lodash, "debounce").mockImplementation((fn) => fn);

    // Mock useInView to return a ref callback
    useInViewMock.mockReturnValue({ ref: vi.fn() });

    // Mock overlay state: opened, closed toggle
    useOverlayTriggerStateMock.mockImplementation(() => {
      let isOpen = false;
      return {
        isOpen,
        open: vi.fn(() => {
          isOpen = true;
        }),
        close: vi.fn(() => {
          isOpen = false;
        }),
      };
    });

    // Default infinite query mock, no error, with data
    useInfiniteQueryMock.mockReturnValue({
      error: null,
      data: {
        pages: [
          { data: mockAnswers, meta: { nextPage: 2, totalCount: 2 } },
        ],
      },
      isLoading: false,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    // debounce calls the function immediately for test simplicity
    vi.mocked(lodashDebounce).mockImplementation((fn) => fn);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("renders correctly with data", () => {
    render(
      <Provider store={store}>
        <AnswerListPage />
      </Provider>
    );
    expect(screen.getByText("Answer List")).toBeDefined();

    // Check AddButton label
    expect(screen.getByText("Add Answer")).toBeDefined();

    // Check answers are rendered
    expect(screen.getByText("Answer One")).toBeDefined();
    expect(screen.getByText("Answer Two")).toBeDefined();
  });

  it("shows error message when error exists", () => {
    useInfiniteQueryMock.mockReturnValue({
      error: { message: "Test Error" },
      data: null,
      isLoading: false,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

   render(
      <Provider store={store}>
        <AnswerListPage />
      </Provider>
    );
    expect(screen.getByText(/Error:/)).toBeDefined();
    expect(screen.getByText(/Test Error/)).toBeDefined();
  });

  it("shows loading state", () => {
    useInfiniteQueryMock.mockReturnValue({
      error: null,
      data: null,
      isLoading: true,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });
    render(
      <Provider store={store}>
        <AnswerListPage />
      </Provider>
    );
    // PaginationAwareContainer will receive fetching = true
    // We test indirectly by checking component rendered without errors
    expect(screen.getByText("Answer List")).toBeDefined();
  });

  it("opens add answer dialog on AddButton press", () => {
    const openMock = vi.fn();
    useOverlayTriggerStateMock.mockReturnValue({
      isOpen: false,
      open: openMock,
      close: vi.fn(),
    });
   render(
      <Provider store={store}>
        <AnswerListPage />
      </Provider>
    );

    fireEvent.click(screen.getByText("Add Answer"));
    expect(openMock).toHaveBeenCalled();
  });

  it("calls debounce and sets search params on InputComponent change", async () => {
    vi.useFakeTimers();
   render(
      <Provider store={store}>
        <AnswerListPage />
      </Provider>
    );

    const input = screen.getByPlaceholderText("Search...");
    fireEvent.change(input, { target: { value: "query" } });

 
    vi.runAllTimers();
    vi.useRealTimers();
  });

  

});
