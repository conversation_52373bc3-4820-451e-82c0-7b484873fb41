import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import '@testing-library/jest-dom';
import AnswerForm from "../../../src/pages/answers/AnswerForm";
import { OverlayTriggerState } from "react-stately";
import { Answer } from "../../../src/types/answer.interface";

vi.mock("@/repositories/AnswerRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      create: vi.fn().mockResolvedValue({ id: 1 }),
      update: vi.fn().mockResolvedValue({ id: 1 }),
    })),
  };
});

const mockClose = vi.fn();
const mockRefetch = vi.fn();

const mockState = {
  close: mockClose,
  isOpen: true,
} as unknown as OverlayTriggerState;

describe("AnswerForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders form with empty fields for create", () => {
    render(<AnswerForm state={mockState} />);
    expect(screen.getByRole("textbox", { name: /answer/i })).toHaveValue("");
    expect(screen.getByRole("textbox", { name: /info/i })).toHaveValue("");
  });

  it("renders form with prefilled data for edit", () => {
    const mockAnswer: Answer = {
      id: 1,
      answer: "Existing answer",
      info: "Some info",
      pageAnswerId: 1,
      conditions: {} as JSON,
      answerGroup: "group_1",
      position: "0",
    };
    render(<AnswerForm state={mockState} answer={mockAnswer} refetch={mockRefetch} />);
    expect(screen.getByRole("textbox", { name: /answer/i })).toHaveValue("Existing answer");
    expect(screen.getByRole("textbox", { name: /info/i })).toHaveValue("Some info");
  });

  it("calls onCancel when cancelled", () => {
    render(<AnswerForm state={mockState} />);
    const cancelButton = screen.getByRole("button", { name: /cancel/i });
    fireEvent.click(cancelButton);
    expect(mockClose).toHaveBeenCalled();
  });


});

