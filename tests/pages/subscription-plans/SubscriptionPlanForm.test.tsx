import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import SubscriptionPlanForm from "../../../src/pages/subscription-plans/SubscriptionPlanForm";
import { Provider } from "react-redux";
import configureS<PERSON> from "redux-mock-store";
import thunk from "redux-thunk";
import "@testing-library/jest-dom"

// Mocks
vi.mock("@/components/dialogs/FormDialog", () => ({
  default: ({ children, onCancel, onSubmit, confirmLabel, loading, disabled }: any) => (
    <form onSubmit={onSubmit}>
      <button type="button" onClick={onCancel}>Cancel</button>
      {children}
      <button type="submit" disabled={disabled}>{confirmLabel}</button>
      {loading && <div>Loading...</div>}
    </form>
  ),
}));

vi.mock("@/components/forms/InputComponent", () => ({
  default: ({ label, id, onChange, value, errorMessage }: any) => (
    <div>
      <label htmlFor={id}>{label}</label>
      <input
        id={id}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      {errorMessage && <span>{errorMessage}</span>}
    </div>
  ),
}));

vi.mock("@/store/slicers/admin.slicer", () => ({
  createSubscriptionPlanAction: vi.fn(() => () => Promise.resolve({ status: 201 })),
  updateSubscriptionPlanAction: vi.fn(() => () => Promise.resolve({ status: 200 })),
}));

vi.mock("@/utils/app.util", () => ({
  validatePrice: vi.fn((val) => (val && Number(val) > 0 ? "" : "Invalid price")),
}));

// Test setup
const mockStore = configureStore([thunk]);
const mockClose = vi.fn();
const mockRefetch = vi.fn();

const renderComponent = (plan?: any) => {
  const store = mockStore({});
  return render(
    <Provider store={store}>
      <SubscriptionPlanForm
        state={{ close: mockClose, isOpen: true } as any}
        plan={plan}
        refetch={mockRefetch}
      />
    </Provider>
  );
};

vi.mock("@/utils/app.util", () => ({
  validatePrice: vi.fn((val) => (val && Number(val) > 0 ? "" : "Invalid price")),
}));

describe("SubscriptionPlanForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders with empty form when no plan is provided", () => {
    renderComponent();
    expect(screen.getByLabelText("Annual Price")).toBeInTheDocument();
    expect(screen.getByLabelText("Monthly Price")).toBeInTheDocument();
    expect(screen.getByText("Create")).toBeInTheDocument();
  });

  it("renders with plan data when plan is provided", () => {
    renderComponent({ id: "1", annualPrice: 500, monthlyPrice: 50 });
    expect(screen.getByDisplayValue("500")).toBeInTheDocument();
    expect(screen.getByDisplayValue("50")).toBeInTheDocument();
    expect(screen.getByText("Update")).toBeInTheDocument();
  });

  it("shows validation errors and prevents submission", async () => {
    renderComponent();

    fireEvent.change(screen.getByLabelText("Annual Price"), { target: { value: "-1" } });
    fireEvent.change(screen.getByLabelText("Monthly Price"), { target: { value: "0" } });

    fireEvent.click(screen.getByText("Create"));

    await waitFor(() => {
      expect(screen.getAllByText("Invalid price").length).toBe(2);
    });
  });

  it("dispatches createSubscriptionPlanAction on valid submit", async () => {
    const { createSubscriptionPlanAction } = await import("../../../src/store/slicers/admin.slicer");
    renderComponent();

    fireEvent.change(screen.getByLabelText("Annual Price"), { target: { value: "100" } });
    fireEvent.change(screen.getByLabelText("Monthly Price"), { target: { value: "10" } });

    fireEvent.click(screen.getByText("Create"));

    await waitFor(() => {
      expect(createSubscriptionPlanAction).toHaveBeenCalledWith({ annualPrice: 100, monthlyPrice: 10 });
      expect(mockRefetch).toHaveBeenCalled();
      expect(mockClose).toHaveBeenCalled();
    });
  });

  it("dispatches updateSubscriptionPlanAction when editing", async () => {
    const { updateSubscriptionPlanAction } = await import("../../../src/store/slicers/admin.slicer");

    renderComponent({ id: "abc", annualPrice: 200, monthlyPrice: 20 });

    fireEvent.change(screen.getByLabelText("Annual Price"), { target: { value: "300" } });
    fireEvent.change(screen.getByLabelText("Monthly Price"), { target: { value: "30" } });

    fireEvent.click(screen.getByText("Update"));

    await waitFor(() => {
      expect(updateSubscriptionPlanAction).toHaveBeenCalledWith({ annualPrice: 300, monthlyPrice: 30 }, "abc");
      expect(mockRefetch).toHaveBeenCalled();
      expect(mockClose).toHaveBeenCalled();
    });
  });

  it("resets form and closes on cancel", () => {
    renderComponent({ id: "1", annualPrice: 500, monthlyPrice: 50 });
    fireEvent.click(screen.getByText("Cancel"));
    expect(mockClose).toHaveBeenCalled();
  });




});
