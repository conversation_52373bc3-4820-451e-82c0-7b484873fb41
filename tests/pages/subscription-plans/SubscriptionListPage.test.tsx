import { render, screen, fireEvent } from "@testing-library/react";
import SubscriptionListPage from "../../../src/pages/subscription-plans/SubscriptionListPage";
import { beforeEach, describe, expect, it, vi } from "vitest";
import dayjs from "dayjs";
import "@testing-library/jest-dom"

// Mock Redux
const dispatch = vi.fn();

// Mock overlay trigger state
const mockState = {
  isOpen: false,
  open: vi.fn(),
  close: vi.fn(),
};


// Sample data
const samplePlan = {
  id: "1",
  annualPrice: 100,
  monthlyPrice: 10,
  isActive: false,
  createdAt: dayjs().subtract(1, "day").toISOString(),
};

const mockData = {
  pages: [
    {
      data: [samplePlan],
      meta: { nextPage: null },
    },
  ],
};

vi.mock("@/store/slicers/admin.slicer", async () => {
  const actual = await vi.importActual<any>("@/store/slicers/admin.slicer");
  return {
    ...actual,
    fetchSubscriptionPlansAction: vi.fn(() => Promise.resolve(mockData)),
    deleteSubscriptionPlanAction: vi.fn(() => Promise.resolve({ status: 200 })),
    activateSubscriptionPlanAction: vi.fn(() => Promise.resolve({ status: 200 })),
  };
});

vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual<any>("@tanstack/react-query");
  return {
    ...actual,
    useInfiniteQuery: vi.fn(() => ({
      error: null,
      data: mockData,
      isLoading: false,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    })),
  };
});

vi.mock("react-redux", async () => {
  const actual = await vi.importActual<any>("react-redux");
  return {
    ...actual,
    useDispatch: () => dispatch,
  };
});

vi.mock("react-stately", async () => {
  const actual = await vi.importActual<any>("react-stately");
  return {
    ...actual,
    useOverlayTriggerState: () => mockState,
  };
});

describe("SubscriptionListPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders the subscription list page correctly", () => {
    render(<SubscriptionListPage />);
    expect(screen.getByText("Subscription Plan List")).toBeInTheDocument();
    expect(screen.getByText("Annual Price")).toBeInTheDocument();
    expect(screen.getByText("$ 100")).toBeInTheDocument();
  });

  it("opens the add subscription form dialog", () => {
    render(<SubscriptionListPage />);
    const addButton = screen.getByText("Add Subscription Plan");
    fireEvent.click(addButton);
    expect(mockState.open).toHaveBeenCalled();
  });

  it("calls edit and opens the dialog", async () => {
    render(<SubscriptionListPage />);
    const editButton = screen.getAllByRole("button")[0];
    fireEvent.click(editButton);
    expect(mockState.open).toHaveBeenCalled();
  });

  
});
