import { render } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import "@testing-library/jest-dom";
import LoadingPage from "../../src/pages/LoadingPage";
import React from "react";


describe("LoadingPage Component", () => {
  it("should render without crashing", () => {
    const { container } = render(<LoadingPage />);
    expect(container).toBeDefined();
  });

  it("should render a div with the correct class names", () => {
    const { container } = render(<LoadingPage />);
    const divElement = container.querySelector("div");
    expect(divElement).toHaveClass("h-full w-full flex flex-row");
  });

  it("should render a main element with the correct class names", () => {
    const { container } = render(<LoadingPage />);
    const mainElement = container.querySelector("main");
    expect(mainElement).toHaveClass(
      "relative flex-grow bg-gray-100 bg-opacity-50 flex flex-col"
    );
  });
});