import { render } from "@testing-library/react";
import { describe, it, expect, beforeAll } from "vitest";
import "@testing-library/jest-dom";
import Setting from "../../src/pages/setting.page";
import React from "react";


beforeAll(() => {
    global.IntersectionObserver = class IntersectionObserver {
      root = null;
      rootMargin = "";
      thresholds = [];
      observe() {}
      unobserve() {}
      disconnect() {}
      takeRecords() {
        return [];
      }
    };
  });

describe("Setting Component", () => {
  it("should render without crashing", () => {
    const { container } = render(<Setting />);
    expect(container).toBeDefined();
  });

  it("should render the outer div with the correct class names", () => {
    const { container } = render(<Setting />);
    const outerDiv = container.querySelector("div");
    expect(outerDiv).toHaveClass("pr-3 pt-8 pb-4 px-16 h-full flex flex-col flex-grow");
  });

  it("should render the 'Settings' text", () => {
    const { getByText } = render(<Setting />);
    const settingsText = getByText("Settings");
    expect(settingsText).toBeInTheDocument();
    expect(settingsText).toHaveClass("font-thin text-3xl");
  });

  it("should render the child div with correct structure", () => {
    const { container } = render(<Setting />);
    const childDiv = container.querySelector(".flex-none.flex.flex-row.justify-between.items-center.mb-4");
    expect(childDiv).toBeInTheDocument();
    expect(childDiv?.querySelector(".font-thin.text-3xl")).toBeInTheDocument();
    expect(childDiv?.querySelector(".flex.flex-row.space-x-2")).toBeInTheDocument();
  });
});