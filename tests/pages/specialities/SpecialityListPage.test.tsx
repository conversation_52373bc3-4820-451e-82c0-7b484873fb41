import { render, screen, fireEvent } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { Provider } from "react-redux";
import configureStore from "redux-mock-store";

// ✅ Mock store and state
const mockStore = configureStore();
const store = mockStore({
  auth: {
    token: "test-token",
    refreshToken: "test-refresh",
  },
});

// ✅ Mocks BEFORE imports
const open = vi.fn();
const close = vi.fn();

vi.mock("react-stately", () => ({
  useOverlayTriggerState: () => ({
    isOpen: false,
    open,
    close,
    toggle: vi.fn(),
    setOpen: vi.fn(),
  }),
}));

vi.mock("react-intersection-observer", () => ({
  useInView: () => ({ ref: vi.fn() }),
}));

vi.mock("@tanstack/react-query", () => ({
 useInfiniteQuery: vi.fn(({ queryFn }) => {
  queryFn({ pageParam: 2 }); // forces line coverage for pageParam
  return {
    data: {
      pages: [
        {
          data: [
            { id: 1, name: "Cardiology", description: "Heart" },
            { id: 2, name: "Neurology", description: "Brain" },
          ],
          meta: { nextPage: 3 },
        },
      ],
    },
    fetchNextPage: vi.fn(),
    refetch: vi.fn(),
    error: null,
    isLoading: false,
    hasNextPage: true,
    isFetchingNextPage: false,
    status: "success",
    isSuccess: true,
    isError: false,
    isPending: false,
    isFetched: true,
    isStale: false,
    fetchStatus: "idle",
  };
}),

}));

vi.mock("@/components/buttons/AddButton", () => ({
  __esModule: true,
  default: ({ onPress }: { onPress: () => void }) => (
    <button onClick={onPress}>Add Speciality</button>
  ),
}));

vi.mock("@/components/buttons/ActionButton", () => ({
  __esModule: true,
  default: ({ onEdit, onDelete }: { onEdit: () => void; onDelete: () => void }) => (
    <>
      <button onClick={onEdit}>Edit</button>
      <button onClick={onDelete}>Delete</button>
    </>
  ),
}));

vi.mock("@/components/dialogs/DeleteDialog", () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock("@/components/PaginationAwareContainer", () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock("@/components/PaginatedDataContainer", () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock("@/pages/specialities/SpecialityForm", () => ({
  __esModule: true,
  default: () => <div>Speciality Form</div>,
}));

// ✅ AFTER mocks
import SpecialityListPage from "../../../src/pages/specialities/SpecialityListPage";

describe("SpecialityListPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderWithProvider = () =>
    render(
      <Provider store={store}>
        <SpecialityListPage />
      </Provider>
    );

  it("renders list of specialities", () => {
    renderWithProvider();
    expect(screen.getByText("Speciality List")).toBeInTheDocument();
    expect(screen.getByText("Cardiology")).toBeInTheDocument();
    expect(screen.getByText("Neurology")).toBeInTheDocument();
  });

  it("opens form on Add Speciality", () => {
    renderWithProvider();
    fireEvent.click(screen.getByText("Add Speciality"));
    expect(open).toHaveBeenCalled();
  });

it("calls edit and delete handlers", () => {
  renderWithProvider();
  const editButtons = screen.getAllByText("Edit");
  const deleteButtons = screen.getAllByText("Delete");

  fireEvent.click(editButtons[0]); 
  fireEvent.click(deleteButtons[0]);

  expect(open).toHaveBeenCalledTimes(2);
});

it("resets speciality after closing the form", () => {
  vi.mock("react-stately", () => ({
    useOverlayTriggerState: () => ({
      isOpen: true,
      open,
      close,
      toggle: vi.fn(),
      setOpen: vi.fn(),
    }),
  }));

 
  renderWithProvider();
});


});
