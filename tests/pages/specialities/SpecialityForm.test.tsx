import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import SpecialityForm from "../../../src/pages/specialities/SpecialityForm";
import { Speciality } from "../../../src/types/speciality.interface";
import "@testing-library/jest-dom"
import { OverlayTriggerState } from "react-stately";

describe("SpecialityForm", () => {
  const mockClose = vi.fn();

  const mockState: OverlayTriggerState = {
    isOpen: true,
    close: mockClose,
    open: vi.fn(),
    toggle: vi.fn(),
    setOpen: vi.fn(),
  };
  const mockRefetch = vi.fn();
  const mockCreate = vi.fn();
  const mockUpdate = vi.fn();

  const repo = {
    create: mockCreate,
    update: mockUpdate,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders empty form in create mode", () => {
    render(<SpecialityForm state={mockState} repo={repo} />);
   expect(screen.getByLabelText(/Name/i)).toHaveValue("");
    expect(screen.getByLabelText("Description")).toHaveValue("");
  });

  it("renders form with data in edit mode", () => {
    const speciality: Speciality = {
      id: 123,
      name: "Dermatology",
      description: "Skin specialist",
    };

    render(
      <SpecialityForm
        state={mockState}
        repo={repo}
        speciality={speciality}
        refetch={mockRefetch}
      />
    );

    expect(screen.getByLabelText(/Name/i)).toHaveValue("Dermatology");
    expect(screen.getByLabelText("Description")).toHaveValue("Skin specialist");
  });

  it("submits form in create mode", async () => {
    mockCreate.mockResolvedValue({ id: "new" });

    render(<SpecialityForm state={mockState} repo={repo} refetch={mockRefetch} />);

    fireEvent.change(screen.getByLabelText(/Name/i), { target: { value: "Cardiology" } });
    fireEvent.change(screen.getByLabelText("Description"), {
      target: { value: "Heart specialist" },
    });

    fireEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockCreate).toHaveBeenCalledWith({
        name: "Cardiology",
        description: "Heart specialist",
      });
    });

    expect(mockRefetch).toHaveBeenCalled();
    expect(mockClose).toHaveBeenCalled();
  });

  it("submits form in update mode", async () => {
    const speciality: Speciality = {
      id: 321,
      name: "Neuro",
      description: "Old Desc",
    };
    mockUpdate.mockResolvedValue({});

    render(
      <SpecialityForm
        state={mockState}
        repo={repo}
        speciality={speciality}
        refetch={mockRefetch}
      />
    );

    fireEvent.change(screen.getByLabelText("Description"), {
      target: { value: "Updated Desc" },
    });

   fireEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockUpdate).toHaveBeenCalledWith(321, {
        name: "Neuro",
        description: "Updated Desc",
      });
    });

    expect(mockRefetch).toHaveBeenCalled();
    expect(mockClose).toHaveBeenCalled();
  });

  it("handles cancel", () => {
    render(<SpecialityForm state={mockState} repo={repo} />);

   fireEvent.click(screen.getByRole("button", { name: /cancel/i }));

    expect(mockClose).toHaveBeenCalled();
  });

  it("does not refetch or close if submit fails", async () => {
    mockCreate.mockResolvedValue(null);

    render(<SpecialityForm state={mockState} repo={repo} refetch={mockRefetch} />);

    fireEvent.change(screen.getByLabelText(/Name/i), { target: { value: "Test" } });
    fireEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockCreate).toHaveBeenCalled();
    });

    expect(mockRefetch).not.toHaveBeenCalled();
    expect(mockClose).not.toHaveBeenCalled();
  });
});
