import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, vi, beforeEach, expect } from "vitest";
import EventListPage from "../../../src/pages/events/EventListPage";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useOverlayTriggerState } from "react-stately";
import '@testing-library/jest-dom'

vi.mock("@/repositories/EventRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getAll: vi.fn().mockResolvedValue({
        data: [
          { id: 1, name: "Event One", description: "First Event" },
          { id: 2, name: "Event Two", description: "Second Event" },
        ],
        meta: { nextPage: null },
      }),
    })),
  };
});

vi.mock("@/components/buttons/AddButton", () => ({
  default: ({ label, onPress }: any) => (
    <button onClick={onPress}>{label}</button>
  ),
}));
vi.mock("@/components/buttons/EditButton", () => ({
  default: ({ label, onPress }: any) => (
    <button onClick={onPress}>{label}</button>
  ),
}));
vi.mock("@/components/buttons/DeleteButton", () => ({
  default: ({ label, onPress }: any) => (
    <button onClick={onPress}>{label}</button>
  ),
}));
vi.mock("@/components/dialogs/DeleteDialog", () => ({
  default: ({ state, children }: any) => state.isOpen ? <div>{children}</div> : null,
}));
vi.mock("./EventForm", () => ({
  default: ({ state }: any) => state.isOpen ? <div>Event Form</div> : null,
}));
vi.mock("@/components/PaginationAwareContainer", () => ({
  default: ({ children }: any) => <div>{children}</div>,
}));
vi.mock("@/components/PaginatedDataContainer", () => ({
  default: ({ children }: any) => <div>{children}</div>,
}));
vi.mock("react-intersection-observer", () => ({
  useInView: vi.fn(() => ({ ref: vi.fn(), inView: true })),
}));
vi.mock("react-stately", () => ({
  useOverlayTriggerState: vi.fn(() => ({
    isOpen: false,
    open: vi.fn(),
    close: vi.fn(),
  })),
}));

vi.mock("framer-motion", async () => {
  const mod = await vi.importActual<any>("framer-motion");
  return {
    ...mod,
    motion: {
      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    },
  };
});

vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual<any>("@tanstack/react-query");
  return {
    ...actual,
    useInfiniteQuery: vi.fn(),
  };
});

describe("EventListPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders loading state", () => {
    (useInfiniteQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    render(<EventListPage />);
    expect(screen.getByText("Event List")).toBeInTheDocument();
  });

  it("renders error state", () => {
    (useInfiniteQuery as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error("Something went wrong"),
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    render(<EventListPage />);
    expect(screen.getByText(/Error:/)).toBeInTheDocument();
  });

  it("renders list of events", () => {
    (useInfiniteQuery as any).mockReturnValue({
      data: {
        pages: [
          {
            data: [
              { id: 1, name: "Event One", description: "First Event" },
              { id: 2, name: "Event Two", description: "Second Event" },
            ],
            meta: { nextPage: null },
          },
        ],
      },
      isLoading: false,
      error: null,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    render(<EventListPage />);
    expect(screen.getByText("Event One")).toBeInTheDocument();
    expect(screen.getByText("Second Event")).toBeInTheDocument();
  });

  it("opens add event dialog", async () => {
    const openMock = vi.fn();
    (useOverlayTriggerState as any).mockReturnValueOnce({
      isOpen: false,
      open: openMock,
      close: vi.fn(),
    }).mockReturnValueOnce({
      isOpen: false,
      open: vi.fn(),
      close: vi.fn(),
    });

    (useInfiniteQuery as any).mockReturnValue({
      data: {
        pages: [],
      },
      isLoading: false,
      error: null,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    render(<EventListPage />);
    const addButton = screen.getByText("Add Event");
    fireEvent.click(addButton);
    expect(openMock).toHaveBeenCalled();
  });

  it("opens edit and delete dialogs", () => {
    const openMock = vi.fn();
    (useOverlayTriggerState as any).mockReturnValueOnce({
      isOpen: false,
      open: openMock,
      close: vi.fn(),
    }).mockReturnValueOnce({
      isOpen: false,
      open: openMock,
      close: vi.fn(),
    });

    (useInfiniteQuery as any).mockReturnValue({
      data: {
        pages: [
          {
            data: [{ id: 1, name: "Event One", description: "First Event" }],
            meta: { nextPage: null },
          },
        ],
      },
      isLoading: false,
      error: null,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    render(<EventListPage />);
    fireEvent.click(screen.getByText("Edit"));
    fireEvent.click(screen.getByText("Delete"));

    expect(openMock).toHaveBeenCalledTimes(2);
  });
});
