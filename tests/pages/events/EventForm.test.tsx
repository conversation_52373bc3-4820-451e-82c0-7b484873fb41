import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import EventForm from '../../../src/pages/events/EventForm';
import EventRepository from '../../../src/repositories/EventRepository';
import { OverlayTriggerState } from 'react-stately';
import '@testing-library/jest-dom'

vi.mock('cleave.js/react', async () => {
  const React = await import('react');
  return {
    default: React.forwardRef((props: any, ref) => (
      <input ref={ref} {...props} />
    )),
  };
});


const mockClose = vi.fn();
const mockRefetch = vi.fn();

const createOverlayState = (): OverlayTriggerState => ({
  isOpen: true,
  open: vi.fn(),
  close: mockClose,
  setOpen: vi.fn(),
  toggle: vi.fn(),
});

const renderComponent = (props?: Partial<React.ComponentProps<typeof EventForm>>) => {
  const repo = {
    create: vi.fn().mockResolvedValue({ id: 1 }),
    update: vi.fn().mockResolvedValue({ id: 1 }),
  } as unknown as EventRepository;

  const defaultProps = {
    state: createOverlayState(),
    repo,
    refetch: mockRefetch,
    ...props,
  };

  return {
    ...render(<EventForm {...defaultProps} />),
    repo: defaultProps.repo,
  };
};

describe('EventForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all form fields for creating an event', () => {
    renderComponent();
    expect(screen.getByLabelText(/Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Location/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Start Date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/End Date/i)).toBeInTheDocument();
    expect(screen.getByText(/Is all day\?/i)).toBeInTheDocument();
    expect(screen.getByText(/Is recurring\?/i)).toBeInTheDocument();
  });

  it('fills form and submits for create', async () => {
    const { repo } = renderComponent();

    fireEvent.change(screen.getByLabelText(/Name/i), { target: { value: 'Event A' } });
    fireEvent.change(screen.getByLabelText(/Location/i), { target: { value: 'Delhi' } });
    fireEvent.change(screen.getByLabelText(/Description/i), { target: { value: 'Description here' } });
    fireEvent.change(screen.getByLabelText(/Start Date/i), { target: { value: '2025-06-20T10:00' } });
    fireEvent.change(screen.getByLabelText(/End Date/i), { target: { value: '2025-06-20T12:00' } });
    fireEvent.click(screen.getByText(/Is all day\?/i));
    fireEvent.click(screen.getByText(/Is recurring\?/i));

    fireEvent.click(screen.getByRole('button', { name: /save/i }));

    await waitFor(() => {
      expect(repo.create).toHaveBeenCalledWith({
        name: 'Event A',
        location: 'Delhi',
        description: 'Description here',
        startDate: '2025-06-20T10:00',
        endDate: '2025-06-20T12:00',
        isAllDay: true,
        isRecurring: true,
      });
    });

    expect(mockClose).toHaveBeenCalled();
    expect(mockRefetch).toHaveBeenCalled();
  });

  it('fills form and submits for update', async () => {
    const event = {
      id: 123,
      name: 'Existing Event',
      location: 'Mumbai',
      description: 'Old desc',
      startDate: '2025-06-20T10:00',
      endDate: '2025-06-20T12:00',
      isAllDay: false,
      isRecurring: true,
    };

    const { repo } = renderComponent({ event });

    await waitFor(() => {
      expect(screen.getByDisplayValue('Existing Event')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByLabelText(/Description/i), { target: { value: 'Updated desc' } });

    fireEvent.click(screen.getByRole('button', { name: /save/i }));

    await waitFor(() => {
      expect(repo.update).toHaveBeenCalledWith(123, {
        name: 'Existing Event',
        location: 'Mumbai',
        description: 'Updated desc',
        startDate: '2025-06-20T10:00',
        endDate: '2025-06-20T12:00',
        isAllDay: false,
        isRecurring: true,
      });
    });

    expect(mockClose).toHaveBeenCalled();
    expect(mockRefetch).toHaveBeenCalled();
  });

  it('calls close and resets form on cancel', () => {
    renderComponent();
    const cancelButton = screen.getByText(/Cancel/i);
    fireEvent.click(cancelButton);
    expect(mockClose).toHaveBeenCalled();
  });
});
