import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import LoginPage from "../../../src/pages/auth/login.page";
import { useApiClient } from "../../../src/utils/api.util";
import { HttpStatusCode } from "axios";
import "@testing-library/jest-dom";
import { describe, it, expect, beforeEach, vi } from "vitest";

// Mock modules
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

// Mock react-hot-toast with proper default export and methods
vi.mock("react-hot-toast", () => {
  const mockToast = {
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  };
  return {
    ...mockToast,
    default: mockToast, // This is crucial - providing the default export
  };
});

// Mock react-router-dom navigation
const mockNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock notification util if needed
vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("LoginPage", () => {
  const mockClient = {
    post: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useApiClient as any).mockReturnValue(mockClient);
    mockNavigate.mockClear();
    mockClient.post.mockClear();
  });

  const renderComponent = (isAuthenticated = false) => {
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated }),
        therapist: () => ({}),
        sidebar: () => ({}),
      },
    });

    return render(
      <Provider store={store}>
        <BrowserRouter>
          <LoginPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it("renders the login form correctly", () => {
    renderComponent();
    expect(screen.getByText("Get started today.")).toBeInTheDocument();
    expect(screen.getByText("Empathy, Support, and Healing—Right at Your Fingertips")).toBeInTheDocument();
    expect(screen.getByLabelText("Login Details")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("your email address")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("your password")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Sign In" })).toBeInTheDocument();
  });

  it("redirects to dashboard if user is already authenticated", () => {
    renderComponent(true);
    expect(mockNavigate).not.toHaveBeenCalled(); // The Navigate component is used instead of navigate function
  });

  it("focuses on email input when component mounts", () => {
    renderComponent();
    expect(document.activeElement).toBe(screen.getByPlaceholderText("your email address"));
  });

  it("updates the credential state when inputs change", () => {
    renderComponent();
    
    const emailInput = screen.getByPlaceholderText("your email address");
    const passwordInput = screen.getByPlaceholderText("your password");
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    
    expect(emailInput).toHaveValue("<EMAIL>");
    expect(passwordInput).toHaveValue("Password123!");
  });

  it("handles successful login with tokens", async () => {
    const mockUserData = {
      id: "123",
      name: "Test User",
      email: "<EMAIL>",
      role: "therapist",
    };
    
    mockClient.post.mockResolvedValueOnce({
      status: HttpStatusCode.Ok,
      data: {
        accessToken: "mock-access-token",
        refreshToken: "mock-refresh-token",
        user: mockUserData,
      },
    });

    const { getByPlaceholderText, getByRole } = renderComponent();
    
    const emailInput = getByPlaceholderText("your email address");
    const passwordInput = getByPlaceholderText("your password");
    const signInButton = getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
    
    await waitFor(() => {
      expect(mockClient.post).toHaveBeenCalledWith("/auth/login", {
        email: "<EMAIL>",
        password: "Password123!",
      });
    });
    
    expect(mockClient.post).toHaveBeenCalledTimes(1);
  });

  it("handles MFA code sent", async () => {
    mockClient.post.mockResolvedValueOnce({
      status: HttpStatusCode.Ok,
      data: {
        mfaCodeSent: true,
        message: "MFA code sent to email",
      },
    });

    const { getByPlaceholderText, getByRole } = renderComponent();
    
    const emailInput = getByPlaceholderText("your email address");
    const passwordInput = getByPlaceholderText("your password");
    const signInButton = getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
    
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/mfa?email=<EMAIL>");
    });
  });

  it("handles email verification error", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        status: 403,
        data: {
          message: "Email not verified",
          data: {
            emailNotVerified: true
          }
        },
      },
    });

    const { getByPlaceholderText, getByRole, findByText } = renderComponent();
    
    const emailInput = getByPlaceholderText("your email address");
    const passwordInput = getByPlaceholderText("your password");
    const signInButton = getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
    
    const errorMessage = await findByText("Email not verified. Please verify your email address.");
    expect(errorMessage).toBeInTheDocument();
    
    const verifyEmailLink = screen.getByText("Verify Email");
    expect(verifyEmailLink).toBeInTheDocument();
  });

  it("handles account under review", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        status: 403,
        data: {
          message: "Account under review",
          data: {
            accountUnderReview: true
          }
        },
      },
    });

    const { getByPlaceholderText, getByRole } = renderComponent();
    
    const emailInput = getByPlaceholderText("your email address");
    const passwordInput = getByPlaceholderText("your password");
    const signInButton = getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
  });

  it("handles account rejected", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        status: 403,
        data: {
          message: "Account rejected",
          data: {
            accountRejected: true,
            userId: 123
          }
        },
      },
    });

    renderComponent();
    
    const emailInput = screen.getByPlaceholderText("your email address");
    const passwordInput = screen.getByPlaceholderText("your password");
    const signInButton = screen.getByRole("button", { name: "Sign In" });
    
    // Fill in form and submit
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
    
    // Use a more flexible approach to check for rejection messages
    
  });

  it("handles generic login error", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        status: 401,
        data: {
          message: "Invalid credentials",
        },
      },
    });

    const { getByPlaceholderText, getByRole, findByText } = renderComponent();
    
    const emailInput = getByPlaceholderText("your email address");
    const passwordInput = getByPlaceholderText("your password");
    const signInButton = getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "WrongPassword123!" } });
    fireEvent.click(signInButton);
    
    const errorMessage = await findByText("Invalid credentials");
    expect(errorMessage).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.queryByText("Invalid credentials"));
    });
  });

  it("disables the sign in button while processing", async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });
    
    mockClient.post.mockReturnValueOnce(promise);

    const { getByPlaceholderText, getByRole } = renderComponent();
    
    const emailInput = getByPlaceholderText("your email address");
    const passwordInput = getByPlaceholderText("your password");
    const signInButton = getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
    
    // Button should be disabled while processing
    expect(signInButton).toBeDisabled();
    
    // Now resolve the promise
    resolvePromise({
      status: HttpStatusCode.Ok,
      data: {
        accessToken: "mock-access-token",
        refreshToken: "mock-refresh-token",
        user: { id: "123", name: "Test User" },
      },
    });
    
    await waitFor(() => {
      expect(signInButton).not.toBeDisabled();
    });
  });

  it("navigates to sign up page when clicking 'Sign Up' link", () => {
    renderComponent();
    const signUpLink = screen.getByText("Sign Up");
    expect(signUpLink).toHaveAttribute("href", "/auth/register/create-account");
  });

  it("navigates to forgot password page when clicking 'Forgot Password?' link", () => {
    renderComponent();
    const forgotPasswordLink = screen.getByText("Forgot Password?");
    expect(forgotPasswordLink).toHaveAttribute("href", "/auth/forgot-password");
  });
  
  it("displays the correct copyright year", () => {
    renderComponent();
    const currentYear = new Date().getFullYear().toString();
    expect(screen.getByText(`© NextTherapist, ${currentYear}`)).toBeInTheDocument();
  });
  
  it("allows resubmitting registration with previous details for rejected accounts", async () => {
    // Mock fetch registration info action
    const mockDispatch = vi.fn().mockImplementation((action) => {
      if (typeof action === 'function') {
        return Promise.resolve({
          status: 200,
          data: {
            userToken: 'test-token',
            // Add other necessary registration info here
          }
        });
      }
      return action;
    });
    
    // Override store to allow testing the dispatch
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated: false }),
        therapist: () => ({}),
        sidebar: () => ({}),
      },
      middleware: (getDefaultMiddleware) => 
        getDefaultMiddleware({
          thunk: {
            extraArgument: {
              dispatch: mockDispatch,
            },
          },
        }),
    });

    mockClient.post.mockRejectedValueOnce({
      response: {
        status: 403,
        data: {
          message: "Account rejected",
          data: {
            accountRejected: true,
            userId: 123
          }
        },
      },
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <LoginPage />
        </BrowserRouter>
      </Provider>
    );
    
    const emailInput = screen.getByPlaceholderText("your email address");
    const passwordInput = screen.getByPlaceholderText("your password");
    const signInButton = screen.getByRole("button", { name: "Sign In" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.click(signInButton);
    
    // Add assertions for this test case
  });
});