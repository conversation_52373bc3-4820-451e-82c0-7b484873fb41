import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import MfaPage from "../../../src/pages/auth/MfaPage";
import { useApiClient } from "../../../src/utils/api.util";
import "@testing-library/jest-dom";
import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";

// Mock modules
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

// Mock react-hot-toast
vi.mock("react-hot-toast", () => {
  const mockToast = {
    success: vi.fn(),
    error: vi.fn(),
    dismiss: vi.fn(),
  };
  return {
    ...mockToast,
    default: mockToast,
  };
});

// Mock react-router-dom navigation
const mockNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock notification util
vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe("MfaPage", () => {
  const mockClient = {
    post: vi.fn(),
  };

  // Mock URL parameters
  const mockEmailParam = "<EMAIL>";
  const originalWindowLocation = window.location;

  beforeEach(() => {
    vi.clearAllMocks();
    (useApiClient as any).mockReturnValue(mockClient);
    mockNavigate.mockClear();
    mockClient.post.mockClear();

    // Mock window.location.search
    delete window.location;
    window.location = {
      ...originalWindowLocation,
      search: `?email=${mockEmailParam}`,
    } as Location;

    // Mock setInterval and clearInterval
    vi.useFakeTimers();
  });

  afterEach(() => {
    window.location = originalWindowLocation;
    vi.useRealTimers();
  });

  const renderComponent = (isAuthenticated = false) => {
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated }),
        therapist: () => ({}),
        sidebar: () => ({}),
      },
    });

    return render(
      <Provider store={store}>
        <BrowserRouter>
          <MfaPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it("renders the MFA form correctly", () => {
    renderComponent();
    expect(screen.getByText("Multi Factor Authentication")).toBeInTheDocument();
    expect(screen.getByText(/Enter the 6-digit code/)).toBeInTheDocument();
    expect(screen.getByLabelText("6-Digit Code")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("your 6-digit code")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Sign In/i })).toBeInTheDocument();
    expect(screen.getByText(/Don't receive the code?/)).toBeInTheDocument();
    
    // Using a more flexible regex pattern to match the resend text
    expect(screen.getByText(/Resend in \d+/)).toBeInTheDocument();
  });

  it("redirects to dashboard if user is already authenticated", () => {
    renderComponent(true);
    // Navigate component is used for redirection in this case
    expect(screen.queryByText("Multi Factor Authentication")).not.toBeInTheDocument();
  });

  it("focuses on code input when component mounts", () => {
    renderComponent();
    expect(document.activeElement).toBe(screen.getByPlaceholderText("your 6-digit code"));
  });

  it("updates the code state when input changes", () => {
    renderComponent();
    
    const codeInput = screen.getByPlaceholderText("your 6-digit code");
    
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    expect(codeInput).toHaveValue("123456");
  });

  it("prevents form submission when code is empty", async () => {
    renderComponent();
    
    const signInButton = screen.getByRole("button", { name: /Sign In/i });
    
    fireEvent.click(signInButton);
    
    // The API should not be called
    expect(mockClient.post).not.toHaveBeenCalled();
  });

  it("displays the correct copyright year", () => {
    renderComponent();
    expect(screen.getByText(/© NextTherapist, 2025/)).toBeInTheDocument();
  });

  it("prevents resend when timer is active", async () => {
    renderComponent();
    
    // The button with the timer should be disabled
    const resendButton = screen.getByRole("button", { name: /Resend in \d+/i });
    fireEvent.click(resendButton);
    
    // The API should not be called
    expect(mockClient.post).not.toHaveBeenCalled();
  });

  it("correctly handles absence of email in URL parameters", () => {
    // Remove email from URL
    delete window.location;
    window.location = {
      ...originalWindowLocation,
      search: "",
    } as Location;

    renderComponent();
    
    const codeInput = screen.getByPlaceholderText("your 6-digit code");
    const signInButton = screen.getByRole("button", { name: /Sign In/i });
    
    fireEvent.change(codeInput, { target: { value: "123456" } });
    fireEvent.click(signInButton);
    
    // The API should not be called without email
    expect(mockClient.post).not.toHaveBeenCalled();
  });

  it("cleans up timer interval on unmount", () => {
    // Create a spy for clearInterval but don't rely on the global object directly
    const clearIntervalSpy = vi.spyOn(window, 'clearInterval');
    
    const { unmount } = renderComponent();
    unmount();
    
    expect(clearIntervalSpy).toHaveBeenCalled();
    clearIntervalSpy.mockRestore(); // Restore the original function after test
  });
});