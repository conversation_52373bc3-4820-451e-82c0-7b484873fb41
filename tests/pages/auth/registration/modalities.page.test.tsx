import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import '@testing-library/jest-dom';
import Page from "../../../../src/pages/auth/registration/modalities.page";
import { Provider } from "react-redux"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>er as Router } from "react-router-dom"
import configureStore from "redux-mock-store"
import thunk from "redux-thunk"
import * as therapistSlicer from "../../../../src/store/slicers/therapist.slicer"
import * as notificationUtil from "../../../../src/utils/notification.util"
import * as appUtil from "../../../../src/utils/app.util"
import { beforeEach, describe, expect, it, vi } from "vitest";
import { updateRegistrationState } from "../../../../src/store/slicers/auth.slicer"
const mockNavigate = vi.fn();

vi.mock('react-router-dom', async (importOriginal) => {
  const actual = (await importOriginal()) as Record<string, unknown>;
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      pathname: '/auth/register/modalities',
    }),
    Link: (props: any) => <a {...props} />,
  };
});

const mockStore = configureStore([thunk])

describe("Page Component", () => {
  let store;

  beforeEach(() => {
    store = mockStore({
      auth: {
        user: { userToken: "mockUserToken" },
        registration: {
          formContent: {
            modalities: {
            "Acceptance and Commitment Therapy (ACT)": [
            "Acceptance and Commitment Therapy (ACT)_certified"
            ]
          }
          },
          userRegistrationToken: "mockToken",
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: []
        },
      },
    });
  });

it("toggles checkbox state", () => {
  render(
    <Provider store={store}>
      <Router>
        <Page />
      </Router>
    </Provider>
  );

  // Use the correct aria-label value for your test modality
  const checkbox = screen.getByRole("checkbox", { name: "Acceptance and Commitment Therapy (ACT)_trained" });
  fireEvent.click(checkbox);
  expect(checkbox).toBeChecked();
});


it("dispatches registerTherapistAction and updates form", async () => {
    const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
    vi.spyOn(therapistSlicer, "registerTherapistAction").mockReturnValue(mockDispatch);

    render(
      <Provider store={store}>
        <Router>
          <Page />
        </Router>
      </Provider>
    );

    fireEvent.click(screen.getByRole("button", { name: /save & continue/i }))

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
    });
  });

  it("navigates to the next page if navToInvalidPage is true and registration is incomplete", async () => {
    const getNextInvalidPageSpy = vi.spyOn(appUtil, "getNextInvalidPage").mockReturnValue("some-page")

    store = mockStore({
      ...store.getState(),
      auth: {
        ...store.getState().auth,
        registration: {
          ...store.getState().auth.registration,
          navToInvalidPage: true,
        },
      },
    });

    const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
    vi.spyOn(therapistSlicer, "registerTherapistAction").mockReturnValue(mockDispatch);

    render(
      <Provider store={store}>
        <Router>
          <Page />
        </Router>
      </Provider>
    );

    fireEvent.click(screen.getByRole("button", { name: /save & continue/i }))

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/register/some-page")
    });

    getNextInvalidPageSpy.mockRestore();
  });

it("shows error if no user token is found", async () => {
  const errorSpy = vi.spyOn(notificationUtil.default, "error");
  store = mockStore({
    ...store.getState(),
    auth: {
      ...store.getState().auth,
      registration: {
        ...store.getState().auth.registration,
        userRegistrationToken: "",
      },
    },
  });

  render(
    <Provider store={store}>
      <Router>
        <Page />
      </Router>
    </Provider>
  );

  fireEvent.click(screen.getByRole("button", { name: /save & continue/i }))

  await waitFor(() => {
    expect(errorSpy).toHaveBeenCalledWith("User Token is required");
  });
});

it("updates modalitiesState correctly when toggling certified and trained checkboxes", async () => {
  render(
    <Provider store={store}>
      <Router>
        <Page />
      </Router>
    </Provider>
  );

  const trainedCheckbox = screen.getByRole("checkbox", { name: "Acceptance and Commitment Therapy (ACT)_trained" });

  fireEvent.click(trainedCheckbox);
  expect(trainedCheckbox).toBeChecked();

  fireEvent.click(trainedCheckbox);
  expect(trainedCheckbox).not.toBeChecked();
 
  const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
  vi.spyOn(therapistSlicer, "registerTherapistAction").mockReturnValue(mockDispatch);

  fireEvent.click(screen.getByRole("button", { name: /save & continue/i }));

  await waitFor(() => {
    const calledWith = mockDispatch.mock.calls[0][0];
    expect(calledWith["Acceptance and Commitment Therapy (ACT)"]).toBeUndefined();
  });
});

it("removes modality from state when both certified and trained are unchecked", async () => {
  render(
    <Provider store={store}>
      <Router>
        <Page />
      </Router>
    </Provider>
  );

  const certifiedCheckbox = screen.getByRole("checkbox", { name: "Acceptance and Commitment Therapy (ACT)_certified" });
  const trainedCheckbox = screen.getByRole("checkbox", { name: "Acceptance and Commitment Therapy (ACT)_trained" });

  // Check both
  fireEvent.click(certifiedCheckbox);
  fireEvent.click(trainedCheckbox);
  expect(certifiedCheckbox).toBeChecked();
  expect(trainedCheckbox).toBeChecked();

  // Uncheck certified
  fireEvent.click(certifiedCheckbox);
  expect(certifiedCheckbox).not.toBeChecked();
  expect(trainedCheckbox).toBeChecked();

  // Uncheck trained (now both are unchecked, so state for this modality should be removed)
  fireEvent.click(trainedCheckbox);
  expect(trainedCheckbox).not.toBeChecked();

  // Submit and check that the dispatched payload does not include this modality
  const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
  vi.spyOn(therapistSlicer, "registerTherapistAction").mockReturnValue(mockDispatch);

  fireEvent.click(screen.getByRole("button", { name: /save & continue/i }));

  await waitFor(() => {
    const calledWith = mockDispatch.mock.calls[0][0];
    expect(calledWith["Acceptance and Commitment Therapy (ACT)"]).toBeUndefined();
  });
});




});

describe("Page Component Other", () => {
  let store: any;

it("shows registration complete dialog if registration is complete and shouldShowRegCompleteDialog is true", async () => {

  const validateSpy = vi.spyOn(appUtil, "validateTherapistRegistration").mockReturnValue(true);
    vi.spyOn({ updateRegistrationState }, "updateRegistrationState");
    store = mockStore({
    auth: {
      user: { userToken: "mockUserToken" },
      registration: {
        formContent: {
          modalities: {
            "Acceptance and Commitment Therapy (ACT)": [
              "Acceptance and Commitment Therapy (ACT)_certified"
            ]
          }
        },
        userRegistrationToken: "mockToken",
        navToInvalidPage: true,
        shouldShowRegCompleteDialog: true,
        editFlag: false,
      },
    },
    sidebar: {
      registerTherapist: {
        invalidMenus: []
      },
    },
  });

  render(
    <Provider store={store}>
      <Router>
        <Page />
      </Router>
    </Provider>
  );

  fireEvent.click(screen.getByRole("button", { name: /save & continue/i }));

await waitFor(() => {
  const actions = store.getActions();
   console.log("Dispatched actions:", actions);
  expect(actions).toContainEqual({
    type: "auth/updateRegistrationState",
    payload: { key: "showRegCompleteDialog", value: true }
  });
});

  validateSpy.mockRestore();
});
})