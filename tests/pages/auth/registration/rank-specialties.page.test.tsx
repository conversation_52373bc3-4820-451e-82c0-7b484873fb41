import React from "react";
import '@testing-library/jest-dom'
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { MemoryRouter } from "react-router-dom";
import { describe, it, expect, beforeEach, vi } from "vitest";
import configureS<PERSON> from "redux-mock-store";
import thunk from "redux-thunk";
import RankSpecialtiesPage from "../../../../src/pages/auth/registration/rank-specialties.page";
import notification from "../../../../src/utils/notification.util";
import { registerTherapistAction } from "../../../../src/store/slicers/therapist.slicer";

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async (importOriginal) => {
   const actual = (await importOriginal()) as Record<string, unknown>;

  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const middlewares = [thunk];
const mockStore = configureStore(middlewares);
let store;

vi.mock("../../../../src/utils/notification.util", () => ({
  __esModule: true,
  default: { error: vi.fn(), success: vi.fn() },
}));

vi.mock("../../../../src/store/slicers/therapist.slicer", async (importOriginal) => {
  const actual = (await importOriginal()) as Record<string, unknown>;
  return {
    ...actual,
    registerTherapistAction: vi.fn(() => async () => Promise.resolve({ status: 200 })),
  };
});

vi.mock("../../../../src/utils/registrationHelpers", () => ({
  getNextInvalidPage: vi.fn(),
  validateTherapistRegistration: vi.fn(),
}));


beforeEach(() => {
  vi.clearAllMocks();

  store = mockStore({
    auth: {
      user: { id: "123", userToken: "mock-token" },
      registration: {
        formContent: {
          specialization: { depression: ["major-depression"] },
          "rank-specialties": {},
        },
        userRegistrationToken: "mock-token",
        navToInvalidPage: false,
        shouldShowRegCompleteDialog: false,
        editFlag: false,
      },
    },
    sidebar: {
      registerTherapist: { invalidMenus: [] },
    },
  });
});

const setup = () =>
  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={["/auth/registration/rank-specialties"]}>
        <RankSpecialtiesPage />
      </MemoryRouter>
    </Provider>
  );

describe("Rank Specialties Page", () => {
  it("renders the page title and instructions", async () => {
    setup();
    expect(await screen.findByText("Rank Specialties")).toBeInTheDocument();
    expect(screen.getByText(/Drag and drop the rankings/i)).toBeInTheDocument();
  });

  it("renders info alert if no selected specialties", async () => {
    store = mockStore({
      auth: {
        user: { id: "123", userToken: "mock-token" },
        registration: {
          formContent: {
            specialization: {},
            "rank-specialties": {},
          },
          userRegistrationToken: "mock-token",
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
      },
      sidebar: {
        registerTherapist: { invalidMenus: [] },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={["/auth/registration/rank-specialties"]}>
          <RankSpecialtiesPage />
        </MemoryRouter>
      </Provider>
    );

    expect(await screen.findByText(/Specialties should be selected first/i)).toBeInTheDocument();
  });

  it("shows error notification when user token is missing", async () => {
    store = mockStore({
      auth: {
        user: { id: "123" },
        registration: {
          formContent: {
            specialization: { depression: ["major-depression"] },
            "rank-specialties": {},
          },
          userRegistrationToken: null,
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
      },
      sidebar: {
        registerTherapist: { invalidMenus: [] },
      },
    });

    setup();

    const saveButton = await screen.findByRole("button", { name: /save/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith("User Token is required");
    });
  });

  it("submits valid form and updates registration form", async () => {
    setup();

    const saveButton = await screen.findByRole("button", { name: /save/i });

    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(registerTherapistAction).toHaveBeenCalled();
    });
  });

  it("displays error if not all specialties are ranked", async () => {
    store = mockStore({
      auth: {
        user: { id: "123", userToken: "mock-token" },
        registration: {
          formContent: {
          specialization: {
            depression: ["major-depression"],
            anxiety: ["general-anxiety"],
          },
          "rank-specialties": { "1": "depression" },
          },
          userRegistrationToken: "mock-token",
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
      },
      sidebar: {
        registerTherapist: { invalidMenus: ["rank-specialties"] },
      },
    });

    setup();

    expect(await screen.findByText(/Please rank the specialties in the available slots/i)).toBeInTheDocument();
  });

  it("does not navigate if registration is not complete", async () => {
    const mockNavigate = vi.fn();
    vi.mock("react-router-dom", async (importActual) => {
      const actual = (await importActual()) as Record<string, unknown>
      return {
        ...actual,
        useNavigate: () => mockNavigate,
      };
    });

    setup();

    const saveButton = await screen.findByRole("button", { name: /save/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
     expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  it("shows success notification on successful save", async () => {
     (registerTherapistAction as any).mockImplementation(() => async () => {
      notification.success("Registration saved successfully");
      return { status: 200 };
    });

    setup();

    const saveButton = await screen.findByRole("button", { name: /save/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(notification.success).toHaveBeenCalledWith("Registration saved successfully");
    });
  });

  it("shows error notification on failed save", async () => {
    (registerTherapistAction as any).mockImplementation(() => async () => {
      notification.error("Failed to save registration");
      return { status: 500 };
    });

    setup();

    const saveButton = await screen.findByRole("button", { name: /save/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith("Failed to save registration");
    });
  });

//  it("navigates immediately if editFlag is true", async () => {
//   store = mockStore({
//     auth: {
//       registration: {
//         editFlag: true,
//         // mock the page to which your component navigates
//         navigateToPage: "/auth/register/rank-sub-specialties",
//       },
//     },
//     sidebar: {
//       registerTherapist: { invalidMenus: [] },
//     },
//   });

//   render(
//     <Provider store={store}>
//       <MemoryRouter>
//        <RankSpecialtiesPage />
//       </MemoryRouter>
//     </Provider>
//   );
  
//   await waitFor(() => {
//    expect(mockNavigate).toHaveBeenCalledWith("/auth/registration/specialization");
//   });
// });


// it("navigates immediately if editFlag is true", async () => {
//   store = mockStore({
//     auth: {
//       user: { id: "123", userToken: "mock-token" },
//       registration: {
//         formContent: {
//           specialization: { depression: ["major-depression"] },
//           "rank-specialties": {},
//         },
//         userRegistrationToken: "mock-token",
//         navToInvalidPage: false,
//         shouldShowRegCompleteDialog: false,
//         editFlag: true, // ✅ Important!
//       },
//     },
//     sidebar: {
//       registerTherapist: { invalidMenus: [] },
//     },
//   });

//   render(
//     <Provider store={store}>
//       <MemoryRouter initialEntries={["/auth/registration/rank-specialties"]}>
//         <RankSpecialtiesPage />
//       </MemoryRouter>
//     </Provider>
//   );

//   await waitFor(() => {
//     expect(mockNavigate).toHaveBeenCalledWith("/auth/registration/next-page");
//   });
// });


  // it("navigates to next invalid page if navToInvalidPage is true and there are invalid menus", async () => {
  //   // Setup with invalid menus and navToInvalidPage = true
  //   store = mockStore({
  //     auth: {
  //       user: { id: "123", userToken: "mock-token" },
  //       registration: {
  //         formContent: {},
  //         userRegistrationToken: "mock-token",
  //         navToInvalidPage: true,
  //         shouldShowRegCompleteDialog: false,
  //         editFlag: false,
  //       },
  //     },
  //     sidebar: {
  //       registerTherapist: { invalidMenus: ["rank-specialties", "some-invalid-page"] },
  //     },
  //   });

  //   setup();

  //   await waitFor(() => {
  //     // Expect navigation to first invalid menu page
  //     expect(mockNavigate).toHaveBeenCalledWith("/auth/register/some-invalid-page");
  //   });
  // });

  // it("dispatches dialog state update if registration complete and dialog flag true", async () => {
  //   // Here you set navToInvalidPage true but no invalidMenus left
  //   // Also, userRegistrationToken etc are normal and shouldShowRegCompleteDialog true

  //   store = mockStore({
  //     auth: {
  //       user: { id: "123", userToken: "mock-token" },
  //       registration: {
  //         formContent: { allPagesValid: true }, // your formContent that makes registration complete
  //         userRegistrationToken: "mock-token",
  //         navToInvalidPage: true,
  //         shouldShowRegCompleteDialog: true,
  //         editFlag: false,
  //       },
  //     },
  //     sidebar: {
  //       registerTherapist: { invalidMenus: [] },
  //     },
  //   });

  //   const dispatchSpy = vi.spyOn(store, "dispatch");

  //   setup();

  //   await waitFor(() => {
  //     expect(dispatchSpy).toHaveBeenCalledWith(
  //       expect.objectContaining({
  //         type: "auth/updateRegistrationState",
  //         payload: { key: "showRegCompleteDialog", value: true },
  //       })
  //     );
  //   });
  // });

  // it("navigates to default page if registration incomplete and no invalid pages left", async () => {
  //   store = mockStore({
  //     auth: {
  //       user: { id: "123", userToken: "mock-token" },
  //       registration: {
  //         formContent: { incomplete: true },  // represents incomplete registration
  //         userRegistrationToken: "mock-token",
  //         navToInvalidPage: true,
  //         shouldShowRegCompleteDialog: false,
  //         editFlag: false,
  //       },
  //     },
  //     sidebar: {
  //       registerTherapist: { invalidMenus: [] },
  //     },
  //   });

  //   setup();

  //   await waitFor(() => {
  //     expect(mockNavigate).toHaveBeenCalledWith("/auth/registration/some-page");
  //   });
  // });

});

