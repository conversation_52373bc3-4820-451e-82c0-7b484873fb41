// Page.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { Provider } from 'react-redux'
import { MemoryRouter } from 'react-router-dom'
import Page from '../../../../src/pages/auth/registration/practice-focus.page'
import configureStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import '@testing-library/jest-dom'

// Mock components
vi.mock('@/components/buttons/SaveButton', () => ({
	default: ({ onClick, ...props }) => (
		<button onClick={onClick} {...props}>
			Save
		</button>
	),
}))

vi.mock('@/components/forms/AppCheckBoxListComponent', () => ({
	default: ({ checked, onChange, checkList }) => (
		<div>
			{checkList.map((item) => (
				<label key={item.value}>
					<input
						type="checkbox"
						checked={checked.includes(item.value)}
						onChange={() => {
							const newChecked = checked.includes(item.value)
								? checked.filter((val) => val !== item.value)
								: [...checked, item.value]
							onChange(newChecked)
						}}
					/>
					{item.label}
				</label>
			))}
		</div>
	),
}))

vi.mock('@/components/forms/AppRadioComponent', () => ({
	default: ({ checked, onChange, checkList }) => (
		<div>
			{checkList.map((item) => (
				<label key={item.value}>
					<input
						type="radio"
						checked={checked === item.value}
						onChange={() => onChange(item.value)}
              disabled={item.disabled} 
					/>
					{item.label}
				</label>
			))}
		</div>
	),
}))

vi.mock('@/components/forms/InputComponent', () => ({
	default: ({ value, onChange, errorMessage }) => (
		<div>
			<input value={value} onChange={(e) => onChange(e.target.value)} />
			{errorMessage && <span>{errorMessage}</span>}
		</div>
	),
}))

vi.mock('@/components/ToolbarComponent', () => ({
	default: () => <div>Toolbar</div>,
}))

vi.mock('@/store/slicers/auth.slicer', () => ({
	updateRegistrationForm: vi.fn(() => ({ type: 'updateRegistrationForm' })),
	updateRegistrationState: vi.fn(() => ({ type: 'updateRegistrationState' })),
}))

vi.mock('@/store/slicers/sidebar.slicer', () => ({
	updateSidebarState: vi.fn(() => ({ type: 'updateSidebarState' })),
}))


vi.mock('@/store/slicers/therapist.slicer', () => ({
	registerTherapistAction: vi.fn(() => async () => ({ status: 200 })),
}))

vi.mock('../../../../src/utils/notification.util', () => ({
	default: {
		error: vi.fn(),
	},
}))

vi.mock('@/utils/app.util', () => ({
	getNextInvalidPage: vi.fn(() => null),
	validateTherapistRegistration: vi.fn(() => true),
}))

const middlewares = [thunk]
const mockStore = configureStore(middlewares)

describe('Page Component', () => {
	let store

	beforeEach(() => {
		store = mockStore({
			auth: {
				registration: {
					formContent: {
						'practice-info': {
							client_served: [],
							client_preferred: '',
							genders: [],
							religious_specialization: {
								checked_list: [],
								other_religion: '',
							},
							additional_focus: { checked_list: [], other_focus: '' },
						},
					},
					userRegistrationToken: 'token123',
					navToInvalidPage: false,
					shouldShowRegCompleteDialog: false,
					editFlag: false,
				},
				user: { userToken: 'userToken123' },
			},
			sidebar: {
				registerTherapist: {
					invalidMenus: [],
				},
			},
		})
	})

	it('renders the component', () => {
		render(
			<Provider store={store}>
				<MemoryRouter initialEntries={['/auth/register/practice-info']}>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		expect(screen.getByText('Practice Focus')).toBeInTheDocument()
	})

	it('shows validation errors when required fields are empty', async () => {
		store = mockStore({
			...store.getState(),
			sidebar: {
				registerTherapist: {
					invalidMenus: ['practice-info'],
				},
			},
		})

		render(
			<Provider store={store}>
				<MemoryRouter initialEntries={['/auth/register/practice-info']}>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		fireEvent.click(screen.getByRole('button', { name: /save/i }))

		await waitFor(() => {
			expect(
				screen.getByText(/Clients Served is required/i)
			).toBeInTheDocument()
			expect(
				screen.getByText(/Preferred Client is required/i)
			).toBeInTheDocument()
			expect(
				screen.getByText(/Genders You Work With is required/i)
			).toBeInTheDocument()
			expect(
				screen.getByText(/Religious Specialization is required/i)
			).toBeInTheDocument()
			expect(
				screen.getByText(/Additional Focus is required/i)
			).toBeInTheDocument()
		})
	})

	it('shows error for missing "Other" religion text input', async () => {
		render(
			<Provider store={store}>
				<MemoryRouter initialEntries={['/auth/register/practice-info']}>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		// Select "Other (Please specify)" for religion
		fireEvent.click(screen.getByLabelText(/Other \(Please specify\)/i))
		fireEvent.click(screen.getByRole('button', { name: /save/i }))

		await waitFor(() => {
			expect(
				screen.getAllByText(
					(content) =>
						typeof content === 'string' &&
						content.includes('Specify other Religion')
				).length
			).toBeGreaterThan(0)
		})
	})

	it('shows error for missing "Other" additional focus text input', async () => {
		render(
			<Provider store={store}>
				<MemoryRouter initialEntries={['/auth/register/practice-info']}>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		// Select "Other" for additional focus
		fireEvent.click(screen.getByLabelText(/^Other$/i))
		fireEvent.click(screen.getByRole('button', { name: /save/i }))

		await waitFor(() => {
			expect(
				screen.getAllByText(
					(content) =>
						typeof content === 'string' &&
						content.includes('Specify other area of focus')
				).length
			).toBeGreaterThan(0)
		})
	})

it('disables Preferred Client radio options not in Clients Served', async () => {
  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={['/auth/register/practice-info']}>
        <Page />
      </MemoryRouter>
    </Provider>
  );

  // Only select "Individual" for Clients Served (checkbox)
  fireEvent.click(screen.getByLabelText('Individual', { selector: 'input[type="checkbox"]' }));

  // Only "Individual" radio should be enabled
  const radios = screen.getAllByRole('radio');
  expect(radios[0]).not.toBeDisabled();
  for (let i = 1; i < radios.length; i++) {
    expect(radios[i]).toBeDisabled();
  }
});

	it('shows and hides "Other" input for religion and additional focus', async () => {
		render(
			<Provider store={store}>
				<MemoryRouter initialEntries={['/auth/register/practice-info']}>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		// Religion "Other"
		fireEvent.click(screen.getByLabelText(/Other \(Please specify\)/i))
		expect(screen.getAllByRole('textbox').length).toBeGreaterThan(0)

		// Unselect "Other"
		fireEvent.click(screen.getByLabelText(/Other \(Please specify\)/i))
		expect(screen.queryByRole('textbox')).not.toBeInTheDocument()

		// Additional Focus "Other"
		fireEvent.click(screen.getByLabelText(/^Other$/i))
		expect(screen.getAllByRole('textbox').length).toBeGreaterThan(0)

		// Unselect "Other"
		fireEvent.click(screen.getByLabelText(/^Other$/i))
		expect(screen.queryByRole('textbox')).not.toBeInTheDocument()
	}) 

  it('navigates to /profile if editFlag is true and registration is successful', async () => {
   
    store = mockStore({
      ...store.getState(),
      auth: {
        ...store.getState().auth,
        registration: { ...store.getState().auth.registration, editFlag: true }
      }
    })
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/practice-info']}>
          <Page />
        </MemoryRouter>
      </Provider>
    )
    fireEvent.click(screen.getByLabelText('Individual', { selector: 'input[type="checkbox"]' }))
    fireEvent.click(screen.getByLabelText('Individual', { selector: 'input[type="radio"]' }))
    fireEvent.click(screen.getByLabelText('All Genders', { selector: 'input[type="checkbox"]' }))
    fireEvent.click(screen.getByLabelText('Aethism', { selector: 'input[type="checkbox"]' }))
    fireEvent.click(screen.getByLabelText("Women's Mental Health", { selector: 'input[type="checkbox"]' }))
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
  
  })
})



