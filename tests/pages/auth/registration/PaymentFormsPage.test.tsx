import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON>rowserRouter, MemoryRouter, Route, Routes } from 'react-router-dom';
import { Provider } from 'react-redux';
import { describe, expect, vi } from 'vitest';
import PaymentFormsPage from '../../../../src/pages/auth/registration/PaymentFormsPage';  
import store from '../../../../src/store';
import * as appUtil from '../../../../src/utils/app.util';
import { beforeEach, it } from 'vitest';
import { getNextInvalidPage } from "../../../../src/utils/app.util";
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';


vi.mock('@/utils/registration.util', () => ({
  getNextInvalidPage: vi.fn(),
  validateTherapistRegistration: vi.fn(),
}));

const mockNavigate = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});


vi.mock('react-redux', async () => {
  const actual = await vi.importActual('react-redux');
  return {
    ...actual,
    useSelector: vi.fn().mockImplementation((selector) =>
      selector({
        auth: {
          registration: {
            formContent: {
              payment: {
                title: 'Mock Payment Title',
                fields: [],
              },
            },
            userRegistrationToken: 'mock-token',
          },
        },
        sidebar: {
          isSidebarVisible: true,
        },
      })
    ),
  };
});

vi.mock('@/store/slicers/therapist.slicer', () => ({
  registerTherapistAction: vi.fn(() => () => Promise.resolve({ status: 200 })),
  default: () => ({
    therapistInfo: null,
    loading: false,
    error: null,
  }),
}));

vi.mock('@/utils/notification.util', () => ({
  error: vi.fn(),
  success: vi.fn(),
}));

vi.mock('@/utils/app.util', async () => {
  const actual = await vi.importActual<typeof import('../../../../src/utils/app.util')>('@/utils/app.util');
  return {
    ...actual,
    getNextInvalidPage: vi.fn(),
    validateTherapistRegistration: vi.fn(),
  };
});



vi.mock('@/store/slicers/sidebar.slicer', () => ({
  __esModule: true,
  updateSidebarState: vi.fn(() => ({ type: 'sidebar/updateSidebarState' })),
  default: () => ({
    isSidebarVisible: true,
  }),
}));

describe('PaymentFormsPage Component', () => {
  const renderComponent = () =>
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/payment']}>
          <Routes>
            <Route path="/auth/register/:pageId" element={<PaymentFormsPage />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders form fields correctly', () => {
    renderComponent();

    expect(screen.getByLabelText(/60-min session fee/i)).toBeInTheDocument();
    expect(screen.getByText(/Accepted Payment Methods/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Save & Continue/i })).toBeInTheDocument();
  });

  it('shows validation errors when submitting empty form', async () => {
    renderComponent();

    fireEvent.blur(screen.getByLabelText(/60-min session fee/i));
    
    const saveButton = screen.getByRole('button', { name: /Save & Continue/i });
    await userEvent.click(saveButton);

 await waitFor(() => {
  expect(screen.getByText(/Session Fee is required/i)).toBeInTheDocument();
    });
  });

it('navigates to next invalid page if found', async () => {
  vi.mocked(getNextInvalidPage).mockReturnValue('next-therapist-plan');
  renderComponent();

  const saveButton = screen.getByRole('button', { name: /Save & Continue/i });
  await userEvent.click(saveButton);

  await waitFor(() => {
    expect(mockNavigate).toHaveBeenCalledWith('/auth/register/next-therapist-plan');
  });
});

it('navigates to next page if registration is not complete and no invalid page found', async () => {
  vi.mocked(getNextInvalidPage).mockReturnValue(null);
  vi.mocked(appUtil.validateTherapistRegistration).mockReturnValue(false);

  renderComponent();

  const saveButton = screen.getByRole('button', { name: /Save & Continue/i });
  await userEvent.click(saveButton);

  await waitFor(() => {
    expect(mockNavigate).toHaveBeenCalledWith('/auth/register/next-therapist-plan');
  });
});

});

const setup = (initialState = {}) => {
  const store = configureStore({
    reducer: (state = initialState) => state,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
    preloadedState: {
      auth: {
        registration: {
          formContent: {},
          userRegistrationToken: "mockToken",
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
      ...initialState,
    },
  })

  render(
    <Provider store={store}>
      <BrowserRouter>
        <PaymentFormsPage />
      </BrowserRouter>
    </Provider>
  )
}
describe("PaymentFormsPage Validation", () => {
    it("should show error when session fee is empty", async () => {
        setup()

        fireEvent.blur(screen.getByLabelText(/60-min session fee/i));
     
        fireEvent.click(screen.getByText("Save & Continue"))
        await waitFor(() => {
            expect(screen.getByText("Session Fee is required")).toBeInTheDocument()
        })
    })


    it("should show error when insurance selected but no provider chosen", async () => {
        setup()
        fireEvent.change(screen.getByPlaceholderText("Enter #"), {
            target: { value: "100" },
        })
        fireEvent.click(screen.getByLabelText("insurance"))
        fireEvent.click(screen.getByText("Save & Continue"))
        await waitFor(() => {
            expect(screen.getByText("Insurance Provider is required")).toBeInTheDocument()
        })
    })

        it("should not show errors when all fields are valid", async () => {
        setup()
        fireEvent.change(screen.getByPlaceholderText("Enter #"), {
            target: { value: "150" },
        })
        fireEvent.click(screen.getByLabelText("self_pay"))
        fireEvent.click(screen.getByText("Save & Continue"))
        await waitFor(() => {
            expect(screen.queryByText("Session Fee is required")).not.toBeInTheDocument()
            expect(screen.queryByText("Select at least one payment method")).not.toBeInTheDocument()
        })
    })

    it("should allow valid insurance selection and not show errors", async () => {
        setup()
        fireEvent.change(screen.getByPlaceholderText("Enter #"), {
            target: { value: "200" },
        })
        fireEvent.click(screen.getByLabelText("insurance"))
        await waitFor(() => {
            // Select the first insurance type if available
            const insuranceCheckboxes = screen.getAllByRole("checkbox")
            // The first insurance provider checkbox after payment methods
            if (insuranceCheckboxes.length > 4) {
                fireEvent.click(insuranceCheckboxes[4])
            }
        })
        fireEvent.click(screen.getByText("Save & Continue"))
        await waitFor(() => {
            expect(screen.queryByText("Insurance Provider is required")).not.toBeInTheDocument()
            expect(screen.queryByText("Specify other Insurance Provider")).not.toBeInTheDocument()
        })
    })
})
