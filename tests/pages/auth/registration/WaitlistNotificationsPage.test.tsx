// WaitlistNotificationsPage.it.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import WaitlistNotificationsPage from '../../../../src/pages/auth/registration/WaitlistNotificationsPage';
import { afterEach, beforeEach, describe, expect, it ,vi} from 'vitest';
import '@testing-library/jest-dom';

// Mock external components
vi.mock('@/components/ToolbarComponent', () => ({
  default: () => <div data-testid="toolbar">Toolbar</div>,
}));

vi.mock('@/components/forms/AppCheckBoxListComponent', () => ({
  default: ({ checked, onChange }: any) => (
    <div data-testid="checkbox-list">
      <label>
        <input
          type="checkbox"
          checked={checked.includes('email')}
          onChange={() => onChange(['email'])}
        />
        Email
      </label>
      <label>
        <input
          type="checkbox"
          checked={checked.includes('text')}
          onChange={() => onChange(['text'])}
        />
        Text
      </label>
    </div>
  ),
}));

vi.mock('@/components/buttons/SaveButton', () => ({
  default: ({ onClick }: any) => (
    <button data-testid="save-button" onClick={onClick}>
      Save & Continue
    </button>
  ),
}));

vi.mock('react-phone-number-input', () => ({
  __esModule: true,
  default: ({ value, onChange }: any) => (
    <input
      data-testid="phone-input"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  ),
  isPossiblePhoneNumber: (number: string) => number.length >= 10,
}));

// Mock Redux hooks
vi.mock('react-redux', async () => {
  const actual = await vi.importActual<typeof import('react-redux')>('react-redux');
  return {
    ...actual,
    useSelector: vi.fn(),
    useDispatch: () => vi.fn(),
  };
});

// Mock React Router hooks
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/auth/register/notifications',
    }),
  };
});

// Mock utility functions
vi.mock('@/utils/app.util', () => ({
  getNextInvalidPage: () => null,
  validateTherapistRegistration: () => true,
}));

// Mock notification utility
vi.mock('@/utils/notification.util', () => ({
  default: {
    error: vi.fn(),
  },
}));

// Mock Redux actions
vi.mock('@/store/slicers/therapist.slicer', () => ({
  registerTherapistAction: vi.fn(() => Promise.resolve({ status: 200 })),
}));

vi.mock('@/store/slicers/auth.slicer', () => ({
  updateRegistrationForm: vi.fn(),
  updateRegistrationState: vi.fn(),
}));

vi.mock('@/store/slicers/sidebar.slicer', () => ({
  updateSidebarState: vi.fn(),
}));

// Import useSelector from react-redux for mocking
import { useSelector as _useSelector } from 'react-redux';

const mockUseSelector = _useSelector as unknown as vi.Mock;

describe('WaitlistNotificationsPage', () => {
  beforeEach(() => {
    mockUseSelector.mockImplementation((selector: any) =>
      selector({
        auth: {
          registration: {
            formContent: {
              notifications: {
                booking_time_hour: '1',
                notification_preferences: [],
                phone_number: '',
                week_visibility: '2',
              },
            },
            userRegistrationToken: 'it-token',
            navToInvalidPage: false,
            shouldShowRegCompleteDialog: false,
          },
        },
        sidebar: {
          registerTherapist: {
            invalidMenus: [],
          },
        },
      })
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component correctly', () => {
    render(
      <MemoryRouter>
        <WaitlistNotificationsPage />
      </MemoryRouter>
    );

    expect(screen.getByTestId('toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('checkbox-list')).toBeInTheDocument();
    expect(screen.getByTestId('save-button')).toBeInTheDocument();
  });



  it('shows error when phone number is invalid', async () => {
    render(
      <MemoryRouter>
        <WaitlistNotificationsPage />
      </MemoryRouter>
    );

    // Simulate selecting 'text' notification preference
    fireEvent.click(screen.getByLabelText('Text'));

    // Enter invalid phone number
    const phoneInput = screen.getByTestId('phone-input');
    fireEvent.change(phoneInput, { target: { value: '123' } });

    fireEvent.click(screen.getByTestId('save-button'));

    await waitFor(() => {
      expect(screen.getByText('Input a valid phone number')).toBeInTheDocument();
    });
  });

  it('submits the form successfully', async () => {
    render(
      <MemoryRouter>
        <WaitlistNotificationsPage />
      </MemoryRouter>
    );

    // Simulate selecting 'email' notification preference
    fireEvent.click(screen.getByLabelText('Email'));

    fireEvent.click(screen.getByTestId('save-button'));

    await waitFor(() => {
      expect(screen.queryByText('Notification Preferences is required')).not.toBeInTheDocument();
    });
  });

  it('shows error when userRegistrationToken is missing', async () => {
    mockUseSelector.mockImplementation((selector: any) =>
      selector({
        auth: {
          registration: {
            formContent: {
              notifications: {
                booking_time_hour: '1',
                notification_preferences: ['email'],
                phone_number: '',
                week_visibility: '2',
              },
            },
            userRegistrationToken: null,
            navToInvalidPage: false,
            shouldShowRegCompleteDialog: false,
          },
        },
        sidebar: {
          registerTherapist: {
            invalidMenus: [],
          },
        },
      })
    );

    const notification = await import('../../../../src/utils/notification.util');

    render(
      <MemoryRouter>
        <WaitlistNotificationsPage />
      </MemoryRouter>
    );

    fireEvent.click(screen.getByTestId('save-button'));

    await waitFor(() => {
      expect(notification.default.error).toHaveBeenCalledWith('User Token is required');
    });
  });

  
});
