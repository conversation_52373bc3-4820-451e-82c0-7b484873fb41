import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import PracticeInfoPage from '../../../../src/pages/auth/registration/practice-info.page';
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../../../../src/store/slicers/auth.slicer';
import therapistReducer from '../../../../src/store/slicers/therapist.slicer';
import sidebarReducer from '../../../../src/store/slicers/sidebar.slicer';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

vi.mock('compressorjs', () => {
  return {
    default: vi.fn((file, options) => {
      options.success(file);
    })
  };
});

vi.mock('react-google-places-autocomplete', () => ({
  default: vi.fn(() => <div data-testid="google-places-autocomplete" />),
  geocodeByPlaceId: vi.fn().mockResolvedValue([
    {
      address_components: [
        { long_name: 'New York', types: ['locality'] },
        { long_name: 'NY', types: ['administrative_area_level_1'] }
      ]
    }
  ])
}));

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      pathname: '/auth/register/practice-info'
    })
  };
});

describe('PracticeInfoPage', () => {
  let store;

  beforeEach(() => {
    const initialState = {
      auth: {
        isAuthenticated: false,
        registration: {
          fetchingPage: false,
          formContent: {
            'create-account': {
              email: '<EMAIL>'
            }
          },
          therapistMatchKey: null,
          matchedTherapists: [],
          patientAnswers: {},
          userRegistrationToken: 'test-token',
          savingTherapistInfo: false,
          currentPage: null,
          editFlag: false,
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false
        },
        user: null
      },
      sidebar: {
        on: false,
        registerTherapist: {
          openSubMenu: {},
          invalidMenus: []
        }
      },
      therapist: {
        fetchingInfo: false,
        profileInfo: [],
        profilePageInfo: []
      }
    };

    store = configureStore({
      reducer: {
        auth: authReducer,
        therapist: therapistReducer,
        sidebar: sidebarReducer
      },
      preloadedState: initialState
    });
    mockNavigate.mockClear();
  });

  const renderComponent = () => {
    return render(
      <Provider store={store}>
        <BrowserRouter>
          <PracticeInfoPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it('renders the practice info form correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Practice Information')).toBeInTheDocument();
    expect(screen.getByLabelText(/Practice Name/i)).toBeInTheDocument();
    expect(screen.getByText(/Business Phone/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Business Email/i)).toBeInTheDocument();
    expect(screen.getByTestId('google-places-autocomplete')).toBeInTheDocument();
  });

  it('validates business email format', async () => {
    renderComponent();
    const emailInput = screen.getByLabelText(/Business Email/i);
    
    await userEvent.type(emailInput, 'invalid-email');
    
    await waitFor(() => {
      expect(screen.getByText(/Input a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates required fields', async () => {
    renderComponent();
    const submitButton = screen.getByRole('button', { name: /Save/i });
    
    await userEvent.click(submitButton);
    const emailInput = screen.getByLabelText(/Business Email/i);
    await userEvent.clear(emailInput);
    await userEvent.tab();
    await waitFor(() => {
      expect(screen.getByText(/Practice name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Business phone is required/i)).toBeInTheDocument();
      expect(screen.getByText((content) => content.includes('Business email is required'))).toBeInTheDocument();
      expect(screen.getByText(/Business address is required/i)).toBeInTheDocument();
    });
  });

it('validates business phone format', async () => {
  renderComponent();

  const phoneInput = document.querySelector('.PhoneInputInput') as HTMLInputElement;
  expect(phoneInput).toBeInTheDocument();

  await userEvent.clear(phoneInput);
  await userEvent.type(phoneInput, '123');

  const submitButton = screen.getByRole('button', { name: /Save/i });
  await userEvent.click(submitButton);

  await waitFor(() => {
    expect(screen.getByText(/Input a valid phone number/i)).toBeInTheDocument();
  });
});


  it('submits form successfully with valid data', async () => {
    const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
    store.dispatch = mockDispatch;
    renderComponent();    
  
    await userEvent.type(screen.getByLabelText(/Practice Name/i), 'Test Practice');
    const phoneInput = document.querySelector('.PhoneInputInput') as HTMLInputElement;
    expect(phoneInput).toBeInTheDocument();

    await userEvent.clear(phoneInput);
    await userEvent.type(phoneInput, '+1234567890');

    await userEvent.type(screen.getByLabelText(/Business Email/i), '<EMAIL>');    
   
    const addressInput = screen.getByTestId('google-places-autocomplete');
    await userEvent.type(addressInput, '123 Main St');
    
    const submitButton = screen.getByRole('button', { name: /Save/i });
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/auth/register/normal-office-hours');
    });
  });
   
}); 