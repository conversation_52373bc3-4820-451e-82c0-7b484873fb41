import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import CreateAccountPage from '../../../../src/pages/auth/registration/create-account.page';
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../../../../src/store/slicers/auth.slicer';
import therapistReducer from '../../../../src/store/slicers/therapist.slicer';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

interface UserState {
  token?: string;
  refreshToken?: string;
  user?: any;
  isAuthenticated: boolean;
  registration: {
    fetchingPage: boolean;
    formContent: Record<string, any>;
    therapistMatchKey: string | null;
    matchedTherapists: any[];
    page?: any;
    patientAnswers: Record<string, any>;
    userRegistrationToken: string | null;
    savingTherapistInfo: boolean;
    currentPage?: string | null;
    editFlag: boolean;
    navToInvalidPage?: boolean;
    showRegCompleteDialog?: boolean;
    shouldShowRegCompleteDialog?: boolean;
  };
}

interface TherapistState {
  fetchingInfo: boolean;
  therapistInfo: any;
  error: any;
}

interface InitialState {
  auth?: Partial<UserState>;
  therapist?: Partial<TherapistState>;
}

const createTestStore = (initialState: InitialState = {}) => {
  const defaultState = {
    auth: {
      isAuthenticated: false,
      registration: {
        formContent: {},
        currentPage: 'create-account',
        userRegistrationToken: null,
        fetchingPage: false,
        therapistMatchKey: null,
        matchedTherapists: [],
        page: undefined,
        patientAnswers: {},
        savingTherapistInfo: false,
        editFlag: false,
        navToInvalidPage: false,
        showRegCompleteDialog: false,
        shouldShowRegCompleteDialog: true
      }
    },
    therapist: {
      fetchingInfo: false,
      therapistInfo: null,
      error: null
    }
  };

  return configureStore({
    reducer: {
      auth: authReducer,
      therapist: therapistReducer
    },
    preloadedState: {
      auth: { ...defaultState.auth, ...initialState.auth },
      therapist: { ...defaultState.therapist, ...initialState.therapist }
    }
  });
};

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      pathname: '/auth/register/create-account'
    })
  };
});

describe('CreateAccountPage', () => {
  let store;

  beforeEach(() => {
    store = createTestStore();
    mockNavigate.mockClear();
  });

  const renderComponent = () => {
    return render(
      <Provider store={store}>
        <BrowserRouter>
          <CreateAccountPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it('renders all form fields correctly', () => {
    renderComponent();
    
    expect(screen.getByRole('textbox', { name: /First Name/i })).toBeInTheDocument();
    expect(screen.getByRole('textbox', { name: /Last Name/i })).toBeInTheDocument();
    expect(screen.getByRole('textbox', { name: /Email Address/i })).toBeInTheDocument();
    expect(screen.getByTestId('password')).toBeInTheDocument();
    expect(screen.getByTestId('confirm_password')).toBeInTheDocument();
    expect(screen.getByTestId('terms-checkbox')).toBeInTheDocument();
    expect(screen.getByTestId('privacy-policy-checkbox')).toBeInTheDocument();
  });

  it('validates required fields', async () => {
    // Create a mock dispatch function that we can use to check if form submission is attempted
    const mockDispatch = vi.fn().mockResolvedValue({ status: 201 });
    store.dispatch = mockDispatch;
    renderComponent();
 
    // Get all form fields
    const firstNameInput = screen.getByRole('textbox', { name: /First Name/i });
    const lastNameInput = screen.getByRole('textbox', { name: /Last Name/i });
    const emailInput = screen.getByRole('textbox', { name: /Email Address/i });
    const passwordInput = screen.getByTestId('password');
    const confirmPasswordInput = screen.getByTestId('confirm_password');
    const termsCheckbox = screen.getByTestId('terms-checkbox');
    const privacyCheckbox = screen.getByTestId('privacy-policy-checkbox');
    const submitButton = screen.getByRole('button', { name: /Continue/i });
 
    // Try to submit the form with empty fields - this should not call dispatch
    await userEvent.click(submitButton);
    expect(mockDispatch).not.toHaveBeenCalled();
 
    // Fill in all fields except first name and try to submit
    await userEvent.type(lastNameInput, 'Doe');
    await userEvent.type(emailInput, '<EMAIL>');
    await userEvent.type(passwordInput, 'StrongPass1!');
    await userEvent.type(confirmPasswordInput, 'StrongPass1!');
    await userEvent.click(termsCheckbox);
    await userEvent.click(privacyCheckbox);
    await userEvent.click(submitButton);
    expect(mockDispatch).not.toHaveBeenCalled();
 
    // Now fill in first name and try again - this should succeed
    await userEvent.type(firstNameInput, 'John');
    await userEvent.click(submitButton);
 
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
    });
 
    // Reset the mock for the next test
    mockDispatch.mockClear();
 
    // Now test that clearing a field makes the form invalid again
    await userEvent.clear(firstNameInput);
    await userEvent.click(submitButton);
    expect(mockDispatch).not.toHaveBeenCalled();
  });

  it('validates first name format', async () => {
    renderComponent();
    const firstNameInput = screen.getByRole('textbox', { name: /First Name/i });
    
    await userEvent.type(firstNameInput, 'John');
    await userEvent.clear(firstNameInput);
    await userEvent.type(firstNameInput, '123'); 
    await userEvent.tab();
    
    await waitFor(() => {
      expect(screen.getByText('First Name is a required')).toBeInTheDocument();
    });
  });

  it('validates last name format', async () => {
    renderComponent();
    const lastNameInput = screen.getByRole('textbox', { name: /Last Name/i });
    await userEvent.type(lastNameInput, 'Doe');
    await userEvent.clear(lastNameInput);
    await userEvent.type(lastNameInput, '123');
    await userEvent.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Last Name is a required')).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    renderComponent();
    const emailInput = screen.getByRole('textbox', { name: /Email Address/i });
    
    await userEvent.type(emailInput, 'invalid-email');
    fireEvent.blur(emailInput);
    
    await waitFor(() => {
      expect(screen.getByText(/Input a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates password requirements', async () => {
    renderComponent();
    const passwordInput = screen.getByTestId('password');
    
    await userEvent.type(passwordInput, 'weak');
    fireEvent.blur(passwordInput);
    
    await waitFor(() => {
      const errorText = screen.getByText(/Password must be/i);
      expect(errorText).toBeInTheDocument();
      expect(errorText.textContent).toContain('at least 8 characters long');
      expect(errorText.textContent).toContain('uppercase letter');
      expect(errorText.textContent).toContain('number');
      expect(errorText.textContent).toContain('special character');
    });
  });

  it('validates password confirmation match', async () => {
    renderComponent();
    const passwordInput = screen.getByTestId('password');
    const confirmPasswordInput = screen.getByTestId('confirm_password');
    
    await userEvent.type(passwordInput, 'StrongPass1!');
    await userEvent.type(confirmPasswordInput, 'DifferentPass1!');
    await userEvent.tab();
    
    await waitFor(() => {
      expect(screen.getByText('Passwords do not match')).toBeInTheDocument();
    });
  });

  it('submits form successfully with valid data', async () => {
    const mockDispatch = vi.fn().mockResolvedValue({ status: 201 });
    store.dispatch = mockDispatch;
    renderComponent();
    
    await userEvent.type(screen.getByRole('textbox', { name: /First Name/i }), 'John');
    await userEvent.type(screen.getByRole('textbox', { name: /Last Name/i }), 'Doe');
    await userEvent.type(screen.getByRole('textbox', { name: /Email Address/i }), '<EMAIL>');
    await userEvent.type(screen.getByTestId('password'), 'StrongPass1!');
    await userEvent.type(screen.getByTestId('confirm_password'), 'StrongPass1!');
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    await userEvent.click(screen.getByTestId('privacy-policy-checkbox'));
    
    const submitButton = screen.getByRole('button', { name: /Continue/i });
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/auth/register/license');
    });
  });

  it('handles account under review response', async () => {
    const mockDispatch = vi.fn().mockResolvedValue({ 
      status: 403, 
      data: { data: { accountUnderReview: true } } 
    });
    store.dispatch = mockDispatch;
    renderComponent();
    
    await userEvent.type(screen.getByRole('textbox', { name: /First Name/i }), 'John');
    await userEvent.type(screen.getByRole('textbox', { name: /Last Name/i }), 'Doe');
    await userEvent.type(screen.getByRole('textbox', { name: /Email Address/i }), '<EMAIL>');
    await userEvent.type(screen.getByTestId('password'), 'StrongPass1!');
    await userEvent.type(screen.getByTestId('confirm_password'), 'StrongPass1!');
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    await userEvent.click(screen.getByTestId('privacy-policy-checkbox'));
    
    const submitButton = screen.getByRole('button', { name: /Continue/i });
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  it('handles verification email flow', async () => {
    const mockDispatch = vi.fn().mockResolvedValue({ 
      status: 201, 
      data: { data: { verificationEmailSent: true } } 
    });
    store.dispatch = mockDispatch;
    renderComponent();
    
    await userEvent.type(screen.getByRole('textbox', { name: /First Name/i }), 'John');
    await userEvent.type(screen.getByRole('textbox', { name: /Last Name/i }), 'Doe');
    await userEvent.type(screen.getByRole('textbox', { name: /Email Address/i }), '<EMAIL>');
    await userEvent.type(screen.getByTestId('password'), 'StrongPass1!');
    await userEvent.type(screen.getByTestId('confirm_password'), 'StrongPass1!');
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    await userEvent.click(screen.getByTestId('privacy-policy-checkbox'));
    
    const submitButton = screen.getByRole('button', { name: /Continue/i });
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText(/A verification code has been sent/i)).toBeInTheDocument();
    });
  });

  it('validates privacy policy checkbox separately', async () => {
    renderComponent();
    
    // Fill in all other required fields to isolate privacy policy validation
    await userEvent.type(screen.getByRole('textbox', { name: /First Name/i }), 'John');
    await userEvent.type(screen.getByRole('textbox', { name: /Last Name/i }), 'Doe');
    await userEvent.type(screen.getByRole('textbox', { name: /Email Address/i }), '<EMAIL>');
    await userEvent.type(screen.getByTestId('password'), 'StrongPass1!');
    await userEvent.type(screen.getByTestId('confirm_password'), 'StrongPass1!');
    
    // Check terms to isolate privacy policy validation
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    
    // Get the privacy policy checkbox and check/uncheck it to ensure it's touched
    const privacyCheckbox = screen.getByTestId('privacy-policy-checkbox');
    
    // First check it
    await userEvent.click(privacyCheckbox);
    
    // Then uncheck it to trigger validation error
    await userEvent.click(privacyCheckbox);
    
    // Tab away to ensure field is blurred
    await userEvent.tab();
    
    // Check for the error message
    await waitFor(() => {
      const errorElement = screen.getByTestId('privacy-policy-error');
      expect(errorElement).toBeInTheDocument();
      expect(errorElement).toHaveTextContent('You must accept the Privacy Policy to continue');
    });
    
    // Now check the privacy policy again
    await userEvent.click(privacyCheckbox);
    
    // Error should be gone
    await waitFor(() => {
      expect(screen.queryByTestId('privacy-policy-error')).not.toBeInTheDocument();
    });
  });

  it('validates terms and conditions checkbox separately', async () => {
    renderComponent();
    
    // Fill in all other required fields to isolate terms validation
    await userEvent.type(screen.getByRole('textbox', { name: /First Name/i }), 'John');
    await userEvent.type(screen.getByRole('textbox', { name: /Last Name/i }), 'Doe');
    await userEvent.type(screen.getByRole('textbox', { name: /Email Address/i }), '<EMAIL>');
    await userEvent.type(screen.getByTestId('password'), 'StrongPass1!');
    await userEvent.type(screen.getByTestId('confirm_password'), 'StrongPass1!');
    
    // Check privacy policy to isolate terms validation
    await userEvent.click(screen.getByTestId('privacy-policy-checkbox'));
    
    // Get the terms checkbox and check/uncheck it to ensure it's touched
    const termsCheckbox = screen.getByTestId('terms-checkbox');
    
    // First check it
    await userEvent.click(termsCheckbox);
    
    // Then uncheck it to trigger validation error
    await userEvent.click(termsCheckbox);
    
    // Tab away to ensure field is blurred
    await userEvent.tab();
    
    // Check for the error message
    await waitFor(() => {
      const errorElement = screen.getByTestId('terms-error');
      expect(errorElement).toBeInTheDocument();
    });
    
    // Now check the terms again
    await userEvent.click(termsCheckbox);
    
    // Error should be gone
    await waitFor(() => {
      expect(screen.queryByTestId('terms-error')).not.toBeInTheDocument();
    });
  });

  it('validates both checkboxes are required for form submission', async () => {
    const mockDispatch = vi.fn().mockResolvedValue({ status: 201 });
    store.dispatch = mockDispatch;
    renderComponent();
    
    // Fill in all text fields
    await userEvent.type(screen.getByRole('textbox', { name: /First Name/i }), 'John');
    await userEvent.type(screen.getByRole('textbox', { name: /Last Name/i }), 'Doe');
    await userEvent.type(screen.getByRole('textbox', { name: /Email Address/i }), '<EMAIL>');
    await userEvent.type(screen.getByTestId('password'), 'StrongPass1!');
    await userEvent.type(screen.getByTestId('confirm_password'), 'StrongPass1!');
    
    const submitButton = screen.getByRole('button', { name: /Continue/i });
    
    // Try to submit without any checkboxes - should not submit
    await userEvent.click(submitButton);
    expect(mockDispatch).not.toHaveBeenCalled();
    
    // Check only terms checkbox - should not submit
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    await userEvent.click(submitButton);
    expect(mockDispatch).not.toHaveBeenCalled();
    
    // Uncheck terms and check only privacy policy - should not submit
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    await userEvent.click(screen.getByTestId('privacy-policy-checkbox'));
    await userEvent.click(submitButton);
    expect(mockDispatch).not.toHaveBeenCalled();
    
    // Check both checkboxes - should submit successfully
    await userEvent.click(screen.getByTestId('terms-checkbox'));
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
    });
  });
});