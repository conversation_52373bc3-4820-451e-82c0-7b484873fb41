// RegisterSuccessPage.it.tsx
import { render, screen, waitFor } from "@testing-library/react";
import RegisterSuccessPage from "../../../../src/pages/auth/registration/RegisterSuccessPage";
import { BrowserRouter, useNavigate } from "react-router-dom";
import { expect, vi } from "vitest";
import { beforeEach, describe, it } from "vitest";

// Mock useNavigate
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: vi.fn(),
  };
});

// Utility to update window.location.search
const setSearchParams = (params: Record<string, string>) => {
  const query = new URLSearchParams(params).toString();
  window.history.pushState({}, "Test page", `?${query}`);
};

describe("RegisterSuccessPage", () => {
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    mockNavigate.mockClear();
  });

  it("redirects to signin when email is missing", async () => {
    setSearchParams({ success: "true" });
    render(
      <BrowserRouter>
        <RegisterSuccessPage />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/signin", { replace: true });
    });
  });

  it("redirects to signin when success is not 'true'", async () => {
    setSearchParams({ email: "<EMAIL>", success: "false" });
    render(
      <BrowserRouter>
        <RegisterSuccessPage />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/signin", { replace: true });
    });
  });

  it("renders success message when email and success are valid", () => {
    setSearchParams({ email: "<EMAIL>", success: "true" });
    render(
      <BrowserRouter>
        <RegisterSuccessPage />
      </BrowserRouter>
    );

    expect(screen.getByText("Registration was successful")).toBeInTheDocument();
    expect(screen.getByText(/it@example\.com/)).toBeInTheDocument();
  });
});
