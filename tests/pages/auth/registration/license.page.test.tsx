import React from 'react'
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { configureStore } from '@reduxjs/toolkit'
import '@testing-library/jest-dom'
import LicensePage from '../../../../src/pages/auth/registration/license.page'
import authReducer from '../../../../src/store/slicers/auth.slicer'
import therapistReducer from '../../../../src/store/slicers/therapist.slicer'
import sidebarReducer from '../../../../src/store/slicers/sidebar.slicer'
import notification from '../../../../src/utils/notification.util'
import userEvent from '@testing-library/user-event'

import * as therapistActions from '../../../../src/store/slicers/therapist.slicer'
vi.spyOn(therapistActions, 'registerTherapistAction').mockImplementation(() =>
	vi.fn()
)

vi.mock('../../../../src/utils/notification.util', () => ({
	__esModule: true,
	default: {
		error: vi.fn((msg) =>
			console.log('notification.error called with:', msg)
		),
	},
}))

vi.mock(
	'../../../../src/store/slicers/therapist.slicer',
	async (importOriginal) => {
		const actual = (await importOriginal()) as Record<string, unknown>
		return {
			...actual,
			registerTherapistAction: vi.fn(
				() => async () => Promise.resolve({ status: 200 })
			),
		}
	}
)

vi.mock('cleave.js/react', () => ({
	default: vi.fn(({ onChange, value, placeholder, 'data-testid': dataTestId }) => (
		<input
			data-testid={dataTestId}
			placeholder={placeholder}
			value={value}
			onChange={(e) => onChange && onChange(e)}
		/>
	)),
}))


vi.mock('@/components/forms/DateComponent', () => ({
  __esModule: true,
  default: ({ 'data-testid': testId, errorMessage, onChange, onBlur, value }: any) => (
    <>
      <input
        data-testid={testId}
        value={value}
        onChange={e => onChange?.(e.target.value)}
        onBlur={onBlur}
      />
      {errorMessage && (
        <div>
          {typeof errorMessage === 'string'
            ? errorMessage
            : Array.isArray(errorMessage)
            ? errorMessage.join(' ')
            : JSON.stringify(errorMessage)}
        </div>
      )}
    </>
  ),
}));


vi.mock('react-router-dom', async () => {
	const actual = await vi.importActual('react-router-dom')
	return {
		...actual,
		useNavigate: () => vi.fn(),
	}
})

vi.mock('@/utils/api.util', () => ({
	useApiClient: () => ({
		post: vi.fn().mockResolvedValue({ data: { Location: 'mock-file-location', upload_name: 'test.pdf' } })
	})
}))

vi.mock('@/utils/request.utils', () => ({
  request: {
    get: vi.fn().mockResolvedValue({
      data: [
        { name: 'California', abbrev: 'CA' },
        { name: 'New York', abbrev: 'NY' },
       
      ],
    }),
  },
}));


let store

beforeEach(() => {
	vi.spyOn(notification, 'error').mockClear()

	store = configureStore({
		reducer: {
			auth: authReducer,
			therapist: therapistReducer,
			sidebar: sidebarReducer,
		},
		preloadedState: {
			auth: {
				isAuthenticated: false,
				user: { id: '123', userToken: 'mock-token' },
				registration: {
					formContent: {},
					userRegistrationToken: 'mock-token',
					editFlag: false,
					fetchingPage: false,
					therapistMatchKey: null,
					matchedTherapists: [],
					patientAnswers: {},
					page: 1,
					savingTherapistInfo: false,
					shouldShowRegCompleteDialog: false
				},
			},
			therapist: {
				fetchingInfo: false,
				profileInfo: [],
				profilePageInfo: [
					{
						pageName: 'license',
						payloadInfo: { licenses: [] },
					},
				],
			},
			sidebar: { 
				on: false,
				registerTherapist: { 
					invalidMenus: [],
					openSubMenu: {}
				} 
			},
		},
	})
})

describe('License Page', () => {
	it('renders the license page title', () => {
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		)
		expect(screen.getByText('Your License(s)')).toBeInTheDocument()
	})

	it('validates required fields', async () => {
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		)
		const submitButton = screen.getByRole('button', { name: /Save/i })
		
		await userEvent.click(submitButton)
		
		await waitFor(() => {
			expect(screen.getByText(/License type is required/i)).toBeInTheDocument()
			expect(screen.getByText(/State is required/i)).toBeInTheDocument()
			expect(screen.getByText(/Board is required/i)).toBeInTheDocument()
			expect(screen.getByText(/License number is required/i)).toBeInTheDocument()
			expect(screen.getByText(/Issue date is required/i)).toBeInTheDocument()
			expect(screen.getByText(/Expiry date is required/i)).toBeInTheDocument()
		})
	})


it('validates license issue date correctly', async () => {
  render(
    <Provider store={store}>
      <BrowserRouter>
        <LicensePage />
      </BrowserRouter>
    </Provider>
  );

  const submitButton = screen.getByRole('button', { name: /Save/i });
  await userEvent.click(submitButton);

  await waitFor(() => {
    expect(screen.getByText(/Issue date is required/i)).toBeInTheDocument();
  });

  // simulate error message from invalid date
  const issueDateInput = screen.getByTestId('issue-date-0');
  expect(issueDateInput).toBeInTheDocument();

  // manually set a bad value
  fireEvent.change(issueDateInput, { target: { value: '13-32-2025' } });
  fireEvent.blur(issueDateInput);

 await waitFor(() => {
  expect(screen.getByText(/Invalid date/i)).toBeInTheDocument();
});
});

it('validates date fields', async () => {
	render(
		<Provider store={store}>
			<BrowserRouter>
				<LicensePage />
			</BrowserRouter>
		</Provider>
	)
	
	const issueDateInput = screen.getByTestId('issue-date-0')
	const expiryDateInput = screen.getByTestId('expiry-date-0')
	
	// Test invalid date format
	await userEvent.type(issueDateInput, '13-32-2023')
	await userEvent.type(expiryDateInput, '13-32-2023')
	await userEvent.tab() 		
	await waitFor(() => {
		const errors = screen.getAllByText(/Invalid date/i);
		expect(errors.length).toBeGreaterThanOrEqual(1);
	});
	
	// Test future issue date
	const futureDate = new Date()
	futureDate.setFullYear(futureDate.getFullYear() + 1)
	const futureDateString = `${futureDate.getMonth() + 1}-${futureDate.getDate()}-${futureDate.getFullYear()}`
	
	await userEvent.clear(issueDateInput)
	await userEvent.type(issueDateInput, futureDateString)
	await userEvent.tab() 		
	
	fireEvent.blur(issueDateInput)
	fireEvent.change(issueDateInput, { target: { value: futureDateString } })
	
	await waitFor(() => {
		const errorMessage = screen.getByText((content) => {
			return content.toLowerCase().includes('future');
		});
		expect(errorMessage).toBeInTheDocument();
	}, { timeout: 3000 })
	
	// Test expired license
	const pastDate = new Date()
	pastDate.setFullYear(pastDate.getFullYear() - 1)
	const pastDateString = `${pastDate.getMonth() + 1}-${pastDate.getDate()}-${pastDate.getFullYear()}`
	
	await userEvent.clear(expiryDateInput)
	await userEvent.type(expiryDateInput, pastDateString)
	await userEvent.tab()
	
	await waitFor(() => {
		expect(screen.getByText(/license appears to be expired/i)).toBeInTheDocument()
	})
})

	it('validates NPI number', async () => {
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		)		
		
		const submitButton = screen.getByRole('button', { name: /Save/i })		
		
		await userEvent.click(submitButton)	
		
		await waitFor(() => {
			expect(screen.getByText(/NPI number is required/i)).toBeInTheDocument()
		})	
		
		const npiInput = screen.getByRole('textbox', { name: /NPI number/i })
		await userEvent.type(npiInput, '123')
		await userEvent.tab()
				
		await waitFor(() => {
			expect(screen.getByText(/NPI number is invalid. Must be 10 digits/i)).toBeInTheDocument()
		})
		
		await userEvent.clear(npiInput)
		await userEvent.type(npiInput, '**********')
		await userEvent.tab()
		
		await waitFor(() => {
			expect(screen.getByText(/NPI number cannot start with zero/i)).toBeInTheDocument()
		})
		
		// Test all zeros
		await userEvent.clear(npiInput)
		await userEvent.type(npiInput, '**********')
		await userEvent.tab()
		
		await waitFor(() => {
			expect(screen.getByText(/NPI number cannot contain all zeros/i)).toBeInTheDocument()
		})
	})

	it('handles provisional license status', async () => {
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		)		
		
		const licenseTypeContainer = document.getElementById('licenses[0].license_type')
		expect(licenseTypeContainer).toBeInTheDocument()
		const licenseTypeInput = within(licenseTypeContainer!).getByRole('combobox')
		await userEvent.click(licenseTypeInput)
		await userEvent.click(screen.getByText('Licensed Clinical Mental Health Counselor / Professional Counselor'))
	
		const stateContainer = document.getElementById('licenses[0].license_state')
		expect(stateContainer).toBeInTheDocument()
		const stateInput = within(stateContainer!).getByRole('combobox')
		await userEvent.click(stateInput)
		await userEvent.type(stateInput, 'California{enter}')		
		
		const statusContainer = document.getElementById('licenses[0].license_status')
		expect(statusContainer).toBeInTheDocument()
		const statusInput = within(statusContainer!).getByRole('combobox')
		await userEvent.click(statusInput)
		await userEvent.click(screen.getByText('Provisional'))
		
		await waitFor(() => {
			expect(screen.getByText(/Full Name of Clinical Supervisor/i)).toBeInTheDocument()
			expect(screen.getByText(/Clinical Supervisor Email/i)).toBeInTheDocument()
			expect(screen.getByText(/Clinical Supervisor Phone/i)).toBeInTheDocument()
		})		
	
		const submitButton = screen.getByRole('button', { name: /Save/i })
		await userEvent.click(submitButton)
		
		await waitFor(() => {
			expect(screen.getByText(/Clinical supervisor name is required/i)).toBeInTheDocument()
			expect(screen.getByText(/Clinical supervisor email is required/i)).toBeInTheDocument()
			expect(screen.getByText(/Clinical supervisor phone is required/i)).toBeInTheDocument()
		})
	})

	it('handles suspended license status', async () => {
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		)		
		
		const statusSelect = screen.getByText(/License Status/i).closest('section')
		const statusInput = statusSelect?.querySelector('input')
		expect(statusInput).toBeInTheDocument()
		
		await userEvent.click(statusInput!)
		await userEvent.click(screen.getByText('Suspended'))
		
		await waitFor(() => {
			expect(screen.getByText(/Must have a non-suspended license/i)).toBeInTheDocument()
		})
	})

	it('handles disciplinary action', async () => {
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		)		
	
		const actionSelect = screen.getByText(/Have you ever been put on probation/i).closest('section')
		const actionInput = actionSelect?.querySelector('input')
		expect(actionInput).toBeInTheDocument()
		
		await userEvent.click(actionInput!)
		await userEvent.click(screen.getByText('Yes'))
		
		await waitFor(() => {
			expect(screen.getByText(/If prior disciplinary action/i)).toBeInTheDocument()
		})		
		
		const submitButton = screen.getByRole('button', { name: /Save/i })
		await userEvent.click(submitButton)
		
		await waitFor(() => {
			expect(screen.getByText(/Disciplinary action description is required/i)).toBeInTheDocument()
		})
	})

	it('submits form successfully with valid data', async () => {
		const mockRegisterAction = vi.fn().mockResolvedValue({ status: 200 });
		const registerSpy = vi.spyOn(therapistActions, 'registerTherapistAction')
			.mockImplementation(() => () => mockRegisterAction());
	
		render(
			<Provider store={store}>
				<BrowserRouter>
					<LicensePage />
				</BrowserRouter>
			</Provider>
		);
	
		const submitButton = screen.getByRole('button', { name: /Save/i });
		await userEvent.click(submitButton);
	
		
		await waitFor(() => {
			expect(registerSpy).toHaveBeenCalledTimes(1);
			expect(mockRegisterAction).toHaveBeenCalledTimes(1);
		});
	});
})
