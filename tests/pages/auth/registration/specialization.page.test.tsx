import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { MemoryRouter } from 'react-router-dom'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import configureStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import SpecializationPage from '../../../../src/pages/auth/registration/specialization.page'
import '@testing-library/jest-dom'
import { BrowserRouter } from 'react-router-dom'
import { configureStore as reduxjsConfigureStore } from '@reduxjs/toolkit'
import authReducer from '../../../../src/store/slicers/auth.slicer'
import therapistReducer from '../../../../src/store/slicers/therapist.slicer'
import sidebarReducer from '../../../../src/store/slicers/sidebar.slicer'
import userEvent from '@testing-library/user-event'

vi.mock('../../../../src/store/index', async () => {
	const actual = await vi.importActual('../../../../src/store/index')
	return {
		...actual,
		default: {
			...(actual.default || {}),
		},
	}
})

vi.mock('../../../../src/utils/notification.util', () => ({
	__esModule: true,
	default: {
		error: vi.fn(),
		success: vi.fn()
	}
}))

vi.mock('../../../../src/store/slicers/therapist.slicer', async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...(actual || {}),
        registerTherapistAction: vi.fn(() => async (dispatch) => {
            dispatch({
                type: 'therapist/registerTherapistAction',
                payload: { status: 200 },
            });
        }),
    };
});

const middlewares = [thunk]
const mockStore = configureStore(middlewares)
let store

beforeEach(() => {
	vi.clearAllMocks();
	store = mockStore({
		auth: {
			user: { id: '123', userToken: 'mock-token' },
			registration: {
				fetchingPage: false,
				formContent: {},
				therapistMatchKey: null,
				matchedTherapists: [],
				patientAnswers: {},
				userRegistrationToken: 'mock-token',
				editFlag: false,
				savingTherapistInfo: false,
				page: null
			},
		},
		sidebar: { 
			on: false,
			registerTherapist: {
				openSubMenu: {},
				invalidMenus: []
			}
		},
	});
})

vi.mock('@/configs/registration.configs', () => ({
	specialities: [
		{
			value: '1',
			label: 'Anxiety',
			children: [
				{ value: '1-1', label: 'General Anxiety' },
				{ value: '1-2', label: 'Social Anxiety' }
			]
		},
		{
			value: '2',
			label: 'Depression',
			children: [
				{ value: '2-1', label: 'Major Depression' },
				{ value: '2-2', label: 'Bipolar Depression' }
			]
		}
	]
}));

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
	const actual = await vi.importActual('react-router-dom');
	return {
		...actual,
		useNavigate: () => mockNavigate,
		useLocation: () => ({
			pathname: '/auth/register/specialization'
		})
	};
});

describe('Specialization Page', () => {
	it('renders the specialization page title', () => {
		render(
			<Provider store={store}>
				<MemoryRouter>
					<SpecializationPage />
				</MemoryRouter>
			</Provider>
		)

		expect(screen.getByText('Your Specialties')).toBeInTheDocument()
	})	
})

describe('SpecializationPage', () => {
	let store;

	beforeEach(() => {
		const initialState = {
			auth: {
				isAuthenticated: false,
				registration: {
					fetchingPage: false,
					formContent: {},
					therapistMatchKey: null,
					matchedTherapists: [],
					patientAnswers: {},
					userRegistrationToken: 'test-token',
					navToInvalidPage: false,
					shouldShowRegCompleteDialog: false,
					editFlag: false,
					savingTherapistInfo: false
				},
				user: null
			},
			sidebar: {
				on: false,
				registerTherapist: {
					openSubMenu: {},
					invalidMenus: []
				}
			},
			therapist: {
				fetchingInfo: false,
				profileInfo: [],
				profilePageInfo: []
			}
		};

		store = reduxjsConfigureStore({
			reducer: {
				auth: authReducer,
				therapist: therapistReducer,
				sidebar: sidebarReducer
			},
			preloadedState: initialState
		});
		mockNavigate.mockClear();
	});

	const renderComponent = () => {
		return render(
			<Provider store={store}>
				<BrowserRouter>
					<SpecializationPage />
				</BrowserRouter>
			</Provider>
		);
	};

	it('renders the specialization form correctly', () => {
		renderComponent();
		
		expect(screen.getByText('Your Specialties')).toBeInTheDocument();
		expect(screen.getByText(/select up to 5 major specialties/i)).toBeInTheDocument();
		expect(screen.getByText('Anxiety')).toBeInTheDocument();
		expect(screen.getByText('Depression')).toBeInTheDocument();
	});

	

	it('shows error when no sub-specialty is selected', async () => {
		renderComponent();
		
		// Select a specialty without selecting sub-specialty
		const anxietyCheckbox = screen.getByLabelText('Anxiety');
		await userEvent.click(anxietyCheckbox);
		
		await waitFor(() => {
			expect(screen.getByText(/Must choose at least 1 sub-specialty/i)).toBeInTheDocument();
		});
	});

	it('limits selection to 5 specialties', async () => {
		renderComponent();
		
		const anxietyCheckbox = screen.getByLabelText('Anxiety');
		const depressionCheckbox = screen.getByLabelText('Depression');
		
		await userEvent.click(anxietyCheckbox);
		expect(anxietyCheckbox).not.toBeDisabled();
		
		await userEvent.click(depressionCheckbox);
		expect(depressionCheckbox).not.toBeDisabled();
	});

	it('submits form successfully with valid selections', async () => {
		const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
		store.dispatch = mockDispatch;
		renderComponent();
		
		const anxietyCheckbox = screen.getByLabelText('Anxiety');
		await userEvent.click(anxietyCheckbox);
		
		const generalAnxietyCheckbox = screen.getByLabelText('General Anxiety');
		await userEvent.click(generalAnxietyCheckbox);
		
		const submitButton = screen.getByRole('button', { name: /Save/i });
		await userEvent.click(submitButton);
		
		await waitFor(() => {
			expect(mockDispatch).toHaveBeenCalled();
			expect(mockNavigate).toHaveBeenCalledWith('/auth/register/rank-specialties');
		});
	});

	it('handles edit mode correctly', async () => {
		const editModeState = {
			auth: {
				isAuthenticated: true,
				registration: {
					fetchingPage: false,
					formContent: {
						specialization: {
							'1': ['1-1']
						}
					},
					therapistMatchKey: null,
					matchedTherapists: [],
					patientAnswers: {},
					editFlag: true,
					savingTherapistInfo: false,
					page: null,
					userRegistrationToken: 'test-token'
				},
				user: {
					userToken: 'test-token'
				}
			},
			sidebar: {
				on: false,
				registerTherapist: {
					openSubMenu: {},
					invalidMenus: []
				}
			},
			therapist: {
				fetchingInfo: false,
				profileInfo: [],
				profilePageInfo: []
			}
		};

		store = reduxjsConfigureStore({
			reducer: {
				auth: authReducer,
				therapist: therapistReducer,
				sidebar: sidebarReducer
			},
			preloadedState: editModeState
		});
		
		const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
		store.dispatch = mockDispatch;
		renderComponent();		
		
		expect(screen.getByLabelText('Anxiety')).toBeChecked();
		expect(screen.getByLabelText('General Anxiety')).toBeChecked();
		
		const submitButton = screen.getByRole('button', { name: /Save/i });
		await userEvent.click(submitButton);
		
		await waitFor(() => {
			expect(mockDispatch).toHaveBeenCalled();
			expect(mockNavigate).toHaveBeenCalledWith('/auth/register/rank-specialties');
		});
	});

	it('handles invalid page navigation', async () => {
		const mockDispatch = vi.fn().mockResolvedValue({ status: 200 });
		store.dispatch = mockDispatch;
		renderComponent();		
	
		const anxietyCheckbox = screen.getByLabelText('Anxiety');
		await userEvent.click(anxietyCheckbox);
		
		const generalAnxietyCheckbox = screen.getByLabelText('General Anxiety');
		await userEvent.click(generalAnxietyCheckbox);
		
		const submitButton = screen.getByRole('button', { name: /Save/i });
		await userEvent.click(submitButton);
		
		await waitFor(() => {
			expect(mockDispatch).toHaveBeenCalled();
			expect(mockNavigate).toHaveBeenCalledWith('/auth/register/rank-specialties');
		});
	});
});

