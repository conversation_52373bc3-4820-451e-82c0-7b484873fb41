import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, beforeEach, afterEach, vi, expect, Mock } from 'vitest';
import Page from '../../../../src/pages/auth/registration/next-therapist-plan.page';
import * as reactRedux from 'react-redux';
import { MemoryRouter, useNavigate } from 'react-router-dom';
import notification from '../../../../src/utils/notification.util';

vi.mock('react-redux', async () => {
  const actual = await vi.importActual<typeof reactRedux>('react-redux');
  return {
    ...actual,
    useSelector: vi.fn(),
    useDispatch: vi.fn(),
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    useLocation: () => ({ pathname: '/auth/register/next-therapist-plan' }),
  };
});

vi.mock('@/utils/notification.util', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}));

describe('NextTherapistPlan Page Component', () => {
  const mockDispatch = vi.fn();
  const mockNavigate = vi.fn();

  const renderPage = () => render(<Page />, { wrapper: MemoryRouter });

  beforeEach(() => {
    vi.clearAllMocks();

    Object.defineProperty(window, 'location', {
      value: {
        ...window.location,
        search: '?success=true&session_id=abc123',
        pathname: '/auth/register/next-therapist-plan',
        origin: 'http://localhost',
      },
      writable: true,
    });

    window.history.replaceState = vi.fn();

    (reactRedux.useDispatch as Mock).mockReturnValue(mockDispatch);
    (reactRedux.useSelector as Mock).mockImplementation((selectorFn: any) =>
      selectorFn({
        auth: {
          registration: {
            formContent: {
              'next-therapist-plan': {
                subscriptionPlanId: 'plan_123',
                subscriptionType: 'monthly',
                therapistSubscriptionId: 'sub_123',
                subscriptionSuccess: true,
              },
            },
            userRegistrationToken: 'token_abc',
            navToInvalidPage: false,
            shouldShowRegCompleteDialog: false,
          },
        },
        sidebar: {
          registerTherapist: {
            invalidMenus: [],
          },
        },
      })
    );

    (useNavigate as Mock).mockReturnValue(mockNavigate);

    mockDispatch.mockResolvedValue({
      data: [
        { id: 'plan_monthly', name: 'Monthly Plan' },
        { id: 'plan_annual', name: 'Annual Plan' },
      ],
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders subscription plans', async () => {
    renderPage();
    await waitFor(() => {
      expect(screen.getByText(/Monthly Plan/i)).toBeInTheDocument();
      expect(screen.getByText(/Annual Plan/i)).toBeInTheDocument();
    });
  });

  it('dispatches select action when plan is clicked', async () => {
    renderPage();
    await waitFor(() => {
      const selectButtons = screen.getAllByRole('button', { name: /Select/i });
      expect(selectButtons.length).toBeGreaterThan(0);
      fireEvent.click(selectButtons[0]);
    });
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalled();
    });
  });

  it('preselects plan from pageContent on mount', async () => {
    renderPage();
    await waitFor(() => {
      expect(screen.getByText(/Monthly Plan/i)).toBeInTheDocument();
    });
  });

  it('shows loader when loading is true', async () => {
    (reactRedux.useSelector as Mock).mockImplementation((selectorFn: any) =>
      selectorFn({
        auth: {
          registration: {
            formContent: {},
            userRegistrationToken: 'token_abc',
            navToInvalidPage: false,
            shouldShowRegCompleteDialog: false,
          },
        },
        sidebar: { registerTherapist: { invalidMenus: [] } },
      })
    );

    let resolveFn: any;
    mockDispatch.mockImplementationOnce(() => new Promise((resolve) => (resolveFn = resolve)));

    renderPage();
    expect(await screen.findByRole('status')).toBeInTheDocument();
    resolveFn({ data: [] });
  });

  it('shows error when no subscription plans are found', async () => {
    const errorMock = vi.spyOn(notification, 'error');
    mockDispatch.mockResolvedValue({ data: [] });
    renderPage();
    await waitFor(() => {
      expect(errorMock).toHaveBeenCalledWith('No active subscription plans found');
    });
  });

  it('disables Save & Continue button during loading', async () => {
    let resolveFn: any;
    mockDispatch.mockImplementationOnce(() => new Promise((resolve) => (resolveFn = resolve)));
    renderPage();
    const continueBtn = await screen.findByRole('button', { name: /Save & Continue/i });
    expect(continueBtn).toBeDisabled();
    resolveFn({ data: [] });
  });
});
