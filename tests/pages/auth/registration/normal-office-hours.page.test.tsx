import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import Page from '../../../../src/pages/auth/registration/normal-office-hours.page';
import * as therapistSlicer from '../../../../src/store/slicers/therapist.slicer';
import * as notificationUtil from '../../../../src/utils/notification.util';
import { getNextInvalidPage } from "../../../../src/utils/app.util";

const openSpy = vi.fn();
const closeSpy = vi.fn();
vi.mock("react-stately", async () => {
  const actual = await vi.importActual("react-stately");
  return {
    ...actual,
    useOverlayTriggerState: () => ({
      isOpen: false,
      open: openSpy,
      close: closeSpy,
    }),
  };
});

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

const mockNavigate = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});


vi.mock('@/components/ToolbarComponent', () => ({
  default: () => <div>ToolbarComponent</div>,
}));
vi.mock('@/components/office-hours/office-hours.component', () => ({
  __esModule: true,
  default: ({ onSave }: any) => (
    <button data-testid="valid-save" onClick={() => onSave({ e: { preventDefault: () => {} }, isPageValid: true })}>
      Save & Continue
    </button>
  ),
}));
vi.mock('@/components/dialogs/NormalDialog', () => ({
  default: (props: any) => (
    <div>
      NormalDialog
      <button data-testid="close-dialog" onClick={props.onClose}>
        X
      </button>
    </div>
  ),
}));

vi.mock('@/utils/notification.util', async () => {
  const actual = await vi.importActual('@/utils/notification.util');
  return {
    ...actual,
    default: {
      error: vi.fn(),
    },
  };
});

describe('Page Component', () => {
  let store: any;
  let dispatchSpy: any;

  beforeEach(() => {
    store = mockStore({
      auth: {
        registration: {
          formContent: {
            officeHours: {
              activeTimes: {},
              timezone: null,
              appointmentMethod: null,
            },
          },
          userRegistrationToken: 'token123',
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });
    dispatchSpy = vi.spyOn(store, 'dispatch');
  });

  it('renders ToolbarComponent and NormalDialog', () => {
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/officeHours']}>
          <Routes>
            <Route path="/auth/register/:pageId" element={<Page />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('ToolbarComponent')).toBeInTheDocument();
    expect(screen.getByText('NormalDialog')).toBeInTheDocument();
  });

  it('calls onSave and dispatches actions', async () => {
    const registerTherapistActionSpy = vi
      .spyOn(therapistSlicer, 'registerTherapistAction')
      .mockImplementation(() => () => Promise.resolve({ status: 200 }));

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/officeHours']}>
          <Routes>
            <Route path="/auth/register/:pageId" element={<Page />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    fireEvent.click(screen.getByText('Save & Continue'));

    await waitFor(() => {
      expect(registerTherapistActionSpy).toHaveBeenCalled();
      expect(dispatchSpy).toHaveBeenCalled();
    });
  });

  it('shows error notification when userRegistrationToken is missing', async () => {
    store = mockStore({
      auth: {
        registration: {
          formContent: {
            officeHours: {
              activeTimes: {},
              timezone: null,
              appointmentMethod: null,
            },
          },
          userRegistrationToken: null,
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/officeHours']}>
          <Routes>
            <Route path="/auth/register/:pageId" element={<Page />} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );

    fireEvent.click(screen.getByText('Save & Continue'));

    await waitFor(() => {
      expect(notificationUtil.default.error).toHaveBeenCalledWith('User Token is required');
    });
  });

  it('navigates to next invalid page when navToInvalidPage is true and nextInvalidPage exists', async () => {
  const store = mockStore({
    auth: {
      registration: {
        formContent: {},
        userRegistrationToken: 'token123',
        navToInvalidPage: true,
        shouldShowRegCompleteDialog: false,
      },
    },
    sidebar: {
      registerTherapist: {
        invalidMenus: ['contact'],
      },
    },
  });

  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={['/auth/register/officeHours']}>
        <Routes>
          <Route path="/auth/register/:pageId" element={<Page />} />
        </Routes>
      </MemoryRouter>
    </Provider>
  );

  fireEvent.click(screen.getByText("Save & Continue"));

  await waitFor(() => {
    expect(mockNavigate).toHaveBeenCalledWith('/auth/register/waitlist-notifications');
  });
});

it("calls infoState.open when info icon is clicked", () => {
  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={["/auth/register/officeHours"]}>
        <Routes>
          <Route path="/auth/register/:pageId" element={<Page />} />
        </Routes>
      </MemoryRouter>
    </Provider>
  );

  fireEvent.click(screen.getByTestId("info-icon-open"));
  expect(openSpy).toHaveBeenCalled();
});

it("calls infoState.close when dialog onClose is clicked", () => {
  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={["/auth/register/officeHours"]}>
        <Routes>
          <Route path="/auth/register/:pageId" element={<Page />} />
        </Routes>
      </MemoryRouter>
    </Provider>
  );

  fireEvent.click(screen.getByTestId("close-dialog"));
  expect(closeSpy).toHaveBeenCalled();
});

it("adds pageId to invalidMenus when isPageValid=false", async () => {
  // 1. Reset modules so our new mock takes effect
  vi.resetModules();

  // 2. Re-mock office-hours to be the invalid-save version
  vi.doMock("@/components/office-hours/office-hours.component", () => ({
    __esModule: true,
    default: ({ onSave }: any) => (
      <button data-testid="invalid-save" onClick={() => onSave({ e: { preventDefault: () => {} }, isPageValid: false })}>
        Save Invalid
      </button>
    ),
  }));

  // 3. Re-import Page (after mocks)
  const { default: Page } = await import("../../../../src/pages/auth/registration/normal-office-hours.page");

  // 4. Build a fresh store
  const testStore = mockStore({
    auth: {
      registration: {
        formContent: {},
        userRegistrationToken: "token123",
        navToInvalidPage: false,
        shouldShowRegCompleteDialog: false,
      },
    },
    sidebar: {
      registerTherapist: { invalidMenus: [] },
    },
  });

  // 5. Render
  render(
    <Provider store={testStore}>
      <MemoryRouter initialEntries={["/auth/register/officeHours"]}>
        <Routes>
          <Route path="/auth/register/:pageId" element={<Page />} />
        </Routes>
      </MemoryRouter>
    </Provider>
  );

  // 6. Click the invalid-save button
  fireEvent.click(screen.getByTestId("invalid-save"));

  // 7. Assert the sidebar action contains "officeHours"
  await waitFor(() => {
    expect(testStore.getActions()).toContainEqual(
      expect.objectContaining({
        type: "sidebar/updateSidebarState",
        payload: {
          key: "registerTherapist",
          value: expect.objectContaining({
            invalidMenus: expect.arrayContaining(["officeHours"]),
          }),
        },
      })
    );
  });
});

 it("navigates to next invalid page when navToInvalidPage is true and nextInvalidPage exists", async () => {
  // 1) Spy on app.util
  vi.spyOn(getNextInvalidPage as any, "apply").mockImplementation(() => "profile");
  // 2) Build store with navToInvalidPage = true
  const store = mockStore({
    auth: {
      registration: {
        formContent: {},
        userRegistrationToken: "token123",
        navToInvalidPage: true,
        shouldShowRegCompleteDialog: false,
      },
    },
    sidebar: {
      registerTherapist: {
        invalidMenus: ["profile"],
      },
    },
  });
  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={["/auth/register/officeHours"]}>
        <Routes>
          <Route path="/auth/register/:pageId" element={<Page />} />
        </Routes>
      </MemoryRouter>
    </Provider>
  );

  fireEvent.click(screen.getByText("Save & Continue"));

  await waitFor(() => {
    expect(mockNavigate).toHaveBeenCalledWith("/auth/register/profile");
  });
});
});



