import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import ProfilePage from '../../../../src/pages/auth/registration/profile.page';
import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../../../../src/store/slicers/auth.slicer';
import therapistReducer from '../../../../src/store/slicers/therapist.slicer';
import sidebarReducer from '../../../../src/store/slicers/sidebar.slicer';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      pathname: '/auth/register/profile'
    })
  };
});

vi.mock('compressorjs', () => {
  return {
    default: vi.fn((file, options) => {
      options.success(file);
    })
  };
});

describe('ProfilePage', () => {
  let store;

  beforeEach(() => {
    const initialState = {
      auth: {
        isAuthenticated: false,
        registration: {
          fetchingPage: false,
          formContent: {
            'create-account': {
              first_name: 'John',
              last_name: 'Doe',
              email: '<EMAIL>',
              password: 'Password123!'
            }
          },
          therapistMatchKey: null,
          matchedTherapists: [],
          patientAnswers: {},
          userRegistrationToken: 'test-token',
          savingTherapistInfo: false,
          currentPage: null,
          editFlag: false,
          navToInvalidPage: false,
          showRegCompleteDialog: false,
          shouldShowRegCompleteDialog: false
        },
        user: null
      },
      sidebar: {
        on: false,
        registerTherapist: {
          openSubMenu: {},
          invalidMenus: []
        }
      },
      therapist: {
        fetchingInfo: false
      }
    };

    store = configureStore({
      reducer: {
        auth: authReducer,
        therapist: therapistReducer,
        sidebar: sidebarReducer
      },
      preloadedState: initialState
    });
    mockNavigate.mockClear();
  });

  const renderComponent = () => {
    return render(
      <Provider store={store}>
        <BrowserRouter>
          <ProfilePage />
        </BrowserRouter>
      </Provider>
    );
  };

  it('renders the profile form correctly', async () => {
    renderComponent();
    
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByLabelText(/First Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Last Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/i)).toBeInTheDocument();
    expect(screen.getByText(/Date of Birth/i)).toBeInTheDocument();
    expect(screen.getByText('Enter Your Education Experience')).toBeInTheDocument();
    expect(screen.getByText(/Experience Years/i)).toBeInTheDocument();
  });

  it('validates education fields', async () => {
    renderComponent();
    const submitButton = screen.getByRole('button', { name: /Save/i });
    
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/College name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Degree type is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Graduation year is required/i)).toBeInTheDocument();
    });
  });

  it('validates graduation year', async () => {
    renderComponent();
    const yearInput = screen.getByLabelText(/Year Graduated/i);
    
    await userEvent.type(yearInput, '1800');
    
    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid year/i)).toBeInTheDocument();
    });
    
    await userEvent.clear(yearInput);
    await userEvent.type(yearInput, '2050');
    
    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid year/i)).toBeInTheDocument();
    });
  });

  

  it('validates identity selection', async () => {
    renderComponent();
    const submitButton = screen.getByRole('button', { name: /Save/i });
    
    await userEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Identify is a required/i)).toBeInTheDocument();
    });    
    
    const otherRadio = screen.getByLabelText('Other (Please specify)');
    await userEvent.click(otherRadio);
    
    await waitFor(() => {
      expect(screen.getByText(/Specify other Identify/i)).toBeInTheDocument();
    });
  });

}); 