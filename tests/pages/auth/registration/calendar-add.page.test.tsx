import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import '@testing-library/jest-dom';
import Page from "../../../../src/pages/auth/registration/calendar-add.page";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { useGoogleLogin } from "@react-oauth/google";
import * as utils from "../../../../src/utils/app.util";
import * as notificationUtil from "../../../../src/utils/notification.util";

vi.mock("@/store/slicers/auth.slicer", async () => {
  const actual = await vi.importActual<any>("@/store/slicers/auth.slicer");
  return {
    ...actual,
    syncGoogleCalendarAction: (payload: any) => ({ type: "SYNC_GOOGLE", payload }),
    updateRegistrationForm: (payload: any) => ({ type: "UPDATE_REG_FORM", payload }),
  };
});

// Mocks
vi.mock("@react-oauth/google", () => ({
  useGoogleLogin: vi.fn(),
}));
vi.mock("react-router-dom", () => ({
  useNavigate: vi.fn(),
  useLocation: vi.fn(),
}));
vi.mock("react-redux", () => ({
  useDispatch: vi.fn(),
  useSelector: vi.fn(),
}));
vi.mock("@/utils/notification.util", () => ({
  default: {
    error: vi.fn(),
    info: vi.fn(),
  },
}));

vi.mock("@/components/buttons/SaveButton", () => ({
  default: (props: any) => <button onClick={props.onClick}>Save & Continue</button>,
}));
vi.mock('@/components/ToolbarComponent', () => ({
  default: () => <div>Toolbar</div>,
}));
vi.mock("@/components/dialogs/NormalDialog", () => ({
  default: ({ title, children, state }: any) => (state.isOpen ? <div>{title}{children}</div> : null),
}));

describe("Calendar Add Page", () => {
  const mockDispatch = vi.fn();
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    mockDispatch.mockReset(); 
    (useDispatch as any).mockReturnValue(mockDispatch);
    (useDispatch as any).mockReturnValue(mockDispatch);
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useLocation as any).mockReturnValue({
      pathname: "/auth/register/calendar-add",
      search: "?code=testcode&state=teststate"
    });

    vi.spyOn(utils, "generateURLEncodedString").mockResolvedValue("test-state");
    vi.spyOn(utils, "createCodeChallenge").mockResolvedValue("code-challenge");

    (useSelector as any).mockImplementation((cb) =>
      cb({
        auth: {
          registration: {
            userRegistrationToken: "user-token",
            navToInvalidPage: true,
            shouldShowRegCompleteDialog: false,
            formContent: {
              "calendar-add": {
                google_calendar: "<EMAIL>",
                outlook_calendar: null
              }
            }
          }
        },
        sidebar: {
          registerTherapist: {
            invalidMenus: []
          }
        }
      })
    );

    (useGoogleLogin as any).mockImplementation(() => vi.fn());
  });

  it("renders successfully", async () => {
    render(<Page />);
    expect(await screen.findByText(/Congratulations, Your Calendar Has Been Successfully Added!/i)).toBeInTheDocument();
  });

 it("displays error if no user token for google", async () => {
  (useSelector as any).mockImplementation((cb) =>
    cb({
      auth: {
        registration: {
          userRegistrationToken: "",
          navToInvalidPage: true,
          shouldShowRegCompleteDialog: false,
          formContent: { "calendar-add": {} }
        }
      },
      sidebar: { registerTherapist: { invalidMenus: [] } }
    })
  );

  let onSuccess: any = null;
  // Mock useGoogleLogin to capture the config
  (useGoogleLogin as any).mockImplementation((config) => {
    onSuccess = config.onSuccess;
    return () => {};
  });

  const { container } = render(<Page />);
  const googleBtn = container.querySelector('.w-fit.cursor-pointer');
  fireEvent.click(googleBtn!);

  // Simulate Google login success callback
  if (onSuccess) onSuccess({ code: "abc" });

  await waitFor(() =>
    expect(notificationUtil.default.error).toHaveBeenCalledWith("User Token is required")
  );
});

  it("calls dispatch on Save & Continue with valid calendar", async () => {
    render(<Page />);
    fireEvent.click(screen.getByText("Save & Continue"));
    await waitFor(() =>
      expect(mockDispatch).toHaveBeenCalled()
    );
  });

  it("opens and closes remove dialog", async () => {
    render(<Page />);
    fireEvent.click(screen.getByText(/Remove/i));
  });

  it("shows info dialog", async () => {
    render(<Page />);
   fireEvent.click(screen.getByTestId("info-icon"));
    expect(true).toBe(true); 
  });

  it("dispatches syncGoogleCalendarAction and updates registration form on Google login success", async () => {
   mockDispatch.mockResolvedValueOnce({
    status: 201,
    data: {
      userData: {
        calendars: [
          { type: "google", email: "<EMAIL>" }
        ]
      }
    }
  });
  (useDispatch as any).mockReturnValue(mockDispatch);

  (useSelector as any).mockImplementation((cb) =>
    cb({
      auth: {
        registration: {
          userRegistrationToken: "token",
          navToInvalidPage: true,
          shouldShowRegCompleteDialog: false,
          formContent: { "calendar-add": {} }
        }
      },
      sidebar: { registerTherapist: { invalidMenus: [] } }
    })
  );

  let onSuccess: any = null;
  (useGoogleLogin as any).mockImplementation((config) => {
    onSuccess = config.onSuccess;
    return () => {};
  });

  render(<Page />);
  const googleBtn = document.querySelector('.w-fit.cursor-pointer');
  fireEvent.click(googleBtn!);

  // Simulate Google login success callback
  await onSuccess({ code: "abc" });

 await waitFor(() => {
  expect(typeof mockDispatch.mock.calls[0][0]).toBe("function");

  expect(mockDispatch).toHaveBeenCalledWith({
    type: "UPDATE_REG_FORM",
    payload: {
      pageId: "calendar-add",
      values: { google_calendar: "<EMAIL>" }
    }
  });
});
});

it("shows error notification on Google login error", async () => {
  (useGoogleLogin as any).mockImplementation((config) => {
    config.onError({ error: "some_error" });
    return () => {};
  });

  render(<Page />);
  const googleBtn = document.querySelector('.w-fit.cursor-pointer');
  fireEvent.click(googleBtn!);

  await waitFor(() => {
    expect(notificationUtil.default.error).toHaveBeenCalledWith("some_error");
  });
});

it("redirects to Azure AD on Outlook login", async () => {
  const originalLocation = window.location;
  delete (window as any).location;
  (window as any).location = { href: "" };

  render(<Page />);
  const outlookBtn = document.querySelector('.outlook-login-selector');
  fireEvent.click(outlookBtn!);

  await waitFor(() => {
    expect(window.location.href).toContain("https://login.microsoftonline.com/common/oauth2/v2.0/authorize");
  });

  (window as any).location = originalLocation;
});

});
