

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import Page from '../../../../src/pages/auth/registration/rank-sub-specialties.page';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { specialities } from "../../../../src/configs/registration.configs";

import notificationUtil from '../../../../src/utils/notification.util';
import * as appUtil from '../../../../src/utils/app.util';
const mockNavigate = vi.fn();

vi.mock('react-router-dom', async (importOriginal) => {
  const actual = (await importOriginal()) as Record<string, unknown>;
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      pathname: '/auth/register/rank-sub-specialties',
    }),
    Link: (props: any) => <a {...props} />,
  };
});


vi.mock('@/utils/notification.util', () => ({
  default: {
    error: vi.fn(),
  },
}));

vi.mock('@/components/buttons/SaveButton', () => ({
  default: ({ onClick, disabled, value }: any) => (
    <button onClick={onClick} disabled={disabled}>
      {value}
    </button>
  ),
}));

vi.mock('@/components/ToolbarComponent', () => ({
  default: () => <div>Toolbar</div>,
}));

vi.mock('@/utils/app.util', () => ({
  getNextInvalidPage: vi.fn(() => null),
  validateTherapistRegistration: vi.fn(() => true),
}));

vi.mock("../../../../src/store/slicers/therapist.slicer", async (importOriginal) => {
  const actual = (await importOriginal()) as Record<string, unknown>;
  return {
    ...actual,
    registerTherapistAction: vi.fn(() => async () => Promise.resolve({ status: 200 })),
  };
});

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

describe('Page Component', () => {
  let store: any;

  beforeEach(() => {
    store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: {
              '1': ['101', '102'],
            },
            subSpecialties: {},
          },
          userRegistrationToken: 'test-token',
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
        user: {
          userToken: 'user-token',
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });
  });

  it('renders the component with necessary elements', () => {
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/subSpecialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Rank Sub-Specialties')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Drag and drop the rankings to indicate your preferences, with 1 being the strongest.'
      )
    ).toBeInTheDocument();
  });

  it('handles form submission with valid data', async () => {
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/subSpecialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    const saveButton = screen.getByText(/Save & Continue/i);
    fireEvent.click(saveButton);

    await waitFor(() => {
      const actions = store.getActions();
      expect(actions).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: expect.any(String),
          }),
        ])
      );
    });
  });

  it('shows error notification when user token is missing', async () => {
    store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: {
              '1': ['101', '102'],
            },
            subSpecialties: {},
          },
          userRegistrationToken: null,
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
        user: {
          userToken: null,
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/subSpecialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    const saveButton = screen.getByText(/Save & Continue/i);
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(notificationUtil.error).toHaveBeenCalledWith('User Token is required');
    });
  });


   it('displays info alert when no specialties are selected', async () => {
    store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: {},
           "rank-sub-specialties": {},
          },
          userRegistrationToken: 'test-token',
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
        user: {
          userToken: 'user-token',
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/rank-sub-specialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

     expect(await screen.findByText(/Specialties should be selected first in order to rank their respective sub-specialties. Please select your preferred/i)).toBeInTheDocument();

  });
 
it('disables SaveButton when editFlag is true and subSpecialitiesData.length >= 5 but not 5 ranked', () => {
  // Mock 6 sub-specialties selected
  const selectedSpecialities = {};
  specialities.slice(0, 1).forEach(spc => {
    selectedSpecialities[spc.value] = spc.children.slice(0, 6).map(child => child.value.toString());
  });

  const store = mockStore({
    auth: {
      registration: {
        formContent: {
          specialization: selectedSpecialities,
          "rank-sub-specialties": { 1: "101", 2: "102", 3: "103", 4: "104" }, // Only 4 ranked, but 6 available
        },
        userRegistrationToken: 'test-token',
        navToInvalidPage: false,
        shouldShowRegCompleteDialog: false,
        editFlag: true,
      },
      user: {
        userToken: 'user-token',
      },
    },
    sidebar: {
      registerTherapist: {
        invalidMenus: [],
      },
    },
  });

  render(
    <Provider store={store}>
      <MemoryRouter initialEntries={['/auth/register/rank-sub-specialties']}>
        <Page />
      </MemoryRouter>
    </Provider>
  );

  const saveButton = screen.getByText(/Save/i);
  expect(saveButton).toBeDisabled();
});

it('navigates to /modalities if editFlag is true after successful submit', async () => {
    // const mockNavigate = vi.fn();
    // vi.spyOn(reactRouterDom, 'useNavigate').mockReturnValue(mockNavigate);

    const spc = specialities[0];
    const childValues = spc.children.slice(0, 5).map(child => child.value.toString());
    const selectedSpecialities = { [spc.value]: childValues };
    const ranked = { 1: childValues[0], 2: childValues[1], 3: childValues[2], 4: childValues[3], 5: childValues[4] };

    const store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: selectedSpecialities,
            "rank-sub-specialties": ranked,
          },
          userRegistrationToken: 'test-token',
          navToInvalidPage: false,
          shouldShowRegCompleteDialog: false,
          editFlag: true,
        },
        user: {
          userToken: 'user-token',
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/rank-sub-specialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/auth/register/modalities');
    });
  });

  it('navigates to next invalid page if navToInvalidPage is true and nextInvalidPage exists', async () => {
     const spc = specialities[0];
    const childValues = spc.children.slice(0, 5).map(child => child.value.toString());
    const selectedSpecialities = { [spc.value]: childValues };
    const ranked = { 1: childValues[0], 2: childValues[1], 3: childValues[2], 4: childValues[3], 5: childValues[4] };

    const store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: selectedSpecialities,
            "rank-sub-specialties": ranked,
          },
          userRegistrationToken: 'test-token',
          navToInvalidPage: true,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
        user: {
          userToken: 'user-token',
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: ['modalities'],
        },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/rank-sub-specialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    const saveButton = screen.getByText('Save & Continue');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/auth/register/modalities');
    });

  });

  it('shows registration complete dialog if no invalid page and registration is complete', async () => {
    const validateTherapistRegistration = vi.spyOn(appUtil, 'validateTherapistRegistration').mockReturnValue(true);
    const spc = specialities[0];
    const childValues = spc.children.slice(0, 5).map(child => child.value.toString());
    const selectedSpecialities = { [spc.value]: childValues };
    const ranked = { 1: childValues[0], 2: childValues[1], 3: childValues[2], 4: childValues[3], 5: childValues[4] };

    const store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: selectedSpecialities,
            "rank-sub-specialties": ranked,
          },
          userRegistrationToken: 'test-token',
          navToInvalidPage: true,
          shouldShowRegCompleteDialog: true,
          editFlag: false,
        },
        user: {
          userToken: 'user-token',
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });

    store.dispatch = vi.fn();

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/rank-sub-specialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    const saveButton = screen.getByText('Save & Continue');
    fireEvent.click(saveButton);

    await waitFor(() => {
        expect(store.dispatch).toHaveBeenCalledWith(
            expect.objectContaining({
            type: "sidebar/updateSidebarState",
            payload: {
                key: "registerTherapist",
                value: expect.objectContaining({
                invalidMenus: expect.any(Array),
                }),
            },
            })
        );
});

    validateTherapistRegistration.mockRestore();
  });

  it('navigates to next page if registration not complete and no invalid page', async () => {
     const validateTherapistRegistration = vi.spyOn(appUtil, 'validateTherapistRegistration').mockReturnValue(false);

    const spc = specialities[0];
    const childValues = spc.children.slice(0, 5).map(child => child.value.toString());
    const selectedSpecialities = { [spc.value]: childValues };
    const ranked = { 1: childValues[0], 2: childValues[1], 3: childValues[2], 4: childValues[3], 5: childValues[4] };

    const store = mockStore({
      auth: {
        registration: {
          formContent: {
            specialization: selectedSpecialities,
            "rank-sub-specialties": ranked,
          },
          userRegistrationToken: 'test-token',
          navToInvalidPage: true,
          shouldShowRegCompleteDialog: false,
          editFlag: false,
        },
        user: {
          userToken: 'user-token',
        },
      },
      sidebar: {
        registerTherapist: {
          invalidMenus: [],
        },
      },
    });

    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/auth/register/rank-sub-specialties']}>
          <Page />
        </MemoryRouter>
      </Provider>
    );

    const saveButton = screen.getByText('Save & Continue');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/auth/register/modalities');
    });

    validateTherapistRegistration.mockRestore();
  });

});
