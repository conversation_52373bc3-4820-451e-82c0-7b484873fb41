import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { <PERSON><PERSON><PERSON>Router } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import EmailVerificationPage from "../../../src/pages/auth/EmailVerificationPage";
import { useApiClient } from "../../../src/utils/api.util";
import "@testing-library/jest-dom";
import { describe, it, expect, beforeEach, vi } from "vitest";

// Mock the API client
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

// Mock the router with importOriginal to preserve the actual implementation
vi.mock("react-router-dom", async () => {
  const actual = await import("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock navigate function
const mockNavigate = vi.fn();

describe("EmailVerificationPage", () => {
  const mockClient = {
    post: vi.fn(),
  };

  beforeEach(() => {
    (useApiClient as any).mockReturnValue(mockClient);
    mockNavigate.mockClear();
    mockClient.post.mockClear();
  });

  const renderComponent = () => {
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated: false }),
      },
    });

    return render(
      <Provider store={store}>
        <BrowserRouter>
          <EmailVerificationPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it("renders the email verification form", () => {
    renderComponent();
    expect(screen.getByText("Verify your email")).toBeInTheDocument();
    expect(screen.getByLabelText("E-mail")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Send Magic Link" })).toBeInTheDocument();
    expect(screen.getByText(/Enter your email address below/)).toBeInTheDocument();
  });

  it("focuses on email input when component mounts", () => {
    renderComponent();
    expect(screen.getByLabelText("E-mail")).toHaveFocus();
  });

  it("prevents form submission with empty email", () => {
    renderComponent();
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.click(submitButton);
    
    // Verify API was not called
    expect(mockClient.post).not.toHaveBeenCalled();
  });

  it("handles successful magic link request for email verification", async () => {
    mockClient.post.mockResolvedValueOnce({
      data: {
        message: "Verification link sent to your email.",
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Verification link sent to your email.")).toBeInTheDocument();
    });
    
    // Verify API call
    expect(mockClient.post).toHaveBeenCalledWith("/auth/email-verification", { email: "<EMAIL>" });
    
    // Check navigation after delay
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/login");
    }, { timeout: 3100 });
  });

  it("displays default success message when API response doesn't include message", async () => {
    mockClient.post.mockResolvedValueOnce({
      data: {},
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Magic link sent to your email.")).toBeInTheDocument();
    });
  });

  it("handles API error with specific error message", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        data: {
          message: "Email not registered",
        },
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Email not registered")).toBeInTheDocument();
    });
  });

  it("shows generic error message when API response is missing error message", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        data: {},
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Something went wrong.")).toBeInTheDocument();
    });
  });

  it("disables form submission while processing", async () => {
    // Create a delayed promise that won't resolve immediately
    mockClient.post.mockImplementationOnce(() => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({ data: { message: "Verification link sent to your email." } });
        }, 100);
      });
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    // Try clicking again immediately
    fireEvent.click(submitButton);
    
    // Should only be called once because second click should be ignored
    expect(mockClient.post).toHaveBeenCalledTimes(1);
  });

  it("clears the email field after successful submission", async () => {
    mockClient.post.mockResolvedValueOnce({
      data: {
        message: "Verification link sent to your email.",
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(emailInput).toHaveValue("");
    });
  });

  it("verifies the 'Go back to Log In Page' link", () => {
    renderComponent();
    
    const loginLink = screen.getByText("Log In Page");
    expect(loginLink).toBeInTheDocument();
    expect(loginLink.closest('a')).toHaveAttribute('href', '/auth/login');
  });
  
  it("shows copyright information with current year", () => {
    renderComponent();
    
    const copyrightText = screen.getByText((content) => {
      return content.includes("NextTherapist");
    });
    expect(copyrightText).toBeInTheDocument();
  });
});