import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import VerifyEmailPage from "../../../src/pages/auth/VerifyEmailPage";
import { YEAR } from "../../../src/pages/auth/constants";
import "@testing-library/jest-dom";
import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";
import { HttpStatusCode } from "axios";

// Define mocks outside
const mockPost = vi.fn();
const mockNavigate = vi.fn();

// Mock API client
vi.mock("@/utils/api.util", () => ({
  useApiClient: () => ({
    post: mockPost
  })
}));

// Mock react-router-dom
vi.mock("react-router-dom", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useNavigate: () => mockNavigate
  };
});

// Mock setTimeout without replacing the original implementation
const originalSetTimeout = global.setTimeout;

describe("VerifyEmailPage", () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    mockPost.mockReset();
    mockNavigate.mockReset();
    
    // Mock URL search params
    Object.defineProperty(window, 'location', {
      value: {
        search: '?token=test-token'
      },
      writable: true
    });

    // Restore the original setTimeout
    global.setTimeout = originalSetTimeout;
  });

  afterEach(() => {
    // Restore the original setTimeout after each test
    global.setTimeout = originalSetTimeout;
  });

  const renderComponent = () => {
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated: false })
      }
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <VerifyEmailPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it("renders the verification page with logo and copyright", () => {
    renderComponent();
    
    expect(screen.getByAltText("Next Therapists")).toBeInTheDocument();
    expect(screen.getByText(`© NextTherapist, ${YEAR}`)).toBeInTheDocument();
    expect(screen.getByText("Verify your email address.")).toBeInTheDocument();
    expect(screen.getByText(/Enter the 6-digit code sent to your email/)).toBeInTheDocument();
  });

  it("focuses on code input field on mount", () => {
    renderComponent();
    
    const codeInput = screen.getByLabelText("6-Digit Code");
    expect(document.activeElement).toBe(codeInput);
  });

  it("updates credential state when code input changes", () => {
    renderComponent();
    
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    expect(codeInput.value).toBe("123456");
  });

  it("prevents form submission when code is empty", async () => {
    renderComponent();
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    expect(mockPost).not.toHaveBeenCalled();
  });

  it("submits verification code and handles therapist role success response", async () => {
    // Set up successful response for therapist
    mockPost.mockResolvedValueOnce({
      status: HttpStatusCode.Created,
      data: {
        message: "Email verified successfully",
        role: "therapist"
      }
    });
    
    // Spy on setTimeout but don't execute callbacks immediately
    vi.spyOn(global, 'setTimeout');
    
    renderComponent();
    
    // Enter code and submit form
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    // // Verify API call
    // expect(mockPost).toHaveBeenCalledWith("/auth/verify-email", {
    //   code: "123456",
    //   token: "test-token"
    // });
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText(/Email verified successfully.*Redirecting to next page/)).toBeInTheDocument();
    });
    
    // Verify login redirect button appears
    expect(screen.getByText("Go back to")).toBeInTheDocument();
    expect(screen.getByText("Log In Page")).toBeInTheDocument();
    
    // Verify setTimeout was called
    expect(setTimeout).toHaveBeenCalled();
  });

  it("submits verification code and handles patient role success response", async () => {
    // Set up successful response for patient
    mockPost.mockResolvedValueOnce({
      status: HttpStatusCode.Created,
      data: {
        message: "Email verified successfully",
        role: "patient"
      }
    });
    
    renderComponent();
    
    // Enter code and submit form
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    // Check success message
    await waitFor(() => {
      expect(screen.getByText(/Email verified successfully.*Please log in from the mobile app/)).toBeInTheDocument();
    });
    
    // Verify login redirect button does NOT appear for patients
    expect(screen.queryByText("Go back to")).not.toBeInTheDocument();
  });

  it("handles API errors without specific messages", async () => {
    // Set up error response without specific message
    mockPost.mockRejectedValueOnce({
      response: {}
    });
    
    renderComponent();
    
    // Enter code and submit form
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    // Check for default error message
    await waitFor(() => {
      expect(screen.getByText("Unable to verify, try again later.")).toBeInTheDocument();
    });
  });

  it("handles non-201 success status codes", async () => {
    // Set up successful response with non-created status
    mockPost.mockResolvedValueOnce({
      status: HttpStatusCode.Ok,
    });
    
    renderComponent();
    
    // Enter code and submit form
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText("Something went wrong. Please try again.")).toBeInTheDocument();
    });
  });

  it("disables submit button and shows processing state during API call", async () => {
    // Create a manually controlled promise for the API call
    let resolveApiCall;
    const apiPromise = new Promise(resolve => {
      resolveApiCall = resolve;
    });
    
    // Setup mock with our controlled promise
    mockPost.mockReturnValueOnce(apiPromise);
    
    renderComponent();
    
    // Enter code and submit form
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    // Button should be disabled immediately after clicking
    expect(submitButton).toBeDisabled();
    
    // Now resolve the API call
    resolveApiCall({
      status: HttpStatusCode.Created,
      data: {
        message: "Email verified successfully",
        role: "therapist"
      }
    });
    
    // Verify button remains disabled after success
    await waitFor(() => {
      expect(submitButton).toBeDisabled();
    });
  });

  it("disables input field after successful verification", async () => {
    mockPost.mockResolvedValueOnce({
      status: HttpStatusCode.Created,
      data: {
        message: "Email verified successfully",
        role: "therapist"
      }
    });
    
    renderComponent();
    
    // Enter code and submit form
    const codeInput = screen.getByLabelText("6-Digit Code");
    fireEvent.change(codeInput, { target: { value: "123456" } });
    
    const submitButton = screen.getByText("Verify Email");
    fireEvent.click(submitButton);
    
    // Check input is disabled after verification
    await waitFor(() => {
      expect(codeInput).toBeDisabled();
    });
  });
});