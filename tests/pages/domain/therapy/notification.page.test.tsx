import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, vi, expect, beforeEach } from 'vitest';
import Page from '../../../../src/pages/domain/therapy/notifications/index.page';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import { fetchRegInfoAction } from '../../../../src/store/slicers/therapist.slicer';
import '@testing-library/jest-dom'

// mock useInView
vi.mock('react-intersection-observer', () => ({
  useInView: () => ({ ref: vi.fn() })
}));

vi.mock('@/store/slicers/therapist.slicer', () => ({
  fetchRegInfoAction: vi.fn(),
  registerTherapistAction: vi.fn(),
  sendSmsAction: vi.fn(),
}));

vi.mock('@/utils/notification.util', () => ({
  error: vi.fn(),
  success: vi.fn(),
}));

const middlewares = [thunk];
const mockStore = configureStore(middlewares);

let store: ReturnType<typeof mockStore>;

beforeEach(() => {
  // clear mocks
  vi.clearAllMocks();

  store = mockStore({
    auth: {
      user: {
        userToken: 'token123',
        phone: '+15551234567',
        phoneVerifiedAt: 'someDate',
      }
    }
  });

  (fetchRegInfoAction as any).mockImplementation(() => () => Promise.resolve({
    status: 200,
    data: { payloadInfo: { notification_preferences: [], booking_time_hour: '', phone_number: '' } }
  }));
});

async function setup() {
  render(
    <Provider store={store}>
      <Page />
    </Provider>
  );
  // wait initial useEffect fetch
  await waitFor(() => expect(fetchRegInfoAction).toHaveBeenCalled());
}

describe('Page Component', () => {

  it('renders header, form and save button disabled initially', async () => {
    await setup();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText(/How Would You Like To Be Notified/)).toBeInTheDocument();
    const saveBtn = screen.getByRole('button', { name: /Save/i });
    expect(saveBtn).toBeDisabled();
  });

});
