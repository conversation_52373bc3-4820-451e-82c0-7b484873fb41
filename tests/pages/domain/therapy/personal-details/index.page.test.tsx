import { describe, it, vi, beforeEach, expect, afterEach } from "vitest"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import { <PERSON><PERSON><PERSON>Router } from "react-router-dom"
import Page from "../../../../../src/pages/domain/therapy/personal-details/index.page"
import * as reduxHooks from "react-redux"
import userEvent from "@testing-library/user-event"
import '@testing-library/jest-dom'

// Mocking Redux
vi.mock("react-redux", async () => {
  const actual = await vi.importActual("react-redux")
  return {
    ...actual,
    useSelector: vi.fn(),
    useDispatch: vi.fn(),
  }
})

// Mock Repository
vi.mock("@/repositories/TherapistRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      update: vi.fn().mockResolvedValue({
        status: 200,
        data: {
          therapist: {
            id: "1",
            firstname: "Updated",
            lastname: "User",
            gender: "female",
            dob: "2000-01-01",
          },
        },
      }),
    })),
  }
})



describe("Page Component", () => {
  const dispatch = vi.fn()

  beforeEach(() => {
    vi.spyOn(reduxHooks, "useDispatch").mockReturnValue(dispatch)
    vi.spyOn(reduxHooks, "useSelector").mockReturnValue({
      user: {
        id: "1",
        firstname: "Test",
        lastname: "User",
        gender: "male",
        dob: "2000-01-01",
      },
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  const setup = () =>
    render(
      <BrowserRouter>
        <Page />
      </BrowserRouter>
    )

 it("renders form and inputs", () => {
  setup()
  expect(screen.getByLabelText("First Name")).toBeInTheDocument()
  expect(screen.getByLabelText("Last Name")).toBeInTheDocument()
  expect(screen.getByLabelText("Gender")).toBeInTheDocument()
  expect(screen.getByText("Date of Birth")).toBeInTheDocument()
  expect(screen.getByPlaceholderText("MM-DD-YYYY")).toBeInTheDocument()
})



  it("does not submit if user.id is missing", async () => {
    vi.spyOn(reduxHooks, "useSelector").mockReturnValue({ user: null })
    setup()

    fireEvent.click(screen.getByRole("button", { name: /save/i }))
    await waitFor(() => {
      expect(dispatch).not.toHaveBeenCalled()
    })
  })

  it("renders info alert with link to security page", () => {
    setup()
    expect(screen.getByText("Security Page")).toBeInTheDocument()
    expect(screen.getByRole("link", { name: /security page/i })).toHaveAttribute("href", "/security")
  })

    it("shows error messages for invalid names and future date", async () => {
    setup()

    const firstNameInput = screen.getByLabelText("First Name")
    const lastNameInput = screen.getByLabelText("Last Name")
    const dateInput = screen.getByPlaceholderText("MM-DD-YYYY")

    // Simulate invalid input
    await userEvent.clear(firstNameInput)
    await userEvent.type(firstNameInput, "123")

    await userEvent.clear(lastNameInput)
    await userEvent.type(lastNameInput, "123")

    Object.defineProperty(dateInput, "readOnly", { value: false })

    await userEvent.clear(dateInput)
    await userEvent.type(dateInput, "2999-01-01")
    fireEvent.blur(dateInput) 
  
    const saveButton = screen.getByRole("button", { name: /save/i })
    fireEvent.click(saveButton)


   await waitFor(() => {
    expect(screen.getByText(/first name should contain only letters/i)).toBeInTheDocument()
    expect(screen.getByText(/last name should contain only letters/i)).toBeInTheDocument()
  })
  })

it("submits form with valid values and dispatches update", async () => {
  setup()


  const firstNameInput = screen.getByLabelText("First Name")
  const lastNameInput = screen.getByLabelText("Last Name")
  const genderSelect = screen.getByLabelText("Gender")
  const dobInput = screen.getByPlaceholderText("MM-DD-YYYY") // <- use the placeholder

  await userEvent.clear(firstNameInput)
  await userEvent.type(firstNameInput, "John")

  await userEvent.clear(lastNameInput)
  await userEvent.type(lastNameInput, "Doe")

  await userEvent.selectOptions(genderSelect, "male")  
  Object.defineProperty(dobInput, "readOnly", { value: false })

  await userEvent.clear(dobInput)
  await userEvent.type(dobInput, "2000-01-01")
  fireEvent.blur(dobInput) 
  const saveButton = screen.getByRole("button", { name: /save/i })
  await userEvent.click(saveButton)

  await waitFor(() => {
    expect(dispatch).toHaveBeenCalled()
  })
})


it("disables save button if form is invalid", async () => {
  setup()

  const firstNameInput = screen.getByLabelText("First Name")
  await userEvent.clear(firstNameInput)

  const saveBtn = screen.getByRole("button", { name: /save/i })
  expect(saveBtn).toBeDisabled()
})

it("shows error when first or last name exceeds 20 characters", async () => {
 setup()

  const firstNameInput = screen.getByLabelText(/first name/i);
  const lastNameInput = screen.getByLabelText(/last name/i);

  fireEvent.change(firstNameInput, { target: { value: "a".repeat(21) } });
  fireEvent.change(lastNameInput, { target: { value: "b".repeat(21) } });

  fireEvent.submit(screen.getByRole("button", { name: /save/i }));

  await waitFor(() => {
    const errorMessages = screen.getAllByText(/character limit exceeded/i);
    expect(errorMessages).toHaveLength(2);
  });
});


})
