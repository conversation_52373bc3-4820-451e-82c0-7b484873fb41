import { render, screen } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import Dashboard from "../../../../src/pages/domain/therapy/dashboard.page";
import { describe, expect, it, vi } from "vitest";
import "@testing-library/jest-dom"


vi.mock("../../../../src/pages/auth/constants", () => ({
  LOGO_PATH: "mock-logo.png",
}));

vi.mock("react-intersection-observer", () => ({
  useInView: () => ({
    ref: vi.fn(),
  }),
}));

const renderWithRouter = (ui: React.ReactNode) => {
  return render(<BrowserRouter>{ui}</BrowserRouter>);
};

describe("Dashboard", () => {
  it("renders the dashboard layout", () => {
    renderWithRouter(<Dashboard />);
    expect(screen.getByAltText("logo")).toBeInTheDocument();
  });

  it("renders all dashboard links with correct labels and icons", () => {
    renderWithRouter(<Dashboard />);

    const links = [
      "Contact Info",
      "Your Profile",
      "Calendar & Appointments",
      "Subscriptions & Payment",
      "Reporting",
      "Password & Security",
      "Notifications",
      "Help Center",
    ];

    links.forEach((label) => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  it("renders logo with correct src", () => {
    renderWithRouter(<Dashboard />);
    const logo = screen.getByAltText("logo") as HTMLImageElement;
    expect(logo.src).toContain("mock-logo.png");
  });
});
