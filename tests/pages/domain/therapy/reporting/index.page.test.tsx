import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import Page from '../../../../../src/pages/domain/therapy/reporting/index.page'
import { Provider } from 'react-redux'
import store from '../../../../../src/store'
import * as requestUtils from '../../../../../src/utils/request.utils'
import * as notificationUtil from '../../../../../src/utils/notification.util'
import { <PERSON>rowserRouter } from 'react-router-dom'
import '@testing-library/jest-dom'

// Mock notification utils
vi.mock('@/utils/notification.util', () => ({
  default: {
    error: vi.fn(),
    success: vi.fn()
  }
}))

vi.mock('@/store/slicers/therapist.slicer', async (importOriginal) => {
  const actual = await importOriginal<any>();
  return {
    ...actual,
    __esModule: true,
    default: vi.fn((state = {}) => state),
   confirmPaymentAction: vi.fn(() => async () => {
      notificationUtil.default.success('Payment confirmed successfully')
      return { status: 200 }
    }),
  }
})

// Fix for React.forwardRef error in framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: React.forwardRef((props: any, ref: any) => <div {...props} ref={ref} />)
  }
}))

const mockAppointments = Array.from({ length: 6 }, (_, i) => ({
  id: i + 1,
  appointmentDate: new Date().toISOString(),
  description: 'Session',
  initial_appointment: 'first',
  cancelledAppointment: null,
  isBilled: false,
  past: true,
  patient: {
    id: i + 1,
    firstname: `John`,
    lastname: `Doe${i + 1}`,
    email: `john${i + 1}@example.com`,
    phone: '**********',
    dob: '1990-01-01',
    gender: 'male',
    address: {
      city: 'New York',
      state: 'NY',
      street: '1st Ave',
      country: 'USA',
    },
    bio: '',
    userProfile: null,
    cancelled: false,
  },
  formattedDateTime: '',
}))


const mockPayments = [
  {
    id: 1,
    createDate: new Date().toISOString(),
    paymentCreateDate: new Date().toISOString(),
    cost: 1000,
    currency: 'USD',
	confirmed: false,
    cancelled: false, 
    patient: {
      id: 1,
      firstname: 'Jane',
      lastname: 'Smith',
      email: '<EMAIL>',
      phone: '**********',
      dob: null,
      gender: null,
      userProfile: null
    }
  }
]

describe('Reporting Page', () => {
  beforeEach(() => {
    vi.spyOn(requestUtils.request, 'get').mockImplementation((url) => {
      if (url.includes('payments-appointments')) {
        return Promise.resolve({ data: mockPayments })
      }
	if (url.includes('appointments')) {
        return Promise.resolve({ data: mockAppointments })
      }
      if (url.includes('export')) {
        return Promise.resolve({ data: new Blob(['excel']) })
      }
      return Promise.resolve({ data: [] })
    })

  })

  const renderComponent = () =>
    render(
      <Provider store={store}>
        <BrowserRouter>
          <Page />
        </BrowserRouter>
      </Provider>
    )

  it('renders loading skeletons initially', async () => {
    renderComponent()
    expect(await screen.findByText(/Appointments/i)).toBeInTheDocument()
    expect(screen.getAllByText('Date & Time').length).toBeGreaterThan(0)
  })

  it('renders appointments and payments after loading', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText(/John Doe1/i)).toBeInTheDocument()
    //   expect(screen.getByText(/Jane Smith/i)).toBeInTheDocument()
    })
  })

  it('shows and hides more appointments', async () => {
    renderComponent()
    await waitFor(() => screen.getByText(/John Doe1/i))

    const showMore = screen.getByText('+ Show More')
    fireEvent.click(showMore)

    const showLess = await screen.findByText('Show Less')
    expect(showLess).toBeInTheDocument()
    fireEvent.click(showLess)
    expect(await screen.findByText('+ Show More')).toBeInTheDocument()
  })


  it('sorts payments', async () => {
    renderComponent()
    await waitFor(() => screen.getByText(/Jane Smith/i))

    const amountHeader = screen.getByText('Amount')
    fireEvent.click(amountHeader)
    fireEvent.click(amountHeader)
    expect(screen.getByText('$10.00')).toBeInTheDocument()
  })




})
