import { describe, it, expect, vi, beforeEach } from "vitest"
import { render, screen, fireEvent, waitFor } from "@testing-library/react"
import { Provider, useDispatch, useSelector } from "react-redux"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom"
import  store  from "../../../../../src/store/index"
import Page from "../../../../../src/pages/domain/therapy/calendars-appointments/index.page"
import "@testing-library/jest-dom"
import { useGoogleLogin } from "@react-oauth/google"
import * as therapistSlicer from "../../../../../src/store/slicers/therapist.slicer"
import notification from "../../../../../src/utils/notification.util"
import * as utils from "../../../../../src/utils/app.util"
import { act } from "react-dom/test-utils"


import * as therapistActions from "../../../../../src/store/slicers/therapist.slicer"

vi.spyOn(therapistActions, "syncOutlookCalendarAction").mockImplementation(() => async () => ({
  status: 201,
  data: { userData: { id: "mock-user" } },
}))


vi.spyOn(therapistSlicer, "syncGoogleCalendarAction").mockImplementation(() => async () => {
  return {
    status: 201,
    data: { userData: { id: "new-user" } },
  }
})

vi.mock("@react-oauth/google", () => ({
  useGoogleLogin: () => vi.fn(),
}))

vi.mock("react-redux", async () => {
  const actual = await vi.importActual<typeof import("react-redux")>("react-redux")
  return {
    ...actual,
    useDispatch: vi.fn(),
    useSelector: vi.fn(),
  }
})

const mockDispatch = vi.fn().mockResolvedValue({
  status: 200,
  data: { payloadInfo: {}, userData: { id: "mock-user" } },
})

vi.mock("@/repositories/TherapistRepository", () => ({
  default: vi.fn().mockImplementation(() => ({
    getdurationCost: vi.fn().mockResolvedValue({ data: { status: 200, data: {} } }),
  })),
}))
vi.mock("react-hot-toast", () => ({
  default: { dismiss: vi.fn() },
}))
vi.mock("formik", async () => {
  const actual = await vi.importActual("formik")
  return {
    ...actual,
    Formik: ({ children }: any) => <form>{children({ values: {}, errors: {}, handleChange: vi.fn(), handleSubmit: vi.fn() })}</form>,
  }
})

vi.mock("@react-oauth/google", () => ({
  useGoogleLogin: vi.fn(),
}))

const mockSetUser = vi.fn()
vi.mock("@/store/slicers/auth.slicer", async () => {
  const actual = await vi.importActual("@/store/slicers/auth.slicer")
  return {
    ...actual,
    setUser: (...args: any[]) => mockSetUser(...args),
  }
})

vi.mock("@/utils/notification.util", () => ({
  default: {
    error: vi.fn(),
    info: vi.fn(),
  },
}))

const renderPage = () =>
  render(
    <Provider store={store}>
      <BrowserRouter>
        <Page />
      </BrowserRouter>
    </Provider>,
  )

describe("Calendars & Appointments Page", () => {
  beforeEach(() => {
    vi.clearAllMocks()

     ;(useSelector as any).mockImplementation((cb) =>
    cb({
      auth: {
        user: {
          id: "mock-user",
          userToken: "mock-token",
          hasGoogleCalendar: false,
          hasOutlookCalendar: false,
          calendars: [],
        },
        registration: {
          formContent: {
            "calendars-appointments": {},
          },
        },
      },
      therapist: {
        fetchingInfo: false,
      },
    }),
  )

  ;(useDispatch as any).mockReturnValue(mockDispatch)

  
  })

  it("renders page title correctly", () => {
    renderPage()
    expect(screen.getByText("Calendars & Appointments")).toBeInTheDocument()
  })

it("shows Office Hours section and toggles correctly", async () => {
  renderPage()

  await waitFor(() =>
    expect(screen.queryByText(/Calendars & Appointments/i)).toBeInTheDocument()
  )

  const btn = screen.getByRole("button", {
    name: /View & Edit Office Hours/i,
  })
  fireEvent.click(btn)
 
  await waitFor(() =>
    expect(screen.getByText("Back")).toBeInTheDocument()
  )
})
  it("renders waitlist form with select inputs", async () => {
    renderPage()
    await waitFor(() => {
      expect(screen.getByText("Waitlist")).toBeInTheDocument()
    })
    expect(screen.getByText("Advance Notice")).toBeInTheDocument()
    expect(screen.getByText("Calendar Visibility")).toBeInTheDocument()
  })

  it("renders Save button", async () => {
    renderPage()
    await waitFor(() => {
      expect(screen.getByText("Save")).toBeInTheDocument()
    })
  })

it("handles Google login success", async () => {
  const mockTokens = { code: "mock-auth-code" }

  const mockDispatch = vi.fn().mockResolvedValueOnce({
    status: 201,
    data: { userData: { id: "new-user" } },
  })

  ;(useDispatch as any).mockReturnValue(mockDispatch)
  ;(useSelector as any).mockImplementation((cb) =>
    cb({
      auth: {
        user: { userToken: "mock-token", calendars: [], hasGoogleCalendar: false, hasOutlookCalendar: false },
        registration: { formContent: { "calendars-appointments": {} } },
      },
      therapist: { fetchingInfo: false },
    })
  )

  let onSuccessHandler: any
  ;(useGoogleLogin as any).mockImplementation(({ onSuccess }: any) => {
    onSuccessHandler = onSuccess
    return vi.fn()
  })

  renderPage()

  await waitFor(() => {
    onSuccessHandler(mockTokens)
  })

  expect(mockDispatch).toHaveBeenCalledWith(expect.any(Function))

})

it("ignores access_denied error from Google login", () => {
  const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {})

  const error = { error: "access_denied" }
  const googleLoginMock = vi.fn(({ onError }: any) => {
    onError(error)
  })

  ;(useGoogleLogin as any).mockImplementation(googleLoginMock)

  renderPage()

  expect(consoleSpy).toHaveBeenCalledWith(error)
  expect(notification.error).not.toHaveBeenCalled()
})

it("shows error notification for other Google login errors", () => {
  const error = { error: "something_failed" }

  const googleLoginMock = vi.fn(({ onError }: any) => {
    onError(error)
  })
  ;(useGoogleLogin as any).mockImplementation(googleLoginMock)

  renderPage()

  expect(notification.error).toHaveBeenCalledWith("something_failed")
})
})


describe("Outlook Calendar Integration", () => {
  beforeEach(() => {
    vi.clearAllMocks()

    window.history.pushState({}, "", "/calendars-appointments?code=test-code&state=test-state")
  localStorage.setItem("codeVerifier", "mockVerifier")
  localStorage.setItem("state", "test-state")
    ;(useDispatch as any).mockReturnValue(mockDispatch)
    ;(useSelector as any).mockImplementation((cb) =>
      cb({
        auth: {
          user: {
            userToken: "mock-token",
            calendars: [],
            hasGoogleCalendar: false,
            hasOutlookCalendar: false,
          },
          registration: {
            formContent: {
              "calendars-appointments": {},
            },
          },
        },
        therapist: { fetchingInfo: false },
      })
    )
  })

  it("should redirect to Outlook login URL", async () => {
  vi.spyOn(utils, "generateURLEncodedString").mockResolvedValue("mockValue")
vi.spyOn(utils, "createCodeChallenge").mockResolvedValue("mockChallenge")

  Object.defineProperty(window, "location", {
    writable: true,
    value: { href: "" },
  })

  renderPage()

  const outlookButton = await screen.findByRole("button", {
    name: /Sync Microsoft Calendar/i,
  })

  await act(async () => {
    fireEvent.click(outlookButton)
  })

  expect(window.location.href).toContain("login.microsoftonline.com")
})


  it("shows error if codeVerifier is missing", async () => {
    localStorage.removeItem("codeVerifier")
    localStorage.setItem("state", "expected-state")

    const params = new URLSearchParams("?code=test-code&state=expected-state")
    Object.defineProperty(window, "location", {
      writable: true,
      value: {
        search: params.toString(),
        pathname: "/calendars-appointments",
        replaceState: vi.fn(),
      },
    })

    renderPage()

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith("Something went wrong. Please try Logging in again.")
    })
  })

  it("shows error if state does not match", async () => {
    localStorage.setItem("codeVerifier", "mockVerifier")
    localStorage.setItem("state", "expected-state")

    const params = new URLSearchParams("?code=test-code&state=wrong-state")
    Object.defineProperty(window, "location", {
      writable: true,
      value: {
        search: params.toString(),
        pathname: "/calendars-appointments",
        replaceState: vi.fn(),
      },
    })

    renderPage()

    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith("Something went wrong. Please try Logging in again.")
    })
  })
})

