import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import configureStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import Page from '../../../../../src/pages/domain/therapy/subscriptions-payments/index.page'

// Mock components
vi.mock('@/components/buttons/AppButton', () => ({ default: ({ onClick, value, disabled }: any) => (
  <button disabled={disabled} onClick={onClick}>{value}</button>
)}))

vi.mock('@/components/buttons/SaveButton', () => ({ default: ({ disabled, loading, value }: any) => (
  <button disabled={disabled}>{loading ? 'Loading...' : value}</button>
)}))

vi.mock('@/components/forms/InputComponent', () => ({ default: ({ value = '', onChange, errorMessage }: any) => (
  <>
    <input value={value} onChange={(e) => onChange(e.target.value)} />
    {errorMessage && <span>{errorMessage}</span>}
  </>
)}))

vi.mock('@/components/forms/AppCheckBoxListComponent', () => ({
  default: ({ checked = [], onChange }: any) => (
    <div>
      <label>
        <input type="checkbox" checked={checked.includes("insurance")} onChange={() => {
          onChange(checked.includes("insurance") ? [] : ["insurance"])
        }} />
        Insurance
      </label>
    </div>
  )
}))

vi.mock('@/components/subscription-plan/SubscriptionPlanComponent', () => ({
  default: ({ onCLick }: any) => <button onClick={onCLick}>Select Plan</button>
}))

vi.mock('@/components/dialogs/NormalDialog', () => ({
  __esModule: true,
  default: ({ title, children, onAccept, onCancel, confirmLabel = 'Confirm' }: any) => (
    <div>
      {title && <h2>{title}</h2>}
      {children}
      <button onClick={onAccept}>{confirmLabel}</button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  )
}))


vi.mock('@/utils/notification.util', () => ({
  default: {
    error: vi.fn(),
  }
}))

export const mockedSubscribeUsingStripeAction = vi.fn(() => async () => ({
  status: 200,
  data: { stripeSessionUrl: 'https://stripe.com' },
}))

// Mock Redux actions
vi.mock('@/store/slicers/therapist.slicer', async () => {
  return {
    getTherapistSubscriptionAction: vi.fn(() => async () => ({ status: 200, data: { plan: null } })),
    fetchRegInfoAction: vi.fn(() => async () => ({ status: 200, data: { payloadInfo: {} } })),
    createTherapistSubscriptionAction: vi.fn(() => async () => ({ status: 201, data: { data: {} } })),
    subscribeUsingStripeAction: vi.fn(() => async () => ({ status: 200, data: { stripeSessionUrl: 'https://stripe.com' } })),
    registerTherapistAction: vi.fn(() => async () => ({ status: 200 })),
    updateTherapistState: vi.fn(() => ({ type: 'UPDATE_STATE' })),
    cancelOrRenewSubscriptionAction: vi.fn(() => async () => ({ status: 200, data: { billingSessionUrl: 'https://billing.com' } })),
    updateTherapistSubscriptionAction: vi.fn(() => async () => ({ status: 200 })),
  }
})

vi.mock('@/store/slicers/admin.slicer', async () => {
  return {
    fetchSubscriptionPlansAction: vi.fn(() => async () => ({ status: 200, data: [{ id: 1, monthlyPrice: 25, annualPrice: 250 }] })),
  }
})

// Helpers
const mockStore = configureStore([thunk])
const renderWithStore = (store: any) =>
  render(
    <Provider store={store}>
      <BrowserRouter>
        <Page />
      </BrowserRouter>
    </Provider>
  )

describe('Subscriptions & Payment Page', () => {
  let store: any

  beforeEach(() => {
    store = mockStore({
      auth: { user: { userToken: 'mock-token' } },
      therapist: { fetchingInfo: false },
    })
  })

  it('renders the page heading', () => {
    renderWithStore(store)
    expect(screen.getByText(/Subscriptions & Payment/i)).toBeInTheDocument()
  })

  it('shows subscribe now message if no plan', async () => {
    renderWithStore(store)
    await waitFor(() => {
      expect(screen.getByText(/Your subscription to NextTherapist is not active/i)).toBeInTheDocument()
    })
  })

  it('opens plan dialog on clicking Subscribe Now', async () => {
    renderWithStore(store)
    const btn = await screen.findByText('Subscribe Now')
    fireEvent.click(btn)
    expect(await screen.findByText('Select Plan')).toBeInTheDocument()
  })

 
})
