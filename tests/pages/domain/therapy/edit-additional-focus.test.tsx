import { render, screen, fireEvent } from "@testing-library/react";
import EditAdditionalFocus from "../../../../src/pages/domain/therapy/edit-additional-focus";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import notification from "../../../../src/utils/notification.util";

// Mocks
vi.mock("react-redux", async () => {
  const actual = await vi.importActual("react-redux");
  return {
    ...actual,
    useSelector: vi.fn(),
    useDispatch: vi.fn(),
  };
});

vi.mock("react-router-dom", () => ({
  useNavigate: vi.fn(),
  useLocation: vi.fn(),
}));

vi.mock("@/configs/registration.configs", () => ({
  areaOfFocus: ["option1", "option2", "others"],
}));

vi.mock("@/store/slicers/auth.slicer", () => ({
  updateRegistrationForm: vi.fn().mockReturnValue({ type: "update" }),
}));

vi.mock("@/store/slicers/therapist.slicer", () => ({
  registerTherapistAction: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    error: vi.fn(),
  },
}));

// Dummy Components
vi.mock("@/components/forms/AppCheckBoxListComponent", () => ({
  default: ({ checked, onChange, checkList }: any) => (
    <div data-testid="checkbox">
      {checkList.map((item: string) => (
        <label key={item}>
          <input
            type="checkbox"
            checked={checked.includes(item)}
            onChange={(e) =>
              onChange(
                e.target.checked
                  ? [...checked, item]
                  : checked.filter((val: string) => val !== item)
              )
            }
          />
          {item}
        </label>
      ))}
    </div>
  ),
}));

vi.mock("@/components/forms/InputComponent", () => ({
  default: ({ value, onChange, errorMessage }: any) => (
    <div>
      <input data-testid="input" value={value} onChange={(e) => onChange(e.target.value)} />
      {errorMessage && <span>{errorMessage}</span>}
    </div>
  ),
}));

vi.mock("@/components/buttons/AppButton", () => ({
  default: ({ disabled, onClick }: any) => (
    <button disabled={disabled} onClick={onClick}>
      Save
    </button>
  ),
}));

vi.mock("@/components/ToolbarComponent", () => ({
  default: () => <div data-testid="toolbar" />,
}));

describe("EditAdditionalFocus", () => {
  const mockDispatch = vi.fn();
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useDispatch as any).mockReturnValue(mockDispatch);
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useLocation as any).mockReturnValue({ pathname: "/auth/register/focus" });
  });

  const setup = (overrides = {}) => {
    (useSelector as any).mockImplementation((fn: any) =>
      fn({
        auth: {
          registration: {
            formContent: {
              focus: {
                checked_lists: ["others"],
                other_focus: "",
                ...overrides,
              },
            },
            userRegistrationToken: "token",
          },
        },
      })
    );

    render(<EditAdditionalFocus />);
  };

  it("renders heading and toolbar", () => {
    setup();
    expect(screen.getByText("Additional Areas of Focus:")).toBeInTheDocument();
    expect(screen.getByTestId("toolbar")).toBeInTheDocument();
  });

  it("renders checkboxes and input when 'others' is selected", () => {
    setup();
    expect(screen.getByTestId("checkbox")).toBeInTheDocument();
    expect(screen.getByTestId("input")).toBeInTheDocument();
    expect(screen.getByText("Specify other area of focus")).toBeInTheDocument();
  });

  it("dispatches updateRegistrationForm on checkbox change", () => {
    setup({ other_focus: "Custom text" });

    const otherCheckbox = screen.getByLabelText("option1");
    fireEvent.click(otherCheckbox);

    expect(mockDispatch).toHaveBeenCalled();
  });

  it("updates input value on change", () => {
    setup();
    const input = screen.getByTestId("input") as HTMLInputElement;
    fireEvent.change(input, { target: { value: "My Custom Area" } });

    expect(mockDispatch).toHaveBeenCalledWith(
      expect.objectContaining({ type: "update" })
    );
  });

  it("disables save button if form is invalid", () => {
    setup();
    expect(screen.getByText("Save")).toBeDisabled();
  });

  it("shows error if user token is missing", async () => {
    (useSelector as any).mockImplementation((fn: any) =>
      fn({
        auth: {
          registration: {
            formContent: {
              focus: { checked_lists: ["others"], other_focus: "test" },
            },
            userRegistrationToken: null,
          },
        },
      })
    );

    render(<EditAdditionalFocus />);
    const button = screen.getByText("Save");
    fireEvent.click(button);
    expect(notification.error).toHaveBeenCalledWith("User Token is required");
  });



});
