import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { describe, expect, it, vi } from 'vitest'
import configureStore from 'redux-mock-store'
import thunk from 'redux-thunk'
import Page from '../../../../../src/pages/domain/therapy/password-security/index.page'

vi.mock('react-phone-number-input', async () => {
  const actual = await vi.importActual<any>('react-phone-number-input')
  return {
    ...actual,
    default: ({ onChange, value }: any) => (
      <input
        type="tel"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Phone Number"
      />
    ),
  }
})

// Mock the Redux actions
vi.mock('@/store/slicers/therapist.slicer', () => ({
  sendTokenAction: vi.fn(() => async () => ({ status: 200 })),
  resetEmailAction: vi.fn(() => async () => ({ status: 200 })),
  sendSmsAction: vi.fn(() => async () => ({ status: 200 })),
  resetPhoneAction: vi.fn(() => async () => ({ status: 200 })),
  changePasswordAction: vi.fn(() => async () => ({ status: 201 })),
  enableDisableMfaAction: vi.fn(() => async () => ({ status: 200 })),
}))

const mockStore = configureStore([thunk])

const renderPage = (storeOverrides = {}) => {
  const store = mockStore({
    auth: {
      user: {
        email: '<EMAIL>',
        emailVerifiedAt: new Date().toISOString(),
        phone: '+**********',
        phoneVerifiedAt: new Date().toISOString(),
        mfaEnabled: false,
        passwordUpdatedAt: new Date().toISOString(),
      },
      ...storeOverrides,
    },
  })

  render(
    <Provider store={store}>
      <Page />
    </Provider>
  )
  return store
}

describe('Security Page', () => {
  it('renders initial view correctly', () => {
    renderPage()
    expect(screen.getByText('Primary Contact')).toBeInTheDocument()
    expect(screen.getByText('Phone Number')).toBeInTheDocument()
    expect(screen.getByText('Password')).toBeInTheDocument()
    expect(screen.getByText('Multi-Factor Authentication')).toBeInTheDocument()
  })

  it("allows editing email and submitting flow", async () => {
  renderPage()

  const editButtons = screen.getAllByRole("button", { name: /edit/i })
  expect(editButtons.length).toBeGreaterThan(0)

  fireEvent.click(editButtons[0]) // First "Edit" is for email

  const emailInput = await screen.findByLabelText(/email/i)
  expect(emailInput).toBeInTheDocument()

  fireEvent.change(emailInput, { target: { value: "<EMAIL>" } })

  const saveButton = screen.getByRole("button", { name: /save/i })
  fireEvent.click(saveButton)

  // Trigger dialog
  const dialogText = await screen.findByText(/you will receive a reset token/i)
  expect(dialogText).toBeInTheDocument()
})

it('allows editing phone and submitting flow', async () => {
  renderPage()

  const editButtons = screen.getAllByRole('button', {
    name: (_, el) => el?.textContent?.trim() === 'Edit',
  })
  fireEvent.click(editButtons[1]) // Phone section

  const input = screen.getByRole('textbox') // Phone input
  fireEvent.change(input, { target: { value: '+14155552671' } }) // ✅ valid phone

  fireEvent.click(screen.getByRole('button', { name: /save/i }))

  await waitFor(() => {
    expect(screen.getByText('Update Phone Number?')).toBeInTheDocument()
  })

  fireEvent.click(screen.getByRole('button', { name: 'Ok' }))

  await waitFor(() => {
    expect(screen.getByText(/You requested to update your phone number/)).toBeInTheDocument()
  })

  const resetCodeInput = screen.getByPlaceholderText('Enter reset code')
  fireEvent.change(resetCodeInput, { target: { value: '654321' } })

  fireEvent.click(screen.getByText('Reset'))

  await waitFor(() => {
    expect(screen.queryByText('Change Phone Number')).not.toBeInTheDocument()
  })
})


it('allows editing password with validation and submission', async () => {
  renderPage()

  const editButtons = screen.getAllByRole('button', {
    name: (_, el) => el?.textContent?.trim() === 'Edit',
  })

  fireEvent.click(editButtons[2]) // Click "Edit" for password

  fireEvent.change(screen.getByLabelText('Old Password'), {
    target: { value: 'OldPass@123' },
  })
  fireEvent.change(screen.getByLabelText('New Password'), {
    target: { value: 'NewPass@123' },
  })
  fireEvent.change(screen.getByLabelText('Confirm Password'), {
    target: { value: 'NewPass@123' },
  })

  fireEvent.click(screen.getByText('Save'))

  await waitFor(() => {
    expect(screen.queryByText('Change Password')).not.toBeInTheDocument()
  })
})


it('toggles MFA', async () => {
  renderPage()

  // Click "Enable MFA" button
  fireEvent.click(screen.getByRole('button', { name: /enable mfa/i }))

  // Assert dialog opens (use h3 heading)
  await waitFor(() => {
    expect(screen.getByRole('heading', { name: /enable mfa/i })).toBeInTheDocument()
  })

  // Click "Confirm" inside dialog
  fireEvent.click(screen.getByRole('button', { name: /confirm/i }))

  // Dialog should close
  await waitFor(() => {
    expect(screen.queryByRole('heading', { name: /enable mfa/i })).not.toBeInTheDocument()
  })
})




})
