import { render, screen, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { describe, it, expect, vi, beforeEach } from "vitest";
import NotificationSettings from "../../../../../src/pages/domain/therapy/profile/notification-settings.page";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { useInView } from "react-intersection-observer";

// Mock the dependencies
vi.mock("react-redux", () => ({
  useDispatch: vi.fn()
}));

vi.mock("react-router-dom", () => ({
  useParams: vi.fn(),
  Link: ({ to, children }) => <a href={to}>{children}</a>
}));

vi.mock("react-intersection-observer", () => ({
  useInView: vi.fn()
}));

// Mock the phone input component
vi.mock("react-phone-number-input", () => ({
  default: ({ value, onChange, disabled, className }) => (
    <input
      data-testid="phone-input"
      value={value || ""}
      onChange={(e) => onChange && onChange(e.target.value)}
      disabled={disabled}
      className={className}
    />
  )
}));

// Mock the configs
vi.mock("@/configs/registration.configs", () => ({
  appointmentNotifications: [
    { value: "email", label: "Email Notifications" },
    { value: "text", label: "Text Message Notifications" },
    { value: "push", label: "Push Notifications" }
  ]
}));

// Mock FontAwesome
vi.mock('@fortawesome/react-fontawesome', () => ({
  FontAwesomeIcon: () => <i className="fa-solid fa-chevron-left" />
}));

// Mock the action
vi.mock("@/store/slicers/therapist.slicer", () => ({
  fetchRegInfoAction: vi.fn()
}));

describe("NotificationSettings", () => {
  const mockDispatch = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mocks
    useDispatch.mockReturnValue(mockDispatch);
    useParams.mockReturnValue({ therapistId: "123" });
    useInView.mockReturnValue({ ref: vi.fn(), inView: true });
    
    // Default dispatch implementation
    mockDispatch.mockImplementation((action) => {
      if (typeof action === "function") {
        return action(mockDispatch);
      }
      return action;
    });

    // Mock console.error to prevent test output pollution
    console.error = vi.fn();
  });

  afterEach(() => {
    // Restore console.error
    console.error = vi.fn();
  });

  // Test loading state
  it("displays loading state initially", () => {
    // Mock the async action to not resolve immediately
    mockDispatch.mockReturnValue(new Promise(() => {}));
    
    const { container } = render(<NotificationSettings />);
    
    // Check for loading skeleton using class name
    const loadingElement = container.querySelector('.animate-pulse');
    expect(loadingElement).toBeInTheDocument();
  });

  // Test when there are no notification preferences
  it("displays 'No preferences selected' when no preferences are available", async () => {
    // Mock successful API response with empty preferences
    mockDispatch.mockResolvedValue({
      status: 200,
      data: {
        payloadInfo: {
          notification_preferences: []
        }
      }
    });
    
    render(<NotificationSettings />);
    
    // Wait for the loading state to resolve
    await waitFor(() => {
      expect(screen.getByText("No preferences selected")).toBeInTheDocument();
    });
  });

 


  // Test with notification preferences but without 'text'
  it("displays selected notification preferences correctly without text preference", async () => {
    // Mock successful API response with preferences
    mockDispatch.mockResolvedValue({
      status: 200,
      data: {
        payloadInfo: {
          notification_preferences: ["email", "push"]
        }
      }
    });
    
    render(<NotificationSettings />);
    
    // Wait for the loading state to resolve
    await waitFor(() => {
      // Check for the section title
      expect(screen.getByText("Selected Preferences")).toBeInTheDocument();
      
      // Check for the preference labels
      expect(screen.getByText("Email Notifications")).toBeInTheDocument();
      expect(screen.getByText("Push Notifications")).toBeInTheDocument();
      
      // Phone input should not be rendered
      expect(screen.queryByTestId("phone-input")).not.toBeInTheDocument();
    });
  });

  // Test with unknown notification types (fallback to value)
  it("falls back to value when label is not found for notification type", async () => {
    // Mock successful API response with unknown notification type
    mockDispatch.mockResolvedValue({
      status: 200,
      data: {
        payloadInfo: {
          notification_preferences: ["unknown_notification_type"]
        }
      }
    });
    
    render(<NotificationSettings />);
    
    // Wait for the loading state to resolve
    await waitFor(() => {
      // Check for the raw value as label
      expect(screen.getByText("unknown_notification_type")).toBeInTheDocument();
    });
  });

  // Test component with null notification preferences
  it("handles null notification preferences correctly", async () => {
    // Mock successful API response with null preferences
    mockDispatch.mockResolvedValue({
      status: 200,
      data: {
        payloadInfo: {
          notification_preferences: null
        }
      }
    });
    
    render(<NotificationSettings />);
    
    // Wait for the loading state to resolve
    await waitFor(() => {
      expect(screen.getByText("No preferences selected")).toBeInTheDocument();
    });
  });

  // Test API response with non-200 status
  it("handles non-200 status from API", async () => {
    // Mock API response with non-200 status
    mockDispatch.mockResolvedValue({
      status: 404,
      data: {}
    });
    
    render(<NotificationSettings />);
    
    // Wait for any async operations to complete and verify component loaded
    await waitFor(() => {
      expect(screen.getByText("No preferences selected")).toBeInTheDocument();
    });
    
    // Verify dispatch was called
    expect(mockDispatch).toHaveBeenCalled();
  });

  // Test with multiple notification preferences
  it("displays all selected notification preferences correctly", async () => {
    // Mock successful API response with all preferences
    mockDispatch.mockResolvedValue({
      status: 200,
      data: {
        payloadInfo: {
          notification_preferences: ["email", "text", "push"],
          phone_number: "+1234567890" // Updated to match component property name
        }
      }
    });
    
    render(<NotificationSettings />);
    
    // Wait for the loading state to resolve
    await waitFor(() => {
      // Check for all preference labels
      expect(screen.getByText("Email Notifications")).toBeInTheDocument();
      expect(screen.getByText("Text Message Notifications")).toBeInTheDocument();
      expect(screen.getByText("Push Notifications")).toBeInTheDocument();
      
      // Phone input should be rendered
      expect(screen.getByTestId("phone-input")).toBeInTheDocument();
    });
  });
});