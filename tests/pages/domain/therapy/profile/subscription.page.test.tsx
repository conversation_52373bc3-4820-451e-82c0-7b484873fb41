import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import { describe, it, expect, vi, beforeEach } from "vitest";
import SubscriptionSection from "../../../../../src/pages/domain/therapy/profile/subscription-section.page";
import * as dateFns from 'date-fns';
import dayjs from 'dayjs';

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn().mockReturnValue('01-01-2023')
}));

// Mock dayjs correctly
vi.mock('dayjs', () => {
  const formatMock = vi.fn().mockReturnValue('01-01-2023');
  const mockDayjs = vi.fn().mockImplementation(() => ({
    format: formatMock
  }));
  return { default: mockDayjs };
});

describe("SubscriptionSection", () => {
  // Reset mocks before each test
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Test when subscription is not available
  it("renders 'No active subscription' when subscription is null", () => {
    render(<SubscriptionSection subscription={undefined} />);
    expect(screen.getByText("No active subscription.")).toBeInTheDocument();
  });

  // Test when subscription is not active
  it("renders 'No active subscription' when subscription is not active", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: false,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    expect(screen.getByText("No active subscription.")).toBeInTheDocument();
  });

  // Test when stripe status is not active
  it("renders 'No active subscription' when stripe status is not active", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'inactive',
        billedPrice: 1000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    expect(screen.getByText("No active subscription.")).toBeInTheDocument();
  });

  // Test active monthly subscription
  it("renders active monthly subscription details correctly", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    expect(screen.getByText("Plan:")).toBeInTheDocument();
    expect(screen.getByText("Monthly")).toBeInTheDocument();
    expect(screen.getByText("Status:")).toBeInTheDocument();
    expect(screen.getByText("active")).toBeInTheDocument();
    expect(screen.getByText("Start Date:")).toBeInTheDocument();
    expect(screen.getByText("Renewal Date:")).toBeInTheDocument();
    expect(screen.getByText("Amount Paid:")).toBeInTheDocument();
    expect(screen.getByText("$10.00")).toBeInTheDocument();
    
    // Verify date formatting was called
    expect(dateFns.format).toHaveBeenCalledWith(expect.any(Date), 'MM-dd-yyyy');
    expect(dayjs).toHaveBeenCalledWith('2023-02-01T00:00:00Z');
  });

  // Test active annual subscription
  it("renders active annual subscription details correctly", () => {
    const mockSubscription = {
      subscriptionType: 'annual',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 10000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2024-01-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    expect(screen.getByText("Plan:")).toBeInTheDocument();
    expect(screen.getByText("Annually")).toBeInTheDocument();
    expect(screen.getByText("$100.00")).toBeInTheDocument();
  });

  // Test cancelled subscription
  it("renders cancelled subscription message correctly", () => {
    // Set up a specific mock for the format call for cancelled subscription
    const mockFormat = vi.fn().mockReturnValue('02-01-2023');
    vi.mocked(dayjs).mockImplementation(() => ({
      format: mockFormat
    }));

    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'true',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    // Check that cancellation message is displayed
    expect(screen.getByText("Subscription Cancelled - No Renewal (", { exact: false })).toBeInTheDocument();
    expect(screen.getByText("Valid Until:", { exact: false })).toBeInTheDocument();
  });

  // Test with subscription schedule ID
  it("does not render cancelled subscription message when subscription schedule ID exists", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'true', // Set to true
        stripeSubscriptionScheduleId: 'sub_123456' // But has a schedule ID
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    // Should not find the cancellation message
    expect(screen.queryByText("Subscription Cancelled - No Renewal (", { exact: false })).not.toBeInTheDocument();
    expect(screen.queryByText("Valid Until:", { exact: false })).not.toBeInTheDocument();
    
    // Should show the formatted renewal date
    expect(screen.getByText("01-01-2023")).toBeInTheDocument();
  });

  // Test price formatting
  it("formats billedPrice correctly by dividing by 100", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1999, // $19.99
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    // Should format billedPrice correctly
    expect(screen.getByText("$19.99")).toBeInTheDocument();
  });

  // Test CSS classes for active status
  it("applies correct CSS class for active status", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1000,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    // Get the status text
    const statusElement = screen.getByText("active");
    
    // Check it has the green text class
    expect(statusElement).toHaveClass("text-green-600");
    expect(statusElement).toHaveClass("capitalize");
  });

  // Edge cases
  it("handles edge case with zero billedPrice", () => {
    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 0,
        stripeCurrentPeriodStart: '2023-01-01T00:00:00Z',
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    render(<SubscriptionSection subscription={mockSubscription} />);
    
    // Should format zero billedPrice correctly
    expect(screen.getByText("$0.00")).toBeInTheDocument();
  });

  it("handles invalid date gracefully", () => {
    // Mock date-fns to throw an error
    vi.mocked(dateFns.format).mockImplementationOnce(() => {
      throw new Error("Invalid date");
    });

    const mockSubscription = {
      subscriptionType: 'monthly',
      isActive: true,
      subscribedAt: '2023-01-01T00:00:00Z',
      expiredAt: null,
      subscriptionPlan: {
        monthlyPrice: '10.00',
        annualPrice: '100.00'
      },
      stripeInfo: {
        status: 'active',
        billedPrice: 1000,
        stripeCurrentPeriodStart: 'invalid-date', // Invalid date
        stripeCurrentPeriodEnd: '2023-02-01T00:00:00Z',
        stripeCancelAtPeriodEnd: 'false',
        stripeSubscriptionScheduleId: ''
      }
    };

    // This should not throw an error
    expect(() => render(<SubscriptionSection subscription={mockSubscription} />)).not.toThrow();
  });
});