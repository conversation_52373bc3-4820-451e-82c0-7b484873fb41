import React from 'react'
import {
	render,
	screen,
	fireEvent,
	waitFor,
	within,
	cleanup,
} from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { Provider } from 'react-redux'
import { MemoryRouter } from 'react-router-dom'
import thunk from 'redux-thunk'
import '@testing-library/jest-dom'
import Page from '../../../../../src/pages/domain/therapy/profile/index.page'
import { request } from "../../../../../src/utils/request.utils";
import therapistReducer from '../../../../../src/store/slicers/therapist.slicer'
import authReducer from '../../../../../src/store/slicers/auth.slicer'
import { configureStore } from '@reduxjs/toolkit'
import { fetchRegInfoAction } from '../../../../../src/store/slicers/therapist.slicer'
import { afterEach } from 'node:test'

import { signOut } from '../../../../../src/store/slicers/auth.slicer'

vi.spyOn(request, 'delete').mockImplementation(() =>
	Promise.resolve({ status: 200 })
)

vi.mock(
	'../../../../../src/store/slicers/therapist.slicer',
	async (importOriginal) => {
		const actual = await importOriginal()
		return {
			...actual,
			fetchRegInfoAction: vi.fn(() => async () => ({
				status: 200,
				data: [
					{
						pageName: 'license',
						payloadInfo: {
							licenses: [
								{
									license_type: { label: 'Test License' },
									license_state: { label: 'California' },
									issuing_board: 'California Board of Psychology',
									issue_date: '2020-06-15',
									expiry_date: '2025-06-15',
									license_status: { label: 'Active' },
									probation_or_disciplinary_action: { label: 'None' },
									license_verification: {
										location: 'https://example.com/license.pdf',
										upload_name: 'License Document',
									},
								},
							],
						},
					},
					{
						pageName: 'specialization',
						payloadInfo: { specializations: ['Psychology'] },
					},
					{
						pageName: 'rank-specialties',
						payloadInfo: { specialties: ['Neuroscience'] },
					},
					{
						pageName: 'rank-sub-specialties',
						payloadInfo: { subSpecialties: ['Cognitive Therapy'] },
					},
					{
						pageName: 'modalities',
						payloadInfo: { treatmentMethods: ['CBT', 'DBT'] },
					},
					{
						pageName: 'practice-focus',
						payloadInfo: { focusAreas: ['Anxiety', 'Depression'] },
					},
					{
						pageName: 'practice-info',
						payloadInfo: {
							practice_name: 'MindWell Clinic',
							services_offered: ['CBT', 'DBT'],
							business_address: {
								full_address: '123 Wellness St, New York, NY',
							},
							patient_info_sheet: {
								location: 'https://example.com/patient-info.pdf',
								upload_name: 'Patient Info Sheet',
							},
						},
					},
					{
						pageName: 'profile',
						payloadInfo: {
							educations: [
								{
									college: 'Harvard University',
									degree_type: 'PhD',
									year_graduated: '2010',
								},
							],
							experience_years: '5',
							race: { checked: 'Asian' },
							language: { checked_list: ['English', 'Spanish'] },
							profile_statement: 'I am a licensed therapist.',
							social_media: { linkedin: 'https://linkedin.com/in/test' },
							video_links: [{ url: 'https://youtube.com/video1' }],
							bio: 'Experienced therapist specializing in cognitive therapy.',
						},
					},
				],
			})),
		}
	}
)

global.IntersectionObserver = vi.fn(() => ({
	observe: vi.fn(),
	disconnect: vi.fn(),
	unobserve: vi.fn(),
}))

const createTestStore = (preloadedState = {}) =>
	configureStore({
		reducer: {
			auth: authReducer,
			therapist: therapistReducer,
		},
		preloadedState,
	})

describe('Page Component', () => {
	let store
	let dispatchSpy
	vi.restoreAllMocks()

	beforeEach(() => {
		store = configureStore({
			reducer: {
				auth: authReducer,
				therapist: therapistReducer,
			},
			preloadedState: {
				auth: {
					user: { id: '123' },
					registration: { formContent: {} },
				},
				therapist: {
					profileInfo: [],
					profilePageInfo: [],
				},
			},
		})

		dispatchSpy = vi.spyOn(store, 'dispatch')

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)
	})

	afterEach(() => {
		cleanup()
	})

	const editButtons = [		
		{
			testId: 'edit-practice-focus',
			expectedPath: '/auth/register/practice-focus',
			label: /practice focus/i,
		},
		{
			testId: 'edit-practice-info',
			expectedPath: '/auth/register/practice-info',
			label: /practice info/i,
		},
	]

	editButtons.forEach(({ testId, expectedPath, label }) => {
		it(`navigates to ${expectedPath} page when edit button is clicked`, async () => {
			fireEvent.click(screen.getByTestId(testId))

			await waitFor(() => {
				expect(screen.getByText(label)).toBeInTheDocument()
			})
		})
	})

	it('renders the page title', () => {
		expect(screen.getByText('Your Profile Information')).toBeInTheDocument()
	})

	it('opens delete modal when Delete Profile button is clicked', () => {
		fireEvent.click(screen.getByText('Delete Profile'))
		expect(screen.getByText('Confirm Deletion')).toBeInTheDocument()
	})

	it('closes delete modal when Cancel is clicked', async () => {
		fireEvent.click(screen.getByText('Delete Profile'))
		fireEvent.click(screen.getByText('Cancel'))
		await waitFor(() =>
			expect(screen.queryByText('Confirm Deletion')).not.toBeInTheDocument()
		)
	})

	it('handles API error on profile deletion', async () => {
		vi.spyOn(request, 'delete').mockImplementation(() =>
			Promise.reject(new Error('API Error'))
		)

		fireEvent.click(screen.getByText('Delete Profile'))
		fireEvent.click(screen.getByText('Confirm'))

		await waitFor(() => expect(request.delete).toHaveBeenCalled())
	})

	it('navigates to edit license page when edit button is clicked', () => {
		fireEvent.click(screen.getByTestId('edit-license'))
		const licenseHeaders = screen.getAllByText(/license/i)
		expect(licenseHeaders[0]).toBeInTheDocument()
	})

	it('navigates to edit specialization page when edit button is clicked', () => {
		fireEvent.click(screen.getByTestId('edit-specialization'))
		const specializationHeaders = screen.getAllByText(/specialization/i)
		expect(specializationHeaders[0]).toBeInTheDocument()
	})

	it(`navigates to /auth/register/profile page when edit button is clicked`, async () => {
		fireEvent.click(screen.getByTestId('edit-profile'))

		await waitFor(() => {
			const profileHeaders = screen.getAllByText(/profile/i)
			expect(profileHeaders[0]).toBeInTheDocument()
		})
	})

	it('renders the edit buttons for each section', async () => {
		await waitFor(() => {
			const editButtons = screen.getAllByRole('button')
			expect(editButtons.length).toBeGreaterThan(0)
		})
	})

	it('dispatches fetchProfilePageInfo on mount', async () => {
		const dispatchSpy = vi.spyOn(store, 'dispatch')

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		expect(dispatchSpy).toHaveBeenCalled()
	})

	it('renders all profile sections when data is available', async () => {
		await store.dispatch(
			fetchRegInfoAction({
				id: 123,
				pages: ['license', 'specialization'],
			})
		)

		await waitFor(() => {
			console.log('🟢 Updated Redux Store:', store.getState())
			expect(screen.getByText('Test License')).toBeInTheDocument()
			expect(screen.getByText('Psychology')).toBeInTheDocument()
		})
	})

	// it('calls API to delete profile when Confirm is clicked', async () => {
	// 	store.dispatch = vi.fn(store.dispatch)

	// 	const deleteButton = screen.getByText('Delete Profile')
	// 	fireEvent.click(deleteButton)

	// 	await waitFor(() =>
	// 		expect(screen.getByText('Confirm Deletion')).toBeInTheDocument()
	// 	)

	// 	vi.spyOn(request, 'delete').mockResolvedValueOnce({ status: 200, data: {} })

	// 	fireEvent.click(screen.getByText('Confirm'))

	// 	await waitFor(() => {
	// 		console.log('🟢 request.delete calls:', request.delete.mock.calls)
	// 		expect(request.delete).toHaveBeenCalledWith('/therapists/profile/123')
	// 	})

	// 	await waitFor(() => {
	// 		console.log('🟢 Checking modal state...')
	// 		expect(screen.queryByText('Confirm Deletion')).not.toBeInTheDocument()
	// 	})

	// 	await waitFor(() => {
	// 		console.log('🟢 Checking signOut dispatch...')
	// 		expect(store.dispatch).toHaveBeenCalledWith(signOut())
	// 	})
	// })

	it('renders no profile information message when data is missing', async () => {
		store = configureStore({
			reducer: {
				auth: authReducer,
				therapist: therapistReducer,
			},
			preloadedState: {
				auth: { user: { id: '123' }, registration: { formContent: {} } },
				therapist: { profileInfo: [], profilePageInfo: [] }, // No profile data
			},
		})

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)
	})

	it('renders license verification upload link correctly', async () => {
		store = configureStore({
			reducer: {
				auth: authReducer,
				therapist: therapistReducer,
			},
			preloadedState: {
				auth: {
					user: { id: '123' },
					registration: { formContent: {} },
				},
				therapist: {
					profileInfo: [],
					profilePageInfo: [
						{
							pageName: 'license',
							payloadInfo: {
								licenses: [
									{
										license_type: { label: 'Clinical Psychology' },
										license_state: { label: 'California' },
										issuing_board: 'California Board of Psychology',
										issue_date: '2020-06-15',
										expiry_date: '2025-06-15',
										license_status: { label: 'Active' },
										probation_or_disciplinary_action: { label: 'None' },
										license_verification: {
											location: 'https://example.com/license.pdf',
											upload_name: 'License Document',
										},
									},
								],
							},
						},
					],
				},
			},
		})

		console.log('🟢 Mocked Redux Store:', store.getState())

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		screen.debug()

		const downloadLink = await screen.findByRole('link', {
			name: /license document/i,
		})

		expect(downloadLink).toBeInTheDocument()
		expect(downloadLink).toHaveAttribute(
			'href',
			'https://example.com/license.pdf'
		)
	})

	it('renders practice name correctly', async () => {
		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		await waitFor(() => {
			expect(screen.getByText('MindWell Clinic')).toBeInTheDocument()
		})
	})

	0

	it('renders business address correctly', async () => {
		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		await waitFor(() => {
			expect(
				screen.getByText('123 Wellness St, New York, NY')
			).toBeInTheDocument()
		})
	})

	it('renders patient info sheet download link', async () => {
		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		const documentLink = await screen.findByRole('link', {
			name: /Patient Info Sheet/i,
		})

		expect(documentLink).toBeInTheDocument()
		expect(documentLink).toHaveAttribute(
			'href',
			'https://example.com/patient-info.pdf'
		)
	})

	it('displays fallback text when patient info sheet is missing', async () => {
		store = configureStore({
			reducer: { auth: authReducer, therapist: therapistReducer },
			preloadedState: {
				auth: { user: { id: '123' }, registration: { formContent: {} } },
				therapist: {
					profileInfo: [],
					profilePageInfo: [{ pageName: 'practice-info', payloadInfo: {} }],
				},
			},
		})

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Page />
				</MemoryRouter>
			</Provider>
		)

		const practiceInfoSections = screen.getAllByTestId('practice-info-section')
		expect(practiceInfoSections.length).toBeGreaterThan(0)

		const fallbackSection = practiceInfoSections.find((section) =>
			within(section).queryByText('No practice information available')
		)

		expect(fallbackSection)
	})

	describe('Profile Info Section', () => {
		it('renders education details correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{
								pageName: 'profile',
								payloadInfo: {
									educations: [
										{
											college: 'Harvard University',
											degree_type: 'PhD',
											year_graduated: '2010',
										},
									],
								},
							},
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				const profileSections = screen.getAllByTestId('profile-info-section')

				const profileSection = profileSections[0]

				expect(
					within(profileSection).getByText(/harvard university/i)
				).toBeInTheDocument()
				expect(within(profileSection).getByText(/phd/i)).toBeInTheDocument()
				expect(within(profileSection).getByText(/2010/i)).toBeInTheDocument()
			})
		})

		it('displays experience years correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{ pageName: 'profile', payloadInfo: { experience_years: '5' } },
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				expect(screen.getByText('5 Years')).toBeInTheDocument()
			})
		})

		it('displays race correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{
								pageName: 'profile',
								payloadInfo: { race: { checked: 'Asian' } },
							},
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				expect(screen.getByText('Asian')).toBeInTheDocument()
			})
		})

		it('displays language list correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{
								pageName: 'profile',
								payloadInfo: {
									language: { checked_list: ['English', 'Spanish'] },
								},
							},
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				expect(screen.getByText('English')).toBeInTheDocument()
				expect(screen.getByText('Spanish')).toBeInTheDocument()
			})
		})

		it('displays profile statement correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{
								pageName: 'profile',
								payloadInfo: {
									profile_statement: 'I am a licensed therapist.',
								},
							},
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				expect(
					screen.getByText('I am a licensed therapist.')
				).toBeInTheDocument()
			})
		})

		it('renders social media links correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{
								pageName: 'profile',
								payloadInfo: {
									social_media: { linkedin: 'https://linkedin.com/in/test' },
								},
							},
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				const link = screen.getByRole('link', { name: /linkedin/i })
				expect(link).toBeInTheDocument()
				expect(link).toHaveAttribute('href', 'https://linkedin.com/in/test')
			})
		})

		it('renders video links correctly', async () => {
			store = configureStore({
				reducer: { auth: authReducer, therapist: therapistReducer },
				preloadedState: {
					auth: { user: { id: '123' }, registration: { formContent: {} } },
					therapist: {
						profileInfo: [],
						profilePageInfo: [
							{
								pageName: 'profile',
								payloadInfo: {
									video_links: [{ url: 'https://youtube.com/video1' }],
								},
							},
						],
					},
				},
			})

			render(
				<Provider store={store}>
					<MemoryRouter>
						<Page />
					</MemoryRouter>
				</Provider>
			)

			await waitFor(() => {
				const link = screen.getByRole('link', { name: /video 1/i })
				expect(link).toBeInTheDocument()
				expect(link).toHaveAttribute('href', 'https://youtube.com/video1')
			})
		})
	})
})
