import { render, screen, fireEvent } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import Page from "../../../../src/pages/domain/therapy/help-center/index.page";
import "@testing-library/jest-dom";

// ✅ Mock react-redux safely inside the factory
vi.mock("react-redux", async (importOriginal) => {
  const actual = (await importOriginal()) as object;
  return {
    ...actual,
    useSelector: vi.fn((selector: any) =>
      selector({
        auth: {
          user: {
            email: "<EMAIL>",
          },
        },
      })
    ),
    useDispatch: vi.fn(() => vi.fn()),
  };
});

// ✅ Mock react-intersection-observer
vi.mock("react-intersection-observer", () => ({
  useInView: () => ({ ref: vi.fn() }),
}));

// ✅ Mock ContactUsModal safely
vi.mock("@/pages/domain/therapy/help-center/ContactUsModal", () => ({
  __esModule: true,
  default: () => <div data-testid="contact-us-modal">Mock ContactUsModal</div>,
}));

// ✅ Mock react-stately overlay state
const mockOverlayState = {
  isOpen: false,
  open: vi.fn(),
  close: vi.fn(),
};
vi.mock("react-stately", async (importOriginal) => {
  const actual = (await importOriginal()) as object;
  return {
    ...actual,
    useOverlayTriggerState: () => mockOverlayState,
  };
});

describe("Page", () => {
  beforeEach(() => {
    mockOverlayState.open.mockClear();
  });

  it("renders Help Center title and FAQ section", () => {
    render(<Page />);
    expect(screen.getByRole("heading", { name: /Help Center/i })).toBeInTheDocument();
    expect(screen.getByRole("heading", { name: /Frequently Asked Questions/i })).toBeInTheDocument();
    expect(screen.getByText(/The answer will go here/i)).toBeInTheDocument();
  });

  it("renders Contact Us section and modal", () => {
    render(<Page />);
    expect(screen.getByRole("heading", { name: /Contact Us/i })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Contact Us/i })).toBeInTheDocument();
    expect(screen.getByTestId("contact-us-modal")).toBeInTheDocument();
  });

  it("calls open when Contact Us button is clicked", () => {
    render(<Page />);
    fireEvent.click(screen.getByRole("button", { name: /Contact Us/i }));
    expect(mockOverlayState.open).toHaveBeenCalledTimes(1);
  });
});
