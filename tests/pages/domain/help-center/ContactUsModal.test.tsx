import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ContactUsModal from "../../../../src/pages/domain/therapy/help-center/ContactUsModal";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useDispatch } from "react-redux";
import { OverlayTriggerState } from "react-stately";
import '@testing-library/jest-dom';

vi.mock("react-redux", () => ({
  useDispatch: vi.fn()
}));

vi.mock("@/store/slicers/therapist.slicer", () => ({
  contactUsAction: vi.fn()
}));

// Mock subcomponents
vi.mock("@/components/dialogs/FormDialog", () => ({
  default: ({ children, onSubmit, onCancel, title, confirmLabel }: any) => (
    <div data-testid="form-dialog">
      <h1>{title}</h1>
      <button onClick={onSubmit}>Submit</button>
      <button onClick={onCancel}>Cancel</button>
      {children}
      <div data-testid="confirm-label">{confirmLabel}</div>
    </div>
  )
}));

vi.mock("@/components/forms/InputComponent", () => ({
 default: ({ id, label, value, onChange, errorMessage }: any) => (
  <div>
    <label htmlFor={id}>{label}</label>
    <input
      id={id}
      value={value}
      onChange={(e) =>
        onChange({ target: { name: id, value: e.target.value } }) // 🔥 Important
      }
    />
    {errorMessage && <span>{errorMessage}</span>}
  </div>
)

}));

vi.mock("@/components/forms/TextAreaComponent", () => ({
 default: ({ id, label, value, onChange, errorMessage }: any) => (
  <div>
    <label htmlFor={id}>{label}</label>
    <textarea
      id={id}
      value={value}
      onChange={(e) =>
        onChange({ target: { name: id, value: e.target.value } }) // 🔥 Important
      }
    />
    {errorMessage && <span>{errorMessage}</span>}
  </div>
)

}));

describe("ContactUsModal", () => {
  const closeMock = vi.fn();
  const openMock = vi.fn();
    const toggleMock = vi.fn();
    const setOpenMock = vi.fn();
   const state: OverlayTriggerState = {
  isOpen: true,
  open: openMock,
  close: closeMock,
  toggle: toggleMock,
  setOpen: setOpenMock 
};

  const mockDispatch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useDispatch as any).mockReturnValue(mockDispatch);
  });

  it("renders with correct initial values", () => {
    render(<ContactUsModal email="<EMAIL>" state={state} />);
    expect(screen.getByLabelText("Subject")).toHaveValue("");
    expect(screen.getByLabelText("Reply Email")).toHaveValue("<EMAIL>");
    expect(screen.getByLabelText("Email Content")).toHaveValue("");
  });

  it("validates empty form", async () => {
    render(<ContactUsModal email="" state={state} />);
    fireEvent.click(screen.getByText("Submit"));

    await waitFor(() => {
      expect(screen.getByText("Subject is required")).toBeInTheDocument();
      expect(screen.getByText("Reply Email is required")).toBeInTheDocument();
      expect(screen.getByText("Email Content is required")).toBeInTheDocument();
    });
  });

  it("shows email format error", async () => {
    render(<ContactUsModal email="invalid-email" state={state} />);

    fireEvent.change(screen.getByLabelText("Reply Email"), {
      target: { value: "invalid" }
    });

    fireEvent.click(screen.getByText("Submit"));

    await waitFor(() => {
      expect(screen.getByText("Please enter a valid Email.")).toBeInTheDocument();
    });
  });


  it("resets form and closes on cancel", async () => {
    render(<ContactUsModal email="<EMAIL>" state={state} />);

    fireEvent.change(screen.getByLabelText("Subject"), {
      target: { value: "To be cleared" }
    });

    fireEvent.click(screen.getByText("Cancel"));

    await waitFor(() => {
      expect(closeMock).toHaveBeenCalled();
    });
  });

  
});
