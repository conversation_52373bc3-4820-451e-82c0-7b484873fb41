import { render, screen, fireEvent, waitFor, within } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "../../../src/store/slicers/auth.slicer";
import PatientListPage from "../../../src/pages/patients/PatientListPage";
import * as reactQuery from "@tanstack/react-query";
import * as useInViewModule from "react-intersection-observer";
import * as PatientRepoModule from "../../../src/repositories/PatientRepository";
import * as UserRepoModule from "../../../src/repositories/UserRepository";
import { beforeEach, describe, expect, it, vi } from "vitest";
import "@testing-library/jest-dom";

vi.mock("react-intersection-observer", () => ({
  useInView: () => ({
    ref: vi.fn(),
    inView: true,
  }),
}));
vi.mock("@tanstack/react-query", () => ({
  useInfiniteQuery: vi.fn(),
}));

const mockStore = configureStore({
  reducer: {
    auth: authReducer,
  },
  preloadedState: {
    auth: {
      token: "mock-token",
      refreshToken: "mock-refresh-token",
      user: null,
    },
  },
});

const mockPatient = {
  id: "1",
  firstname: "John",
  lastname: "Doe",
  email: "<EMAIL>",
  emailVerifiedAt: null,
  phone: "**********",
  phoneVerifiedAt: null,
  removedAt: null,
  deactivatedAt: null,
  acceptedAt: new Date(),
};

describe("PatientListPage", () => {
  let restoreMock: any;
  let resetPasswordMock: any;
  let activateMock: any;
  let refetchMock: any;
let restoreStateCloseMock: any;
let deactivateStateCloseMock: any;

  beforeEach(() => {
    vi.spyOn(useInViewModule, "useInView").mockReturnValue({ ref: vi.fn(), inView: true });

    restoreMock = vi.fn().mockResolvedValue(true);
    resetPasswordMock = vi.fn().mockResolvedValue(true);
    activateMock = vi.fn().mockResolvedValue(true);
      refetchMock = vi.fn();

  restoreStateCloseMock = vi.fn();
  deactivateStateCloseMock = vi.fn();

    vi.spyOn(PatientRepoModule, "default").mockImplementation(() => ({
      getAll: vi.fn().mockResolvedValue({ data: [mockPatient], meta: { nextPage: undefined } }),
      restore: restoreMock,
    }));

    vi.spyOn(UserRepoModule, "default").mockImplementation(() => ({
      resetPassword: resetPasswordMock,
      activate: activateMock,
    }));

    vi.spyOn(reactQuery, "useInfiniteQuery").mockReturnValue({
      error: null,
      data: {
        pages: [
          {
            data: [mockPatient],
            meta: { nextPage: undefined },
          },
        ],
      },
      isLoading: false,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });
  });

  it("renders the patient list page", () => {
    render(
      <Provider store={mockStore}>
        <PatientListPage />
      </Provider>
    );

    expect(screen.getByText("Patient List")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Search...")).toBeInTheDocument();
    expect(screen.getByText("John Doe")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    expect(screen.getByText("**********")).toBeInTheDocument();
  });

  it("searches patients", async () => {
    render(
      <Provider store={mockStore}>
        <PatientListPage />
      </Provider>
    );

    const input = screen.getByPlaceholderText("Search...");
    fireEvent.change(input, { target: { value: "Jane" } });
    await waitFor(() => {
      expect(input).toHaveValue("Jane");
    });
  });

  it("shows dialog when reset password triggered", async () => {
    render(
      <Provider store={mockStore}>
        <PatientListPage />
      </Provider>
    );

    const actionButtons = await screen.findAllByRole("button");
    const actionBtn = actionButtons.find((btn) => btn.querySelector(".fa-cogs"));
    expect(actionBtn).toBeDefined();

    fireEvent.click(actionBtn!);
    const resetBtn = await screen.findByText("Reset Password");
    fireEvent.click(resetBtn);

    await waitFor(() => {
      expect(screen.getByText("Reset Password")).toBeInTheDocument();
    });
  });

  it("shows error when reset password submitted with wrong email", async () => {
    render(
      <Provider store={mockStore}>
        <PatientListPage />
      </Provider>
    );

    const actionButtons = await screen.findAllByRole("button");
    const actionBtn = actionButtons.find((btn) => btn.querySelector(".fa-cogs"));
    fireEvent.click(actionBtn!);
    fireEvent.click(await screen.findByText("Reset Password"));

    const emailInput = screen.getByLabelText(/email/i);
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

    const submit = screen.getByRole("button", { name: /reset/i });
    fireEvent.click(submit);

    await waitFor(() => {
      expect(
        screen.getByText("Entered email does not match the patient's email")
      ).toBeInTheDocument();
    });
  });

  it("submits reset password with correct email", async () => {
    render(
      <Provider store={mockStore}>
        <PatientListPage />
      </Provider>
    );

    const actionButtons = await screen.findAllByRole("button");
    const actionBtn = actionButtons.find((btn) => btn.querySelector(".fa-cogs"));
    fireEvent.click(actionBtn!);
    fireEvent.click(await screen.findByText("Reset Password"));

    const emailInput = screen.getByLabelText(/email/i);
    fireEvent.change(emailInput, { target: { value: mockPatient.email } });

    const submit = screen.getByRole("button", { name: /reset/i });
    fireEvent.click(submit);

    await waitFor(() => {
      expect(resetPasswordMock).toHaveBeenCalledWith("/auth/forgot-password", mockPatient.email);
    });
  });


});
