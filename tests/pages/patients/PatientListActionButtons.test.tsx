import { render } from "@testing-library/react";
import { vi, describe, it, expect, beforeEach } from "vitest";
import PatientListActionButtons from "../../../src/pages/patients/PatientListActionButtons";
import { User } from "../../../src/types/user.interface";
import { OverlayTriggerState } from "react-stately";

let latestProps: any = {};

// Mock ActionButton and capture props
vi.mock("@/components/buttons/ActionButton", () => ({
  default: (props: any) => {
    latestProps = props;
    return <div>Mocked Action Button</div>;
  },
}));

// Full mock for OverlayTriggerState
const mockOverlayTriggerState = (): OverlayTriggerState => ({
  isOpen: false,
  open: vi.fn(),
  close: vi.fn(),
  toggle: vi.fn(),
  setOpen: vi.fn(),
});

describe("PatientListActionButtons", () => {
  const baseUser: Partial<User> = {
    id: 1,
    email: "<EMAIL>",
    acceptedAt: new Date("2024-01-01"),
  };

  const setup = (userOverrides: Partial<User> = {}) => {
    const user = { ...baseUser, ...userOverrides } as User;

    const setPatient = vi.fn();
    const setModalType = vi.fn();
    const setEnteredEmail = vi.fn();

    const restoreState = mockOverlayTriggerState();
    const deactivateState = mockOverlayTriggerState();
    const resetPasswordState = mockOverlayTriggerState();
    const deleteState = mockOverlayTriggerState();

    render(
      <PatientListActionButtons
        user={user}
        setPatient={setPatient}
        restoreState={restoreState}
        deactivateState={deactivateState}
        resetPasswordState={resetPasswordState}
        deleteState={deleteState}
        setModalType={setModalType}
        setEnteredEmail={setEnteredEmail}
      />
    );

    return {
      user,
      setPatient,
      setModalType,
      setEnteredEmail,
      restoreState,
      deactivateState,
      resetPasswordState,
      deleteState,
    };
  };

  beforeEach(() => {
    latestProps = {};
    vi.clearAllMocks();
  });

  it("handles restore button for removed user", () => {
    const { setPatient, restoreState, user } = setup({
      removedAt: "2024-01-01",
    });

    latestProps.onRestore();
    expect(setPatient).toHaveBeenCalledWith(user);
    expect(restoreState.open).toHaveBeenCalled();
  });

  it("handles activate and delete buttons for deactivated user", () => {
    const {
      setPatient,
      setModalType,
      deactivateState,
      deleteState,
      user,
    } = setup({ deactivatedAt: new Date("2024-01-01") });

    latestProps.onActivate();
    expect(setPatient).toHaveBeenCalledWith(user);
    expect(setModalType).toHaveBeenCalledWith("reactivate");
    expect(deactivateState.open).toHaveBeenCalled();

    latestProps.onDelete();
    expect(setPatient).toHaveBeenCalledWith(user);
    expect(deleteState.open).toHaveBeenCalled();
  });

  it("handles deactivate, delete, and reset password for active user", () => {
    const {
      setPatient,
      setModalType,
      deactivateState,
      deleteState,
      resetPasswordState,
      setEnteredEmail,
      user,
    } = setup();

    latestProps.onDeactivate();
    expect(setPatient).toHaveBeenCalledWith(user);
    expect(setModalType).toHaveBeenCalledWith("deactivate");
    expect(deactivateState.open).toHaveBeenCalled();

    latestProps.onDelete();
    expect(setPatient).toHaveBeenCalledWith(user);
    expect(deleteState.open).toHaveBeenCalled();

    latestProps.onResetPassword();
    expect(setPatient).toHaveBeenCalledWith(user);
    expect(resetPasswordState.open).toHaveBeenCalled();
    expect(setEnteredEmail).toHaveBeenCalledWith("");
    expect(latestProps.isResetPasswordDisabled).toBe(false);
  });

  it("disables reset password when acceptedAt is missing", () => {
    setup({ acceptedAt: undefined });

    expect(latestProps.isResetPasswordDisabled).toBe(true);
  });
});
