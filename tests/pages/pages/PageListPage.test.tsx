import { render, screen, fireEvent } from "@testing-library/react";
import PageListPage from "../../../src/pages/pages/PageListPage";
import { describe, expect, it, vi, beforeEach } from "vitest";
import { MemoryRouter } from "react-router-dom";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";

// Mock PageRepository
import PageRepository from "../../../src/repositories/PageRepository";
vi.mock("@/repositories/PageRepository");

const mockStartPage = vi.fn(() => Promise.resolve());
const mockMatcherInvolvement = vi.fn(() => Promise.resolve());
const mockGetAll = vi.fn().mockResolvedValue({
  data: [
    {
      id: 1,
      code: "page-001",
      title: "First Page",
      type: "info",
      matcherInvolvement: false,
      initialPage: false,
      category: "self",
    },
  ],
  meta: { nextPage: undefined },
});

// Mock react-intersection-observer
vi.mock("react-intersection-observer", () => ({
  useInView: () => ({
    ref: vi.fn(),
    inView: true,
    entry: undefined,
  }),
}));

// Mock tanstack/react-query
vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual<typeof import("@tanstack/react-query")>(
    "@tanstack/react-query"
  );

  return {
    ...actual,
    useInfiniteQuery: vi.fn(() => ({
      data: {
        pages: [
          {
            data: [
              {
                id: 1,
                code: "page-001",
                title: "First Page",
                type: "info",
                matcherInvolvement: false,
                initialPage: false,
                category: "self",
              },
            ],
            meta: {},
          },
        ],
      },
      fetchNextPage: vi.fn(),
      isLoading: false,
      error: null,
      refetch: vi.fn(),
    })),
  };
});

// Mock Redux store
const mockStore = configureMockStore([thunk]);
const store = mockStore({
  auth: {
    token: "mock-token",
    refreshToken: "mock-refresh-token",
  },
});

// Helper to render the component with required providers
const renderWithProviders = () =>
  render(
    <Provider store={store}>
      <MemoryRouter>
        <PageListPage />
      </MemoryRouter>
    </Provider>
  );

describe("PageListPage", () => {
  beforeEach(() => {
    PageRepository.mockImplementation(() => ({
      getAll: mockGetAll,
      startPage: mockStartPage,
      matcherInvolvement: mockMatcherInvolvement,
    }));
  });

  it("renders the page list", async () => {
    renderWithProviders();
    expect(await screen.findByText("First Page")).toBeInTheDocument();
    expect(screen.getByText("page-001")).toBeInTheDocument();
    expect(screen.getByText("info")).toBeInTheDocument();
  });


  it("navigates to Add Page form", () => {
    renderWithProviders();
    fireEvent.click(screen.getByText("Add Page"));
  });

  it("handles error state gracefully", async () => {
    // Override useInfiniteQuery to simulate error
    const useInfiniteQuery = (await import("@tanstack/react-query"))
      .useInfiniteQuery as unknown as vi.Mock;
    useInfiniteQuery.mockReturnValueOnce({
      data: undefined,
      fetchNextPage: vi.fn(),
      isLoading: false,
      error: "Network error",
      refetch: vi.fn(),
    });

    renderWithProviders();
    expect(screen.getByText(/Error/i)).toBeInTheDocument();
  });
});
