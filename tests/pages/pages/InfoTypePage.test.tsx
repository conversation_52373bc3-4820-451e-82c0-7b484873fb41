import { render, screen, fireEvent } from "@testing-library/react";
import InfoTypePage from "../../../src/pages/pages/InfoTypePage"
import { describe, it, expect, vi } from "vitest";

// Mock InputComponent to simulate rendering and interaction
vi.mock("@/components/forms/InputComponent", () => ({
  default: ({ label, value, onChange }: any) => (
    <div>
      <label>{label}</label>
      <input
        aria-label={label}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  ),
}));

describe("InfoTypePage", () => {
  it("renders both input fields with default values", () => {
    const setFormData = vi.fn();
    render(<InfoTypePage formData={{ info: "Info text", extra: "Extra text" }} setFormData={setFormData} />);

    expect(screen.getByLabelText("Info")).toHaveValue("Info text");
    expect(screen.getByLabelText("Extra Info.")).toHaveValue("Extra text");
  });

  it("calls setFormData correctly on info change", () => {
    const setFormData = vi.fn();
    render(<InfoTypePage formData={{ info: "", extra: "" }} setFormData={setFormData} />);

    fireEvent.change(screen.getByLabelText("Info"), { target: { value: "Updated Info" } });
    expect(setFormData).toHaveBeenCalledWith({ info: "Updated Info", extra: "" });
  });

  it("calls setFormData correctly on extra info change", () => {
    const setFormData = vi.fn();
    render(<InfoTypePage formData={{ info: "hello", extra: "" }} setFormData={setFormData} />);

    fireEvent.change(screen.getByLabelText("Extra Info."), { target: { value: "Updated Extra" } });
    expect(setFormData).toHaveBeenCalledWith({ info: "hello", extra: "Updated Extra" });
  });
});
