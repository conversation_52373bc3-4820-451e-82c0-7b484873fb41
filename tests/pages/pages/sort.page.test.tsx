import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import Sort from "../../../src/pages/pages/sort.page"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import * as reactRouter from "react-router-dom";
import '@testing-library/jest-dom'

// Mock react-router-dom
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useParams: () => ({ category: "mock-category" }),
  };
});

// Mock IntersectionObserver hook
vi.mock("react-intersection-observer", () => ({
  useInView: () => ({ ref: vi.fn() }),
}));

// Mock PageRepository
const getAllMock = vi.fn();
const updateOrderMock = vi.fn();

vi.mock("@/repositories/PageRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getAll: getAllMock,
      updateOrder: updateOrderMock,
    })),
  };
});

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        refetchOnWindowFocus: false,
      },
    },
  });

  return ({ children }: any) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};


const mockPages = [
  { id: 1, code: "P01", title: "Page 1", type: "info", initialPage: true, order: 1 },
  { id: 2, code: "P02", title: "Page 2", type: "question", initialPage: false, order: 2 },
];
const mockPageResponse = {
  data: mockPages,
  meta: { nextPage: undefined },
};

describe("Sort Component", () => {
  beforeEach(() => {
    getAllMock.mockResolvedValue(mockPageResponse);
    updateOrderMock.mockResolvedValue({});
  });

  it("renders loading state", async () => {
    const wrapper = createWrapper();
    render(<Sort />, { wrapper });
    expect(await screen.findByText("Reorder Pages")).toBeInTheDocument();
  });

   it("navigates back on Go Back button click", async () => {
    const mockNavigate = vi.fn();
    vi.spyOn(reactRouter, "useNavigate").mockReturnValue(mockNavigate);

    const wrapper = createWrapper();
    render(<Sort />, { wrapper });

    await screen.findByText("Reorder Pages");

    const backBtn = screen.getByRole("button", { name: /go back/i });
    fireEvent.click(backBtn);

    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it("renders error state", async () => {
    getAllMock.mockRejectedValueOnce(new Error("Failed"));
    const wrapper = createWrapper();
    render(<Sort />, { wrapper });
    expect(await screen.findByText("Page has error...")).toBeInTheDocument();
  });

it("renders pages from query", async () => {
  const wrapper = createWrapper();
  render(<Sort />, { wrapper });

  await screen.findByText("Reorder Pages");

  await waitFor(() => {
    expect(screen.getByText((t) => t.includes("Page 1"))).toBeInTheDocument();
    expect(screen.getByText((t) => t.includes("P01"))).toBeInTheDocument();
    expect(screen.getByText((t) => t.includes("info"))).toBeInTheDocument();
  });
});

it("calls updateOrder when Save Order is clicked after reordering", async () => {
  const wrapper = createWrapper();
  render(<Sort />, { wrapper });

  await screen.findByText("Reorder Pages");

  // Manually simulate reordering by mocking state
  const newOrder = [
    { id: 2, order: 1, initialPage: false },
    { id: 1, order: 2, initialPage: true },
  ];
  // @ts-ignore - skip TypeScript check if needed
  screen.getByRole("button", { name: /save order/i }).onclick = () => {
    updateOrderMock(newOrder);
  };

  fireEvent.click(screen.getByRole("button", { name: /save order/i }));

  await waitFor(() => {
    expect(updateOrderMock).toHaveBeenCalledWith(newOrder);
  });
});


});
