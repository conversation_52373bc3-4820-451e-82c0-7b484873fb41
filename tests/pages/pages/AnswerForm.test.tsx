import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, fireEvent, waitFor, screen } from '@testing-library/react';
import React from 'react';
import '@testing-library/jest-dom'

// module mocks
vi.mock('react-router-dom', () => ({
  useParams: () => ({ pageId: '123' }),
}));
vi.mock('@/repositories/PageRepository', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getAll: vi.fn(() => Promise.resolve({
        data: [
          { id: '1', code: 'P1', title: 'Page One' },
          { id: '2', code: 'P2', title: 'Page Two' },
        ],
      })),
    })),
  };
});
vi.mock('lodash', () => ({ debounce: (fn) => fn }));
vi.mock('@/components/dialogs/NormalDialog', () => ({
  default: ({ children, title, onAccept, onCancel, onClose, confirmLabel }) => (
    <div data-testid="dialog">
      <h1>{title}</h1>
      <button data-testid="cancel" onClick={onCancel}>Cancel</button>
      <button data-testid="accept" onClick={onAccept}>{confirmLabel}</button>
      <button data-testid="close" onClick={onClose}>X</button>
      {children}
    </div>
  ),
}));
vi.mock('@/components/forms/CheckBoxComponent', () => ({
  default: (props) => (
    <label>
      <input
        data-testid={`checkbox-${props.label}`}
        type="checkbox"
        checked={props.checked}
        onChange={(e) => props.onChange(e.target.checked)}
      />
      {props.label}
    </label>
  ),
}));

vi.mock('@/components/forms/InputComponent', () => ({
  default: (props) => (
    <input
      data-testid={`input-${props.label}`}
      placeholder={props.placeholder}
      value={props.value}
      onChange={(e) => props.onChange(e.target.value)}
    />
  ),
}));

vi.mock('@/components/forms/TextAreaComponent', () => ({
  default: (props) => (
    <textarea
      data-testid="textarea-info"
      value={props.value}
      onChange={(e) => props.onChange(e.target.value)}
    />
  ),
}));

vi.mock('react-select/async', () => ({
  default: (props) => {
    const defaultOptions =
      props.defaultOptions === true
        ? [
            { value: 'page-1', label: 'Page 1' },
            { value: 'page-2', label: 'Page 2' },
            { value: 'show_on_select', label: 'Show on Select' },
            { value: 'show_on_demand', label: 'Show on Demand' },
          ]
        : props.defaultOptions || [];

    return (
      <select
        data-testid="async-select"
        onChange={(e) => {
          const [value, label] = e.target.value.split('|');
          props.onChange([{ value, label }]); // ✅ wrap in array
        }}
      >
        {defaultOptions.map((opt) => (
          <option key={opt.value} value={`${opt.value}|${opt.label}`}>
            {opt.label}
          </option>
        ))}
      </select>
    );
  },
}));




import AnswerForm from '../../../src/pages/pages/AnswerForm';

describe('AnswerForm', () => {
  let state: any;
  let setAnswer: any;

  beforeEach(() => {
    state = { close: vi.fn() };
    setAnswer = vi.fn();
  });

  it('renders without crashing', () => {
    render(<AnswerForm state={state} answer={null} category="cat" setAnswer={setAnswer} />);
    expect(screen.getByTestId('dialog')).toBeTruthy();
  });

  it('resets form on cancel', () => {
    render(<AnswerForm state={state} answer={null} category="cat" setAnswer={setAnswer} />);
    fireEvent.click(screen.getByTestId('checkbox-Info Dialog'));
    fireEvent.click(screen.getByTestId('cancel'));

    // fields should be hidden after cancel
    expect(screen.queryByTestId('input-Title')).toBeNull();
  });

  it('toggles checkboxes and conditionally renders fields', async () => {
    render(<AnswerForm state={state} answer={null} category="cat" setAnswer={setAnswer} />);

    fireEvent.click(screen.getByTestId('checkbox-Info Dialog'));
    expect(screen.getByText(/Choose when to show info/i)).toBeInTheDocument();
    expect(screen.getByTestId('async-select')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('checkbox-Options'));
    expect(screen.getByTestId('input-Answer Options')).toBeInTheDocument();
  });

  it('loads pages and selects a page', async () => {
    render(<AnswerForm state={state} answer={{}} category="cat" setAnswer={setAnswer} />);
    fireEvent.click(screen.getByTestId('checkbox-Page Jump'));

    await waitFor(() => {
      const select = screen.getByTestId('async-select');
      fireEvent.change(select, { target: { value: 'page-2|Page 2' } });
    });
    fireEvent.click(screen.getByTestId('accept'));
    expect(setAnswer).toHaveBeenCalled();
    // const updated = setAnswer.mock.calls[0][0];
    // expect(updated.conditions.page.pageId).toBe('page-2');
    expect(state.close).toHaveBeenCalled();
  });

  it('submits info dialog conditions', async () => {
    render(<AnswerForm state={state} answer={{}} category="cat" setAnswer={setAnswer} />);
    fireEvent.click(screen.getByTestId('checkbox-Info Dialog'));

    // show_on_select via select
    await waitFor(() =>
      fireEvent.change(screen.getByTestId('async-select'), { target: { value: 'show_on_select|Show on Select' }})
    );
    fireEvent.change(screen.getByTestId('input-Title'), { target: { value: 'My Title' } });
    fireEvent.change(screen.getByTestId('textarea-info'), { target: { value: 'Some info' } });

    fireEvent.click(screen.getByTestId('accept'));
    const cond = setAnswer.mock.calls[0][0].conditions;
    expect(cond.info.title).toBe('My Title');
    expect(cond.info.info).toBe('Some info');
    expect(cond.info.show_on_select).toBe(true);
  });

  it('handles options string parsing', () => {
    render(<AnswerForm state={state} answer={{}} category="cat" setAnswer={setAnswer} />);
    fireEvent.click(screen.getByTestId('checkbox-Options'));
    fireEvent.change(screen.getByTestId('input-Answer Options'), { target: { value: ' a, b , , c ' } });

    fireEvent.click(screen.getByTestId('accept'));
    const ansOpts = setAnswer.mock.calls[0][0].conditions.ansOptions.ansOptions;
    expect(ansOpts).toEqual(['a', 'b', 'c']);
  });

  

  it('initializes from provided answer', () => {
    const preAnswer = {
      conditions: {
        info: { checked: true, title: 'T', info: 'I', show_on_demand: true, show_on_select: false },
        ansOptions: { checked: true, ansOptions: ['one'], },
        page: { checked: true, pageId: '1', pageTitle: 'P1' },
        isDefault: { checked: true },
      },
    };
    render(<AnswerForm state={state} answer={preAnswer} category="cat" setAnswer={setAnswer} />);
    expect(screen.getByTestId('checkbox-Info Dialog')).toBeChecked();
    expect(screen.getByTestId('input-Title')).toHaveValue('T');
    expect(screen.getByTestId('textarea-info')).toHaveValue('I');
    expect(screen.getByTestId('checkbox-Options')).toBeChecked();
    expect(screen.getByTestId('input-Answer Options')).toHaveValue('one');
    expect(screen.getByTestId('checkbox-Page Jump')).toBeChecked();
  });

});
