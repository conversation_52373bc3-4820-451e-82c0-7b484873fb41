// tests/pages/pages/QuestionnaireTypePage.test.tsx

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, fireEvent } from '@testing-library/react';
import QuestionnaireTypePage from '../../../src/pages/pages/QuestionnaireTypePage';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import "@testing-library/jest-dom"
vi.mock('@/repositories/QuestionnaireRepository', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getAll: vi.fn().mockResolvedValue({ data: [] }),
    })),
  };
});

// Mock AnswerRepository
vi.mock('@/repositories/AnswerRepository', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getAll: vi.fn().mockResolvedValue({ data: [] }),
    })),
  };
});

vi.mock('lodash', () => ({ debounce: (fn: any) => fn }));
vi.mock('@/components/dialogs/NormalDialog', () => ({
  default: ({ state, children }: any) => state?.isOpen ? <div data-testid="dialog">{children}</div> : null
}));
vi.mock('@/components/forms/CheckBoxComponent', () => ({
  default: ({ onChange, checked }: any) => (
    <input data-testid="checkbox" type="checkbox" checked={checked} onChange={e => onChange(e.target.checked)} />
  )
}));
vi.mock('react-perfect-scrollbar', () => ({ default: ({ children }: any) => <div>{children}</div> }));
vi.mock('@react-stately/overlays', () => ({
  useOverlayTriggerState: () => ({ isOpen: false, open: vi.fn(), close: vi.fn() }),
}));

const createMockStore = () =>
  configureStore({
    reducer: {
      auth: () => ({ token: 'mock-token', refreshToken: 'mock-refresh-token' }),
    },
  });

describe('QuestionnaireTypePage', () => {
  const defaultProps = {
    formData: { category: 'cat', questionnaireType: null, required: false },
    setFormData: vi.fn(),
    question: null,
    setQuestion: vi.fn(),
    answers: [],
    setAnswers: vi.fn(),
    answer: undefined,
    setAnswer: vi.fn(),
    removedAnswers: [],
    setRemovedAnswers: vi.fn(),
    loadPages: vi.fn(),
    copiedAnswers: [],
    followThroughAnswers: undefined,
  };

  const renderWithProvider = (ui: React.ReactNode) => {
    const store = createMockStore();
    return render(<Provider store={store}>{ui}</Provider>);
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders no answer placeholder', () => {
    const { getByText } = renderWithProvider(<QuestionnaireTypePage {...defaultProps} />);
    expect(getByText('No answers selected yet.')).toBeDefined();
  });

  it('updates formData when checkbox is toggled', () => {
    const { getByTestId } = renderWithProvider(<QuestionnaireTypePage {...defaultProps} />);
    const checkbox = getByTestId('checkbox') as HTMLInputElement;
    fireEvent.click(checkbox);
    expect(defaultProps.setFormData).toHaveBeenCalled();
  });

  it('deduplicates answers if no followThroughFor is provided', () => {
  const props = {
    ...defaultProps,
    answers: [
      { label: 'A', value: 1, order: null, conditions: {} },
      { label: 'A', value: 2, order: null, conditions: {} },
      { label: 'B', value: 3, order: null, conditions: {} },
    ],
  };

  const { queryAllByText } = renderWithProvider(<QuestionnaireTypePage {...props} />);

  // Assert only one instance of each label is visible
  expect(queryAllByText('A').length).toBe(1);
  expect(queryAllByText('B').length).toBe(1);
});


it('orders answers with null at the end', () => {
  const props = {
    ...defaultProps,
    answers: [
      { label: 'X', value: 1, order: 3, conditions: {} },
      { label: 'Y', value: 2, order: null, conditions: {} },
      { label: 'Z', value: 3, order: 1, conditions: {} },
    ],
  };

  const { getAllByRole } = renderWithProvider(<QuestionnaireTypePage {...props} />);
  const inputs = getAllByRole('spinbutton');
  
  expect(inputs[0]).toHaveValue(1);
  expect(inputs[1]).toHaveValue(2); 
  expect(inputs[2]).toHaveValue(3); 
});


it('initializes followThroughAnswers into answers', () => {
  const fa = [
    {
      followingToAnswer: { id: 10, answer: 'To me' },
      followingByAnswer: { id: 20, answer: 'By me' },
    },
  ];
  const props = { ...defaultProps, followThroughAnswers: fa };
  renderWithProvider(<QuestionnaireTypePage {...props} />);
  expect(props.setAnswers).toHaveBeenCalledWith([
    {
      label: 'To me',
      value: 10,
      condition: {},
      followThrough: { label: 'By me', value: 20 },
    },
  ]);
});

// it('opens delete dialog', async () => {
//   const props = {
//     ...defaultProps,
//     answers: [{ label: 'A', value: 1, order: 1 }],
//   };

//   const { getByTestId, getByText } = renderWithProvider(<QuestionnaireTypePage {...props} />);
  
//   const deleteButton = getByTestId('delete-button');
//   fireEvent.click(deleteButton);

//   await waitFor(() => {
//     const dialogText = getByText((_, element) =>
//       element?.textContent?.toLowerCase().includes("are you sure you want to remove this answer")
//     );
//     expect(dialogText).toBeInTheDocument();
//   });
// });



});


