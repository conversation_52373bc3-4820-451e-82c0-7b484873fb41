import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import ValueTypePage from "../../../src/pages/pages/ValueTypePage";
import { describe, it, vi, beforeEach, expect } from "vitest";
import '@testing-library/jest-dom'
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "../../../src/store/slicers/auth.slicer";
import { Provider } from "react-redux";
import userEvent from "@testing-library/user-event";

const mockStore = configureStore({
  reducer: {
    auth: authReducer,
  },
  preloadedState: {
    auth: {
      token: "mock-token",
      refreshToken: "mock-refresh-token",
      user: null,
    },
  },
});

vi.mock("react-stately", () => {
  return {
    useOverlayTriggerState: () => ({
      isOpen: true,
      open: vi.fn(),
      close: vi.fn(),
    }),
  };
});


vi.mock("uuid", () => ({
  v4: vi.fn(() => "mock-uuid"),
}));

vi.mock("react-select/async", async () => {
  const actual = await vi.importActual("react-select/async");
  return {
    ...actual,
    __esModule: true,
    default: ({ loadOptions, onChange, value, placeholder, ...props }: any) => (
      <select
        data-testid={props.id || "async-select"}
        onChange={(e) =>
          onChange({ label: e.target.value, value: e.target.value })
        }
        value={value?.value || ""}
      >
        <option value="">{placeholder}</option>
        <option value="q1">Question 1</option>
        <option value="a1">Answer 1</option>
        <option value="a2">Answer 2</option>
      </select>
    ),
  };
});

vi.mock("react-perfect-scrollbar", () => ({
  __esModule: true,
  default: ({ children }: any) => <div>{children}</div>,
}));

vi.mock("@/repositories/QuestionnaireRepository", () => ({
  default: vi.fn().mockImplementation(() => ({
    getAll: vi.fn().mockResolvedValue({
      data: [{ id: "q1", question: "Question 1" }],
    }),
  })),
}));

vi.mock("@/repositories/AnswerRepository", () => ({
  default: vi.fn().mockImplementation(() => ({
    getAll: vi.fn().mockResolvedValue({
      data: [
        { id: "a1", answer: "Answer 1", answerGroup: "group1" },
        { id: "a2", answer: "Answer 2", answerGroup: "group1" },
      ],
    }),
  })),
}));

const defaultProps = {
  formData: { category: { value: "cat1" }, type: { value: "value" } },
  setFormData: vi.fn(),
  question: undefined,
  setQuestion: vi.fn(),
  answers: [],
  setAnswers: vi.fn(),
  answer: undefined,
  setAnswer: vi.fn(),
  removedAnswers: [],
  setRemovedAnswers: vi.fn(),
};

const renderWithProvider = (ui: React.ReactNode) => {
  return render(<Provider store={mockStore}>{ui}</Provider>);
};

describe("ValueTypePage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders select placeholders and disables interaction if no category", () => {
    renderWithProvider(<ValueTypePage {...defaultProps} formData={{}} />);
    expect(screen.getByText(/Select `Page for:` and `Question`/)).toBeInTheDocument();
  });

   it("displays 'No sections added yet' if no groups", () => {
    renderWithProvider(<ValueTypePage {...defaultProps} />);
    expect(screen.getByText(/No sections added yet/)).toBeInTheDocument();
  });

   it("handles answer selection changes", async () => {
    const answers = [
      {
        label: "",
        value: "",
        position: "left",
        answerGroup: "group1",
        uid: "a-uid-1",
        conditions: {},
      },
      {
        label: "",
        value: "",
        position: "right",
        answerGroup: "group1",
        uid: "a-uid-2",
        conditions: {},
      },
    ];
    const setAnswers = vi.fn();
    renderWithProvider(
      <ValueTypePage
        {...defaultProps}
        answers={answers}
        setAnswers={setAnswers}
      />
    );

    const selects = screen.getAllByTestId(/group1-/);
    fireEvent.change(selects[0], { target: { value: "a1" } });
    await waitFor(() => {
      expect(setAnswers).toHaveBeenCalled();
    });
  });

 it("opens and confirms delete dialog", async () => {
  const setAnswer = vi.fn();
  const setAnswers = vi.fn();
  const setRemovedAnswers = vi.fn();

  renderWithProvider(
    <ValueTypePage
      {...defaultProps}
      question={{ label: "Question 1", value: "q1" }}
      answer={{
        label: "Answer 1",
        value: "a1",
        uid: "1",
        position: "left",
        answerGroup: "g",
        conditions: {},
      }}
      setAnswer={setAnswer}
      answers={[
        {
          label: "Answer 1",
          value: "a1",
          uid: "1",
          position: "left",
          answerGroup: "g",
          conditions: {},
        },
      ]}
      setAnswers={setAnswers}
      removedAnswers={[]}
      setRemovedAnswers={setRemovedAnswers}
    />
  );

  const deleteButton = await screen.findByRole("button", { name: /Delete/i });
  fireEvent.click(deleteButton);

  await waitFor(() => {
    expect(setRemovedAnswers).toHaveBeenCalledWith(["a1"]);
    expect(setAnswers).toHaveBeenCalled();
    expect(setAnswer).toHaveBeenCalledWith(undefined);
  });
});


  it("opens and cancels delete dialog", async () => {
    const setAnswer = vi.fn();
    renderWithProvider(
      <ValueTypePage
        {...defaultProps}
        answer={{ label: "Answer 1", value: "a1", uid: "1", position: "left", answerGroup: "g", conditions: {} }}
        setAnswer={setAnswer}
      />
    );

   const cancelButtons = screen.getAllByText("Cancel");
   fireEvent.click(cancelButtons[0]);
    await waitFor(() => {
      expect(setAnswer).toHaveBeenCalledWith(undefined);
    });
  });
});
