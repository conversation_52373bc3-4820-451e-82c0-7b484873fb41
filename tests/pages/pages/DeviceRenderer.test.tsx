import { render, screen } from "@testing-library/react";
import Devi<PERSON><PERSON>ender<PERSON> from "../../../src/pages/pages/DeviceRenderer";
import { describe, it, expect } from "vitest";
import '@testing-library/jest-dom'

const mockQuestion = {
  label: "Sample Question",
};

const baseAnswer = [{ label: "Answer 1" }, { label: "Answer 2" }];

describe("DeviceRenderer", () => {
  it("renders InfoSection correctly", () => {
    render(
      <DeviceRenderer
        formData={{ type: { value: "info" }, title: "Info Title", info: "Some Info", extra: "Extra Info", button: "Continue" }}
        question={null}
        answers={[]}
      />
    );
    expect(screen.getByText("Info Title")).toBeInTheDocument();
    expect(screen.getByText("Some Info")).toBeInTheDocument();
    expect(screen.getByText("Extra Info")).toBeInTheDocument();
    expect(screen.getByText("Continue")).toBeInTheDocument();
  });

  it("renders QuestionnaireSection with multiple answers", () => {
    render(
      <DeviceRenderer
        formData={{ type: { value: "questionnaire" }, questionnaireType: { value: "multiple" }, button: "Next", required: false }}
        question={mockQuestion}
        answers={baseAnswer}
      />
    );
    expect(screen.getByText("Sample Question")).toBeInTheDocument();
    expect(screen.getAllByText("Answer 1").length).toBeGreaterThan(0);
    expect(screen.getByText("Next")).toBeInTheDocument();
    expect(screen.getByText("Skip this question.")).toBeInTheDocument();
  });

  it("renders ValueSection with grouped answers", () => {
    const answersWithGroup = [
      { label: "Group A1", answerGroup: "Group A" },
      { label: "Group A2", answerGroup: "Group A" },
      { label: "Group B1", answerGroup: "Group B" },
      { label: "Group B2", answerGroup: "Group B" },
    ];

    render(
      <DeviceRenderer
        formData={{ type: { value: "value" }, button: "Next", required: false }}
        question={mockQuestion}
        answers={answersWithGroup}
      />
    );

    expect(screen.getByText("Sample Question")).toBeInTheDocument();
    expect(screen.getAllByText("Most").length).toBeGreaterThan(0);
    expect(screen.getByText("Group A1")).toBeInTheDocument();
    expect(screen.getByText("Group A2")).toBeInTheDocument();
    expect(screen.getByText("Group B1")).toBeInTheDocument();
    expect(screen.getByText("Group B2")).toBeInTheDocument();
    expect(screen.getByText("Skip this question.")).toBeInTheDocument();
  });

  it("renders Selectable section", () => {
    render(
      <DeviceRenderer
        formData={{ type: { value: "selectable" }, button: "Submit" }}
        question={mockQuestion}
        answers={baseAnswer}
      />
    );
    expect(screen.getByText("Sample Question")).toBeInTheDocument();
    expect(screen.getByText("Answer 1")).toBeInTheDocument();
    expect(screen.getByText("Answer 2")).toBeInTheDocument();
    expect(screen.getByText("Submit")).toBeInTheDocument();
  });

  it("renders fallback for unknown form type", () => {
    render(
      <DeviceRenderer
        formData={{ type: { value: "unknown" }, button: "Unknown Button" }}
        question={mockQuestion}
        answers={[]}
      />
    );
    expect(screen.getByText("Registration Page")).toBeInTheDocument();
    expect(screen.getByText("Preview not available")).toBeInTheDocument();
  });

  it("renders fallback when no form type is defined", () => {
    render(
      <DeviceRenderer
        formData={{ button: "No Type Button" }}
        question={mockQuestion}
        answers={[]}
      />
    );
    expect(screen.getByText("Page not selected")).toBeInTheDocument();
  });
});
