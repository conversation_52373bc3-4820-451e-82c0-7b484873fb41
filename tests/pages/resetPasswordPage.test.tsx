import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { BrowserRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import ResetPasswordPage from "../../src/pages/auth/ResetPasswordPage";
import { useApiClient } from "../../src/utils/api.util";
import { HttpStatusCode } from "axios";
import "@testing-library/jest-dom";
import { describe, it, expect, beforeEach, vi } from "vitest";

vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("react-router-dom", async () => {
  const actual = await import("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const mockNavigate = vi.fn();

describe("ResetPasswordPage", () => {
  const mockClient = {
    post: vi.fn(),
  };

  beforeEach(() => {
    (useApiClient as any).mockReturnValue(mockClient);
    mockNavigate.mockClear();
    mockClient.post.mockClear();
  });

  const renderComponent = () => {
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated: false }),
      },
    });

    return render(
      <Provider store={store}>
        <BrowserRouter>
          <ResetPasswordPage />
        </BrowserRouter>
      </Provider>
    );
  };

  it("renders the reset password form", () => {
    renderComponent();
    expect(screen.getByText("Update your password.")).toBeInTheDocument();
    expect(screen.getByLabelText("Password")).toBeInTheDocument();
    expect(screen.getByLabelText("Confirm Password")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Update Password" })).toBeInTheDocument();
  });

  it("validates password requirements", () => {
    renderComponent();
    const passwordInput = screen.getByLabelText("Password");
    
    fireEvent.change(passwordInput, { target: { value: "short" } });
    expect(screen.getByText(/Password must be at least 8 characters long/)).toBeInTheDocument();
    
    fireEvent.change(passwordInput, { target: { value: "password123!" } });
    expect(screen.getByText(/Password must be at least one uppercase letter/)).toBeInTheDocument();
    
    fireEvent.change(passwordInput, { target: { value: "Password!" } });
    expect(screen.getByText(/Password must be at least one number/)).toBeInTheDocument();
    
    fireEvent.change(passwordInput, { target: { value: "Password123" } });
    expect(screen.getByText(/Password must be at least one special character/)).toBeInTheDocument();
    
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    expect(screen.queryByText(/Password must be/)).not.toBeInTheDocument();
  });

  it("validates password confirmation match", () => {
    renderComponent();
    const passwordInput = screen.getByLabelText("Password");
    const confirmPasswordInput = screen.getByLabelText("Confirm Password");
    
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Different123!" } });
    
    expect(screen.getByText(/Passwords do not match/)).toBeInTheDocument();
  });

  it("handles successful password reset for therapist", async () => {
    mockClient.post.mockResolvedValueOnce({
      status: HttpStatusCode.Created,
      data: {
        message: "Password updated successfully",
        role: "therapist",
      },
    });

    renderComponent();
    
    const passwordInput = screen.getByLabelText("Password");
    const confirmPasswordInput = screen.getByLabelText("Confirm Password");
    const submitButton = screen.getByRole("button", { name: "Update Password" });
    
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText((content) => {
        return content.includes("Password updated successfully") && 
               content.includes("Redirecting to login page");
      })).toBeInTheDocument();
    });
    
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/login");
    }, { timeout: 3000 });
  });

  it("handles successful password reset for patient", async () => {
    mockClient.post.mockResolvedValueOnce({
      status: HttpStatusCode.Created,
      data: {
        message: "Password updated successfully",
        role: "patient",
      },
    });

    renderComponent();
    
    const passwordInput = screen.getByLabelText("Password");
    const confirmPasswordInput = screen.getByLabelText("Confirm Password");
    const submitButton = screen.getByRole("button", { name: "Update Password" });
    
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText((content) => {
        return content.includes("Password updated successfully") && 
               content.includes("You can now login to your account from mobile");
      })).toBeInTheDocument();
    });
  });

  it("handles API error", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        data: {
          message: "Invalid token",
        },
      },
    });

    renderComponent();
    
    const passwordInput = screen.getByLabelText("Password");
    const confirmPasswordInput = screen.getByLabelText("Confirm Password");
    const submitButton = screen.getByRole("button", { name: "Update Password" });
    
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Invalid token")).toBeInTheDocument();
    });
  });

  it("disables submit button when form is invalid", () => {
    renderComponent();
    const submitButton = screen.getByRole("button", { name: "Update Password" });
    expect(submitButton).toBeDisabled();
  });

  it("enables submit button when form is valid", () => {
    renderComponent();
    const passwordInput = screen.getByLabelText("Password");
    const confirmPasswordInput = screen.getByLabelText("Confirm Password");
    const submitButton = screen.getByRole("button", { name: "Update Password" });
    
    fireEvent.change(passwordInput, { target: { value: "Password123!" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Password123!" } });
    
    expect(submitButton).not.toBeDisabled();
  });
});