import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import ForgotPassword from "../../src/pages/auth/ForgotPassword";
import { useApiClient } from "../../src/utils/api.util";
import { EMAIL_REQUIRED_ERROR } from "../../src/pages/auth/constants";
import "@testing-library/jest-dom";
import { describe, it, expect, beforeEach, vi } from "vitest";

// Mock the API client
vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

// Mock the router with importOriginal to preserve the actual implementation
vi.mock("react-router-dom", async () => {
  const actual = await import("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Mock navigate function
const mockNavigate = vi.fn();

describe("ForgotPassword", () => {
  const mockClient = {
    post: vi.fn(),
  };

  beforeEach(() => {
    (useApiClient as any).mockReturnValue(mockClient);
    mockNavigate.mockClear();
    mockClient.post.mockClear();
  });

  const renderComponent = () => {
    const store = configureStore({
      reducer: {
        auth: () => ({ isAuthenticated: false }),
      },
    });

    return render(
      <Provider store={store}>
        <BrowserRouter>
          <ForgotPassword />
        </BrowserRouter>
      </Provider>
    );
  };

  it("renders the forgot password form", () => {
    renderComponent();
    expect(screen.getByText("Forgot your password?")).toBeInTheDocument();
    expect(screen.getByLabelText("E-mail")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Send Magic Link" })).toBeInTheDocument();
    expect(screen.getByText(/Enter your email address below/)).toBeInTheDocument();
  });

  it("focuses on email input when component mounts", () => {
    renderComponent();
    expect(screen.getByLabelText("E-mail")).toHaveFocus();
  });

  it("displays error when submitting empty email", () => {
    renderComponent();
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.click(submitButton);
    
    expect(screen.getByText(EMAIL_REQUIRED_ERROR)).toBeInTheDocument();
  });

  it("handles successful magic link request", async () => {
    mockClient.post.mockResolvedValueOnce({
      data: {
        message: "Magic link sent to your email."
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Magic link sent to your email.")).toBeInTheDocument();
    });
    
    // Verify API call
    expect(mockClient.post).toHaveBeenCalledWith("/auth/forgot-password", { email: "<EMAIL>" });
    
    // Check navigation after delay
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith("/auth/login");
    }, { timeout: 3100 });
  });

  it("handles API error", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        data: {
          message: "Email not found",
        },
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Email not found")).toBeInTheDocument();
    });
  });

  it("shows generic error message when API response is missing error message", async () => {
    mockClient.post.mockRejectedValueOnce({
      response: {
        data: {}
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText("Something went wrong.")).toBeInTheDocument();
    });
  });

  it("disables form submission while processing", async () => {
    // Create a delayed promise that won't resolve immediately
    mockClient.post.mockImplementationOnce(() => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({ data: { message: "Magic link sent to your email." } });
        }, 100);
      });
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    // Try clicking again immediately
    fireEvent.click(submitButton);
    
    // Should only be called once because second click should be ignored
    expect(mockClient.post).toHaveBeenCalledTimes(1);
  });

  it("clears the email field after successful submission", async () => {
    mockClient.post.mockResolvedValueOnce({
      data: {
        message: "Magic link sent to your email."
      },
    });

    renderComponent();
    
    const emailInput = screen.getByLabelText("E-mail");
    const submitButton = screen.getByRole("button", { name: "Send Magic Link" });
    
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(emailInput).toHaveValue("");
    });
  });

  it("verifies the 'Go back to Log In Page' link", () => {
    renderComponent();
    
    const loginLink = screen.getByText("Log In Page");
    expect(loginLink).toBeInTheDocument();
    expect(loginLink.closest('a')).toHaveAttribute('href', '/auth/login');
  });
});