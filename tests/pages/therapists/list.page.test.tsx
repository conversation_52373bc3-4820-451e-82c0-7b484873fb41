import React from "react";
import { render, screen, fireEvent,  } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import TherapistListPage from "../../../src/pages/therapists/TherapistListPage";
import { useInfiniteQuery } from "@tanstack/react-query";
import "@testing-library/jest-dom";
import { describe, it, expect, vi, beforeEach } from "vitest";
import TherapistRepository from '../../../src/repositories/TherapistRepository'
import UserRepository from '../../../src/repositories/UserRepository'
import ActionButton from '../../../src/components/buttons/ActionButton'

vi.mock("@tanstack/react-query", () => ({
    useInfiniteQuery: vi.fn(),
}));

vi.mock("react-router-dom", async () => {
    const actual = await import("react-router-dom");
    return {
        ...actual,
        useNavigate: vi.fn(),
    };
});

vi.mock('../../../src/components/buttons/ActionButton', () => ({
    __esModule: true,
    default: vi.fn((props) => {
        const {
            onEdit,
            onRejectedReason,
            onDelete,
            onDeactivate,
            onActivate,
            onResetPassword,
            onViewProfile,
            setTherapist,
            isResetPasswordDisabled
        } = props;
       
        return (
            <div>
                {onEdit && (
                    <button onClick={onEdit} data-testid="edit-button">
                        Edit
                    </button>
                )}
                {onRejectedReason && (
                    <button onClick={onRejectedReason} data-testid="rejected-reasons-button">Rejected Reasons</button>
                )}
                {onDelete && <button onClick={onDelete}>Delete</button>}
                {onDeactivate && <button onClick={onDeactivate}>Deactivate</button>}
                {onActivate && <button onClick={onActivate}>Activate</button>}
                {onResetPassword && (
                    <button
                        onClick={() => {
                            if (setTherapist) {
                                setTherapist({
                                    id: 1,
                                    firstname: "John",
                                    lastname: "Doe",
                                    email: "<EMAIL>",
                                    // emailVerifiedAt: "2023-01-01T00:00:00Z",
                                });
                            }
                            onResetPassword();
                        }}
                        data-testid="reset-password-button"
                        disabled={isResetPasswordDisabled}
                    >
                        Reset Password
                    </button>
                )}
                {onViewProfile && <button onClick={onViewProfile}>View Profile</button>}
            </div>
        );
    }),
}));

vi.mock("@headlessui/react", async () => {
    const actual = await vi.importActual("@headlessui/react");
    return {
        ...actual,
        Menu: actual.Menu,
        Transition: actual.Transition,
        MenuItem: ({ children }: any) => children({ active: true }),
    };
});

describe("TherapistListPage", () => {
    const mockFetchNextPage = vi.fn();
    const mockRefetch = vi.fn();

    beforeEach(() => {
        (useInfiniteQuery as any).mockReturnValue({
            data: {
                pages: [
                    {
                        data: [
                            { id: 1, firstname: "John", lastname: "Doe", email: "<EMAIL>", emailVerifiedAt: "2023-01-01T00:00:00Z"},
                            { id: 2, firstname: "Jane", lastname: "Smith", email: "<EMAIL>", emailVerifiedAt: null},
                        ],
                        meta: { nextPage: null },
                    },
                ],
            },
            isLoading: false,
            isFetchingNextPage: false,
            fetchNextPage: mockFetchNextPage,
            refetch: mockRefetch,
        });
    });

    const renderComponent = () => {
        const store = configureStore({
            reducer: {
                auth: () => ({ isAuthenticated: true }),
            },
        });

        return render(
            <Provider store={store}>
                <BrowserRouter>
                    <TherapistListPage />
                </BrowserRouter>
            </Provider>
        );
    };

    it("renders the therapist list table", () => {
        renderComponent();
        expect(screen.getByText("Therapist List")).toBeInTheDocument();
        expect(screen.getByText("John Doe")).toBeInTheDocument();
        expect(screen.getByText("Jane Smith")).toBeInTheDocument();
    });
	vi.mock("react-intersection-observer", () => ({
		useInView: () => ({
			ref: vi.fn(),
			inView: true,
		}),
	}));

	describe("TherapistListPage", () => {
        const mockFetchRejectionReasons = vi.fn();
        const mockResetPassword = vi.fn();
        const mockAcceptTherapist = vi.fn();
        const mockRejectTherapist = vi.fn();

        beforeEach(() => {
            vi.spyOn(TherapistRepository.prototype, "getAll").mockResolvedValue({
            data: [],
            meta: { total: 0 },
            });

            vi.spyOn(TherapistRepository.prototype, "getProfileRejectionReasons").mockImplementation(mockFetchRejectionReasons);
            vi.spyOn(UserRepository.prototype, "resetPassword").mockImplementation(mockResetPassword);
            vi.spyOn(TherapistRepository.prototype, "accept").mockImplementation(mockAcceptTherapist);
            vi.spyOn(TherapistRepository.prototype, "reject").mockImplementation(mockRejectTherapist);
        });

        it("displays 'No more data' message when no additional pages are available", () => {
			renderComponent();
			expect(screen.getByText("No more data")).toBeInTheDocument();
		});

        // it("renders rejection reasons popup when rejection reasons are fetched", async () => {
        //     const mockFetchRejectionReasons = vi.fn().mockResolvedValue({
        //         data: {
        //             reasons: [
        //                 { rejectedAt: "2023-01-01T00:00:00Z", reason: "Incomplete documents" },
        //                 { rejectedAt: "2023-01-02T00:00:00Z", reason: "Invalid license" },
        //             ],
        //         },
        //     });
        
        //     vi.spyOn(TherapistRepository.prototype, "getProfileRejectionReasons").mockImplementation(mockFetchRejectionReasons);
        
        //     renderComponent();       
            
        //     const rejectReasonButtons = screen.getAllByTestId("rejected-reasons-button");        
           
        //     fireEvent.click(rejectReasonButtons[0]);  
           
        //     await waitFor(() => {
        //         expect(screen.getByText("Rejection Reasons")).toBeInTheDocument();
        //         expect(screen.getByText("Incomplete documents")).toBeInTheDocument();
        //         expect(screen.getByText("Invalid license")).toBeInTheDocument();
        //     });        
            
        //     expect(ActionButton).toHaveBeenCalledWith(
        //         expect.objectContaining({
        //             onRejectedReason: expect.any(Function),
        //         }),
        //         expect.anything()
        //     );
        // });

     

	});
   
   
});



describe("ActionButton Component", () => {
    it("renders all buttons when all props are provided", () => {
        const mockHandlers = {
            onEdit: vi.fn(),
            onDelete: vi.fn(),
            onDeactivate: vi.fn(),
            onActivate: vi.fn(),
            onViewProfile: vi.fn(),
            onResetPassword: vi.fn(),
            onAccept: vi.fn(),
            onReject: vi.fn(),
            onRejectedReason: vi.fn(),
        };

        render(<ActionButton {...mockHandlers} isResetPasswordDisabled={false} />);

        expect(screen.getByText("Delete")).toBeInTheDocument();
        expect(screen.getByText("Deactivate")).toBeInTheDocument();
        expect(screen.getByText("Activate")).toBeInTheDocument();
        expect(screen.getByText("View Profile")).toBeInTheDocument();
        expect(screen.getByText("Reset Password")).toBeInTheDocument();
        expect(screen.getByText("Rejected Reasons")).toBeInTheDocument();
    });

    it("calls the correct handler when a button is clicked", () => {
        const mockHandlers = {
            onEdit: vi.fn(),
            onDelete: vi.fn(),
            onDeactivate: vi.fn(),
            onActivate: vi.fn(),
            onViewProfile: vi.fn(),
            onResetPassword: vi.fn(),
            onAccept: vi.fn(),
            onReject: vi.fn(),
            onRejectedReason: vi.fn(),
        };

        render(<ActionButton {...mockHandlers} isResetPasswordDisabled={false} />);

        fireEvent.click(screen.getByText("Delete"));
        expect(mockHandlers.onDelete).toHaveBeenCalled();

        fireEvent.click(screen.getByText("Deactivate"));
        expect(mockHandlers.onDeactivate).toHaveBeenCalled();

        fireEvent.click(screen.getByText("Activate"));
        expect(mockHandlers.onActivate).toHaveBeenCalled();

        fireEvent.click(screen.getByText("View Profile"));
        expect(mockHandlers.onViewProfile).toHaveBeenCalled();

        fireEvent.click(screen.getByText("Reset Password"));
        expect(mockHandlers.onResetPassword).toHaveBeenCalled();

        fireEvent.click(screen.getByText("Rejected Reasons"));
        expect(mockHandlers.onRejectedReason).toHaveBeenCalled();
    });
   

    it("does not render buttons when handlers are not provided", () => {
        render(<ActionButton />);
        expect(screen.queryByText("Edit")).not.toBeInTheDocument();
        expect(screen.queryByText("Delete")).not.toBeInTheDocument();
        expect(screen.queryByText("Deactivate")).not.toBeInTheDocument();
        expect(screen.queryByText("Activate")).not.toBeInTheDocument();
        expect(screen.queryByText("View Profile")).not.toBeInTheDocument();
        expect(screen.queryByText("Reset Password")).not.toBeInTheDocument();
        expect(screen.queryByText("Accept")).not.toBeInTheDocument();
        expect(screen.queryByText("Reject")).not.toBeInTheDocument();
        expect(screen.queryByText("Rejected Reasons")).not.toBeInTheDocument();
    });

    it("renders only the buttons for the provided handlers", () => {
        const mockHandlers = {
            onEdit: vi.fn(),
            onDelete: vi.fn(),
        };

        render(<ActionButton {...mockHandlers} />);

        expect(screen.getByText("Delete")).toBeInTheDocument();
        expect(screen.queryByText("Deactivate")).not.toBeInTheDocument();
        expect(screen.queryByText("Activate")).not.toBeInTheDocument();
        expect(screen.queryByText("View Profile")).not.toBeInTheDocument();
        expect(screen.queryByText("Reset Password")).not.toBeInTheDocument();
        expect(screen.queryByText("Accept")).not.toBeInTheDocument();
        expect(screen.queryByText("Reject")).not.toBeInTheDocument();
        expect(screen.queryByText("Rejected Reasons")).not.toBeInTheDocument();
    });

    it("disables the Reset Password button when isResetPasswordDisabled is true", () => {
        render(<ActionButton onResetPassword={() => {}} isResetPasswordDisabled={true} />);
        const resetPasswordButton = screen.getByTestId("reset-password-button");
        expect(resetPasswordButton).toBeDisabled();
      });
    
      it("enables the Reset Password button when isResetPasswordDisabled is false", () => {
        render(<ActionButton onResetPassword={() => {}} isResetPasswordDisabled={false} />);
        const resetPasswordButton = screen.getByTestId("reset-password-button");
        expect(resetPasswordButton).not.toBeDisabled();
      });
    
      it("does not render the Reset Password button if onResetPassword is not provided", () => {
        render(<ActionButton />);
        const resetPasswordButton = screen.queryByTestId("reset-password-button");
        expect(resetPasswordButton).not.toBeInTheDocument();
      });
	

    
});
