import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { describe, it, vi, beforeEach, expect, afterEach } from 'vitest'
import TherapistProfilePage from '../../../src/pages/therapists/TherapistProfilePage'
import { MemoryRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import store from '../../../src/store'
import React from 'react'
import * as reduxHooks from 'react-redux'
import * as routerHooks from 'react-router-dom'

vi.mock('react-redux', async () => ({
    ...(await vi.importActual('react-redux')),
    useDispatch: vi.fn(),
    useSelector: vi.fn(),
}))
vi.mock('react-router-dom', async () => ({
    ...(await vi.importActual('react-router-dom')),
    useLocation: vi.fn(),
}))

const getOneMock = vi.fn().mockResolvedValue({
    registrationInfo: [
      { pageName: 'license', payloadInfo: 'License Info' },
      { pageName: 'specialization', payloadInfo: 'Specialization Info' },
    ],
    emailVerifiedAt: true,
  })

  vi.mock('../../../src/repositories/TherapistRepository', () => {
    return {
      default: class {
        getOne = getOneMock
      }
    }
  })
  

describe('TherapistProfilePage', () => {
    const mockDispatch = vi.fn()

    beforeEach(() => {
        vi.spyOn(reduxHooks, 'useDispatch').mockReturnValue(mockDispatch)
        vi.spyOn(routerHooks, 'useLocation').mockReturnValue({
            pathname: '/therapists/123/details',
            state: undefined,
            key: '',
            search: '',
            hash: '',
        })
    })

    afterEach(() => {
        vi.restoreAllMocks()
    })
    
    it('should render the component initially in loading state', () => {
        render(
            <Provider store={store}>
                <MemoryRouter>
                    <TherapistProfilePage />
                </MemoryRouter>
            </Provider>
        )
        expect(screen.getByTestId('loading-indicator')).toBeInTheDocument()
    })

})