import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import SortQuestionnaire from "../../../src/pages/questionnaires/SortQuestionnaire";
import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

import "@testing-library/jest-dom"

// Mock Draggable and Droppable since react-beautiful-dnd doesn’t work well in tests
vi.mock("react-beautiful-dnd", async () => {
  const actual = await vi.importActual("react-beautiful-dnd");
  return {
    ...actual,
    Droppable: ({ children }: any) => (
      <div data-testid="droppable">{children({ droppableProps: {}, innerRef: vi.fn(), placeholder: null })}</div>
    ),
    Draggable: ({ children, index }: any) => (
      <div data-testid={`draggable-${index}`}>
        {children({ draggableProps: {}, dragHandleProps: {}, innerRef: vi.fn() })}
      </div>
    ),
  };
});

const mockData = [
  {
    id: 1,
    required: true,
    question: "What is your name?",
    answers: [{ id: 101, label: "<PERSON>" }],
    type: "TEXT",
  },
  {
    id: 2,
    required: false,
    question: "What is your age?",
    answers: [],
    type: "NUMBER",
  },
];

const createWrapper = (repoMock: any, sort = "demographic") => {
  const queryClient = new QueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <SortQuestionnaire repo={repoMock} sort={sort} />
    </QueryClientProvider>
  );
};

describe("SortQuestionnaire", () => {
   it("renders data correctly", async () => {
    const repo = {
      getAll: vi.fn().mockResolvedValue({ result: mockData }),
      sortOrder: vi.fn(),
    };
    render(createWrapper(repo));

    expect(await screen.findByText("What is your name?")).toBeInTheDocument();
    expect(screen.getByText("What is your age?")).toBeInTheDocument();
    expect(screen.getByText("1 Answer(s)")).toBeInTheDocument();
    expect(screen.getByText("0 Answer(s)")).toBeInTheDocument();
  });

  it("shows loading state", async () => {
    const repo = {
      getAll: vi.fn().mockReturnValue(new Promise(() => {})),
      sortOrder: vi.fn(),
    };
    render(createWrapper(repo));
    expect(await screen.findByRole("status")).toBeInTheDocument();
  });

});
