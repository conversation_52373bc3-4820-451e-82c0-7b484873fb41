import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import QuestionnaireForm from "../../../src/pages/questionnaires/QuestionnaireForm";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { OverlayTriggerState } from "react-stately";
import { Questionnaire } from "../../../src/types/questionnaire.interface";
import "@testing-library/jest-dom"

// Mocks
vi.mock("react-hot-toast", () => ({
  toast: {
    error: vi.fn(),
  },
}));

vi.mock("uuid", () => ({
  v4: () => "uuid-mock-id",
}));

const mockState: OverlayTriggerState = {
  isOpen: true,
  close: vi.fn(),
  open: vi.fn(),
  toggle: vi.fn(),
};

const mockRepo = {
  create: vi.fn().mockResolvedValue(true),
  update: vi.fn().mockResolvedValue(true),
};

const baseProps = {
  state: mockState,
  repo: mockRepo,
  refetch: vi.fn(),
};

const existingQuestionnaire: Questionnaire = {
  id: 1,
  question: "Existing question?",
  type: "checkbox",
  required: true,
  patientSort: 1,
  therapistSort: 2,
  category: ["self", "therapist"],
  info: "Info text",
  answers: [
    { id: "a-1", answer: "Answer 1", info: "Info 1" },
    { id: "a-2", answer: "Answer 2", info: "Info 2" },
  ],
};

vi.mock('@/components/forms/CheckBoxComponent', () => ({
  default: ({ label, onChange }) => (
    <label>
      <input
        type="checkbox"
        onChange={(e) => onChange(e.target.checked)}
      />
      {label}
    </label>
  ),
}));

describe("QuestionnaireForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders with default values (create mode)", () => {
    render(<QuestionnaireForm {...baseProps} />);
    expect(screen.getByText("Questionnaire Form")).toBeInTheDocument();
    expect(screen.getByText("No answers added")).toBeInTheDocument();
  });

  it("renders with questionnaire values (edit mode)", () => {
    render(
      <QuestionnaireForm {...baseProps} questionnaire={existingQuestionnaire} />
    );
    expect(screen.getByDisplayValue("Existing question?")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Answer 1")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Answer 2")).toBeInTheDocument();
  });

it("adds and removes answers", () => {
  render(<QuestionnaireForm {...baseProps} />);

  fireEvent.click(screen.getByTestId("add-answer-button"));

  expect(screen.getByPlaceholderText("answer 1")).toBeInTheDocument();

  fireEvent.click(screen.getByTestId("remove-answer-button"));

  expect(screen.queryByPlaceholderText("answer 1")).not.toBeInTheDocument();
});

  it("handles checkbox category and required changes", () => {
    render(<QuestionnaireForm {...baseProps} />);

    const selfCheckbox = screen.getByLabelText("MySelf");
    fireEvent.click(selfCheckbox);
    expect((selfCheckbox as HTMLInputElement).checked).toBe(true);

    const requiredCheckbox = screen.getByLabelText("Is question required?");
    fireEvent.click(requiredCheckbox);
    expect((requiredCheckbox as HTMLInputElement).checked).toBe(true);
  });

  it("shows error toast if type is missing", async () => {
    const { toast } = await import("react-hot-toast");
    render(<QuestionnaireForm {...baseProps} />);
    fireEvent.click(screen.getByRole("button", { name: /save/i }));
    await waitFor(() =>
      expect(toast.error).toHaveBeenCalledWith(
        "Please select at least one category",
        expect.anything()
      )
    );
  });

it("shows error toast if category is missing", async () => {
  const { toast } = await import("react-hot-toast");

  render(<QuestionnaireForm {...baseProps} />);

  fireEvent.change(screen.getByRole("textbox", { name: /\*?\s*Question/i }), {
    target: { value: "New Question" },
  });
  fireEvent.change(screen.getByRole("combobox", { name: /\*?\s*Question Type/i }), {
    target: { value: "checkbox" },
  });

  fireEvent.click(screen.getByRole("button", { name: /save/i }));

  await waitFor(() =>
    expect(toast.error).toHaveBeenCalledWith(
      "Please select at least one category",
      expect.anything()
    )
  );
});

  it("submits and calls repo.create", async () => {
    render(<QuestionnaireForm {...baseProps} />);
    fireEvent.change(screen.getByRole("textbox", { name: /\*?\s*Question/i }), {
      target: { value: "New Question" },
    });
    fireEvent.change(screen.getByRole("combobox", { name: /\*?\s*Question Type/i }), {
      target: { value: "checkbox" },
    });
    fireEvent.click(screen.getByLabelText("MySelf"));
    fireEvent.click(screen.getByTestId("add-answer-button"));
    fireEvent.change(screen.getByPlaceholderText("answer 1"), {
      target: { value: "A1" },
    });

    fireEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockRepo.create).toHaveBeenCalled();
      expect(mockState.close).toHaveBeenCalled();
      expect(baseProps.refetch).toHaveBeenCalled();
    });
  });

  it("submits and calls repo.update", async () => {
    render(
      <QuestionnaireForm {...baseProps} questionnaire={existingQuestionnaire} />
    );
    fireEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(mockRepo.update).toHaveBeenCalledWith(1, expect.any(Object));
      expect(mockState.close).toHaveBeenCalled();
    });
  });

it("calls onCancel", () => {
  render(<QuestionnaireForm {...baseProps} />);
  fireEvent.click(screen.getByText("Cancel"));
  expect(mockState.close).toHaveBeenCalled();
});

});
