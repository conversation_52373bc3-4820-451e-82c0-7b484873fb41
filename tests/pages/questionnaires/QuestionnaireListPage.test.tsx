import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { beforeEach, describe, expect, it, vi } from "vitest";
import QuestionnaireListPage from "../../../src/pages/questionnaires/QuestionnaireListPage";
import * as ReactStately from "react-stately";
import { useInfiniteQuery } from "@tanstack/react-query";
import "@testing-library/jest-dom"

vi.mock("@tanstack/react-query", async () => {
  const actual = await vi.importActual<any>("@tanstack/react-query");
  return {
    ...actual,
    useInfiniteQuery: vi.fn(),
  };
});

// ✅ Mock IntersectionObserver
vi.mock("react-intersection-observer", async () => ({
  useInView: () => ({
    ref: vi.fn(),
    inView: true,
  }),
}));

// ✅ Mock react-stately overlay triggers
vi.mock("react-stately", async () => ({
  useOverlayTriggerState: vi.fn(() => ({
    isOpen: false,
    open: vi.fn(),
    close: vi.fn(),
  })),
}));

vi.mock("@/components/ActionButton", () => ({
  default: ({ onEdit, onDelete }: any) => (
    <div>
      <button onClick={onEdit}>Edit</button>
      <button onClick={onDelete}>Delete</button>
    </div>
  ),
}));


// ✅ Setup mock data for infiniteQuery
const mockData = {
  pages: [
    {
      data: [
        { id: 1, question: "What is your name?", required: true },
        { id: 2, question: "How old are you?", required: false },
      ],
      meta: {
        currentPage: 1,
        nextPage: 2,
        totalPages: 2,
      },
    },
  ],
};

// ✅ Mock Redux store with token
const renderWithRedux = (ui: React.ReactElement) => {
  const store = configureStore({
    reducer: () => ({
      auth: {
        token: "mock-token",
        refreshToken: "mock-refresh-token",
      },
    }),
  });

  return render(<Provider store={store}>{ui}</Provider>);
};

describe("QuestionnaireListPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    (useInfiniteQuery as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      data: mockData,
      error: null,
      isLoading: false,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });
  });

  it("renders heading and search input", () => {
    renderWithRedux(<QuestionnaireListPage />);
    expect(screen.getByText("Questionnaire List")).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Search...")).toBeInTheDocument();
  });

  it("renders questionnaire data", () => {
    renderWithRedux(<QuestionnaireListPage />);
    expect(screen.getByText("What is your name?")).toBeInTheDocument();
    expect(screen.getByText("How old are you?")).toBeInTheDocument();
  });

  it("triggers search debounce", async () => {
    renderWithRedux(<QuestionnaireListPage />);
    const input = screen.getByPlaceholderText("Search...");
    fireEvent.change(input, { target: { value: "age" } });

    await waitFor(() => {
      expect(input).toHaveValue("age");
    }, { timeout: 600 });
  });

  it("shows error state", () => {
    (useInfiniteQuery as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
      data: undefined,
      error: { message: "Failed" },
      isLoading: false,
      fetchNextPage: vi.fn(),
      refetch: vi.fn(),
    });

    renderWithRedux(<QuestionnaireListPage />);
    expect(screen.getByText(/Error/i)).toBeInTheDocument();
  });

  it("opens Add Question modal", () => {
    const openMock = vi.fn();
    (ReactStately.useOverlayTriggerState as any).mockReturnValueOnce({
      isOpen: false,
      open: openMock,
      close: vi.fn(),
    });

    renderWithRedux(<QuestionnaireListPage />);
    fireEvent.click(screen.getByText("Add Question"));
    expect(openMock).toHaveBeenCalled();
  });



// 2. Fix fetchNextPage by spying on PaginationAwareContainer
vi.mock("@/components/PaginationAwareContainer", async () => {
 
  return {
    __esModule: true,
    default: ({ children, onLoad }: any) => {
      onLoad(); // Trigger fetchNextPage
      return <div>{children}</div>;
    },
  };
});

it("fetches next page on intersection", () => {
  const fetchNextPage = vi.fn();

  (useInfiniteQuery as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
    data: mockData,
    error: null,
    isLoading: false,
    fetchNextPage,
    refetch: vi.fn(),
  });

  renderWithRedux(<QuestionnaireListPage />);
  expect(fetchNextPage).toHaveBeenCalled();
});

  it("fetches next page on intersection", async () => {
  const fetchNextPage = vi.fn();
  (useInfiniteQuery as unknown as ReturnType<typeof vi.fn>).mockReturnValueOnce({
    data: mockData,
    error: null,
    isLoading: false,
    fetchNextPage,
    refetch: vi.fn(),
  });

  renderWithRedux(<QuestionnaireListPage />);

  // Wait for it to be called due to inView=true + nextPage available
  await waitFor(() => {
    expect(fetchNextPage).toHaveBeenCalled();
  });
});

  it("clears questionnaire state on dialog close", async () => {
    let stateIsOpen = true;

    const stateMock = {
      isOpen: stateIsOpen,
      open: vi.fn(),
      close: vi.fn(),
    };

    const deleteStateMock = {
      isOpen: false,
      open: vi.fn(),
      close: vi.fn(),
    };

    (ReactStately.useOverlayTriggerState as any)
      .mockReturnValueOnce(stateMock)
      .mockReturnValueOnce(deleteStateMock);

    const { rerender } = renderWithRedux(<QuestionnaireListPage />);
    stateIsOpen = false;
    rerender(<Provider store={configureStore({ reducer: () => ({ auth: {} }) })}><QuestionnaireListPage /></Provider>);
  });
});
