import { render, screen, fireEvent } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import QuestionForm from "../../../src/pages/questionnaires/QuestionForm";
import { OverlayTriggerState } from "react-stately";
import "@testing-library/jest-dom"

vi.mock("@/repositories/QuestionnaireRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getAll: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      errorToast: vi.fn(),
    })),
  };
});

const mockClose = vi.fn();

const mockState: OverlayTriggerState = {
  isOpen: true,
  close: mockClose,
  open: vi.fn(),
  toggle: vi.fn(),
};

describe("QuestionForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

 it("renders empty form for new question", () => {
    render(<QuestionForm state={mockState} />);
    
    expect(screen.getByRole("textbox", { name: /\*?\s*Question/i })).toBeInTheDocument();
    expect(screen.getByRole("textbox", { name: /info/i })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /save/i })).toBeInTheDocument();
  });

  it("pre-fills form when questionnaire prop is provided", () => {
    render(
      <QuestionForm
        state={mockState}
        questionnaire={{ id: "1", question: "Existing?", info: "Details" }}
      />
    );
    expect(screen.getByDisplayValue("Existing?")).toBeInTheDocument();
    expect(screen.getByDisplayValue("Details")).toBeInTheDocument();
  });

  it("handles cancel button and resets form", () => {
    render(<QuestionForm state={mockState} />);
    fireEvent.click(screen.getByText("Cancel"));
    expect(mockClose).toHaveBeenCalled();
  });

});
