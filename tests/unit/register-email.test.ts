import { expect } from 'chai';
import sinon from 'sinon';
import fs from 'fs';
import Handlebars from 'handlebars';
import { 
  RegisterTherapistEmail, 
  RegisterPatientEmail, 
  RegisterMemberEmail,
  MfaVerificationEmail,
  FailedLoginAlertEmail
} from '../../src/application/emails/register.email';
import { User } from '../../src/models';

describe('Email Templates', function() {
  let sandbox: sinon.SinonSandbox;
  let readFileSyncStub: sinon.SinonStub;
  let handlebarsCompileStub: sinon.SinonStub;
  let templateStub: sinon.SinonStub;
  
  const mockUser = {
    id: '123',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    toJSON: () => ({
      id: '123',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      email: '<EMAIL>'
    })
  } as unknown as User;
  
  const mockHtmlContent = '<html><body>Template content</body></html>';
  const mockCompiledHtml = '<html><body>Compiled content</body></html>';
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Stub fs.readFileSync
    readFileSyncStub = sandbox.stub(fs, 'readFileSync').returns(mockHtmlContent);
    
    // Stub Handlebars.compile
    templateStub = sandbox.stub().returns(mockCompiledHtml);
    handlebarsCompileStub = sandbox.stub(Handlebars, 'compile').returns(templateStub);
    
    // Set environment variable
    process.env.INFO_EMAIL_ADDRESS = '<EMAIL>';
  });
  
  afterEach(function() {
    sandbox.restore();
    delete process.env.INFO_EMAIL_ADDRESS;
  });

  describe('RegisterTherapistEmail', function() {
    it('should compile the therapist email template with correct data', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      const result = RegisterTherapistEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledWith('src/templates/emails/register-therapist.html', 'utf8')).to.be.true;
      expect(handlebarsCompileStub.calledWith(mockHtmlContent)).to.be.true;
      expect(templateStub.calledWith(emailArgs)).to.be.true;
      
      expect(result).to.deep.equal({
        From: '<EMAIL>',
        To: '<EMAIL>',
        Subject: 'Welcome to Next Therapist',
        HtmlBody: mockCompiledHtml
      });
    });
    
    it('should handle optional url parameter', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456',
        url: 'https://example.com/verify'
      };
      
      // Act
      const result = RegisterTherapistEmail.compile(emailArgs);
      
      // Assert
      expect(templateStub.calledWith(sinon.match({ url: 'https://example.com/verify' }))).to.be.true;
      expect(result.HtmlBody).to.equal(mockCompiledHtml);
    });
  });

  describe('RegisterPatientEmail', function() {
    it('should compile the patient email template with correct data', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456',
        url: 'https://example.com/verify'
      };
      
      // Act
      const result = RegisterPatientEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledWith('src/templates/emails/register-patient.html', 'utf8')).to.be.true;
      expect(handlebarsCompileStub.calledWith(mockHtmlContent)).to.be.true;
      expect(templateStub.calledWith(emailArgs)).to.be.true;
      
      expect(result).to.deep.equal({
        From: '<EMAIL>',
        To: '<EMAIL>',
        Subject: 'Welcome to Next Therapist',
        HtmlBody: mockCompiledHtml
      });
    });
  });

  describe('RegisterMemberEmail', function() {
    it('should compile the member email template with correct data', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        url: 'https://example.com/verify'
      };
      
      // Act
      const result = RegisterMemberEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledWith('src/templates/emails/register-member.html', 'utf8')).to.be.true;
      expect(handlebarsCompileStub.calledWith(mockHtmlContent)).to.be.true;
      expect(templateStub.calledWith(emailArgs)).to.be.true;
      
      expect(result).to.deep.equal({
        From: '<EMAIL>',
        To: '<EMAIL>',
        Subject: 'Welcome to Next Therapist',
        HtmlBody: mockCompiledHtml
      });
    });
  });

  describe('MfaVerificationEmail', function() {
    it('should compile the MFA verification email template with correct data', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      const result = MfaVerificationEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledWith('src/templates/emails/mfa-login.html', 'utf8')).to.be.true;
      expect(handlebarsCompileStub.calledWith(mockHtmlContent)).to.be.true;
      expect(templateStub.calledWith(emailArgs)).to.be.true;
      
      expect(result).to.deep.equal({
        From: '<EMAIL>',
        To: '<EMAIL>',
        Subject: 'Welcome to Next Therapist',
        HtmlBody: mockCompiledHtml
      });
    });
  });

  describe('FailedLoginAlertEmail', function() {
    it('should compile the failed login alert email template with correct data', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser
      };
      
      // Act
      const result = FailedLoginAlertEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledWith('src/templates/emails/failed-login-attempt-alert.html', 'utf8')).to.be.true;
      expect(handlebarsCompileStub.calledWith(mockHtmlContent)).to.be.true;
      expect(templateStub.calledWith(emailArgs)).to.be.true;
      
      expect(result).to.deep.equal({
        From: '<EMAIL>',
        To: '<EMAIL>',
        Subject: 'Welcome to Next Therapist',
        HtmlBody: mockCompiledHtml
      });
    });
  });

  describe('Template helper functions', function() {
    it('getTherapistEmailTemplate should read the correct file and compile template', function() {
      // Reset stubs to verify call count
      readFileSyncStub.resetHistory();
      handlebarsCompileStub.resetHistory();
      templateStub.resetHistory();
      
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      RegisterTherapistEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledOnce).to.be.true;
      expect(handlebarsCompileStub.calledOnce).to.be.true;
      expect(templateStub.calledOnce).to.be.true;
    });
    
    it('getPatientEmailTemplate should read the correct file and compile template', function() {
      // Reset stubs to verify call count
      readFileSyncStub.resetHistory();
      handlebarsCompileStub.resetHistory();
      templateStub.resetHistory();
      
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456',
        url: 'https://example.com/verify'
      };
      
      // Act
      RegisterPatientEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledOnce).to.be.true;
      expect(handlebarsCompileStub.calledOnce).to.be.true;
      expect(templateStub.calledOnce).to.be.true;
    });
    
    it('getMemberEmailTemplate should read the correct file and compile template', function() {
      // Reset stubs to verify call count
      readFileSyncStub.resetHistory();
      handlebarsCompileStub.resetHistory();
      templateStub.resetHistory();
      
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        url: 'https://example.com/verify'
      };
      
      // Act
      RegisterMemberEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledOnce).to.be.true;
      expect(handlebarsCompileStub.calledOnce).to.be.true;
      expect(templateStub.calledOnce).to.be.true;
    });
    
    it('getMfaVerificationTemplate should read the correct file and compile template', function() {
      // Reset stubs to verify call count
      readFileSyncStub.resetHistory();
      handlebarsCompileStub.resetHistory();
      templateStub.resetHistory();
      
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      MfaVerificationEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledOnce).to.be.true;
      expect(handlebarsCompileStub.calledOnce).to.be.true;
      expect(templateStub.calledOnce).to.be.true;
    });
    
    it('getFailedLoginAttemptTemplate should read the correct file and compile template', function() {
      // Reset stubs to verify call count
      readFileSyncStub.resetHistory();
      handlebarsCompileStub.resetHistory();
      templateStub.resetHistory();
      
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser
      };
      
      // Act
      FailedLoginAlertEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledOnce).to.be.true;
      expect(handlebarsCompileStub.calledOnce).to.be.true;
      expect(templateStub.calledOnce).to.be.true;
    });
  });
  
  describe('Environment variables', function() {
    it('should use the INFO_EMAIL_ADDRESS environment variable as From address', function() {
      // Arrange
      const testEmail = '<EMAIL>';
      process.env.INFO_EMAIL_ADDRESS = testEmail;
      
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      const result = RegisterTherapistEmail.compile(emailArgs);
      
      // Assert
      expect(result.From).to.equal(testEmail);
    });
  });
});
