import { expect } from 'chai';
import * as sinon from 'sinon';

describe('Pagination Helper', function() {
  // Setup sandbox for mocking
  let sandbox: sinon.SinonSandbox;
  let mockGetPage: sinon.SinonStub;
  let mockGetPerPage: sinon.SinonStub;
  let paginated: any;
  let paginatedData: any;
  
  beforeEach(function() {
    // Create a sandbox for mocking
    sandbox = sinon.createSandbox();
    
    // Create stubs for the internal functions
    mockGetPage = sandbox.stub();
    mockGetPerPage = sandbox.stub();
    
    // Reset the module to avoid caching issues
    delete require.cache[require.resolve('../../src/application/helpers/pagination.helper')];
    
    // Mock the internal functions
    const originalModule = require('../../src/application/helpers/pagination.helper');
    paginated = originalModule.paginated;
    paginatedData = originalModule.paginatedData;
  });
  
  afterEach(function() {
    // Restore all mocks
    sandbox.restore();
  });

  describe('paginated', function() {
    it('should return correct pagination parameters with default values', function() {
      // Mock request object
      const mockReq: any = { query: {} };
      
      // Set up stub behavior
      mockGetPage.withArgs(mockReq).returns(1);
      mockGetPerPage.withArgs(mockReq).returns(10);
      
      const result = paginated(mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('limit', 10); // Default perPage is 10
      expect(result).to.have.property('offset', 0); // Page 1 with limit 10 gives offset 0
    });
    
    it('should return correct pagination parameters with custom page', function() {
      // Mock request object
      const mockReq: any = { query: { page: '3' } };
      
      const result = paginated(mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('limit');
      expect(result).to.have.property('offset');
    });
    
    it('should return correct pagination parameters with custom perPage', function() {
      // Mock request object
      const mockReq: any = { query: { perPage: '20' } };
      
      const result = paginated(mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('limit');
      expect(result).to.have.property('offset');
    });
    
    it('should return correct pagination parameters with custom page and perPage', function() {
      // Mock request object
      const mockReq: any = { query: { page: '3', perPage: '15' } };
      
      const result = paginated(mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('limit');
      expect(result).to.have.property('offset');
    });
    
    it('should use provided perPage parameter instead of request query', function() {
      // Mock request object
      const mockReq: any = { query: { page: '2', perPage: '20' } };
      
      // Override perPage with function parameter
      const result = paginated(mockReq, 25);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('limit');
      expect(result).to.have.property('offset');
    });
    
    it('should handle non-numeric page and perPage values', function() {
      // Mock request object
      const mockReq: any = { query: { page: 'abc', perPage: 'xyz' } };
      
      const result = paginated(mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('limit');
      expect(result).to.have.property('offset');
    });
  });
  
  describe('paginatedData', function() {
    it('should format data with count and rows for first page', function() {
      // Create mock data with count and rows
      const data = {
        count: 100,
        rows: [{ id: 1 }, { id: 2 }, { id: 3 }]
      };
      
      // Mock request object
      const mockReq: any = { query: { page: '1', perPage: '10' } };
      
      const result = paginatedData(data, mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('data');
      expect(result).to.have.property('meta');
      expect(result.meta).to.have.property('currentPage');
      expect(result.meta).to.have.property('perPage');
      expect(result.meta).to.have.property('total');
      expect(result.meta).to.have.property('lastPage');
      expect(result.meta).to.have.property('nextPage');
      expect(result.meta).to.have.property('prevPage');
    });
    
    it('should format data with count and rows for middle page', function() {
      // Create mock data with count and rows
      const data = {
        count: 100,
        rows: [{ id: 21 }, { id: 22 }, { id: 23 }]
      };
      
      // Mock request object
      const mockReq: any = { query: { page: '3', perPage: '10' } };
      
      const result = paginatedData(data, mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('data');
      expect(result).to.have.property('meta');
    });
    
    it('should format data with count and rows for last page', function() {
      // Create mock data with count and rows
      const data = {
        count: 100,
        rows: [{ id: 91 }, { id: 92 }, { id: 93 }]
      };
      
      // Mock request object
      const mockReq: any = { query: { page: '10', perPage: '10' } };
      
      const result = paginatedData(data, mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('data');
      expect(result).to.have.property('meta');
    });
    
    it('should handle array count format', function() {
      // Create mock data with count as an array with count property
      const data = {
        count: [{ count: 50 }],
        rows: [{ id: 1 }, { id: 2 }]
      };
      
      // Mock request object
      const mockReq: any = { query: { page: '1', perPage: '10' } };
      
      const result = paginatedData(data, mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('data');
      expect(result.meta).to.have.property('total');
    });
    
    it('should handle empty array count format', function() {
      // Create mock data with empty count array
      const data = {
        count: [],
        rows: []
      };
      
      // Mock request object
      const mockReq: any = { query: { page: '1', perPage: '10' } };
      
      const result = paginatedData(data, mockReq);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('data');
      expect(result.meta).to.have.property('total');
    });
    
    it('should handle non-integer division for lastPage calculation', function() {
      // Create mock data with count that doesn't divide evenly by perPage
      const data = {
        count: 95,
        rows: [{ id: 1 }, { id: 2 }]
      };
      
      // Mock request object
      const mockReq: any = { query: { page: '1', perPage: '10' } };
      
      const result = paginatedData(data, mockReq);
      
      expect(result).to.be.an('object');
      expect(result.meta).to.have.property('total');
      expect(result.meta).to.have.property('lastPage');
    });
  });
});
