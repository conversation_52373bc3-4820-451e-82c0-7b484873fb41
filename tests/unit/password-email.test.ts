import { expect } from 'chai';
import sinon from 'sinon';
import fs from 'fs';
import Handlebars from 'handlebars';
import { PasswordResetEmail } from '../../src/application/emails/password.email';
import { User } from '../../src/models';

describe('PasswordResetEmail', function() {
  let sandbox: sinon.SinonSandbox;
  let readFileSyncStub: sinon.SinonStub;
  let handlebarsCompileStub: sinon.SinonStub;
  let templateStub: sinon.SinonStub;
  
  const mockUser = {
    id: '123',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    toJSON: () => ({
      id: '123',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      email: '<EMAIL>'
    })
  } as unknown as User;
  
  const mockHtmlContent = '<html><body>Reset your password: <a href="{{url}}">Click here</a></body></html>';
  const mockCompiledHtml = '<html><body>Reset your password: <a href="https://example.com/reset">Click here</a></body></html>';
  const mockResetUrl = 'https://example.com/reset';
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Stub fs.readFileSync
    readFileSyncStub = sandbox.stub(fs, 'readFileSync').returns(mockHtmlContent);
    
    // Stub Handlebars.compile
    templateStub = sandbox.stub().returns(mockCompiledHtml);
    handlebarsCompileStub = sandbox.stub(Handlebars, 'compile').returns(templateStub);
    
    // Set environment variable
    process.env.INFO_EMAIL_ADDRESS = '<EMAIL>';
  });
  
  afterEach(function() {
    sandbox.restore();
    delete process.env.INFO_EMAIL_ADDRESS;
  });
  
  it('should compile the password reset email template with correct data', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: mockResetUrl
    };
    
    // Act
    const result = PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(readFileSyncStub.calledOnce).to.be.true;
    expect(readFileSyncStub.firstCall.args[0]).to.equal('src/templates/emails/reset-password.html');
    expect(readFileSyncStub.firstCall.args[1]).to.equal('utf8');
    
    expect(handlebarsCompileStub.calledOnce).to.be.true;
    expect(handlebarsCompileStub.firstCall.args[0]).to.equal(mockHtmlContent);
    
    expect(templateStub.calledOnce).to.be.true;
    expect(templateStub.firstCall.args[0]).to.deep.equal(emailArgs);
    
    expect(result).to.deep.equal({
      From: '<EMAIL>',
      To: '<EMAIL>',
      Subject: 'Password Reset Magic Link',
      HtmlBody: mockCompiledHtml
    });
  });
  
  it('should use the correct template file path for password reset', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: mockResetUrl
    };
    
    // Act
    PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(readFileSyncStub.calledWith('src/templates/emails/reset-password.html', 'utf8')).to.be.true;
  });
  
  it('should use the INFO_EMAIL_ADDRESS environment variable as From address for password reset', function() {
    // Arrange
    const testEmail = '<EMAIL>';
    process.env.INFO_EMAIL_ADDRESS = testEmail;
    
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: mockResetUrl
    };
    
    // Act
    const result = PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(result.From).to.equal(testEmail);
  });
  
  it('should use the provided email as To address for password reset', function() {
    // Arrange
    const recipientEmail = '<EMAIL>';
    
    const emailArgs = {
      email: recipientEmail,
      user: mockUser,
      url: mockResetUrl
    };
    
    // Act
    const result = PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(result.To).to.equal(recipientEmail);
  });
  
  it('should set the correct email subject for password reset', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: mockResetUrl
    };
    
    // Act
    const result = PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(result.Subject).to.equal('Password Reset Magic Link');
  });
  
  it('should pass the reset URL to the template', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: mockResetUrl
    };
    
    // Act
    PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(templateStub.calledWith(sinon.match({ url: mockResetUrl }))).to.be.true;
  });
  
  it('should handle different reset URLs correctly', function() {
    // Arrange
    const customResetUrl = 'https://custom-domain.com/reset-password?token=abc123';
    
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: customResetUrl
    };
    
    // Act
    PasswordResetEmail.compile(emailArgs);
    
    // Assert
    expect(templateStub.calledWith(sinon.match({ url: customResetUrl }))).to.be.true;
  });
});
