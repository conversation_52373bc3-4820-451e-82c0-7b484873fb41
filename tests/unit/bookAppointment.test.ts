import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import sinon from 'sinon';
import { HttpStatusCode } from 'axios';
import { expect } from 'chai';
import bookAppointmentController from '../../src/application/controllers/api/patients/bookAppointment.get';
import { 
  User, 
  Appointments, 
  UserRegistrationInfo, 
  TherapistProfile, 
  MinorPatient, 
  TherapistSubscription 
} from '../../src/models';
import { Op, Sequelize } from 'sequelize';
import logger from '../../src/configs/logger.config';
import jwt from 'jsonwebtoken';

const app = express();
app.use(express.json());

// Mock user data
const mockPatient = {
  id: '123',
  role: 'patient',
  version: 1,
  firstname: '<PERSON>',
  lastname: '<PERSON><PERSON>',
  email: '<EMAIL>'
} as any;

process.env.JWT_SECRET_KEY = 'test-secret-key';

app.use('/api/patients', bookAppointmentController);

describe('Book Appointment Controller', function () {
  let findOneStub: sinon.SinonStub;
  let findAllStub: sinon.SinonStub;
  let findByPkStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let sandbox: sinon.SinonSandbox;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    findOneStub = sandbox.stub(Appointments, 'findOne');
    findAllStub = sandbox.stub(Appointments, 'findAll');
    findByPkStub = sandbox.stub(User, 'findByPk');
    findByPkStub.resolves(mockPatient);
    
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    jwtVerifyStub = sandbox.stub(jwt, 'verify');
    jwtVerifyStub.returns({
      id: '123',
      type: 'api',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    } as any);

    // Mock middleware to set req.user
    app.use((req: Request, res: Response, next: NextFunction) => {
      req.user = mockPatient;
      next();
    });
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('GET /events/scheduled/:id', function () {

    it('should return transformed appointments when found', async function () {
      const mockAppointment = {
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled'
      };

      findOneStub.resolves(mockAppointment);
      
      const mockAppointments = [{
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled',
        isMinor: false,
        get: () => ({
          id: '456',
          patientId: '123',
          therapistId: '789',
          appointmentDate: '2025-05-21',
          startTime: '10:00',
          endTime: '11:00',
          status: 'scheduled',
          isMinor: false,
          profile: {
            payloadInfo: {
              specialties: ['Anxiety', 'Depression'],
              education: 'PhD Psychology'
            }
          },
          therapist: {
            firstname: 'Jane',
            lastname: 'Smith',
            dob: '1980-01-01',
            email: '<EMAIL>',
            userProfile: 'therapist',
            subscription: [{
              isActive: true
            }]
          },
          minorPatient: null
        })
      }];

      findAllStub.resolves(mockAppointments);
      
      const appointmentId = '456';
      
      const response = await request(app)
        .get(`/api/patients/events/scheduled/${appointmentId}`)
        .set('Authorization', 'Bearer valid-token')
        .expect(HttpStatusCode.Ok);
      
      expect(Array.isArray(response.body)).to.be.true;
      expect(response.body.length).to.equal(1);
      expect(response.body[0]).to.have.property('id', '456');
      expect(response.body[0]).to.have.property('patientId', '123');
      expect(response.body[0]).to.have.property('therapist');
      expect(response.body[0].therapist).to.have.property('firstName', 'Jane');
      expect(response.body[0].therapist).to.have.property('lastName', 'Smith');
      expect(response.body[0].therapist).to.have.property('isSubscriptionActive', true);
    });

    it('should handle appointments with minor patients', async function () {
      const mockAppointment = {
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled',
        isMinor: true
      };

      findOneStub.resolves(mockAppointment);
      
      const mockAppointments = [{
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled',
        isMinor: true,
        get: () => ({
          id: '456',
          patientId: '123',
          therapistId: '789',
          appointmentDate: '2025-05-21',
          startTime: '10:00',
          endTime: '11:00',
          status: 'scheduled',
          isMinor: true,
          profile: {
            payloadInfo: {
              specialties: ['Child Psychology'],
              education: 'PhD Child Psychology'
            }
          },
          therapist: {
            firstname: 'Jane',
            lastname: 'Smith',
            dob: '1980-01-01',
            email: '<EMAIL>',
            userProfile: 'therapist',
            subscription: {
              isActive: true
            }
          },
          minorPatient: {
            firstName: 'Jimmy',
            lastName: 'Doe'
          }
        })
      }];

      findAllStub.resolves(mockAppointments);
      
      const appointmentId = '456';
      
      const response = await request(app)
        .get(`/api/patients/events/scheduled/${appointmentId}`)
        .set('Authorization', 'Bearer valid-token')
        .expect(HttpStatusCode.Ok);
      
      expect(Array.isArray(response.body)).to.be.true;
      expect(response.body.length).to.equal(1);
      expect(response.body[0]).to.have.property('isMinor', true);
      expect(response.body[0]).to.have.property('minorPatient');
      expect(response.body[0].minorPatient).to.have.property('firstName', 'Jimmy');
      expect(response.body[0].minorPatient).to.have.property('lastName', 'Doe');
    });

    it('should handle empty appointments array', async function () {
      const mockAppointment = {
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled'
      };

      findOneStub.resolves(mockAppointment);
      findAllStub.resolves([]);
      
      const appointmentId = '456';
      
      const response = await request(app)
        .get(`/api/patients/events/scheduled/${appointmentId}`)
        .set('Authorization', 'Bearer valid-token')
        .expect(HttpStatusCode.Ok);
      
      expect(Array.isArray(response.body)).to.be.true;
      expect(response.body.length).to.equal(0);
    });

    it('should handle database errors gracefully', async function () {
      findOneStub.throws(new Error('Database connection error'));
      
      const appointmentId = '456';
      
      const response = await request(app)
        .get(`/api/patients/events/scheduled/${appointmentId}`)
        .set('Authorization', 'Bearer valid-token');
      
      expect(response.status).to.be.greaterThan(399);
      // Error is caught by the wrap function, which logs it
      // No direct assertion on loggerErrorStub as it depends on implementation
    });

    it('should handle case where findAll throws an error', async function () {
      const mockAppointment = {
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled'
      };

      findOneStub.resolves(mockAppointment);
      findAllStub.throws(new Error('Database query error'));
      
      const appointmentId = '456';
      
      const response = await request(app)
        .get(`/api/patients/events/scheduled/${appointmentId}`)
        .set('Authorization', 'Bearer valid-token');
      
      expect(response.status).to.be.greaterThan(399);
      // Error is caught by the wrap function, which logs it
      // No direct assertion on loggerErrorStub as it depends on implementation
    });

    it('should handle case where appointment transformation throws an error', async function () {
      const mockAppointment = {
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled'
      };

      findOneStub.resolves(mockAppointment);
      
      const mockAppointments = [{
        id: '456',
        patientId: '123',
        therapistId: '789',
        appointmentDate: '2025-05-21',
        startTime: '10:00',
        endTime: '11:00',
        status: 'scheduled',
        get: () => {
          throw new Error('Transformation error');
        }
      }];

      findAllStub.resolves(mockAppointments);
      
      const appointmentId = '456';
      
      const response = await request(app)
        .get(`/api/patients/events/scheduled/${appointmentId}`)
        .set('Authorization', 'Bearer valid-token');
      
      expect(response.status).to.be.greaterThan(399);
      // Error is caught by the wrap function, which logs it
      // No direct assertion on loggerErrorStub as it depends on implementation
    });
  });
});
