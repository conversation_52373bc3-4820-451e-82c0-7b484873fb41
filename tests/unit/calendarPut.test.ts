import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import sinon from 'sinon';
import { HttpStatusCode } from 'axios';
import { expect } from 'chai';
import calendarPutController from '../../src/application/controllers/api/patients/calendar.put';
import { User } from '../../src/models';
import CalendarPatient from '../../src/models/calendar-patient.model';
import { INVALID_CREDENTIALS_MESSAGE } from '../../src/application/controllers/api/patients/constants';
import jwt from 'jsonwebtoken';
import logger from '../../src/configs/logger.config';

const app = express();
app.use(express.json());

// Mock user data
const mockPatient = {
  id: '123',
  role: 'patient',
  version: 1,
  firstname: 'John',
  lastname: 'Doe',
  email: '<EMAIL>'
} as any;

process.env.JWT_SECRET_KEY = 'test-secret-key';

app.use('/api/patients', calendarPutController);

describe('Calendar Put Controller', function () {
  let findOneStub: sinon.SinonStub;
  let createStub: sinon.SinonStub;
  let updateStub: sinon.SinonStub;
  let findByPkStub: sinon.SinonStub;
  let jwtVerifyStub: sinon.SinonStub;
  let loggerInfoStub: sinon.SinonStub;
  let loggerErrorStub: sinon.SinonStub;
  let sandbox: sinon.SinonSandbox;

  beforeEach(function() {
    sandbox = sinon.createSandbox();
    findOneStub = sandbox.stub(CalendarPatient, 'findOne');
    createStub = sandbox.stub(CalendarPatient, 'create');
    updateStub = sandbox.stub(CalendarPatient.prototype, 'update');
    findByPkStub = sandbox.stub(User, 'findByPk');
    findByPkStub.resolves(mockPatient);
    
    loggerInfoStub = sandbox.stub(logger, 'info');
    loggerErrorStub = sandbox.stub(logger, 'error');
    
    jwtVerifyStub = sandbox.stub(jwt, 'verify');
    jwtVerifyStub.returns({
      id: '123',
      type: 'api',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    } as any);

    // Mock middleware to set req.user
    app.use((req: Request, res: Response, next: NextFunction) => {
      req.user = mockPatient;
      next();
    });
  });

  afterEach(function() {
    sandbox.restore();
  });

  describe('PUT /calendar-patient', function () {
    it('should return 400 when calendar settings are not boolean values', async function () {
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: 'true', // String instead of boolean
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        })
        .expect(HttpStatusCode.BadRequest);
      
      expect(response.body).to.have.property('INVALID_CREDENTIALS_MESSAGE');
    });

    it('should return 400 when any calendar setting is missing', async function () {
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          // isMicrosoftCalendar is missing
          isAppleCalendar: false,
          appointmentId: 456
        })
        .expect(HttpStatusCode.BadRequest);
      
      expect(response.body).to.have.property('INVALID_CREDENTIALS_MESSAGE');
    });

    it('should update existing record and return 200 when record exists', async function () {
      const existingRecord = {
        id: 1,
        userId: '123',
        appointmentId: 456,
        isGoogleCalendar: false,
        isMicrosoftCalendar: false,
        isAppleCalendar: false,
        update: updateStub
      };
      
      findOneStub.resolves(existingRecord);
      updateStub.resolves(existingRecord);
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        })
        .expect(HttpStatusCode.Ok);
      
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.have.property('id');
      expect(updateStub.calledOnce).to.be.true;
      expect(updateStub.firstCall.args[0]).to.deep.equal({
        isGoogleCalendar: true,
        isMicrosoftCalendar: false,
        isAppleCalendar: false
      });
    });

    it('should create new record and return 201 when record does not exist', async function () {
      findOneStub.resolves(null);
      
      const newRecord = {
        id: 1,
        userId: '123',
        appointmentId: 456,
        isGoogleCalendar: true,
        isMicrosoftCalendar: false,
        isAppleCalendar: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      createStub.resolves(newRecord);
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        })
        .expect(HttpStatusCode.Created);
      
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.have.property('id');
      expect(createStub.calledOnce).to.be.true;
      expect(createStub.firstCall.args[0]).to.deep.equal({
        userId: '123',
        isGoogleCalendar: true,
        isMicrosoftCalendar: false,
        isAppleCalendar: false,
        appointmentId: 456
      });
    });

    it('should handle database errors during findOne', async function () {
      findOneStub.throws(new Error('Database connection error'));
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        });
      
      expect(response.status).to.be.greaterThan(399);
    });

    it('should handle database errors during update', async function () {
      const existingRecord = {
        id: 1,
        userId: '123',
        appointmentId: 456,
        isGoogleCalendar: false,
        isMicrosoftCalendar: false,
        isAppleCalendar: false,
        update: updateStub
      };
      
      findOneStub.resolves(existingRecord);
      updateStub.throws(new Error('Database update error'));
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        });
      
      expect(response.status).to.be.greaterThan(399);
    });

    it('should handle database errors during create', async function () {
      findOneStub.resolves(null);
      createStub.throws(new Error('Database create error'));
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        });
      
      expect(response.status).to.be.greaterThan(399);
    });

    // it('should handle case where user is not authenticated', async function () {
    //   // Override middleware to simulate missing user
    //   app.use((req: Request, res: Response, next: NextFunction) => {
    //     req.user = null;
    //     next();
    //   });
      
    //   const response = await request(app)
    //     .put('/api/patients/calendar-patient')
    //     .set('Authorization', 'Bearer valid-token')
    //     .send({
    //       isGoogleCalendar: true,
    //       isMicrosoftCalendar: false,
    //       isAppleCalendar: false,
    //       appointmentId: 456
    //     });
      
    //   expect(response.status).to.be.greaterThan(399);
    // });

    it('should handle all calendar settings being true', async function () {
      findOneStub.resolves(null);
      
      const newRecord = {
        id: 1,
        userId: '123',
        appointmentId: 456,
        isGoogleCalendar: true,
        isMicrosoftCalendar: true,
        isAppleCalendar: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      createStub.resolves(newRecord);
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: true,
          isMicrosoftCalendar: true,
          isAppleCalendar: true,
          appointmentId: 456
        })
        .expect(HttpStatusCode.Created);
      
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.have.property('id');
      expect(createStub.calledOnce).to.be.true;
    });

    it('should handle all calendar settings being false', async function () {
      findOneStub.resolves(null);
      
      const newRecord = {
        id: 1,
        userId: '123',
        appointmentId: 456,
        isGoogleCalendar: false,
        isMicrosoftCalendar: false,
        isAppleCalendar: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      createStub.resolves(newRecord);
      
      const response = await request(app)
        .put('/api/patients/calendar-patient')
        .set('Authorization', 'Bearer valid-token')
        .send({
          isGoogleCalendar: false,
          isMicrosoftCalendar: false,
          isAppleCalendar: false,
          appointmentId: 456
        })
        .expect(HttpStatusCode.Created);
      
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.have.property('id');
      expect(createStub.calledOnce).to.be.true;
    });
  });
});
