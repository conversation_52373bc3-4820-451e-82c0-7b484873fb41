import express, { Request, Response, NextFunction } from 'express';
import request from 'supertest';
import sinon from 'sinon';
import { HttpStatusCode } from 'axios';
import { expect } from 'chai';
import getController from '../../src/application/controllers/api/patients/calendar.get';
import { User as UserModel } from '../../src/models'
import CalendarPatient from '../../src/models/calendar-patient.model';
import { NO_CALENDAR_DATA } from '../../src/application/controllers/api/patients/constants';
import jwt from 'jsonwebtoken';

const app = express();
app.use(express.json());

// const jwtVerifyStub = sinon.stub(jwt, 'verify');
// jwtVerifyStub.returns({
//     id: '123',
//     type: 'api',
//     iat: Math.floor(Date.now() / 1000),
//     exp: Math.floor(Date.now() / 1000) + 3600
// } as any);

const mockUser = {
    id: '123',
    role: 'patient',
    version: 1,
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>'
} as UserModel;

// const findByPkStub = sinon.stub(UserModel, 'findByPk');
// findByPkStub.resolves(mockUser);

// process.env.JWT_SECRET_KEY = 'test-secret-key';

app.use('/api/v1', getController);

describe('Calendar Controller', function () {
    let findOneStub: sinon.SinonStub;
    let createStub: sinon.SinonStub;
    let updateStub: sinon.SinonStub;
    let findByPkStub: sinon.SinonStub;
    let jwtVerifyStub: sinon.SinonStub;

    beforeEach(function() {
        findOneStub = sinon.stub(CalendarPatient, 'findOne');
        createStub = sinon.stub(CalendarPatient, 'create');
        updateStub = sinon.stub(CalendarPatient.prototype, 'update');
        findByPkStub = sinon.stub(UserModel, 'findByPk');
        findByPkStub.resolves(mockUser);

        jwtVerifyStub = sinon.stub(jwt, 'verify');
        jwtVerifyStub.returns({
            id: '123',
            type: 'api',
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 3600
        } as any);
    });

    afterEach(function() {
        findOneStub.restore();
        createStub.restore();
        updateStub.restore();
        findByPkStub.restore();
        jwtVerifyStub.restore();
    });

    describe('GET /calendar-patient', function () {
        it('should return 200 with calendar data when found', async function () {
            const mockCalendarData = {
                id: 1,
                userId: '123',
                calendarSettings: {
                    workingHours: '9-5',
                    timezone: 'UTC'
                }
            };
    
            findOneStub.resolves(mockCalendarData);
    
            const appointmentId = '456';
    
            const response = await request(app)
                .get(`/api/v1/calendar-patient?appointmentId=${appointmentId}`)
                .set('Authorization', 'Bearer valid-token')
                .expect(HttpStatusCode.Ok);

            expect(response.body).to.deep.equal(mockCalendarData);
        });

        it('should return 404 when no calendar data is found', async function () {
            findOneStub.resolves(null);
        
            const appointmentId = '456';
        
            const response = await request(app)
                .get(`/api/v1/calendar-patient?appointmentId=${appointmentId}`)
                .set('Authorization', 'Bearer valid-token')
                .expect(HttpStatusCode.NotFound);
        
            expect(response.body).to.deep.equal({ NO_CALENDAR_DATA });
        });
        
        it('should return 401 when no token is provided', async function () {
            const response = await request(app)
                .get('/api/v1/calendar-patient')
                .expect(HttpStatusCode.Unauthorized);

            expect(response.body).to.have.property('message', 'No token provided');
        });

        it('should return 401 when invalid token is provided', async function () {
            jwtVerifyStub.throws(new Error('Invalid token'));

            const response = await request(app)
                .get('/api/v1/calendar-patient')
                .set('Authorization', 'Bearer invalid-token')
                .expect(HttpStatusCode.Unauthorized);

            expect(response.body).to.have.property('message', 'Invalid token');
        });

        it('should handle malformed authorization header', async function () {
            jwtVerifyStub.throws(new Error('Error'));

            const response = await request(app)
                .get('/api/v1/calendar-patient')
                .set('Authorization', 'malformed-token')
                .expect(HttpStatusCode.Unauthorized);

            expect(response.body).to.have.property('message', 'Invalid token');
        });

        it('should handle empty authorization header', async function () {
            const response = await request(app)
                .get('/api/v1/calendar-patient')
                .set('Authorization', '')
                .expect(HttpStatusCode.Unauthorized);

            expect(response.body).to.have.property('message', 'No token provided');
        });
    });
});