import { expect } from 'chai';
import { HttpStatusCode } from 'axios';
import {
  NotFoundError,
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  InternalServerError,
  UnprocessableEntityError,
  ConflictError,
  TooManyRequestsError,
  ServiceUnavailableError,
  GatewayTimeoutError,
  NotImplementedError,
  BadGatewayError,
  PaymentRequiredError,
  PreconditionFailedError,
  RequestEntityTooLargeError,
  RequestURITooLongError,
  UnsupportedMediaTypeError,
  NotAcceptableError
} from '../../src/application/handlers/errors';

describe('Error Classes', function() {
  describe('NotFoundError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Resource not found';
      const error = new NotFoundError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('NotFoundError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.NotFound);
    });
  });
  
  describe('BadRequestError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Bad request';
      const error = new BadRequestError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('BadRequestError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.BadRequest);
    });
  });
  
  describe('UnauthorizedError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Unauthorized';
      const error = new UnauthorizedError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('UnauthorizedError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.Unauthorized);
    });
  });
  
  describe('ForbiddenError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Forbidden';
      const error = new ForbiddenError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('ForbiddenError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.Forbidden);
      expect(error.data).to.be.undefined;
    });
    
    it('should store additional data when provided', function() {
      const message = 'Forbidden';
      const data = { reason: 'insufficient_permissions', requiredRole: 'admin' };
      const error = new ForbiddenError(message, data);
      
      expect(error.data).to.deep.equal(data);
    });
  });
  
  describe('InternalServerError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Internal server error';
      const error = new InternalServerError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('InternalServerError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.InternalServerError);
    });
  });
  
  describe('UnprocessableEntityError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Unprocessable entity';
      const error = new UnprocessableEntityError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('UnprocessableEntityError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.UnprocessableEntity);
    });
  });
  
  describe('ConflictError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Conflict';
      const error = new ConflictError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('ConflictError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.Conflict);
    });
  });
  
  describe('TooManyRequestsError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Too many requests';
      const error = new TooManyRequestsError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('TooManyRequestsError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.TooManyRequests);
    });
  });
  
  describe('ServiceUnavailableError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Service unavailable';
      const error = new ServiceUnavailableError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('ServiceUnavailableError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.ServiceUnavailable);
    });
  });
  
  describe('GatewayTimeoutError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Gateway timeout';
      const error = new GatewayTimeoutError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('GatewayTimeoutError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.GatewayTimeout);
    });
  });
  
  describe('NotImplementedError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Not implemented';
      const error = new NotImplementedError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('NotImplementedError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.NotImplemented);
    });
  });
  
  describe('BadGatewayError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Bad gateway';
      const error = new BadGatewayError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('BadGatewayError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.BadGateway);
    });
  });
  
  describe('PaymentRequiredError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Payment required';
      const error = new PaymentRequiredError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('PaymentRequiredError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.PaymentRequired);
    });
  });
  
  describe('PreconditionFailedError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Precondition failed';
      const error = new PreconditionFailedError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('PreconditionFailedError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.PreconditionFailed);
    });
  });
  
  describe('RequestEntityTooLargeError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Request entity too large';
      const error = new RequestEntityTooLargeError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('RequestEntityTooLargeError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(413);
    });
  });
  
  describe('RequestURITooLongError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Request URI too long';
      const error = new RequestURITooLongError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('RequestURITooLongError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(414);
    });
  });
  
  describe('UnsupportedMediaTypeError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Unsupported media type';
      const error = new UnsupportedMediaTypeError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('UnsupportedMediaTypeError');
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.UnsupportedMediaType);
    });
  });
  
  describe('NotAcceptableError', function() {
    it('should create an error with correct properties', function() {
      const message = 'Not acceptable';
      const error = new NotAcceptableError(message);
      
      expect(error).to.be.instanceOf(Error);
      expect(error.name).to.equal('UnAcceptedError'); // Note: The name is different from the class name
      expect(error.message).to.equal(message);
      expect(error.statusCode).to.equal(HttpStatusCode.NotAcceptable);
    });
  });
});
