import { expect } from 'chai';
import sinon from 'sinon';
import fs from 'fs';
import Handlebars from 'handlebars';
import { VerificationEmail } from '../../src/application/emails/verify.email';
import { User } from '../../src/models';

describe('Verification Email Template', function() {
  let sandbox: sinon.SinonSandbox;
  let readFileSyncStub: sinon.SinonStub;
  
  const mockUser = {
    id: '123',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    toJSON: () => ({
      id: '123',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      email: '<EMAIL>'
    })
  } as unknown as User;
  
  const mockHtmlContent = '<html><body>Hello {{user.firstname}}, your verification code is {{code}}{{#if url}} or click <a href="{{url}}">here</a>{{/if}}</body></html>';
  const mockCompiledHtml = '<html><body>Hello John, your verification code is 123456</body></html>';
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Stub fs.readFileSync to return mock HTML content
    readFileSyncStub = sandbox.stub(fs, 'readFileSync').returns(mockHtmlContent);
    
    // Create a template stub that returns mock compiled HTML
    const templateStub = sandbox.stub().returns(mockCompiledHtml);
    sandbox.stub(Handlebars, 'compile').returns(templateStub);
    
    // Set environment variable
    process.env.INFO_EMAIL_ADDRESS = '<EMAIL>';
  });
  
  afterEach(function() {
    sandbox.restore();
    delete process.env.INFO_EMAIL_ADDRESS;
  });

  describe('VerificationEmail', function() {
    it('should compile the verification email template with correct data', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      const result = VerificationEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledWith('src/templates/emails/email-verification.html', 'utf-8')).to.be.true;
      expect(result).to.deep.equal({
        From: '<EMAIL>',
        To: '<EMAIL>',
        Subject: 'Email Verification Magic Link',
        HtmlBody: mockCompiledHtml
      });
    });
    
    it('should handle optional url parameter', function() {
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456',
        url: 'https://example.com/verify'
      };
      
      // Act
      const result = VerificationEmail.compile(emailArgs);
      
      // Assert
      expect(result).to.have.property('HtmlBody', mockCompiledHtml);
    });
  });

  describe('Environment variables', function() {
    it('should use the INFO_EMAIL_ADDRESS environment variable as From address', function() {
      // Arrange
      const testEmail = '<EMAIL>';
      process.env.INFO_EMAIL_ADDRESS = testEmail;
      
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      const result = VerificationEmail.compile(emailArgs);
      
      // Assert
      expect(result.From).to.equal(testEmail);
    });
    
    it('should handle missing INFO_EMAIL_ADDRESS environment variable', function() {
      // Arrange
      delete process.env.INFO_EMAIL_ADDRESS;
      
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      const result = VerificationEmail.compile(emailArgs);
      
      // Assert
      // Just verify it doesn't throw an error and returns something
      expect(result).to.have.property('To', '<EMAIL>');
      expect(result).to.have.property('Subject', 'Email Verification Magic Link');
      expect(result).to.have.property('HtmlBody', mockCompiledHtml);
    });
  });
  
  describe('Template helper function', function() {
    it('getVerificationEmailTemplate should read the correct file and compile template', function() {
      // Reset stubs to verify call count
      readFileSyncStub.resetHistory();
      
      // Arrange
      const emailArgs = {
        email: '<EMAIL>',
        user: mockUser,
        code: '123456'
      };
      
      // Act
      VerificationEmail.compile(emailArgs);
      
      // Assert
      expect(readFileSyncStub.calledOnce).to.be.true;
      expect(readFileSyncStub.calledWith('src/templates/emails/email-verification.html', 'utf-8')).to.be.true;
    });
  });
});
