import { expect } from 'chai';
import sinon from 'sinon';
import fs from 'fs';
import Handlebars from 'handlebars';
import { AcceptPatientEmail } from '../../src/application/emails/accept-patient.email';
import { User } from '../../src/models';

describe('AcceptPatientEmail', function() {
  let sandbox: sinon.SinonSandbox;
  let readFileSyncStub: sinon.SinonStub;
  let handlebarsCompileStub: sinon.SinonStub;
  let templateStub: sinon.SinonStub;
  
  const mockUser = {
    id: '123',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    toJSON: () => ({
      id: '123',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      email: '<EMAIL>'
    })
  } as unknown as User;
  
  const mockHtmlContent = '<html><body>Hello {{user.firstname}}</body></html>';
  const mockCompiledHtml = '<html><body>Hello John</body></html>';
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Stub fs.readFileSync
    readFileSyncStub = sandbox.stub(fs, 'readFileSync').returns(mockHtmlContent);
    
    // Stub Handlebars.compile
    templateStub = sandbox.stub().returns(mockCompiledHtml);
    handlebarsCompileStub = sandbox.stub(Handlebars, 'compile').returns(templateStub);
    
    // Set environment variable
    process.env.INFO_EMAIL_ADDRESS = '<EMAIL>';
  });
  
  afterEach(function() {
    sandbox.restore();
    delete process.env.INFO_EMAIL_ADDRESS;
  });
  
  it('should compile the email template with correct data', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: 'https://example.com/verify'
    };
    
    // Act
    const result = AcceptPatientEmail.compile(emailArgs);
    
    // Assert
    expect(readFileSyncStub.calledOnce).to.be.true;
    expect(readFileSyncStub.firstCall.args[0]).to.equal('src/templates/emails/accept-patient.html');
    expect(readFileSyncStub.firstCall.args[1]).to.equal('utf8');
    
    expect(handlebarsCompileStub.calledOnce).to.be.true;
    expect(handlebarsCompileStub.firstCall.args[0]).to.equal(mockHtmlContent);
    
    expect(templateStub.calledOnce).to.be.true;
    expect(templateStub.firstCall.args[0]).to.deep.equal(emailArgs);
    
    expect(result).to.deep.equal({
      From: '<EMAIL>',
      To: '<EMAIL>',
      Subject: 'Account Verified Successfully',
      HtmlBody: mockCompiledHtml
    });
  });
  
  it('should use the correct template file path', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: 'https://example.com/verify'
    };
    
    // Act
    AcceptPatientEmail.compile(emailArgs);
    
    // Assert
    expect(readFileSyncStub.calledWith('src/templates/emails/accept-patient.html', 'utf8')).to.be.true;
  });
  
  it('should use the INFO_EMAIL_ADDRESS environment variable as From address', function() {
    // Arrange
    const testEmail = '<EMAIL>';
    process.env.INFO_EMAIL_ADDRESS = testEmail;
    
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      url: 'https://example.com/verify'
    };
    
    // Act
    const result = AcceptPatientEmail.compile(emailArgs);
    
    // Assert
    expect(result.From).to.equal(testEmail);
  });
  
  it('should use the provided email as To address', function() {
    // Arrange
    const recipientEmail = '<EMAIL>';
    
    const emailArgs = {
      email: recipientEmail,
      user: mockUser,
      url: 'https://example.com/verify'
    };
    
    // Act
    const result = AcceptPatientEmail.compile(emailArgs);
    
    // Assert
    expect(result.To).to.equal(recipientEmail);
  });
});
