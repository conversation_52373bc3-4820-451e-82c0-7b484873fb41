import { expect } from 'chai';
import * as sinon from 'sinon';
import crypto from 'crypto';

// We need to mock the environment and crypto before importing the module
const originalEnv = { ...process.env };
process.env.ENCRYPTION_KEY = 'e41c966f21f9e1577802463f8924e5ffef6b6c4965fbd14cd96a3c778f482593';

// Mock crypto.randomBytes to return consistent values for testing
const randomBytesStub = sinon.stub(crypto, 'randomBytes');
randomBytesStub.callsFake((size) => {
  return Buffer.from('a'.repeat(size));
});

// Now import the module after setting up the mocks
import { encrypt, decrypt, encryptedB64, decryptedB64, createUniqueCharGenerator } from '../../src/application/helpers/encryption.helper';

describe('Encryption Helper', function() {
  // Setup sandbox for mocking
  let sandbox: sinon.SinonSandbox;
  
  beforeEach(function() {
    // Create a sandbox for mocking
    sandbox = sinon.createSandbox();
  });
  
  afterEach(function() {
    // Restore all mocks
    sandbox.restore();
  });
  
  after(function() {
    // Restore original environment after all tests
    process.env = originalEnv;
    randomBytesStub.restore();
  });

  describe('encrypt', function() {
    it('should encrypt a string and return object with iv and encryptedData', function() {
      const text = 'Hello World';
      const result = encrypt(text);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('iv').that.is.a('string');
      expect(result).to.have.property('encryptedData').that.is.a('string');
      expect(result.iv).to.have.lengthOf(32); // 16 bytes in hex = 32 chars
      expect(result.encryptedData).to.be.a('string').that.is.not.empty;
    });
    
    it('should handle empty strings', function() {
      const text = '';
      const result = encrypt(text);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('iv').that.is.a('string');
      expect(result).to.have.property('encryptedData').that.is.a('string');
    });
    
    it('should handle special characters', function() {
      const text = '!@#$%^&*()_+{}:"<>?[];\',./=';
      const result = encrypt(text);
      
      expect(result).to.be.an('object');
      expect(result).to.have.property('iv').that.is.a('string');
      expect(result).to.have.property('encryptedData').that.is.a('string');
    });
    
    it('should throw error when text is undefined', function() {
      try {
        encrypt(undefined as any);
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
    
    it('should throw error when encryption key is not set', function() {
      // Save current key
      const savedKey = process.env.ENCRYPTION_KEY;
      // Remove encryption key
      delete process.env.ENCRYPTION_KEY;
      
      try {
        encrypt('test');
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      } finally {
        // Restore key
        process.env.ENCRYPTION_KEY = savedKey;
      }
    });
  });
  
  describe('decrypt', function() {
    it('should decrypt an encrypted string back to original text', function() {
      const originalText = 'Hello World';
      const encrypted = encrypt(originalText);
      const decrypted = decrypt(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should handle empty strings', function() {
      const originalText = '';
      const encrypted = encrypt(originalText);
      const decrypted = decrypt(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should handle special characters', function() {
      const originalText = '!@#$%^&*()_+{}:"<>?[];\',./=';
      const encrypted = encrypt(originalText);
      const decrypted = decrypt(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should handle long text', function() {
      const originalText = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.';
      const encrypted = encrypt(originalText);
      const decrypted = decrypt(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should throw error when text is undefined', function() {
      try {
        decrypt(undefined as any);
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
    
    it('should throw error when text is missing required properties', function() {
      try {
        decrypt({ invalidProperty: 'test' });
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
    
    it('should throw error when encryption key is not set', function() {
      const encrypted = encrypt('test');
      
      // Save current key
      const savedKey = process.env.ENCRYPTION_KEY;
      // Remove encryption key
      delete process.env.ENCRYPTION_KEY;
      
      try {
        decrypt(encrypted);
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      } finally {
        // Restore key
        process.env.ENCRYPTION_KEY = savedKey;
      }
    });
  });
  
  describe('encryptedB64', function() {
    it('should encrypt text and return a base64url string', function() {
      const text = 'Hello World';
      const result = encryptedB64(text);
      
      expect(result).to.be.a('string');
      // Base64url encoding uses alphanumeric characters plus '-' and '_'
      expect(result).to.match(/^[A-Za-z0-9_-]+$/);
    });
    
    it('should produce a string containing iv and encryptedData separated by colon when decoded', function() {
      const text = 'Test message';
      const result = encryptedB64(text);
      
      // Decode the base64url string
      const decoded = Buffer.from(result, 'base64url').toString('utf-8');
      
      expect(decoded).to.include(':'); // Should contain the separator
      const parts = decoded.split(':');
      expect(parts).to.have.lengthOf(2);
      expect(parts[0]).to.have.lengthOf(32); // IV in hex is 32 chars
      expect(parts[1]).to.be.a('string').that.is.not.empty; // Encrypted data
    });
    
    it('should handle empty strings', function() {
      const text = '';
      const result = encryptedB64(text);
      
      expect(result).to.be.a('string');
    });
    
    it('should throw error when text is undefined', function() {
      try {
        encryptedB64(undefined as any);
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
  });
  
  describe('decryptedB64', function() {
    it('should decrypt a base64url string back to original text', function() {
      const originalText = 'Hello World';
      const encrypted = encryptedB64(originalText);
      const decrypted = decryptedB64(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should handle empty strings', function() {
      const originalText = '';
      const encrypted = encryptedB64(originalText);
      const decrypted = decryptedB64(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should handle special characters', function() {
      const originalText = '!@#$%^&*()_+{}:"<>?[];\',./=';
      const encrypted = encryptedB64(originalText);
      const decrypted = decryptedB64(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should handle long text', function() {
      const originalText = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.';
      const encrypted = encryptedB64(originalText);
      const decrypted = decryptedB64(encrypted);
      
      expect(decrypted).to.equal(originalText);
    });
    
    it('should throw error when text is not a valid base64url string', function() {
      try {
        decryptedB64('invalid!base64');
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
    
    it('should throw error when decoded text does not contain separator', function() {
      // Create a base64url string without the separator
      const invalidB64 = Buffer.from('invalidformat').toString('base64url');
      
      try {
        decryptedB64(invalidB64);
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
    
    it('should throw error when text is undefined', function() {
      try {
        decryptedB64(undefined as any);
        // If we reach here, the test should fail
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
    
    it('should correctly handle the round trip encryption/decryption', function() {
      const testCases = [
        'Simple text',
        '123456789',
        'Text with spaces and numbers 123',
        JSON.stringify({ key: 'value', nested: { data: true } })
      ];
      
      testCases.forEach(text => {
        const encrypted = encryptedB64(text);
        const decrypted = decryptedB64(encrypted);
        expect(decrypted).to.equal(text);
      });
    });
  });
  
  describe('createUniqueCharGenerator', function() {
    it('should generate a string of the specified length', function() {
      const length = 10;
      const result = createUniqueCharGenerator(length);
      
      expect(result).to.be.a('string');
      expect(result).to.have.lengthOf(length);
    });
    
    it('should generate only alphanumeric characters', function() {
      const length = 20;
      const result = createUniqueCharGenerator(length);
      
      expect(result).to.match(/^[A-Za-z0-9]+$/);
    });
    
    it('should handle zero length', function() {
      const length = 0;
      const result = createUniqueCharGenerator(length);
      
      expect(result).to.be.a('string');
      expect(result).to.have.lengthOf(length);
      expect(result).to.equal('');
    });
    
    it('should handle negative length by returning empty string', function() {
      const length = -5;
      const result = createUniqueCharGenerator(length);
      
      expect(result).to.be.a('string');
      expect(result).to.equal('');
    });
  });
  
  describe('Integration tests with mocked dependencies', function() {
    it('should maintain data integrity through multiple encryption/decryption cycles', function() {
      const originalText = 'This is a test message for multiple cycles';
      
      // First cycle: normal encryption/decryption
      const encrypted1 = encrypt(originalText);
      const decrypted1 = decrypt(encrypted1);
      
      // Second cycle: encrypt the result of the first decryption
      const encrypted2 = encrypt(decrypted1);
      const decrypted2 = decrypt(encrypted2);
      
      // Third cycle: base64 encryption/decryption
      const encryptedB64Value = encryptedB64(decrypted2);
      const decryptedB64Value = decryptedB64(encryptedB64Value);
      
      expect(decrypted1).to.equal(originalText);
      expect(decrypted2).to.equal(originalText);
      expect(decryptedB64Value).to.equal(originalText);
    });
    
    it('should work with JSON data', function() {
      const jsonData = JSON.stringify({
        id: 123,
        name: 'Test User',
        email: '<EMAIL>'
      });
      
      // Test with standard encryption
      const encrypted = encrypt(jsonData);
      const decrypted = decrypt(encrypted);
      expect(decrypted).to.equal(jsonData);
      expect(JSON.parse(decrypted)).to.deep.equal(JSON.parse(jsonData));
      
      // Test with base64 encryption
      const encryptedB64Value = encryptedB64(jsonData);
      const decryptedB64Value = decryptedB64(encryptedB64Value);
      expect(decryptedB64Value).to.equal(jsonData);
      expect(JSON.parse(decryptedB64Value)).to.deep.equal(JSON.parse(jsonData));
    });
  });
  
  // Test coverage for edge cases
  describe('Edge cases and error handling', function() {
    it('should handle non-string inputs by converting them to strings', function() {
      const numberInput = 12345;
      const encrypted = encrypt(String(numberInput));
      const decrypted = decrypt(encrypted);
      
      expect(decrypted).to.equal(String(numberInput));
    });
    
    it('should handle objects by converting them to string representation', function() {
      const objInput = { test: 'value' };
      const encrypted = encrypt(String(objInput));
      const decrypted = decrypt(encrypted);
      
      expect(decrypted).to.equal(String(objInput));
    });
  });
});
