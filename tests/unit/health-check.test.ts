import express from 'express';
import request from 'supertest';
import { HttpStatusCode } from 'axios';
import defaultController from '../../src/application/controllers/default.controller';

const app = express();
app.use(express.json());

app.use('/', defaultController);

describe('APP Health Check', function () {

  describe('GET /', function () {
    it('should return success on request', async function () {
      await request(app).get(`/`).expect(HttpStatusCode.Ok);
    });

  });
});
