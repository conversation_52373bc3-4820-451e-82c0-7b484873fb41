import { expect } from 'chai';
import sinon from 'sinon';
import fs from 'fs';
import Handlebars from 'handlebars';
import { EmailResetEmail } from '../../src/application/emails/email-reset.email';
import { User } from '../../src/models';

describe('EmailResetEmail', function() {
  let sandbox: sinon.SinonSandbox;
  let readFileSyncStub: sinon.SinonStub;
  let handlebarsCompileStub: sinon.SinonStub;
  let templateStub: sinon.SinonStub;
  
  const mockUser = {
    id: '123',
    firstname: '<PERSON>',
    lastname: '<PERSON><PERSON>',
    email: '<EMAIL>',
    toJSON: () => ({
      id: '123',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      email: '<EMAIL>'
    })
  } as unknown as User;
  
  const mockHtmlContent = '<html><body>Your reset code is: {{code}}</body></html>';
  const mockCompiledHtml = '<html><body>Your reset code is: 123456</body></html>';
  const mockResetCode = '123456';
  
  beforeEach(function() {
    sandbox = sinon.createSandbox();
    
    // Stub fs.readFileSync
    readFileSyncStub = sandbox.stub(fs, 'readFileSync').returns(mockHtmlContent);
    
    // Stub Handlebars.compile
    templateStub = sandbox.stub().returns(mockCompiledHtml);
    handlebarsCompileStub = sandbox.stub(Handlebars, 'compile').returns(templateStub);
    
    // Set environment variable
    process.env.INFO_EMAIL_ADDRESS = '<EMAIL>';
  });
  
  afterEach(function() {
    sandbox.restore();
    delete process.env.INFO_EMAIL_ADDRESS;
  });
  
  it('should compile the email template with correct data', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      code: mockResetCode
    };
    
    // Act
    const result = EmailResetEmail.compile(emailArgs);
    
    // Assert
    expect(readFileSyncStub.calledOnce).to.be.true;
    expect(readFileSyncStub.firstCall.args[0]).to.equal('src/templates/emails/reset-email.html');
    expect(readFileSyncStub.firstCall.args[1]).to.equal('utf8');
    
    expect(handlebarsCompileStub.calledOnce).to.be.true;
    expect(handlebarsCompileStub.firstCall.args[0]).to.equal(mockHtmlContent);
    
    expect(templateStub.calledOnce).to.be.true;
    expect(templateStub.firstCall.args[0]).to.deep.equal(emailArgs);
    
    expect(result).to.deep.equal({
      From: '<EMAIL>',
      To: '<EMAIL>',
      Subject: 'Email Reset Code',
      HtmlBody: mockCompiledHtml
    });
  });
  
  it('should use the correct template file path', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      code: mockResetCode
    };
    
    // Act
    EmailResetEmail.compile(emailArgs);
    
    // Assert
    expect(readFileSyncStub.calledWith('src/templates/emails/reset-email.html', 'utf8')).to.be.true;
  });
  
  it('should use the INFO_EMAIL_ADDRESS environment variable as From address', function() {
    // Arrange
    const testEmail = '<EMAIL>';
    process.env.INFO_EMAIL_ADDRESS = testEmail;
    
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      code: mockResetCode
    };
    
    // Act
    const result = EmailResetEmail.compile(emailArgs);
    
    // Assert
    expect(result.From).to.equal(testEmail);
  });
  
  it('should use the provided email as To address', function() {
    // Arrange
    const recipientEmail = '<EMAIL>';
    
    const emailArgs = {
      email: recipientEmail,
      user: mockUser,
      code: mockResetCode
    };
    
    // Act
    const result = EmailResetEmail.compile(emailArgs);
    
    // Assert
    expect(result.To).to.equal(recipientEmail);
  });
  
  it('should set the correct email subject', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      code: mockResetCode
    };
    
    // Act
    const result = EmailResetEmail.compile(emailArgs);
    
    // Assert
    expect(result.Subject).to.equal('Email Reset Code');
  });
  
  it('should pass the reset code to the template', function() {
    // Arrange
    const emailArgs = {
      email: '<EMAIL>',
      user: mockUser,
      code: mockResetCode
    };
    
    // Act
    EmailResetEmail.compile(emailArgs);
    
    // Assert
    expect(templateStub.calledWith(sinon.match({ code: mockResetCode }))).to.be.true;
  });
});
