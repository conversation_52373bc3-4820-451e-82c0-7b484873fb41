import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, fireEvent, act } from "@testing-library/react";
import SessionTimeout from "../src/sessionTimeout";
import * as reduxHooks from "../src/store/hooks";
import { signOut } from "../src/store/slicers/auth.slicer";
import toast from "react-hot-toast";

// Mock toast and dismiss
vi.mock("react-hot-toast", () => {
  const dismiss = vi.fn();
  const toastFn = vi.fn().mockReturnValue("mock-toast-id");
  toastFn.dismiss = dismiss;
  return {
    __esModule: true,
    default: toastFn,
  };
});

// Mock Redux hooks
vi.mock("@/store/hooks", async () => {
  const actual = await vi.importActual<any>("@/store/hooks");
  return {
    ...actual,
    useAppDispatch: vi.fn(),
    useAppSelector: vi.fn(),
  };
});

describe("SessionTimeout", () => {
  const dispatchMock = vi.fn();
  const toastMock = toast as any;
  const dismissMock = toastMock.dismiss;

  let setTimeoutSpy: any;
  let clearTimeoutSpy: any;

  beforeEach(() => {
    vi.useFakeTimers();
    vi.clearAllMocks();

    setTimeoutSpy = vi.spyOn(global, "setTimeout");
    clearTimeoutSpy = vi.spyOn(global, "clearTimeout");

    (reduxHooks.useAppDispatch as vi.Mock).mockReturnValue(dispatchMock);
    (reduxHooks.useAppSelector as vi.Mock).mockImplementation((selector) =>
      selector({ auth: { user: { role: "therapist" } } })
    );
  });

  afterEach(() => {
    vi.useRealTimers();
    setTimeoutSpy.mockRestore();
    clearTimeoutSpy.mockRestore();
  });

  it("sets timers and listens to user events for therapist", () => {
    render(<SessionTimeout />);
    expect(setTimeoutSpy).toHaveBeenCalledTimes(2);

    fireEvent.mouseMove(window);
    fireEvent.keyDown(window);
    fireEvent.click(window);
  });

  it("shows warning and signs out after inactivity", () => {
    render(<SessionTimeout />);

    // Trigger warning toast
    act(() => {
      vi.advanceTimersByTime(14 * 60 * 1000);
    });
    expect(toastMock).toHaveBeenCalled();

    // Trigger signOut after full timeout
    act(() => {
      vi.advanceTimersByTime(1 * 60 * 1000);
    });
    expect(dispatchMock).toHaveBeenCalledWith(signOut());
  });

 it("cleans up on unmount", () => {
  const { unmount } = render(<SessionTimeout />);

  // Simulate warning toast being triggered (after 14 mins)
  act(() => {
    vi.advanceTimersByTime(14 * 60 * 1000); // 14 mins
  });

  expect(toastMock).toHaveBeenCalled(); // toast shown
  unmount();

  expect(clearTimeoutSpy).toHaveBeenCalledTimes(2);
  expect(dismissMock).toHaveBeenCalledWith("mock-toast-id");
});


  it("does nothing if user is not therapist", () => {
    (reduxHooks.useAppSelector as vi.Mock).mockImplementation((selector) =>
      selector({ auth: { user: { role: "admin" } } })
    );
    render(<SessionTimeout />);
    expect(setTimeoutSpy).not.toHaveBeenCalled();
  });
  
  it("dismisses existing warning toast on activity", () => {
  render(<SessionTimeout />);

  // Let the warning toast timeout trigger (simulate 14 minutes)
  act(() => {
    vi.advanceTimersByTime(14 * 60 * 1000);
  });

  expect(toastMock).toHaveBeenCalled(); // Warning toast shown

  // Simulate user activity (resets timer, should dismiss toast)
  fireEvent.mouseMove(window);

  expect(dismissMock).toHaveBeenCalledWith("mock-toast-id"); // ✅ This covers the block
});

});
