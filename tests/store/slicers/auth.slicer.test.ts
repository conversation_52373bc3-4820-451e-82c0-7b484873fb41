import reducer, {
  setUser, signIn, signOut, setTokens, resetRegistration, updateRegistrationForm,
  updateRegistrationState, resetRegistrationForm, setEditFlag, setFormContent,
  setNewRegistrationState, setPatientRegistrationPage, updatePatientAnswers,
  getPatientIntialPage, getPatientRegistrationPage, getPatientRegPageAndMatch,
  verifyEmail, registerPatient
} from "../../../src/store/slicers/auth.slicer";

import { request } from "../../../src/utils/request.utils";
import { vi, describe, it, expect, beforeEach } from "vitest";

// Mock request utils
vi.mock("@/utils/request.utils", () => ({
  request: {
    get: vi.fn(),
    post: vi.fn(),
  },
}));

describe("UserSlice Reducers", () => {
  const initialState = reducer(undefined, { type: "" });

  it("setUser", () => {
    const user = { id: 1, name: "Test" };
    const state = reducer(initialState, setUser(user));
    expect(state.user).toEqual(user);
  });

  it("signIn", () => {
    const payload = { token: "abc", refreshToken: "def", user: { id: 1 } };
    const state = reducer(initialState, signIn(payload));
    expect(state.token).toBe("abc");
    expect(state.isAuthenticated).toBe(true);
  });

  it("signOut", () => {
    const state = reducer(initialState, signOut());
    expect(state.user).toBeUndefined();
    expect(state.token).toBeUndefined();
    expect(state.isAuthenticated).toBe(false);
  });

  it("setTokens", () => {
    const state = reducer(initialState, setTokens({ accessToken: "tok", refreshToken: "ref" }));
    expect(state.token).toBe("tok");
    expect(state.refreshToken).toBe("ref");
  });

  it("resetRegistration", () => {
    const state = reducer(initialState, resetRegistration());
    expect(state.registration).toEqual(initialState.registration);
  });

  it("updateRegistrationForm", () => {
    const state = reducer(initialState, updateRegistrationForm({ pageId: "1", values: { answer: "yes" } }));
    expect(state.registration.formContent["1"]).toEqual({ answer: "yes" });
  });

  it("updateRegistrationState", () => {
    const state = reducer(initialState, updateRegistrationState({ key: "fetchingPage", value: true }));
    expect(state.registration.fetchingPage).toBe(true);
  });

  it("resetRegistrationForm", () => {
    const state = reducer(initialState, resetRegistrationForm({ pageId: "2" }));
    expect(state.registration.formContent["2"]).toEqual({});
  });

  it("setEditFlag", () => {
    const state = reducer(initialState, setEditFlag(true));
    expect(state.registration.editFlag).toBe(true);
  });

  it("setFormContent", () => {
    const formContent = { "1": { answer: "A" } };
    const state = reducer(initialState, setFormContent(formContent));
    expect(state.registration.formContent).toEqual(formContent);
  });

  it("setNewRegistrationState", () => {
    const state = reducer(initialState, setNewRegistrationState({
      newFormContent: { "1": { answer: "X" } },
      useToken: "abc"
    }));
    expect(state.registration.formContent["1"]).toEqual({ answer: "X" });
    expect(state.registration.userRegistrationToken).toBe("abc");
  });

  it("setPatientRegistrationPage", () => {
    const page = { id: "page1", content: "Q1" };
    const state = reducer(initialState, setPatientRegistrationPage(page));
    expect(state.registration.page).toEqual(page);
  });

  it("updatePatientAnswers", () => {
    const state = reducer(initialState, updatePatientAnswers({ pageId: "3", values: { selected: "yes" } }));
    expect(state.registration.patientAnswers["3"]).toEqual({ selected: "yes" });
  });
});

describe("UserSlice Thunks", () => {
  const dispatch = vi.fn();
  const getSpy = request.get as any;
  const postSpy = request.post as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("getPatientIntialPage - success", async () => {
    getSpy.mockResolvedValueOnce({ data: [{ id: "pg1", therapistMatchKey: "tmk" }] });
    await getPatientIntialPage({ category: "anxiety" })(dispatch);
    expect(getSpy).toHaveBeenCalled();
    expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: "auth/setPatientRegistrationPage" }));
  });

  it("getPatientRegistrationPage - success", async () => {
    getSpy.mockResolvedValueOnce({ data: { id: "pg2" } });
    const res = await getPatientRegistrationPage({
      pageId: "pg2", category: "stress"
    })(dispatch);
    expect(res).toEqual({ id: "pg2" });
    expect(getSpy).toHaveBeenCalled();
  });

  it("getPatientRegistrationPage - error", async () => {
    getSpy.mockRejectedValueOnce({ response: { error: "fail" } });
    const res = await getPatientRegistrationPage({ pageId: "x", category: "c" })(dispatch);
    expect(res).toEqual({ error: "fail" });
  });

  it("getPatientRegPageAndMatch - success with matcher", async () => {
    postSpy.mockResolvedValueOnce({ data: { id: "pg3", matchedTherapists: [1, 2] } });
    const res = await getPatientRegPageAndMatch({
      pageId: "pg3",
      category: "stress",
      matcherInvolvement: true,
      answers: [{ id: "a1", value: "yes" }],
      currentPageId: "pg2"
    })(dispatch);
    expect(res.id).toBe("pg3");
    expect(dispatch).toHaveBeenCalledWith(expect.objectContaining({ type: "auth/updateRegistrationState" }));
  });

  it("getPatientRegPageAndMatch - error", async () => {
    postSpy.mockRejectedValueOnce({ response: { message: "err" } });
    const res = await getPatientRegPageAndMatch({})(dispatch);
    expect(res).toEqual({ message: "err" });
  });

  it("verifyEmail - success", async () => {
    postSpy.mockResolvedValueOnce({ data: { verified: true } });
    const result = await verifyEmail({ email: "<EMAIL>" })();
    expect(postSpy).toHaveBeenCalled();
    expect(result).toEqual({ data: { verified: true } });
  });

  it("verifyEmail - error", async () => {
    postSpy.mockRejectedValueOnce({ response: { status: 400 } });
    const result = await verifyEmail({ email: "bad" })();
    expect(result).toEqual({ status: 400 });
  });

  it("registerPatient - success", async () => {
    postSpy.mockResolvedValueOnce({ data: { user: 123 } });
    const res = await registerPatient({
      category: "c", ansObject: { a: 1 }
    })();
    expect(postSpy).toHaveBeenCalledWith("/auth/register/patient?category=c", { a: 1 });
    expect(res).toEqual({ data: { user: 123 } });
  });

  it("registerPatient - error", async () => {
    postSpy.mockRejectedValueOnce({ response: { error: "bad" } });
    const res = await registerPatient({ category: "c", ansObject: {} })();
    expect(res).toEqual({ error: "bad" });
  });

  it("getPatientRegPageAndMatch - answers with gender", async () => {
  postSpy.mockResolvedValueOnce({ data: { id: "pg4" } });
  const payload = {
    pageId: "pg4",
    category: "general",
    matcherInvolvement: true,
    answers: { gender: "female" },
    currentPageId: "pg3"
  };
  const res = await getPatientRegPageAndMatch(payload)(dispatch);
  expect(res).toEqual({ id: "pg4" });
  expect(postSpy).toHaveBeenCalledWith(
    expect.stringContaining("/pages/register/pg4"),
    expect.objectContaining({
      answers: { gender: "female" }
    })
  );
});

it("getPatientRegPageAndMatch - answers with id and value", async () => {
  postSpy.mockResolvedValueOnce({ data: { id: "pg5" } });
  const payload = {
    pageId: "pg5",
    category: "sleep",
    matcherInvolvement: true,
    answers: { id: "q1", value: "sometimes" },
    currentPageId: "pg4"
  };
  const res = await getPatientRegPageAndMatch(payload)(dispatch);
  expect(res).toEqual({ id: "pg5" });
  expect(postSpy).toHaveBeenCalledWith(
    expect.stringContaining("/pages/register/pg5"),
    expect.objectContaining({
      answers: { id: "q1", slug: "sometimes" }
    })
  );
});


});
