import reducer, {
  setSelectedDate,
  setEvents,
  setDays,
} from "../../../src/store/slicers/calendar.slicer"; // adjust path as needed
import { describe, it, expect } from "vitest";
import { TherapyDate, TherapyEvent } from "../../../src/plugins/calendar/types";

describe("CalendarSlice Reducer", () => {
  const initialState = {
    selectedDate: expect.any(String),
    events: [],
    days: [],
  };

  it("should return initial state", () => {
    const state = reducer(undefined, { type: "" });
    expect(state.selectedDate).toBeDefined();
    expect(state.events).toEqual([]);
    expect(state.days).toEqual([]);
  });

  it("should handle setSelectedDate", () => {
    const newDate = "2025-06-25T00:00:00.000Z";
    const state = reducer(initialState, setSelectedDate(newDate));
    expect(state.selectedDate).toBe(newDate);
  });

  it("should handle setEvents", () => {
    const events: TherapyEvent[] = [
      { id: 1, title: "Therapy Session", date: "2025-06-26T10:00:00.000Z" },
    ];
    const state = reducer(initialState, setEvents(events));
    expect(state.events).toEqual(events);
  });

  it("should handle setDays", () => {
    const days: TherapyDate[] = [
      { date: "2025-06-25", isAvailable: true },
      { date: "2025-06-26", isAvailable: false },
    ];
    const state = reducer(initialState, setDays(days));
    expect(state.days).toEqual(days);
  });
});
