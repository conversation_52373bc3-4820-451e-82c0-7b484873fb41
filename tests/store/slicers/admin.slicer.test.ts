import {
  fetchSubscriptionPlansAction,
  createSubscriptionPlanAction,
  updateSubscriptionPlanAction,
  activateSubscriptionPlanAction,
  deleteSubscriptionPlanAction,
} from "../../../src/store/slicers/admin.slicer"; // adjust path if needed

import { vi, describe, it, expect, beforeEach } from "vitest";
import { request } from "../../../src/utils/request.utils";

// Mock the request module
vi.mock("@/utils/request.utils", () => ({
  request: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  },
}));

describe("Subscription Plan Actions", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("fetchSubscriptionPlansAction - success", async () => {
    (request.get as any).mockResolvedValueOnce({ data: [{ id: 1 }] });

    const action = fetchSubscriptionPlansAction({ status: "active" });
    const result = await action();

    expect(request.get).toHaveBeenCalledWith("/subscription-plan", { params: { status: "active" } });
    expect(result).toEqual([{ id: 1 }]);
  });

  it("fetchSubscriptionPlansAction - error", async () => {
    const error = { response: { status: 500 } };
    (request.get as any).mockRejectedValueOnce(error);

    const action = fetchSubscriptionPlansAction({});
    const result = await action();

    expect(result).toEqual({ status: 500 });
  });

  it("createSubscriptionPlanAction - success", async () => {
    const payload = { name: "Basic" };
    (request.post as any).mockResolvedValueOnce({ data: { id: 1, ...payload } });

    const action = createSubscriptionPlanAction(payload);
    const result = await action();

    expect(request.post).toHaveBeenCalledWith("/subscription-plan", payload);
   expect(result).toEqual({ data: { id: 1, name: "Basic" } });
  });

  it("createSubscriptionPlanAction - error", async () => {
    const error = { response: { message: "Validation failed" } };
    (request.post as any).mockRejectedValueOnce(error);

    const action = createSubscriptionPlanAction({ name: "Pro" });
    const result = await action();

    expect(result).toEqual({ message: "Validation failed" });
  });

  it("updateSubscriptionPlanAction - success", async () => {
    const payload = { name: "Updated Plan" };
    (request.put as any).mockResolvedValueOnce({ data: { success: true } });

    const action = updateSubscriptionPlanAction(payload, 10);
    const result = await action();

    expect(request.put).toHaveBeenCalledWith("/subscription-plan/10", payload);
   expect(result).toEqual({ data: { success: true } });
  });

  it("updateSubscriptionPlanAction - error", async () => {
    const error = { response: { error: "Update failed" } };
    (request.put as any).mockRejectedValueOnce(error);

    const action = updateSubscriptionPlanAction({}, 5);
    const result = await action();

    expect(result).toEqual({ error: "Update failed" });
  });

  it("activateSubscriptionPlanAction - success", async () => {
    (request.put as any).mockResolvedValueOnce({ data: { activated: true } });

    const action = activateSubscriptionPlanAction(3);
    const result = await action();

    expect(request.put).toHaveBeenCalledWith("/subscription-plan/activate/3");
    expect(result).toEqual({ data: { activated: true } });
  });

  it("activateSubscriptionPlanAction - error", async () => {
    const error = { response: { error: "Not found" } };
    (request.put as any).mockRejectedValueOnce(error);

    const action = activateSubscriptionPlanAction(3);
    const result = await action();

    expect(result).toEqual({ error: "Not found" });
  });

  it("deleteSubscriptionPlanAction - success", async () => {
    (request.delete as any).mockResolvedValueOnce({ data: { deleted: true } });

    const action = deleteSubscriptionPlanAction(7);
    const result = await action();

    expect(request.delete).toHaveBeenCalledWith("/subscription-plan/7");
    expect(result).toEqual({ data: { deleted: true } });
  });

  it("deleteSubscriptionPlanAction - error", async () => {
    const error = { response: { error: "Cannot delete" } };
    (request.delete as any).mockRejectedValueOnce(error);

    const action = deleteSubscriptionPlanAction(7);
    const result = await action();

    expect(result).toEqual({ error: "Cannot delete" });
  });
});
