import reducer, {
  updateTherapistState,
  registerTherapistAction,
  contactUsAction,
  changePasswordAction,
  sendVerificationEmailAction,
  verifyEmailAction,
  sendTokenAction,
  resetEmailAction,
  sendSmsAction,
  resetPhoneAction,
  enableDisableMfaAction,
  fetchRegInfoAction,
  getTherapistSubscriptionAction,
  getTherapistSubscriptionActionById,
  createTherapistSubscriptionAction,
  updateTherapistSubscriptionAction,
  subscribeUsingStripeAction,
  cancelOrRenewSubscriptionAction,
  syncGoogleCalendarAction,
  syncOutlookCalendarAction,
  removeGoogleCalendarAction,
  removeOutlookCalendarAction,
  fetchAllRegInfoAction,
  fetchLoginInfo,
  confirmPaymentAction
} from "../../../src/store/slicers/therapist.slicer";

import { setUser } from "../../../src/store/slicers/auth.slicer";
import { request } from "../../../src/utils/request.utils";
import { describe, expect, it, vi, beforeEach } from "vitest";

vi.mock("@/utils/request.utils", () => ({
  request: {
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    get: vi.fn(),
    delete: vi.fn()
  }
}));

vi.mock("@/store/slicers/auth.slicer", async () => {
  const actual = await vi.importActual<any>("@/store/slicers/auth.slicer");
  return {
    ...actual,
    setUser: vi.fn(),
    updateRegistrationState: vi.fn()
  };
});

const dispatch = vi.fn();

describe("TherapistSlice", () => {
  const initialState = { fetchingInfo: false };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("should return initial state", () => {
    const state = reducer(undefined, { type: "" });
    expect(state).toEqual(initialState);
  });

  it("should handle updateTherapistState", () => {
    const newState = reducer(initialState, updateTherapistState({
      key: "fetchingInfo",
      value: true
    }));
    expect(newState.fetchingInfo).toBe(true);
  });

  const mockResponse = (data: any, status = 200) => ({
    data,
    status
  });

  const testThunk = async (thunk: any, expectedMethod: keyof typeof request, ...args: any[]) => {
    const resData = { success: true, user: { name: "Dr. Test" } };
    (request[expectedMethod] as any).mockResolvedValue(mockResponse(resData, 200));
    const fn = thunk(...args);
    const result = await fn(dispatch);
    expect(result).toEqual(mockResponse(resData, 200));
  };

  it("registerTherapistAction", async () => {
    await testThunk(registerTherapistAction, "post", { email: "<EMAIL>" }, "1", "tok");
  });

  it("contactUsAction", async () => {
    await testThunk(contactUsAction, "post", { message: "hi" });
  });

 it("changePasswordAction", async () => {
  const resData = { success: true, user: { name: "Dr. Test" } };
  (request.post as any).mockResolvedValue({ data: resData, status: 201 });
  const fn = changePasswordAction({ old: "123", new: "456" });
  const result = await fn(dispatch);
  expect(setUser).toHaveBeenCalledWith(resData.user);
  expect(result).toEqual({ data: resData, status: 201 });
});


  it("sendVerificationEmailAction", async () => {
    await testThunk(sendVerificationEmailAction, "post", { email: "<EMAIL>" });
  });

  it("verifyEmailAction", async () => {
    await testThunk(verifyEmailAction, "post", { email: "<EMAIL>" });
  });

  it("sendTokenAction", async () => {
    await testThunk(sendTokenAction, "post", { email: "<EMAIL>" });
  });

  it("resetEmailAction", async () => {
    await testThunk(resetEmailAction, "post", { email: "<EMAIL>" });
    expect(setUser).toHaveBeenCalled();
  });

  it("sendSmsAction", async () => {
    await testThunk(sendSmsAction, "post", { phone: "123" });
  });

  it("resetPhoneAction", async () => {
    await testThunk(resetPhoneAction, "post", { phone: "123" });
    expect(setUser).toHaveBeenCalled();
  });

  it("enableDisableMfaAction", async () => {
  const response = {
    data: {
      data: {
        user: { name: "Dr. Test" }
      }
    },
    status: 200,
  };
  (request.patch as any).mockResolvedValue(response);
  const fn = enableDisableMfaAction(true);
  const result = await fn(dispatch);
  expect(setUser).toHaveBeenCalledWith(response.data.data.user);
  expect(result).toEqual(response);
});


  it("fetchRegInfoAction with pages", async () => {
    await testThunk(fetchRegInfoAction, "post", { pages: ["page1"] });
  });

  it("fetchRegInfoAction with pageName", async () => {
    await testThunk(fetchRegInfoAction, "get", { pageName: "intro" });
  });

  it("getTherapistSubscriptionAction", async () => {
    await testThunk(getTherapistSubscriptionAction, "get");
  });

  it("getTherapistSubscriptionActionById", async () => {
    await testThunk(getTherapistSubscriptionActionById, "get", 1);
  });

  it("createTherapistSubscriptionAction", async () => {
    await testThunk(createTherapistSubscriptionAction, "post", "sess123", "tok123");
  });

  it("updateTherapistSubscriptionAction", async () => {
    await testThunk(updateTherapistSubscriptionAction, "put", { id: 1 });
  });

  it("subscribeUsingStripeAction", async () => {
    await testThunk(subscribeUsingStripeAction, "post", { plan: "premium" });
  });

  it("cancelOrRenewSubscriptionAction", async () => {
    await testThunk(cancelOrRenewSubscriptionAction, "post", 5);
  });

  it("syncGoogleCalendarAction", async () => {
    await testThunk(syncGoogleCalendarAction, "post", { authCode: "abc" });
  });

  it("syncOutlookCalendarAction", async () => {
    await testThunk(syncOutlookCalendarAction, "post", { authCode: "xyz" });
  });

  it("removeGoogleCalendarAction", async () => {
    await testThunk(removeGoogleCalendarAction, "delete", "usertoken123");
  });

  it("removeOutlookCalendarAction", async () => {
    await testThunk(removeOutlookCalendarAction, "delete", "usertoken456");
  });

  it("fetchAllRegInfoAction", async () => {
    await testThunk(fetchAllRegInfoAction, "get", 1, "abc123");
  });

  it("fetchLoginInfo", async () => {
    await testThunk(fetchLoginInfo, "get", 2, "code456");
  });

  it("confirmPaymentAction", async () => {
    await testThunk(confirmPaymentAction, "post", 42);
  });

  it("registerTherapistAction - error", async () => {
    (request.post as any).mockRejectedValue({ response: { status: 400, message: "Error" } });
    const fn = registerTherapistAction({ email: "<EMAIL>" }, "1", "tok");
    const result = await fn(dispatch);
    expect(result).toEqual({ status: 400, message: "Error" });
  });

  it("changePasswordAction - error", async () => {
    (request.post as any).mockRejectedValue({ response: { status: 401, message: "Unauthorized" } });
    const fn = changePasswordAction({ old: "123", new: "456" });
    const result = await fn(dispatch);
    expect(result).toEqual({ status: 401, message: "Unauthorized" });
  });

  it("resetEmailAction - error", async () => {
    (request.post as any).mockRejectedValue({ response: { status: 403, message: "Forbidden" } });
    const fn = resetEmailAction({ email: "<EMAIL>" });
    const result = await fn(dispatch);
    expect(result).toEqual({ status: 403, message: "Forbidden" });
  });

  it("fetchLoginInfo - error", async () => {
    (request.get as any).mockRejectedValue({ response: { status: 404, message: "Not found" } });
    const fn = fetchLoginInfo(99, "codeX");
    const result = await fn(dispatch);
    expect(result).toEqual({ status: 404, message: "Not found" });
  });

  it("syncGoogleCalendarAction - error", async () => {
    (request.post as any).mockRejectedValue({ response: { status: 500, message: "Internal error" } });
    const fn = syncGoogleCalendarAction({ authCode: "bad-code" });
    const result = await fn(dispatch);
    expect(result).toEqual({ status: 500, message: "Internal error" });
  });

 
});
