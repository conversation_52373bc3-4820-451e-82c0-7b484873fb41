import { render, screen } from "@testing-library/react";
import PaginatedDataContainer from "../../src/components/PaginatedDataContainer";
import '@testing-library/jest-dom';
import { describe, expect, it } from "vitest";

describe("PaginatedDataContainer", () => {
  it("displays default empty message when meta.total is 0 and emptyText is not provided", () => {
    render(
      <PaginatedDataContainer meta={{ total: 0 }}>
        <div>Should not be rendered</div>
      </PaginatedDataContainer>
    );
    expect(screen.getByText("No data available")).toBeInTheDocument();
  });

  it("displays provided emptyText when meta.total is 0", () => {
    render(
      <PaginatedDataContainer meta={{ total: 0 }} emptyText="Nothing found">
        <div>Should not be rendered</div>
      </PaginatedDataContainer>
    );
    expect(screen.getByText("Nothing found")).toBeInTheDocument();
  });

  it("renders children when meta.total is greater than 0", () => {
    render(
      <PaginatedDataContainer meta={{ total: 1 }}>
        <div>Child content here</div>
      </PaginatedDataContainer>
    );
    expect(screen.getByText("Child content here")).toBeInTheDocument();
  });

  
});
