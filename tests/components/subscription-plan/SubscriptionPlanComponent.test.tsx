import { describe, it, expect, vi } from "vitest"
import { render, screen, fireEvent } from "@testing-library/react"
import SubscriptionPlanComponent from "../../../src/components/subscription-plan/SubscriptionPlanComponent"
import type { TherapistSubscription } from "../../../src/types/therapist-subscription.interface"
import type { SubscriptionPlan } from "../../../src/types/subscription-plan.interface"
import "@testing-library/jest-dom"


const mockPlan: TherapistSubscription = {
  subscriptionType: "monthly",
} as TherapistSubscription

const mockSubscriptionPlan: SubscriptionPlan = {
  monthlyPrice: 30,
  annualPrice: 300,
} as SubscriptionPlan

describe("SubscriptionPlanComponent", () => {
  it("renders both Monthly and Annual plans", () => {
    render(
      <SubscriptionPlanComponent
        plan={null}
        subscriptionPlan={mockSubscriptionPlan}
      />
    )

    expect(screen.getByText("Monthly Plan")).toBeInTheDocument()
    expect(screen.getByText("Annual Plan")).toBeInTheDocument()
    expect(screen.getByText("$ 30")).toBeInTheDocument()
    expect(screen.getByText("$ 300")).toBeInTheDocument()
  })

  it("calls setPlanType and onClick when selecting annual plan", () => {
    const setPlanType = vi.fn()
    const onCLick = vi.fn()

    render(
      <SubscriptionPlanComponent
        plan={mockPlan}
        subscriptionPlan={mockSubscriptionPlan}
        setPlanType={setPlanType}
        onCLick={onCLick}
      />
    )

    const annualSelectButton = screen.getAllByRole("button")[1]
    fireEvent.click(annualSelectButton)

    expect(setPlanType).toHaveBeenCalledWith("yearly")
    expect(onCLick).toHaveBeenCalled()
  })

  it("disables annual button if yearly is selected", () => {
    const yearlyPlan: TherapistSubscription = {
      subscriptionType: "yearly",
    } as TherapistSubscription

    render(
      <SubscriptionPlanComponent
        plan={yearlyPlan}
        subscriptionPlan={mockSubscriptionPlan}
      />
    )

    const monthlyBtn = screen.getAllByRole("button")[0]
    const yearlyBtn = screen.getAllByRole("button")[1]

    expect(monthlyBtn).not.toBeDisabled()
    expect(yearlyBtn).toBeDisabled()
    expect(yearlyBtn).toHaveTextContent("Selected")
  })

  it("handles null subscriptionPlan gracefully", () => {
    render(
      <SubscriptionPlanComponent
        plan={null}
        subscriptionPlan={null}
      />
    )

    expect(screen.getByText("Monthly Plan")).toBeInTheDocument()
    expect(screen.getByText("Annual Plan")).toBeInTheDocument()
  })

  it("shows selected icon and disables button for selected plan (monthly)", () => {
  render(
    <SubscriptionPlanComponent
      plan={{ subscriptionType: "monthly" }}
      subscriptionPlan={mockSubscriptionPlan}
    />
  )

  const monthlyHeading = screen.getByText(/monthly plan/i)
  const icon = monthlyHeading.querySelector("i")

  expect(icon).toBeInTheDocument()
  expect(icon).toHaveClass("fa-circle-check")

  const monthlyButton = screen.getByRole("button", { name: "Selected" })
  expect(monthlyButton).toBeDisabled()
})

it("calls setPlanType and onClick when selecting monthly plan", () => {
  const setPlanType = vi.fn()
  const onCLick = vi.fn()

  render(
    <SubscriptionPlanComponent
      plan={{ subscriptionType: "yearly" }}
      subscriptionPlan={mockSubscriptionPlan}
      setPlanType={setPlanType}
      onCLick={onCLick}
    />
  )

  const monthlySelectButton = screen.getByRole("button", { name: "Select" })
  fireEvent.click(monthlySelectButton)

  expect(setPlanType).toHaveBeenCalledWith("monthly")
  expect(onCLick).toHaveBeenCalled()
})
})
