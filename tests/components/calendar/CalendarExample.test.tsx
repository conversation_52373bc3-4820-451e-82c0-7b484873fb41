import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CalendarExample from '../../../src/components/calendar/CalendarExample';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import * as Google from '@react-oauth/google';
import notification from '../../../src/utils/notification.util';
import CalendarRepository from '../../../src/repositories/CalendarRepository';
import "@testing-library/jest-dom";

vi.mock('@/repositories/CalendarRepository');
vi.mock('@react-oauth/google', () => ({
  useGoogleLogin: vi.fn()
}));
vi.mock('@/utils/notification.util', () => ({
  default: {
    error: vi.fn(),
    info: vi.fn(),
  }
}));

vi.mock('@/utils/app.util', () => ({
  generateURLEncodedString: vi.fn(),
  createCodeChallenge: vi.fn(),
}));

const mockSyncGoogleCalendar = vi.fn();
const mockSyncOutlookCalendar = vi.fn();

const MockedCalendarRepository = CalendarRepository as unknown as vi.Mock;

MockedCalendarRepository.mockImplementation(() => ({
  syncGoogleCalendar: mockSyncGoogleCalendar,
  syncOutlookCalendar: mockSyncOutlookCalendar,
}));

describe('CalendarExample', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    window.location.href = 'http://localhost:5173/';
  });

  it('should render Google and Outlook sync buttons', () => {
    (Google.useGoogleLogin as any).mockReturnValue(() => {});
    render(<CalendarExample />);
    expect(screen.getByText('Sync Google Calendar')).toBeInTheDocument();
    expect(screen.getByText('Sync Outlook Calendar')).toBeInTheDocument();
  });

  it('should call google login on button click', async () => {
    const googleLoginMock = vi.fn();
    (Google.useGoogleLogin as any).mockReturnValue(googleLoginMock);
    render(<CalendarExample />);
    fireEvent.click(screen.getByText('Sync Google Calendar'));
    expect(googleLoginMock).toHaveBeenCalled();
  });

  it('should handle google login error (not access_denied)', async () => {
    (Google.useGoogleLogin as any).mockImplementation(({ onError }) => () => {
      onError({ error: 'some_error' });
    });
    render(<CalendarExample />);
    fireEvent.click(screen.getByText('Sync Google Calendar'));
    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith('some_error');
    });
  });

  it('should handle google login access_denied error gracefully', async () => {
    (Google.useGoogleLogin as any).mockImplementation(({ onError }) => () => {
      onError({ error: 'access_denied' });
    });
    render(<CalendarExample />);
    fireEvent.click(screen.getByText('Sync Google Calendar'));
    expect(notification.error).not.toHaveBeenCalled();
  });

  it('should show error if codeVerifier is missing', async () => {
    localStorage.removeItem('codeVerifier');
    localStorage.setItem('state', 'testState');

    Object.defineProperty(window, 'location', {
      value: {
        search: '?code=testCode&state=testState',
        pathname: '/',
        href: '',
      },
      writable: true,
    });

    render(<CalendarExample />);
    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith('Something went wrong. Please try Logging in again.');
    });
  });

  it('should show error if state mismatch', async () => {
    localStorage.setItem('codeVerifier', 'mock-verifier');
    localStorage.setItem('state', 'someOtherState');

    Object.defineProperty(window, 'location', {
      value: {
        search: '?code=testCode&state=testState',
        pathname: '/',
        href: '',
      },
      writable: true,
    });

    render(<CalendarExample />);
    await waitFor(() => {
      expect(notification.error).toHaveBeenCalledWith('Something went wrong. Please try Logging in again.');
    });
  });

  it('should not render events if events array is empty', () => {
    (Google.useGoogleLogin as any).mockReturnValue(() => {});
    render(<CalendarExample />);
    expect(screen.queryByText('Upcoming Events')).not.toBeInTheDocument();
  });

  it('should redirect to Outlook authorization URL on Outlook button click', async () => {
  const mockState = 'mock-state';
  const mockCodeVerifier = 'mock-verifier';
  const mockCodeChallenge = 'mock-challenge';

  const mockSetItem = vi.spyOn(window.localStorage.__proto__, 'setItem');

  const generateURLEncodedString = (await import('../../../src/utils/app.util')).generateURLEncodedString as any;
  const createCodeChallenge = (await import('../../../src/utils/app.util')).createCodeChallenge as any;

  generateURLEncodedString.mockResolvedValueOnce(mockState); 
  generateURLEncodedString.mockResolvedValueOnce(mockCodeVerifier);
  createCodeChallenge.mockResolvedValueOnce(mockCodeChallenge); 
  delete window.location;
  window.location = { href: '', pathname: '/' } as any;

  render(<CalendarExample />);
  fireEvent.click(screen.getByText('Sync Outlook Calendar'));

  await waitFor(() => {
    expect(mockSetItem).toHaveBeenCalledWith('state', mockState);
    expect(mockSetItem).toHaveBeenCalledWith('codeVerifier', mockCodeVerifier);
    expect(window.location.href).toContain('https://login.microsoftonline.com/common/oauth2/v2.0/authorize');
    expect(window.location.href).toContain(`state=${mockState}`);
    expect(window.location.href).toContain(`code_challenge=${mockCodeChallenge}`);
  });
});



});
