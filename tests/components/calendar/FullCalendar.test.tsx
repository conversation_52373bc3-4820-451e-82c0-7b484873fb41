import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import FullCalendar from '../../../src/components/calendar/FullCalendar';
import { TherapyDate } from '../../../src/plugins/calendar/types';
import "@testing-library/jest-dom";

vi.mock('@/store/hooks', () => ({
  useAppDispatch: () => vi.fn(),
}));


vi.mock('framer-motion', async () => {
  const actual = await vi.importActual('framer-motion');
  return {
    ...actual,
    AnimatePresence: ({ children }: any) => <>{children}</>,
    motion: {
      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    },
  };
});

describe('FullCalendar', () => {  
  const mockCalendar = {
    previous: vi.fn(),
    next: vi.fn(),
    goto: vi.fn(),
  };

const mockDays: TherapyDate[] = [
  {
    key: '2025-05-30',
    date: new Date('2025-05-30'),
    time: '2025-05-30',
    day: 'Friday',
    dayOfMonth: '30',
    isCurrentMonth: true,
    isToday: true,
    isSelected: false,
    events: [
      { id: '1', name: 'Event 1', date: new Date('2025-05-30T10:00:00'), time: '10:00 AM' },
      { id: '2', name: 'Event 2', date: new Date('2025-05-30T11:00:00'), time: '11:00 AM' },
      { id: '3', name: 'Event 3', date: new Date('2025-05-30T12:00:00'), time: '12:00 PM' },
    ],
  },
  {
    key: '2025-05-31',
    date: new Date('2025-05-31'),
    time: '2025-05-31',
    day: 'Saturday',
    dayOfMonth: '31',
    isCurrentMonth: false,
    isToday: false,
    isSelected: false,
    events: [],
  },
];

  it('renders calendar with filters', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={true} />);
    expect(screen.getByText('Sun')).toBeInTheDocument();
    expect(screen.getByText('Mon')).toBeInTheDocument();
    expect(screen.getByText('Tue')).toBeInTheDocument();
    expect(screen.getByText('Wed')).toBeInTheDocument();
    expect(screen.getByText('Thu')).toBeInTheDocument();
    expect(screen.getByText('Fri')).toBeInTheDocument();
    expect(screen.getByText('Sat')).toBeInTheDocument();
  });

  it('calls calendar.previous on previous button click', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={true} />);
    const prevButton = screen.getAllByRole('button')[0];
    fireEvent.click(prevButton);
    expect(mockCalendar.previous).toHaveBeenCalled();
  });

  it('calls calendar.goto on today button click', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={true} />);
    const todayButton = screen.getByText('Today');
    fireEvent.click(todayButton);
    expect(mockCalendar.goto).toHaveBeenCalled();
  });

  it('calls calendar.next on next button click', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={true} />);
    const buttons = screen.getAllByRole('button');
    const nextButton = buttons[2];
    fireEvent.click(nextButton);
    expect(mockCalendar.next).toHaveBeenCalled();
  });

  it('toggles view dropdown and selects a view', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={true} />);
    const viewButton = screen.getByText('Month View');
    fireEvent.click(viewButton);
    const dayViewOption = screen.getByText('Day view');
    fireEvent.click(dayViewOption);
    expect(screen.getByText('Day view')).toBeInTheDocument();
  });

  it('renders events and shows "+ more" when more than 2 events', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={true} />);
    expect(screen.getByText('Event 1')).toBeInTheDocument();
    expect(screen.getByText('Event 2')).toBeInTheDocument();
    expect(screen.getByText('+ 1 more')).toBeInTheDocument();
  });

  it('renders calendar without filters', () => {
    render(<FullCalendar days={mockDays} calendar={mockCalendar} filters={false} />);
    expect(screen.queryByText('Today')).not.toBeInTheDocument();
  });
});
