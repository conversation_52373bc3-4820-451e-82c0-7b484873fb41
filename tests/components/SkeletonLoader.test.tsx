import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { Skeleton, TherapistRowSkeleton } from '../../src/components/SkeletonLoader';
import React from 'react';
import "@testing-library/jest-dom"

describe('Skeleton', () => {
  it('renders with default props', () => {
    render(<Skeleton />);
    const div = screen.getByTestId('skeleton');
    expect(div).toHaveStyle({ width: '100%', height: '1rem' });
    expect(div).toHaveClass('animate-pulse bg-gray-200 rounded');
  });

 it('renders with custom props', () => {
    render(<Skeleton width="50px" height="20px" className="custom-class" />);
    const div = screen.getByTestId('skeleton');
    expect(div).toHaveStyle({ width: '50px', height: '20px' });
    expect(div).toHaveClass('custom-class');
  });
});

describe('TherapistRowSkeleton', () => {
  it('renders 5 rows by default', () => {
    render(
      <table>
        <tbody>
          <TherapistRowSkeleton />
        </tbody>
      </table>
    );
    const rows = screen.getAllByRole('row');
    expect(rows).toHaveLength(5);
  });

  it('renders custom number of rows', () => {
    render(
      <table>
        <tbody>
          <TherapistRowSkeleton count={3} />
        </tbody>
      </table>
    );
    const rows = screen.getAllByRole('row');
    expect(rows).toHaveLength(3);
  });
});
