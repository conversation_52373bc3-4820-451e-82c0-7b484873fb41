import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import SortButton from "../../../src/components/buttons/SortButton";
import { describe, it, vi, expect } from "vitest";
import "@testing-library/jest-dom"

describe("SortButton", () => {
  it("renders with default (inactive) styles and label", () => {
    render(<SortButton label="Sort" onPress={() => {}} />);
    const button = screen.getByRole("button", { name: /sort/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass("text-quaternary");
    expect(button).toHaveTextContent("Sort");
  });

  it("renders with active styles", () => {
    render(<SortButton label="Sort" active onPress={() => {}} />);
    const button = screen.getByRole("button", { name: /sort/i });
    expect(button).toHaveClass("bg-quaternary");
  });

  it("calls onPress when clicked", async () => {
    const user = userEvent.setup();
    const onPress = vi.fn();
    render(<SortButton label="Sort" onPress={onPress} />);
    const button = screen.getByRole("button", { name: /sort/i });
    await user.click(button);
    expect(onPress).toHaveBeenCalledTimes(1);
  });
});
