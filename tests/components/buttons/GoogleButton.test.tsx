import { render, screen, fireEvent } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import GoogleButton from "../../../src/components/buttons/GoogleButton";
import { useAppSelector } from "../../../src/store/hooks";
import { useGoogleLogin } from "@react-oauth/google";
import CalendarRepository from "../../../src/repositories/CalendarRepository";
import "@testing-library/jest-dom";

// Mocks
vi.mock("@/store/hooks", () => ({
  useAppSelector: vi.fn(),
}));

vi.mock("@react-oauth/google", () => ({
  useGoogleLogin: vi.fn(),
}));

vi.mock("@/repositories/CalendarRepository");

describe("GoogleButton", () => {
  const mockCreate = vi.fn(() => Promise.resolve("created"));
  const mockErrorToast = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (CalendarRepository as any).mockImplementation(() => ({
      create: mockCreate,
      errorToast: mockErrorToast,
    }));
  });

  it("does not render button if user is null", () => {
    (useAppSelector as any).mockReturnValue(null);
    render(<GoogleButton />);
    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  it("does not render button if user hasGoogleCalendar is true", () => {
    (useAppSelector as any).mockReturnValue({ hasGoogleCalendar: true });
    render(<GoogleButton />);
    expect(screen.queryByRole("button")).not.toBeInTheDocument();
  });

  it("renders button if user hasGoogleCalendar is false", () => {
    (useAppSelector as any).mockReturnValue({ hasGoogleCalendar: false });
    (useGoogleLogin as any).mockReturnValue(() => {});
    render(<GoogleButton />);
    expect(screen.getByRole("button", { name: /sync google calendar/i })).toBeInTheDocument();
  });

  it("calls googleLogin on button click", () => {
    const mockLogin = vi.fn();
    (useAppSelector as any).mockReturnValue({ hasGoogleCalendar: false });
    (useGoogleLogin as any).mockReturnValue(mockLogin);

    render(<GoogleButton />);
    fireEvent.click(screen.getByRole("button"));
    expect(mockLogin).toHaveBeenCalled();
  });

  it("calls repository.create on successful login", async () => {
    let successCallback: any;
    (useAppSelector as any).mockReturnValue({ hasGoogleCalendar: false });

    (useGoogleLogin as any).mockImplementation(({ onSuccess }) => {
      successCallback = onSuccess;
      return () => {};
    });

    render(<GoogleButton />);
    await successCallback({ token: "abc123" });
    expect(mockCreate).toHaveBeenCalledWith({
      type: "google",
      credentials: { token: "abc123" },
    });
  });

  it("calls errorToast on login error", async () => {
    let errorCallback: any;
    (useAppSelector as any).mockReturnValue({ hasGoogleCalendar: false });

    (useGoogleLogin as any).mockImplementation(({ onError }) => {
      errorCallback = onError;
      return () => {};
    });

    render(<GoogleButton />);
    await errorCallback({ error_description: "OAuth failed" });
    expect(mockErrorToast).toHaveBeenCalledWith("OAuth failed");

    await errorCallback({ error: "Generic error" });
    expect(mockErrorToast).toHaveBeenCalledWith("Generic error");

    await errorCallback({});
    expect(mockErrorToast).toHaveBeenCalledWith("Unable to login with Google.");
  });
});
