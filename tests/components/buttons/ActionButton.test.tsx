import { render, fireEvent, screen } from '@testing-library/react';
import ActionButton from '../../../src/components/buttons/ActionButton';
import { describe, it, expect, vi } from 'vitest';
import React from 'react';
import userEvent from '@testing-library/user-event';
import "@testing-library/jest-dom"

describe('ActionButton Component', () => {
    const createHandlers = () => ({
        onEdit: vi.fn(),
        onDelete: vi.fn(),
        onDeactivate: vi.fn(),
        onActivate: vi.fn(),
        onViewProfile: vi.fn(),
        onResetPassword: vi.fn(),
        onAccept: vi.fn(),
        onReject: vi.fn(),
        onRejectedReason: vi.fn(),
    });

    it('renders all action buttons and triggers all handlers (fixed)', async () => {
        const handlers = createHandlers();
        render(<ActionButton {...handlers} isResetPasswordDisabled={false} />);
    
        const toggleButton = screen.getByRole('button');
        fireEvent.click(toggleButton);
    
        const actions = [
            { testId: 'edit-button', fn: handlers.onEdit },          
          ];
      
          for (const { testId, fn } of actions) {
            const button = await screen.findByTestId(testId);
            await userEvent.hover(button);
            fireEvent.click(button);
            expect(fn).toHaveBeenCalled();
          }
      });

    it('disables reset password button when flag is true', async () => {
        const handlers = createHandlers();
        render(<ActionButton {...handlers} isResetPasswordDisabled={true} />);
        fireEvent.click(screen.getByRole('button'));

        const resetBtn = await screen.findByText('Reset Password');
        expect(resetBtn).toBeDisabled();
    });

    it('renders nothing if no handlers are provided', () => {
        render(<ActionButton />);
        fireEvent.click(screen.getByRole('button'));
        expect(screen.queryByText('Edit')).toBeNull();
    });

    it('renders only provided action buttons', async () => {
        const handlers = {
            onEdit: vi.fn(),
            onDelete: vi.fn(),
        };
        render(<ActionButton {...handlers} isResetPasswordDisabled={false} />);
        fireEvent.click(screen.getByRole('button'));

        const editButton = await screen.findByText('Edit');
        expect(editButton).toBeInTheDocument();

        const deleteButton = await screen.findByText('Delete');
        expect(deleteButton).toBeInTheDocument();

        expect(screen.queryByText('View Profile')).toBeNull();
        expect(screen.queryByText('Reset Password')).toBeNull();
    });

    it('does not trigger handlers for disabled buttons', async () => {
        const handlers = createHandlers();
        render(<ActionButton {...handlers} isResetPasswordDisabled={true} />);
        fireEvent.click(screen.getByRole('button'));

        const resetBtn = await screen.findByText('Reset Password');
        fireEvent.click(resetBtn);
        expect(handlers.onResetPassword).not.toHaveBeenCalled();
    });

    it('disables reset password button when flag is true', async () => {
        const handlers = createHandlers();
        render(<ActionButton {...handlers} isResetPasswordDisabled={true} />);
        fireEvent.click(screen.getByRole('button'));
    
        const resetBtn = await screen.findByText('Reset Password');
        expect(resetBtn).toBeDisabled();
      });
    
      it('renders nothing if no handlers are provided', () => {
        render(<ActionButton />);
        fireEvent.click(screen.getByRole('button'));
        expect(screen.queryByText('Edit')).toBeNull();
      });

    it('renders as a simple button when onClick is provided and triggers onClick', async () => {
        const handleClick = vi.fn();

        render(
            <ActionButton onClick={handleClick} variant="danger" size="sm">
            Click Me
            </ActionButton>
        );

        const button = screen.getByRole('button', { name: 'Click Me' });
        expect(button).toBeInTheDocument();
        expect(button).toHaveClass('bg-red-600 text-white hover:bg-red-700');
        expect(button).toHaveClass('text-xs px-2 py-1');

        await userEvent.click(button);
        expect(handleClick).toHaveBeenCalledTimes(1);
    });
    it('renders with default blue variant when variant is not danger', async () => {
        const handleClick = vi.fn();

        render(
            <ActionButton onClick={handleClick} variant="primary">
            Submit
            </ActionButton>
        );

        const button = screen.getByRole('button', { name: 'Submit' });
        expect(button).toBeInTheDocument();
        expect(button).toHaveClass('bg-blue-600 text-white hover:bg-blue-700');

        await userEvent.click(button);
        expect(handleClick).toHaveBeenCalledTimes(1);
    });


});
