import { render, screen, fireEvent } from '@testing-library/react';
import DeleteButton from '../../../src/components/buttons/DeleteButton';
import EditButton from '../../../src/components/buttons/EditButton';
import { describe, it, expect, vi } from 'vitest';

describe('DeleteButton', () => {
  it('renders with label', () => {
    render(<DeleteButton label="Delete" />);
    expect(screen.getByText('Delete')).toBeInTheDocument();
    expect(screen.getByRole('button')).toHaveClass('text-black');
  });

  it('calls onPress when clicked', () => {
    const onPress = vi.fn();
    render(<DeleteButton label="Delete" onPress={onPress} />);
    fireEvent.click(screen.getByRole('button'));
    expect(onPress).toHaveBeenCalledTimes(1);
  });
});

describe('EditButton', () => {
  it('renders with label and icon', () => {
    render(<EditButton label="Edit" />);
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Edit');
    expect(button.querySelector('i')).toHaveClass('fa-edit');
    expect(button).toHaveClass('text-quaternary');
  });

  it('calls onPress when clicked', () => {
    const onPress = vi.fn();
    render(<EditButton label="Edit" onPress={onPress} />);
    fireEvent.click(screen.getByRole('button'));
    expect(onPress).toHaveBeenCalledTimes(1);
  });
});
