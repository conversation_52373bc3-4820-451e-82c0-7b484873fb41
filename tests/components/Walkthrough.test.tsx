import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, vi, expect } from 'vitest';
import WalkThrough from '../../src/components/common/Walkthrough';
import type { Step, TooltipRenderProps } from 'react-joyride';
import '@testing-library/jest-dom'

const baseStep: Step = {
  content: '<p>Test Step Content</p>',
  placement: 'top',
  target: 'body',
  styles: {},
  disableBeacon: true,
};

const baseProps: TooltipRenderProps = {
  step: baseStep,
  backProps: { onClick: vi.fn(), disabled: false, title: '1/3' },
  primaryProps: { onClick: vi.fn(), title: 'Next' },
  tooltipProps: { style: {} },
  index: 0,
  size: 3,
  closeButtonHandler: vi.fn(),
};

describe('WalkThrough', () => {
  it('renders content and arrow based on placement', () => {
    render(<WalkThrough {...baseProps} />);
    expect(screen.getByText('Test Step Content')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Close/i })).toBeInTheDocument();
  });

  it('hides arrow when placement is center', () => {
    render(
      <WalkThrough {...baseProps} step={{ ...baseProps.step!, placement: 'center' }} />
    );
    expect(document.querySelector('.tooltip-arrow')).toBeNull();
  });

  it('calls closeButtonHandler when close button is clicked', () => {
    const mockClose = vi.fn();
    render(<WalkThrough {...baseProps} closeButtonHandler={mockClose} />);
    fireEvent.click(screen.getByRole('button', { name: /Close/i }));
    expect(mockClose).toHaveBeenCalled();
  });

  it('disables back button when index is 0', () => {
    render(<WalkThrough {...baseProps} index={0} />);
    const backButton = screen.getByRole('button', { name: /1\/3/i });
    expect(backButton).toBeDisabled();
  });

  it('renders "Finish" on last step', () => {
    render(<WalkThrough {...baseProps} index={2} size={3} />);
    expect(screen.getByText('Finish')).toBeInTheDocument();
  });

  it('renders "Next" on middle steps', () => {
    render(<WalkThrough {...baseProps} index={1} size={3} />);
    expect(screen.getByText('Next')).toBeInTheDocument();
  });

  it('calls primaryProps.onClick when next button is clicked', () => {
    const mockNext = vi.fn();
    render(<WalkThrough {...baseProps} primaryProps={{ ...baseProps.primaryProps, onClick: mockNext }} />);
    fireEvent.click(screen.getByText('Next'));
    expect(mockNext).toHaveBeenCalled();
  });

  it('handles unknown placement gracefully (fallback arrow)', () => {
    render(
      <WalkThrough
        {...baseProps}
        step={{ ...baseProps.step!, placement: 'unknown' as any }}
      />
    );
    expect(document.querySelector('.arrow-top')).toBeNull(); // fallback arrow
  });
});


