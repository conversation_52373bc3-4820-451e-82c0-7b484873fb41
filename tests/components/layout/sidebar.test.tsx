import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import Sidebar from '../../../src/components/layouts/Sidebar';
import authReducer from '../../../src/store/slicers/auth.slicer';
import sidebarReducer from '../../../src/store/slicers/sidebar.slicer';
import * as menuUtil from '../../../src/utils/menu.util';

vi.mock('react-hot-toast', () => ({
  default: {
    dismiss: vi.fn(),
  },
}));

vi.mock('../../../src/utils/menu.util', () => ({
  prepareMenus: vi.fn().mockReturnValue([
    { label: 'Dashboard', link: '/', roles: ['Therapists'] },
    { label: 'Management', isTitle: true, roles: ['Therapists'] },
    { label: 'Therapists', link: '/therapists', roles: ['Therapists'] },
  ]),
}));


vi.mock('../../../src/repositories/TherapistRepository', () => {
  class TherapistRepositoryMock {
    getAll = vi.fn().mockResolvedValue({
      data: [],
      meta: { total: 5 },
    });
  }

  return {
    default: TherapistRepositoryMock,
  };
});


const setupTest = (initialState = {}) => {
  const store = configureStore({
    reducer: {
      auth: authReducer,
      sidebar: sidebarReducer,
    },
    preloadedState: initialState,
  });

  return render(
    <Provider store={store}>
      <BrowserRouter>
        <Sidebar />
      </BrowserRouter>
    </Provider>
  );
};

describe('Sidebar Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
    vi.resetModules();
    cleanup();
  });

  it('renders sidebar with menus', async () => {
    setupTest({
      auth: { user: { role: 'Therapists' } },
      sidebar: { on: true },
    });
  
    await waitFor(() => {
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Management')).toBeInTheDocument();
      expect(screen.getByText('Therapists')).toBeInTheDocument();
    });
  });

  it('handles sign out correctly', () => {
    setupTest();
    
    const signOutButton = screen.getByLabelText('sign out');
    fireEvent.click(signOutButton);
    
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  it('renders sidebar with logo', () => {
    setupTest();
    const logo = screen.getByAltText('logo');
    expect(logo).toBeInTheDocument();
  });

  it('renders user information when user is logged in', () => {
    const mockUser = {
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
      role: 'Therapists',
    };

    setupTest({
      auth: { user: mockUser },
      sidebar: { on: true },
    });

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles sidebar visibility toggle', () => {
    const { container } = setupTest({
      auth: { user: { role: 'Therapists' } },
      sidebar: { on: true },
    });
    const closeButton = screen.getByTestId('close-button');
    fireEvent.click(closeButton!);

    expect(container.firstChild).toHaveClass('-translate-x-[250px]');
  });

  it('generates navigation menu based on user role', async () => {
    setupTest({
      auth: { user: { role: 'Therapists' } },
    });

    await waitFor(() => {
      expect(menuUtil.prepareMenus).toHaveBeenCalledWith('Therapists', 5);
    });
  });

  it('is responsive on mobile devices', () => {
    const { container } = setupTest({
      sidebar: { on: false },
    });

    expect(container.firstChild).toHaveClass('-translate-x-[250px]');
    expect(container.firstChild).toHaveClass('md:w-[250px]');
  });
});