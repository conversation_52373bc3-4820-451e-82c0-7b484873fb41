import React from 'react'
import { Suspense } from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import configureStore from 'redux-mock-store'
import RegisterLayout from '../../../src/components/layouts/RegisterLayout'
import '@testing-library/jest-dom'
import * as hooks from '../../../src/store/hooks'

const mockStore = configureStore([])

vi.mock('react-perfect-scrollbar', () => ({
	__esModule: true,
	default: vi.fn(() => <div data-testid="perfect-scrollbar" />),
}))

vi.mock('react-redux', async () => {
	const actual = await import('react-redux')
	return {
		...actual,
		useSelector: vi.fn(),
		useDispatch: vi.fn(() => vi.fn()),
	}
})

describe('RegisterLayout Component', () => {
	let store
	let useAppSelectorMock

    beforeEach(() => {
        vi.clearAllMocks()
        store = mockStore({
          sideBar: { open: false },
          auth: {
            registration: {
              editFlag: false,
              savingTherapistInfo: false,
              formContent: {
                'create-account': {
                  title: 'Create Account',
                  description: 'Enter your details',
                },
              },
            },
          },
        })
      
        useAppSelectorMock = vi.spyOn(hooks, 'useAppSelector')
      })

	it('renders without crashing', () => {
  useAppSelectorMock.mockImplementation((selector) =>
    selector({
      sidebar: { open: false },
      auth: {
        registration: {
          editFlag: true,
          savingTherapistInfo: false,
          formContent: {
            'create-account': {
              title: 'Create Account',
              description: 'Enter your details',
            },
          },
        },
      },
    })
  )

  render(
    <Provider store={store}>
      <MemoryRouter>
        <Suspense fallback={<div>Loading...</div>}>
          <RegisterLayout />
        </Suspense>
      </MemoryRouter>
    </Provider>
  )

  expect(screen.getByTestId('perfect-scrollbar')).toBeInTheDocument()
})
	it('shows the sidebar when editFlag is false', () => {
		useAppSelectorMock.mockImplementation((selector) =>
			selector({
				sidebar: { open: false },
				auth: { registration: { editFlag: false, savingTherapistInfo: false ,formContent: {
                    'create-account': {
                      title: 'Create Account',
                      description: 'Enter your details',
                    },
                  },} },
			})
		)

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Suspense fallback={<div>Loading...</div>}>
						<RegisterLayout />
					</Suspense>
				</MemoryRouter>
			</Provider>
		)

		expect(screen.getByTestId('register-therapist-sidebar')).toBeInTheDocument()
	})

	it('hides the sidebar when editFlag is true', () => {
		useAppSelectorMock.mockImplementation((selector) =>
			selector({
				sidebar: { open: false },
				auth: { registration: { editFlag: true, savingTherapistInfo: false ,formContent: {
                    'create-account': {
                      title: 'Create Account',
                      description: 'Enter your details',
                    },
                  },} },
			})
		)

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Suspense fallback={<div>Loading...</div>}>
						<RegisterLayout />
					</Suspense>
				</MemoryRouter>
			</Provider>
		)

		expect(
			screen.queryByText(/register-therapist-sidebar/i)
		).not.toBeInTheDocument()
	})

	it('displays loader when savingTherapistInfo is true', async () => {
		useAppSelectorMock.mockImplementation((selector) =>
			selector({
				sidebar: { open: false },
				auth: { registration: { editFlag: false, savingTherapistInfo: true ,formContent: {
                    'create-account': {
                      title: 'Create Account',
                      description: 'Enter your details',
                    },
                  },} },
			})
		)

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Suspense fallback={<div>Loading...</div>}>
						<RegisterLayout />
					</Suspense>
				</MemoryRouter>
			</Provider>
		)

		await screen.findByTestId('loader')
		expect(screen.getByTestId('loader')).toBeInTheDocument()
	})

	it('hides loader when savingTherapistInfo is false', async () => {
		useAppSelectorMock.mockImplementation((selector) =>
			selector({
				sidebar: { open: false },
				auth: { registration: { editFlag: false, savingTherapistInfo: false ,formContent: {
                    'create-account': {
                      title: 'Create Account',
                      description: 'Enter your details',
                    },
                  },} },
			})
		)

		render(
			<Provider store={store}>
				<MemoryRouter>
					<Suspense fallback={<div>Loading...</div>}>
						<RegisterLayout />
					</Suspense>
				</MemoryRouter>
			</Provider>
		)

		await waitFor(() => {
			expect(screen.queryByTestId('loader')).not.toBeInTheDocument()
		})
	})
})
