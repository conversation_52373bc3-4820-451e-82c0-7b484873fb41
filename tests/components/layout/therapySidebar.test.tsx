import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import TherapySidebar from '../../../src/components/layouts/TherapySidebar';
import authReducer from '../../../src/store/slicers/auth.slicer';
import sidebarReducer from '../../../src/store/slicers/sidebar.slicer';
import * as menuUtil from '../../../src/utils/menu.util';
import '@testing-library/jest-dom';
import { User } from '../../../src/types/user.interface';

const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

vi.mock('@/utils/menu.util', () => ({
  prepareMenus: vi.fn().mockReturnValue([
    {
      label: 'Dashboard',
      link: '/dashboard',
      icon: {
        active: '/icons/dashboard-active.svg',
        inactive: '/icons/dashboard-inactive.svg',
      },
    },
  ]),
}));

interface RootState {
  auth: {
    user: User;
    isAuthenticated: boolean;
    registration: {
      fetchingPage: boolean;
      formContent: Record<string, any>;
      therapistMatchKey: string | null;
      matchedTherapists: any[];
      patientAnswers: Record<string, any>;
      userRegistrationToken: string | null;
      savingTherapistInfo: boolean;
      currentPage: string | null;
      editFlag: boolean;
      navToInvalidPage: boolean;
      showRegCompleteDialog: boolean;
      shouldShowRegCompleteDialog: boolean;
    };
  };
  sidebar: {
    on: boolean;
    registerTherapist: {
      openSubMenu: Record<string, boolean>;
      invalidMenus: string[];
    };
  };
}

describe('TherapySidebar', () => {
  let store: ReturnType<typeof configureStore<{
    auth: ReturnType<typeof authReducer>;
    sidebar: ReturnType<typeof sidebarReducer>;
  }>>;

  const mockUser: Partial<User> = {
    firstname: 'John',
    lastname: 'Doe',
    email: '<EMAIL>',
    role: 'admin',
    userProfile: 'profile.jpg',
  };

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authReducer,
        sidebar: sidebarReducer,
      },
      preloadedState: {
        auth: {
          user: mockUser as User,
          isAuthenticated: true,
          registration: {
            fetchingPage: false,
            formContent: {},
            therapistMatchKey: null,
            matchedTherapists: [],
            patientAnswers: {},
            userRegistrationToken: null,
            savingTherapistInfo: false,
            currentPage: null,
            editFlag: false,
            navToInvalidPage: false,
            showRegCompleteDialog: false,
            shouldShowRegCompleteDialog: true,
          },
        },
        sidebar: {
          on: true,
          registerTherapist: {
            openSubMenu: {},
            invalidMenus: [],
          },
        },
      } as RootState,
    });
  });

  const renderComponent = () => {
    return render(
      <Provider store={store}>
        <BrowserRouter>
          <TherapySidebar />
        </BrowserRouter>
      </Provider>
    );
  };

  it('renders the sidebar correctly', () => {
    renderComponent();
    expect(screen.getByTestId('therapy-sidebar')).toBeInTheDocument();
    expect(screen.getByAltText('logo')).toBeInTheDocument();
  });

  it('displays user information correctly', () => {
    renderComponent();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByAltText('Profile Picture')).toHaveAttribute('src', 'profile.jpg');
  });

  it('uses avatar API when userProfile is not provided', () => {
    store = configureStore({
      reducer: {
        auth: authReducer,
        sidebar: sidebarReducer,
      },
      preloadedState: {
        auth: {
          user: {
            ...mockUser,
            userProfile: undefined,
          } as User,
          isAuthenticated: true,
          registration: {
            fetchingPage: false,
            formContent: {},
            therapistMatchKey: null,
            matchedTherapists: [],
            patientAnswers: {},
            userRegistrationToken: null,
            savingTherapistInfo: false,
            currentPage: null,
            editFlag: false,
            navToInvalidPage: false,
            showRegCompleteDialog: false,
            shouldShowRegCompleteDialog: true,
          },
        },
        sidebar: {
          on: true,
          registerTherapist: {
            openSubMenu: {},
            invalidMenus: [],
          },
        },
      } as RootState,
    });

    renderComponent();
    expect(screen.getByAltText('Profile Picture')).toHaveAttribute(
      'src',
      'https://ui-avatars.com/api?name=John+Doe'
    );
  });

  it('loads and displays menu items correctly', () => {
    renderComponent();
    expect(menuUtil.prepareMenus).toHaveBeenCalledWith('admin');
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  it('handles sign out correctly', () => {
    renderComponent();
    const signOutButton = screen.getByRole('img', { name: 'Profile Picture' }).parentElement?.parentElement?.nextElementSibling?.querySelector('i');
    fireEvent.click(signOutButton!);
    expect(mockNavigate).toHaveBeenCalledWith('/auth/login');
  });

 it('toggles sidebar visibility on close button click', () => {
  renderComponent();
  const closeButton = screen.getByTestId('close-button');
  fireEvent.click(closeButton);
  expect((store.getState() as RootState).sidebar.on).toBe(false);
});

  it('renders navigation links with correct active states', () => {
    renderComponent();
    const navLink = screen.getByText('Dashboard').closest('a');
    expect(navLink).toHaveAttribute('href', '/dashboard');
   
    const icon = screen.getByAltText('Dashboard');
    expect(icon).toHaveAttribute('src', '/icons/dashboard-inactive.svg');
  });

  it('handles menu click and hides sidebar on mobile', () => {
    renderComponent();
    const navLink = screen.getByText('Dashboard');
    fireEvent.click(navLink);
    expect((store.getState() as RootState).sidebar.on).toBe(false);
  });
}); 