import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import EditTherapistPage from '../../../src/components/layouts/EditTherapistPage';

const LicensePage = () => <div data-testid="license-page">License Page</div>;
const SpecializationPage = () => <div data-testid="specialization-page">Specialization Page</div>;
const RankSpecialtiesPage = () => <div data-testid="rank-specialties-page">Rank Specialties Page</div>;
const RankSubSpecialtiesPage = () => <div data-testid="rank-sub-specialties-page">Rank Sub Specialties Page</div>;
const ModalitiesPage = () => <div data-testid="modalities-page">Modalities Page</div>;
const PracticeFocusPage = () => <div data-testid="practice-focus-page">Practice Focus Page</div>;
const PracticeInfoPage = () => <div data-testid="practice-info-page">Practice Info Page</div>;
const ProfilePage = () => <div data-testid="profile-page">Profile Page</div>;

vi.mock('@loadable/component', () => ({
  __esModule: true,
  default: (loader: any) => {  
    const Component = () => {   
      const path = loader.toString();
      if (path.includes('license.page')) return <LicensePage />;
      if (path.includes('specialization.page')) return <SpecializationPage />;
      if (path.includes('rank-specialties.page')) return <RankSpecialtiesPage />;
      if (path.includes('rank-sub-specialties.page')) return <RankSubSpecialtiesPage />;
      if (path.includes('modalities.page')) return <ModalitiesPage />;
      if (path.includes('practice-focus.page')) return <PracticeFocusPage />;
      if (path.includes('practice-info.page')) return <PracticeInfoPage />;
      if (path.includes('profile.page')) return <ProfilePage />;
      return null;
    };
    Component.preload = () => loader();
    return Component;
  }
}));

vi.mock('@/pages/auth/registration/license.page', () => ({
  __esModule: true,
  default: LicensePage
}));

vi.mock('@/pages/auth/registration/specialization.page', () => ({
  __esModule: true,
  default: SpecializationPage
}));

vi.mock('@/pages/auth/registration/rank-specialties.page', () => ({
  __esModule: true,
  default: RankSpecialtiesPage
}));

vi.mock('@/pages/auth/registration/rank-sub-specialties.page', () => ({
  __esModule: true,
  default: RankSubSpecialtiesPage
}));

vi.mock('@/pages/auth/registration/modalities.page', () => ({
  __esModule: true,
  default: ModalitiesPage
}));

vi.mock('@/pages/auth/registration/practice-focus.page', () => ({
  __esModule: true,
  default: PracticeFocusPage
}));

vi.mock('@/pages/auth/registration/practice-info.page', () => ({
  __esModule: true,
  default: PracticeInfoPage
}));

vi.mock('@/pages/auth/registration/profile.page', () => ({
  __esModule: true,
  default: ProfilePage
}));

const renderWithRouter = (section?: string) => {
  return render(
    <MemoryRouter initialEntries={[section ? `/edit/${section}` : '/']}>
      <Routes>
        <Route path="/edit/:section" element={<EditTherapistPage />} />
        <Route path="/" element={<div data-testid="home-page">Home Page</div>} />
      </Routes>
    </MemoryRouter>
  );
};

describe('EditTherapistPage', () => {
  it('should redirect to home when no section is provided', async () => {
    renderWithRouter();
    expect(await screen.findByTestId('home-page')).toBeInTheDocument();
  });

  it('should redirect to home when invalid section is provided', async () => {
    renderWithRouter('invalid-section');
    expect(await screen.findByTestId('home-page')).toBeInTheDocument();
  });

  it('should render license page when section is "license"', async () => {
    renderWithRouter('license');
    expect(await screen.findByTestId('license-page')).toBeInTheDocument();
  });

  it('should render specialization page when section is "specialization"', async () => {
    renderWithRouter('specialization');
    expect(await screen.findByTestId('specialization-page')).toBeInTheDocument();
  });

  it('should render rank-specialties page when section is "rank-specialties"', async () => {
    renderWithRouter('rank-specialties');
    expect(await screen.findByTestId('rank-specialties-page')).toBeInTheDocument();
  });

  it('should render rank-sub-specialties page when section is "rank-sub-specialties"', async () => {
    renderWithRouter('rank-sub-specialties');
    expect(await screen.findByTestId('rank-sub-specialties-page')).toBeInTheDocument();
  });

  it('should render modalities page when section is "modalities"', async () => {
    renderWithRouter('modalities');
    expect(await screen.findByTestId('modalities-page')).toBeInTheDocument();
  });

  it('should render practice-focus page when section is "practice-focus"', async () => {
    renderWithRouter('practice-focus');
    expect(await screen.findByTestId('practice-focus-page')).toBeInTheDocument();
  });

  it('should render practice-info page when section is "practice-info"', async () => {
    renderWithRouter('practice-info');
    expect(await screen.findByTestId('practice-info-page')).toBeInTheDocument();
  });

  it('should render profile page when section is "profile"', async () => {
    renderWithRouter('profile');
    expect(await screen.findByTestId('profile-page')).toBeInTheDocument();
  });
}); 