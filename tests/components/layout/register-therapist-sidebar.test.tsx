import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { MemoryRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import configureStore from 'redux-mock-store'
import RegisterTherapistSidebar from '../../../src/components/layouts/RegisterTherapistSidebar'
import '@testing-library/jest-dom'
import { updateSidebarState } from '../../../src/store/slicers/sidebar.slicer'
import * as hooks from '../../../src/store/hooks'
import { hideSidebar } from '../../../src/store/slicers/sidebar.slicer'

const mockDispatch = vi.fn()
// Mock Redux
vi.mock('../../../src/store/hooks', () => ({
	useAppSelector: vi.fn((selector) =>
		selector({
			sidebar: {
				on: true,
				registerTherapist: {
					openSubMenu: {},
					invalidMenus: [],
				},
			},
		})
	),
	useAppDispatch: () => mockDispatch,
}))

const mockStore = configureStore([])
let store

describe('RegisterTherapistSidebar Component', () => {
	beforeEach(() => {
		vi.clearAllMocks()
		store = mockStore({
			sidebar: {
				on: true,
				registerTherapist: {
					openSubMenu: {},
					invalidMenus: [],
				},
			},
		})
	})

	it('renders the sidebar', () => {
		render(
			<Provider store={store}>
				<MemoryRouter>
					<RegisterTherapistSidebar />
				</MemoryRouter>
			</Provider>
		)

		expect(screen.getByTestId('register-therapist-sidebar')).toBeInTheDocument()
	})

	it("does not render menus when pathname is '/auth/register/create-account'", () => {
		render(
			<Provider store={store}>
				<MemoryRouter initialEntries={['/auth/register/create-account']}>
					<RegisterTherapistSidebar />
				</MemoryRouter>
			</Provider>
		)

		expect(screen.queryByRole('list')).not.toBeInTheDocument()
	})

	it('toggles submenu when clicking on a menu with submenus', () => {
		render(
			<Provider store={store}>
				<MemoryRouter>
					<RegisterTherapistSidebar />
				</MemoryRouter>
			</Provider>
		)

		const specialitiesMenu = screen.getByText('Specialities')
		fireEvent.click(specialitiesMenu)

		expect(mockDispatch).toHaveBeenCalledWith(
			updateSidebarState({
				key: 'registerTherapist',
				value: expect.objectContaining({
					openSubMenu: { Specialities: true },
				}),
			})
		)
	})

	it('closes the sidebar when close button is clicked', () => {
		store = mockStore({
			sidebar: { open: true },
		})

		const mockDispatch = vi.fn()
		vi.spyOn(hooks, 'useAppDispatch').mockReturnValue(mockDispatch)

		render(
			<Provider store={store}>
				<MemoryRouter>
					<RegisterTherapistSidebar />
				</MemoryRouter>
			</Provider>
		)

		const closeButton = screen.getByTestId('close-sidebar')
		fireEvent.click(closeButton)

		expect(mockDispatch).toHaveBeenCalledWith(hideSidebar())
	})
})
