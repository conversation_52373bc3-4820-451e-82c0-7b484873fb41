import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom/vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Navbar from '../../../src/components/layouts/Navbar';
import sidebarReducer from '../../../src/store/slicers/sidebar.slicer';


const createMockStore = () => {
  return configureStore({
    reducer: {
      sidebar: sidebarReducer,
    },
  });
};

describe('Navbar', () => {
  it('renders without crashing', () => {
    const store = createMockStore();
    render(
      <Provider store={store}>
        <Navbar />
      </Provider>
    );
    
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('dispatches toggleSidebar action when hamburger icon is clicked', () => {
    const store = createMockStore();
    const dispatchSpy = vi.spyOn(store, 'dispatch');

    render(
      <Provider store={store}>
        <Navbar />
      </Provider>
    );

    const hamburgerIcon = screen.getByRole('button');
    fireEvent.click(hamburgerIcon);

    expect(dispatchSpy).toHaveBeenCalledTimes(1);
    expect(dispatchSpy).toHaveBeenCalledWith(expect.any(Object));
  });

  it('has correct styling classes', () => {
    const store = createMockStore();
    render(
      <Provider store={store}>
        <Navbar />
      </Provider>
    );

    const navbar = screen.getByRole('navigation');
    expect(navbar).toHaveClass('px-4', 'py-2', 'bg-yellow-500');

    const hamburgerIcon = screen.getByRole('button');
    expect(hamburgerIcon).toHaveClass('cursor-pointer', 'fas', 'fa-bars', 'text-white', 'md:invisible');
  });
}); 