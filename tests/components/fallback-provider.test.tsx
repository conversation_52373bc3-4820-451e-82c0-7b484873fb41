import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, act } from "@testing-library/react";
import { FallbackProvider, FallbackContext } from "../../src/components/fallback-provider";
import { useContext, Suspense } from "react";

// A lazy component to simulate Suspense behavior
const LazyComponent = () => {
  throw new Promise(() => {}); // force Suspense fallback
};

describe("FallbackProvider", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should render children", () => {
    render(
      <FallbackProvider>
        <div data-testid="child">Hello</div>
      </FallbackProvider>
    );
    expect(screen.getByTestId("child")).toHaveTextContent("Hello");
  });

  it("should provide updateFallback and update fallback value", () => {
    let updateFallbackFn: ((fallback: any) => void) | undefined;

    const TestComponent = () => {
      const { updateFallback } = useContext(FallbackContext);
      updateFallbackFn = updateFallback;
      return <div>Test</div>;
    };

    render(
      <FallbackProvider>
        <TestComponent />
      </FallbackProvider>
    );

    expect(typeof updateFallbackFn).toBe("function");

    act(() => {
      updateFallbackFn!(<div data-testid="new-fallback">Loading...</div>);
    });

    // We can't assert the fallback until it's rendered, so simulate Suspense
    render(
      <FallbackProvider>
        <Suspense fallback={<div data-testid="initial">Initial</div>}>
          <LazyComponent />
        </Suspense>
      </FallbackProvider>
    );
  });

  it("should use default noop if context used outside provider", () => {
    const spy = vi.fn();

    const TestComponent = () => {
      const { updateFallback } = useContext(FallbackContext);
      updateFallback("test"); // Should call noop
      spy();
      return <div>Outside</div>;
    };

    render(<TestComponent />);
    expect(spy).toHaveBeenCalled();
  });
});
