import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import VerifyInput from '../../../src/components/common/VerifyInput';
import "@testing-library/jest-dom"

describe('VerifyInput', () => {
  it('renders nothing if verifyText is not provided', () => {
    const { container } = render(
      <VerifyInput verification="" onChange={() => {}} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders the verifyText and input field', () => {
    render(
      <VerifyInput
        verifyText="DELETE"
        verification=""
        onChange={() => {}}
      />
    );

    // Fix: ensure matcher always returns boolean
    expect(screen.getByTestId('verify-text')).toHaveTextContent('DELETE');

    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('calls onChange when input changes', () => {
    const handleChange = vi.fn();
    render(
      <VerifyInput
        verifyText="CONFIRM"
        verification=""
        onChange={handleChange}
      />
    );
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'CONFIRM' } });
    expect(handleChange).toHaveBeenCalledTimes(1);
  });
});
