import { render, fireEvent } from "@testing-library/react";
import PaginationAwareContainer from "../../src/components/PaginationAwareContainer";
import { describe, it, expect, vi } from "vitest";
import React from "react";


vi.mock("react-perfect-scrollbar", () => ({
  default: ({ children, onScroll, className }: any) => (
    <div
      onScroll={(e) =>
        onScroll({
          ...e,
          target: {
            scrollTop: 500,
            clientHeight: 500,
            scrollHeight: 500,
          },
        })
      }
      data-testid="scroll-container"
      className={className}
    >
      {children}
    </div>
  ),
}));

describe("PaginationAwareContainer", () => {
  const metaWithNextPage = { nextPage: 2 };
  const metaWithoutNextPage = { nextPage: null };
  const Child = () => <div>Child Content</div>;

  it("renders children properly", () => {
    const { getByText } = render(
      <PaginationAwareContainer meta={metaWithNextPage} onLoad={() => {}}>
        <Child />
      </PaginationAwareContainer>
    );
    expect(getByText("Child Content")).toBeInTheDocument();
  });

  it("calls onLoad when scrolled to bottom and has next page", () => {
    const onLoad = vi.fn();

    const { getByTestId } = render(
      <PaginationAwareContainer
        meta={metaWithNextPage}
        onLoad={onLoad}
        fetching={false}
      >
        <Child />
      </PaginationAwareContainer>
    );

    const container = getByTestId("scroll-container");
    fireEvent.scroll(container); // our mock adds custom target values
    expect(onLoad).toHaveBeenCalled();
  });

  it("does not call onLoad if already fetching", () => {
    const onLoad = vi.fn();

    const { getByTestId } = render(
      <PaginationAwareContainer
        meta={metaWithNextPage}
        onLoad={onLoad}
        fetching={true}
      >
        <Child />
      </PaginationAwareContainer>
    );

    const container = getByTestId("scroll-container");
    fireEvent.scroll(container);
    expect(onLoad).not.toHaveBeenCalled();
  });

  it("does not call onLoad if no next page", () => {
    const onLoad = vi.fn();

    const { getByTestId } = render(
      <PaginationAwareContainer
        meta={metaWithoutNextPage}
        onLoad={onLoad}
        fetching={false}
      >
        <Child />
      </PaginationAwareContainer>
    );

    const container = getByTestId("scroll-container");
    fireEvent.scroll(container);
    expect(onLoad).not.toHaveBeenCalled();
  });

  it("renders loader when fetching is true", () => {
    const { container } = render(
      <PaginationAwareContainer
        meta={metaWithNextPage}
        onLoad={() => {}}
        fetching={true}
      >
        <Child />
      </PaginationAwareContainer>
    );

    const spinner = container.querySelector("i.fa-spinner");
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass("animate-spin");
  });
});
