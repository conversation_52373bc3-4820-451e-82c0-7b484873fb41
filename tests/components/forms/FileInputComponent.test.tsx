import { render, fireEvent, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import FileInputComponent from "../../../src/components/forms/FileInputComponent";
import { describe, it, expect, beforeEach, vi, beforeAll, afterAll } from "vitest";
import "@testing-library/jest-dom"

beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => "blob:http://localhost/fake-url");
});

afterAll(() => {
  vi.restoreAllMocks();
});

describe("FileInputComponent", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockReset();
  });

  it("renders with default label", () => {
    render(<FileInputComponent onChange={mockOnChange} />);
    expect(screen.getByText("Upload file")).toBeInTheDocument();
  });

  it("renders with custom label", () => {
    render(<FileInputComponent label="Profile Picture" onChange={mockOnChange} />);
    expect(screen.getByText("Profile Picture")).toBeInTheDocument();
  });

  it("triggers file input click when div is clicked", async () => {
    render(<FileInputComponent onChange={mockOnChange} />);
   const input = screen.getByTestId("file-input") as HTMLInputElement;
    const clickSpy = vi.spyOn(input, "click");

    await userEvent.click(screen.getByText("Upload file"));
    expect(clickSpy).toHaveBeenCalled();
  });

  it("calls onChange with selected file", async () => {
    render(<FileInputComponent onChange={mockOnChange} />);
   const input = screen.getByTestId("file-input") as HTMLInputElement;

    const file = new File(["dummy content"], "example.png", { type: "image/png" });
    await waitFor(() =>
      fireEvent.change(input, {
        target: { files: [file] },
      })
    );

    expect(mockOnChange).toHaveBeenCalledWith(file);
  });

  it("calls onChange with selected file", async () => {
  render(<FileInputComponent onChange={mockOnChange} />);
  const input = screen.getByTestId("file-input") as HTMLInputElement;

  const file = new File(["dummy content"], "example.png", { type: "image/png" });

  await waitFor(() =>
    fireEvent.change(input, {
      target: { files: [file] },
    })
  );

  expect(mockOnChange).toHaveBeenCalledWith(file);
});



  it("renders preview from props", () => {
    render(
      <FileInputComponent
        onChange={mockOnChange}
        preview="https://example.com/image.png"
      />
    );

    const img = screen.getByAltText("preview") as HTMLImageElement;
    expect(img).toBeInTheDocument();
    expect(img.src).toContain("https://example.com/image.png");
  });

  it("renders preview from selected file", async () => {
    render(<FileInputComponent onChange={mockOnChange} />);
   const input = screen.getByTestId("file-input") as HTMLInputElement;

    const file = new File(["img"], "test.jpg", { type: "image/jpeg" });

    await waitFor(() =>
      fireEvent.change(input, {
        target: { files: [file] },
      })
    );

    const img = screen.getByAltText("preview") as HTMLImageElement;
    expect(img).toBeInTheDocument();
    expect(img.src).toContain("blob:");
  });
});
