import { render, screen, fireEvent } from "@testing-library/react";
import AppCheckBoxInputComponent from "../../../src/components/forms/AppCheckBoxInputComponent";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom"
import type { OptionType } from "../../../src//types/registration.interface";

const itemList: OptionType[] = [
  { id: "linkedin", name: "social", label: "LinkedIn", value: "linkedin" },
  { id: "facebook", name: "social", label: "Facebook", value: "facebook", disabled: true },
];

describe("AppCheckBoxInputComponent", () => {
  it("renders all options", () => {
    render(<AppCheckBoxInputComponent itemList={itemList} />);
    expect(screen.getByLabelText("linkedin")).toBeInTheDocument();
    const checkbox = screen.getByRole("checkbox", { name: "facebook" }) as HTMLInputElement;
    expect(checkbox).toBeDisabled();
  });

  it("initializes with checked values", () => {
    render(
      <AppCheckBoxInputComponent
        itemList={itemList}
        checked={{ linkedin: "https://linkedin.com" }}
      />
    );
    const input = screen.getByPlaceholderText("Paste LinkedIn profile link") as HTMLInputElement;
    expect(input).toBeInTheDocument();
    expect(input.value).toBe("https://linkedin.com");
  });

  it("checks and unchecks a checkbox", () => {
    const onChange = vi.fn();
    render(<AppCheckBoxInputComponent itemList={itemList} onChange={onChange} />);

    const checkbox = screen.getByLabelText("linkedin") as HTMLInputElement;
    fireEvent.click(checkbox);
    expect(onChange).toHaveBeenCalledWith({ linkedin: "" });

    fireEvent.click(checkbox);
    expect(onChange).toHaveBeenCalledWith({});
  });

  it("updates input value when typing", () => {
    const onChange = vi.fn();
    render(
      <AppCheckBoxInputComponent
        itemList={itemList}
        onChange={onChange}
        checked={{ linkedin: "" }}
      />
    );

    const input = screen.getByPlaceholderText("Paste LinkedIn profile link");
    fireEvent.change(input, { target: { value: "https://linkedin.com/in/user" } });
    expect(onChange).toHaveBeenCalledWith({ linkedin: "https://linkedin.com/in/user" });
  });

});
