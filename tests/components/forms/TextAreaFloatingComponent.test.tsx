import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import TextAreaFloatingComponent from "../../../src/components/forms/TextAreaFloatingComponent";
import "@testing-library/jest-dom";

describe("TextAreaFloatingComponent", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockReset();
  });

  it("renders with label and placeholder", () => {
    render(
      <TextAreaFloatingComponent
        id="bio"
        label="Bio"
        placeholder="Enter your bio"
      />
    );
    const textarea = screen.getByPlaceholderText("Enter your bio");
    const label = screen.getByLabelText("Bio");
    expect(textarea).toBeInTheDocument();
    expect(label).toBeInTheDocument();
  });

  it("displays required asterisk if required is true", () => {
    render(
      <TextAreaFloatingComponent
        id="bio"
        label="Bio"
        required
        placeholder="Bio"
      />
    );
    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("sets textarea value from props", () => {
    render(
      <TextAreaFloatingComponent
        id="bio"
        label="Bio"
        value="My bio content"
        placeholder="Bio"
      />
    );
    const textarea = screen.getByLabelText("Bio") as HTMLTextAreaElement;
    expect(textarea.value).toBe("My bio content");
  });

  it("calls onChange when user types", () => {
    render(
      <TextAreaFloatingComponent
        id="bio"
        label="Bio"
        onChange={mockOnChange}
        placeholder="Bio"
      />
    );
    const textarea = screen.getByPlaceholderText("Bio");
    fireEvent.change(textarea, { target: { value: "New text" } });
    expect(mockOnChange).toHaveBeenCalledWith("New text");
  });

  it("applies custom input class name", () => {
    render(
      <TextAreaFloatingComponent
        id="bio"
        label="Bio"
        inputClassName="custom-input"
        placeholder="Bio"
      />
    );
    const textarea = screen.getByPlaceholderText("Bio");
    expect(textarea.className).toContain("custom-input");
  });
});
