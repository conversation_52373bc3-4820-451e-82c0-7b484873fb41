import { render, screen, fireEvent } from "@testing-library/react";
import FilterSelectComponent from "../../../src/components/forms/FilterSelectComponent";
import { describe, it, vi, expect } from "vitest";
import React from "react";

describe("FilterSelectComponent", () => {
  const defaultProps = {
    label: "Select Option",
    options: ["One", "Two", "Three"],
    id: "my-select",
  };

  it("renders with label and id", () => {
    render(<FilterSelectComponent {...defaultProps} />);
    expect(screen.getByLabelText("Select Option")).toBeInTheDocument();
    expect(screen.getByRole("combobox")).toHaveAttribute("id", "my-select");
  });

  it("shows required asterisk if required is true", () => {
    render(<FilterSelectComponent {...defaultProps} required />);
    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("renders string/number options correctly", () => {
    render(<FilterSelectComponent {...defaultProps} options={[
      { label: "Apple", value: "Apple" },
      { label: "Answer to life", value: 42 },
    ]} />);
    expect(screen.getByText("Apple")).toBeInTheDocument();
    expect(screen.getByText("Answer to life")).toBeInTheDocument();
  });

  it("renders object options correctly", () => {
    render(
      <FilterSelectComponent
        {...defaultProps}
        options={[
          { label: "Option A", value: "A" },
          { label: "Option B", value: "B" },
        ]}
      />
    );
    expect(screen.getByText("Option A")).toBeInTheDocument();
    expect(screen.getByText("Option B")).toBeInTheDocument();
  });

  it("calls onChange without regex", () => {
    const onChange = vi.fn();
    render(<FilterSelectComponent {...defaultProps} onChange={onChange} />);
    fireEvent.change(screen.getByRole("combobox"), { target: { value: "Two" } });
    expect(onChange).toHaveBeenCalledWith("Two");
  });

 it("calls onChange with regex if match passes", () => {
  const onChange = vi.fn();
  render(
    <FilterSelectComponent
      label="Label"
      id="regex-select"
      options={["123", "456"]}
      onChange={onChange}
      regex={/^\d+$/} // only digits allowed
    />
  );
  fireEvent.change(screen.getByRole("combobox"), {
    target: { value: "123" },
  });
  expect(onChange).toHaveBeenCalledWith("123");
});

  it("does not call onChange if regex does not match", () => {
    const onChange = vi.fn();
    render(
      <FilterSelectComponent
        {...defaultProps}
        onChange={onChange}
        regex={/^\d+$/}
      />
    );
    fireEvent.change(screen.getByRole("combobox"), { target: { value: "abc" } });
    expect(onChange).not.toHaveBeenCalled();
  });

  it("uses controlled value prop", () => {
    render(<FilterSelectComponent {...defaultProps} value="Two" />);
    expect(screen.getByRole("combobox")).toHaveValue("Two");
  });
});
