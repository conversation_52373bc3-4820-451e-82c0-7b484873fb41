import { render, screen, fireEvent } from "@testing-library/react";
import PasswordComponent from "../../../src/components/forms/PasswordComponent";
import { describe, it, expect } from "vitest";
import React from "react";

describe("PasswordComponent", () => {
  const baseProps = {
    id: "password",
    label: "Password",
    name: "password",
  };

  it("renders with default type as password", () => {
    render(<PasswordComponent {...baseProps} />);
    const input = screen.getByLabelText("Password") as HTMLInputElement;
    expect(input).toBeInTheDocument();
    expect(input.type).toBe("password");
  });

 it("toggles password visibility", () => {
  render(<PasswordComponent {...baseProps} />);
  const input = screen.getByLabelText("Password") as HTMLInputElement;
  const toggleBtn = screen.getByTestId("toggle-password");

  fireEvent.click(toggleBtn);
  expect(input.type).toBe("text");

  fireEvent.click(toggleBtn);
  expect(input.type).toBe("password");
});


  it("does not show toggle icon when disabled", () => {
    render(<PasswordComponent {...baseProps} isDisabled />);
    expect(screen.queryByRole("img", { hidden: true })).toBeNull();
  });

  it("shows required asterisk when isRequired is true", () => {
    render(<PasswordComponent {...baseProps} isRequired />);
    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("displays description if provided", () => {
    render(
      <PasswordComponent {...baseProps} description="Enter a strong password" />
    );
    expect(screen.getByText("Enter a strong password")).toBeInTheDocument();
  });

  it("displays errorMessage if provided", () => {
    render(
      <PasswordComponent {...baseProps} errorMessage="This field is required" />
    );
    expect(screen.getByText("This field is required")).toBeInTheDocument();
  });
});
