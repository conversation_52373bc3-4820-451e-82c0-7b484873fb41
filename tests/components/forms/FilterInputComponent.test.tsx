import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import FilterInputComponent from "../../../src/components/forms/FilterInputComponent";
import { beforeEach, describe, expect, it, vi } from "vitest";
import React from "react";
import { debounce } from "lodash"; // ✅ use import instead of require
import "@testing-library/jest-dom";

// ✅ Mock lodash.debounce
vi.mock("lodash", async () => {
  const actual = await vi.importActual<any>("lodash");
  const cancelMock = vi.fn();
  const mockDebounce = (fn: any) => {
    fn.cancel = cancelMock;
    return fn;
  };
  mockDebounce.cancel = cancelMock;
  return {
    ...actual,
    debounce: mockDebounce,
  };
});

describe("FilterInputComponent", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockReset();
  });

  it("renders with default type and placeholder", () => {
    render(<FilterInputComponent placeholder="Search..." onChange={mockOnChange} />);
    const input = screen.getByPlaceholderText("Search...") as HTMLInputElement;
    expect(input).toBeInTheDocument();
    expect(input.type).toBe("text"); // default type
  });

  it("renders with custom type and value", () => {
    render(<FilterInputComponent type="email" value="<EMAIL>" />);
    const input = screen.getByDisplayValue("<EMAIL>") as HTMLInputElement;
    expect(input.type).toBe("email");
  });

  it("calls onChange when input changes (debounced)", async () => {
    render(<FilterInputComponent onChange={mockOnChange} />);
    const input = screen.getByRole("textbox");

    await userEvent.type(input, "Hello");
    expect(mockOnChange).toHaveBeenCalledTimes(5);
    expect(mockOnChange).toHaveBeenLastCalledWith("Hello");
  });

  it("clears input and triggers onChange when clear icon is clicked", async () => {
    render(<FilterInputComponent onChange={mockOnChange} />);
    const input = screen.getByRole("textbox");
    const clearButton = screen.getByTestId("clear-button");

    await userEvent.type(input, "Test");
    fireEvent.click(clearButton);

    expect(mockOnChange).toHaveBeenLastCalledWith("");
  });

  it("cancels debounced function on unmount", () => {
    const { unmount } = render(<FilterInputComponent onChange={mockOnChange} />);
    unmount();

    expect(debounce.cancel).toHaveBeenCalled();
  });
});
