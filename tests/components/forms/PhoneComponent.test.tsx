import { describe, it, expect, vi, beforeEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import PhoneComponent from "../../../src/components/forms/PhoneComponent";
import "@testing-library/jest-dom"
// Prevent Cleave warnings
vi.mock("cleave.js/react", async () => {
  const React = await vi.importActual<any>("react");
  return {
    __esModule: true,
    default: React.forwardRef(({ value, onChange, ...props }: any, ref) => (
      <input
        ref={ref}
        value={value}
        onChange={onChange}
        {...props}
        data-testid="cleave-input"
      />
    )),
  };
});

describe("PhoneComponent", () => {
  const mockOnChange = vi.fn();

  beforeEach(() => {
    mockOnChange.mockReset();
  });

  it("renders with label and optional asterisk", () => {
    render(<PhoneComponent label="Phone Number" id="phone" required />);
    expect(screen.getByLabelText(/Phone Number/)).toBeInTheDocument();
    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("renders with given value minus +1 prefix", () => {
    render(<PhoneComponent label="Phone" id="phone" value="+1234567890" />);
    const input = screen.getByTestId("cleave-input") as HTMLInputElement;
    expect(input.value).toBe("234567890");
  });

  it("handles input changes and calls onChange with cleaned number", () => {
    render(
      <PhoneComponent
        label="Phone"
        id="phone"
        onChange={mockOnChange}
        value=""
      />
    );
    const input = screen.getByTestId("cleave-input");
    fireEvent.change(input, { target: { value: "***** 765 4321" } });
    expect(mockOnChange).toHaveBeenCalledWith("+1987654321");
  });

  it("respects disabled prop", () => {
    render(<PhoneComponent label="Phone" id="phone" disabled />);
    const input = screen.getByTestId("cleave-input") as HTMLInputElement;
    expect(input).toBeDisabled();
  });

  it("applies custom placeholder and class", () => {
    render(
      <PhoneComponent
        label="Phone"
        id="phone"
        placeholder="Enter phone"
        inputClassName="custom-class"
      />
    );
    const input = screen.getByPlaceholderText("Enter phone");
    expect(input.className).toContain("custom-class");
  });
});
