import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, vi, beforeEach, Mock, expect } from 'vitest';
import ToolbarComponent from '../../src/components/ToolbarComponent';
import * as reactRedux from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { getAllConditionalPageIds, getMainPageId, getPreviousPageId, updateConditionalPageData } from '../../src/utils/storage.util';
import { getPatientRegistrationPage } from '../../src/store/slicers/auth.slicer';
import { useAppSelector } from '../../src/store/hooks';
import "@testing-library/jest-dom";

vi.mock('react-redux', async () => {
  const actual = await vi.importActual<typeof reactRedux>('react-redux');
  return {
    ...actual,
    useDispatch: vi.fn(),
    useSelector: vi.fn(),
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
  };
});

vi.mock('@/utils/storage.util', () => ({
  getAllConditionalPageIds: vi.fn(),
  getMainPageId: vi.fn(),
  getPreviousPageId: vi.fn(),
  updateConditionalPageData: vi.fn(),
}));

vi.mock('@/store/slicers/auth.slicer', () => ({
  getPatientRegistrationPage: vi.fn(),
}));

vi.mock('@/store/hooks', () => ({
  useAppSelector: vi.fn(),
}));

describe('ToolbarComponent', () => {
  const mockDispatch = vi.fn();
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    (reactRedux.useDispatch as Mock).mockReturnValue(mockDispatch);
    (useNavigate as Mock).mockReturnValue(mockNavigate);

    (useAppSelector as Mock).mockImplementation((selectorFn: any) =>
      selectorFn({
        auth: {
          registration: {
            editFlag: false,
            patientAnswers: {
              2: { answers: { id: 42 } },
              1: { page: { questionnaireId: 99 } },
            },
          },
        },
      })
    );

    (getAllConditionalPageIds as Mock).mockReturnValue([3]);
    (getMainPageId as Mock).mockReturnValue(2);
    (getPreviousPageId as Mock).mockImplementation((id: number) => {
      const map: Record<number, number | null> = {
        3: 2,
        2: 1,
        1: null,
      };
      return map[id] ?? null;
    });

    // Set up window.location
    delete (window as any).location;
    (window as any).location = {
      pathname: '/auth/register/patient',
      search: '?currentPage=3&category=general',
    };
  });

  const renderComponent = () => render(<ToolbarComponent />);

  it('should navigate back if not on patient page', async () => {
    window.location.pathname = '/auth/register/therapist';

    renderComponent();

    const backButton = screen.getByText(/Previous/i);
    fireEvent.click(backButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });
  });

  it('should navigate back if currentPage is missing', async () => {
    window.location.search = '?category=general';

    renderComponent();

    const backButton = screen.getByText(/Previous/i);
    fireEvent.click(backButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });
  });

  it('should navigate back if previous page ID is missing', async () => {
    (getPreviousPageId as Mock).mockReturnValueOnce(null);

    renderComponent();

    const backButton = screen.getByText(/Previous/i);
    fireEvent.click(backButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith(-1);
    });
  });

  it('should handle conditional page and dispatch previous page with mainPageId', async () => {
    renderComponent();

    const backButton = screen.getByText(/Previous/i);
    fireEvent.click(backButton);

    await waitFor(() => {
      expect(updateConditionalPageData).toHaveBeenCalledWith(3, true);
      expect(getPatientRegistrationPage).toHaveBeenCalledWith({
        pageId: 2,
        category: 'general',
        prevQuestionId: 99,
        prevSelectedAnswer: 42,
      });
      expect(mockDispatch).toHaveBeenCalled();
    });
  });

  it('should dispatch previous page without mainPageId', async () => {
    (getMainPageId as Mock).mockReturnValueOnce(null);

    renderComponent();

    const backButton = screen.getByText(/Previous/i);
    fireEvent.click(backButton);

    await waitFor(() => {
      expect(getPatientRegistrationPage).toHaveBeenCalledWith({
        pageId: 2,
        category: 'general',
      });
      expect(mockDispatch).toHaveBeenCalled();
    });
  });

  it('should display "Back" when editFlag is true', () => {
    (useAppSelector as Mock).mockImplementation((selectorFn: any) =>
      selectorFn({
        auth: {
          registration: {
            editFlag: true,
            patientAnswers: {},
          },
        },
      })
    );

    renderComponent();

    expect(screen.getByText(/Back/i)).toBeInTheDocument();
  });

 it('should dispatch previous page with prevQuestionId as undefined when secondPrevPageId is null', async () => {
  (getMainPageId as Mock).mockReturnValueOnce(2);
  (getPreviousPageId as Mock).mockImplementation((id: number) => {
    const map: Record<number, number | null> = {
      3: 2,
      2: null,
    };
    return map[id] ?? null;
  });

  // Override selector for this test
  (useAppSelector as Mock).mockImplementation((selectorFn: any) =>
    selectorFn({
      auth: {
        registration: {
          editFlag: false,
          patientAnswers: {
            // No answers or page info to simulate missing data
          },
        },
      },
    })
  );

  renderComponent();

  const backButton = screen.getByText(/Previous/i);
  fireEvent.click(backButton);

  await waitFor(() => {
    expect(getPatientRegistrationPage).toHaveBeenCalledWith({
      pageId: 2,
      category: 'general',
      prevQuestionId: undefined,
      prevSelectedAnswer: undefined,
    });
    expect(mockDispatch).toHaveBeenCalled();
  });
});

});
