import { describe, it, vi, expect, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import PatientActivateDialog from "../../../src/components/dialogs/PatientActivateDialog";
import "@testing-library/jest-dom";

// Shared spies
const patch = vi.fn();
const successToast = vi.fn();
const errorToast = vi.fn();

// Mock repository globally
vi.mock("../../../src/repositories/BaseRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      patch,
      successToast,
      errorToast,
    })),
  };
});

// Dialog state factory
const createState = (isOpen = true) => ({
  isOpen,
  close: vi.fn(),
  setOpen: vi.fn(),
  open: vi.fn(),
  toggle: vi.fn(),
});

describe("PatientActivateDialog", () => {
  const refetch = vi.fn();
  const setGlobalLoading = vi.fn();
  const title = "Activate Patient";
  const url = "/patients/1/activate";
  const state = createState();

  const renderComponent = () =>
    render(
      <PatientActivateDialog
        state={state}
        title={title}
        url={url}
        refetch={refetch}
        setGlobalLoading={setGlobalLoading}
      >
        Are you sure you want to activate?
      </PatientActivateDialog>
    );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders dialog with title and children", () => {
    renderComponent();
    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText("Are you sure you want to activate?")).toBeInTheDocument();
  });

  it("shows input field and label", () => {
    renderComponent();
    expect(screen.getByLabelText(/Type ACTIVATE to confirm/i)).toBeInTheDocument();
  });

  it("displays error when wrong input is typed and form is submitted", async () => {
    renderComponent();
    fireEvent.change(screen.getByLabelText(/Type ACTIVATE to confirm/i), {
      target: { value: "WRONG" },
    });
    fireEvent.click(screen.getByText("Activate"));

    expect(await screen.findByText("Please type 'ACTIVATE' to confirm")).toBeInTheDocument();
  });

  it("clears error when input is corrected", async () => {
    renderComponent();
    const input = screen.getByLabelText(/Type ACTIVATE to confirm/i);
    fireEvent.change(input, { target: { value: "WRONG" } });
    fireEvent.click(screen.getByText("Activate"));
    await screen.findByText("Please type 'ACTIVATE' to confirm");

    fireEvent.change(input, { target: { value: "ACTIVATE" } });
    expect(screen.queryByText("Please type 'ACTIVATE' to confirm")).not.toBeInTheDocument();
  });

  it("calls API and closes dialog on success", async () => {
    patch.mockResolvedValueOnce({ status: 200 });

    renderComponent();
    fireEvent.change(screen.getByLabelText(/Type ACTIVATE to confirm/i), {
      target: { value: "ACTIVATE" },
    });
    fireEvent.click(screen.getByText("Activate"));

    await waitFor(() => {
      expect(patch).toHaveBeenCalledWith(url, {});
      expect(successToast).toHaveBeenCalledWith("Patient activated successfully");
      expect(state.close).toHaveBeenCalled();
      expect(refetch).toHaveBeenCalled();
    });
  });

  it("disables buttons when processing is true", async () => {
    patch.mockResolvedValueOnce({ status: 200 });

    renderComponent();
    const input = screen.getByLabelText(/Type ACTIVATE to confirm/i);
    fireEvent.change(input, { target: { value: "ACTIVATE" } });

    const activateButton = screen.getByText("Activate");
    fireEvent.click(activateButton);

    await waitFor(() => {
      expect(activateButton).toHaveAttribute("disabled");
    });
  });

  it("resets form when dialog opens", async () => {
    const dynamicState = createState(false);
    const { rerender } = render(
      <PatientActivateDialog
        state={dynamicState}
        title={title}
        url={url}
        refetch={refetch}
        setGlobalLoading={setGlobalLoading}
      >
        Confirm
      </PatientActivateDialog>
    );

    dynamicState.isOpen = true;
    rerender(
      <PatientActivateDialog
        state={dynamicState}
        title={title}
        url={url}
        refetch={refetch}
        setGlobalLoading={setGlobalLoading}
      >
        Confirm
      </PatientActivateDialog>
    );

    expect(screen.getByLabelText(/Type ACTIVATE to confirm/i)).toHaveValue("");
  });

  it("calls state.close on cancel", async () => {
    renderComponent();
    fireEvent.click(screen.getByText("Cancel"));
    expect(state.close).toHaveBeenCalled();
  });

  it("shows error toast on API failure with message", async () => {
    patch.mockRejectedValueOnce({
      response: {
        data: { message: "Failed request" },
      },
    });

    renderComponent();
    fireEvent.change(screen.getByLabelText(/Type ACTIVATE to confirm/i), {
      target: { value: "ACTIVATE" },
    });
    fireEvent.click(screen.getByText("Activate"));

    await waitFor(() => {
      expect(errorToast).toHaveBeenCalledWith("Failed request");
    });
  });

  it("shows fallback error toast on unknown error", async () => {
    patch.mockRejectedValueOnce({});

    renderComponent();
    fireEvent.change(screen.getByLabelText(/Type ACTIVATE to confirm/i), {
      target: { value: "ACTIVATE" },
    });
    fireEvent.click(screen.getByText("Activate"));

    await waitFor(() => {
      expect(errorToast).toHaveBeenCalledWith("Failed to activate patient");
    });
  });
});
