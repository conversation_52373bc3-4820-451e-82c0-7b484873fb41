import { describe, it, vi, expect, beforeEach } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import PatientDeactivateDialog from "../../../src/components/dialogs/PatientDeactivateDialog";
import BaseRepository from "../../../src//repositories/BaseRepository";
import { HttpStatusCode } from "axios";
import "@testing-library/jest-dom";

vi.mock("@/repositories/BaseRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      patch: vi.fn().mockResolvedValue({ status: HttpStatusCode.Ok }),
      successToast: vi.fn(),
      errorToast: vi.fn(),
    })),
  };
});

vi.mock("@/repositories/PatientRepository", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      getDeleteDeactivateReasons: vi.fn().mockResolvedValue({
        reasons: [
          {
            id: 1,
            heading: "Category A",
            subheadings: [
              { id: 101, subheading: "Reason A1" },
              { id: 102, subheading: "Reason A2" },
            ],
          },
        ],
      }),
    })),
  };
});

describe("PatientDeactivateDialog", () => {
  const refetch = vi.fn();
  const setGlobalLoading = vi.fn();
  const title = "Deactivate Patient";
  const url = "/patients/1/deactivate";
  const createState = (isOpen = true) => ({
    isOpen,
    close: vi.fn(),
    setOpen: vi.fn(),
    open: vi.fn(),
    toggle: vi.fn()
  });
  const renderComponent = (state = createState()) =>
    render(
      <PatientDeactivateDialog
        state={state}
        title={title}
        url={url}
        refetch={refetch}
        setGlobalLoading={setGlobalLoading}
      >
        Are you sure you want to deactivate?
      </PatientDeactivateDialog>
    );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("renders dialog with title and content", async () => {
    renderComponent();
    expect(await screen.findByText(title)).toBeInTheDocument();
    expect(screen.getByText("Are you sure you want to deactivate?")).toBeInTheDocument();
  });

  it("loads and shows reason categories and subheadings", async () => {
    renderComponent();
    expect(await screen.findByLabelText("Category")).toBeInTheDocument();
    expect(screen.getByLabelText("Specific Reason")).toBeInTheDocument();
  });

  it("calls API and closes dialog on successful deactivation", async () => {
    const state = createState();
    renderComponent(state);
    fireEvent.change(await screen.findByRole("textbox"), { target: { value: "DEACTIVATE" } });
    fireEvent.click(screen.getByText("Deactivate"));

    await waitFor(() => {
      expect(BaseRepository).toHaveBeenCalled();
      expect(state.close).toHaveBeenCalled();
      expect(refetch).toHaveBeenCalled();
    });
  });

  it("shows error if API fails", async () => {
    const errorMessage = "Failed to deactivate";
    (BaseRepository as any).mockImplementation(() => ({
      patch: vi.fn().mockRejectedValue(new Error(errorMessage)),
      successToast: vi.fn(),
      errorToast: vi.fn(),
    }));
    renderComponent();
    fireEvent.change(await screen.findByRole("textbox"), { target: { value: "DEACTIVATE" } });
    fireEvent.click(screen.getByText("Deactivate"));
    expect(await screen.findByText(errorMessage)).toBeInTheDocument();
  });

  it("closes dialog on cancel", async () => {
    const state = createState();
    renderComponent(state);
    fireEvent.click(screen.getByText("Cancel"));
    expect(state.close).toHaveBeenCalled();
  });


});
