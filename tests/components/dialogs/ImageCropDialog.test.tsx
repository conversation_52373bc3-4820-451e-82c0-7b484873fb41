import { describe, it, vi, beforeEach, afterEach, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import ImageCropDialog from '../../../src/components/dialogs/ImageCropDialog';
import "@testing-library/jest-dom"
// ✅ Mock Image to trigger onload
vi.stubGlobal('Image', class {
  _src = '';
  onload = () => {};
  width = 100;
  height = 50;
  naturalWidth = 100;
  naturalHeight = 50;

  set src(value: string) {
    this._src = value;
    setTimeout(() => {
      this.onload(); // Trigger onload after setting src
    }, 0);
  }

  get src() {
    return this._src;
  }
});

// ✅ Mock canvas and toBlob
vi.stubGlobal('HTMLCanvasElement', class {
  getContext = vi.fn(() => ({
    scale: vi.fn(),
    drawImage: vi.fn(),
    save: vi.fn(),
    restore: vi.fn(),
    imageSmoothingQuality: '',
  }));

  toBlob(cb: (blob: Blob) => void) {
    const blob = new Blob(['mock'], { type: 'image/png' });
    cb(blob);
  }
} as any);



// ✅ Mocks for overlay state and callbacks
const mockClose = vi.fn();
const mockOnCropComplete = vi.fn();

const mockState = {
  isOpen: true,
  close: mockClose,
  setOpen: vi.fn(),
  open: vi.fn(),
  toggle: vi.fn(),
};

beforeEach(() => {
  vi.useFakeTimers();
  mockClose.mockClear();
  mockOnCropComplete.mockClear();
});

afterEach(() => {
  vi.runOnlyPendingTimers();
  vi.useRealTimers();
});

describe('ImageCropDialog', () => {

  it('does not load image when selectedFile is null', () => {
    render(
      <ImageCropDialog
        selectedFile={null}
        imageCropState={mockState}
        onCropComplete={mockOnCropComplete}
      />
    );

    expect(screen.queryByAltText('Selected')).not.toBeInTheDocument();
  });

  
});
