import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";
import EmailVerificationDialog from "../../../src/components/dialogs/EmailVerificationDialog";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useApiClient } from "../../../src/utils/api.util";
import {
  sendVerificationEmailAction,
  fetchLoginInfo,
} from "../../../src/store/slicers/therapist.slicer";
import "@testing-library/jest-dom"

vi.mock("react-redux", () => ({
  useDispatch: vi.fn(),
}));

vi.mock("react-router-dom", () => ({
  useNavigate: vi.fn(),
}));

vi.mock("@/utils/api.util", () => ({
  useApiClient: vi.fn(),
}));

vi.mock("@/utils/notification.util", () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock("@/store/slicers/therapist.slicer", () => ({
  sendVerificationEmailAction: vi.fn(),
  fetchLoginInfo: vi.fn(),
}));

vi.mock("@/store/slicers/auth.slicer", () => ({
  signIn: vi.fn(),
  setNewRegistrationState: vi.fn(),
}));

vi.mock("@/store/slicers/sidebar.slicer", () => ({
  resetSidebarState: vi.fn(),
}));

vi.mock("@/utils/app.util", () => ({
  generateRegFormContent: vi.fn().mockReturnValue({ foo: "bar" }),
}));

describe("EmailVerificationDialog", () => {
  const dispatch = vi.fn();
  const navigate = vi.fn();
  const client = { post: vi.fn() };
  const verificationState = { isOpen: true, close: vi.fn(), setOpen: vi.fn(),open: vi.fn(), toggle: vi.fn(),};
  const userId = 123;
  const email = "<EMAIL>";

  beforeEach(() => {
    vi.clearAllMocks();
    (useDispatch as any).mockReturnValue(dispatch);
    (useNavigate as any).mockReturnValue(navigate);
    (useApiClient as any).mockReturnValue(client);
    (sendVerificationEmailAction as any).mockReturnValue({ status: 200 });
    (fetchLoginInfo as any).mockResolvedValue({ status: 200, data: { userToken: "t", user: {}, accessToken: "a", refreshToken: "r" } });
  });

  it("auto-sends code on open", async () => {
    render(<EmailVerificationDialog verificationState={verificationState} userId={userId} email={email} />);
    await waitFor(() => expect(dispatch).toHaveBeenCalledWith(sendVerificationEmailAction({ email, noLink: true })));
  });

 it("renders with normal dialog", () => {
  render(
    <EmailVerificationDialog
      verificationState={verificationState}
      userId={userId}
      email={email}
    />
  );

  expect(
    screen.getByText((content) => content.includes(email))
  ).toBeInTheDocument();
});


 it("displays resend label and countdown", async () => {
  render(<EmailVerificationDialog verificationState={verificationState} userId={userId} email={email} />);

  await waitFor(() =>
    expect(screen.getByText((text) => text.includes("Try again"))).toBeInTheDocument()
  );

  fireEvent.click(screen.getByText((text) => text.includes("Try again")));

  expect(dispatch).toHaveBeenCalledTimes(2); // initial + resend
});

});
