import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import AppBar from "../../../src/components/mobile/appbar";
import "@testing-library/jest-dom"

describe("AppBar", () => {
  it('renders default AppBar with title and custom backgroundColor', () => {
    render(<AppBar type="default" title="Dashboard" backgroundColor="bg-red-500" />);
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
    const container = screen.getByText("Dashboard").closest("div");
    expect(container?.className).toContain("bg-red-500");
  });

  it('renders default AppBar with no title and fallback backgroundColor', () => {
    render(<AppBar type="default" />);
    const container = screen.getByRole("presentation-new", { hidden: true })?.closest("div");
    expect(container?.className).toContain("bg-therapy-blue");
  });

  it('renders info type AppBar', () => {
    render(<AppBar type="info" />);
    expect(screen.getByAltText("logo")).toBeInTheDocument();
  });

  it('renders questionnaire type AppBar with custom backgroundColor', () => {
    render(<AppBar type="questionnaire" backgroundColor="bg-green-300" />);
    const container = screen.getByRole("presentation-new", { hidden: true })?.closest("div");
    expect(container?.className).toContain("bg-green-300");
  });

  it('renders fallback AppBar for unknown type', () => {
    render(<AppBar type="unknown-type" />);
    const container = screen.getByRole("presentation-new", { hidden: true })?.closest("div");
    expect(container?.className).toContain("bg-therapy-blue");
  });
});
