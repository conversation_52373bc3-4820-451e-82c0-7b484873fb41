# Code of Conduct

This file typically serves as a document outlining the expected behavior and guidelines for participants, contributors, and users of a software project or an open-source community.

## Our Standards


### API URI Standards

To ensure clarity, maintainability, and consistency across all API endpoints, developers must adhere to the following guidelines when designing and naming URI paths:

1) Every API for mobile will have `/api/v1` prefixed. eg: `api/v1/users` instead of `/users`
3) We will structure endpoints to represent resources, not actions. eg: Use `/users` instead of `/getUsers`
3) Use lowercase and hyphenated words. eg: `/user-profiles` instead of `/userProfiles` or `/user_profiles`.

4) Follow consistent pluralization. eg: `/products (plural)` instead of `/product (singular)`.
5) Define relationships with nested resources. eg: `/users/{userId}/orders` to indicate orders for a specific user.
6) Do not include filtering or search criteria in the URI path. Use query parameters instead. eg: `/products?category=electronics&price=asc` instead of `/products/electronics/asc`
7) Adhere to HTTP methods to indicate the action:

```README.md

GET: Retrieve multiple resource (e.g., GET /users).

GET: Retrieve a resource (e.g., GET /users/{id}).

POST: Create a resource (e.g., POST /users).

PUT: Update a resource (e.g., PUT /users/{id}).

DELETE: Delete a resource (e.g., DELETE /users/{id})

```


### Response

To response `failed` or any success response like `created`, `deleted` etc use the methods from response middleware that is attached to express's `res` handler.


```typescript
return res.success("You message");
return res.created("You message");

//for data collection
return res.collection([]);

return res.data({});
// for other methods checkout response.helper.ts
```


### Error Handling

There is a error handle middleware that will catch all the exception throught out the app and send a uniform response. Use predefined exception class to through exception or you can also use methods attached to `res` handler


```typescript
throw new UnauthorizedError("Your error message");
// for other exception checkout exception.handle.ts

// or you can also
return res.failure("your error message");
```



----

## Usage and Pattern Guidelines

The below heading list will desribe "coding guidelines" or "coding standards," a set of rules, conventions, and best practices that developers should follow when writing code for a specific programming language or framework. These guidelines help maintain consistency, readability, and quality in a codebase. that needs to be followed while contributing to this repository.

### Naming Conventions

Each developer must adhere to the following naming conventions across various domains specified as follows:

#### Database

1) tables are lower case sperated by `_`  if there is multiple words. eg: `notify_subscription_updates`
2) tables are always plural unless that is a pivot table. eg: `users` `appointments`
3) column name for the table will always follow camelCase format. eg: `annualPrice`, `createdAt`
4) any functions or trigger defined in database will follow camelCase eg: `triggerNoticeUpdate`

**NOTE** whenever a extra key is added to the response object please make sure that particular key is also camelCase.


#### Javascript (Backend / Frontend)

1) Variables are alayws camelCase eg: `myCalendar`
2) function names are always camelCase eg: `prepareMenus`
3) Constants are alayws UPPERCASE separated by _ if multiple workds. eg: `PAYMENT_PREFERENCE`
4) ClassName are always PascalCase. eg: `TherapistCalendar`


### Request Payload

Always use request payload extracted from `matchedData` helper method of `express-validator`. For some rare case and when it is absolutely required we can fall back to `req.body.somevalue`

```typescript
const { ...rest } = matchedData(req);
```

### Git

1. Branch name if there is ticket. `fix-[TICKET-ID]` `feature-[TICKET-ID]` eg: `feature-NT-12`
2. Always Make sure the eslint rule check pipeline has always passed before you merge to development.


## TODO

Create github actions for eslint checks.
Create API example in test.cotroller.ts
COC for test cases.
