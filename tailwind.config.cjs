/* eslint-disable no-undef */
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#F1C376",
        secondary: "#F7E6C4",
        tertiary: "#FFF4F4",
        quaternary: "#606C5D",
        danger: "#E74646",
        "therapy-blue": "#547191",
        "therapy-blue-dark": "#431d42",
        "therapy-success": "#54915b",
        "therapy-telehealth": "#c79cc8",
        "therapy-both-tele-and-in-person": "#f3b749",
        "therapy-login":"#c49ccc"
      },
      fontFamily: {
        muller: ['"Muller"',"Arial","sans-serif"]
      },
      fontSize: {
        base: "16px"
      },
      fontWeight: {
        bold: 700
      },
      lineHeight: {
        base: "17.28px"
      },
      textDecoration: {
        underline: "underline",
        "decoration-skip-ink-none": {
          "text-underline-position": "from-font",
          "text-decoration-skip-ink": "none",
        },
      },
      animation: {
        enter: 'enter 200ms ease-out',
        'slide-in': 'slide-in 1.2s cubic-bezier(.41,.73,.51,1.02)',
        leave: 'leave 150ms ease-in forwards',
      },
      keyframes: {
        enter: {
          '0%': { transform: 'scale(0.9)', opacity: 0 },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        leave: {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '100%': { transform: 'scale(0.9)', opacity: 0 },
        },
        'slide-in': {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
      },
    },
  },
  plugins: [
    require("./plugins/neumorphism"),
    require("@headlessui/tailwindcss"),
  ],
}

