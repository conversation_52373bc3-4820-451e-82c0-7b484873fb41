{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vitest/globals"], "baseUrl": "./src", "paths": {"@/components/*": ["components/*"], "@/context/*": ["context/*"], "@/plugins/*": ["plugins/*"], "@/store/*": ["store/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/styles/*": ["styles/*"], "@/pages/*": ["pages/*"], "@/repositories/*": ["repositories/*"], "@/configs/*": ["configs/*"]}}, "include": ["src", "src/plugins/calendar"], "exclude": ["node_modules", "tailwind.config.js", "ecosystem.config.cjs"], "references": [{"path": "./tsconfig.node.json"}]}