{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "outDir": "build", "strict": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "nodenext", "sourceMap": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/src/*": ["src/*"]}, "typeRoots": ["./src/types", "./node_modules/@types"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.spec.ts", "database/**/*.js"]}