{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "plugins": [
    "@typescript-eslint",
    "no-loops"
  ],
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "rules": {
    "no-console": 1,
    "no-comma-dangle": 0,
    // "no-loops/no-loops": 2,
    "max-len": [
      "error",
      {
        "code": 180,
        "ignoreComments": true,
        "ignoreStrings": true,
        "ignoreTemplateLiterals": true,
        "ignoreRegExpLiterals": true
      }
    ],
    // "no-tabs": [
    //   "error",
    //   {
    //     "allowIndentationTabs": false
    //   }
    // ],
    "@typescript-eslint/no-explicit-any": "off"
  }
}