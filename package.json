{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite:start", "dev": "vite", "build": "tsc && vite build", "lint": "eslint ./src/**/*.ts --fix", "preview": "vite preview", "up": "pm2 startOrRestart ecosystem.config.cjs --env production", "down": "pm2 delete ecosystem.config.cjs", "test": "vitest", "test:ui": "vitest --ui", "coverage": "nyc --reporter=lcov --reporter=text npm run test"}, "dependencies": {"@azure/msal-browser": "^3.28.1", "@azure/msal-react": "^2.2.0", "@headlessui/react": "^1.7.18", "@headlessui/tailwindcss": "^0.2.0", "@loadable/component": "^5.16.3", "@microsoft/microsoft-graph-client": "^3.0.7", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.2.3", "@stripe/react-stripe-js": "^2.7.0", "@stripe/stripe-js": "^3.3.0", "@tanstack/react-query": "^5.17.15", "@types/react-datepicker": "^4.19.6", "axios": "^1.6.5", "cleave.js": "^1.6.0", "color": "^4.2.3", "compressorjs": "^1.2.1", "dayjs": "^1.11.10", "dragselect": "^3.1.1", "formik": "^2.4.5", "framer-motion": "^10.18.0", "google-auth-library": "^9.4.2", "googleapis": "^128.0.0", "lodash": "^4.17.21", "moment-timezone": "^0.5.47", "nprogress": "^0.2.0", "path": "^0.12.7", "react": "^18.2.0", "react-aria": "^3.31.1", "react-beautiful-dnd": "^13.1.1", "react-confetti": "^6.1.0", "react-datepicker": "^6.1.0", "react-dom": "^18.2.0", "react-google-places-autocomplete": "^4.0.1", "react-hot-toast": "^2.4.1", "react-image-crop": "^11.0.10", "react-intersection-observer": "^9.5.3", "react-joyride": "^2.9.3", "react-perfect-scrollbar": "^1.5.8", "react-phone-number-input": "^3.4.11", "react-redux": "^8.1.3", "react-router-dom": "^6.21.2", "react-select": "^5.8.0", "react-stately": "^3.29.1", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "typescript": "^5.3.3", "uuid": "^9.0.1", "vite": "^5.0.8"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.0", "@testing-library/user-event": "^14.6.1", "@types/cleave.js": "^1.4.12", "@types/loadable__component": "^5.13.8", "@types/lodash": "^4.14.202", "@types/node": "^20.11.5", "@types/nprogress": "^0.2.3", "@types/react": "^18.2.48", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.0.7", "@vitest/ui": "^3.0.7", "autoprefixer": "^10.4.16", "dotenv": "^16.3.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-no-loops": "^0.3.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^24.0.0", "nyc": "^17.1.0", "postcss": "^8.4.33", "redux-mock-store": "^1.5.5", "sass": "^1.69.7", "tailwindcss": "^3.4.1", "vite": "^5.4.11", "vitest": "^3.0.7"}}