{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "NODE_ENV=production node build/server.js", "start:dev": "npm run dev", "prestart:dev": "NODE_ENV=development npx sequelize-cli db:migrate", "dev": "npm run build:dev && NODE_ENV=development node build/server.js", "build": "NODE_ENV=production rimraf build && tsc -p .", "build:dev": "rimraf build && tsc -p .", "worker:dev": "NODE_ENV=development ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/worker.ts", "worker": "NODE_ENV=production node build/worker.js", "lint": "eslint ./src --ext .ts --fix", "seed:create": "npx sequelize-cli seed:create --name", "seed": "npx sequelize-cli db:seed:all", "migrate:create": "npx sequelize-cli migration:create --name", "create:migration": "npx sequelize-cli migration:create --name", "migrate": "npx sequelize-cli db:migrate", "migrate:rollback": "npx sequelize-cli db:migrate:undo", "rollback:migration": "npx sequelize-cli db:migrate:undo", "migrate:reset": "npx sequelize-cli db:migrate:undo:all", "migrate:refresh": "npm run migrate:reset && npm run migrate", "test": "NODE_ENV=test nyc --reporter=lcov --reporter=text mocha --exit", "up": "pm2 startOrRestart ecosystem.config.js --env production", "down": "pm2 delete ecosystem.config.js", "coverage": "nyc --reporter=lcov --reporter=text npm run test"}, "keywords": [], "author": "<PERSON><PERSON><PERSON><<EMAIL>>", "license": "ISC", "devDependencies": {"@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/bcryptjs": "^2.4.6", "@types/chai": "^4.3.11", "@types/compression": "^1.7.5", "@types/convert-excel-to-json": "^1.7.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/klaw-sync": "^6.0.5", "@types/mocha": "^10.0.6", "@types/mock-require": "^3.0.0", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.9.4", "@types/node-cron": "^3.0.11", "@types/sinon": "^17.0.3", "@types/supertest": "^2.0.16", "@types/validator": "^13.11.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "chai": "^4.3.10", "eslint": "^8.54.0", "mocha": "^10.2.0", "mock-require": "^3.0.3", "module-alias": "^2.2.3", "rimraf": "^5.0.5", "sequelize-cli": "^6.6.2", "sinon": "^19.0.2", "supertest": "^6.3.3", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.782.0", "@faker-js/faker": "^8.3.1", "@google-cloud/local-auth": "^2.1.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@react-oauth/google": "^0.12.1", "@sendgrid/helpers": "^8.0.0", "@sendgrid/mail": "^8.1.4", "aws-sdk": "^2.1567.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "bullmq": "^4.14.0", "compression": "^1.7.4", "convert-excel-to-json": "^1.7.0", "cors": "^2.8.5", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-validator": "^7.0.1", "firebase-admin": "^13.0.2", "googleapis": "^105.0.0", "handlebars": "^4.7.8", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "klaw-sync": "^6.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-geocoder": "^4.4.1", "nyc": "^17.1.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "redis": "^4.6.11", "sequelize": "^6.35.1", "sequelize-typescript": "^2.1.5", "stripe": "^15.4.0", "twilio": "^5.4.3", "winston": "^3.11.0", "winston-cloudwatch": "^6.3.0", "xlsx": "^0.18.5"}, "_moduleAliases": {"@/src": "build"}}