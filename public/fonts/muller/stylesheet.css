@font-face {
    font-family: 'Muller';
    src: url('Muller-MediumItalic.woff2') format('woff2'),
        url('Muller-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Medium.woff2') format('woff2'),
        url('Muller-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-ThinItalic.woff2') format('woff2'),
        url('Muller-ThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Thin.woff2') format('woff2'),
        url('Muller-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-RegularItalic.woff2') format('woff2'),
        url('Muller-RegularItalic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Regular.woff2') format('woff2'),
        url('Muller-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-LightItalic.woff2') format('woff2'),
        url('Muller-LightItalic.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-ExtraBoldItalic.woff2') format('woff2'),
        url('Muller-ExtraBoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Bold.woff2') format('woff2'),
        url('Muller-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Heavy.woff2') format('woff2'),
        url('Muller-Heavy.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-BoldItalic.woff2') format('woff2'),
        url('Muller-BoldItalic.woff') format('woff');
    font-weight: bold;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-HeavyItalic.woff2') format('woff2'),
        url('Muller-HeavyItalic.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-ExtraBold.woff2') format('woff2'),
        url('Muller-ExtraBold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-BlackItalic.woff2') format('woff2'),
        url('Muller-BlackItalic.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Light.woff2') format('woff2'),
        url('Muller-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Muller';
    src: url('Muller-Black.woff2') format('woff2'),
        url('Muller-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

