version: 1
environment_variables:
  plaintext:
    projectKey: "NextTherapist-Admin-FE"
    projectVersion: "main"
    projectName: "NextTherapist-Admin-FE"

env:
  parameter-store:
    SONAR_TOKEN: $SONAR_TOKEN
    SONAR_HOST: $SONAR_HOST

frontend:
  phases:
    preBuild:
      commands:
        - npm install --cache .npm --prefer-offline
    build:
      commands:
        - echo "Running sonar-scanner"
        - wget "https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-3.2.0.1227-linux.zip" || { echo "Failed to download sonar-scanner-cli"; exit 1; }
        - unzip sonar-scanner-cli-3.2.0.1227-linux.zip
        - echo "Downloading sonar-scanner.properties from S3..."
        - aws s3 cp "s3://nexttherapist-frontend-artifacts/config/sonar-scanner.properties" "./sonar-scanner.properties" || { echo "Failed to download sonar-scanner.properties"; exit 1; }
        - mv sonar-scanner.properties sonar-scanner-3.2.0.1227-linux/conf/sonar-scanner.properties
        - echo "SonarQube"
        - npm run test
        - npx vitest run --coverage
        # Debugging:
        - echo "Files in current directory after coverage:"
        - ls -la
        - echo "Files in coverage directory:"
        - ls -la coverage
        - echo "Checking for coverage/lcov.info file..."
        - if [ -f "coverage/lcov.info" ]; then
            echo "coverage/lcov.info exists.";
            echo "Checking content of coverage/lcov.info...";
            cat coverage/lcov.info;
            echo "Checking current working directory...";
            pwd;
          else
            echo "coverage/lcov.info does not exist!";
            exit 1;
          fi
        - "./sonar-scanner-3.2.0.1227-linux/bin/sonar-scanner -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectKey=$projectKey -Dsonar.projectVersion=$projectVersion"
        - npm run build
  artifacts:
    baseDirectory: dist
    files:
      - '**/*'
  cache:
    paths:
      - .npm/**/*
  customHeaders:
  - pattern: '/.well-known/apple-app-site-association'
    headers:
      - key: 'Content-Type'
        value: 'application/json'
      - key: 'Cache-Control'
        value: 'no-store'
